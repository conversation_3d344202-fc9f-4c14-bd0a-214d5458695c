package br.com.alice.fleuryintegrationservice.clients

import br.com.alice.common.client.DefaultHttpClient
import br.com.alice.common.core.PersonId
import br.com.alice.common.core.extensions.toBrazilianDateFormat
import br.com.alice.common.core.extensions.toCustomFormat
import br.com.alice.common.extensions.coResultOf
import br.com.alice.common.extensions.then
import br.com.alice.common.extensions.thenError
import br.com.alice.common.logging.logger
import br.com.alice.common.service.serialization.gsonIdentity
import br.com.alice.data.layer.models.FleuryLaudo
import br.com.alice.data.layer.models.Person
import br.com.alice.fleuryintegrationclient.exceptions.FleuryPdfItemIsEmptyException
import br.com.alice.fleuryintegrationclient.exceptions.FleuryResponseException
import br.com.alice.fleuryintegrationservice.ServiceConfig
import br.com.alice.fleuryintegrationservice.metrics.FichasResponseMetrics
import br.com.alice.fleuryintegrationservice.metrics.FichasResponseStatus
import br.com.alice.fleuryintegrationservice.metrics.ImageLinkResponseMetrics
import br.com.alice.fleuryintegrationservice.metrics.ImageLinkResponseStatus
import br.com.alice.fleuryintegrationservice.metrics.ItensResponseMetrics
import br.com.alice.fleuryintegrationservice.metrics.ItensResponseStatus
import br.com.alice.fleuryintegrationservice.metrics.LaudoResponseMetrics
import br.com.alice.fleuryintegrationservice.metrics.LaudoResponseStatus
import br.com.alice.fleuryintegrationservice.metrics.PdfResponseMetrics
import br.com.alice.fleuryintegrationservice.metrics.PdfResponseStatus
import br.com.alice.fleuryintegrationservice.models.Ficha
import br.com.alice.fleuryintegrationservice.models.FichaResponse
import br.com.alice.fleuryintegrationservice.models.ImageLaudoResponse
import br.com.alice.fleuryintegrationservice.models.ImageLinkResponse
import br.com.alice.fleuryintegrationservice.models.ItemResponse
import br.com.alice.fleuryintegrationservice.models.LaudoRequest
import br.com.alice.fleuryintegrationservice.models.LaudoRequestType
import br.com.alice.fleuryintegrationservice.models.LaudoRequestType.FORMATTED
import br.com.alice.fleuryintegrationservice.models.LaudoRequestType.STRUCTURED
import br.com.alice.fleuryintegrationservice.models.PdfResponse
import com.github.kittinunf.result.Result
import com.github.kittinunf.result.failure
import com.github.kittinunf.result.flatMap
import com.github.kittinunf.result.success
import com.google.gson.reflect.TypeToken
import io.ktor.client.HttpClient
import io.ktor.client.plugins.contentnegotiation.ContentNegotiation
import io.ktor.client.request.get
import io.ktor.client.request.header
import io.ktor.client.request.headers
import io.ktor.client.request.parameter
import io.ktor.client.statement.HttpResponse
import io.ktor.client.statement.bodyAsText
import io.ktor.client.statement.readBytes
import io.ktor.http.HttpStatusCode
import java.time.LocalDateTime

class FleuryClient(
    private val client: HttpClient = DefaultHttpClient({
        expectSuccess = false

        install(ContentNegotiation) {
            gsonIdentity()
        }
    }, timeoutInMillis = 15_000),
) {
    private val credentials = ServiceConfig.ApiCredentials
    private val baseUrl = "${credentials.baseUrl}/integracao-operadora/v2"

    suspend fun getFichas(
        accessToken: String,
        person: Person,
        since: LocalDateTime,
    ): Result<List<Ficha>, Throwable> {
        logger.info(
            "FleuryClient GetFichas request",
            "person_id" to person.id
        )
        return getFichasGet(
            accessToken,
            person.nationalId,
            person.dateOfBirth!!.toCustomFormat("yyyy-MM-dd"),
            since
        ).flatMap { handleGetFichasResponse(it, person.id) }
    }


    suspend fun getItensByFicha(accessToken: String, idFicha: String): Result<ItemResponse, Throwable> {
        logger.info(
            "FleuryClient Sending GetItens request V2",
            "idFicha" to idFicha
        )
        return getItensByFichaGet(accessToken, idFicha)
            .flatMap { handleGetItensResponse(it, idFicha) }
    }


    suspend fun getImageLink(
        accessToken: String,
        idFicha: String,
        idItem: String,
    ): Result<String, Throwable> {
        logger.info(
            "FleuryClient GetImageLink request",
            "id_ficha" to idFicha,
            "id_item" to idItem
        )

        return getImageLinkPost(accessToken, idFicha, idItem)
            .thenError {
                logger.error("Error on getImageLink call", "id_ficha" to idFicha, "id_item" to idItem, it)
                ImageLinkResponseMetrics.incrementImageLinkResponseCounterRaw(ImageLinkResponseStatus.FAILURE)
            }
            .flatMap { handleGetImageLinkResponse(it, idFicha) }
    }

    private suspend fun getImageLinkPost(accessToken: String, idFicha: String, idItem: String) =
        coResultOf<HttpResponse, Throwable> {
            client.get("$baseUrl/ficha/$idFicha/item/$idItem/imagem") {
                header("access_token", accessToken)
                header("client_id", credentials.clientId)
            }
        }

    suspend fun getStructuredLaudo(
        accessToken: String,
        request: LaudoRequest,
    ): Result<List<FleuryLaudo>, Throwable> {
        logger.info(
            "FleuryClient getStructuredLaudo request",
            "idFicha" to request.idFicha,
            "idItem" to request.idItem,
            "procedimento" to request.procedimento
        )

        return getLaudoGet(accessToken, request.idFicha, request.idItem, STRUCTURED)
            .flatMap {
                handleGetLaudoResponse(it, request) {
                    gsonIdentity.fromJson(
                        it.bodyAsText(),
                        object : TypeToken<List<FleuryLaudo>>() {}.type
                    )
                }
            }
    }

    suspend fun getFormattedLaudo(
        accessToken: String,
        request: LaudoRequest,
    ): Result<ImageLaudoResponse, Throwable> {
        logger.info(
            "FleuryClient getImageLaudo request",
            "idFicha" to request.idFicha,
            "idItem" to request.idItem,
            "procedimento" to request.procedimento
        )
        return getLaudoGet(accessToken, request.idFicha, request.idItem, FORMATTED)
            .flatMap {
                handleGetLaudoResponse(it, request) {
                    gsonIdentity.fromJson(it.bodyAsText(), ImageLaudoResponse::class.java)
                }
            }
    }

    private suspend fun getFichasGet(
        accessToken: String,
        nationalId: String,
        birthDate: String,
        since: LocalDateTime,
    ) = coResultOf<HttpResponse, Throwable> {
        client.get("$baseUrl/fichas") {
            parameter("cpf", nationalId)
            parameter("dtNascimento", birthDate)
            parameter("dataInicio", since.minusMonths(1).toBrazilianDateFormat())
            parameter("dataFim", "01/12/2100")
            parameter("_limit", "100")

            headers {
                append("access_token", accessToken)
                append("client_id", credentials.clientId)
            }
        }
    }

    private suspend fun getItensByFichaGet(accessToken: String, idFicha: String) =
        coResultOf<HttpResponse, Throwable> {
            client.get("$baseUrl/ficha/$idFicha/itens") {
                parameter("_limit", 100)
                headers {
                    append("access_token", accessToken)
                    append("client_id", credentials.clientId)
                }
            }
        }

    private suspend fun getLaudoGet(
        accessToken: String,
        idFicha: String,
        idItem: String,
        requestType: LaudoRequestType,
    ) = coResultOf<HttpResponse, Throwable> {
        client.get("$baseUrl/ficha/$idFicha/item/$idItem/json") {
            parameter("formato", requestType.format)

            header("access_token", accessToken)
            header("client_id", credentials.clientId)
        }
    }

    suspend fun getPDF(
        accessToken: String,
        idFicha: String,
        idItem: String
    ): Result<ByteArray, Throwable> {
        logger.info(
            "FleuryClient getPDF request",
            "id_ficha" to idFicha,
            "id_item" to idItem
        )
        return downloadFileGet(accessToken, idFicha, idItem).flatMap { response ->
            handleGetPdfResponse(response, idFicha, idItem)
        }
    }
    private suspend fun downloadFileGet(
        accessToken: String,
        idFicha: String,
        idItem: String
    ): Result<HttpResponse, Throwable> =
        coResultOf {
            client.get("$baseUrl/ficha/$idFicha/item/$idItem/pdf") {
                header("access_token", accessToken)
                header("client_id", credentials.clientId)
            }
        }

    private suspend fun <T : Any> handleGetLaudoResponse(
        response: HttpResponse,
        request: LaudoRequest,
        conversion: suspend () -> T,
    ) = when (response.status) {
        HttpStatusCode.OK -> {
            conversion().success()
                .then {
                    logger.info(
                        "getImageLaudo returned successfully",
                        "response" to it
                    )
                    LaudoResponseMetrics.incrementLaudoResponseCounterRaw(LaudoResponseStatus.SUCCESS)
                }
        }
        HttpStatusCode.NoContent -> {
            logger.info(
                "FleuryClient Get Laudo response - no content",
                "ficha_id" to request.idFicha,
                "item_id" to request.idItem,
                "procedimento" to request.procedimento
            )
            LaudoResponseMetrics.incrementLaudoResponseCounterRaw(LaudoResponseStatus.EMPTY)
            FleuryLaudoNotAvailableException().failure()
        }
        else -> {
            logger.error(
                "FleuryClient Get Laudo response error",
                "ficha_id" to request.idFicha,
                "item_id" to request.idItem,
                "procedimento" to request.procedimento,
                "response_status" to response.status.value,
                "response_body" to String(response.readBytes())
            )
            LaudoResponseMetrics.incrementLaudoResponseCounterRaw(LaudoResponseStatus.FAILURE)
            FleuryResponseException(response.status.value).failure()
        }
    }

    private suspend fun handleGetItensResponse(
        response: HttpResponse,
        idFicha: String,
    ): Result<ItemResponse, Throwable> = when (response.status) {
        HttpStatusCode.OK -> {
            val payload = response.bodyAsText()
            logger.info(
                "FleuryClient Get Itens response - Success",
                "ficha_id" to idFicha,
                "response_body" to payload
            )
            val itemResponse = gsonIdentity.fromJson(payload, ItemResponse::class.java)
            if (itemResponse.itens.isEmpty()) {
                ItensResponseMetrics.incrementItensResponseCounterRaw(ItensResponseStatus.EMPTY)
                FleuryItensIsEmptyException(idFicha).failure()
            } else {
                ItensResponseMetrics.incrementItensResponseCounterRaw(ItensResponseStatus.SUCCESS)
                itemResponse.success()
            }
        }
        HttpStatusCode.NoContent -> {
            logger.info(
                "FleuryClient Get Itens response - no content",
                "ficha_id" to idFicha
            )
            ItensResponseMetrics.incrementItensResponseCounterRaw(ItensResponseStatus.EMPTY)
            FleuryItensIsEmptyException(idFicha).failure()
        }
        else -> {
            logger.error(
                "FleuryClient Get Itens response error",
                "ficha_id" to idFicha,
                "response_status" to response.status.value,
                "response_body" to String(response.readBytes())
            )
            ItensResponseMetrics.incrementItensResponseCounterRaw(ItensResponseStatus.FAILURE)
            FleuryResponseException(response.status.value).failure()
        }
    }

    private suspend fun handleGetFichasResponse(
        response: HttpResponse,
        personId: PersonId,
    ): Result<List<Ficha>, Throwable> =
        when (response.status) {
            HttpStatusCode.OK -> {
                gsonIdentity.fromJson(response.bodyAsText(), FichaResponse::class.java).fichas.success().then {
                    logger.info(
                        "GetFichas returned successfully",
                        "fichas" to it
                    )
                }.flatMap {
                    if (it.isEmpty()) {
                        FichasResponseMetrics.incrementFichasResponseCounterRaw(FichasResponseStatus.EMPTY)
                        FleuryFichasIsEmptyException(personId).failure()
                    } else {
                        FichasResponseMetrics.incrementFichasResponseCounterRaw(FichasResponseStatus.SUCCESS)
                        it.success()
                    }
                }
            }
            HttpStatusCode.NoContent -> {
                logger.info(
                    "FleuryClient Get Fichas response - no content",
                    "person_id" to personId
                )
                FichasResponseMetrics.incrementFichasResponseCounterRaw(FichasResponseStatus.EMPTY)
                FleuryFichasIsEmptyException(personId).failure()
            }
            else -> {
                logger.error(
                    "FleuryClient Get Fichas response error",
                    "person_id" to personId,
                    "response_status" to response.status.value,
                    "response_body" to String(response.readBytes())
                )
                FichasResponseMetrics.incrementFichasResponseCounterRaw(FichasResponseStatus.FAILURE)
                FleuryResponseException(response.status.value).failure()
            }
        }

    private suspend fun handleGetImageLinkResponse(response: HttpResponse, idFicha: String) =
        when (response.status) {
            HttpStatusCode.OK -> {
                ImageLinkResponseMetrics.incrementImageLinkResponseCounterRaw(ImageLinkResponseStatus.SUCCESS)
                gsonIdentity.fromJson(response.bodyAsText(), ImageLinkResponse::class.java)
                    .exame.urlImage.success()
            }
            else -> {
                logger.error(
                    "FleuryClient Get Image Link response error",
                    "ficha_id" to idFicha,
                    "response_status" to response.status.value
                )
                ImageLinkResponseMetrics.incrementImageLinkResponseCounterRaw(ImageLinkResponseStatus.FAILURE)
                FleuryResponseException(response.status.value).failure()
            }
        }

    private suspend fun handleGetPdfResponse(response: HttpResponse, idFicha: String, idItem: String) =
        when (response.status) {
            HttpStatusCode.OK -> {
                gsonIdentity.fromJson(response.bodyAsText(), PdfResponse::class.java)
                    .pdf.toByteArray().success().then {
                        PdfResponseMetrics.incrementPdfResponseCounterRaw(PdfResponseStatus.SUCCESS)
                    }
            }
            HttpStatusCode.NoContent -> {
                logger.info(
                    "FleuryClient Get pdf - no content",
                    "idItem" to idItem,
                    "idFicha" to idFicha,
                )
                PdfResponseMetrics.incrementPdfResponseCounterRaw(PdfResponseStatus.EMPTY)
                FleuryPdfItemIsEmptyException(idFicha, idItem).failure()
            }
            else -> {
                PdfResponseMetrics.incrementPdfResponseCounterRaw(PdfResponseStatus.FAILURE)

                logger.error(
                    "FleuryClient Get PDF response error",
                    "ficha_id" to idFicha,
                    "item_id" to idItem,
                    "response_status" to response.status.value,
                    "response_body" to String(response.readBytes()),
                )

                FleuryResponseException(response.status.value).failure()
            }
        }


}

