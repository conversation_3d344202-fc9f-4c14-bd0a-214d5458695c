package br.com.alice.nullvs.services.internals

import br.com.alice.common.RangeUUID
import br.com.alice.common.core.extensions.onlyDigits
import br.com.alice.common.core.extensions.onlyNumbers
import br.com.alice.common.data.dsl.matchers.ResultAssert
import br.com.alice.common.helpers.coVerifyNone
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.common.helpers.returns
import br.com.alice.common.kafka.internals.LocalProducer
import br.com.alice.common.models.Sex
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.BatchType
import br.com.alice.data.layer.models.ExternalModelType
import br.com.alice.data.layer.models.InternalModelType
import br.com.alice.data.layer.models.LogStatus
import br.com.alice.nullvs.clients.TotvsMemberClient
import br.com.alice.nullvs.common.NullvsActionType
import br.com.alice.nullvs.converters.toModel
import br.com.alice.nullvs.events.NullvsMemberBatchResponseEvent
import br.com.alice.nullvs.logics.TotvsRequestLogic.generateHash
import br.com.alice.nullvs.models.Meta
import br.com.alice.nullvs.models.TotvsCityCode
import br.com.alice.nullvs.models.TotvsGroupCompany
import br.com.alice.nullvs.models.TotvsRelationType
import br.com.alice.nullvs.models.TotvsUser
import br.com.alice.nullvs.models.member.GracePeriodType
import br.com.alice.nullvs.models.member.NullvsMemberBatchRequest
import br.com.alice.nullvs.models.member.NullvsMemberBatchResponse
import br.com.alice.nullvs.models.member.NullvsMemberReactivationBatchRequest
import br.com.alice.nullvs.models.member.TotvsMemberReactivationRequest
import br.com.alice.nullvs.models.member.TotvsMemberRequest
import br.com.alice.nullvs.models.member.TotvsMemberResponse
import com.github.kittinunf.result.success
import io.mockk.clearAllMocks
import io.mockk.coEvery
import io.mockk.mockk
import kotlinx.coroutines.runBlocking
import org.assertj.core.api.Assertions
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.TestInstance
import java.time.LocalDate
import java.time.LocalDateTime
import kotlin.test.AfterTest
import kotlin.test.Test

@TestInstance(TestInstance.Lifecycle.PER_CLASS)
class TotvsMemberServiceTest {

    private val totvsMemberClient: TotvsMemberClient = mockk()
    private val nullvsIntegrationLogService: NullvsIntegrationLogService = mockk()
    private val service = TotvsMemberService(totvsMemberClient, LocalProducer, nullvsIntegrationLogService)

    private val internalId = RangeUUID.generate()
    private val meta = Meta(
        eventId = RangeUUID.generate(),
        eventName = "event01",
        internalId = internalId,
        internalModelName = InternalModelType.MEMBER,
        integrationEventName = "integration01",
        externalId = null,
        externalModelName = ExternalModelType.BENEFICIARY,
        integratedAt = LocalDateTime.now().minusDays(1),
        originalTopic = "original01",
    )
    private val nullvsIntegrationLog = TestModelFactory.buildNullvsIntegrationLog()

    @AfterTest
    fun setup() = clearAllMocks()

    @Nested
    inner class RouteMemberToIntegrationFlowsCreateBeneficiary {

        private val member = TestModelFactory.buildMember()
        private val person = TestModelFactory.buildPerson(
            mothersName = "Mother's name",
            phoneNumber = "phone",
            dateOfBirth = LocalDateTime.of(1994, 10, 17, 14, 0)
        )

        private val requestMemberBatchRequest = NullvsMemberBatchRequest(
            meta,
            NullvsActionType.CREATE,
            TotvsMemberRequest(
                company = TotvsGroupCompany.ALICE_INDIVIDUAL,
                client = "billing-accountable-party",
                createdAt = LocalDateTime.now(),
                ANSProductId = "ans-product-id",
                idPayload = 1,
                beneficiaries = listOf(
                    TotvsMemberRequest.TotvsBeneficiary(
                        userType = TotvsUser.HOLDER,
                        email = person.email,
                        fullName = person.fullRegisterName,
                        fullSocialName = person.fullSocialName,
                        aliceId = member.id,
                        nationalId = person.nationalId.onlyNumbers(),
                        identityDocument = person.identityDocument,
                        mothersName = person.mothersName!!,
                        addressPostalCode = person.mainAddress!!.postalCode!!.onlyDigits(),
                        addressStreet = person.mainAddress!!.street,
                        addressNumber = person.mainAddress!!.number,
                        addressComplement = person.mainAddress!!.complement,
                        addressNeighborhood = person.mainAddress!!.neighbourhood!!,
                        addressCity = TotvsCityCode("3550308", "SAO PAULO", "SP"),
                        addressState = person.mainAddress!!.state.toString(),
                        phoneNumber = person.phoneNumber!!,
                        dateOfBirth = person.dateOfBirth!!,
                        createdAt = person.createdAt,
                        parentBeneficiaryRelationType = TotvsRelationType.HOLDER,
                        sex = Sex.FEMALE,
                        cnsNumber = person.cnsNumber,
                        ccoCodeANS = null,
                        ANSProductId = "ans-id",
                        canceledAt = null,
                        canceledReason = null,
                        cpts = listOf(
                            TotvsMemberRequest.TotvsBeneficiary.CPT(
                                cid = "XPTO",
                                startedAt = LocalDate.of(2023, 6, 1),
                                periodInDays = 730L
                            )
                        ),
                        gracePeriods = listOf(
                            TotvsMemberRequest.TotvsBeneficiary.GracePeriod(
                                type = GracePeriodType.BIRTH,
                                startedAt = LocalDate.of(2023, 6, 1),
                                periodInDays = 300L
                            )
                        ),
                    ),
                ),
            ),
        )

        private val nullvsMemberReactivationBatchRequest =
            NullvsMemberReactivationBatchRequest(
                meta, NullvsActionType.REACTIVATION, TotvsMemberReactivationRequest(
                    totvsId = "0000000000000000000",
                    idPayload = 1,
                    aliceId = member.id,
                    actionAt = LocalDate.now(),
                    actionCode = "009",
                    observation = "Reactivation",
                )
            )

        @Test
        fun `#should produce a NullvsMemberBatchResponseEvent`() = runBlocking {
            val response = NullvsMemberBatchResponse(meta, TotvsMemberResponse("200", "00000001", "1", "I"))

            coEvery { totvsMemberClient.postMember(requestMemberBatchRequest) } returns response

            service.routeMemberToIntegrationFlows(requestMemberBatchRequest)

            Assertions.assertThat(LocalProducer.hasEvent(NullvsMemberBatchResponseEvent.name)).isTrue

            coVerifyOnce { totvsMemberClient.postMember(requestMemberBatchRequest) }
        }

        @Test
        fun `#shouldn't produce a NullvsMemberBatchResponseEvent when something is wrong`() = runBlocking {
            coEvery { totvsMemberClient.postMember(requestMemberBatchRequest) } returns Exception("Erro!")

            coEvery { nullvsIntegrationLogService.add(any()) } returns nullvsIntegrationLog.success()

            val result = service.routeMemberToIntegrationFlows(requestMemberBatchRequest)

            ResultAssert.assertThat(result).isFailureOfType(Exception::class)

            Assertions.assertThat(LocalProducer.hasEvent(NullvsMemberBatchResponseEvent.name)).isTrue

            coVerifyOnce { totvsMemberClient.postMember(requestMemberBatchRequest) }

            coVerifyOnce {
                nullvsIntegrationLogService.add(match {
                    it.eventId == meta.eventId &&
                            it.eventName == meta.eventName &&
                            it.integrationEventName == meta.integrationEventName &&
                            it.internalId == meta.internalId &&
                            it.internalModelName == meta.internalModelName &&
                            it.externalModelName == meta.externalModelName &&
                            it.batchType == BatchType.CREATE &&
                            it.description == "Erro!" &&
                            it.status == LogStatus.TOTVS_NOT_CALLED
                })
            }
        }

        @Test
        fun `#should produce a NullsMemberBatchResponseEvent on reactivation flow`() = runBlocking {

            val response = NullvsMemberBatchResponse(meta, TotvsMemberResponse("200", "00000001", "1", "I"))

            coEvery { totvsMemberClient.reactivateMember(nullvsMemberReactivationBatchRequest) } returns response

            service.routeMemberToIntegrationFlows(nullvsMemberReactivationBatchRequest)

            Assertions.assertThat(LocalProducer.hasEvent(NullvsMemberBatchResponseEvent.name)).isTrue

            coVerifyOnce { totvsMemberClient.reactivateMember(nullvsMemberReactivationBatchRequest) }

        }

        @Test
        fun `#shouldn't produce a NullvsMemberBatchResponseEvent when something is wrong on reactivation flow`() =
            runBlocking {

                val nullvsReactivationIntegrationLog = nullvsIntegrationLog.copy(batchType = BatchType.REACTIVATION)

                coEvery { totvsMemberClient.reactivateMember(nullvsMemberReactivationBatchRequest) } returns Exception("Erro!")
                coEvery { nullvsIntegrationLogService.add(any()) } returns nullvsReactivationIntegrationLog

                val result = service.routeMemberToIntegrationFlows(nullvsMemberReactivationBatchRequest)

                ResultAssert.assertThat(result).isFailureOfType(Exception::class)

                Assertions.assertThat(LocalProducer.hasEvent(NullvsMemberBatchResponseEvent.name)).isTrue

                coVerifyOnce { totvsMemberClient.reactivateMember(nullvsMemberReactivationBatchRequest) }

                coVerifyOnce {
                    nullvsIntegrationLogService.add(match {
                        it.eventId == meta.eventId &&
                                it.eventName == meta.eventName &&
                                it.integrationEventName == meta.integrationEventName &&
                                it.internalId == meta.internalId &&
                                it.internalModelName == meta.internalModelName &&
                                it.externalModelName == meta.externalModelName &&
                                it.batchType == BatchType.REACTIVATION &&
                                it.description == "Erro!" &&
                                it.status == LogStatus.TOTVS_NOT_CALLED
                    })
                }
            }

    }

    @Nested
    inner class RouteMemberToIntegrationFlowsCancelBeneficiary {

        private val member = TestModelFactory.buildMember(id = internalId)
        private val person = TestModelFactory.buildPerson(
            mothersName = "Mother's name", phoneNumber = "phone", dateOfBirth = LocalDateTime.of(1994, 10, 17, 14, 0),
        )

        private val requestMemberBatchRequest = NullvsMemberBatchRequest(
            meta,
            NullvsActionType.CANCEL,
            TotvsMemberRequest(
                company = TotvsGroupCompany.ALICE_INDIVIDUAL,
                client = "billing-accountable-party",
                createdAt = LocalDateTime.now(),
                ANSProductId = "ans-product-id",
                idPayload = 1,
                beneficiaries = listOf(
                    TotvsMemberRequest.TotvsBeneficiary(
                        userType = TotvsUser.HOLDER,
                        email = person.email,
                        fullName = person.fullRegisterName,
                        fullSocialName = person.fullSocialName,
                        aliceId = member.id,
                        nationalId = person.nationalId.onlyNumbers(),
                        identityDocument = person.identityDocument,
                        mothersName = person.mothersName!!,
                        addressPostalCode = person.mainAddress!!.postalCode!!.onlyDigits(),
                        addressStreet = person.mainAddress!!.street,
                        addressNumber = person.mainAddress!!.number,
                        addressComplement = person.mainAddress!!.complement,
                        addressNeighborhood = person.mainAddress!!.neighbourhood!!,
                        addressCity = TotvsCityCode("3550308", "SAO PAULO", "SP"),
                        addressState = person.mainAddress!!.state.toString(),
                        phoneNumber = person.phoneNumber!!,
                        dateOfBirth = person.dateOfBirth!!,
                        createdAt = person.createdAt,
                        parentBeneficiaryRelationType = TotvsRelationType.HOLDER,
                        sex = Sex.FEMALE,
                        cnsNumber = person.cnsNumber,
                        ccoCodeANS = null,
                        ANSProductId = "ans-id",
                        canceledReason = null,
                        canceledAt = null,
                        cpts = listOf(
                            TotvsMemberRequest.TotvsBeneficiary.CPT(
                                cid = "XPTO",
                                startedAt = LocalDate.of(2023, 6, 1),
                                periodInDays = 730L
                            )
                        ),
                        gracePeriods = listOf(
                            TotvsMemberRequest.TotvsBeneficiary.GracePeriod(
                                type = GracePeriodType.BIRTH,
                                startedAt = LocalDate.of(2023, 6, 1),
                                periodInDays = 300L
                            )
                        ),
                    ),
                ),
            ),
        )

        @Test
        fun `#should produce a NullvsMemberBatchResponseEvent`() = runBlocking {
            val response = NullvsMemberBatchResponse(meta, TotvsMemberResponse("200", "00000001", "1", "C"))

            coEvery { totvsMemberClient.postMember(requestMemberBatchRequest) } returns response

            service.routeMemberToIntegrationFlows(requestMemberBatchRequest)

            Assertions.assertThat(LocalProducer.hasEvent(NullvsMemberBatchResponseEvent.name)).isTrue

            coVerifyOnce { totvsMemberClient.postMember(requestMemberBatchRequest) }
        }

        @Test
        fun `#shouldn't produce a NullvsMemberBatchResponseEvent when something is wrong`() = runBlocking {
            coEvery { totvsMemberClient.postMember(requestMemberBatchRequest) } returns Exception("Erro!")

            coEvery { nullvsIntegrationLogService.add(any()) } returns nullvsIntegrationLog.success()

            val result = service.routeMemberToIntegrationFlows(requestMemberBatchRequest)

            ResultAssert.assertThat(result).isFailureOfType(Exception::class)

            Assertions.assertThat(LocalProducer.hasEvent(NullvsMemberBatchResponseEvent.name)).isTrue

            coVerifyOnce { totvsMemberClient.postMember(requestMemberBatchRequest) }

            coVerifyOnce {
                nullvsIntegrationLogService.add(match {
                    it.eventId == meta.eventId &&
                            it.eventName == meta.eventName &&
                            it.integrationEventName == meta.integrationEventName &&
                            it.internalId == meta.internalId &&
                            it.internalModelName == meta.internalModelName &&
                            it.externalModelName == meta.externalModelName &&
                            it.batchType == BatchType.CANCEL &&
                            it.description == "Erro!" &&
                            it.status == LogStatus.TOTVS_NOT_CALLED
                })
            }
        }
    }

    @Nested
    inner class RouteMemberToIntegrationFlowsUpdateBeneficiary {

        private val member = TestModelFactory.buildMember()
        private val person = TestModelFactory.buildPerson(
            mothersName = "Mother's name",
            phoneNumber = "phone",
            dateOfBirth = LocalDateTime.of(1994, 10, 17, 14, 0)
        )

        private val requestMemberBatchRequest = NullvsMemberBatchRequest(
            meta,
            NullvsActionType.UPDATE,
            TotvsMemberRequest(
                company = TotvsGroupCompany.ALICE_INDIVIDUAL,
                client = "billing-accountable-party",
                createdAt = LocalDateTime.now(),
                ANSProductId = "ans-product-id",
                idPayload = 1,
                beneficiaries = listOf(
                    TotvsMemberRequest.TotvsBeneficiary(
                        userType = TotvsUser.HOLDER,
                        email = person.email,
                        fullName = person.fullRegisterName,
                        fullSocialName = person.fullSocialName,
                        aliceId = member.id,
                        nationalId = person.nationalId.onlyNumbers(),
                        identityDocument = person.identityDocument,
                        mothersName = person.mothersName!!,
                        addressPostalCode = person.mainAddress!!.postalCode!!.onlyDigits(),
                        addressStreet = person.mainAddress!!.street,
                        addressNumber = person.mainAddress!!.number,
                        addressComplement = person.mainAddress!!.complement,
                        addressNeighborhood = person.mainAddress!!.neighbourhood!!,
                        addressCity = TotvsCityCode("3550308", "SAO PAULO", "SP"),
                        addressState = person.mainAddress!!.state.toString(),
                        phoneNumber = person.phoneNumber!!,
                        dateOfBirth = person.dateOfBirth!!,
                        createdAt = person.createdAt,
                        parentBeneficiaryRelationType = TotvsRelationType.HOLDER,
                        sex = Sex.FEMALE,
                        cnsNumber = person.cnsNumber,
                        ccoCodeANS = null,
                        ANSProductId = "ans-id",
                        canceledAt = null,
                        canceledReason = null,
                        cpts = listOf(
                            TotvsMemberRequest.TotvsBeneficiary.CPT(
                                cid = "XPTO",
                                startedAt = LocalDate.of(2023, 6, 1),
                                periodInDays = 730L
                            )
                        ),
                        gracePeriods = listOf(
                            TotvsMemberRequest.TotvsBeneficiary.GracePeriod(
                                type = GracePeriodType.BIRTH,
                                startedAt = LocalDate.of(2023, 6, 1),
                                periodInDays = 300L
                            )
                        ),
                    ),
                ),
            ),
        )

        @Test
        fun `#should produce a NullvsMemberBatchResponseEvent`() = runBlocking {
            val response = NullvsMemberBatchResponse(meta, TotvsMemberResponse("200", "00000001", "1", "I"))
            val currentLog = TestModelFactory.buildNullvsIntegrationLog()
            val currentLogModel = currentLog.toModel()

            coEvery { totvsMemberClient.postMember(requestMemberBatchRequest) } returns response
            coEvery {
                nullvsIntegrationLogService.findCurrentLogByInternalIdAndModel(
                    requestMemberBatchRequest.metadata.internalId,
                    requestMemberBatchRequest.metadata.internalModelName
                )
            } returns currentLogModel


            service.routeMemberToIntegrationFlows(requestMemberBatchRequest)

            Assertions.assertThat(LocalProducer.hasEvent(NullvsMemberBatchResponseEvent.name)).isTrue

            coVerifyOnce { totvsMemberClient.postMember(requestMemberBatchRequest) }
            coVerifyOnce { nullvsIntegrationLogService.findCurrentLogByInternalIdAndModel(any(), any()) }
        }

        @Test
        fun `#should produce a NullvsMemberBatchResponseEvent even when it has the same hash but the status log is failed`() =
            runBlocking {
                val response = NullvsMemberBatchResponse(meta, TotvsMemberResponse("200", "00000001", "1", "I"))
                val currentLog = TestModelFactory.buildNullvsIntegrationLog()
                    .copy(
                        hash = requestMemberBatchRequest.totvsMemberRequest.generateHash(),
                        status = LogStatus.FAILURE,
                    )
                val currentLogModel = currentLog.toModel()

                coEvery { totvsMemberClient.postMember(requestMemberBatchRequest) } returns response
                coEvery {
                    nullvsIntegrationLogService.findCurrentLogByInternalIdAndModel(
                        requestMemberBatchRequest.metadata.internalId,
                        requestMemberBatchRequest.metadata.internalModelName
                    )
                } returns currentLogModel


                service.routeMemberToIntegrationFlows(requestMemberBatchRequest)

                Assertions.assertThat(LocalProducer.hasEvent(NullvsMemberBatchResponseEvent.name)).isTrue

                coVerifyOnce { totvsMemberClient.postMember(requestMemberBatchRequest) }
                coVerifyOnce { nullvsIntegrationLogService.findCurrentLogByInternalIdAndModel(any(), any()) }
            }

        @Test
        fun `#shouldn't produce a NullvsMemberBatchResponseEvent when something is wrong`() = runBlocking {
            coEvery { totvsMemberClient.postMember(requestMemberBatchRequest) } returns Exception("")
            val currentLog = TestModelFactory.buildNullvsIntegrationLog()
            val currentLogModel = currentLog.toModel()

            coEvery {
                nullvsIntegrationLogService.findCurrentLogByInternalIdAndModel(
                    requestMemberBatchRequest.metadata.internalId,
                    requestMemberBatchRequest.metadata.internalModelName
                )
            } returns currentLogModel
            coEvery { totvsMemberClient.postMember(requestMemberBatchRequest) } returns Exception("Erro!")

            coEvery { nullvsIntegrationLogService.add(any()) } returns nullvsIntegrationLog.success()

            val result = service.routeMemberToIntegrationFlows(requestMemberBatchRequest)

            ResultAssert.assertThat(result).isFailureOfType(Exception::class)

            Assertions.assertThat(LocalProducer.hasEvent(NullvsMemberBatchResponseEvent.name)).isTrue

            coVerifyOnce { totvsMemberClient.postMember(requestMemberBatchRequest) }

            coVerifyOnce {
                nullvsIntegrationLogService.add(match {
                    it.eventId == meta.eventId &&
                            it.eventName == meta.eventName &&
                            it.integrationEventName == meta.integrationEventName &&
                            it.internalId == meta.internalId &&
                            it.internalModelName == meta.internalModelName &&
                            it.externalModelName == meta.externalModelName &&
                            it.batchType == BatchType.UPDATE &&
                            it.description == "Erro!" &&
                            it.status == LogStatus.TOTVS_NOT_CALLED
                })
            }

            coVerifyOnce { totvsMemberClient.postMember(requestMemberBatchRequest) }
            coVerifyOnce { nullvsIntegrationLogService.findCurrentLogByInternalIdAndModel(any(), any()) }
        }


        @Test
        fun `#shouldn't produce a NullvsMemberBatchResponseEvent when the hash is same`() = runBlocking {
            val currentLog = TestModelFactory.buildNullvsIntegrationLog()
                .copy(hash = requestMemberBatchRequest.totvsMemberRequest.generateHash())
            val currentLogModel = currentLog.toModel()

            coEvery {
                nullvsIntegrationLogService.findCurrentLogByInternalIdAndModel(
                    requestMemberBatchRequest.metadata.internalId,
                    requestMemberBatchRequest.metadata.internalModelName
                )
            } returns currentLogModel

            val result = service.routeMemberToIntegrationFlows(
                requestMemberBatchRequest
            )

            Assertions.assertThat(result.get().toString().toBoolean()).isFalse

            coVerifyNone { totvsMemberClient.postMember(requestMemberBatchRequest) }
            coVerifyOnce { nullvsIntegrationLogService.findCurrentLogByInternalIdAndModel(any(), any()) }
        }
    }

}
