openapi: 3.0.3
info:
  title: Swagger Health Logics API - OpenApi
  description: This is the Health Logics API documentation
  version: 1.0.0
servers:
  - url: https://health-logics-api.wonderland.engineering
    description: Production Environment
  - url: https://health-logics-api.staging.wonderland.engineering
    description: Staging Environment
  - url: https://health-logics-api-dev1.dev.wonderland.engineering
    description: Dev1 Environment
  - url: https://health-logics-api-dev2.dev.wonderland.engineering
    description: Dev2 Environment
tags:
  - name: Protocol
  - name: Node
  - name: Relationship
  - name: Template
  - name: Health form
  - name: File
  - name: Health condition
  - name: Outcome conf
paths:
  /health_logic/protocol/:
    post:
      tags:
        - Protocol
      summary: Create protocol
      description: Create protocol
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ProtocolRequest'
      responses:
        '200':
          description: successful operation
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ProtocolResponse'
        '400':
          description: Invalid status value
    get:
      tags:
        - Protocol
      summary: Get protocols
      description: Get protocols
      parameters:
        - name: filter
          in: query
          description: Filter parameters
          required: false
          schema:
            type: object
            properties:
              q:
                type: string
                description: string that can be based in protocol title, health conditions or related terms
              range:
                type: string
                description: range to paginate
      responses:
        '200':
          description: successful operation
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/ProtocolSimpleResponse'
        '400':
          description: Invalid status value
  /health_logic/protocol/{id}/:
    put:
      tags:
        - Protocol
      summary: Update protocol
      description: Update protocol
      parameters:
        - name: id
          in: path
          description: Protocol Id
          required: true
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ProtocolRequest'
      responses:
        '200':
          description: successful operation
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ProtocolResponse'
        '400':
          description: Invalid status value
    get:
      tags:
        - Protocol
      summary: Get protocol
      description: Get protocol
      parameters:
        - name: id
          in: path
          description: Protocol Id
          required: true
          schema:
            type: string
      responses:
        '200':
          description: successful operation
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ProtocolResponse'
        '400':
          description: Invalid status value
  /health_logic/protocol/staff_roles}/:
    get:
      tags:
        - Protocol
      summary: Get staffs roles
      description: Get staffs roles
      parameters:
        - name: query
          in: path
          description: Staff role
          required: true
          schema:
            type: string
        - name: filter
          in: query
          description: Filter parameters
          required: false
          schema:
            type: object
            properties:
              levels:
                type: array
                items:
                  type: string
                  enum: [PRIMARY, SECONDARY, TERTIARY]
      responses:
        '200':
          description: successful operation
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/StaffResponse'
        '400':
          description: Invalid status value
  /health_logic/protocol/targets}/:
    get:
      tags:
        - Protocol
      summary: Get protocol targets
      description: Get protocol targets
      parameters:
        - name: query
          in: path
          description: Protocol targets
          required: true
          schema:
            type: array
            items:
              type: string
              enum: [INFANCY, EARLY_CHILDHOOD, MIDDLE_CHILDHOOD, ADOLESCENCE, ADULT, ELDERLY, FEMALE_BIOLOGICAL_SEX, MALE_BIOLOGICAL_SEX, PREGNANT, ALL]
      responses:
        '200':
          description: successful operation
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/TargetResponse'
  /health_logic/protocol/node/:
    post:
      tags:
        - Node
      summary: Create node
      description: Create node
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ServiceScriptNodeRequest'
      responses:
        '200':
          description: successful operation
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ServiceScriptNodeResponse'
        '400':
          description: Invalid status value
  /health_logic/protocol/node/{id}/:
    put:
      tags:
        - Node
      summary: Update node
      description: Update node
      parameters:
        - name: id
          in: path
          description: Node id
          required: true
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ServiceScriptNodeRequest'
      responses:
        '200':
          description: successful operation
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ServiceScriptNodeResponse'
        '400':
          description: Invalid status value
    get:
      tags:
        - Node
      summary: Get start tree nodes
      description: Get start tree nodes
      parameters:
        - name: id
          in: path
          description: Root node id
          required: true
          schema:
            type: string
      responses:
        '200':
          description: successful operation
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/BudNodeRelationshipResponse'
        '400':
          description: Invalid status value
  /health_logic/protocol/node/{id}/details:
    get:
      tags:
        - Node
      summary: Get node details
      description: Get node details
      parameters:
        - name: id
          in: path
          description: Node id
          required: true
          schema:
            type: string
      responses:
        '200':
          description: successful operation
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ServiceScriptNodeResponse'
        '400':
          description: Invalid status value
  /health_logic/protocol/node/{id}/option:
    get:
      tags:
        - Node
      summary: Get node options
      description: Get node option
      parameters:
        - name: id
          in: path
          description: Node id
          required: true
          schema:
            type: string
      responses:
        '200':
          description: successful operation
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/OptionResponse'
        '400':
          description: Invalid status value
  /health_logic/protocol/relationship/:
    post:
      tags:
        - Relationship
      summary: Create relationship
      description: Create relationship
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ServiceScriptRelationshipRequest'
      responses:
        '200':
          description: successful operation
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ServiceScriptRelationshipResponse'
        '400':
          description: Invalid status value
  /health_logic/protocol/relationship/{id}:
    put:
      tags:
        - Relationship
      summary: Update relationship
      description: Update relationship
      parameters:
        - name: id
          in: path
          description: Relationship id
          required: true
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ServiceScriptRelationshipRequest'
      responses:
        '200':
          description: successful operation
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ServiceScriptRelationshipResponse'
        '400':
          description: Invalid status value
    get:
      tags:
        - Relationship
      summary: Get relationship
      description: Get relationship
      parameters:
        - name: id
          in: path
          description: Relationship id
          required: true
          schema:
            type: string
      responses:
        '200':
          description: successful operation
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ServiceScriptRelationshipResponse'
        '400':
          description: Invalid status value
  /health_logic/template/:
    get:
      tags:
        - Template
      summary: Get templates
      description: Get templates
      parameters:
        - name: range
          in: query
          description: Range to paginate [start, end]
          required: false
          schema:
            type: array
            items:
              type: integer
        - name: sort
          in: query
          description: value to sort [field, order]
          required: false
          schema:
            type: array
            items:
              type: string
        - name: filter
          in: query
          description: Filter parameters
          required: false
          schema:
            type: object
            properties:
              title:
                type: string
              description:
                type: string
              type:
                $ref: "#/components/schemas/TemplateType"
              active:
                type: boolean
      responses:
        '200':
          description: successful operation
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/HealthPlanTaskTemplateResponse'
        '400':
          description: Invalid status value
  /health_logic/health_form/:
    get:
      tags:
        - Health form
      summary: Get health forms
      description: Get health forms
      parameters:
        - name: range
          in: query
          description: Range to paginate [start, end]
          required: false
          schema:
            type: array
            items:
              type: integer
        - name: sort
          in: query
          description: value to sort [field, order]
          required: false
          schema:
            type: array
            items:
              type: string
        - name: filter
          in: query
          description: Filter parameters
          required: false
          schema:
            type: object
            properties:
              id:
                type: array
                items:
                  type: string
                  format: uuid
              q:
                type: string
                description: form name
              outcome_conf_id:
                type: string
                format: uuid
      responses:
        '200':
          description: successful operation
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/HealthForm'
        '400':
          description: Invalid status value
  /health_logic/health_form/bud/valid:
    get:
      tags:
        - Health form
      summary: Get valid health forms
      description: Get valid health forms
      parameters:
        - name: sort
          in: query
          description: value to sort [field, order]
          required: false
          schema:
            type: array
            items:
              type: string
        - name: filter
          in: query
          description: Filter parameters
          required: false
          schema:
            type: object
            properties:
              q:
                type: string
                description: form name
      responses:
        '200':
          description: successful operation
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/HealthForm'
        '400':
          description: Invalid status value
  /health_logic/upload/file:
    post:
      tags:
        - File
      summary: Upload file
      description: Upload file
      requestBody:
        content:
          multipart/form-data:
            schema:
              type: object
              properties:
                parameters:
                  type: object
                  additionalProperties:
                    type: string
                file_content:
                  type: string
                  format: binary
                file:
                  type: string
                  format: binary
      responses:
        '200':
          description: successful operation
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/FileUploadResponse'
  /health_logic/file/{id}:
    get:
      tags:
        - File
      summary: Get file
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: string
            format: uuid
      responses:
        '200':
          description: successful operation
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/VaultResponse'
  /health_logic/health_condition_search/:
    get:
      tags:
        - Health condition
      summary: Get health condition
      description: Get health condition
      parameters:
        - name: query
          in: query
          required: true
          description: Health condition query
          schema:
            type: string
        - name: types
          in: query
          required: false
          schema:
            type: array
            items:
              $ref: '#/components/schemas/HealthConditionClassification'
      responses:
        '200':
          description: successful operation
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/HealthConditionResponse'
  /health_logic/outcome_conf/:
    get:
      tags:
        - Outcome conf
      summary: Get outcome conf
      description: Get outcome conf
      parameters:
        - name: filter
          in: query
          description: Filter parameters
          required: false
          schema:
            type: object
            properties:
              q:
                type: string
                description: outcome conf query
      responses:
        '200':
          description: successful operation
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/OutcomeConfResponse'

components:
  schemas:
    ProtocolRequest:
      type: object
      properties:
        health_condition_ids:
          type: array
          items:
            type: string
        root_node_id:
          type: string
        title:
          type: string
        metadata:
          $ref: '#/components/schemas/ProtocolMetadata'
        version:
          type: integer
        status:
          type: string
          enum: [ACTIVE, INACTIVE]
        related_terms:
          type: array
          items:
            type: string
        type_of_service:
          type: array
          items:
            type: string
            enum: [ACUTE, LONGITUDINAL]
        category:
          type: string
          enum: [ADMINISTRATIVE, ASSISTANCE, SCREENING]
        attention_levels:
          type: array
          items:
            type: string
            enum: [PRIMARY, SECONDARY, TERTIARY]
        targets:
          type: array
          items:
            type: string
            enum: [INFANCY, EARLY_CHILDHOOD, MIDDLE_CHILDHOOD, ADOLESCENCE, ADULT, ELDERLY, FEMALE_BIOLOGICAL_SEX, MALE_BIOLOGICAL_SEX, PREGNANT, ALL]
        type_of_material:
          type: string
          enum: [CARE_LINE, PROTOCOL, SOP]
        staff_roles:
          type: array
          items:
            type: string
      required:
        - title
        - metadata
        - status
        - category
        - type_of_material
    ProtocolMetadata:
      type: object
      properties:
        health_condition_codes:
          type: array
          items:
            type: string
        health_condition_names:
          type: array
          items:
            type: string
      required:
        - health_condition_codes
        - health_condition_names
    ProtocolStatus:
      type: object
      properties:
        key:
          type: string
          enum: [ACTIVE, INACTIVE]
      required:
        - health_condition_codes
    ProtocolResponse:
      type: object
      properties:
        id:
          type: string
        health_condition_ids:
          type: array
          items:
            type: string
        root_node_id:
          type: string
        title:
          type: string
        metadata:
          $ref: '#/components/schemas/ProtocolMetadata'
        attributes:
          $ref: '#/components/schemas/ProtocolAttributes'
        search_tokens:
          $ref: '#/components/schemas/TsVector'
        status:
          type: string
          enum: [ACTIVE, INACTIVE]
        related_terms:
          type: array
          items:
            type: string
        type_of_service:
          type: array
          items:
            type: string
            enum: [ ACUTE, LONGITUDINAL ]
        category:
          type: string
          enum: [ ADMINISTRATIVE, ASSISTANCE, SCREENING ]
        attention_levels:
          type: array
          items:
            type: string
            enum: [ PRIMARY, SECONDARY, TERTIARY ]
        targets:
          type: array
          items:
            type: string
            enum: [ INFANCY, EARLY_CHILDHOOD, MIDDLE_CHILDHOOD, ADOLESCENCE, ADULT, ELDERLY, FEMALE_BIOLOGICAL_SEX, MALE_BIOLOGICAL_SEX, PREGNANT, ALL ]
        type_of_material:
          type: string
          enum: [ CARE_LINE, PROTOCOL, SOP ]
        staff_roles:
          type: array
          items:
            type: string
        version:
          type: integer
        created_at:
          type: string
        updated_at:
          type: string
    ProtocolAttributes:
      type: object
      properties:
        health_conditions:
          type: array
          items:
            $ref: '#/components/schemas/HealthConditionLight'
    HealthConditionLight:
      type: object
      properties:
        code:
          type: string
        name:
          type: string
        code_type:
          type: string
          enum: [CID_10, CIAP_2, CIPE, GOAL, FREE_TEXT, SYMPTOM]
      required:
        - code
        - name
        - code_type
    TsVector:
      type: object
      properties:
        value:
          type: string
        weight:
          type: string
          enum: [A, B, C, D]
          default: D
    ProtocolSimpleResponse:
      type: object
      properties:
        id:
          type: string
        title:
          type: string
        is_active:
          type: boolean
        category:
          type: string
          enum: [ ADMINISTRATIVE, ASSISTANCE, SCREENING ]
        attention_levels:
          type: array
          items:
            type: string
            enum: [ PRIMARY, SECONDARY, TERTIARY ]
        type_of_material:
          type: string
          enum: [ CARE_LINE, PROTOCOL, SOP ]
        created_at:
          type: string
        updated_at:
          type: string
      required:
        - id
        - title
        - is_active
        - category
        - created_at
        - updated_at
    StaffResponse:
      type: object
      properties:
        label:
          type: string
          description: role description
        value:
          type: string
          description: role name
      required:
        - label
        - value
    TargetResponse:
      type: object
      properties:
        label:
          type: string
          description: target description
        value:
          type: string
          description: target name
      required:
        - label
        - value
    ServiceScriptNodeRequest:
      type: object
      properties:
        name:
          type: string
        internal_orientation:
          type: string
        content:
          type: string
        type:
          type: string
          enum: [CATEGORY, SUB_CATEGORY, BUS, SCRIPT, QUESTION, ACTION, QUESTION_WITH_OPTIONS, EXTERNAL_VALIDATION, EXTERNAL_OPTIONS]
        status:
          type: string
          enum: [ACTIVE, INACTIVE, DELETED]
        private_orientation:
          type: boolean
          default: false
        actions:
          type: array
          items:
            $ref: '#/components/schemas/ScriptActionRequest'
        root_node_id:
          type: string
        files:
          $ref: '#/components/schemas/BudNodeFile'
        staff_roles:
          type: array
          items:
            type: string
        service_script_actions:
          type: array
          items:
            $ref: '#/components/schemas/ServiceScriptActionRequest'
        member_friendly_content:
          type: string
        member_friendly_sub_content:
          type: string
        option:
          $ref: '#/components/schemas/OptionRequest'
        external_source:
          type: string
          enum: [HEALTH_PLAN_TASK_PRESCRIPTION, SEND_PRESCRIPTION, DURATION_PRESCRIPTION, APPOINTMENT_SCHEDULE_FIRST_OCCURRENCE]
        version:
          type: integer
      required:
        - name,
        - content
        - type
        - status
    ScriptActionRequest:
      type: object
      properties:
        id:
          type: string
        type:
          type: string
          enum: [PERSON_HEALTH_EVENT, TEST_REQUEST_TASK, REFERRAL_TASK, HEALTH_LOGIC_RECORD, QUESTIONNAIRE, MEDICAL_DISCHARGE_RECOMMENDATION, FINISH_NAVIGATION]
        content:
          type: object
      required:
        - type
    BudNodeFile:
      type: object
      properties:
        name:
          type: string
        type:
          type: string
        url:
          type: string
      required:
        - name
        - type
        - url
    ServiceScriptActionRequest:
      type: object
      properties:
        type:
          type: string
          enum: [HEALTH_PLAN_TASK_TEMPLATE, HEALTH_PLAN_TASK_GROUP_TEMPLATE, HEALTH_CONDITION, SYMPTOM, WANDA_PHYSICAL_PRESCRIPTION, WANDA_NOT_PHYSICAL_PRESCRIPTION, WANDA_NOT_PRESCRIPTION]
        external_id:
          type: string
        status:
          type: string
          enum: [ACTIVE, INACTIVE]
          default: ACTIVE
        title:
          type: string
        template_type:
          $ref: "#/components/schemas/TemplateType"
      required:
        - type
        - external_id
    OptionRequest:
      type: object
      properties:
        selection_type:
          type: string
          enum: [SINGLE, MULTIPLE, NONE]
        type:
          type: string
          enum: [HEALTH_CONDITION, FREE_TEXT, NUMBER, SYMPTOMS, CALENDAR, INPUT_TEXT, CALENDAR_ALL_DATES]
        answers:
          type: array
          items:
            $ref: '#/components/schemas/AnswerRequest'
      required:
        - selection_type
        - type
    AnswerRequest:
      type: object
      properties:
        title:
          type: string
        external_id:
          type: string
        value:
          type: string
        id:
          type: string
      required:
        - title
    ServiceScriptNodeResponse:
      type: object
      properties:
        id:
          type: string
        name:
          type: string
        internal_orientation:
          type: string
        content:
          type: string
        type:
          type: string
          enum: [ CATEGORY, SUB_CATEGORY, BUS, SCRIPT, QUESTION, ACTION, QUESTION_WITH_OPTIONS, EXTERNAL_VALIDATION, EXTERNAL_OPTIONS ]
        status:
          type: string
          enum: [ ACTIVE, INACTIVE, DELETED ]
        private_orientation:
          type: boolean
          default: false
        actions:
          type: array
          items:
            $ref: '#/components/schemas/ScriptActionRequest'
        root_node_id:
          type: string
        files:
          $ref: '#/components/schemas/BudNodeFile'
        staff_roles:
          type: array
          items:
            type: string
        service_script_actions:
          type: array
          items:
            $ref: '#/components/schemas/ServiceScriptActionRequest'
        member_friendly_content:
          type: string
        member_friendly_sub_content:
          type: string
        option:
          $ref: '#/components/schemas/OptionRequest'
        external_source:
          type: string
          enum: [ HEALTH_PLAN_TASK_PRESCRIPTION, SEND_PRESCRIPTION, DURATION_PRESCRIPTION, APPOINTMENT_SCHEDULE_FIRST_OCCURRENCE ]
        version:
          type: integer
        attention_levels:
          type: array
          items:
            type: string
            enum: [PRIMARY, SECONDARY, TERTIARY]
      required:
        - id
        - name
        - content
        - type
        - status
    BudNodeRelationshipResponse:
      type: object
      properties:
        bud_node:
          $ref: '#/components/schemas/BudNodeInformation'
        has_children:
          type: boolean
        relationships:
          type: array
          items:
            $ref: '#/components/schemas/ServiceScriptRelationship'
      required:
        - bud_node
        - has_children
        - relationships
    BudNodeInformation:
      type: object
      properties:
        id:
          type: string
        name:
          type: string
        type:
          type: string
          enum: [CATEGORY, SUB_CATEGORY, BUS, SCRIPT, QUESTION, ACTION, QUESTION_WITH_OPTIONS, EXTERNAL_VALIDATION, EXTERNAL_OPTIONS]
        content:
          type: string
        staff_roles:
          type: array
          items:
            type: string
        has_files:
          type: boolean
        internal_orientation:
          type: string
      required:
        - id
        - name
        - type
        - content
        - has_files
    ServiceScriptRelationship:
      type: object
      properties:
        id:
          type: string
        name:
          type: string
        node_parent_id:
          type: string
        node_child_id:
          type: string
        status:
          type: string
          enum: [ACTIVE, INACTIVE, DELETED]
        conditions:
          type: array
          items:
            $ref: '#/components/schemas/Condition'
        priority:
          type: integer
        version:
          type: integer
      required:
        - id
        - name
        - status
    Condition:
      type: object
      properties:
        key:
          type: string
        operator:
          type: string
          enum: [EQUALITY, NOT_EQUALS, CONTAINS, RANGE, IN_LIST, AND, OR]
        value:
          $ref: '#/components/schemas/Any'
        join_condition_type:
          type: string
          enum: [OUTCOME, DEMAND]
    Any:
      description: 'Can be anything: string, number, array, object, etc.'
    OptionResponse:
      type: object
      properties:
        option:
          $ref: '#/components/schemas/Option'
    Option:
      type: object
      properties:
        selection_type:
          type: string
          enum: [ SINGLE, MULTIPLE, NONE ]
        type:
          type: string
          enum: [ HEALTH_CONDITION, FREE_TEXT, NUMBER, SYMPTOMS, CALENDAR, INPUT_TEXT, CALENDAR_ALL_DATES ]
        answers:
          type: array
          items:
            $ref: '#/components/schemas/Answer'
      required:
        - selection_type
        - type
    Answer:
      type: object
      properties:
        title:
          type: string
        external_id:
          type: string
        value:
          type: string
        id:
          type: string
      required:
        - title
        - id
    ServiceScriptRelationshipRequest:
      type: object
      properties:
        name:
          type: string
        node_parent_id:
          type: string
        node_child_id:
          type: string
        status:
          type: string
          enum: [ ACTIVE, INACTIVE, DELETED ]
        priority:
          type: integer
        version:
          type: integer
        demands_with_seriousness:
            $ref: '#/components/schemas/ConditionResponse'
            description: use DemandItems as items
        outcomes:
          $ref: '#/components/schemas/ConditionResponse'
          description: use OutcomeItems as items
        symptoms:
          $ref: '#/components/schemas/ConditionResponse'
          description: use SymptomItems as items
        bud_node_options:
          $ref: '#/components/schemas/ConditionResponse'
          description: use NodeOptionItems as items
        text:
          $ref: '#/components/schemas/TextCondition'
        age:
          $ref: '#/components/schemas/AgeCondition'
      required:
        - name
        - status
    ConditionResponse:
      type: object
      properties:
        items:
          type: array
          items:
            oneOf:
              - $ref: '#/components/schemas/DemandItems'
              - $ref: '#/components/schemas/OutcomeItems'
              - $ref: '#/components/schemas/SymptomItems'
              - $ref: '#/components/schemas/NodeOptionItems'
        operator:
          type: string
          enum: [EQUALITY, NOT_EQUALS, CONTAINS, RANGE, IN_LIST, AND, OR]
      required:
        - items
        - operator
    DemandItems:
      type: object
      properties:
        id:
          type: string
        description:
          type: string
        seriousness:
          type: string
          enum: [LOW, MEDIUM, HIGH]
      required:
        - id
        - description
    OutcomeItems:
      type: object
      properties:
        id:
          type: string
        description:
          type: string
        min_value:
          type: integer
        max_value:
          type: integer
      required:
        - id
        - description
        - min_value
        - max_value
    SymptomItems:
      type: object
      properties:
        id:
          type: string
        description:
          type: string
      required:
        - id
        - description
    NodeOptionItems:
      type: object
      properties:
        id:
          type: string
        description:
          type: string
      required:
        - id
        - description
    TextCondition:
      type: object
      properties:
        value:
          type: string
        operator:
          type: string
          enum: [EQUALITY, NOT_EQUALS, CONTAINS, RANGE, IN_LIST, AND, OR]
      required:
        - value
        - operator
    AgeCondition:
      type: object
      properties:
        min_value:
          type: integer
        max_value:
          type: integer
    ServiceScriptRelationshipResponse:
      allOf:
        - $ref: "#/components/schemas/ServiceScriptRelationshipRequest"
        - type: object
          properties:
            id:
              type: string
          required:
            - id
            - priority
            - version
    TemplateType:
      type: string
      enum: [PRESCRIPTION, EATING, PHYSICAL, SLEEP, MOOD, OTHERS, TEST, REFERRAL, EMERGENCY, SCHEDULING, FOLLOW, SURGERY]
    HealthPlanTaskTemplateResponse:
      type: object
      properties:
        id:
          type: string
          format: uuid
        type:
          $ref: '#/components/schemas/TemplateType'
        title:
          type: string
        description:
          type: string
      required:
        - id
        - type
        - title
    HealthForm:
      type: object
      properties:
        name:
          type: string
        key:
          type: string
        type:
          type: string
          enum:
            - HEALTH
            - CSAT
        version:
          type: integer
        created_at:
          type: string
          format: date-time
        updated_at:
          type: string
          format: date-time
        id:
          type: string
          format: uuid
        updatedBy:
          $ref: '#/components/schemas/UpdatedBy'
      required:
        - name
        - key
        - type
    UpdatedBy:
      type: object
      properties:
        user_type:
          type: string
        user_id:
          type: string
        environment_name:
          type: string
      required:
        - user_type
        - user_id
        - environment_name
    FileUploadResponse:
      type: object
      properties:
        id:
          type: string
          format: uuid
        url:
          type: string
      required:
        - id
        - url
    VaultResponse:
      type: object
      properties:
        url:
          type: string
        id:
          type: string
          format: uuid
        type:
          type: string
        vault_url:
          type: string
        file_name:
          type: string
        file_size:
          type: integer
      required:
        - url
        - id
        - type
        - vault_url
    HealthConditionResponse:
      type: object
      properties:
        id:
          type: string
          format: uuid
        code:
          type: string
        name:
          type: string
        description:
          type: string
        score:
          type: integer
        classification:
          $ref: '#/components/schemas/HealthConditionClassification'
        is_enriched:
          type: boolean
      required:
        - id
        - code
        - name
        - description
        - classification
        - is_enriched
    HealthConditionClassification:
      type: string
      enum:
        - CID
        - CIAP
        - GOAL
        - CIPE
        - SYMPTOM
    OutcomeConfResponse:
      type: object
      properties:
        id:
          type: string
          format: uuid
        key:
          type: string
        description:
          type: string
        status:
          $ref: '#/components/schemas/OutcomeStatus'
      required:
        - id
        - key
        - description
        - status
    OutcomeStatus:
      type: string
      enum:
        - ACTIVE
        - INACTIVE
