package br.com.alice.testresult.services.impl

import br.com.alice.common.client.DefaultHttpClient
import br.com.alice.common.core.exceptions.InvalidArgumentException
import br.com.alice.common.extensions.coResultOf
import br.com.alice.data.layer.models.ProviderIntegration
import br.com.alice.dbintegrationclient.client.DbFileService
import br.com.alice.fhir.client.AttachmentType
import br.com.alice.fhir.client.GetAttachmentUrlRequest
import br.com.alice.fhir.client.ImageTestResultService
import br.com.alice.testresult.services.TestResultAttachmentService
import com.github.kittinunf.result.Result
import com.github.kittinunf.result.failure
import com.github.kittinunf.result.flatMap
import com.github.kittinunf.result.success
import io.ktor.client.HttpClient
import io.ktor.client.call.body
import io.ktor.client.request.get
import io.ktor.client.statement.HttpResponse

fun TestResultAttachmentHttpClient() = DefaultHttpClient(timeoutInMillis = 30_000)

class TestResultAttachmentServiceImpl(
    private val dbFileService: DbFileService,
    private val imageTestResultService: ImageTestResultService,
    private val httpClient: HttpClient
) : TestResultAttachmentService {
    override suspend fun getUrl(
        provider: ProviderIntegration,
        externalReference: String,
        externalId: String
    ): Result<String, Throwable> {
        return when (provider) {
            ProviderIntegration.DB -> {
                dbFileService.getUrl(externalId, listOf(externalReference))
            }
            ProviderIntegration.DASA -> {
                imageTestResultService.getAttachmentUrl(
                    GetAttachmentUrlRequest(
                        code = externalReference,
                        provider = ProviderIntegration.DASA
                    )
                ).flatMap {
                    if (it.type == AttachmentType.PDF) return it.url.success()
                    return TypeNotSupportedException().failure()
                }
            }
            else -> {
                ProviderNotSupportedException().failure()
            }
        }
    }

    override suspend fun getFile(provider: ProviderIntegration, url: String): Result<ByteArray, Throwable> {
        return when (provider) {
            ProviderIntegration.DB -> {
                dbFileService.getFile(url)
            }
            else -> {
                downloadFile(url)
            }
        }
    }

    private suspend fun downloadFile(url: String): Result<ByteArray, Throwable> =
        coResultOf {
            val httpResponse: HttpResponse = httpClient.get(url)
            httpResponse.body()
        }

    override fun providerHasFileIntegration(provider: ProviderIntegration): Boolean {
        return provider == ProviderIntegration.DASA
    }
}

class TypeNotSupportedException(
    message: String = "Attachment type not supported",
    code: String = "test_result_attachment_type_not_supported",
    cause: Throwable? = null
) : InvalidArgumentException(message, code, cause)

class ProviderNotSupportedException(
    message: String = "Provider integration not supported",
    code: String = "test_result_attachment_provider_not_supported",
    cause: Throwable? = null
) : InvalidArgumentException(message, code, cause)
