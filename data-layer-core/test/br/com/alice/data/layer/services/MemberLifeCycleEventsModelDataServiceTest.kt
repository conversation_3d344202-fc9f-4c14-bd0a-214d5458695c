package br.com.alice.data.layer.services

import br.com.alice.common.RangeUUID
import br.com.alice.common.data.dsl.matchers.ResultAssert.Companion.assertThat
import br.com.alice.data.layer.helpers.DataServiceTestHelper
import br.com.alice.data.layer.models.MemberLifeCycleEventsModel
import br.com.alice.data.layer.models.MemberLifecycleEventType
import br.com.alice.data.layer.models.MemberLifecycleReasonEvents
import br.com.alice.data.layer.tables.MemberLifeCycleEventsTable
import kotlinx.coroutines.runBlocking
import java.time.LocalDate
import java.util.UUID
import kotlin.test.BeforeTest
import kotlin.test.Test

class MemberLifeCycleEventsModelDataServiceTest : DataServiceTestHelper() {

    private val dataService = MemberLifeCycleEventsModelDataServiceClient(httpInvoker)
    private val event = buildMemberLifeCycleEvents()

    @BeforeTest
    fun setup() {
        truncate(MemberLifeCycleEventsTable::class)
    }

    @Test
    fun `#add new event`() = runBlocking<Unit> {
        assertThat(dataService.add(event)).isSuccess()
    }

    @Test
    fun `#get event`() = runBlocking {

        dataService.add(event)
        val retrievedEvent = dataService.get(event.id)

        assertThat(retrievedEvent).isSuccessWithDataIgnoringGivenFields(event, "createdAt", "updatedAt", "updatedBy")
    }

    @Test
    fun `#update event`() = runBlocking {

        val expectedObservation = "New Observation"
        val updateEvent = event.copy(observation = expectedObservation)

        dataService.add(event)

        assertThat(dataService.update(updateEvent)).isSuccess()
        assertThat(dataService.get(event.id)).isSuccessWithDataIgnoringGivenFields(updateEvent, "createdAt", "updatedAt", "updatedBy", "version")
    }


    @Test
    fun `#filter`() = runBlocking {
        val memberId = RangeUUID.generate()
        val event1 = buildMemberLifeCycleEvents(memberId = memberId)
        val event2 = buildMemberLifeCycleEvents(memberId = memberId, type = MemberLifecycleEventType.CANCEL)

        dataService.add(event1)
        dataService.add(event2)

        val resultWithMemberId = dataService.find { where { this.memberId.eq(memberId) } }
        assertThat(resultWithMemberId).isSuccessWithDataIgnoringGivenFields(listOf(event1, event2), "createdAt", "updatedAt", "updatedBy")
    }

    @Test
    fun `#list by members ids`(): Unit = runBlocking {

        val event1 = buildMemberLifeCycleEvents()
        val event2 = buildMemberLifeCycleEvents()
        val ids = listOf(event1.memberId, event2.memberId)

        assertThat(dataService.add(event1)).isSuccess()
        assertThat(dataService.add(event2)).isSuccess()

        val result1 = dataService.find { where { this.memberId.inList(ids) } }
        assertThat(result1).isSuccessWithDataIgnoringGivenFields(listOf(event1, event2), "createdAt", "updatedAt", "updatedBy")
    }

    private fun buildMemberLifeCycleEvents(
        reason: MemberLifecycleReasonEvents = MemberLifecycleReasonEvents.COURT_ORDER,
        observation: String? = "Observation",
        type: MemberLifecycleEventType = MemberLifecycleEventType.REACTIVATION,
        actionAt: LocalDate = LocalDate.now(),
        memberId: UUID = RangeUUID.generate(),
    ) = MemberLifeCycleEventsModel(
        reason = reason,
        observation = observation,
        type = type,
        actionAt = actionAt,
        memberId = memberId
    )
}
