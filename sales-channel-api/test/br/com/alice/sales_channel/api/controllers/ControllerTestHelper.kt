package br.com.alice.sales_channel.api.controllers

import br.com.alice.authentication.Authenticator
import br.com.alice.common.core.Status
import br.com.alice.common.helpers.RoutesTestHelper
import br.com.alice.data.layer.SALES_CHANNEL_API_ROOT_SERVICE_NAME
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.SalesAgent
import br.com.alice.data.layer.models.SalesFirmStaff
import br.com.alice.data.layer.models.SalesFirmStaffRole
import br.com.alice.featureconfig.core.FeaturePopulateService
import br.com.alice.sales_channel.api.module
import com.google.firebase.auth.FirebaseToken
import io.ktor.server.application.Application
import io.mockk.every
import io.mockk.mockk
import io.mockk.mockkObject
import org.koin.core.context.loadKoinModules
import org.koin.dsl.module
import java.util.UUID
import kotlin.test.BeforeTest

abstract class ControllerTestHelper : RoutesTestHelper() {

    protected val idToken = "idToken"

    // TODO: Use DL model after login migration
    protected val salesCompanyStaff = SalesFirmStaff(
        salesFirmId = UUID.randomUUID(),
        firstName = "Rogério",
        lastName = "Ceni",
        email = "<EMAIL>",
        status = Status.ACTIVE,
        role = SalesFirmStaffRole.MAIN_STAFF,
    )

    protected val salesAgent = TestModelFactory.buildSalesAgent()

    private val featurePopulateService: FeaturePopulateService = mockk()

    override val setupFunction: Application.() -> Unit = { module(dependencyInjectionModules = listOf(module)) }

    override val moduleFunction: Application.() -> Unit = {
        loadKoinModules(
            listOf(
                module,
                module(createdAtStart = true) { single { featurePopulateService } }
            )
        )
    }

    open fun authenticatedAsSalesFirmStaff(
        token: String,
        staff: SalesFirmStaff,
        requestFunction: RoutesTestHelper.() -> Unit
    ) {
        val firebaseToken: FirebaseToken = mockk()

        every { firebaseToken.uid } returns staff.email
        every { firebaseToken.email } returns staff.email
        every { firebaseToken.claims } returns mapOf(
            Authenticator.USER_TYPE_KEY to SalesFirmStaff::class.simpleName,
            Authenticator.USER_ID_KEY to staff.id.toString(),
            Authenticator.ROLES_KEY to "GENERIC_USER"
        )
        every { firebaseToken.picture } returns "http://url.to.image/"

        mockkObject(Authenticator) {
            every { Authenticator.verifySystemAccessToken(token) } returns null
            every { Authenticator.verifyIdToken(token) } returns firebaseToken
            every { Authenticator.verifyIdToken(token, false) } returns firebaseToken

            every { Authenticator.generateRootServiceToken(any()) } returns "environmentToken"

            authenticationToken = token
            requestFunction.invoke(this)
            authenticationToken = null
        }
    }

    open fun authenticatedAsSalesAgent(
        token: String,
        salesAgent: SalesAgent,
        requestFunction: RoutesTestHelper.() -> Unit
    ) {
        val firebaseToken: FirebaseToken = mockk()

        every { firebaseToken.uid } returns salesAgent.email
        every { firebaseToken.email } returns salesAgent.email
        every { firebaseToken.claims } returns mapOf(
            Authenticator.USER_TYPE_KEY to SalesAgent::class.simpleName,
            Authenticator.USER_ID_KEY to salesAgent.id.toString(),
            Authenticator.ROLES_KEY to "SALES_AGENT",
        )
        every { firebaseToken.picture } returns "http://url.to.image/"

        mockkObject(Authenticator) {
            every { Authenticator.verifySystemAccessToken(token) } returns null
            every { Authenticator.verifyIdToken(token) } returns firebaseToken
            every { Authenticator.verifyIdToken(token, false) } returns firebaseToken

            every { Authenticator.generateRootServiceToken(any()) } returns "environmentToken"

            authenticationToken = token
            requestFunction.invoke(this)
            authenticationToken = null
        }
    }

    @BeforeTest
    override fun setup() {
        super.setup()

        mockkObject(Authenticator)
        every { Authenticator.generateCustomToken(any(), any<String>()) } returns "idToken"
        every { Authenticator.generateRootServiceToken(SALES_CHANNEL_API_ROOT_SERVICE_NAME) } returns "abc"
    }
}
