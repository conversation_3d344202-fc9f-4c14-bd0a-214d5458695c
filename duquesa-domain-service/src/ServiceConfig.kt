import br.com.alice.common.core.BaseConfig
import com.typesafe.config.ConfigFactory
import io.ktor.server.config.HoconApplicationConfig

object ServiceConfig : BaseConfig(config = HoconApplicationConfig(ConfigFactory.load("application.conf"))) {
    val shareCare = Credentials(
        clientId = config("shareCare.clientId"),
        clientSecret = config("shareCare.clientSecret"),
        apiUrl = config("shareCare.apiUrl"),
    )

    val einstein = Credentials(
        clientId = config("einstein.clientId"),
        clientSecret = config("einstein.clientSecret"),
        apiUrl = config("einstein.apiUrl"),
    )

    val proxxiaCia = Credentials(
        clientId = config("proxxiaCia.clientId"),
        clientSecret = config("proxxiaCia.clientSecret"),
        apiUrl = config("proxxiaCia.apiUrl")
    )

    val einsteinScheduling = Credentials(
        serviceId = config("einsteinScheduling.serviceId"),
        clientId = config("einsteinScheduling.clientId"),
        clientSecret = config("einsteinScheduling.clientSecret"),
        apiUrl = config("einsteinScheduling.apiUrl"),
    )

    val einsteinEligibleLife = Credentials(
        clientId = config("einsteinEligibleLife.clientId"),
        clientSecret = config("einsteinEligibleLife.clientSecret"),
        apiUrl = config("einsteinEligibleLife.apiUrl"),
    )

    val fleuryPa = Credentials(
        clientId = config("fleuryPa.clientId"),
        clientSecret = config("fleuryPa.clientSecret"),
        apiUrl = config("fleuryPa.apiUrl"),
    )

    val fleuryAps = Credentials(
        clientId = config("fleuryAps.clientId"),
        clientSecret = config("fleuryAps.clientSecret"),
        apiUrl = config("fleuryAps.apiUrl"),
    )

    val einsteinEligibleLifePostBackUrl = config("einsteinEligibleLife.postBackUrl")

    val contracts = listOf(config("einsteinEligibleLife.contractPA"), config("einsteinEligibleLife.contractAPS"))
            .filter { it.isNotBlank() }

    val proxxiaVideoCallUrl = config("proxxiaCia.videoCallUrl")

}

data class Credentials(
    val serviceId: String = "",
    val clientId: String,
    val clientSecret: String,
    val apiUrl: String,
)
