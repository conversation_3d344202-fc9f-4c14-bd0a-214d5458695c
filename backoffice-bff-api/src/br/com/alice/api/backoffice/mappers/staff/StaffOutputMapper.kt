package br.com.alice.api.backoffice.mappers.staff

import br.com.alice.api.backoffice.transfers.staff.StaffFullResponse
import br.com.alice.api.backoffice.transfers.staff.StaffShortResponse
import br.com.alice.api.backoffice.transfers.staff.CouncilDTO
import br.com.alice.api.backoffice.transfers.staff.ContactDTO
import br.com.alice.api.backoffice.transfers.staff.AddressDTO
import br.com.alice.common.transfers.ListPaginatedResponse
import br.com.alice.common.convertTo
import br.com.alice.data.layer.models.Staff
import br.com.alice.data.layer.models.HealthProfessional
import io.ktor.http.Parameters
import br.com.alice.common.mappers.CommonOutputMapper

object StaffOutputMapper {
    fun toFullResponse(staff: Staff): StaffFullResponse =
        staff.convertTo(StaffFullResponse::class)

    fun toFullResponse(staff: Staff, healthProfessional: HealthProfessional?, memedStatus: String? = null, healthProfessionalOpsProfile: HealthProfessionalOpsProfile? = null): StaffFullResponse {
        val baseResponse = staff.convertTo(StaffFullResponse::class)

        return if (healthProfessional != null) {
            baseResponse.copy(
                quote = healthProfessional.quote,
                specialty = healthProfessional.specialtyId,
                subSpecialties = healthProfessional.subSpecialtyIds,
                internalSpecialty = healthProfessional.internalSpecialtyId,
                internalSubSpecialties = healthProfessional.internalSubSpecialtyIds,
                providerUnits = healthProfessional.providerUnitIds,
                showOnApp = healthProfessional.showOnApp,
                curiosity = healthProfessional.curiosity,
                qualifications = healthProfessional.qualifications,
                profileBio = healthProfessional.profileBio,
                education = healthProfessional.education,
                tier = healthProfessional.tier,
                theoristTier = healthProfessional.theoristTier,
                deAccreditationDate = healthProfessional.deAccreditationDate,
                healthSpecialistScore = healthProfessional.healthSpecialistScore,
                urlSlug = healthProfessional.urlSlug,
                paymentFrequency = healthProfessional.paymentFrequency,
                contacts = healthProfessional.contacts?.map { contact ->
                    ContactDTO(
                        id = contact.id,
                        address = contact.address?.let { address ->
                            AddressDTO(
                                id = address.id,
                                street = address.street,
                                number = address.number,
                                complement = address.complement,
                                neighborhood = address.neighborhood,
                                state = address.state,
                                city = address.city,
                                zipcode = address.zipcode,
                                label = address.label,
                                active = address.active,
                                latitude = address.latitude,
                                longitude = address.longitude
                            )
                        },
                        phones = contact.phones,
                        scheduleAvailabilityDays = contact.scheduleAvailabilityDays,
                        modality = contact.modality,
                        availableDays = contact.availableDays,
                        website = contact.website
                    )
                },
                onVacationStart = healthProfessional.onVacationStart?.toLocalDate(),
                onVacationUntil = healthProfessional.onVacationUntil?.toLocalDate(),
                council = CouncilDTO(
                    number = healthProfessional.council.number,
                    state = healthProfessional.council.state,
                    type = healthProfessional.council.type?.code
                ),
                memedStatus = memedStatus
            )
        } else {
            baseResponse.copy(memedStatus = memedStatus)
        }
    }

    fun toPaginatedResponse(staffs: List<Staff>, total: Int, queryParams: Parameters): ListPaginatedResponse<StaffShortResponse> =
        ListPaginatedResponse(
            pagination = CommonOutputMapper.toPaginationResponse(queryParams, total),
            results = staffs.map { it.convertTo(StaffShortResponse::class) }
        )
}
