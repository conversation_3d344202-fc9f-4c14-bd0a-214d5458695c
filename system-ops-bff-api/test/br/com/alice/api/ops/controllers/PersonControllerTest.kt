package br.com.alice.api.ops.controllers

import br.com.alice.api.ops.ControllerTestHelper
import br.com.alice.common.ErrorResponse
import br.com.alice.common.core.exceptions.DuplicatedItemException
import br.com.alice.common.data.dsl.matchers.ResponseAssert.Companion.assertThat
import br.com.alice.common.helpers.bodyAsJson
import br.com.alice.common.helpers.coVerifyNone
import br.com.alice.common.models.Gender
import br.com.alice.common.models.Sex
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.Pronoun
import br.com.alice.person.client.PersonService
import com.github.kittinunf.result.Result
import com.github.kittinunf.result.success
import io.ktor.http.HttpHeaders
import io.mockk.clearMocks
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.mockk
import org.assertj.core.api.Assertions.assertThat
import java.time.LocalDateTime
import java.time.format.DateTimeFormatter
import kotlin.test.BeforeTest
import kotlin.test.Test

class PersonControllerTest : ControllerTestHelper() {

    private val personService: PersonService = mockk()
    private val personController = PersonController(
        personService,
    )

    @BeforeTest
    override fun setup() {
        super.setup()
        clearMocks(
            personService,
        )
        module.single { personController }
    }

    @Test
    fun `#create should return 200 OK when a person was created`() {
        val dateOfBirth = LocalDateTime.parse("1980-01-15T00:00:00.000", DateTimeFormatter.ISO_LOCAL_DATE_TIME)
        val expectedTags = listOf("teste", "internal", "new_users")

        val createdPerson = TestModelFactory.buildPerson().copy(
            dateOfBirth = dateOfBirth,
            sex = Sex.MALE,
            gender = Gender.MALE,
            pronoun = Pronoun.MALE,
            isTest = true,
            tags = expectedTags,
            phoneNumber = "11999333222"
        )

        authenticatedAs(idToken, staff) {
            val request = PersonRequest(
                email = "<EMAIL>",
                nationalId = "609.048.950-68",
                phoneNumber = "11999333222",
                firstName = "José",
                lastName = "da Silva",
                nickName = "Zé",
                dateOfBirth = "1980-01-15",
                sex = Sex.MALE,
                gender = Gender.MALE,
                isTest = true,
                pronoun = Pronoun.MALE,
                tags = "teste,internal,new_users"
            )

            coEvery {
                personService.create(match {
                    it.email == "<EMAIL>"
                            && it.nationalId == "609.048.950-68"
                            && it.firstName == "José"
                            && it.phoneNumber == "11999333222"
                            && it.lastName == "da Silva"
                            && it.nickName == "Zé"
                            && it.dateOfBirth == dateOfBirth
                            && it.sex == Sex.MALE
                            && it.gender == Gender.MALE
                            && it.pronoun == Pronoun.MALE
                            && it.isTest
                            && it.tags == expectedTags
                })
            } returns Result.success(createdPerson)

            post(to = "/person", body = request) { response ->
                assertThat(response).isSuccessfulJson()

                val content: PersonResponse = response.bodyAsJson()
                assertThat(content.id).isNotBlank
                assertThat(content.email).isEqualTo("<EMAIL>")
                assertThat(content.nationalId).isEqualTo("609.048.950-68")
                assertThat(content.firstName).isEqualTo("José")
                assertThat(content.lastName).isEqualTo("da Silva")
                assertThat(content.nickName).isEqualTo("Zé")
                assertThat(content.phoneNumber).isEqualTo("11999333222")
                assertThat(content.dateOfBirth).isEqualTo("1980-01-15")
                assertThat(content.sex).isEqualTo(Sex.MALE)
                assertThat(content.gender).isEqualTo(Gender.MALE)
                assertThat(content.pronoun).isEqualTo(Pronoun.MALE)
                assertThat(content.isTest).isTrue
                assertThat(content.fullName).isEqualTo("José da Silva")
                assertThat(content.mothersName).isEqualTo("Mother name")
                assertThat(content.acceptedTermsAt).isNull()
                assertThat(content.updatedAt).isNotNull
                assertThat(content.createdAt).isNotNull
                assertThat(content.tags).isEqualTo("teste,internal,new_users")

                coVerify(exactly = 1) {
                    personService.create(match {
                        it.email == "<EMAIL>"
                                && it.nationalId == "609.048.950-68"
                                && it.firstName == "José"
                                && it.phoneNumber == "11999333222"
                                && it.lastName == "da Silva"
                                && it.nickName == "Zé"
                                && it.dateOfBirth == dateOfBirth
                                && it.sex == Sex.MALE
                                && it.gender == Gender.MALE
                                && it.pronoun == Pronoun.MALE
                                && it.isTest
                                && it.tags == expectedTags
                    })
                }
            }
        }
    }

    @Test
    fun `#create should return 400 Bad Request when trying to create a duplicated person`() {
        authenticatedAs(idToken, staff) {
            val request = PersonRequest(
                email = "<EMAIL>",
                nationalId = "609.048.950-68",
                firstName = "José",
                lastName = "da Silva",
                nickName = "Zé",
                dateOfBirth = "1980-01-15",
                sex = Sex.MALE,
                gender = Gender.MALE,
                isTest = true,
                pronoun = Pronoun.MALE,
                tags = null,
                phoneNumber = "11999333222"
            )

            coEvery { personService.create(any()) } returns Result.failure(DuplicatedItemException("Already exist a person with this national id or email"))

            post(to = "/person", body = request) { response ->
                assertThat(response).isBadRequest()

                val content = getResponse<ErrorResponse>(response)
                assertThat(content.code).isEqualTo("person_duplicated")
                assertThat(content.message).isEqualTo("Already exist a person with this national id or email")
            }

            coVerify(exactly = 1) { personService.create(any()) }
        }
    }

    @Test
    fun `#index should return 200 OK with a list of people`() {
        val person1 = TestModelFactory.buildPerson()
        val person2 = TestModelFactory.buildPerson(socialName = "Joana")
        val ids = listOf(person1.id.toString(), person2.id.toString())

        coEvery { personService.findByIds(ids) } returns listOf(person1, person2).success()

        authenticatedAs(idToken, staff) {
            get("/person?filter={\"id\":[\"${person1.id}\",\"${person2.id}\"]}") { response ->
                assertThat(response).isSuccessfulJson()

                val content = getResponse<List<PersonResponse>>(response)
                val person2FullName = content.first { it.id == person2.id.toString() }.fullName
                assertThat(content.size).isEqualTo(2)
                val responseIds = content.map { it.id }
                assertThat(responseIds).contains(person1.id.toString())
                assertThat(responseIds).contains(person2.id.toString())
                assertThat(person2FullName).isEqualTo(person2.fullSocialName)

                assertThat(response.headers[HttpHeaders.ContentRange]).isEqualTo("2")

                coVerify(exactly = 1) { personService.findByIds(ids) }
            }
        }

    }

    @Test
    fun `#index should return 200 OK when trying to query a person by search token`() {
        val person1 = TestModelFactory.buildPerson()
        val person2 = TestModelFactory.buildPerson()

        coEvery { personService.findBySearchTokens("José") } returns listOf(person1, person2).success()

        authenticatedAs(idToken, staff) {
            get("/person?filter={\"q\":\"José\"}") { response ->
                assertThat(response).isSuccessfulJson()

                val content: List<PersonResponse> = response.bodyAsJson()
                assertThat(content).hasSize(2)

                val responseIds = content.map { it.id }
                assertThat(responseIds).containsExactly(person1.id.toString(), person2.id.toString())
                assertThat(response.headers[HttpHeaders.ContentRange]).isEqualTo("2")

                coVerify(exactly = 1) { personService.findBySearchTokens("José") }
            }
        }
    }

    @Test
    fun `#index should return 200 OK with empty list with range alone as parameter`() {
        authenticatedAs(idToken, staff) {
            get("/person?filter={}&range=[0,49]&sort=[\"id\",\"DESC\"]") { response ->
                assertThat(response).isSuccessfulJson()

                val content: List<PersonResponse> = response.bodyAsJson()
                assertThat(content).hasSize(0)

                assertThat(content).isEmpty()
                assertThat(response.headers[HttpHeaders.ContentRange]).isEqualTo("0")

                coVerifyNone { personService.findPaginated(any(), any()) }
                coVerifyNone { personService.countAll() }
            }
        }
    }

    @Test
    fun `#index should get empty list if People not exists`() {
        authenticatedAs(idToken, staff) {
            get("/person?filter={\"id\":[]}&range=[0,49]&sort=[\"id\",\"DESC\"]") { response ->
                assertThat(response).isSuccessfulJson()

                val content = getResponse<List<PersonResponse>>(response)
                assertThat(content.size).isEqualTo(0)

                assertThat(response.headers[HttpHeaders.ContentRange]).isEqualTo("0")

                coVerify(exactly = 0) { personService.get(any()) }
            }
        }
    }

    @Test
    fun `#get should return 200 OK when a person exists`() {
        val dateOfBirth = LocalDateTime.parse("1980-01-15T12:15:44.000", DateTimeFormatter.ISO_LOCAL_DATE_TIME)

        val person = TestModelFactory.buildPerson().copy(
            dateOfBirth = dateOfBirth,
            sex = Sex.MALE,
            gender = Gender.MALE,
            pronoun = Pronoun.MALE,
            isTest = true
        )

        coEvery { personService.get(person.id) } returns Result.success(person)

        authenticatedAs(idToken, staff) {
            get("/person/${person.id}") { response ->
                assertThat(response).isSuccessfulJson()

                val content: PersonResponse = response.bodyAsJson()
                assertThat(content.id).isNotBlank
                assertThat(content.email).isEqualTo("<EMAIL>")
                assertThat(content.nationalId).isEqualTo("609.048.950-68")
                assertThat(content.firstName).isEqualTo("José")
                assertThat(content.lastName).isEqualTo("da Silva")
                assertThat(content.nickName).isEqualTo("Zé")
                assertThat(content.dateOfBirth).isEqualTo("1980-01-15")
                assertThat(content.sex).isEqualTo(Sex.MALE)
                assertThat(content.gender).isEqualTo(Gender.MALE)
                assertThat(content.pronoun).isEqualTo(Pronoun.MALE)
                assertThat(content.isTest).isTrue()

                coVerify(exactly = 1) { personService.get(person.id) }
            }
        }
    }

    @Test
    fun `#update should update Person`() {
        val person = TestModelFactory.buildPerson()
        val updatedPerson = person.copy(
            firstName = "José Paulo",
            lastName = "da Silva Junior",
            email = "<EMAIL>",
            phoneNumber = "11999333222",
            dateOfBirth = LocalDateTime.of(2020, 8, 10, 0, 0, 0)
        )

        val request = PersonRequest(
            email = "<EMAIL>",
            nationalId = person.nationalId,
            phoneNumber = "11999333222",
            firstName = "José Paulo",
            lastName = "da Silva Junior",
            nickName = person.nickName,
            dateOfBirth = "2020-08-10",
            sex = Sex.MALE,
            gender = Gender.MALE,
            isTest = true,
            pronoun = Pronoun.MALE,
            tags = null
        )

        coEvery { personService.get(person.id) } returns Result.success(person)
        coEvery {
            personService.update(match {
                it.email == updatedPerson.email
                        && it.firstName == updatedPerson.firstName
                        && it.lastName == updatedPerson.lastName
                        && it.phoneNumber == updatedPerson.phoneNumber
            })
        } returns Result.success(updatedPerson)

        authenticatedAs(idToken, staff) {
            put(to = "/person/${person.id}", body = request) { response ->
                assertThat(response).isSuccessfulJson()

                val content: PersonResponse = response.bodyAsJson()
                assertThat(content.id).isEqualTo(updatedPerson.id.toString())
                assertThat(content.email).isEqualTo(updatedPerson.email)
                assertThat(content.firstName).isEqualTo(updatedPerson.firstName)
                assertThat(content.lastName).isEqualTo(updatedPerson.lastName)
                assertThat(content.dateOfBirth).isEqualTo("2020-08-10")
                assertThat(content.phoneNumber).isEqualTo("11999333222")

                coVerify(exactly = 1) { personService.get(person.id) }
                coVerify(exactly = 1) {
                    personService.update(match {
                        it.email == updatedPerson.email
                                && it.firstName == updatedPerson.firstName
                                && it.lastName == updatedPerson.lastName
                                && it.phoneNumber == updatedPerson.phoneNumber
                    })
                }
            }
        }
    }
}
