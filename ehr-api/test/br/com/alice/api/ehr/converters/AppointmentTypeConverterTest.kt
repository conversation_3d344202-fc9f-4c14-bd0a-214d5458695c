package br.com.alice.api.ehr.converters

import br.com.alice.api.ehr.module
import br.com.alice.channel.client.ChannelService
import br.com.alice.channel.models.ChannelResponse
import br.com.alice.common.RangeUUID
import br.com.alice.common.core.extensions.toUUID
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.AppointmentEventDetail
import br.com.alice.data.layer.models.AppointmentEventReferenceModel
import br.com.alice.data.layer.models.AppointmentSchedule
import br.com.alice.data.layer.models.AppointmentScheduleType
import br.com.alice.data.layer.models.AppointmentType
import br.com.alice.data.layer.models.ChannelCategory
import br.com.alice.data.layer.models.ChannelSubCategory
import br.com.alice.data.layer.models.Staff
import br.com.alice.featureconfig.core.FeaturePopulateService
import br.com.alice.schedule.client.AppointmentScheduleService
import com.github.kittinunf.result.failure
import com.github.kittinunf.result.success
import io.ktor.server.testing.TestApplicationEngine
import io.mockk.called
import io.mockk.clearMocks
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.confirmVerified
import io.mockk.every
import io.mockk.mockk
import io.mockk.spyk
import kotlinx.coroutines.runBlocking
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.AfterAll
import org.junit.jupiter.api.BeforeAll
import org.koin.dsl.module
import kotlin.test.Test

class AppointmentTypeConverterTest {

    companion object {
        private val featurePopulateService: FeaturePopulateService = mockk()
        private var testEngine: TestApplicationEngine = TestApplicationEngine()
        val appointmentScheduleService: AppointmentScheduleService = mockk()
        val channelService: ChannelService = mockk()

        @AfterAll
        @JvmStatic
        fun tearDown() {
            testEngine.stop(0, 0)
        }

        @BeforeAll
        @JvmStatic
        fun init() {
            testEngine = TestApplicationEngine()
            val applicationEngine = testEngine.start()

            val modules = listOf(
                module(createdAtStart = true) {
                    single { featurePopulateService }
                    single { appointmentScheduleService }
                    single { channelService }
                }
            )

            applicationEngine.application.module(modules)
        }
    }

    private val staff = spyk(TestModelFactory.buildStaff())
    private val schedule: AppointmentSchedule = mockk()
    private val channel: ChannelResponse = mockk()

    private val scheduleEvent = AppointmentEventDetail(
        name = "scheduleEvent",
        referenceModelId = RangeUUID.generate().toString(),
        referenceModel = AppointmentEventReferenceModel.SCHEDULING
    )

    private val channelEvent = AppointmentEventDetail(
        name = "channelEvent",
        referenceModelId = "channelId",
        referenceModel = AppointmentEventReferenceModel.CHANNEL
    )

    private val testResultEvent = AppointmentEventDetail(
        name = "testResultEvent",
        referenceModelId = RangeUUID.generate().toString(),
        referenceModel = AppointmentEventReferenceModel.TEST_RESULT
    )

    private val wandaEvent = AppointmentEventDetail(
        name = "wandaEvent",
        referenceModelId = RangeUUID.generate().toString(),
        referenceModel = AppointmentEventReferenceModel.WANDA_TASK
    )

    private fun clearMocks() = clearMocks(staff, schedule, channel, appointmentScheduleService, channelService)
    private fun confirmMock() = confirmVerified(appointmentScheduleService, channelService)

    @Test
    fun `#calculateType returns expected type by staff role`() {
        testStaff(AppointmentType.ASSISTANCE_CARE) { staff -> every { staff.isAssistanceCare() } returns true }
        testStaff(AppointmentType.ON_SITE) { staff -> every { staff.isOnSiteProfessional() } returns true }
        testStaff(AppointmentType.STATEMENT_OF_HEALTH) { staff -> every { staff.isRiskNurse() } returns true }
        testStaff(AppointmentType.STATEMENT_OF_HEALTH) { staff -> every { staff.isIntermittentRiskNurse() } returns true }
        testStaff(AppointmentType.STATEMENT_OF_HEALTH) { staff -> every { staff.isMedRisk() } returns true }
        testStaff(AppointmentType.STATEMENT_OF_HEALTH) { staff -> every { staff.isChiefRisk() } returns true }
        testStaff(AppointmentType.TEST_COLLECT) { staff -> every { staff.isTechniqueNurse() } returns true }
        testStaff(AppointmentType.DEFAULT) { staff -> every { staff.isFromMultiTeam() } returns true }
        testStaff(AppointmentType.DEFAULT) { staff -> every { staff.isPhysician() } returns true }
        testStaff(AppointmentType.ASSISTANCE_CARE) { staff -> every { staff.isAssistanceCare() } returns true }
        testStaff(AppointmentType.COUNTER_REFERRAL) { staff -> every { staff.isHealthCommunity() } returns true }
        testStaff(AppointmentType.COUNTER_REFERRAL) { staff -> every { staff.isCommunity() } returns true }
        testStaff(AppointmentType.ANNOTATION) { staff ->
            every { staff.isPhysician() } returns false
            every { staff.isAssistanceCare() } returns false
            every { staff.isOnSiteProfessional() } returns false
            every { staff.isRiskNurse() } returns false
            every { staff.isIntermittentRiskNurse() } returns false
            every { staff.isMedRisk() } returns false
            every { staff.isTechniqueNurse() } returns false
            every { staff.isFromMultiTeam() } returns false
            every { staff.isPhysician() } returns false
        }
    }

    @Test
    fun `#calculateType returns expected type by schedule event`() = runBlocking {
        testScheduleEvent(AppointmentScheduleType.IMMERSION, AppointmentType.IMMERSION)
        testScheduleEvent(AppointmentScheduleType.FOLLOW_UP, AppointmentType.FOLLOW_UP_VISIT)
        testScheduleEvent(AppointmentScheduleType.FOLLOW_UP_NUTRITIONIST, AppointmentType.FOLLOW_UP_VISIT)
        testScheduleEvent(AppointmentScheduleType.FOLLOW_UP_PHYSICAL_EDUCATOR, AppointmentType.FOLLOW_UP_VISIT)
        testScheduleEvent(AppointmentScheduleType.HEALTH_DECLARATION, AppointmentType.STATEMENT_OF_HEALTH)
        testScheduleEvent(AppointmentScheduleType.CONNECTION, AppointmentType.HEALTH_PLAN_CARE)
        testScheduleEvent(AppointmentScheduleType.TEST, AppointmentType.TEST_COLLECT)
        testScheduleEvent(AppointmentScheduleType.NURSE_ONSITE, AppointmentType.ON_SITE)
        testScheduleEvent(AppointmentScheduleType.PROC_NURSE, AppointmentType.ON_SITE)
        testScheduleEvent(AppointmentScheduleType.PROC_ONSITE, AppointmentType.ON_SITE)
        testScheduleEvent(AppointmentScheduleType.COMMUNITY, AppointmentType.ANNOTATION)
        testScheduleEvent(AppointmentScheduleType.HEALTHCARE_TEAM, AppointmentType.DEFAULT)
        testScheduleEvent(AppointmentScheduleType.NURSE, AppointmentType.DEFAULT)
        testScheduleEvent(AppointmentScheduleType.NUTRITIONIST, AppointmentType.DEFAULT)
        testScheduleEvent(AppointmentScheduleType.PHYSICAL_EDUCATOR, AppointmentType.DEFAULT)
        testScheduleEvent(AppointmentScheduleType.PHYSICIAN_ONSITE, AppointmentType.DEFAULT)
        testScheduleEvent(AppointmentScheduleType.PSYCHOLOGIST, AppointmentType.DEFAULT)
        testScheduleEvent(AppointmentScheduleType.OTHER, AppointmentType.DEFAULT)
        testScheduleEvent(AppointmentScheduleType.HOME_TEST, AppointmentType.DEFAULT)
    }

    @Test
    fun `#calculateType returns expected type by channel event`() = runBlocking {
        testChannelEvent(ChannelCategory.ASSISTANCE, null, AppointmentType.ASSISTANCE_CARE)
        testChannelEvent(ChannelCategory.ADMINISTRATIVE, null, AppointmentType.ANNOTATION)
        testChannelEvent(null, null, AppointmentType.DEFAULT)

        testChannelEvent(ChannelCategory.ASSISTANCE, ChannelSubCategory.ACUTE, AppointmentType.ASSISTANCE_CARE)
        testChannelEvent(ChannelCategory.ASSISTANCE, ChannelSubCategory.LONGITUDINAL, AppointmentType.ASSISTANCE_CARE)
        testChannelEvent(ChannelCategory.ASSISTANCE, ChannelSubCategory.SCREENING, AppointmentType.ASSISTANCE_CARE)
        testChannelEvent(ChannelCategory.ASSISTANCE, ChannelSubCategory.VIRTUAL_CLINIC, AppointmentType.ASSISTANCE_CARE)
        testChannelEvent(ChannelCategory.ASSISTANCE, ChannelSubCategory.MULTI, AppointmentType.DEFAULT)
    }

    @Test
    fun `#calculateType returns expected type by test result event`() = runBlocking {
        assertThat(AppointmentTypeConverter.calculateType(staff, testResultEvent)).isEqualTo(AppointmentType.ANNOTATION)

        coVerify { appointmentScheduleService wasNot called }
        coVerify { channelService wasNot called }
        confirmMock()
        clearMocks()
    }

    @Test
    fun `#calculateType returns type by staff when schedule service returns error`() = runBlocking {
        coEvery { appointmentScheduleService.get(scheduleEvent.referenceModelId.toUUID()) } returns Exception().failure()
        every { staff.isOnSiteProfessional() } returns true

        assertThat(AppointmentTypeConverter.calculateType(staff, scheduleEvent)).isEqualTo(AppointmentType.ON_SITE)

        coVerifyOnce { appointmentScheduleService.get(any()) }
        coVerify { channelService wasNot called }
        confirmMock()
        clearMocks()
    }

    @Test
    fun `#calculateType returns type by staff when channel service returns error`() = runBlocking {
        coEvery { channelService.get(channelEvent.referenceModelId) } returns Exception().failure()
        every { staff.isAssistanceCare() } returns true

        assertThat(
            AppointmentTypeConverter.calculateType(
                staff,
                channelEvent
            )
        ).isEqualTo(AppointmentType.ASSISTANCE_CARE)

        coVerifyOnce { channelService.get(any()) }
        coVerify { appointmentScheduleService wasNot called }
        confirmMock()
        clearMocks()
    }

    @Test
    fun `#calculateType returns expected type by wanda event`() = runBlocking {
        assertThat(AppointmentTypeConverter.calculateType(staff, wandaEvent)).isEqualTo(AppointmentType.ANNOTATION)

        coVerify { appointmentScheduleService wasNot called }
        coVerify { channelService wasNot called }
        confirmMock()
        clearMocks()
    }

    private fun testStaff(expectedAppointmentType: AppointmentType, mockFunction: (Staff) -> Unit) = runBlocking {
        mockFunction(staff)

        assertThat(AppointmentTypeConverter.calculateType(staff, null)).isEqualTo(expectedAppointmentType)

        coVerify { appointmentScheduleService wasNot called }
        coVerify { channelService wasNot called }
        confirmMock()
        clearMocks()
    }

    private suspend fun testScheduleEvent(
        scheduleType: AppointmentScheduleType,
        expectedAppointmentType: AppointmentType
    ) {
        coEvery { appointmentScheduleService.get(scheduleEvent.referenceModelId.toUUID()) } returns schedule.success()
        every { schedule.type } returns scheduleType

        assertThat(AppointmentTypeConverter.calculateType(staff, scheduleEvent)).isEqualTo(expectedAppointmentType)

        coVerifyOnce { appointmentScheduleService.get(any()) }
        coVerify { channelService wasNot called }
        confirmMock()
        clearMocks()
    }

    private suspend fun testChannelEvent(
        channelCategory: ChannelCategory?,
        channelSubCategory: ChannelSubCategory?,
        expectedAppointmentType: AppointmentType
    ) {
        coEvery { channelService.get(channelEvent.referenceModelId) } returns channel.success()
        every { channel.category } returns channelCategory
        every { channel.subCategory } returns channelSubCategory

        assertThat(AppointmentTypeConverter.calculateType(staff, channelEvent)).isEqualTo(expectedAppointmentType)

        coVerifyOnce { channelService.get(any()) }
        coVerify { appointmentScheduleService wasNot called }
        confirmMock()
        clearMocks()
    }

}
