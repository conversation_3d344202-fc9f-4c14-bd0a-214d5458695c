package br.com.alice.channel.core.services

import br.com.alice.channel.administrative.services.internal.ChannelThemeService
import br.com.alice.channel.assistance.services.internal.VirtualClinicQueueService
import br.com.alice.channel.client.ChannelService
import br.com.alice.channel.client.ReRoutingExtraInfo
import br.com.alice.channel.client.RoutingDashboardResponse
import br.com.alice.channel.converters.ChannelBackupConverter
import br.com.alice.channel.converters.ChannelResponseConverter
import br.com.alice.channel.core.context.FirestoreContextUsage
import br.com.alice.channel.core.context.firestoreTransactional
import br.com.alice.channel.core.services.internal.ChannelBackupService
import br.com.alice.channel.core.services.internal.ChannelBackupServiceFilters
import br.com.alice.channel.core.services.internal.ChannelCreateService
import br.com.alice.channel.core.services.internal.ChannelNotificationService
import br.com.alice.channel.core.services.internal.firestore.ChannelFirestoreService
import br.com.alice.channel.core.services.internal.logics.ChannelMergeService
import br.com.alice.channel.models.ChannelDocument
import br.com.alice.channel.models.ChannelMessageQuestionOptions
import br.com.alice.channel.models.ChannelResponse
import br.com.alice.channel.models.ChannelStaffInfo
import br.com.alice.channel.models.ChatMessageRequest
import br.com.alice.channel.models.CreateChatRequest
import br.com.alice.channel.models.Origin
import br.com.alice.channel.models.SendMessageRequest
import br.com.alice.channel.models.healthPlanChannelName
import br.com.alice.channel.models.nutritionChannelName
import br.com.alice.channel.models.physicalActivityChannelName
import br.com.alice.channel.models.psychologistChannelName
import br.com.alice.common.core.PersonId
import br.com.alice.common.core.exceptions.AccessForbiddenException
import br.com.alice.common.core.exceptions.ConflictException
import br.com.alice.common.core.exceptions.InvalidArgumentException
import br.com.alice.common.core.exceptions.NotFoundException
import br.com.alice.common.core.extensions.toSafeUUID
import br.com.alice.common.core.extensions.toUUID
import br.com.alice.common.extensions.coFoldNotFound
import br.com.alice.common.extensions.mapEach
import br.com.alice.common.extensions.pmapEach
import br.com.alice.common.observability.Spannable
import br.com.alice.data.layer.models.ChannelArchivedReason
import br.com.alice.data.layer.models.ChannelAssessment
import br.com.alice.data.layer.models.ChannelCategory
import br.com.alice.data.layer.models.ChannelChangeAction
import br.com.alice.data.layer.models.ChannelKind
import br.com.alice.data.layer.models.ChannelStatus
import br.com.alice.data.layer.models.ChannelSubCategory
import br.com.alice.data.layer.models.ChannelSubCategoryClassifier
import br.com.alice.data.layer.models.ChannelTheme
import br.com.alice.data.layer.models.HealthPlanTask
import br.com.alice.data.layer.models.HealthPlanTaskType.EMERGENCY
import br.com.alice.data.layer.models.HealthPlanTaskType.FOLLOW_UP_REQUEST
import br.com.alice.data.layer.models.HealthPlanTaskType.PRESCRIPTION
import br.com.alice.data.layer.models.HealthPlanTaskType.REFERRAL
import br.com.alice.healthplan.client.HealthPlanTaskService
import br.com.alice.staff.client.StaffService
import com.github.kittinunf.result.Result
import com.github.kittinunf.result.failure
import com.github.kittinunf.result.flatMap
import com.github.kittinunf.result.map
import com.github.kittinunf.result.success
import java.math.RoundingMode
import java.time.LocalDateTime
import java.util.UUID
import br.com.alice.channel.core.services.internal.ChannelService as InternalChannelService

class ChannelServiceImpl(
    private val channelNotificationService: ChannelNotificationService,
    private val healthPlanTaskService: HealthPlanTaskService,
    private val channelService: InternalChannelService,
    private val staffService: StaffService,
    private val channelFirestoreService: ChannelFirestoreService,
    private val channelMergeService: ChannelMergeService,
    private val channelBackupService: ChannelBackupService,
    private val virtualClinicQueueService: VirtualClinicQueueService,
    private val channelCreateService: ChannelCreateService,
    private val channelThemeService: ChannelThemeService
) : ChannelService, Spannable {

    override suspend fun add(channelRequest: ChannelDocument): Result<ChannelDocument, Throwable> =
        channelCreateService.createChannel(channelRequest)

    @OptIn(FirestoreContextUsage::class)
    override suspend fun addChatV2(request: CreateChatRequest): Result<ChannelDocument, Throwable> =
        firestoreTransactional {
            channelCreateService.createChat(request)
        }

    override suspend fun get(channelId: String): Result<ChannelResponse, Throwable> =
        channelService.getChannel(channelId).map { channelDocument ->
            ChannelResponseConverter.convert(channelDocument, channelId)
        }

    override suspend fun findChannels(
        personId: String,
        onlyActives: Boolean,
        category: ChannelCategory?,
        subCategories: List<ChannelSubCategory>?,
        createdAfter: LocalDateTime?
    ): Result<List<ChannelResponse>, Throwable> =
        channelService.findChannels(personId, onlyActives, category, subCategories, createdAfter)
            .pmapEach { channelDocument ->
                ChannelResponseConverter.convert(channelDocument)
            }

    override suspend fun findChannelsById(channelIds: List<String>): Result<List<ChannelResponse>, Throwable> =
        channelService.getChannelsById(channelIds)
            .pmapEach { channelDocument ->
                ChannelResponseConverter.convert(channelDocument)
            }

    override suspend fun getByPersonAndStatuses(
        personId: PersonId,
        statuses: List<ChannelStatus>?,
    ): Result<List<ChannelResponse>, Throwable> =
        channelBackupService.findByFilters(
            ChannelBackupServiceFilters(
                personId = personId,
                statuses = statuses
            )
        ).mapEach(ChannelBackupConverter::convert)

    override suspend fun sendMessage(
        personId: PersonId,
        messageRequest: SendMessageRequest,
    ): Result<String, Throwable> =
        when (messageRequest.origin) {
            Origin.HEALTH_PLAN_ITEM -> sendMessageFromHealthPlanItem(personId, messageRequest)
            Origin.HELP_CENTER -> sendMessageFromHelpCenter(personId, messageRequest)
            else -> InvalidArgumentException(
                code = "invalid_message_source",
                message = "Invalid message source: ${messageRequest.source}"
            ).failure()
        }

    @OptIn(FirestoreContextUsage::class)
    override suspend fun rename(
        channelId: String,
        newName: String,
        staffId: String
    ): Result<String, Throwable> = firestoreTransactional {
        channelService.rename(channelId, newName, staffId)
    }

    @OptIn(FirestoreContextUsage::class)
    override suspend fun setTags(channelId: String, tags: List<String>) = firestoreTransactional {
        channelService.setTags(channelId = channelId, tags = tags)
    }

    @OptIn(FirestoreContextUsage::class)
    override suspend fun highlight(
        channelId: String,
        staffId: String
    ): Result<String, Throwable> = firestoreTransactional {
        channelFirestoreService.getChannel(channelId).map { channelDocument ->
            if (channelDocument.status.isNotActive()) throw IllegalArgumentException("Channel is not active")

            val staff = channelDocument.staff[staffId] ?: throw IllegalArgumentException("Staff not exists in channel")

            channelFirestoreService.updateFields(
                channelId,
                mapOf(
                    ChannelDocument::staff to mapOf(
                        staffId to mapOf(ChannelStaffInfo::highlight to staff.highlight.not())
                    )
                )
            )
            channelDocument
        }.map {
            notifyEvent(channelDocument = it, action = ChannelChangeAction.HIGHLIGHT)
            it.id()
        }
    }

    @OptIn(FirestoreContextUsage::class)
    override suspend fun giveOwnership(
        channelId: String,
        staffId: String,
        requesterStaffId: String,
    ): Result<String, Throwable> = firestoreTransactional {
        channelService.giveOwnership(channelId, staffId, requesterStaffId)
    }

    @OptIn(FirestoreContextUsage::class)
    override suspend fun addStaff(
        channelId: String,
        staffId: String,
        requesterStaffId: String,
        owner: Boolean,
        force: Boolean
    ): Result<String, Throwable> = firestoreTransactional {
        staffService.get(staffId.toUUID()).flatMap { staff ->
            if (staff.isAliceHealthProfessionalOrNavigator().not())
                throw InvalidArgumentException("Is not a permitted staff", "invalid_role")

            channelService.addStaff(channelId, staff, requesterStaffId, owner, force)
        }
    }

    @OptIn(FirestoreContextUsage::class)
    override suspend fun removeStaff(
        channelId: String,
        staffId: String,
        requesterStaffId: String,
    ): Result<String, Throwable> = firestoreTransactional {
        channelService.removeStaff(channelId, staffId, requesterStaffId)
    }

    @OptIn(FirestoreContextUsage::class)
    override suspend fun archiveChannel(
        channelId: String,
        requesterStaffId: String,
        archivedReason: ChannelArchivedReason
    ): Result<String, Throwable> =
        firestoreTransactional {
            channelService.archiveChannel(channelId, requesterStaffId, archivedReason)
        }

    @FirestoreContextUsage
    override suspend fun archiveAdministrativeChannel(channelTheme: ChannelTheme): Result<String, Throwable> =
        firestoreTransactional {
            channelThemeService.archiveAdministrativeChannel(channelTheme)
        }

    @OptIn(FirestoreContextUsage::class)
    override suspend fun merge(
        originChannelId: String,
        destinationChannelId: String,
        requesterStaffId: String,
    ): Result<String, Throwable> =
        firestoreTransactional {
            channelMergeService.merge(originChannelId, destinationChannelId, requesterStaffId)
        }

    @OptIn(FirestoreContextUsage::class)
    override suspend fun deleteMessage(
        channelId: String,
        messageId: String,
        staffId: String,
        allowRemoveAnyChannelMessage: Boolean,
    ): Result<String, Throwable> = firestoreTransactional {
        channelService.deleteMessage(channelId, messageId, staffId, allowRemoveAnyChannelMessage)
    }

    override suspend fun getOrCreateByMultiTeamClassifier(
        classifier: ChannelSubCategoryClassifier,
        personId: String,
        channelPersonId: String,
        staffIdsToAdd: List<String>
    ): Result<String, Throwable> =
        if (!ChannelSubCategoryClassifier.forMulti().contains(classifier))
            IllegalArgumentException("Invalid classifier $classifier").failure()
        else {
            channelFirestoreService.find { collectionReference ->
                collectionReference.whereEqualTo(ChannelDocument::channelPersonId.name, channelPersonId)
                    .whereEqualTo(ChannelDocument::subCategoryClassifier.name, classifier)
                    .whereEqualTo(ChannelDocument::status.name, ChannelStatus.ACTIVE)
            }.map {
                it.firstOrNull() ?: throw NotFoundException("Not found channel for $channelPersonId and $classifier")
            }.coFoldNotFound {
                add(
                    ChannelDocument(
                        name = getChannelName(classifier),
                        kind = ChannelKind.CHANNEL,
                        category = ChannelCategory.ASSISTANCE,
                        subCategory = ChannelSubCategory.MULTI,
                        subCategoryClassifier = classifier,
                        personId = personId,
                        channelPersonId = channelPersonId
                    ).populateTypes()
                )
            }.flatMap { channel ->
                if (!channel.staff.keys.containsAll(staffIdsToAdd)) {
                    staffIdsToAdd.minus(channel.staff.keys).map {
                        addStaff(
                            channelId = channel.id!!,
                            staffId = it,
                            requesterStaffId = it,
                            owner = true
                        )
                    }
                }

                channel.id!!.success()
            }
        }

    @OptIn(FirestoreContextUsage::class)
    override suspend fun setChannelAssessment(
        channelId: String,
        channelAssessment: ChannelAssessment,
    ): Result<String, Throwable> = firestoreTransactional {
        channelService.setChannelAssessment(channelId, channelAssessment)
    }

    @OptIn(FirestoreContextUsage::class)
    override suspend fun channelClassification(
        channelId: String,
        kind: ChannelKind,
        category: ChannelCategory?,
        subCategory: ChannelSubCategory?,
        subCategoryClassifier: ChannelSubCategoryClassifier?
    ): Result<String, Throwable> = firestoreTransactional {
        channelService.channelClassification(channelId, kind, category, subCategory, subCategoryClassifier)
    }

    override suspend fun getDashboardCounters(): Result<RoutingDashboardResponse, Throwable> =
        channelBackupService.getActivesChats().map { chats ->

            val totalChats = chats.size
            val totalStaffs = chats.map { it.staff }.distinct().size

            RoutingDashboardResponse(
                simultaneity = calculateSimultaneity(totalChats, totalStaffs),
                chats = totalChats,
            )
        }

    @OptIn(FirestoreContextUsage::class)
    override suspend fun reRouting(
        channelId: String,
        category: ChannelCategory,
        subCategory: ChannelSubCategory?,
        extraInfo: ReRoutingExtraInfo?
    ): Result<String, Throwable> =
        firestoreTransactional {
            channelService.reRouting(channelId, category, subCategory, extraInfo)
        }

    @OptIn(FirestoreContextUsage::class)
    override suspend fun dischargeChannel(
        channelId: String,
        name: String,
        kind: ChannelKind,
        staffId: String,
        category: ChannelCategory?,
        subCategory: ChannelSubCategory?
    ) =
        firestoreTransactional {
            channelService.dischargeChannel(
                channelId = channelId,
                name = name,
                kind = kind,
                staffId = staffId,
                category = category,
                subCategory = subCategory
            )
        }

    @OptIn(FirestoreContextUsage::class)
    override suspend fun getNextVirtualClinic(staffId: UUID): Result<String, Throwable> =
        firestoreTransactional {
            virtualClinicQueueService.getNext(staffId)
        }

    override suspend fun getByScreeningNavigationId(
        personId: PersonId,
        screeningNavigationId: UUID,
        statuses: List<ChannelStatus>?
    ): Result<List<ChannelResponse>, Throwable> =
        channelBackupService.findByFilters(
            ChannelBackupServiceFilters(
                personId = personId,
                screeningNavigationId = screeningNavigationId,
                statuses = statuses
            )
        ).mapEach(ChannelBackupConverter::convert)

    @OptIn(FirestoreContextUsage::class)
    override suspend fun assignToN2Staff(channelId: String, staffId: String): Result<String, Throwable> =
        firestoreTransactional {
            channelService.getChannel(channelId).flatMap { channel ->
                if (channel.tags.contains("redirecionado_n2")) {
                    throw ConflictException("Channel already has been redirected", "channel_already_redirected")
                }

                staffService.get(staffId.toUUID()).flatMap {
                    if (!(it.isNavigator() || it.isChiefDigitalCareNurse() || it.isQualityNurse())) {
                        throw AccessForbiddenException("Is not a permitted staff", "invalid_role")
                    }

                    channelService.assignToN2Staff(channel, it)
                }
            }
        }

    @OptIn(FirestoreContextUsage::class)
    override suspend fun sendQuestionMessage(
        channelId: String,
        content: String,
        channelOptions: List<ChannelMessageQuestionOptions>
    ): Result<String, Throwable> =
        firestoreTransactional {
            channelService.sendQuestionMessage(channelId, content, channelOptions)
        }


    private suspend fun sendMessageFromHealthPlanItem(
        personId: PersonId,
        messageRequest: SendMessageRequest,
    ): Result<String, Throwable> =
        messageRequest.healthPlanItemId?.trim()?.let { healthPlanItemId ->
            if (healthPlanItemId.isEmpty()) {
                InvalidArgumentException(message = "Invalid task id").failure()
            } else {
                val healthPlanTaskId = healthPlanItemId.toSafeUUID()
                healthPlanTaskService.get(id = healthPlanTaskId)
                    .flatMap { healthPlanTask ->
                        val customMessageContent = buildCustomMessage(
                            contentPrefix = healthPlanTask.prefix(),
                            messageContent = messageRequest.content
                        )
                        val message = messageRequest.copy(
                            content = customMessageContent,
                            source = healthPlanTask.type.name.lowercase()
                        )

                        sendMessageByTask(healthPlanTask, personId, message)
                    }
            }
        } ?: InvalidArgumentException(message = "Missing task id").failure()

    private suspend fun sendMessageByTask(
        healthPlanTask: HealthPlanTask,
        personId: PersonId,
        message: SendMessageRequest
    ) =
        when (healthPlanTask.type) {
            REFERRAL, EMERGENCY, FOLLOW_UP_REQUEST -> ChannelCategory.ADMINISTRATIVE to null
            else -> ChannelCategory.ASSISTANCE to ChannelSubCategory.SCREENING
        }.let { (category, subCategory) ->
            sendMessageToChat(
                personId = personId,
                message = message,
                origin = Origin.HEALTH_PLAN_ITEM.description,
                category = category,
                subCategory = subCategory,
                chatName = healthPlanTask.prefix()
            )
        }

    private suspend fun sendMessageFromHelpCenter(
        personId: PersonId,
        messageRequest: SendMessageRequest,
    ): Result<String, Throwable> {
        val customMessageContent = buildCustomMessage(
            messageContent = messageRequest.content
        )
        val message = messageRequest.copy(
            content = customMessageContent,
            source = Origin.HELP_CENTER.description
        )

        return sendMessageToChat(
            personId = personId,
            message = message,
            origin = Origin.HELP_CENTER.description,
            category = ChannelCategory.ADMINISTRATIVE,
            chatName = messageRequest.chatName.orEmpty()
        )
    }

    private suspend fun sendMessageToChat(
        personId: PersonId,
        message: SendMessageRequest,
        origin: String,
        category: ChannelCategory,
        subCategory: ChannelSubCategory? = null,
        subCategoryClassifier: ChannelSubCategoryClassifier? = null,
        chatName: String = "",
    ) =
        addChatV2(
            CreateChatRequest(
                personId = personId,
                allowOnCallFlow = false,
                origin = origin,
                message = ChatMessageRequest(
                    content = message.content,
                    type = message.type,
                    appVersion = message.appVersion,
                    source = message.source
                ),
                category = category,
                subCategory = subCategory,
                subCategoryClassifier = subCategoryClassifier,
                chatName = chatName
            )
        ).map { it.id!! }

    private fun buildCustomMessage(contentPrefix: String? = null, messageContent: String): String =
        if (contentPrefix.isNullOrEmpty()) messageContent
        else "<strong>#$contentPrefix</strong> <br /><br /> $messageContent"

    private suspend fun notifyEvent(channelDocument: ChannelDocument, action: ChannelChangeAction) =
        channelNotificationService.notify(channelDocument, action)

    private fun getChannelName(classifier: ChannelSubCategoryClassifier?) =
        when (classifier) {
            ChannelSubCategoryClassifier.NUTRITION -> nutritionChannelName
            ChannelSubCategoryClassifier.PHYSICAL_PREPARATION -> physicalActivityChannelName
            ChannelSubCategoryClassifier.HEALTH_TEAM -> healthPlanChannelName
            ChannelSubCategoryClassifier.PSYCHOLOGY -> psychologistChannelName
            else -> null
        }

    private fun calculateSimultaneity(chats: Int, totalStaffs: Int) =
        if (totalStaffs != 0) (chats.toDouble() / totalStaffs).toBigDecimal()
            .setScale(2, RoundingMode.HALF_DOWN) else 0.0.toBigDecimal()

    private fun HealthPlanTask.prefix(): String {
        val healthPlanTaskTitle = title?.let { if (it.length <= 70) it else it.substring(0, 70) }

        return when (type) {
            PRESCRIPTION -> "Medicamento - $healthPlanTaskTitle"
            else -> "$healthPlanTaskTitle"
        }
    }

}
