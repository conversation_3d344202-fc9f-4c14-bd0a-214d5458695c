package br.com.alice.data.layer.services

import br.com.alice.common.core.PersonId
import br.com.alice.common.core.exceptions.DuplicatedItemException
import br.com.alice.data.layer.models.PersonModel
import br.com.alice.data.layer.pipelines.DatabasePipelineFactory
import br.com.alice.data.layer.tables.PersonTable
import com.github.kittinunf.result.Result
import com.github.kittinunf.result.flatMap
import com.github.kittinunf.result.mapError
import java.util.UUID
import br.com.alice.data.layer.exceptions.DuplicatedItemException as DBDuplicatedItemException

class PersonDataServiceImpl internal constructor(
    factory: DatabasePipelineFactory,
    private val personTokenService: PersonTokenService
) : BaseDataServiceImpl<PersonModel>(factory.get(PersonModel::class, PersonTable::class)), PersonModelDataService {

    override suspend fun add(model: PersonModel): Result<PersonModel, Throwable> =
        model.sanitize().let { person ->
            personTokenService.createForPersonId(person.id).flatMap {
                databasePipeline.add(person)
            }.mapError {
                if (it is DBDuplicatedItemException) DuplicatedItemException("Duplicated personId=${person.id}") else it
            }
        }

    override suspend fun update(model: PersonModel): Result<PersonModel, Throwable> =
        model.sanitize().let { person -> databasePipeline.update(person) }

    override suspend fun get(id: UUID): Result<PersonModel, Throwable> {
        return databasePipeline.get(PersonId(id))
    }
}
