package br.com.alice.product.services

import br.com.alice.common.core.exceptions.NotFoundException
import br.com.alice.common.data.dsl.matchers.ResultAssert.Companion.assertThat
import br.com.alice.common.data.dsl.matchers.queryEq
import br.com.alice.common.helpers.returns
import br.com.alice.common.kafka.interfaces.KafkaProducerService
import br.com.alice.common.service.data.dsl.and
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.AccommodationType
import br.com.alice.data.layer.models.ProductAnchor
import br.com.alice.data.layer.models.ProductBundleType
import br.com.alice.data.layer.models.ProductType
import br.com.alice.data.layer.models.TierType
import br.com.alice.data.layer.models.withPriceListing
import br.com.alice.data.layer.services.ProductModelDataService
import br.com.alice.product.client.ProductBundleService
import br.com.alice.product.client.ProductPriceListingService
import br.com.alice.product.converters.toModel
import br.com.alice.product.model.events.ProductUpdatedEvent
import br.com.alice.provider.client.ProviderService
import com.github.kittinunf.result.failure
import com.github.kittinunf.result.success
import io.mockk.coEvery
import io.mockk.mockk
import kotlinx.coroutines.runBlocking
import kotlin.test.Test

class ProductServiceTest {

    private val data: ProductModelDataService = mockk()
    private val productPriceListingService: ProductPriceListingService = mockk()
    private val productBundleService: ProductBundleService = mockk()
    private val providerService: ProviderService = mockk()
    private val kafkaProducerService: KafkaProducerService = mockk()
    private val service = ProductServiceImpl(
        data,
        productPriceListingService,
        productBundleService,
        providerService,
        kafkaProducerService
    )

    @Test
    fun `#getActiveAndVisibleForSaleOrFindSimilar should return actual product when it is active and is visible for sale`() =
        runBlocking {
            val product = TestModelFactory.buildProduct(
                active = true,
                isVisibleForSale = true,
                type = ProductType.B2C
            )
            val productModel = product.toModel()
            val priceListing = TestModelFactory.buildPriceListing()

            coEvery { data.get(product.id) } returns productModel.success()
            coEvery { productPriceListingService.getCurrentPriceListing(product.id) } returns priceListing.success()

            val result = service.getActiveAndVisibleForSaleOrFindSimilar(product.id)
            assertThat(result).isSuccessWithData(product.withPriceListing(priceListing))
        }

    @Test
    fun `#updateAndPublishEvent should update and publish event`() =
        runBlocking {
            val product = TestModelFactory.buildProduct(
                active = true,
                isVisibleForSale = true,
                type = ProductType.B2C
            )
            val newProduct = product.copy(tier = TierType.TIER_1)
            val newProductModel = newProduct.toModel()
            val expectedEvent = ProductUpdatedEvent(product, newProduct)

            coEvery { data.update(newProductModel) } returns newProductModel.success()
            coEvery { kafkaProducerService.produce(expectedEvent) } returns mockk()

            val result = service.updateAndPublishEvent(oldModel = product, newModel = newProduct)
            assertThat(result).isSuccessWithData(newProduct)
        }

    @Test
    fun `#getActiveAndVisibleForSaleOrFindSimilar should return updated product with same hospitals when it is not active anymore`() =
        runBlocking {
            val bundle1 = TestModelFactory.buildProductBundle(
                type = ProductBundleType.HOSPITAL
            )
            val bundle2 = TestModelFactory.buildProductBundle(
                type = ProductBundleType.MATERNITY
            )
            val bundle3 = TestModelFactory.buildProductBundle(
                type = ProductBundleType.HOSPITAL
            )
            val bundle4 = TestModelFactory.buildProductBundle(
                type = ProductBundleType.LABORATORY
            )
            val product = TestModelFactory.buildProduct(
                bundleIds = listOf(bundle1.id, bundle2.id),
                active = false,
                isVisibleForSale = true,
                accommodation = AccommodationType.ROOM
            )
            val productModel = product.toModel()
            val aliceFull = TestModelFactory.buildProduct(
                bundleIds = listOf(bundle3.id, bundle4.id),
                active = true,
                anchor = ProductAnchor.ALICE_FULL
            )
            val aliceFullModel = aliceFull.toModel()
            val updatedProduct = TestModelFactory.buildProduct(
                bundleIds = listOf(bundle1.id, bundle2.id, bundle4.id),
                active = true,
            )
            val updatedProductModel = updatedProduct.toModel()
            val priceListing = TestModelFactory.buildPriceListing()

            coEvery { data.get(product.id) } returns productModel.success()
            coEvery {
                productBundleService.findActivesByIds(product.bundleIds!!)
            } returns listOf(bundle1, bundle2).success()
            coEvery { data.getByAnchor(ProductAnchor.ALICE_FULL) } returns aliceFullModel.success()
            coEvery {
                productBundleService.findActivesByIds(aliceFull.bundleIds!!)
            } returns listOf(bundle3, bundle4).success()
            coEvery {
                data.find(queryEq {
                    where {
                        this.active.eq(true) and
                                this.bundleIds.containsAll(listOf(bundle4.id, bundle1.id, bundle2.id)) and
                                this.accommodation.eq(product.accommodation!!.name) and
                                this.type.eq(ProductType.B2C.name) and
                                this.isVisibleForSale.eq(true)
                    }
                })
            } returns listOf(updatedProductModel).success()
            coEvery {
                productBundleService.findActivesByIds(updatedProduct.bundleIds!!)
            } returns listOf(bundle1, bundle2, bundle4).success()
            coEvery { productPriceListingService.getCurrentPriceListing(updatedProduct.id) } returns priceListing.success()


            val result = service.getActiveAndVisibleForSaleOrFindSimilar(product.id)
            assertThat(result).isSuccessWithData(updatedProduct.withPriceListing(priceListing))

        }

    @Test
    fun `#getActiveAndVisibleForSaleOrFindSimilar should return updated product with same hospitals when it is not visible for sale anymore`() =
        runBlocking {
            val bundle1 = TestModelFactory.buildProductBundle(
                type = ProductBundleType.HOSPITAL
            )
            val bundle2 = TestModelFactory.buildProductBundle(
                type = ProductBundleType.MATERNITY
            )
            val bundle3 = TestModelFactory.buildProductBundle(
                type = ProductBundleType.HOSPITAL
            )
            val bundle4 = TestModelFactory.buildProductBundle(
                type = ProductBundleType.LABORATORY
            )
            val product = TestModelFactory.buildProduct(
                bundleIds = listOf(bundle1.id, bundle2.id),
                active = true,
                isVisibleForSale = false,
                accommodation = AccommodationType.ROOM
            )
            val productModel = product.toModel()
            val aliceFull = TestModelFactory.buildProduct(
                bundleIds = listOf(bundle3.id, bundle4.id),
                active = true,
                anchor = ProductAnchor.ALICE_FULL
            )
            val aliceFullModel = aliceFull.toModel()
            val updatedProduct = TestModelFactory.buildProduct(
                bundleIds = listOf(bundle1.id, bundle2.id, bundle4.id),
                active = true,
            )
            val updatedProductModel = updatedProduct.toModel()
            val priceListing = TestModelFactory.buildPriceListing()

            coEvery { data.get(product.id) } returns productModel.success()
            coEvery {
                productBundleService.findActivesByIds(product.bundleIds!!)
            } returns listOf(bundle1, bundle2).success()
            coEvery { data.getByAnchor(ProductAnchor.ALICE_FULL) } returns aliceFullModel.success()
            coEvery {
                productBundleService.findActivesByIds(aliceFull.bundleIds!!)
            } returns listOf(bundle3, bundle4).success()
            coEvery {
                data.find(queryEq {
                    where {
                        this.active.eq(true) and
                                this.bundleIds.containsAll(listOf(bundle4.id, bundle1.id, bundle2.id)) and
                                this.accommodation.eq(product.accommodation!!.name) and
                                this.type.eq(ProductType.B2C.name) and
                                this.isVisibleForSale.eq(true)
                    }
                })
            } returns listOf(updatedProductModel).success()
            coEvery {
                productBundleService.findActivesByIds(updatedProduct.bundleIds!!)
            } returns listOf(bundle1, bundle2, bundle4).success()
            coEvery { productPriceListingService.getCurrentPriceListing(updatedProduct.id) } returns priceListing.success()


            val result = service.getActiveAndVisibleForSaleOrFindSimilar(product.id)
            assertThat(result).isSuccessWithData(updatedProduct.withPriceListing(priceListing))

        }

    @Test
    fun `#getActiveAndVisibleForSaleOrFindSimilar should return alice full product with hospital and maternity bundles are not active or visible for sale anymore`() =
        runBlocking {
            val bundle1 = TestModelFactory.buildProductBundle(
                type = ProductBundleType.HOSPITAL
            )
            val bundle2 = TestModelFactory.buildProductBundle(
                type = ProductBundleType.MATERNITY
            )
            val bundle3 = TestModelFactory.buildProductBundle(
                type = ProductBundleType.HOSPITAL
            )
            val bundle4 = TestModelFactory.buildProductBundle(
                type = ProductBundleType.LABORATORY
            )
            val product = TestModelFactory.buildProduct(
                bundleIds = listOf(bundle1.id, bundle2.id),
                active = false,
                accommodation = AccommodationType.ROOM
            )
            val productModel = product.toModel()
            val aliceFull = TestModelFactory.buildProduct(
                bundleIds = listOf(bundle3.id, bundle4.id),
                active = true,
                anchor = ProductAnchor.ALICE_FULL
            )
            val aliceFullModel = aliceFull.toModel()
            val priceListing = TestModelFactory.buildPriceListing()

            coEvery { data.get(product.id) } returns productModel.success()
            coEvery {
                productBundleService.findActivesByIds(product.bundleIds!!)
            } returns listOf(bundle1, bundle2).success()
            coEvery { data.getByAnchor(ProductAnchor.ALICE_FULL) } returns aliceFullModel.success()
            coEvery {
                productBundleService.findActivesByIds(aliceFull.bundleIds!!)
            } returns listOf(bundle3, bundle4).success()
            coEvery {
                data.find(queryEq {
                    where {
                        this.active.eq(true) and
                                this.bundleIds.containsAll(listOf(bundle4.id, bundle1.id, bundle2.id)) and
                                this.accommodation.eq(product.accommodation!!.name) and
                                this.type.eq(ProductType.B2C.name) and
                                this.isVisibleForSale.eq(true)
                    }
                })
            } returns NotFoundException().failure()
            coEvery { productPriceListingService.getCurrentPriceListing(aliceFull.id) } returns priceListing.success()


            val result = service.getActiveAndVisibleForSaleOrFindSimilar(product.id)
            assertThat(result).isSuccessWithData(aliceFull.withPriceListing(priceListing))

        }

    @Test
    fun `#getActiveAndVisibleForSaleOrFindSimilar should return B2B updated product with same hospitals when it is not active or visible for sale anymore`() =
        runBlocking {
            val bundle1 = TestModelFactory.buildProductBundle(
                type = ProductBundleType.HOSPITAL
            )
            val bundle2 = TestModelFactory.buildProductBundle(
                type = ProductBundleType.MATERNITY
            )
            val bundle3 = TestModelFactory.buildProductBundle(
                type = ProductBundleType.HOSPITAL
            )
            val bundle4 = TestModelFactory.buildProductBundle(
                type = ProductBundleType.LABORATORY
            )
            val product = TestModelFactory.buildProduct(
                bundleIds = listOf(bundle1.id, bundle2.id),
                active = false,
                isVisibleForSale = true,
                accommodation = AccommodationType.ROOM
            )
            val productModel = product.toModel()
            val aliceFull = TestModelFactory.buildProduct(
                bundleIds = listOf(bundle3.id, bundle4.id),
                active = true,
                anchor = ProductAnchor.ALICE_FULL_B2B
            )
            val aliceFullModel = aliceFull.toModel()
            val updatedProduct = TestModelFactory.buildProduct(
                bundleIds = listOf(bundle1.id, bundle2.id, bundle4.id),
                active = true,
            )
            val updatedProductModel = updatedProduct.toModel()
            val priceListing = TestModelFactory.buildPriceListing()

            coEvery { data.get(product.id) } returns productModel.success()
            coEvery {
                productBundleService.findActivesByIds(product.bundleIds!!)
            } returns listOf(bundle1, bundle2).success()
            coEvery { data.getByAnchor(ProductAnchor.ALICE_FULL_B2B) } returns aliceFullModel.success()
            coEvery {
                productBundleService.findActivesByIds(aliceFull.bundleIds!!)
            } returns listOf(bundle3, bundle4).success()
            coEvery {
                data.find(queryEq {
                    where {
                        this.active.eq(true) and
                                this.bundleIds.containsAll(listOf(bundle4.id, bundle1.id, bundle2.id)) and
                                this.accommodation.eq(product.accommodation!!.name) and
                                this.type.eq(ProductType.B2B.name) and
                                this.isVisibleForSale.eq(true)
                    }
                })
            } returns listOf(updatedProductModel).success()
            coEvery {
                productBundleService.findActivesByIds(updatedProduct.bundleIds!!)
            } returns listOf(bundle1, bundle2, bundle4).success()
            coEvery { productPriceListingService.getCurrentPriceListing(updatedProduct.id) } returns priceListing.success()


            val result = service.getActiveAndVisibleForSaleOrFindSimilar(product.id, ProductType.B2B)
            assertThat(result).isSuccessWithData(updatedProduct.withPriceListing(priceListing))

        }

    @Test
    fun `#getActiveAndVisibleForSaleOrFindSimilar should return alice full B2B product with hospital and maternity bundles are not active or visible for sale anymore`() =
        runBlocking {
            val bundle1 = TestModelFactory.buildProductBundle(
                type = ProductBundleType.HOSPITAL
            )
            val bundle2 = TestModelFactory.buildProductBundle(
                type = ProductBundleType.MATERNITY
            )
            val bundle3 = TestModelFactory.buildProductBundle(
                type = ProductBundleType.HOSPITAL
            )
            val bundle4 = TestModelFactory.buildProductBundle(
                type = ProductBundleType.LABORATORY
            )
            val product = TestModelFactory.buildProduct(
                bundleIds = listOf(bundle1.id, bundle2.id),
                active = false,
                isVisibleForSale = true,
                accommodation = AccommodationType.ROOM
            )
            val productModel = product.toModel()
            val aliceFull = TestModelFactory.buildProduct(
                bundleIds = listOf(bundle3.id, bundle4.id),
                active = true,
                anchor = ProductAnchor.ALICE_FULL_B2B
            )
            val aliceFullModel = aliceFull.toModel()
            val priceListing = TestModelFactory.buildPriceListing()

            coEvery { data.get(product.id) } returns productModel.success()
            coEvery {
                productBundleService.findActivesByIds(product.bundleIds!!)
            } returns listOf(bundle1, bundle2).success()
            coEvery { data.getByAnchor(ProductAnchor.ALICE_FULL_B2B) } returns aliceFullModel.success()
            coEvery {
                productBundleService.findActivesByIds(aliceFull.bundleIds!!)
            } returns listOf(bundle3, bundle4).success()
            coEvery {
                data.find(queryEq {
                    where {
                        this.active.eq(true) and
                                this.bundleIds.containsAll(listOf(bundle4.id, bundle1.id, bundle2.id)) and
                                this.accommodation.eq(product.accommodation!!.name) and
                                this.type.eq(ProductType.B2B.name) and
                                this.isVisibleForSale.eq(true)
                    }
                })
            } returns NotFoundException().failure()
            coEvery {
                productPriceListingService.getCurrentPriceListing(aliceFull.id)
            } returns priceListing.success()

            val result = service.getActiveAndVisibleForSaleOrFindSimilar(product.id, ProductType.B2B)
            assertThat(result).isSuccessWithData(aliceFull.withPriceListing(priceListing))

        }


    @Test
    fun `#getByTitle should return a product by a title that matches exactly with product title`() = runBlocking {
        val productTitlesSearch = listOf("Product Title Test")
        val product = TestModelFactory.buildProduct(title = "Product Title Test")
        val productModel = product.toModel()

        coEvery {
            data.find(queryEq { where { this.title.inList(productTitlesSearch) } })
        } returns listOf(productModel).success()


        val result = service.getByTitles(productTitlesSearch)
        assertThat(result).isSuccessWithData(listOf(product))
    }

    @Test
    fun `#findActiveByTitles should return an active product by a title that matches exactly with product title`() = runBlocking {
        val productTitlesSearch = listOf("Product Title Test")
        val product = TestModelFactory.buildProduct(title = "Product Title Test")
        val productModel = product.toModel()

        coEvery {
            data.find(queryEq { where { this.title.inList(productTitlesSearch) and this.active.eq(true) } })
        } returns listOf(productModel).success()


        val result = service.findActiveByTitles(productTitlesSearch)
        assertThat(result).isSuccessWithData(listOf(product))
    }
    @Test
    fun `#listByAnsNumber should return product`() = runBlocking {
        val product1 = TestModelFactory.buildProduct(title = "Product Title Test", ansNumber = "ans-number")
        val product2 = TestModelFactory.buildProduct(title = "Product Title Test 2", ansNumber = "ans-number")
        val products = listOf(product1, product2)
        val productModels = products.map { it.toModel() }
        coEvery {
            data.find(queryEq { where { this.ansNumber.eq("ans-number") } })
        } returns productModels

        val result = service.listByAnsNumber("ans-number")
        assertThat(result).isSuccessWithData(products)
    }
}
