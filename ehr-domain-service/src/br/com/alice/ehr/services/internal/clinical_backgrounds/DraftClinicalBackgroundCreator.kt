package br.com.alice.ehr.services.internal.clinical_backgrounds

import br.com.alice.data.layer.models.ClinicalBackground
import br.com.alice.ehr.services.internal.draft_commands.DraftCommandsCreator

class DraftClinicalBackgroundCreator(
    private val background: ClinicalBackground,
    private val backgroundStateValidator: ClinicalBackgroundStateValidator,
    private val commandCreator: DraftCommandsCreator,
) {
    suspend fun create(): ClinicalBackground {
        backgroundStateValidator.validate()

        commandCreator.createAddCommand(background)

        return background
    }
}
