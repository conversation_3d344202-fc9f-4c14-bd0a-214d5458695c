package br.com.alice.common.featureaccess.features

import br.com.alice.authentication.UserType
import br.com.alice.common.core.BaseConfig
import br.com.alice.common.featureaccess.Feature
import br.com.alice.common.featureaccess.FeatureAccessContext
import br.com.alice.common.featureaccess.FeatureAccessRequest
import br.com.alice.common.featureaccess.FeatureAccessResult
import br.com.alice.common.featureaccess.FeatureAccessSource
import br.com.alice.common.featureaccess.FeatureNavigation
import br.com.alice.common.featureaccess.NavigationRouting
import org.assertj.core.api.Assertions.assertThat
import kotlin.test.Test

class FaqFeedbackFeatureTest {

    @Test
    fun `#filter should return route none and expected message if user is non-member and feedback was not useful`() {
        val context = FeatureAccessContext(
            source = FeatureAccessSource.APP,
            serviceConfig = BaseConfig.instance,
            userType = UserType.NON_MEMBER,
        )

        val resource = FaqFeedbackFeatureResource(useful = false)

        val expectedResult = FeatureAccessResult(
            feature = Feature.FAQ_FEEDBACK,
            response = resource.copy(
                feedbackMessage = "Que pena! Mas ainda podemos te ajudar, basta mandar um <NAME_EMAIL>"
            ),
            featureNavigation = FeatureNavigation(
                routing = NavigationRouting.NONE,
            ),
        )

        val request = FeatureAccessRequest(
            context = context,
            resource = resource,
        )

        val result = FaqFeedbackFeature.filter(request)
        assertThat(result).isEqualTo(expectedResult)
    }

    @Test
    fun `#filter should return route AA and expected message if user is member and feedback was not useful`() {
        val context = FeatureAccessContext(
            source = FeatureAccessSource.APP,
            serviceConfig = BaseConfig.instance,
            userType = UserType.MEMBER,
        )

        val resource = FaqFeedbackFeatureResource(useful = false)

        val expectedResult = FeatureAccessResult(
            feature = Feature.FAQ_FEEDBACK,
            response = resource.copy(
                feedbackMessage = "Ok. Se quiser, fale com a gente."
            ),
            featureNavigation = FeatureNavigation(
                routing = NavigationRouting.EMERGENCY_CARE,
            ),
        )

        val request = FeatureAccessRequest(
            context = context,
            resource = resource,
        )

        val result = FaqFeedbackFeature.filter(request)
        assertThat(result).isEqualTo(expectedResult)
    }

    @Test
    fun `#filter should return route none and expected message if user is member and feedback was useful`() {
        val context = FeatureAccessContext(
            source = FeatureAccessSource.APP,
            serviceConfig = BaseConfig.instance,
            userType = UserType.NON_MEMBER,
        )

        val resource = FaqFeedbackFeatureResource(useful = true)

        val expectedResult = FeatureAccessResult(
            feature = Feature.FAQ_FEEDBACK,
            response = resource.copy(
                feedbackMessage = "Demais!\nQue bom que te ajudou \uD83D\uDE0D"
            ),
            featureNavigation = FeatureNavigation(
                routing = NavigationRouting.NONE,
            ),
        )

        val request = FeatureAccessRequest(
            context = context,
            resource = resource,
        )

        val result = FaqFeedbackFeature.filter(request)
        assertThat(result).isEqualTo(expectedResult)
    }

    @Test
    fun `#filter should return route AA and expected message if user is member and feedback was useful`() {
        val context = FeatureAccessContext(
            source = FeatureAccessSource.APP,
            serviceConfig = BaseConfig.instance,
            userType = UserType.MEMBER,
        )

        val resource = FaqFeedbackFeatureResource(useful = true)

        val expectedResult = FeatureAccessResult(
            feature = Feature.FAQ_FEEDBACK,
            response = resource.copy(
                feedbackMessage = "Demais!\nQue bom que te ajudou \uD83D\uDE0D"
            ),
            featureNavigation = FeatureNavigation(
                routing = NavigationRouting.EMERGENCY_CARE,
            ),
        )

        val request = FeatureAccessRequest(
            context = context,
            resource = resource,
        )

        val result = FaqFeedbackFeature.filter(request)
        assertThat(result).isEqualTo(expectedResult)
    }
}
