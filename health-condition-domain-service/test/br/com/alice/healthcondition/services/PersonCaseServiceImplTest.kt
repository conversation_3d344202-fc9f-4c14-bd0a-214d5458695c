package br.com.alice.healthcondition.services

import br.com.alice.common.RangeUUID
import br.com.alice.common.core.PersonId
import br.com.alice.common.core.exceptions.NotFoundException
import br.com.alice.common.data.dsl.matchers.ResultAssert.Companion.assertThat
import br.com.alice.common.data.dsl.matchers.queryEq
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.common.helpers.mockLocalDateTime
import br.com.alice.common.kafka.interfaces.KafkaProducerService
import br.com.alice.common.service.data.dsl.SortOrder
import br.com.alice.common.service.data.dsl.and
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.CaseRecordReference
import br.com.alice.data.layer.models.CaseRecordReferenceModel
import br.com.alice.data.layer.models.CaseSeriousness
import br.com.alice.data.layer.models.CaseSeverity
import br.com.alice.data.layer.models.CaseStatus
import br.com.alice.common.Disease
import br.com.alice.data.layer.models.Follow
import br.com.alice.data.layer.services.PersonCaseDataService
import br.com.alice.healthcondition.event.PersonCaseUpdatedEvent
import br.com.alice.healthcondition.model.PersonCaseFilterRequest
import com.github.kittinunf.result.failure
import com.github.kittinunf.result.success
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.confirmVerified
import io.mockk.mockk
import kotlinx.coroutines.runBlocking
import java.time.LocalDateTime
import kotlin.test.AfterTest
import kotlin.test.Test

class PersonCaseServiceImplTest {

    private val personCaseDataService: PersonCaseDataService = mockk()
    private val kafkaProducerService: KafkaProducerService = mockk()

    private val service = PersonCaseServiceImpl(personCaseDataService, kafkaProducerService)
    private val testPersonId = PersonId()

    private val baseStatuses = listOf(CaseStatus.ACTIVE, CaseStatus.PENDING, CaseStatus.CONTEMPLATING)

    private val caseRecord = TestModelFactory.buildCaseRecord(
        personId = testPersonId,
        healthConditionId = RangeUUID.generate(),
        addedAt = LocalDateTime.of(2022, 9, 10, 10, 10),
        seriousness = CaseSeriousness.LOW
    )
    private val personCase = TestModelFactory.buildPersonCase(
        id = caseRecord.caseId,
        personId = testPersonId,
        addedAt = LocalDateTime.of(2022, 5, 10, 10, 10)
    )
    private val expectedPersonCase = personCase.copy(
        recordId = caseRecord.id,
        follow = caseRecord.follow,
        responsibleStaffId = caseRecord.responsibleStaffId,
        addedByStaffId = caseRecord.addedByStaffId,
        codeType = caseRecord.description.type,
        codeValue = caseRecord.description.value,
        codeDescription = caseRecord.description.description,
        observation = caseRecord.observation,
        severity = caseRecord.severity,
        status = caseRecord.status,
        addedAt = caseRecord.addedAt,
        startedAt = caseRecord.startedAt,
        healthConditionId = caseRecord.healthConditionId!!,
        seriousness = caseRecord.seriousness
    )

    @AfterTest
    fun confirmMocks() = confirmVerified(
        personCaseDataService,
        kafkaProducerService
    )

    @Test
    fun `#getByHealthConditionId - should return by person id and health condition id`() = runBlocking {
        val healthConditionId = RangeUUID.generate()
        coEvery {
            personCaseDataService.findOne(
                queryEq {
                    where {
                        this.personId.eq(testPersonId) and
                                this.healthConditionId.eq(healthConditionId) and
                                this.status.inList(baseStatuses)
                    }
                }
            )
        } returns personCase.success()

        val result = service.getByHealthConditionId(testPersonId, healthConditionId)
        assertThat(result).isSuccessWithData(personCase)

        coVerifyOnce { personCaseDataService.findOne(any()) }
    }

    @Test
    fun `#getByHealthConditionIds - should return by person id and health condition ids`() = runBlocking {
        val healthConditionIds = listOf(RangeUUID.generate())
        coEvery {
            personCaseDataService.find(
                queryEq {
                    where {
                        this.personId.eq(testPersonId) and
                                this.status.inList(baseStatuses) and
                                this.healthConditionId.inList(healthConditionIds)
                    }.orderBy { addedAt }
                        .sortOrder { SortOrder.Descending }
                }
            )
        } returns listOf(personCase).success()

        val result = service.getByHealthConditionIds(testPersonId, healthConditionIds)
        assertThat(result).isSuccessWithData(listOf(personCase))

        coVerifyOnce { personCaseDataService.find(any()) }
    }

    @Test
    fun `#findByIds - should return by list of ids`() = runBlocking {
        val personCaseIdList = listOf(personCase.id)

        coEvery {
            personCaseDataService.find(
                queryEq {
                    where { this.id.inList(personCaseIdList) }
                }
            )
        } returns listOf(personCase).success()

        val result = service.findByIds(personCaseIdList)
        assertThat(result).isSuccessWithData(listOf(personCase))

        coVerifyOnce { personCaseDataService.find(any()) }
    }

    @Test
    fun `#getByPersonAndCodesType - should return person cases by person and list of codes types`() = runBlocking {
        val codesTypes = listOf(Disease.Type.CID_10, Disease.Type.CIAP_2)
        coEvery {
            personCaseDataService.find(
                queryEq {
                    where {
                        this.personId.eq(testPersonId) and
                                this.codeType.inList(codesTypes) and
                                this.status.inList(baseStatuses)
                    }.orderBy { addedAt }
                        .sortOrder { SortOrder.Descending }
                }
            )
        } returns listOf(personCase).success()

        val result = service.getByPersonAndCodesTypes(testPersonId, codesTypes)
        assertThat(result).isSuccessWithData(listOf(personCase))

        coVerifyOnce { personCaseDataService.find(any()) }
    }

    @Test
    fun `#getByPersonId returns person cases by person and list statuses`() = runBlocking {
        coEvery {
            personCaseDataService.find(
                queryEq {
                    where {
                        this.personId.eq(testPersonId) and
                                this.status.inList(baseStatuses)
                    }.orderBy { addedAt }
                        .sortOrder { SortOrder.Descending }
                }
            )
        } returns listOf(personCase).success()

        val result = service.getByPersonId(testPersonId)
        assertThat(result).isSuccessWithData(listOf(personCase))

        coVerifyOnce { personCaseDataService.find(any()) }
    }

    @Test
    fun `#get returns person cases by id`() = runBlocking {
        coEvery { personCaseDataService.get(personCase.id) } returns personCase.success()

        val result = service.get(personCase.id)
        assertThat(result).isSuccessWithData(personCase)

        coVerifyOnce { personCaseDataService.get(any()) }
    }

    @Test
    fun `#findByPersonId - should return list limited of personCase by personId`() = runBlocking {
        val offSet = 0
        val limit = 10
        coEvery {
            personCaseDataService.find(
                queryEq {
                    where {
                        this.personId.eq(testPersonId) and
                                this.status.inList(baseStatuses)
                    }
                        .orderBy { addedAt }
                        .sortOrder { SortOrder.Descending }
                        .offset { offSet }
                        .limit { limit }
                }
            )
        } returns listOf(personCase).success()

        val result = service.findByPersonId(testPersonId, offSet, limit)
        assertThat(result).isSuccessWithData(listOf(personCase))

        coVerify { personCaseDataService.find(any()) }
    }


    @Test
    fun `#findByPersonAndCodesTypes - should return list limited of personCase by personId and codesTypes`() =
        runBlocking {
            val codesTypes = listOf(Disease.Type.CID_10, Disease.Type.CIAP_2)
            val baseStatuses = baseStatuses
            val offSet = 0
            val limit = 10

            coEvery {
                personCaseDataService.find(
                    queryEq {
                        where {
                            this.personId.eq(testPersonId) and
                                    this.codeType.inList(codesTypes) and
                                    this.status.inList(baseStatuses)
                        }
                            .orderBy { addedAt }
                            .sortOrder { SortOrder.Descending }
                            .offset { offSet }
                            .limit { limit }

                    }
                )
            } returns listOf(personCase).success()

            val result = service.findByPersonAndCodesTypes(testPersonId, codesTypes, offSet, limit)
            assertThat(result).isSuccessWithData(listOf(personCase))

            coVerifyOnce { personCaseDataService.find(any()) }

        }

    @Test
    fun `#findFilters returns error when filter is empty`() = runBlocking {
        val request = PersonCaseFilterRequest()

        val result = service.findFilters(request)
        assertThat(result).isFailureOfType(IllegalArgumentException::class)
    }

    @Test
    fun `#findFilters - should return find by filter of types and status`() = runBlocking {
        val request = PersonCaseFilterRequest(
            personId = testPersonId,
            range = IntRange(0, 20),
            types = listOf(Disease.Type.CIAP_2, Disease.Type.CID_10),
            status = listOf(CaseStatus.PENDING),
            severities = emptyList(),
            staffsIds = emptyList(),
            seriousnesses = emptyList()
        )

        coEvery {
            personCaseDataService.find(
                queryEq {
                    where {
                        this.personId.eq(testPersonId) and
                                this.codeType.inList(listOf(Disease.Type.CIAP_2, Disease.Type.CID_10)) and
                                this.status.inList(listOf(CaseStatus.PENDING))
                    }
                        .orderBy { addedAt }
                        .sortOrder { SortOrder.Descending }
                        .offset { 0 }
                        .limit { 21 }
                }
            )
        } returns listOf(personCase).success()

        val result = service.findFilters(request)
        assertThat(result).isSuccessWithData(listOf(personCase))

        coVerifyOnce { personCaseDataService.find(any()) }
    }

    @Test
    fun `#findFilters - should return find by filter of all`() = runBlocking {
        val staffId = RangeUUID.generate()
        val request = PersonCaseFilterRequest(
            personId = testPersonId,
            range = IntRange(0, 20),
            types = listOf(Disease.Type.CIAP_2, Disease.Type.CID_10),
            status = listOf(CaseStatus.PENDING),
            severities = listOf(CaseSeverity.COMPENSATED),
            staffsIds = listOf(staffId),
            seriousnesses = listOf(CaseSeriousness.LOW)
        )

        coEvery {
            personCaseDataService.find(
                queryEq {
                    where {
                        this.personId.eq(testPersonId) and
                                this.codeType.inList(listOf(Disease.Type.CIAP_2, Disease.Type.CID_10)) and
                                this.status.inList(listOf(CaseStatus.PENDING)) and
                                this.severity.inList(listOf(CaseSeverity.COMPENSATED)) and
                                this.responsibleStaffId.inList(listOf(staffId)) and
                                this.seriousness.inList(listOf(CaseSeriousness.LOW))
                    }
                        .orderBy { addedAt }
                        .sortOrder { SortOrder.Descending }
                        .offset { 0 }
                        .limit { 21 }
                }
            )
        } returns listOf(personCase).success()

        val result = service.findFilters(request)
        assertThat(result).isSuccessWithData(listOf(personCase))

        coVerifyOnce { personCaseDataService.find(any()) }
    }

    @Test
    fun `#findFilters - should return find by without severity`() = runBlocking {
        val staffId = RangeUUID.generate()
        val request = PersonCaseFilterRequest(
            personId = testPersonId,
            range = IntRange(0, 20),
            types = listOf(Disease.Type.CIAP_2, Disease.Type.CID_10),
            status = listOf(CaseStatus.PENDING),
            severities = emptyList(),
            staffsIds = listOf(staffId),
            seriousnesses = listOf(CaseSeriousness.LOW)
        )

        coEvery {
            personCaseDataService.find(
                queryEq {
                    where {
                        this.personId.eq(testPersonId) and
                                this.codeType.inList(listOf(Disease.Type.CIAP_2, Disease.Type.CID_10)) and
                                this.status.inList(listOf(CaseStatus.PENDING)) and
                                this.responsibleStaffId.inList(listOf(staffId)) and
                                this.seriousness.inList(listOf(CaseSeriousness.LOW))
                    }
                        .orderBy { addedAt }
                        .sortOrder { SortOrder.Descending }
                        .offset { 0 }
                        .limit { 21 }
                }
            )
        } returns listOf(personCase).success()

        val result = service.findFilters(request)
        assertThat(result).isSuccessWithData(listOf(personCase))

        coVerifyOnce { personCaseDataService.find(any()) }
    }

    @Test
    fun `#findFilters returns person case when filter is full fill`() = runBlocking {
        val staffId = RangeUUID.generate()
        val healthConditionId = RangeUUID.generate()
        val channelId = "Test"
        val request = PersonCaseFilterRequest(
            personId = testPersonId,
            range = IntRange(0, 20),
            types = listOf(Disease.Type.CIAP_2, Disease.Type.CID_10),
            status = listOf(CaseStatus.PENDING),
            severities = listOf(CaseSeverity.COMPENSATED),
            staffsIds = listOf(staffId),
            seriousnesses = listOf(CaseSeriousness.LOW),
            channelId = channelId,
            healthConditionIds = listOf(healthConditionId)
        )

        coEvery {
            personCaseDataService.find(
                queryEq {
                    where {
                        this.personId.eq(testPersonId) and
                                this.codeType.inList(listOf(Disease.Type.CIAP_2, Disease.Type.CID_10)) and
                                this.status.inList(listOf(CaseStatus.PENDING)) and
                                this.severity.inList(listOf(CaseSeverity.COMPENSATED)) and
                                this.responsibleStaffId.inList(listOf(staffId)) and
                                this.seriousness.inList(listOf(CaseSeriousness.LOW)) and
                                this.channelId.eq(channelId) and
                                this.healthConditionId.inList(listOf(healthConditionId))
                    }
                        .orderBy { addedAt }
                        .sortOrder { SortOrder.Descending }
                        .offset { 0 }
                        .limit { 21 }
                }
            )
        } returns listOf(personCase).success()

        val result = service.findFilters(request)
        assertThat(result).isSuccessWithData(listOf(personCase))

        coVerifyOnce { personCaseDataService.find(any()) }
    }

    @Test
    fun `#createdByCaseRecord returns current when receives older record`() = runBlocking {
        val caseRecord = caseRecord.copy(addedAt = personCase.addedAt.minusDays(2))

        coEvery { personCaseDataService.get(caseRecord.caseId) } returns personCase.success()

        val result = service.createdByCaseRecord(caseRecord)
        assertThat(result).isSuccessWithData(personCase)

        coVerifyOnce { personCaseDataService.get(any()) }
    }

    @Test
    fun `#createdByCaseRecord updates when receives newer record`() = runBlocking {
        coEvery { personCaseDataService.get(caseRecord.caseId) } returns personCase.success()
        coEvery { personCaseDataService.update(expectedPersonCase) } returns expectedPersonCase.success()
        coEvery { kafkaProducerService.produce(PersonCaseUpdatedEvent(expectedPersonCase, caseRecord)) } returns mockk()

        val result = service.createdByCaseRecord(caseRecord)
        assertThat(result).isSuccessWithData(expectedPersonCase)

        coVerifyOnce { personCaseDataService.get(any()) }
        coVerifyOnce { personCaseDataService.update(any()) }
        coVerifyOnce { kafkaProducerService.produce(any()) }
    }

    @Test
    fun `#createdByCaseRecord - updates channels id when current is null`() = runBlocking {
        val expectedToUpdate = expectedPersonCase.copy(channelId = "channel_id")

        val caseRecord = caseRecord.copy(
            referencedLinks = listOf(
                CaseRecordReference(
                    "channel_id",
                    CaseRecordReferenceModel.CHANNEL
                )
            )
        )

        coEvery { personCaseDataService.get(caseRecord.caseId) } returns personCase.success()
        coEvery { personCaseDataService.update(expectedToUpdate) } returns expectedToUpdate.success()
        coEvery { kafkaProducerService.produce(PersonCaseUpdatedEvent(expectedToUpdate, caseRecord)) } returns mockk()

        val result = service.createdByCaseRecord(caseRecord)
        assertThat(result).isSuccessWithData(expectedToUpdate)

        coVerifyOnce { personCaseDataService.get(any()) }
        coVerifyOnce { personCaseDataService.update(any()) }
        coVerifyOnce { kafkaProducerService.produce(any()) }
    }

    @Test
    fun `#createdByCaseRecord - update with new channel id when already has one`() = runBlocking {
        val expectedToUpdate = expectedPersonCase.copy(channelId = "chanel_id")

        val caseRecord = caseRecord.copy(
            referencedLinks = listOf(
                CaseRecordReference(
                    "chanel_id",
                    CaseRecordReferenceModel.CHANNEL
                )
            )
        )

        coEvery {
            personCaseDataService.get(caseRecord.caseId)
        } returns personCase.copy(channelId = "channel_id_2").success()
        coEvery { personCaseDataService.update(expectedToUpdate) } returns expectedToUpdate.success()
        coEvery { kafkaProducerService.produce(PersonCaseUpdatedEvent(expectedToUpdate, caseRecord)) } returns mockk()

        val result = service.createdByCaseRecord(caseRecord)
        assertThat(result).isSuccessWithData(expectedToUpdate)

        coVerifyOnce { personCaseDataService.get(any()) }
        coVerifyOnce { personCaseDataService.update(any()) }
        coVerifyOnce { kafkaProducerService.produce(any()) }
    }

    @Test
    fun `#createdByCaseRecord - does not update channel id with null when already has one`() =
        runBlocking {
            val personCase = personCase.copy(
                channelId = "channel_id_3",
                follow = Follow.REFERRAL_ATTENDANCE_PHYSICIAN
            )

            val expectedToUpdate = expectedPersonCase.copy(channelId = "channel_id_3")
            val caseRecord = caseRecord.copy(referencedLinks = emptyList())

            coEvery { personCaseDataService.get(caseRecord.caseId) } returns personCase.success()
            coEvery { personCaseDataService.update(expectedToUpdate) } returns expectedToUpdate.success()
            coEvery {
                kafkaProducerService.produce(
                    PersonCaseUpdatedEvent(
                        expectedToUpdate,
                        caseRecord
                    )
                )
            } returns mockk()

            val result = service.createdByCaseRecord(caseRecord)
            assertThat(result).isSuccessWithData(expectedToUpdate)

            coVerifyOnce { personCaseDataService.get(any()) }
            coVerifyOnce { personCaseDataService.update(any()) }
            coVerifyOnce { kafkaProducerService.produce(any()) }
        }

    @Test
    fun `#createdByCaseRecord - adds new person case`() = runBlocking {
        val date = LocalDateTime.now()
        val expectedToAdd = expectedPersonCase.copy(createdAt = date)

        coEvery { personCaseDataService.get(caseRecord.caseId) } returns NotFoundException().failure()
        coEvery { personCaseDataService.add(expectedToAdd) } returns expectedToAdd.success()
        coEvery { kafkaProducerService.produce(PersonCaseUpdatedEvent(expectedToAdd, caseRecord)) } returns mockk()

        mockLocalDateTime(date) {
            val result = service.createdByCaseRecord(caseRecord)
            assertThat(result).isSuccessWithData(expectedToAdd)
        }

        coVerifyOnce { personCaseDataService.get(any()) }
        coVerifyOnce { personCaseDataService.add(any()) }
        coVerifyOnce { kafkaProducerService.produce(any()) }
    }

    @Test
    fun `#createdByCaseRecord - adds new person case with channel id`() = runBlocking {
        val date = LocalDateTime.now()

        val expectedToAdd = expectedPersonCase.copy(
            channelId = "chanel_id",
            createdAt = date
        )

        val caseRecord = caseRecord.copy(
            referencedLinks = listOf(
                CaseRecordReference(
                    "chanel_id",
                    CaseRecordReferenceModel.CHANNEL
                )
            )
        )

        coEvery { personCaseDataService.get(caseRecord.caseId) } returns NotFoundException().failure()
        coEvery { personCaseDataService.add(expectedToAdd) } returns expectedToAdd.success()
        coEvery { kafkaProducerService.produce(PersonCaseUpdatedEvent(expectedToAdd, caseRecord)) } returns mockk()

        mockLocalDateTime(date) {
            val result = service.createdByCaseRecord(caseRecord)
            assertThat(result).isSuccessWithData(expectedToAdd)
        }

        coVerifyOnce { personCaseDataService.get(any()) }
        coVerifyOnce { personCaseDataService.add(any()) }
        coVerifyOnce { kafkaProducerService.produce(any()) }
    }

    @Test
    fun `#getDecompensatedByDateAndType - should return list of ciap by date less`() = runBlocking {
        val date = LocalDateTime.now().minusDays(30)
        val type = Disease.Type.CIAP_2
        val limit = 100
        val offset = 0
        coEvery {
            personCaseDataService.find(
                queryEq {
                    where {
                        this.codeType.eq(type) and
                                this.addedAt.less(date) and
                                this.status.inList(listOf(CaseStatus.ACTIVE, CaseStatus.PENDING)) and
                                this.severity.eq(CaseSeverity.DECOMPENSATED)
                    }
                        .orderBy { this.createdAt }
                        .sortOrder { SortOrder.Ascending }
                        .offset { offset }
                        .limit { limit }
                }
            )
        } returns listOf(personCase).success()

        val result = service.getDecompensatedByDateAndType(date, type, limit, offset)
        assertThat(result).isSuccessWithData(listOf(personCase))

        coVerifyOnce { personCaseDataService.find(any()) }
    }

    @Test
    fun `#getCodeDescriptionIsNull - should return person that have code description null`() = runBlocking {
        coEvery {
            personCaseDataService.find(
                queryEq {
                    where { this.codeDescription.isNull() }
                        .orderBy { addedAt }
                        .sortOrder { SortOrder.Descending }
                        .offset { 0 }
                        .limit { 100 }
                }
            )
        } returns listOf(personCase).success()

        val result = service.getCodeDescriptionIsNull(0, 100)
        assertThat(result).isSuccessWithData(listOf(personCase))

        coVerifyOnce { personCaseDataService.find(any()) }
    }
}
