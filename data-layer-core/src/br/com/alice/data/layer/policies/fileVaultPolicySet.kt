package br.com.alice.data.layer.policies

import br.com.alice.data.layer.APPOINTMENT_ROOT_SERVICE_NAME
import br.com.alice.data.layer.FILE_VAULT_ROOT_SERVICE_NAME
import br.com.alice.data.layer.HEALTH_PLAN_DOMAIN_ROOT_SERVICE_NAME
import br.com.alice.data.layer.authorization.Create
import br.com.alice.data.layer.authorization.View
import br.com.alice.data.layer.authorization.policySet
import br.com.alice.data.layer.models.CompanyStaff
import br.com.alice.data.layer.models.FileVault
import br.com.alice.data.layer.models.HealthCommunitySpecialistModel
import br.com.alice.data.layer.models.StaffModel
import br.com.alice.data.layer.pipelines.subjects.PersonSubject
import br.com.alice.data.layer.pipelines.subjects.StaffSubject
import br.com.alice.data.layer.policies.features.deleteDependentFiles
import br.com.alice.data.layer.policies.features.viewAndUpdateAndDeleteHerDependentsFiles
import br.com.alice.data.layer.policies.features.viewAndUpdateAndDeleteHerOwnFiles
import br.com.alice.data.layer.policies.features.viewAndUpdateAnyContractFiles
import br.com.alice.data.layer.policies.features.viewAndUpdateAnyFile
import br.com.alice.data.layer.policies.features.viewAndUpdateAnyInsurancePortabilityFile
import br.com.alice.data.layer.policies.features.viewAndUpdateAnyTermFiles
import br.com.alice.data.layer.policies.features.viewAndUpdateTestPersonFiles
import br.com.alice.data.layer.policies.features.viewAnyExcuseNoteFiles
import br.com.alice.data.layer.policies.features.viewAnyMemberDocuments
import br.com.alice.data.layer.policies.features.viewAnyMemberPhoto
import br.com.alice.data.layer.policies.features.viewAnyPersonHealthDocuments
import br.com.alice.data.layer.policies.features.viewAnyPrescriptionFiles
import br.com.alice.data.layer.policies.features.viewAnyReferralFiles
import br.com.alice.data.layer.policies.features.viewAnyRefundFile
import br.com.alice.data.layer.policies.features.viewCreateUpdateDependentFiles
import br.com.alice.data.layer.policies.features.viewEhrFiles
import br.com.alice.data.layer.policies.features.viewUpdateCreateEhrFiles
import br.com.alice.data.layer.subjects.EmailDomain
import br.com.alice.data.layer.subjects.Unauthenticated

val fileVaultPolicySet = policySet {
    match("at file-vault", { rootService.name == FILE_VAULT_ROOT_SERVICE_NAME }) {
        match("Unauthenticated", { subject is Unauthenticated }) {
            includes(viewAnyPrescriptionFiles)
            includes(viewAnyExcuseNoteFiles)
            includes(viewAnyReferralFiles)

            match("can view", { action is View || action is Create }) {
                match("any excuse note file") {
                    resource is FileVault &&
                            resource.domain == "ehr" &&
                            resource.namespace == "signed_excuse_notes" &&
                            (subject as Unauthenticated).key == APPOINTMENT_ROOT_SERVICE_NAME
                }

                match("any health plan test request and prescription document") {
                    resource is FileVault &&
                            resource.domain == "ehr" &&
                            (resource.namespace == "test_request" ||
                                    resource.namespace == "signed_test_request" ||
                                    resource.namespace == "signed_prescription") &&
                            (subject as Unauthenticated).key == HEALTH_PLAN_DOMAIN_ROOT_SERVICE_NAME
                }
            }
        }
        match("EmailDomain", { subject is EmailDomain }) {
            includes(viewAnyMemberPhoto)
        }
        match("Health Professional or Navigator", { subject is StaffModel && subject.isHealthProfessionalOrNavigator() }) {
            includes(viewAndUpdateAnyFile)
        }
        match("PersonModel", { subject is PersonSubject }) {
            includes(viewAndUpdateAndDeleteHerOwnFiles)
            includes(viewAndUpdateAndDeleteHerDependentsFiles)
        }
        match("Prod and Tech", { subject is StaffModel && subject.isProductTech() }) {
            includes(viewAndUpdateAnyInsurancePortabilityFile)
            includes(viewAndUpdateTestPersonFiles)
            includes(viewAndUpdateAnyContractFiles)
            includes(viewAndUpdateAnyTermFiles)
        }
        match("Member Ops", { subject is StaffModel && subject.isMemberOps() }) {
            includes(viewAndUpdateAnyInsurancePortabilityFile)
        }
        match("Ops Role", { subject is StaffModel && subject.isOps() }) {
            includes(viewAndUpdateAnyInsurancePortabilityFile)
            includes(viewAnyMemberDocuments)
            includes(viewAndUpdateAnyContractFiles)
        }
        match("Chief Risk", { subject is StaffModel && subject.isChiefRisk() }) {
            includes(viewAndUpdateAnyInsurancePortabilityFile)
            includes(viewAnyMemberDocuments)
            includes(viewAndUpdateAnyContractFiles)
            includes(viewAnyMemberPhoto)
            includes(viewAnyPersonHealthDocuments)
            includes(viewUpdateCreateEhrFiles)
        }
        match("HealthCommunitySpecialistModel", { subject is HealthCommunitySpecialistModel }) {
            includes(viewAndUpdateAnyFile)
        }
        match("Health Specialist", { subject is StaffSubject && subject.isCommunity() }) {
            includes(viewAndUpdateAnyFile)
        }
        match("Med Excellence", { subject is StaffModel && subject.isMedEx() }) {
            includes(viewEhrFiles)
            includes(viewAnyMemberPhoto)
        }
        match("Health Ops Lead", { subject is StaffModel && subject.isHealthOpsLead() }) {
            includes(viewEhrFiles)
            includes(viewAnyMemberPhoto)
        }
        match("B2B - Ops or Ops Role", { subject is StaffModel && (subject.isB2BOps() || subject.isOps()) }) {
            includes(viewAndUpdateAnyContractFiles)
            includes(viewAndUpdateAnyTermFiles)
        }
        match("Navigator Chief or Chief Digital Care Nurse",
            { subject is StaffModel && (subject.isChiefNavigator() || subject.isChiefDigitalCareNurse()) }) {
            includes(viewAnyRefundFile)
        }
        match("CompanyStaff", { subject is CompanyStaff && subject.isMainCompanyStaff() }) {
            includes(viewCreateUpdateDependentFiles)
            includes(deleteDependentFiles)
        }
        match("Risk Nurse", { subject is StaffModel && subject.isRiskNurse() }) {
            includes(viewAndUpdateAnyTermFiles)
        }
    }
}
