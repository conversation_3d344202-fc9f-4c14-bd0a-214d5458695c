package br.com.alice.api.backoffice.controllers.staff

import br.com.alice.api.backoffice.ControllerTestHelper
import br.com.alice.api.backoffice.mappers.staff.StaffOutputMapper
import br.com.alice.api.backoffice.mappers.staff.StaffFormOutputMapper
import br.com.alice.api.backoffice.transfers.staff.CouncilTypeResponse
import br.com.alice.api.backoffice.transfers.staff.CreateStaffRequest
import br.com.alice.api.backoffice.transfers.staff.StaffFormResponse
import br.com.alice.common.models.CouncilType
import br.com.alice.common.models.Gender
import br.com.alice.common.core.Role
import io.ktor.http.HttpStatusCode
import br.com.alice.api.backoffice.transfers.staff.StaffRolesResponse
import br.com.alice.api.backoffice.transfers.staff.StaffTiersResponse
import br.com.alice.api.backoffice.transfers.staff.StaffScoreResponse
import br.com.alice.common.core.StaffType
import br.com.alice.common.storage.FileStorage
import br.com.alice.common.storage.FileType
import io.ktor.http.HttpMethod
import io.ktor.client.statement.bodyAsText
import br.com.alice.common.data.dsl.matchers.BFFResponseAssert.Companion.assertThat
import br.com.alice.common.helpers.bodyAsBFFJson
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.MedicalSpecialty
import br.com.alice.data.layer.models.MedicalSpecialtyType
import br.com.alice.data.layer.models.ProviderUnit
import br.com.alice.staff.client.StaffService
import br.com.alice.api.backoffice.services.StaffBackofficeService
import br.com.alice.api.backoffice.transfers.staff.StaffValidationErrorResponse
import io.mockk.coVerify
import br.com.alice.provider.client.MedicalSpecialtyService
import br.com.alice.provider.client.ProviderUnitService
import br.com.alice.common.RangeUUID
import br.com.alice.common.core.extensions.toSafeUUID
import br.com.alice.common.googlemaps.services.AutocompleteTransport
import br.com.alice.common.googlemaps.services.GoogleMapsService
import com.github.kittinunf.result.success
import com.github.kittinunf.result.Result
import br.com.alice.common.core.exceptions.NotFoundException
import io.ktor.http.Parameters
import io.mockk.clearAllMocks
import io.mockk.coEvery
import io.mockk.confirmVerified
import io.mockk.mockk
import kotlin.test.BeforeTest
import kotlin.test.Test
import org.assertj.core.api.Assertions.assertThat
import java.util.UUID

class StaffControllerTest : ControllerTestHelper() {

    private val staffService: StaffService = mockk()
    private val staffBackofficeService: StaffBackofficeService = mockk()
    private val medicalSpecialtyService: MedicalSpecialtyService = mockk()
    private val providerUnitService: ProviderUnitService = mockk()
    private val googleMapsService: GoogleMapsService = mockk()
    private val fileStorage: FileStorage = mockk()
    private val staffController = StaffController(
        staffService,
        staffBackofficeService,
        medicalSpecialtyService,
        providerUnitService,
        googleMapsService,
        fileStorage
    )

    private val staff1 = TestModelFactory.buildStaff()
    private val staffs = listOf(staff1)
    private val total = 2
    private val queryParams = Parameters.build {
        append("page", "1")
        append("pageSize", "10")
    }
    private val expectedResponse = StaffOutputMapper.toPaginatedResponse(staffs, total, queryParams)

    @BeforeTest
    override fun setup() {
        clearAllMocks()
        super.setup()
        module.single { staffController }

        // Default mocks for subspecialty tests
        coEvery { medicalSpecialtyService.getByRange(any(), any()) } returns emptyList<MedicalSpecialty>().success()
        coEvery { medicalSpecialtyService.count() } returns 0.success()
    }

    @BeforeTest
    fun confirmMocks() = confirmVerified(staffService, staffBackofficeService, medicalSpecialtyService, providerUnitService, googleMapsService, fileStorage)

    @Test
    fun `#index returns Staffs found by range`() {
        coEvery { staffService.findByRange(IntRange(0, 9)) } returns staffs.success()
        coEvery { staffService.count() } returns total.success()

        authenticatedAs(idToken, staff) {
            get("/staff?filter={}&page=1&pageSize=10&sort=[\"id\",\"DESC\"]") { response ->
                assertThat(response).isOKWithData(expectedResponse)
            }
        }

        coVerifyOnce { staffService.findByRange(any()) }
        coVerifyOnce { staffService.count() }
    }

    @Test
    fun `#index returns Staffs found by term`() {
        coEvery { staffService.findByTokenAndRange("STAFF", range = IntRange(0, 9)) } returns staffs.success()
        coEvery { staffService.countActiveByToken("STAFF") } returns total.success()

        authenticatedAs(idToken, staff) {
            get("/staff?filter={\"q\":\"STAFF\"}&page=1&pageSize=10&sort=[\"id\",\"DESC\"]") { response ->
                assertThat(response).isOKWithData(expectedResponse)
            }
        }

        coVerifyOnce { staffService.findByTokenAndRange(any(), any()) }
        coVerifyOnce { staffService.countActiveByToken(any()) }
    }



    @Test
    fun `#buildFormByStaffType returns form for COMMUNITY_SPECIALIST`() {
        // Mock data
        val specialties = listOf(MedicalSpecialty(
            name = "Specialty 1",
            type = MedicalSpecialtyType.SPECIALTY,
            urlSlug = "specialty-1"
        ))
        val providerUnits = listOf(ProviderUnit(
            name = "Provider Unit 1",
            id = UUID.randomUUID(),
            type = ProviderUnit.Type.HOSPITAL,
            site = "",
            cnpj = "",
            phones = emptyList(),
            workingPeriods = emptyList(),
            qualifications = emptyList(),
            imageUrl = null,
            providerId = UUID.randomUUID()
        ))
        val staffRoles = listOf(
            StaffRolesResponse(
                id = "role1",
                name = "Role 1",
                value = "ROLE1"
            )
        )
        val staffTiers = listOf(
            StaffTiersResponse(
                id = "tier1",
                name = "Tier 1",
                value = "TIER1"
            )
        )
        val staffScore = listOf(
            StaffScoreResponse(
                id = "score1",
                name = "Score 1",
                value = "SCORE1"
            )
        )
        val councilTypes = listOf(
            CouncilTypeResponse(
                id = 1,
                name = "CRM"
            )
        )

        // Mock service calls
        coEvery { medicalSpecialtyService.getActivesByType(any(), any()) } returns specialties.success()
        coEvery { providerUnitService.getByFilterWithRange(any(), any()) } returns providerUnits.success()
        coEvery { staffBackofficeService.getStaffRoles(StaffType.COMMUNITY_SPECIALIST) } returns staffRoles
        coEvery { staffBackofficeService.getStaffTiers() } returns staffTiers
        coEvery { staffBackofficeService.getStaffScore() } returns staffScore
        coEvery { staffBackofficeService.getCouncilTypes() } returns councilTypes

        // Expected response
        val expectedResponse = StaffFormResponse(
            specialties = specialties,
            providerUnits = providerUnits,
            staffRoles = staffRoles,
            staffTiers = staffTiers,
            staffScore = staffScore,
            councilTypes = councilTypes
        )

        authenticatedAs(idToken, staff) {
            get("/staff/build_staff?filter={\"type\":\"COMMUNITY_SPECIALIST\"}") { response ->
                val responseBody = response.bodyAsBFFJson<StaffFormResponse>()
                assertThat(responseBody.specialties).isNotNull
                assertThat(responseBody.providerUnits).isNotNull
                assertThat(responseBody.staffRoles).isNotNull
                assertThat(responseBody.staffTiers).isNotNull
                assertThat(responseBody.staffScore).isNotNull
                assertThat(responseBody.councilTypes).isNotNull
            }
        }

        // Verify service calls
        coVerifyOnce { medicalSpecialtyService.getActivesByType(any(), any()) }
        coVerifyOnce { providerUnitService.getByFilterWithRange(any(), any()) }
        coVerifyOnce { staffBackofficeService.getStaffRoles(StaffType.COMMUNITY_SPECIALIST) }
        coVerifyOnce { staffBackofficeService.getStaffTiers() }
        coVerifyOnce { staffBackofficeService.getStaffScore() }
        coVerifyOnce { staffBackofficeService.getCouncilTypes() }
    }

    @Test
    fun `#buildFormByStaffType returns form for PITAYA`() {
        // Mock data
        val staffRoles = listOf(
            StaffRolesResponse(
                id = "role1",
                name = "Role 1",
                value = "ROLE1"
            )
        )

        // Mock service calls
        coEvery { staffBackofficeService.getStaffRoles(StaffType.PITAYA) } returns staffRoles

        // Expected response
        val expectedResponse = StaffFormResponse(
            staffRoles = staffRoles
        )

        authenticatedAs(idToken, staff) {
            get("/staff/build_staff?filter={\"type\":\"PITAYA\"}") { response ->
                val responseBody = response.bodyAsBFFJson<StaffFormResponse>()
                assertThat(responseBody.specialties).isNull()
                assertThat(responseBody.providerUnits).isNull()
                assertThat(responseBody.staffRoles).isNotNull
                assertThat(responseBody.staffTiers).isNull()
                assertThat(responseBody.staffScore).isNull()
            }
        }

        // Verify service calls
        coVerifyOnce { staffBackofficeService.getStaffRoles(StaffType.PITAYA) }
    }

    @Test
    fun `#buildFormByStaffType returns form for HEALTH_ADMINISTRATIVE`() {
        // Mock data
        val staffRoles = listOf(
            StaffRolesResponse(
                id = "role1",
                name = "Role 1",
                value = "ROLE1"
            )
        )

        // Mock service calls
        coEvery { staffBackofficeService.getStaffRoles(StaffType.HEALTH_ADMINISTRATIVE) } returns staffRoles

        // Expected response
        val expectedResponse = StaffFormResponse(
            staffRoles = staffRoles
        )

        authenticatedAs(idToken, staff) {
            get("/staff/build_staff?filter={\"type\":\"HEALTH_ADMINISTRATIVE\"}") { response ->
                val responseBody = response.bodyAsBFFJson<StaffFormResponse>()
                assertThat(responseBody.specialties).isNull()
                assertThat(responseBody.providerUnits).isNull()
                assertThat(responseBody.staffRoles).isNotNull
                assertThat(responseBody.staffTiers).isNull()
                assertThat(responseBody.staffScore).isNull()
            }
        }

        // Verify service calls
        coVerifyOnce { staffBackofficeService.getStaffRoles(StaffType.HEALTH_ADMINISTRATIVE) }
    }

    @Test
    fun `#searchProviderUnits returns provider units by search token`() {
        // Mock data
        val providerUnits = listOf(ProviderUnit(
            name = "Provider Unit 1",
            id = UUID.randomUUID(),
            type = ProviderUnit.Type.HOSPITAL,
            site = "",
            cnpj = "",
            phones = emptyList(),
            workingPeriods = emptyList(),
            qualifications = emptyList(),
            imageUrl = null,
            providerId = UUID.randomUUID()
        ))

        // Mock service calls
        coEvery { providerUnitService.getByFilterWithRange(any(), any()) } returns providerUnits.success()

        authenticatedAs(idToken, staff) {
            get("/staff/search_provider_units?filter={\"q\":\"Hospital\"}") { response ->
                assertThat(response).isOKWithData(providerUnits)
            }
        }

        // Verify service calls
        coVerifyOnce { providerUnitService.getByFilterWithRange(any(), any()) }
    }

    @Test
    fun `#searchProviderUnits returns empty list when search token is empty`() {
        // Mock data
        val providerUnits = emptyList<ProviderUnit>()

        // Mock service calls
        coEvery { providerUnitService.getByFilterWithRange(any(), any()) } returns providerUnits.success()

        authenticatedAs(idToken, staff) {
            get("/staff/search_provider_units?filter={\"q\":\"\"}") { response ->
                assertThat(response).isOKWithData(emptyList<ProviderUnit>())
            }
        }

        // Verify service calls
        coVerifyOnce { providerUnitService.getByFilterWithRange(match { it.searchToken == "" }, any()) }
    }

    @Test
    fun `#buildFormByStaffType returns form for HEALTH_PROFESSIONAL`() {
        // Mock data
        val specialties = listOf(MedicalSpecialty(
            name = "Specialty 1",
            type = MedicalSpecialtyType.SPECIALTY,
            urlSlug = "specialty-1"
        ))
        val providerUnits = listOf(ProviderUnit(
            name = "Provider Unit 1",
            id = UUID.randomUUID(),
            type = ProviderUnit.Type.HOSPITAL,
            site = "",
            cnpj = "",
            phones = emptyList(),
            workingPeriods = emptyList(),
            qualifications = emptyList(),
            imageUrl = null,
            providerId = UUID.randomUUID()
        ))
        val staffRoles = listOf(
            StaffRolesResponse(
                id = "role1",
                name = "Role 1",
                value = "ROLE1"
            )
        )
        val staffTiers = listOf(
            StaffTiersResponse(
                id = "tier1",
                name = "Tier 1",
                value = "TIER1"
            )
        )
        val staffScore = listOf(
            StaffScoreResponse(
                id = "score1",
                name = "Score 1",
                value = "SCORE1"
            )
        )

        // Mock service calls
        coEvery { medicalSpecialtyService.getActivesByType(any(), any()) } returns specialties.success()
        coEvery { providerUnitService.getByFilterWithRange(any(), any()) } returns providerUnits.success()
        coEvery { staffBackofficeService.getStaffRoles(StaffType.HEALTH_PROFESSIONAL) } returns staffRoles
        coEvery { staffBackofficeService.getStaffTiers() } returns staffTiers
        coEvery { staffBackofficeService.getStaffScore() } returns staffScore

        // Expected response
        val expectedResponse = StaffFormResponse(
            specialties = specialties,
            providerUnits = providerUnits,
            staffRoles = staffRoles,
            staffTiers = staffTiers,
            staffScore = staffScore
        )

        authenticatedAs(idToken, staff) {
            get("/staff/build_staff?filter={\"type\":\"HEALTH_PROFESSIONAL\"}") { response ->
                val responseBody = response.bodyAsBFFJson<StaffFormResponse>()
                assertThat(responseBody.specialties).isNotNull
                assertThat(responseBody.providerUnits).isNotNull
                assertThat(responseBody.staffRoles).isNotNull
                assertThat(responseBody.staffTiers).isNotNull
                assertThat(responseBody.staffScore).isNotNull
            }
        }

        // Verify service calls
        coVerifyOnce { medicalSpecialtyService.getActivesByType(any(), any()) }
        coVerifyOnce { providerUnitService.getByFilterWithRange(any(), any()) }
        coVerifyOnce { staffBackofficeService.getStaffRoles(StaffType.HEALTH_PROFESSIONAL) }
        coVerifyOnce { staffBackofficeService.getStaffTiers() }
        coVerifyOnce { staffBackofficeService.getStaffScore() }
    }

    @Test
    fun `#buildFormByStaffType returns form for PARTNER_HEALTH_PROFESSIONAL`() {
        // Mock data
        val specialties = listOf(MedicalSpecialty(
            name = "Specialty 1",
            type = MedicalSpecialtyType.SPECIALTY,
            urlSlug = "specialty-1"
        ))
        val providerUnits = listOf(ProviderUnit(
            name = "Provider Unit 1",
            id = UUID.randomUUID(),
            type = ProviderUnit.Type.HOSPITAL,
            site = "",
            cnpj = "",
            phones = emptyList(),
            workingPeriods = emptyList(),
            qualifications = emptyList(),
            imageUrl = null,
            providerId = UUID.randomUUID()
        ))
        val staffRoles = listOf(
            StaffRolesResponse(
                id = "role1",
                name = "Role 1",
                value = "ROLE1"
            )
        )
        val staffTiers = listOf(
            StaffTiersResponse(
                id = "tier1",
                name = "Tier 1",
                value = "TIER1"
            )
        )
        val staffScore = listOf(
            StaffScoreResponse(
                id = "score1",
                name = "Score 1",
                value = "SCORE1"
            )
        )

        // Mock service calls
        coEvery { medicalSpecialtyService.getActivesByType(any(), any()) } returns specialties.success()
        coEvery { providerUnitService.getByFilterWithRange(any(), any()) } returns providerUnits.success()
        coEvery { staffBackofficeService.getStaffRoles(StaffType.PARTNER_HEALTH_PROFESSIONAL) } returns staffRoles
        coEvery { staffBackofficeService.getStaffTiers() } returns staffTiers
        coEvery { staffBackofficeService.getStaffScore() } returns staffScore

        // Expected response
        val expectedResponse = StaffFormResponse(
            specialties = specialties,
            providerUnits = providerUnits,
            staffRoles = staffRoles,
            staffTiers = staffTiers,
            staffScore = staffScore
        )

        authenticatedAs(idToken, staff) {
            get("/staff/build_staff?filter={\"type\":\"PARTNER_HEALTH_PROFESSIONAL\"}") { response ->
                val responseBody = response.bodyAsBFFJson<StaffFormResponse>()
                assertThat(responseBody.specialties).isNotNull
                assertThat(responseBody.providerUnits).isNotNull
                assertThat(responseBody.staffRoles).isNotNull
                assertThat(responseBody.staffTiers).isNotNull
                assertThat(responseBody.staffScore).isNotNull
            }
        }

        // Verify service calls
        coVerifyOnce { medicalSpecialtyService.getActivesByType(any(), any()) }
        coVerifyOnce { providerUnitService.getByFilterWithRange(any(), any()) }
        coVerifyOnce { staffBackofficeService.getStaffRoles(StaffType.PARTNER_HEALTH_PROFESSIONAL) }
        coVerifyOnce { staffBackofficeService.getStaffTiers() }
        coVerifyOnce { staffBackofficeService.getStaffScore() }
    }

    @Test
    fun `#getSubSpecialty should return subspecialties by name`() {
        // Mock data
        val subspecialties = listOf(
            TestModelFactory.buildMedicalSpecialty(type = MedicalSpecialtyType.SUBSPECIALTY, name = "Joelho")
        )

        // Mock service calls
        coEvery { medicalSpecialtyService.getByName("Joelho", MedicalSpecialtyType.SUBSPECIALTY) } returns subspecialties.success()

        authenticatedAs(idToken, staff) {
            get("/staff/sub_specialties?q=Joelho") { response ->
                assertThat(response.status()).isEqualTo(HttpStatusCode.OK)
            }
        }

        // No verification needed
    }

    @Test
    fun `#getSubSpecialty should return subspecialties by parent specialty id`() {
        // Mock data
        val parentId = UUID.randomUUID()
        val subspecialties = listOf(
            TestModelFactory.buildMedicalSpecialty(type = MedicalSpecialtyType.SUBSPECIALTY, parentSpecialtyId = parentId)
        )

        // Mock service calls
        coEvery { medicalSpecialtyService.getByParentId(parentId) } returns subspecialties.success()

        authenticatedAs(idToken, staff) {
            get("/staff/sub_specialties?parent_specialty_id=$parentId") { response ->
                assertThat(response.status()).isEqualTo(HttpStatusCode.OK)
            }
        }

        // No verification needed
    }

    @Test
    fun `#getSubSpecialty should return subspecialties by range when no filter is provided`() {
        // Mock data
        val range = IntRange(0, 9)
        val subspecialties = listOf(
            TestModelFactory.buildMedicalSpecialty(type = MedicalSpecialtyType.SUBSPECIALTY),
            TestModelFactory.buildMedicalSpecialty(type = MedicalSpecialtyType.SUBSPECIALTY)
        )
        val totalCount = 10

        // Mock service calls
        coEvery { medicalSpecialtyService.getByRange(range, listOf(MedicalSpecialtyType.SUBSPECIALTY)) } returns subspecialties.success()
        coEvery { medicalSpecialtyService.count() } returns totalCount.success()

        authenticatedAs(idToken, staff) {
            get("/staff/sub_specialties?page=1&pageSize=10") { response ->
                assertThat(response).isOKWithData(subspecialties)
                assertThat(response).containsHeaderWithValue("Content-Range", totalCount.toString())
            }
        }

        // Verify service calls
        coVerifyOnce { medicalSpecialtyService.getByRange(range, listOf(MedicalSpecialtyType.SUBSPECIALTY)) }
        coVerifyOnce { medicalSpecialtyService.count() }
    }

    @Test
    fun `#autocompleteAddress should return address suggestions`() {
        // Mock data
        val query = "Av. Rebouças"
        val placeId = "place_id_123"
        val autocompleteResults = listOf(
            AutocompleteTransport(
                placeId = placeId,
                description = "Av. Rebouças, São Paulo - SP, Brasil",
                mainText = "Av. Rebouças",
                secondaryText = "São Paulo - SP, Brasil"
            )
        )

        // Mock service calls
        coEvery { googleMapsService.autocompleteByText(query, any()) } returns autocompleteResults.success()

        authenticatedAs(idToken, staff) {
            get("/staff/address/search?q=$query") { response ->
                assertThat(response).isOKWithData(autocompleteResults)
            }
        }

        // Verify service calls
        coVerifyOnce { googleMapsService.autocompleteByText(query, any()) }
    }

    @Test
    fun `#autocompleteAddress should use session id from header if available`() {
        // Mock data
        val query = "Av. Rebouças"
        val sessionId = "session-id".toSafeUUID()
        val placeId = "place_id_123"
        val autocompleteResults = listOf(
            AutocompleteTransport(
                placeId = placeId,
                description = "Av. Rebouças, São Paulo - SP, Brasil",
                mainText = "Av. Rebouças",
                secondaryText = "São Paulo - SP, Brasil"
            )
        )

        // Mock service calls
        coEvery { googleMapsService.autocompleteByText(query, sessionId) } returns autocompleteResults.success()

        authenticatedAs(idToken, staff) {
            get("/staff/address/search?q=$query", mapOf("Session-Id" to sessionId.toString())) { response ->
                assertThat(response).isOKWithData(autocompleteResults)
            }
        }

        // Verify service calls
        coVerifyOnce { googleMapsService.autocompleteByText(query, sessionId) }
    }

    @Test
    fun `#upload_profile_image should return 200 OK with image URL`() {
        val uploadedFileUrl = "http://image.url"

        coEvery {
            fileStorage.store(match {
                it.bucketName == "publicAssetsBucketTest"
                        && it.filePath == "healthcare-team-assets/apple.png"
                        && it.fileType == FileType.IMAGE_PNG
            })
        } returns uploadedFileUrl

        authenticatedAs(idToken, staff) {
            multipart(HttpMethod.Post, "/staff/upload_profile_image", fileName = "apple.png", parameters = emptyMap()) { response ->
                assertThat(response).isOK()

                val resultUrl: String = response.bodyAsText()
                assertThat(resultUrl).isEqualTo(uploadedFileUrl)
                coVerifyOnce { fileStorage.store(any()) }
            }
        }
    }

    @Test
    fun `#create should create a new staff member`() {
        val staffId = RangeUUID.generate()
        val createdStaff = TestModelFactory.buildStaff().copy(id = staffId)
        val createRequest = CreateStaffRequest(
            firstName = "John",
            lastName = "Doe",
            email = "<EMAIL>",
            gender = Gender.MALE,
            role = Role.COMMUNITY,
            type = StaffType.HEALTH_PROFESSIONAL
        )

        coEvery { staffService.add(any(), any(), any()) } returns createdStaff.success()

        authenticatedAs(idToken, staff) {
            post("/staff", createRequest) { response ->
                assertThat(response).isOK()
                val responseBody = response.bodyAsBFFJson<Map<String, Any>>()
                assertThat(responseBody["id"]).isNotNull()
            }
        }

        coVerifyOnce { staffService.add(any(), any(), any()) }
    }

    @Test
    fun `#create should handle minimal request with only required fields`() {
        val staffId = RangeUUID.generate()
        val createdStaff = TestModelFactory.buildStaff().copy(id = staffId)
        val minimalRequest = CreateStaffRequest(
            firstName = "Jane",
            lastName = "Smith",
            email = "<EMAIL>",
            gender = Gender.FEMALE,
            role = Role.MANAGER_PHYSICIAN,
            type = StaffType.COMMUNITY_SPECIALIST
        )

        coEvery { staffService.add(any(), any(), any()) } returns createdStaff.success()

        authenticatedAs(idToken, staff) {
            post("/staff", minimalRequest) { response ->
                assertThat(response).isOK()
                val responseBody = response.bodyAsBFFJson<Map<String, Any>>()
                assertThat(responseBody["id"]).isNotNull()
            }
        }

        coVerifyOnce { staffService.add(any(), any(), any()) }
    }

    @Test
    fun `#create should handle PITAYA staff type correctly`() {
        val staffId = RangeUUID.generate()
        val createdStaff = TestModelFactory.buildStaff().copy(id = staffId)
        val pitayaRequest = CreateStaffRequest(
            firstName = "Carlos",
            lastName = "Santos",
            email = "<EMAIL>",
            gender = Gender.MALE,
            role = Role.B2B_OPS,
            type = StaffType.PITAYA,
            active = true,
            nationalId = "11144477735"
        )

        coEvery { staffService.add(any(), any(), any()) } returns createdStaff.success()

        authenticatedAs(idToken, staff) {
            post("/staff", pitayaRequest) { response ->
                assertThat(response).isOK()
                val responseBody = response.bodyAsBFFJson<Map<String, Any>>()
                assertThat(responseBody["id"]).isNotNull()
            }
        }

        coVerifyOnce {
            staffService.add(
                match { staff ->
                    staff.type == StaffType.PITAYA &&
                    staff.role == Role.B2B_OPS &&
                    staff.firstName == "Carlos" &&
                    staff.lastName == "Santos"
                },
                isNull(),
                any()
            )
        }
    }

    @Test
    fun `#getById should return staff details when staff exists`() {
        val staffId = RangeUUID.generate()
        val existingStaff = TestModelFactory.buildStaff().copy(
            id = staffId,
            firstName = "Maria",
            lastName = "Silva",
            email = "<EMAIL>",
            type = StaffType.HEALTH_PROFESSIONAL,
            role = Role.MANAGER_PHYSICIAN
        )

        coEvery { staffService.get(staffId) } returns existingStaff.success()

        authenticatedAs(idToken, staff) {
            get("/staff/$staffId") { response ->
                assertThat(response).isOK()
                val responseBody = response.bodyAsBFFJson<Map<String, Any>>()
                assertThat(responseBody["id"]).isEqualTo(staffId.toString())
                assertThat(responseBody["firstName"]).isEqualTo("Maria")
                assertThat(responseBody["lastName"]).isEqualTo("Silva")
                assertThat(responseBody["email"]).isEqualTo("<EMAIL>")
                assertThat(responseBody["type"]).isEqualTo("HEALTH_PROFESSIONAL")
                assertThat(responseBody["role"]).isEqualTo("MANAGER_PHYSICIAN")
            }
        }

        coVerifyOnce { staffService.get(staffId) }
    }

    @Test
    fun `#getById should return 404 when staff does not exist`() {
        val nonExistentStaffId = RangeUUID.generate()

        coEvery { staffService.get(nonExistentStaffId) } returns Result.failure(NotFoundException("Staff not found"))

        authenticatedAs(idToken, staff) {
            get("/staff/$nonExistentStaffId") { response ->
                assertThat(response).isNotFound()
            }
        }

        coVerifyOnce { staffService.get(nonExistentStaffId) }
    }

    @Test
    fun `#getById should return staff with PITAYA type correctly`() {
        val staffId = RangeUUID.generate()
        val pitayaStaff = TestModelFactory.buildStaff().copy(
            id = staffId,
            firstName = "João",
            lastName = "Oliveira",
            email = "<EMAIL>",
            type = StaffType.PITAYA,
            role = Role.FIN_OPS,
            active = true
        )

        coEvery { staffService.get(staffId) } returns pitayaStaff.success()

        authenticatedAs(idToken, staff) {
            get("/staff/$staffId") { response ->
                assertThat(response).isOK()
                val responseBody = response.bodyAsBFFJson<Map<String, Any>>()
                assertThat(responseBody["id"]).isEqualTo(staffId.toString())
                assertThat(responseBody["firstName"]).isEqualTo("João")
                assertThat(responseBody["lastName"]).isEqualTo("Oliveira")
                assertThat(responseBody["type"]).isEqualTo("PITAYA")
                assertThat(responseBody["role"]).isEqualTo("FIN_OPS")
                assertThat(responseBody["active"]).isEqualTo(true)
            }
        }

        coVerifyOnce { staffService.get(staffId) }
    }

    @Test
    fun `#upload_profile_image should throw exception when file is not an image`() {
        authenticatedAs(idToken, staff) {
            multipart(
                HttpMethod.Post,
                "/staff/upload_profile_image",
                fileName = "invalid_image.txt",
                parameters = emptyMap()
            ) { response ->
                assertThat(response).isBadRequest()
            }
        }
    }

    @Test
    fun `#create should return validation errors when firstName is missing`() {
        val invalidRequest = CreateStaffRequest(
            firstName = "",
            lastName = "Doe",
            email = "<EMAIL>",
            gender = Gender.MALE,
            role = Role.COMMUNITY,
            type = StaffType.HEALTH_PROFESSIONAL
        )

        authenticatedAs(idToken, staff) {
            post("/staff", invalidRequest) { response ->
                assertThat(response).isBadRequest()
                val responseBody = response.bodyAsBFFJson<Map<String, Any>>()
                @Suppress("UNCHECKED_CAST")
                val errors = responseBody["data"] as List<Map<String, String>>
                val hasFirstNameError = errors.any { it["field"] == "firstName" && it["message"] == "Nome é obrigatório" }
                assertThat(hasFirstNameError).isTrue()
            }
        }

        coVerify(exactly = 0) { staffService.add(any(), any(), any()) }
    }

    @Test
    fun `#create should return validation errors when email is invalid`() {
        val invalidRequest = CreateStaffRequest(
            firstName = "John",
            lastName = "Doe",
            email = "invalid-email",
            gender = Gender.MALE,
            role = Role.COMMUNITY,
            type = StaffType.HEALTH_PROFESSIONAL
        )

        authenticatedAs(idToken, staff) {
            post("/staff", invalidRequest) { response ->
                assertThat(response).isBadRequest()
                val responseBody = response.bodyAsBFFJson<Map<String, Any>>()
                @Suppress("UNCHECKED_CAST")
                val errors = responseBody["data"] as List<Map<String, String>>
                val hasEmailError = errors.any { it["field"] == "email" && it["message"] == "E-mail inválido" }
                assertThat(hasEmailError).isTrue()
            }
        }

        coVerify(exactly = 0) { staffService.add(any(), any(), any()) }
    }

    @Test
    fun `#create should return validation errors when nationalId is invalid`() {
        val invalidRequest = CreateStaffRequest(
            firstName = "John",
            lastName = "Doe",
            email = "<EMAIL>",
            nationalId = "12345",
            gender = Gender.MALE,
            role = Role.COMMUNITY,
            type = StaffType.HEALTH_PROFESSIONAL
        )

        authenticatedAs(idToken, staff) {
            post("/staff", invalidRequest) { response ->
                assertThat(response).isBadRequest()
                val responseBody = response.bodyAsBFFJson<Map<String, Any>>()
                @Suppress("UNCHECKED_CAST")
                val errors = responseBody["data"] as List<Map<String, String>>
                val hasNationalIdError = errors.any { it["field"] == "nationalId" && it["message"] == "CPF inválido" }
                assertThat(hasNationalIdError).isTrue()
            }
        }

        coVerify(exactly = 0) { staffService.add(any(), any(), any()) }
    }

    @Test
    fun `#create should return multiple validation errors when multiple fields are invalid`() {
        val invalidRequest = CreateStaffRequest(
            firstName = "",
            lastName = "",
            email = "invalid-email",
            gender = Gender.MALE,
            role = Role.COMMUNITY,
            type = StaffType.HEALTH_PROFESSIONAL
        )

        authenticatedAs(idToken, staff) {
            post("/staff", invalidRequest) { response ->
                assertThat(response).isBadRequest()
                val responseBody = response.bodyAsBFFJson<Map<String, Any>>()
                @Suppress("UNCHECKED_CAST")
                val errors = responseBody["data"] as List<Map<String, String>>
                assertThat(errors.size).isGreaterThan(2)
                val hasFirstNameError = errors.any { it["field"] == "firstName" }
                val hasLastNameError = errors.any { it["field"] == "lastName" }
                val hasEmailError = errors.any { it["field"] == "email" }
                assertThat(hasFirstNameError).isTrue()
                assertThat(hasLastNameError).isTrue()
                assertThat(hasEmailError).isTrue()
            }
        }

        coVerify(exactly = 0) { staffService.add(any(), any(), any()) }
    }
}
