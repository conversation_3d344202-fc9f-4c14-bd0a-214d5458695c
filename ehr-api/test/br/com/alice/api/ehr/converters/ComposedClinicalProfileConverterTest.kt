package br.com.alice.api.ehr.converters

import br.com.alice.api.ehr.controllers.model.ComposedClinicalProfile
import br.com.alice.api.ehr.controllers.model.CptBackgroundResponse
import br.com.alice.api.ehr.converters.ClinicalBackgroundConverter.backgroundToResponse
import br.com.alice.common.RangeUUID
import br.com.alice.common.core.extensions.toBrazilianDateFormat
import br.com.alice.common.helpers.mockLocalDate
import br.com.alice.common.helpers.mockRangeUUID
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.helpers.TestModelFactory.buildHealthMeasurement
import br.com.alice.data.layer.models.ClinicalBackgroundStatus.ACTIVE
import br.com.alice.data.layer.models.ClinicalBackgroundStatus.INACTIVE
import br.com.alice.data.layer.models.ClinicalBackgroundType.ALLERGY
import br.com.alice.data.layer.models.ClinicalBackgroundType.DISEASE
import br.com.alice.data.layer.models.ClinicalBackgroundType.FAMILY
import br.com.alice.data.layer.models.ClinicalBackgroundType.HABIT
import br.com.alice.data.layer.models.ClinicalBackgroundType.MEDICINE
import br.com.alice.data.layer.models.ClinicalBackgroundType.SURGICAL
import br.com.alice.data.layer.models.ClinicalBackgroundType.VACCINE
import br.com.alice.data.layer.models.DiseaseBackground
import br.com.alice.data.layer.models.HealthMeasurementInternalType.ABDOMINAL_CIRCUMFERENCE
import br.com.alice.data.layer.models.HealthMeasurementInternalType.BODY_FAT_PERCENTAGE
import br.com.alice.data.layer.models.HealthMeasurementInternalType.DIASTOLIC_PRESSURE
import br.com.alice.data.layer.models.HealthMeasurementInternalType.HEART_RATE
import br.com.alice.data.layer.models.HealthMeasurementInternalType.HEIGHT
import br.com.alice.data.layer.models.HealthMeasurementInternalType.NECK_CIRCUMFERENCE
import br.com.alice.data.layer.models.HealthMeasurementInternalType.SKELETAL_MUSCLE_MASS
import br.com.alice.data.layer.models.HealthMeasurementInternalType.SYSTOLIC_PRESSURE
import br.com.alice.data.layer.models.HealthMeasurementInternalType.WEIGHT
import br.com.alice.data.layer.models.copy
import br.com.alice.data.layer.services.testPersonId
import br.com.alice.ehr.model.CptCondition
import br.com.alice.ehr.model.CptGracePeriod
import br.com.alice.ehr.model.GracePeriodType
import org.assertj.core.api.Assertions.assertThat
import java.math.BigDecimal
import java.time.LocalDate
import kotlin.test.Test

class ComposedClinicalProfileConverterTest {

    private val now = LocalDate.now()
    private val uuid = RangeUUID.generate()

    private val cpt1 = CptCondition(
        name = "Condição com cid",
        cid = "123",
        validUntil = "03/03/1994",
        healthCondition = TestModelFactory.buildHealthCondition(specialities = null),
        baseDate = now,
        periodInDays = 0
    )
    private val cpt2 = CptCondition(
        name = "Condição sem cid",
        cid = "",
        validUntil = now.plusDays(1).toBrazilianDateFormat(),
        baseDate = now,
        periodInDays = 0
    )
    private val cpts = listOf(cpt1, cpt2)

    private val grace1 = CptGracePeriod(
        condition = "condition",
        validUntil = "13/02/2021",
        baseDate = now,
        type = GracePeriodType.ELECTIVE_SURGERY,
        periodInDays = 0
    )
    private val grace2 = CptGracePeriod(
        condition = "condition",
        validUntil = now.plusDays(1).toBrazilianDateFormat(),
        baseDate = LocalDate.now(),
        type = GracePeriodType.ELECTIVE_SURGERY,
        periodInDays = 0
    )
    private val gracePeriods = listOf(grace1, grace2)

    private val heightMeasurement = buildHealthMeasurement(value = BigDecimal("1.750"), type = HEIGHT)
    private val weightMeasurement = buildHealthMeasurement(value = BigDecimal("56.645"), type = WEIGHT)
    private val bodyFatPercentageMeasurement = buildHealthMeasurement(value = BigDecimal("0.809"), type = BODY_FAT_PERCENTAGE)
    private val skeletalMuscleMassMeasurement = buildHealthMeasurement(value = BigDecimal("98.298"), type = SKELETAL_MUSCLE_MASS)
    private val systolicPressureMeasurement = buildHealthMeasurement(value = BigDecimal("20.000"), type = SYSTOLIC_PRESSURE)
    private val diastolicPressureMeasurement = buildHealthMeasurement(value = BigDecimal("30.000"), type = DIASTOLIC_PRESSURE)
    private val heartRateMeasurement = buildHealthMeasurement(value = BigDecimal("66.000"), type = HEART_RATE)
    private val neckCircumferenceMeasurement = buildHealthMeasurement(value = BigDecimal("51.000"), type = NECK_CIRCUMFERENCE)
    private val abdominalCircumferenceMeasurement = buildHealthMeasurement(value = BigDecimal("80.000"), type = ABDOMINAL_CIRCUMFERENCE)

    private val healthMeasurements = listOf(
        heightMeasurement,
        weightMeasurement,
        bodyFatPercentageMeasurement,
        skeletalMuscleMassMeasurement,
        systolicPressureMeasurement,
        diastolicPressureMeasurement,
        heartRateMeasurement,
        neckCircumferenceMeasurement,
        abdominalCircumferenceMeasurement
    )

    private val conditionCode = "E23"

    private val diseaseBackground = TestModelFactory.buildDiseaseClinicalBackground(value = conditionCode)
    private val surgicalBackground = TestModelFactory.buildUnstructuredClinicalBackground(type = SURGICAL)
    private val familyBackground = TestModelFactory.buildUnstructuredClinicalBackground(type = FAMILY)
    private val habitBackground = TestModelFactory.buildUnstructuredClinicalBackground(type = HABIT)
    private val allergyBackground = TestModelFactory.buildUnstructuredClinicalBackground(type = ALLERGY)
    private val vaccineBackground = TestModelFactory.buildUnstructuredClinicalBackground(type = VACCINE)
    private val medicineBackground = TestModelFactory.buildUnstructuredClinicalBackground(type = MEDICINE)
    private val dietBackground = TestModelFactory.buildDietClinicalBackground()

    private val clinicalBackgrounds = listOf(
        diseaseBackground,
        surgicalBackground,
        familyBackground,
        habitBackground,
        allergyBackground,
        vaccineBackground,
        medicineBackground,
        dietBackground
    )

    private val condition = TestModelFactory.buildHealthCondition(code = conditionCode)
    private val healthConditions = listOf(condition)

    @Test
    fun `#buildComposedClinicalProfile returns ComposedClinicalProfile with all parameters`() = mockLocalDate(now) {
        mockRangeUUID(uuid) {

            val expectedDiseaseBackground = backgroundToResponse(diseaseBackground, condition)
            val expectedSurgicalBackground = backgroundToResponse(surgicalBackground)
            val expectedFamilyBackground = backgroundToResponse(familyBackground)
            val expectedHabitBackground = backgroundToResponse(habitBackground)
            val expectedAllergyBackground = backgroundToResponse(allergyBackground)
            val expectedVaccineBackground = backgroundToResponse(vaccineBackground)
            val expectedMedicineBackground = backgroundToResponse(medicineBackground)
            val expectedDietBackground = backgroundToResponse(dietBackground)

            val expectedCpt1Response = CptBackgroundResponse(
                id = uuid,
                validUntil = cpt1.validUntil,
                diseaseType = DiseaseBackground.Type.CID_10,
                value = cpt1.cid,
                description = "Condição com cid - CID 123",
                personId = testPersonId.toString(),
                type = DISEASE,
                status = INACTIVE,
                isEnriched = false
            )

            val expectedCpt2Response = CptBackgroundResponse(
                id = uuid,
                validUntil = now.plusDays(1).toBrazilianDateFormat(),
                diseaseType = DiseaseBackground.Type.CID_10,
                value = cpt2.cid,
                description = "Condição sem cid",
                personId = testPersonId.toString(),
                type = DISEASE,
                status = ACTIVE,
                isEnriched = false
            )

            val expectedGrace1 = CptBackgroundResponse(
                diseaseType = DiseaseBackground.Type.CID_10,
                value = grace1.condition,
                description = grace1.condition,
                id = uuid,
                personId = testPersonId.toString(),
                type = DISEASE,
                status = INACTIVE,
                denied = false,
                validUntil = grace1.validUntil,
                isEnriched = true
            )

            val expectedGrace2 = CptBackgroundResponse(
                diseaseType = DiseaseBackground.Type.CID_10,
                value = grace2.condition,
                description = grace2.condition,
                id = uuid,
                personId = testPersonId.toString(),
                type = DISEASE,
                status = ACTIVE,
                denied = false,
                validUntil = grace2.validUntil,
                isEnriched = true
            )

            val expected = ComposedClinicalProfile(
                background = listOf(expectedDiseaseBackground),
                surgicalBackground = listOf(expectedSurgicalBackground),
                familyBackground = listOf(expectedFamilyBackground),
                habits = listOf(expectedHabitBackground),
                allergies = listOf(expectedAllergyBackground),
                vaccines = listOf(expectedVaccineBackground),
                medicines = listOf(expectedMedicineBackground),
                diet = listOf(expectedDietBackground),
                height = heightMeasurement.normalizeValue(),
                weight = weightMeasurement.normalizeValue(),
                bodyFatPercentage = bodyFatPercentageMeasurement.normalizeValue(),
                skeletalMuscleMass = skeletalMuscleMassMeasurement.normalizeValue(),
                systolicPressure = systolicPressureMeasurement.normalizeValue(),
                diastolicPressure = diastolicPressureMeasurement.normalizeValue(),
                heartRate = heartRateMeasurement.normalizeValue(),
                neckCircumference = neckCircumferenceMeasurement.normalizeValue(),
                abdominalCircumference = abdominalCircumferenceMeasurement.normalizeValue(),
                cpts = listOf(expectedCpt1Response, expectedCpt2Response),
                gracePeriods = listOf(expectedGrace1, expectedGrace2)
            )

            val result = ComposedClinicalProfileConverter.buildComposedClinicalProfile(
                cpts = cpts,
                gracePeriods = gracePeriods,
                healthMeasurements = healthMeasurements,
                clinicalBackgrounds = clinicalBackgrounds,
                personId = testPersonId,
                healthConditions = healthConditions
            )

            assertThat(result).isEqualTo(expected)
        }

    }

    @Test
    fun `#buildComposedClinicalProfile returns ComposedClinicalProfile with denied and history`() = mockLocalDate(now) {
        mockRangeUUID(uuid) {

            val diseaseBackground = diseaseBackground.copy(denied = true)
            val allergyBackground = allergyBackground.copy(denied = true)
            val expectedAllergyBackground = backgroundToResponse(allergyBackground)
            val expectedDiseaseBackground = backgroundToResponse(diseaseBackground)

            val expected = ComposedClinicalProfile(
                background = listOf(expectedDiseaseBackground),
                surgicalBackground = emptyList(),
                familyBackground = emptyList(),
                habits = emptyList(),
                allergies = listOf(expectedAllergyBackground),
                vaccines = emptyList(),
                medicines = emptyList(),
                diet = emptyList(),
                height = null,
                weight = null,
                bodyFatPercentage = null,
                skeletalMuscleMass = null,
                systolicPressure = null,
                diastolicPressure = null,
                heartRate = null,
                neckCircumference = null,
                abdominalCircumference = null,
                cpts = emptyList(),
                gracePeriods = emptyList()
            )

            val result = ComposedClinicalProfileConverter.buildComposedClinicalProfile(
                healthMeasurements = emptyList(),
                clinicalBackgrounds = listOf(diseaseBackground, allergyBackground),
                isHistory = true,
                personId = testPersonId,
                healthConditions = emptyList()
            )

            assertThat(result).isEqualTo(expected)
        }
    }

    @Test
    fun `#buildComposedClinicalProfile returns ComposedClinicalProfile with empty parameters`() {
        val expected = ComposedClinicalProfile(
            background = emptyList(),
            surgicalBackground = emptyList(),
            familyBackground = emptyList(),
            habits = emptyList(),
            allergies = emptyList(),
            vaccines = emptyList(),
            medicines = emptyList(),
            diet = emptyList(),
            height = null,
            weight = null,
            bodyFatPercentage = null,
            skeletalMuscleMass = null,
            systolicPressure = null,
            diastolicPressure = null,
            heartRate = null,
            neckCircumference = null,
            abdominalCircumference = null,
            cpts = emptyList(),
            gracePeriods = emptyList()
        )

        val result = ComposedClinicalProfileConverter.buildComposedClinicalProfile(
            cpts = emptyList(),
            gracePeriods = emptyList(),
            clinicalBackgrounds = emptyList(),
            personId = testPersonId,
            healthMeasurements = emptyList(),
            healthConditions = emptyList()
        )

        assertThat(result).isEqualTo(expected)
    }
}
