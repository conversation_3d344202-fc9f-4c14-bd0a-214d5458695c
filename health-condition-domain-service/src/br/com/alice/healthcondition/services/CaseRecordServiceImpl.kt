package br.com.alice.healthcondition.services

import br.com.alice.common.RangeUUID
import br.com.alice.common.core.PersonId
import br.com.alice.common.core.Role
import br.com.alice.common.core.Role.CARE_COORD_NURSE
import br.com.alice.common.core.Role.Companion.physicianRoles
import br.com.alice.common.core.exceptions.NotFoundException
import br.com.alice.common.extensions.coFoldNotFound
import br.com.alice.common.extensions.flatMapPair
import br.com.alice.common.extensions.mapEachNotNull
import br.com.alice.common.extensions.then
import br.com.alice.common.kafka.interfaces.KafkaProducerService
import br.com.alice.common.observability.recordResult
import br.com.alice.common.service.data.dsl.and
import br.com.alice.data.layer.models.CaseRecord
import br.com.alice.data.layer.models.CaseRecordCreatedByType
import br.com.alice.data.layer.models.CaseRecordReference
import br.com.alice.data.layer.models.CaseSeverity.DECOMPENSATED
import br.com.alice.data.layer.models.CaseStatus
import br.com.alice.data.layer.models.CaseStatus.ACTIVE
import br.com.alice.data.layer.models.CaseStatus.CANCELLED
import br.com.alice.data.layer.models.CaseStatus.CONTEMPLATING
import br.com.alice.data.layer.models.CaseStatus.PENDING
import br.com.alice.common.Disease
import br.com.alice.data.layer.models.HealthCondition
import br.com.alice.data.layer.models.HealthConditionCodeType
import br.com.alice.data.layer.services.CaseRecordDataService
import br.com.alice.data.layer.services.PersonCaseDataService
import br.com.alice.healthcondition.client.CaseRecordService
import br.com.alice.healthcondition.client.DuplicatedDescriptionException
import br.com.alice.healthcondition.client.HealthConditionService
import br.com.alice.healthcondition.event.CaseRecordCreatedEvent
import br.com.alice.healthcondition.model.CaseRecordCreatedRequest
import br.com.alice.healthcondition.model.CaseRecordUpdatedRequest
import com.github.kittinunf.result.Result
import com.github.kittinunf.result.failure
import com.github.kittinunf.result.flatMap
import com.github.kittinunf.result.map
import com.github.kittinunf.result.success
import java.time.LocalDateTime
import java.util.UUID

class CaseRecordServiceImpl(
    private val caseRecordDataService: CaseRecordDataService,
    private val healthConditionService: HealthConditionService,
    private val kafkaProducerService: KafkaProducerService,
    private val personCaseDataService: PersonCaseDataService
) : CaseRecordService {

    private val roleThatCanActivateCases = Role.MANAGER_PHYSICIAN
    private val rolesThatCanCreateActiveCases = physicianRoles() + CARE_COORD_NURSE
    override suspend fun getCasesByPersonId(personId: PersonId): Result<List<CaseRecord>, Throwable> =
        caseRecordDataService.find {
            where { this.personId.eq(personId) }
                .orderBy { this.addedAt }
                .sortOrder { desc }
        }.map { getMoreRecent(it) }

    override suspend fun getActiveCasesByPersonIdAndDiseaseAndDiffCaseId(
        personId: PersonId,
        description: Disease,
        caseId: UUID
    ): Result<List<CaseRecord>, Throwable> =
        caseRecordDataService.find {
            where {
                this.personId.eq(personId) and
                        this.description.contains(description) and
                        this.caseId.notEq(caseId)
            }
                .orderBy { this.addedAt }
                .sortOrder { desc }
        }.map { getMoreRecent(it) }

    override suspend fun getById(id: UUID): Result<CaseRecord, Throwable> =
        caseRecordDataService.get(id)

    override suspend fun getCurrentByCaseId(personId: PersonId, caseId: UUID): Result<CaseRecord, Throwable> =
        caseRecordDataService.findOne {
            where { this.caseId.eq(caseId) and this.personId.eq(personId) }
                .orderBy { this.addedAt }
                .sortOrder { desc }
                .limit { 1 }
        }

    override suspend fun getActiveCasesByPersonId(personId: PersonId): Result<List<CaseRecord>, Throwable> =
        caseRecordDataService.find {
            where { this.personId.eq(personId) }
                .orderBy { this.addedAt }
                .sortOrder { desc }
        }.map { getActives(it) }

    override suspend fun getCaseByPersonId(personId: PersonId, caseId: UUID): Result<CaseRecord, Throwable> =
        getCasesByPersonId(personId).flatMap { caseRecords ->
            caseRecords.firstOrNull { it.caseId == caseId }?.success()
                ?: NotFoundException("Case $caseId not found").failure()
        }

    override suspend fun getDetail(caseId: UUID) =
        caseRecordDataService.find {
            where { this.caseId.eq(caseId) }
                .orderBy { addedAt }
                .sortOrder { desc }
        }

    override suspend fun addFromReference(
        caseRecord: CaseRecord,
        reference: CaseRecordReference
    ): Result<CaseRecord, Throwable> =
        getCasesByPersonId(caseRecord.personId)
            .map { caseRecords ->
                caseRecords.firstOrNull { it.description == caseRecord.description } ?: caseRecord
            }
            .flatMap { addOrJoinReferencedLink(it, reference) }
            .coFoldNotFound { addOrJoinReferencedLink(caseRecord, reference) }


    @Deprecated("Use method `create` or `edit`")
    override suspend fun add(caseRecord: CaseRecord): Result<CaseRecord, Throwable> = span("add") { span ->
        span.setAttribute("disease_code_type", caseRecord.description.type.name)
        span.setAttribute("disease_code_value", caseRecord.description.value)
        span.setAttribute("case_id", caseRecord.caseId.toString())

        getHealthConditionByDescription(
            caseRecord.description.value,
            caseRecord.description.type.name
        ).flatMap { healthCondition ->
            getCasesByPersonId(caseRecord.personId)
                .flatMap { validateDisease(it, caseRecord) }
                .flatMap {
                    caseRecordDataService.add(
                        caseRecord.sanitize()
                            .copy(
                                healthConditionId = healthCondition.id,
                                description = buildDisease(healthCondition)
                            )
                    )
                }
                .coFoldNotFound {
                    caseRecordDataService.add(
                        caseRecord.sanitize().copy(
                            healthConditionId = healthCondition.id,
                            description = buildDisease(healthCondition)
                        )
                    )
                }.recordResult("case_record", span)
                .then { kafkaProducerService.produce(CaseRecordCreatedEvent(it), it.personId.toString()) }

        }.coFoldNotFound {
            NotFoundException("Health Condition with value ${caseRecord.description.value} and type ${caseRecord.description.type} not found").failure()
        }.recordResult(span)
    }

    private fun buildDisease(healthCondition: HealthCondition) = Disease(
        type = Disease.Type.valueOf(healthCondition.codeType.name),
        value = healthCondition.code!!,
        description = healthCondition.name,
        id = healthCondition.id
    )

    override suspend fun hasCaseCreatedByReference(
        personId: PersonId,
        reference: CaseRecordReference
    ): Result<List<CaseRecord>, Throwable> =
        getCasesByPersonId(personId).mapEachNotNull { it.takeIf { it.referencedLinks.contains(reference) } }

    override suspend fun getByIds(ids: List<UUID>): Result<List<CaseRecord>, Throwable> =
        caseRecordDataService.find { where { this.id.inList(ids) } }

    override suspend fun getByCaseIds(caseIds: List<UUID>): Result<List<CaseRecord>, Throwable> =
        caseRecordDataService.find { where { this.caseId.inList(caseIds) } }

    override suspend fun isFirstSeverityAfterChange(
        caseId: UUID,
        recordId: UUID
    ): Result<Boolean, Throwable> = Result.of {
        val caseRecords = caseRecordDataService.find {
            where { this.caseId.eq(caseId) }
                .orderBy { this.addedAt }
                .sortOrder { asc }
        }.get()

        val index = caseRecords.indexOfFirst { it.id == recordId }

        if (index == 0) return@of true

        caseRecords[index].severity != caseRecords[index - 1].severity
    }

    override suspend fun getByDiseaseType(
        personId: PersonId,
        types: List<Disease.Type>
    ): Result<List<CaseRecord>, Throwable> =
        caseRecordDataService.find {
            where { this.personId.eq(personId) }
                .orderBy { this.addedAt }
                .sortOrder { desc }
        }.map { filterByTypes(it, types) }

    override suspend fun create(request: CaseRecordCreatedRequest): Result<CaseRecord, Throwable> =
        span("create") { span ->
            span.setAttribute("disease_code_type", request.description.type.name)
            span.setAttribute("disease_code_value", request.description.value)

            validateByDisease(request.personId, null, request.description).flatMap {
                getHealthConditionByDescription(request.description.value, request.description.type.name)
                    .flatMap { healthCondition ->
                        caseRecordDataService.add(
                            CaseRecord(
                                caseId = RangeUUID.generate(),
                                addedByStaffId = request.addedByStaffId,
                                responsibleStaffId = request.responsibleStaffId,
                                description = buildDisease(healthCondition),
                                observation = request.observation,
                                severity = request.severity,
                                status = validateCreationStatus(request.description.type, request.role),
                                addedAt = LocalDateTime.now(),
                                startedAt = request.startedAt ?: LocalDateTime.now(),
                                referencedLinks = request.referencedLinks,
                                caseCreatedBy = request.caseCreatedBy ?: CaseRecordCreatedByType.SYSTEM,
                                healthConditionId = healthCondition.id,
                                personId = request.personId,
                                seriousness = request.seriousness,
                                follow = request.follow,
                                cipes = request.cipes
                            ).sanitize()
                        )
                    }.then { kafkaProducerService.produce(CaseRecordCreatedEvent(it)) }
            }.recordResult(span)
        }

    override suspend fun edit(request: CaseRecordUpdatedRequest): Result<CaseRecord, Throwable> = span("edit") { span ->
        span.setAttribute("disease_code_type", request.description.type.name)
        span.setAttribute("disease_code_value", request.description.value)
        span.setAttribute("case_id", request.caseId.toString())

        validateByDisease(request.personId, request.caseId, request.description).flatMap {
            getCurrentByCaseId(request.personId, request.caseId)
                .flatMapPair {
                    getHealthConditionByDescription(request.description.value, request.description.type.name)
                }
                .flatMap { (healthCondition, caseRecord) ->
                    caseRecordDataService.add(
                        caseRecord.copy(
                            id = RangeUUID.generate(),
                            addedByStaffId = request.addedByStaffId,
                            responsibleStaffId = request.responsibleStaffId,
                            observation = request.observation,
                            severity = request.severity,
                            addedAt = LocalDateTime.now(),
                            startedAt = request.startedAt ?: caseRecord.startedAt,
                            referencedLinks = request.referencedLinks,
                            caseCreatedBy = request.caseCreatedBy ?: CaseRecordCreatedByType.SYSTEM,
                            seriousness = request.seriousness ?: caseRecord.seriousness,
                            follow = request.follow ?: caseRecord.follow,
                            cipes = request.cipes ?: caseRecord.cipes,
                            healthConditionId = healthCondition.id,
                            description = buildDisease(healthCondition)
                        ).let {
                            if (isNewCase(caseRecord.description, request.description)) {
                                it.copy(status = validateCreationStatus(request.description.type, request.role))
                            } else {
                                it.copy(status = validateUpdateStatus(caseRecord.status, request.status, request.role))
                            }
                        }.sanitize()
                    )
                }.then {
                    kafkaProducerService.produce(CaseRecordCreatedEvent(it))
                }
        }.recordResult(span)
    }


    override suspend fun findDecompensatedByHealthCondition(
        personId: PersonId,
        healthConditionIds: List<UUID>
    ): Result<List<CaseRecord>, Throwable> =
        personCaseDataService.find {
            where {
                this.personId.eq(personId) and
                        this.healthConditionId.inList(healthConditionIds) and
                        this.status.eq(ACTIVE) and
                        this.severity.eq(DECOMPENSATED)
            }
        }.flatMap { list ->
            val recordIds = list.map { it.recordId }
            caseRecordDataService.find { where { id.inList(recordIds) } }
        }

    override suspend fun findByHealthCondition(
        personId: PersonId,
        healthConditionId: UUID
    ): Result<CaseRecord, Throwable> =
        personCaseDataService.findOne {
            where {
                this.personId.eq(personId) and
                        this.healthConditionId.eq(healthConditionId) and
                        this.status.inList(listOf(ACTIVE, PENDING, CONTEMPLATING))
            }
        }.flatMap { case ->
            caseRecordDataService.get(case.recordId)
        }

    override suspend fun getDetailByRange(
        caseId: UUID,
        range: IntRange
    ): Result<List<CaseRecord>, Throwable> = caseRecordDataService.find {
        where { this.caseId.eq(caseId) }
            .orderBy { this.addedAt }
            .sortOrder { desc }
            .offset { range.first }
            .limit { range.count() }
    }

    override suspend fun getByPersonIds(personIds: List<PersonId>): Result<Map<PersonId, List<CaseRecord>>, Throwable> =
        caseRecordDataService.find {
            where { this.personId.inList(personIds) }
                .orderBy { this.addedAt }
                .sortOrder { desc }
        }.flatMap { caseRecords ->
            caseRecords.groupBy { it.personId }.mapValues { getMoreRecent(it.value) }.success()
        }

    private fun isNewCase(old: Disease, new: Disease): Boolean = old != new
    private fun filterByTypes(
        cases: List<CaseRecord>,
        types: List<Disease.Type>
    ) = cases
        .groupBy { it.caseId }
        .map { it.value.first() }
        .filter { it.status != CANCELLED && types.contains(it.description.type) }

    private fun validateDisease(caseRecords: List<CaseRecord>, caseRecord: CaseRecord) =
        if (caseRecords.notDuplicated(caseRecord) || caseRecord.status == CANCELLED) {
            caseRecord.sanitize().success()
        } else
            DuplicatedDescriptionException(caseRecord.description).failure()

    private fun List<CaseRecord>.notDuplicated(caseRecord: CaseRecord) =
        this.filter { it.caseId != caseRecord.caseId }
            .all { it.description != caseRecord.description }

    private suspend fun validateByDisease(
        personId: PersonId,
        caseId: UUID?,
        description: Disease
    ) = span("validateByDisease") { span ->
        personCaseDataService.find {
            where {
                this.personId.eq(personId) and
                        status.inList(
                            listOf(ACTIVE, PENDING, CONTEMPLATING)
                        ) and
                        codeType.eq(description.type) and
                        codeValue.eq(description.value)

            }
        }.flatMap { caseRecords ->
            if (caseRecords.isNotEmpty() && !caseRecords.any { it.id == caseId }) {
                val case = caseRecords.firstOrNull { it.id != caseId }
                case?.let { span.setAttribute("caseIdDuplicated", it.id.toString()) }
                DuplicatedDescriptionException(description).failure()
            } else true.success()
        }
    }


    private suspend fun addOrJoinReferencedLink(
        caseRecord: CaseRecord,
        reference: CaseRecordReference
    ): Result<CaseRecord, Throwable> {
        return if (caseRecord.referencedLinks.contains(reference)) caseRecord.success()
        else {
            add(
                caseRecord.copy(
                    id = RangeUUID.generate(),
                    addedAt = LocalDateTime.now(),
                    referencedLinks = caseRecord.referencedLinks.union(listOf(reference)).toList()
                )
            )
        }
    }

    private suspend fun getHealthConditionByDescription(value: String, name: String) =
        healthConditionService.findByCodeAndType(
            value,
            HealthConditionCodeType.valueOf(name)
        )

    private fun getMoreRecent(caseRecords: List<CaseRecord>) = caseRecords
        .groupBy { it.caseId }
        .map { it.value.first() }
        .filter { it.status != CANCELLED }

    private fun getActives(caseRecords: List<CaseRecord>) = caseRecords
        .groupBy { it.caseId }
        .map { it.value.first() }
        .filter { it.isActive() }

    private fun validateCreationStatus(type: Disease.Type, role: Role?) =
        if (type == Disease.Type.CIAP_2 || type == Disease.Type.GOAL) ACTIVE
        else if (role == null) PENDING
        else defineStatusBasedOnRole(true, role)


    private fun validateUpdateStatus(previous: CaseStatus, new: CaseStatus, role: Role?): CaseStatus =
        if (role == null) {
            previous
        } else if (previous == PENDING && new == ACTIVE) {
            defineStatusBasedOnRole(false, role)
        } else new


    private fun defineStatusBasedOnRole(creation: Boolean, role: Role) =
        if (creation) {
            if (rolesThatCanCreateActiveCases.contains(role)) ACTIVE else PENDING
        } else if (roleThatCanActivateCases == role) ACTIVE else PENDING

}
