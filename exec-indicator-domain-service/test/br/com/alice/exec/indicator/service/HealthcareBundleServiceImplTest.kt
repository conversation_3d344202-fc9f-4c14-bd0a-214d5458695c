package br.com.alice.exec.indicator.service

import br.com.alice.common.MvUtil
import br.com.alice.common.RangeUUID
import br.com.alice.common.core.exceptions.NotFoundException
import br.com.alice.common.data.dsl.matchers.ResultAssert.Companion.assertThat
import br.com.alice.common.data.dsl.matchers.queryEq
import br.com.alice.common.featureflag.withFeatureFlag
import br.com.alice.common.helpers.coVerifyNone
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.common.kafka.interfaces.KafkaProducerService
import br.com.alice.common.service.data.dsl.OrPredicateUsage
import br.com.alice.common.service.data.dsl.and
import br.com.alice.common.service.data.dsl.or
import br.com.alice.common.service.data.dsl.scope
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.FeatureNamespace
import br.com.alice.data.layer.models.HealthcareBundle
import br.com.alice.data.layer.models.HealthcareBundleCategory
import br.com.alice.data.layer.models.HealthcareBundleStatus
import br.com.alice.data.layer.services.HealthcareBundleModelDataService
import br.com.alice.exec.indicator.client.HealthcareBundleListFilters
import br.com.alice.exec.indicator.client.HealthcareBundleListWithCount
import br.com.alice.exec.indicator.converters.modelConverters.toModel
import br.com.alice.exec.indicator.events.HealthcareBundleUpdatedEvent
import br.com.alice.exec.indicator.events.HealthcareBundleUpsertedEvent
import br.com.alice.exec.indicator.events.UnlinkHealthcareResourceFromHealthcareBundleEvent
import br.com.alice.exec.indicator.exceptions.ProviderUnitGroupNotFoundByProviderUnit
import br.com.alice.provider.client.ProviderUnitGroupService
import br.com.alice.provider.client.ProviderUnitService
import com.github.kittinunf.result.failure
import com.github.kittinunf.result.success
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.mockk
import kotlinx.coroutines.runBlocking
import org.assertj.core.api.Assertions.assertThat
import java.time.LocalDate
import kotlin.test.Test

class HealthcareBundleServiceImplTest {

    private val healthcareBundleDataService: HealthcareBundleModelDataService = mockk()
    private val providerUnitService: ProviderUnitService = mockk()
    private val producerService: KafkaProducerService = mockk()
    private val providerUnitGroupService: ProviderUnitGroupService = mockk()

    private val healthcareBundleService = HealthcareBundleServiceImpl(
        healthcareBundleDataService,
        providerUnitService,
        producerService,
        providerUnitGroupService
    )

    private val healthcareBundle = TestModelFactory.buildHealthcareBundle()

    private val providerUnit = TestModelFactory.buildProviderUnit()


    @Test
    fun `#add should create a new healthcare bundle`() = runBlocking {
        coEvery {
            healthcareBundleDataService.add(healthcareBundle.toModel())
        } returns healthcareBundle.toModel().success()
        coEvery {
            producerService.produce(match { it::class == HealthcareBundleUpsertedEvent::class })
        } returns mockk()

        val result = healthcareBundleService.add(healthcareBundle)
        assertThat(result).isSuccessWithData(healthcareBundle)

        coVerifyOnce {
            producerService.produce(match { it::class == HealthcareBundleUpsertedEvent::class })
        }
    }

    @Test
    fun `#update should update a healthcare bundle`() = runBlocking {
        coEvery {
            healthcareBundleDataService.findOne(queryEq {
                where {
                    this.id.eq(healthcareBundle.id)
                }
            })
        } returns healthcareBundle.toModel().success()
        coEvery {
            healthcareBundleDataService.update(healthcareBundle.toModel())
        } returns healthcareBundle.toModel().success()
        coEvery {
            producerService.produce(match { it::class == HealthcareBundleUpdatedEvent::class })
        } returns mockk()
        coEvery {
            producerService.produce(match { it::class == HealthcareBundleUpsertedEvent::class })
        } returns mockk()

        val result = healthcareBundleService.update(healthcareBundle)
        assertThat(result).isSuccessWithData(healthcareBundle)

        coVerifyOnce {
            producerService.produce(match { it::class == HealthcareBundleUpdatedEvent::class })
            producerService.produce(match { it::class == HealthcareBundleUpsertedEvent::class })
        }
    }

    @Test
    fun `#getByCodeAndProviderUnitGroup should return correctly`() = runBlocking {
        val code = "123"
        val providerUnitGroupId = RangeUUID.generate()
        val healthcareBundle = TestModelFactory.buildHealthcareBundle(
            code = code,
            providerUnitGroupId = providerUnitGroupId
        )

        coEvery {
            healthcareBundleDataService.findOne(queryEq {
                where { this.code.eq(code).and(this.providerUnitGroupId.eq(providerUnitGroupId)) }
            })
        } returns healthcareBundle.toModel().success()

        val result = healthcareBundleService.getByCodeAndProviderUnitGroup(code, providerUnitGroupId)
        assertThat(result).isSuccessWithData(healthcareBundle)
    }

    @Test
    fun `#update should update a healthcare bundle and send unlink healthcare resource event if composition hash is changed`() =
        runBlocking {
            val healthcareBundle = healthcareBundle.copy(compositionHash = "abc")

            coEvery {
                healthcareBundleDataService.findOne(queryEq {
                    where {
                        this.id.eq(healthcareBundle.id)
                    }
                })
            } returns healthcareBundle.toModel().success()
            coEvery {
                healthcareBundleDataService.update(healthcareBundle.toModel())
            } returns healthcareBundle.toModel().copy(compositionHash = "novo").success()
            coEvery {
                producerService.produce(match { it::class == HealthcareBundleUpdatedEvent::class })
            } returns mockk()
            coEvery {
                producerService.produce(match { it::class == HealthcareBundleUpsertedEvent::class })
            } returns mockk()
            coEvery {
                producerService.produce(match { it::class == UnlinkHealthcareResourceFromHealthcareBundleEvent::class })
            } returns mockk()

            val result = healthcareBundleService.update(healthcareBundle)
            assertThat(result).isSuccessWithData(healthcareBundle.copy(compositionHash = "novo"))

            coVerifyOnce {
                producerService.produce(match { it::class == HealthcareBundleUpdatedEvent::class })
                producerService.produce(match { it::class == HealthcareBundleUpsertedEvent::class })
                producerService.produce(match { it::class == UnlinkHealthcareResourceFromHealthcareBundleEvent::class })
            }
        }

    @Test
    fun `#delete should delete a healthcare bundle`() = runBlocking {
        coEvery {
            healthcareBundleDataService.delete(healthcareBundle.toModel())
        } returns true.success()

        val result = healthcareBundleService.delete(healthcareBundle)
        assertThat(result).isSuccessWithData(true)
    }

    @Test
    fun `#addByBundles should create a new healthcare bundle of HEALTH_INSTITUTION by bundle list`(): Unit =
        runBlocking {
            val bundles = listOf(
                TestModelFactory.buildHealthcareBundle(name = "Template 1", code = "1", primaryTuss = "0001"),
                TestModelFactory.buildHealthcareBundle(name = "Template 2", code = "2", primaryTuss = "0002")
            )
            val bundleIds = bundles.map { it.id }
            val healthcareBundleList = bundles.map {
                HealthcareBundle(
                    id = RangeUUID.generate(),
                    name = it.name,
                    code = it.code,
                    providerName = providerUnit.name,
                    primaryTuss = it.primaryTuss,
                    hasMedicalFees = it.hasMedicalFees,
                    groups = it.groups,
                    includedResources = it.includedResources,
                    excludedResources = it.excludedResources,
                    validAfter = LocalDate.now(),
                ).draft()
            }

            coEvery {
                healthcareBundleDataService.find(
                    queryEq {
                        where { this.id.inList(bundleIds) }
                    }
                )
            } returns bundles.toModel().success()

            val providerUnitGroup = TestModelFactory.buildProviderUnitGroup()
            coEvery {
                providerUnitGroupService.get(providerUnitGroup.id)
            } returns providerUnitGroup.success()

            coEvery {
                healthcareBundleDataService.addList(match {
                    it.first().code == bundles.first().code &&
                            it.last().code == bundles.last().code &&
                            it.first().category == HealthcareBundleCategory.HEALTH_INSTITUTION &&
                            it.last().category == HealthcareBundleCategory.HEALTH_INSTITUTION
                })
            } returns healthcareBundleList.toModel().success()

            val result = healthcareBundleService.addByBundles(
                bundleIds = bundleIds,
                providerUnitGroupId = providerUnitGroup.id,
            )
            assertThat(result).isSuccessWithData(healthcareBundleList)

            coVerifyNone { healthcareBundleDataService.add(any()) }
        }

    @Test
    fun `#addByBundles should create a new healthcare bundle of HEALTH_SPECIALIST by bundle list`(): Unit =
        runBlocking {
            val bundles = listOf(
                TestModelFactory.buildHealthcareBundle(name = "Template 1", code = "1", primaryTuss = "0001")
            )
            val bundleIds = bundles.map { it.id }
            val healthcareBundleList = bundles.map {
                HealthcareBundle(
                    id = RangeUUID.generate(),
                    name = it.name,
                    code = it.code,
                    primaryTuss = it.primaryTuss,
                    hasMedicalFees = it.hasMedicalFees,
                    groups = it.groups,
                    includedResources = it.includedResources,
                    excludedResources = it.excludedResources,
                    validAfter = LocalDate.now(),
                ).draft().identifyCategory()
            }

            coEvery {
                healthcareBundleDataService.find(
                    queryEq {
                        where {
                            this.id.inList(bundleIds)
                        }
                    }
                )
            } returns bundles.toModel().success()

            coEvery {
                healthcareBundleDataService.add(match {
                    it.code == bundles.first().code &&
                            it.category == HealthcareBundleCategory.HEALTH_SPECIALIST
                })
            } returns healthcareBundleList.toModel().first().success()

            val result = healthcareBundleService.addByBundles(bundleIds = bundleIds)
            assertThat(result).isSuccessWithData(healthcareBundleList)

            coVerifyNone { providerUnitService.get(any()) }
            coVerifyNone { healthcareBundleDataService.addList(any()) }
        }

    @Test
    fun `#getByIds should return list of healthcare bundles`() = runBlocking {
        val healthcareBundles = generateSequence { TestModelFactory.buildHealthcareBundle() }.take(5).toList()
        val ids = healthcareBundles.map { it.id }

        coEvery {
            healthcareBundleDataService.find(
                queryEq {
                    where {
                        this.id.inList(ids)
                    }
                }
            )
        } returns healthcareBundles.toModel().success()

        val result = healthcareBundleService.getByIds(ids)
        assertThat(result).isSuccessWithData(healthcareBundles)
    }

    @Test
    fun `#list should return list of healthcare bundle when no parameter is passed`() = runBlocking {
        coEvery {
            healthcareBundleDataService.find(queryEq {
                where {
                    this.status.inList(listOf(HealthcareBundleStatus.ACTIVE, HealthcareBundleStatus.INACTIVE))
                }
                    .orderBy { createdAt }
                    .sortOrder { desc }
                    .offset { 0 }
                    .limit { 10 }
            })
        } returns listOf(healthcareBundle).toModel().success()

        coEvery {
            healthcareBundleDataService.count(queryEq {
                where {
                    this.status.inList(listOf(HealthcareBundleStatus.ACTIVE, HealthcareBundleStatus.INACTIVE))
                }
            })
        } returns 1.success()

        val result = healthcareBundleService.list(
            status = listOf(HealthcareBundleStatus.ACTIVE, HealthcareBundleStatus.INACTIVE),
            range = IntRange(0, 9),
            healthcareBundleListFilters = HealthcareBundleListFilters()
        )
        assertThat(result).isSuccessWithData(
            HealthcareBundleListWithCount(listOf(healthcareBundle), 1)
        )
    }

    @Test
    fun `#list should return list of healthcare bundle when searchTerm is passed`() = runBlocking {
        coEvery {
            healthcareBundleDataService.find(queryEq {
                where {
                    this.status.inList(listOf(HealthcareBundleStatus.ACTIVE))
                        .and(this.searchTokens.search("teste"))
                }
                    .orderBy { createdAt }
                    .sortOrder { desc }
                    .offset { 0 }
                    .limit { 10 }
            })
        } returns listOf(healthcareBundle).toModel().success()

        coEvery {
            healthcareBundleDataService.count(queryEq {
                where {
                    this.status.inList(listOf(HealthcareBundleStatus.ACTIVE))
                        .and(this.searchTokens.search("teste"))
                }
            })
        } returns 1.success()

        val result = healthcareBundleService.list(
            status = listOf(HealthcareBundleStatus.ACTIVE),
            range = IntRange(0, 9),
            healthcareBundleListFilters = HealthcareBundleListFilters(
                searchTerm = "teste"
            )
        )
        assertThat(result).isSuccessWithData(
            HealthcareBundleListWithCount(listOf(healthcareBundle), 1)
        )
    }

    @Test
    fun `#list should return list of healthcare bundle when providerName is passed`() = runBlocking {
        val providerName = "Fleury"

        coEvery {
            healthcareBundleDataService.find(queryEq {
                where {
                    this.status.inList(listOf((HealthcareBundleStatus.ACTIVE)))
                        .and(this.providerName.like(providerName))
                }
                    .orderBy { createdAt }
                    .sortOrder { desc }
                    .offset { 0 }
                    .limit { 10 }
            })
        } returns listOf(healthcareBundle).toModel().success()

        coEvery {
            healthcareBundleDataService.count(queryEq {
                where {
                    this.status.inList(listOf(HealthcareBundleStatus.ACTIVE))
                        .and(this.providerName.like(providerName))
                }
            })
        } returns 1.success()

        val result = healthcareBundleService.list(
            status = listOf(HealthcareBundleStatus.ACTIVE),
            range = IntRange(0, 9),
            healthcareBundleListFilters = HealthcareBundleListFilters(
                providerName = providerName
            )
        )
        assertThat(result).isSuccessWithData(
            HealthcareBundleListWithCount(listOf(healthcareBundle), 1)
        )
    }

    @Test
    fun `#updateList should update list of healthcare bundles`() = runBlocking {
        coEvery {
            healthcareBundleDataService.updateList(listOf(healthcareBundle).toModel(), true)
        } returns listOf(healthcareBundle).toModel().success()
        coEvery {
            producerService.produce(match { it::class == HealthcareBundleUpsertedEvent::class })
        } returns mockk()

        val result = healthcareBundleService.updateList(listOf(healthcareBundle))
        assertThat(result).isSuccessWithData(listOf(healthcareBundle))

        coVerifyOnce {
            healthcareBundleDataService.updateList(listOf(healthcareBundle).toModel(), true)
            producerService.produce(match { it::class == HealthcareBundleUpsertedEvent::class })
        }
    }

    @Test
    fun `#updateList should update long list of healthcare bundles`() = runBlocking {
        val healthcareBundles = List(60) { TestModelFactory.buildHealthcareBundle() }

        val healthcareBundlesPage1 = healthcareBundles.subList(0, 50)
        val healthcareBundlesPage2 = healthcareBundles.subList(50, 60)

        coEvery {
            healthcareBundleDataService.updateList(healthcareBundlesPage1.toModel(), true)
        } returns healthcareBundlesPage1.toModel().success()
        coEvery {
            healthcareBundleDataService.updateList(healthcareBundlesPage2.toModel(), true)
        } returns healthcareBundlesPage2.toModel().success()
        coEvery {
            producerService.produce(match { it::class == HealthcareBundleUpsertedEvent::class })
        } returns mockk()

        val result = healthcareBundleService.updateList(healthcareBundles)
        assertThat(result).isSuccess()

        val returnedHealthcareBundles = result.get()

        assertThat(returnedHealthcareBundles).containsExactlyInAnyOrderElementsOf(healthcareBundles)

        coVerify(exactly = 2) { healthcareBundleDataService.updateList(any(), true) }
        coVerify(exactly = 60) { producerService.produce(match { it::class == HealthcareBundleUpsertedEvent::class }) }
    }

    @Test
    fun `#list should return list of healthcare bundle when category is passed`() = runBlocking {
        coEvery {
            healthcareBundleDataService.find(queryEq {
                where {
                    this.status.inList(listOf((HealthcareBundleStatus.ACTIVE)))
                        .and(this.category.eq(HealthcareBundleCategory.HEALTH_INSTITUTION))
                }
                    .orderBy { createdAt }
                    .sortOrder { desc }
                    .offset { 0 }
                    .limit { 10 }
            })
        } returns listOf(healthcareBundle).toModel().success()

        coEvery {
            healthcareBundleDataService.count(queryEq {
                where {
                    this.status.inList(listOf((HealthcareBundleStatus.ACTIVE)))
                        .and(this.category.eq(HealthcareBundleCategory.HEALTH_INSTITUTION))
                }
            })
        } returns 1.success()

        val result = healthcareBundleService.list(
            status = listOf(HealthcareBundleStatus.ACTIVE),
            range = IntRange(0, 9),
            healthcareBundleListFilters = HealthcareBundleListFilters(
                category = "HEALTH_INSTITUTION"
            )
        )
        assertThat(result).isSuccessWithData(
            HealthcareBundleListWithCount(listOf(healthcareBundle), 1)
        )
    }

    @Test
    fun `#listProviderName should return list of healthcare bundle grouped by provider name`() = runBlocking {
        coEvery {
            healthcareBundleDataService.find(queryEq {
                where {
                    this.category.eq(HealthcareBundleCategory.HEALTH_INSTITUTION)
                }
                    .groupBy { listOf(this.providerName) }
                    .orderBy { updatedAt }
                    .offset { 0 }
                    .limit { 10 }
            })
        } returns listOf(healthcareBundle).toModel().success()

        val result = healthcareBundleService.listProviderName(
            searchTerm = null,
            range = IntRange(0, 9),
            category = HealthcareBundleCategory.HEALTH_INSTITUTION
        )
        assertThat(result).isSuccessWithData(listOf(healthcareBundle))
    }

    @Test
    fun `#listProviderName should return list of healthcare bundle grouped by provider name with search tokens`() =
        runBlocking {
            val searchTerm = "Fleur"
            coEvery {
                healthcareBundleDataService.find(queryEq {
                    where {
                        this.category.eq(HealthcareBundleCategory.HEALTH_INSTITUTION)
                            .and(this.providerName.like(searchTerm))
                    }
                        .groupBy { listOf(this.providerName) }
                        .orderBy { updatedAt }
                        .offset { 0 }
                        .limit { 10 }
                })
            } returns listOf(healthcareBundle).toModel().success()

            val result = healthcareBundleService.listProviderName(
                searchTerm = searchTerm,
                range = IntRange(0, 9),
                category = HealthcareBundleCategory.HEALTH_INSTITUTION
            )
            assertThat(result).isSuccessWithData(listOf(healthcareBundle))
        }

    @Test
    fun `#getByResourceGroupWithNotEmptyCode should get bundles that have group on their composition`() =
        runBlocking {
            val resourceGroupId = RangeUUID.generate()

            coEvery {
                healthcareBundleDataService.find(queryEq {
                    where {
                        this.groups.contains(resourceGroupId).and(this.code.isNotNull())
                    }
                })
            } returns listOf(healthcareBundle).toModel().success()

            val result = healthcareBundleService.getByResourceGroupWithNotEmptyCode(resourceGroupId)
            assertThat(result).isSuccessWithData(listOf(healthcareBundle))
        }

    @Test
    fun `#getActiveNotExpiredByCompositionHash should get bundles that are active and not expired by composition hash`() =
        runBlocking {
            coEvery {
                healthcareBundleDataService.find(queryEq {
                    where {
                        this.status.eq(HealthcareBundleStatus.ACTIVE)
                            .and(this.validBefore.isNull())
                            .and(this.compositionHash.eq("abc"))
                            .and(this.code.eq("123"))
                    }
                })
            } returns listOf(healthcareBundle.toModel()).success()

            val result = healthcareBundleService.getActiveNotExpiredByCompositionHashAndCode("abc", "123")

            assertThat(result).isSuccessWithData(listOf(healthcareBundle))
        }

    @OptIn(OrPredicateUsage::class)
    @Test
    fun `#listByProviderUnitId should get ProviderUnitGroup bundles by ProviderUnit id`() = runBlocking {
        val searchTerm = "query"

        val providerUnitId = RangeUUID.generate()
        val providerUnitGroupId = RangeUUID.generate()

        val providerUnit = TestModelFactory.buildProviderUnit(id = providerUnitId).copy(
            providerUnitGroupId = providerUnitGroupId
        )

        coEvery {
            providerUnitService.get(providerUnitId)
        } returns providerUnit.success()

        val healthcareBundles = generateSequence {
            TestModelFactory.buildHealthcareBundle(providerUnitGroupId = providerUnitGroupId)
        }.take(5).toList()

        coEvery {
            healthcareBundleDataService.find(queryEq {
                where {
                    this.providerUnitGroupId.eq(providerUnitGroupId)
                        .and(this.searchTokens.search(searchTerm))
                        .and(this.status.eq(HealthcareBundleStatus.ACTIVE))
                        .and(scope(this.validBefore.isNull()).or(this.validBefore.greater(LocalDate.now())))
                        .and(this.code.isNotNull())
                }
            })
        } returns healthcareBundles.toModel().success()

        val result = healthcareBundleService.listByProviderUnitId(
            searchTerm = searchTerm,
            providerUnitId = providerUnitId
        )

        assertThat(result).isSuccessWithData(healthcareBundles)

        coVerifyNone { providerUnitGroupService.getByCnpj(any()) }
    }

    @OptIn(OrPredicateUsage::class)
    @Test
    fun `#listByProviderUnitId should get ProviderUnitGroup bundles by ProviderUnit id and Guia Type is PS`() =
        runBlocking {
            val searchTerm = "query"

            val providerUnitId = RangeUUID.generate()
            val providerUnitGroupId = RangeUUID.generate()

            val providerUnit = TestModelFactory.buildProviderUnit(id = providerUnitId).copy(
                providerUnitGroupId = providerUnitGroupId
            )

            coEvery {
                providerUnitService.get(providerUnitId)
            } returns providerUnit.success()

            val healthcareBundles = generateSequence {
                TestModelFactory.buildHealthcareBundle(providerUnitGroupId = providerUnitGroupId)
            }.take(5).toList()

            coEvery {
                healthcareBundleDataService.find(queryEq {
                    where {
                        this.providerUnitGroupId.eq(providerUnitGroupId)
                            .and(this.searchTokens.search(searchTerm))
                            .and(this.status.eq(HealthcareBundleStatus.ACTIVE))
                            .and(scope(this.validBefore.isNull()).or(this.validBefore.greater(LocalDate.now())))
                            .and(this.code.inList(listOf("123", "321")))
                    }
                })
            } returns healthcareBundles.toModel().success()

            withFeatureFlag(FeatureNamespace.EXEC_INDICATOR, "emergency_room_procedures", listOf("123", "321")) {
                val result = healthcareBundleService.listByProviderUnitId(
                    searchTerm = searchTerm,
                    providerUnitId = providerUnitId,
                    guiaType = MvUtil.TISS.PS
                )

                assertThat(result).isSuccessWithData(healthcareBundles)
            }

            coVerifyNone { providerUnitGroupService.getByCnpj(any()) }
        }

    @OptIn(OrPredicateUsage::class)
    @Test
    fun `#listByProviderUnitId should get ProviderUnit bundles by ProviderUnit id with ProviderUnit CNPJ`() =
        runBlocking {
            val searchTerm = "query"

            val providerUnitId = RangeUUID.generate()

            val providerUnit = TestModelFactory.buildProviderUnit(
                id = providerUnitId,
                cnpj = "12345678900"
            ).copy(providerUnitGroupId = null)

            coEvery {
                providerUnitService.get(providerUnitId)
            } returns providerUnit.success()

            val providerUnitGroup = TestModelFactory.buildProviderUnitGroup(cnpj = providerUnit.cnpj!!)

            coEvery {
                providerUnitGroupService.getByCnpj(providerUnit.cnpj!!)
            } returns providerUnitGroup.success()

            val healthcareBundles = generateSequence {
                TestModelFactory.buildHealthcareBundle(providerUnitGroupId = providerUnitGroup.id)
            }.take(5).toList()

            coEvery {
                healthcareBundleDataService.find(queryEq {
                    where {
                        this.providerUnitGroupId.eq(providerUnitGroup.id)
                            .and(this.searchTokens.search(searchTerm))
                            .and(this.status.eq(HealthcareBundleStatus.ACTIVE))
                            .and(scope(this.validBefore.isNull()).or(this.validBefore.greater(LocalDate.now())))
                            .and(this.code.isNotNull())
                    }
                })
            } returns healthcareBundles.toModel().success()

            val result = healthcareBundleService.listByProviderUnitId(
                searchTerm = searchTerm,
                providerUnitId = providerUnitId
            )

            assertThat(result).isSuccessWithData(healthcareBundles)

            coVerifyOnce { providerUnitGroupService.getByCnpj(any()) }
        }

    @Test
    fun `#listByProviderUnitId should throw error when ProviderUnitGroup not found `() = runBlocking {
        val searchTerm = "query"
        val providerUnitId = RangeUUID.generate()

        val providerUnit = TestModelFactory.buildProviderUnit(
            id = providerUnitId,
            cnpj = "12345678900"
        ).copy(providerUnitGroupId = null)

        coEvery {
            providerUnitService.get(providerUnitId)
        } returns providerUnit.success()

        coEvery {
            providerUnitGroupService.getByCnpj(providerUnit.cnpj!!)
        } returns NotFoundException().failure()

        val result = healthcareBundleService.listByProviderUnitId(
            searchTerm = searchTerm,
            providerUnitId = providerUnitId
        )

        assertThat(result).isFailureOfType(NotFoundException::class)

        coVerifyNone { healthcareBundleDataService.find(any()) }
    }

    @Test
    fun `#listByProviderUnitId should throw error when provider unit CNPJ is null`() = runBlocking {
        val searchTerm = "query"
        val providerUnitId = RangeUUID.generate()

        val providerUnit = TestModelFactory.buildProviderUnit(
            id = providerUnitId,
            cnpj = null
        ).copy(providerUnitGroupId = null)

        coEvery {
            providerUnitService.get(providerUnitId)
        } returns providerUnit.success()

        val result = healthcareBundleService.listByProviderUnitId(
            searchTerm = searchTerm,
            providerUnitId = providerUnitId
        )

        assertThat(result).isFailureOfType(ProviderUnitGroupNotFoundByProviderUnit::class)

        coVerifyNone { healthcareBundleDataService.find(any()) }
        coVerifyNone { providerUnitGroupService.getByCnpj(any()) }
    }

    @Test
    fun `#updateMany should update all provided healthcare bundles`() = runBlocking {
        val healthcareBundlesModel = listOf(healthcareBundle.copy(primaryTuss = "123456"))
        coEvery {
            healthcareBundleDataService.findOne(queryEq {
                where {
                    this.id.eq(healthcareBundlesModel[0].id)
                }
            })
        } returns healthcareBundlesModel[0].toModel().success()
        coEvery {
            healthcareBundleDataService.update(healthcareBundlesModel[0].toModel())
        } returns healthcareBundlesModel[0].toModel().success()
        coEvery {
            producerService.produce(match { it::class == HealthcareBundleUpdatedEvent::class })
        } returns mockk()
        coEvery {
            producerService.produce(match { it::class == HealthcareBundleUpsertedEvent::class })
        } returns mockk()

        val result = healthcareBundleService.updateMany(healthcareBundlesModel)
        assertThat(result).isSuccessWithData(healthcareBundlesModel)

        coVerifyOnce {
            producerService.produce(match { it::class == HealthcareBundleUpdatedEvent::class })
            producerService.produce(match { it::class == HealthcareBundleUpsertedEvent::class })
        }
    }
}
