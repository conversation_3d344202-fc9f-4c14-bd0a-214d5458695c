package br.com.alice.api.ops.utils

import br.com.alice.common.logging.logger

object Logging {

    fun logPIISearch(staffId: Any?, logText: String, logValue: Any) {
        val searcher = parseSearcher(staffId)
        logger.info(
            "search_pii_on_aos",
            "searcher" to searcher,
            logText to logValue
        )
    }

    fun logHISearch(staffId: Any?, logText: String, logValue: Any) {
        val searcher = parseSearcher(staffId)
        logger.info(
            "search_hi_on_aos",
            "searcher" to searcher,
            logText to logValue
        )
    }

    private fun parseSearcher(staffId: Any?) = staffId?.let { it.toString().ifEmpty { "unknown" } } ?: "unknown"
}
