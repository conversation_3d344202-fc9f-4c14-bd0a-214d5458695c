package br.com.alice.healthlogic.services.adherence

import br.com.alice.common.RangeUUID
import br.com.alice.common.core.PersonId
import br.com.alice.common.data.dsl.matchers.ResultAssert.Companion.assertThat
import br.com.alice.common.helpers.coVerifyNone
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.AdherenceRecommendedAction
import br.com.alice.data.layer.models.HLAdherence.AdherenceReferenceModel
import br.com.alice.data.layer.models.HLAdherence.AdherenceResultType
import br.com.alice.data.layer.models.ReferralSpecialty
import br.com.alice.data.layer.models.ServiceScriptNode
import br.com.alice.data.layer.models.ServiceScriptRelationship
import br.com.alice.healthlogic.client.HealthLogicRecordService
import br.com.alice.healthlogic.client.ScriptService
import br.com.alice.healthlogic.models.adherence.AdherenceResponse
import com.github.kittinunf.result.success
import io.mockk.clearAllMocks
import io.mockk.coEvery
import io.mockk.mockk
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.TestInstance
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.MethodSource
import kotlin.test.BeforeTest

@TestInstance(TestInstance.Lifecycle.PER_CLASS)
internal class ValidateProgramServiceTest {
    private val healthLogicRecordService: HealthLogicRecordService = mockk()
    private val scriptService: ScriptService = mockk()
    private val validateTemplateService: ValidateTemplateService = mockk()
    private val service = ValidateProgramService(
        healthLogicRecordService,
        scriptService,
        validateTemplateService
    )

    private val specialtyId = RangeUUID.generate()
    private val personId = PersonId()
    private val healthConditionId = RangeUUID.generate()
    private val protocol = TestModelFactory.buildProtocol(healthConditionIds = listOf(healthConditionId))
    private val serviceScriptAction = TestModelFactory.buildServiceScriptAction()
    private val serviceScriptNode = TestModelFactory.buildServiceScriptNode(id= protocol.rootNodeId, serviceScriptActionIds = listOf(serviceScriptAction.id))
    private val nodeWithoutAction = TestModelFactory.buildServiceScriptNode(id= protocol.rootNodeId, serviceScriptActionIds = emptyList())
    private val adherenceRecommendedProgramAction = AdherenceRecommendedAction(
        id = serviceScriptNode.id,
        healthLogicId = serviceScriptNode.rootNodeId,
        model = AdherenceReferenceModel.PROGRAM
    )
    private val healthPlanTask = TestModelFactory.buildHealthPlanTask(personId = personId, content = mapOf("specialty" to ReferralSpecialty(id = specialtyId, name = "Psicologia")))
    private val serviceScriptRelationship = TestModelFactory.buildServiceScriptRelationship(childId = serviceScriptNode.id, parentId = RangeUUID.generate())


    @BeforeTest
    fun before() {
        clearAllMocks()
    }

    private fun testParameters() = listOf(
        arrayOf(
            listOf(serviceScriptNode),
            listOf(serviceScriptRelationship),
            AdherenceResultType.ADHERENT,
            null
        ),
        arrayOf(
            listOf(serviceScriptNode),
            listOf(serviceScriptRelationship),
            AdherenceResultType.NON_ADHERENT,
            listOf(adherenceRecommendedProgramAction)
        ),
        arrayOf(
            listOf(nodeWithoutAction),
            listOf(serviceScriptRelationship),
            AdherenceResultType.WITHOUT_REFERENCE,
            null
        ),
    )

    @ParameterizedTest(name = "with result equal to {2}")
    @MethodSource("testParameters")
    fun `#validate - should validate task adherence`(
        serviceScriptNodeList: List<ServiceScriptNode>,
        serviceScriptRelationshipList: List<ServiceScriptRelationship>,
        expectedResultType: AdherenceResultType,
        adherenceRecomendedActions: List<AdherenceRecommendedAction>?
    ) = runBlocking {
        coEvery {
            healthLogicRecordService.getProgramsByPersonId(personId)
        } returns serviceScriptNodeList.success()
        coEvery {
            scriptService.findRelationshipByChildrenAndHealthCondition(
                listOf(serviceScriptNode.id),
                healthConditionId.toString()
            )
        } returns serviceScriptRelationshipList.success()

        val expected = AdherenceResponse(result = expectedResultType, adherenceRecommendedActions = adherenceRecomendedActions)

        coEvery {
            validateTemplateService.validate(
                serviceScriptActionIds = serviceScriptNode.serviceScriptActionIds,
                healthPlanTask = healthPlanTask,
                adherenceRecommendedActions = listOf(adherenceRecommendedProgramAction)
            )
        } returns expected.success()

        val result = service.validate(
            personId = personId,
            healthConditionId = healthConditionId,
            healthPlanTask = healthPlanTask
        )

        assertThat(result).isSuccessWithData(expected)

        coVerifyOnce { healthLogicRecordService.getProgramsByPersonId(any()) }
        coVerifyOnce { scriptService.findRelationshipByChildrenAndHealthCondition(any(), any()) }
        coVerifyNone { scriptService.getNodesByRootNodesAndType(any(), any()) }
    }

    @ParameterizedTest(name = "with result equal to {2}")
    @MethodSource("testParameters")
    fun `#validateByList - should validate task adherence`(
        serviceScriptNodeList: List<ServiceScriptNode>,
        serviceScriptRelationshipList: List<ServiceScriptRelationship>,
        expectedResultType: AdherenceResultType,
        adherenceRecomendedActions: List<AdherenceRecommendedAction>?
    ) = runBlocking {
        coEvery {
            healthLogicRecordService.getProgramsByPersonId(personId)
        } returns serviceScriptNodeList.success()
        coEvery {
            scriptService.findRelationshipByChildrenAndHealthCondition(
                listOf(serviceScriptNode.id),
                healthConditionId.toString()
            )
        } returns serviceScriptRelationshipList.success()

        val expected = listOf(
            AdherenceResponse(
                result = expectedResultType,
                adherenceRecommendedActions = adherenceRecomendedActions,
                healthPlanTask = healthPlanTask
            )
        )

        coEvery {
            validateTemplateService.validateByList(
                serviceScriptActionIds = serviceScriptNode.serviceScriptActionIds,
                healthPlanTasks = listOf(healthPlanTask),
                adherenceRecommendedActions = listOf(adherenceRecommendedProgramAction)
            )
        } returns expected.success()

        val result = service.validateByList(
            personId = personId,
            healthConditionId = healthConditionId,
            healthPlanTasks = listOf(healthPlanTask)
        )

        assertThat(result).isSuccessWithData(expected)

        coVerifyOnce { healthLogicRecordService.getProgramsByPersonId(any()) }
        coVerifyOnce { scriptService.findRelationshipByChildrenAndHealthCondition(any(), any()) }
        coVerifyNone { scriptService.getNodesByRootNodesAndType(any(), any()) }
    }


}
