package br.com.alice.business.consumers

import br.com.alice.business.clients.ply.PlyClient
import br.com.alice.business.events.CognitoCompanyUpsertRequestedRawEvent
import br.com.alice.common.core.extensions.unescapeJson
import br.com.alice.common.data.dsl.matchers.ResultAssert
import br.com.alice.common.featureflag.withFeatureFlag
import br.com.alice.common.readFile
import br.com.alice.data.layer.models.FeatureNamespace
import com.github.kittinunf.result.failure
import com.github.kittinunf.result.success
import io.mockk.coEvery
import io.mockk.mockk
import io.mockk.unmockkAll
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.TestInstance
import java.io.IOException
import kotlin.test.AfterTest
import kotlin.test.BeforeTest
import kotlin.test.Test

@TestInstance(TestInstance.Lifecycle.PER_CLASS)
class CognitoRawDataConsumerTest : ConsumerTest() {

    private val plyClient: PlyClient = mockk()
    private val consumer = CognitoRawDataConsumer(plyClient)
    private val rawData = readFile("testResources/cognito/raw-cognito-event.json")

    @BeforeTest
    fun setup() {
        super.before()
    }

    @AfterTest
    fun cleanup() {
        super.clear()
        unmockkAll()
    }

    @Test
    fun `#handleCognitoRawData - send raw data on feature flag is enabled`() = runBlocking {

        withFeatureFlag(FeatureNamespace.BUSINESS, "fly_cognito_webhook_enabled", true) {
            val event = CognitoCompanyUpsertRequestedRawEvent(rawEvent = rawData)

            coEvery { plyClient.sendCognitoRawData(rawData) } returns true.success()

            val result = consumer.handleCognitoRawData(event)
            ResultAssert.assertThat(result).isSuccessWithData(true)
        }
    }

    @Test
    fun `#handleCognitoRawData - ignore event if feature flag is disabled`() = runBlocking {

        withFeatureFlag(FeatureNamespace.BUSINESS, "fly_cognito_webhook_enabled", false) {
            val event = CognitoCompanyUpsertRequestedRawEvent(rawEvent = rawData)
            val result = consumer.handleCognitoRawData(event)
            ResultAssert.assertThat(result).isSuccessWithData(true)
        }
    }

    @Test
    fun `#handleCognitoRawData - throw error on send raw data to ply`() = runBlocking {

        withFeatureFlag(FeatureNamespace.BUSINESS, "fly_cognito_webhook_enabled", true) {
            val event = CognitoCompanyUpsertRequestedRawEvent(rawEvent = rawData)

            coEvery { plyClient.sendCognitoRawData(rawData) } returns IOException().failure()

            val result = consumer.handleCognitoRawData(event)
            ResultAssert.assertThat(result).isFailure()
        }
    }

}
