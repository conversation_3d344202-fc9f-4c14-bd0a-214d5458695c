package br.com.alice.member.api.controllers.onboarding.v1

import M
import br.com.alice.common.DefaultErrorResponse
import br.com.alice.common.MultipartRequest
import br.com.alice.common.NotFoundResponse
import br.com.alice.common.Response
import br.com.alice.common.ValidationErrorResponse
import br.com.alice.common.controllers.Controller
import br.com.alice.common.core.PersonId
import br.com.alice.common.core.extensions.isImage
import br.com.alice.common.extensions.getOrNullIfNotFound
import br.com.alice.common.extensions.toPersonId
import br.com.alice.common.logging.logger
import br.com.alice.data.layer.models.OnboardingPhase.REGISTRATION
import br.com.alice.data.layer.models.Person
import br.com.alice.data.layer.models.PersonRegistration
import br.com.alice.data.layer.models.PersonRegistrationAnswer
import br.com.alice.data.layer.models.PersonRegistrationStep
import br.com.alice.member.api.models.ChatActionUrl
import br.com.alice.member.api.models.Navigation
import br.com.alice.member.api.models.onboarding.ChatMessage
import br.com.alice.member.api.models.onboarding.ChatResponse
import br.com.alice.member.api.models.onboarding.PersonRegistrationChat
import br.com.alice.member.api.services.MemberDocuments
import br.com.alice.member.api.services.OnboardingMobileRouter
import br.com.alice.membership.client.onboarding.InvalidAnswerException
import br.com.alice.membership.client.onboarding.OnboardingService
import br.com.alice.membership.client.onboarding.PersonRegistrationService
import br.com.alice.membership.client.onboarding.RegistrationAlreadyFinishedException
import br.com.alice.membership.client.onboarding.RegistrationNotStartedException
import br.com.alice.onboarding.client.InsurancePortabilityService
import br.com.alice.person.client.PersonService
import io.ktor.http.HttpStatusCode.Companion.OK
import kotlinx.coroutines.async
import kotlinx.coroutines.coroutineScope

class PersonRegistrationController(
    private val personRegistrationService: PersonRegistrationService,
    private val personService: PersonService,
    private val memberDocuments: MemberDocuments,
    private val onboardingService: OnboardingService,
    private val portabilityService: InsurancePortabilityService,
) : Controller() {

    suspend fun findNotFinishedOnboardingProcessByPerson(): Response {
        val personId = currentUid().toPersonId()
        val personRegistration = personRegistrationService.findByPerson(personId).getOrNullIfNotFound()

        val response = if (personRegistration == null || personRegistration.hasFinished()) NotFoundResponse() else {
            val chat = getChat(personRegistration)
            val chatResponse = chatByPersonRegistrationProcess(
                chat,
                chat.messages,
                ChatActionUrl.build(personRegistration.currentStep),
            )
            Response(OK, chatResponse)
        }

        logger.info("/onboarding/registration/", "person_id" to personId, "response" to response)
        return response
    }

    suspend fun answer(step: String, answerRequest: AnswerRequest): Response {
        val personId = PersonId.fromString(currentUid())
        val currentStep = PersonRegistrationStep.valueOf(step.uppercase())
        logger.info("/onboarding/registration/answer/$currentStep", "person_id" to personId, "answer_request" to answerRequest.value)
        val response = answer(personId, currentStep, answerRequest.value)

        logger.info("/onboarding/registration/answer/$currentStep", "person_id" to personId, "response" to response)
        return response
    }

    suspend fun uploadIdentityDocument(multipartRequest: MultipartRequest): Response {
        return verifyAnswer(PersonRegistrationStep.DOCUMENT_PHOTO, multipartRequest) { personId ->
            memberDocuments.uploadIdentityDocument(personId, multipartRequest)
        }
    }

    suspend fun uploadSelfiePhoto(multipartRequest: MultipartRequest): Response {
        return verifyAnswer(PersonRegistrationStep.SELFIE_PHOTO, multipartRequest) { personId ->
            val fileContent = multipartRequest.fileContent!!
            memberDocuments.uploadSelfiePhoto(personId, fileContent)
        }
    }

    private suspend fun answer(
        personId: PersonId,
        step: PersonRegistrationStep,
        value: String
    ): Response = coroutineScope {
        val person = personService.get(personId).get()

        personRegistrationService.answer(
            person, PersonRegistrationAnswer(
                step = step,
                value = value
            )
        ).fold(
            {
                when (it.onboardingPhase) {
                    REGISTRATION -> showChatResponse(it.registration, person)
                    else -> {
                        val portabilityDeferred = async { portabilityService.findByPerson(personId).getOrNullIfNotFound() }
                        val onboardingDeferred = async { onboardingService.findByPerson(personId) }

                        val portability = portabilityDeferred.await()
                        val onboarding = onboardingDeferred.await()

                        val portabilityRequested = portability != null

                        onboarding.fold({ onboarding ->
                            Response(
                                OK, PersonRegistrationFinishedResponse(
                                    navigation = OnboardingMobileRouter.getNavigation(
                                        person,
                                        it.onboardingPhase,
                                        portabilityRequested
                                    )
                                )
                            )
                        }, { throw it })
                    }
                }
            },
            {
                when (it) {
                    is InvalidAnswerException -> showValidationResponse(person)
                    is RegistrationAlreadyFinishedException -> DefaultErrorResponse(M.REGISTRATION_ALREADY_FINISHED)
                    is RegistrationNotStartedException -> DefaultErrorResponse(M.REGISTRATION_NOT_STARTED)
                    else -> throw it
                }
            }
        )
    }

    private suspend fun showValidationResponse(person: Person): Response {
        val registration = personRegistrationService.findByPerson(person.id).get()
        val chat = getChat(registration, person)
        val validationChat = chatByPersonRegistrationProcess(
            chat,
            chat.validationMessages,
            ChatActionUrl.build(registration.currentStep)
        )

        return ValidationErrorResponse(validationChat)
    }

    private suspend fun showChatResponse(registration: PersonRegistration, person: Person): Response {
        val chat = getChat(registration, person)

        val chatResponse = chatByPersonRegistrationProcess(
            chat,
            chat.messages,
            ChatActionUrl.build(registration.currentStep)
        )

        return Response(OK, chatResponse)
    }

    private suspend fun getChat(personRegistration: PersonRegistration, person: Person? = null): PersonRegistrationChat {
        val personFound = person ?: personService.get(personRegistration.personId).get()

        return PersonRegistrationChat.get(personRegistration, personFound)
    }

    private fun chatByPersonRegistrationProcess(
        chat: PersonRegistrationChat,
        messages: List<ChatMessage>,
        answerUrl: String
    ): ChatResponse {
        val currentStep = chat.step.toString()
        return ChatResponse(
            id = currentStep,
            input = chat.input(answerUrl),
            messages = messages,
            question = chat.question,
            title = chat.title,
            details = chat.details,
            hideNextButton = chat.hideNextButton,
        )
    }

    private suspend fun verifyAnswer(
        step: PersonRegistrationStep,
        multipartRequest: MultipartRequest,
        function: suspend (PersonId) -> String
    ): Response {
        val personId = PersonId.fromString(currentUid())
        val isImage = multipartRequest.file?.isImage() ?: false

        return if (!isImage) DefaultErrorResponse("image_required") else {
            val fileContent = multipartRequest.fileContent
            if (fileContent == null) DefaultErrorResponse("invalid_file") else {
                val documentId = function.invoke(personId)
                answer(personId, step, documentId)
            }
        }
    }

}

data class AnswerRequest(val value: String)
data class PersonRegistrationFinishedResponse(val navigation: Navigation)
