package br.com.alice.membership.controllers

import br.com.alice.business.client.BeneficiaryService
import br.com.alice.common.RangeUUID
import br.com.alice.common.Response
import br.com.alice.common.controllers.Controller
import br.com.alice.common.core.PersonId
import br.com.alice.common.core.extensions.atEndOfTheDay
import br.com.alice.common.core.extensions.toLocalDateTime
import br.com.alice.common.coroutine.pmap
import br.com.alice.common.extensions.coFoldNotFound
import br.com.alice.common.extensions.flatMapPair
import br.com.alice.common.extensions.getOrNullIfNotFound
import br.com.alice.common.extensions.mapEach
import br.com.alice.common.extensions.pmapEach
import br.com.alice.common.extensions.then
import br.com.alice.common.extensions.thenError
import br.com.alice.common.extensions.toPersonId
import br.com.alice.common.foldResponse
import br.com.alice.common.logging.logger
import br.com.alice.common.storage.FileVaultStorage
import br.com.alice.common.toResponse
import br.com.alice.common.withRootServicePolicy
import br.com.alice.common.withUnauthenticatedTokenWithKey
import br.com.alice.communication.crm.analytics.CrmAnalyticsTracker
import br.com.alice.communication.crm.analytics.UserProfile
import br.com.alice.data.layer.MEMBERSHIP_ENVIRONMENT_BACKFILL
import br.com.alice.data.layer.MEMBERSHIP_ROOT_SERVICE_NAME
import br.com.alice.data.layer.models.Cpt
import br.com.alice.data.layer.models.HealthConditionCodeType
import br.com.alice.data.layer.models.Member
import br.com.alice.data.layer.models.MemberProduct
import br.com.alice.data.layer.models.MemberStatus
import br.com.alice.data.layer.models.MemberStatusHistoryEntry
import br.com.alice.data.layer.models.OnboardingPhase
import br.com.alice.data.layer.models.TermType
import br.com.alice.healthcondition.client.HealthConditionService
import br.com.alice.membership.client.ContractNotReadyException
import br.com.alice.membership.client.ContractRegistry
import br.com.alice.membership.client.MemberContractTermService
import br.com.alice.membership.client.PromoCodeService
import br.com.alice.membership.client.onboarding.GasProcessService
import br.com.alice.membership.client.onboarding.HealthDeclarationForm
import br.com.alice.membership.client.onboarding.OnboardingService
import br.com.alice.membership.model.PersonRequest
import br.com.alice.membership.services.contract.ContractTermBuilder
import br.com.alice.person.client.MemberService
import br.com.alice.person.client.PersonGracePeriodService
import br.com.alice.person.client.PersonService
import br.com.alice.person.model.SUSPersonInfo
import br.com.alice.product.client.ProductService
import com.github.kittinunf.result.failure
import com.github.kittinunf.result.flatMap
import com.github.kittinunf.result.flatMapError
import com.github.kittinunf.result.map
import com.github.kittinunf.result.success
import io.ktor.http.HttpStatusCode
import kotlinx.coroutines.async
import kotlinx.coroutines.coroutineScope
import java.time.LocalDate
import java.time.LocalDateTime
import java.util.UUID
import java.util.concurrent.atomic.AtomicInteger

class BackfillController(
    private val gasProcessService: GasProcessService,
    private val personService: PersonService,
    private val memberService: MemberService,
    private val productService: ProductService,
    private val promoCodeService: PromoCodeService,
    private val contractTermBuilder: ContractTermBuilder,
    private val onboardingService: OnboardingService,
    private val contractRegistry: ContractRegistry,
    private val crmAnalyticsTracker: CrmAnalyticsTracker,
    private val memberContractTermService: MemberContractTermService,
    private val fileVaultStorage: FileVaultStorage,
    private val beneficiaryService: BeneficiaryService,
    private val personGracePeriodService: PersonGracePeriodService,
    private val healthDeclarationForm: HealthDeclarationForm,
    private val healthConditionService: HealthConditionService,
) : Controller() {

    private suspend fun withBackfillEnvironment(func: suspend () -> Response) =
        withRootServicePolicy(MEMBERSHIP_ENVIRONMENT_BACKFILL) {
            withUnauthenticatedTokenWithKey(MEMBERSHIP_ENVIRONMENT_BACKFILL) {
                func.invoke()
            }
        }

    suspend fun backfill(): Response {
        val message = "Backfill RESOURCE-NAME"
        val successCount = AtomicInteger(0)
        val errorCount = AtomicInteger(0)
        val errors = mutableMapOf<UUID, String>()

        return withUnauthenticatedTokenWithKey(MEMBERSHIP_ROOT_SERVICE_NAME) {
            mapOf(
                "backfill" to message,
                "successCount" to successCount.get(),
                "errorCount" to errorCount.get(),
                "errors" to errors
            ).toResponse()
        }
    }

    suspend fun createPersonGas(personIdRequest: PersonRequest) = withBackfillEnvironment {
        personService.findByNationalId(personIdRequest.nationalId).map {
            gasProcessService.start(it.id)
        }.foldResponse()
    }

    suspend fun forceMemberActivation(request: ForceMemberActivationRequest): Response =
        withBackfillEnvironment {
            var errorsCount = 0
            var successCount = 0
            val errorInfo = mutableMapOf<String, String?>()

            request.ids.pmap { id ->
                memberService.get(id)
                    .flatMap { memberService.activateMember(it, request.activationDate) }.fold(
                        { successCount++ },
                        {
                            errorsCount++
                            errorInfo.put(it.toString(), it.message)
                        }
                    )
            }

            Response(
                HttpStatusCode.OK,
                BackfillResponse(
                    successCount,
                    errorsCount,
                    errorInfo = errorInfo,
                )
            )
        }

    suspend fun backFillMGMWithAccent(request: BackFillMGMResquest): Response = withBackfillEnvironment {
        var errorsCount = 0
        var successCount = 0
        val errorInfo = mutableMapOf<String, String?>()

        val promoCodes = promoCodeService.findWithAccent(
            IntRange(request.offset, request.limit + request.offset - 1),
            request.value
        ).get()

        promoCodes.forEach { promoCode ->
            promoCodeService.update(promoCode.copy(code = RangeUUID.generate().toString().take(6).uppercase()))
                .fold(
                    { successCount++ },
                    {
                        errorsCount++
                        errorInfo.put(promoCode.id.toString(), it.message)
                    }
                )
        }

        Response(
            HttpStatusCode.OK,
            BackfillResponse(
                successCount,
                errorsCount,
                errorInfo = errorInfo,
            )
        )
    }

    suspend fun updateMemberContract(request: PersonRequest): Response = withBackfillEnvironment {
        personService.findByNationalId(request.nationalId)
            .flatMap {
                val onboardingPhase = onboardingService.findByPerson(it.id).getOrNullIfNotFound()?.currentPhase

                if (onboardingPhase != OnboardingPhase.CONTRACT) {
                    logger.info("Person is not on CONTRACT phase. Ignoring event")
                    ContractNotReadyException().failure()
                } else {
                    contractTermBuilder.generateDocument(it)
                }
            }
            .flatMap { contractRegistry.updateDocument(it.person.id, it.documentUrl) }
            .map { UpdateMemberContractResponse(it.documentUrl) }
            .foldResponse()
    }

    suspend fun updatePersonFromSusInfo(request: UpdatePersonFromSus): Response = withBackfillEnvironment {
        personService.findByNationalId(request.nationalId)
            .flatMap { personService.validateAndUpdatePersonFromSUS(it.id, request.susPersonInfo) }
            .foldResponse()
    }

    suspend fun cancelMembership(request: CancelMembershipByIdRequest): Response = withBackfillEnvironment {
        if (request.sendEvent) {
            memberService.cancelById(request.memberId)
                .foldResponse()
        } else {
            logger.info("Only update the membership status to cancelled")
            memberService.get(request.memberId)
                .flatMap {
                    memberService.update(it.cancel(request.canceledAt?.atEndOfTheDay() ?: LocalDateTime.now()), false)
                }.foldResponse()
        }

    }

    suspend fun archivePersonOnboarding(request: IdRequest): Response = withBackfillEnvironment {
        onboardingService.archiveById(request.id)
            .foldResponse()
    }

    suspend fun makePending(request: MakePendingMembershipByIdsRequest): Response = withBackfillEnvironment {
        logger.info("BackfillController::makePending", "member_ids" to request.memberIds)

        memberService.findByIds(request.memberIds).pmapEach { member ->
            val now = LocalDateTime.now()
            val pendingStatusHistory = MemberStatusHistoryEntry(MemberStatus.PENDING, now.toString())
            val statusHistory = member.statusHistory?.plus(pendingStatusHistory) ?: listOf(pendingStatusHistory)
            val pendingMember =
                member.copy(status = MemberStatus.PENDING, activationDate = null, statusHistory = statusHistory)
            memberService.update(pendingMember)
                .then {
                    logger.info("MemberWasUpdated::expireToken")
                    personService.terminateSession(it.personId)
                        .coFoldNotFound {
                            false.success()
                        }.thenError {
                            logger.error("MemberWasPendingConsumer::expireToken: error to expire member token", it)
                        }
                }
        }.toResponse()
    }

    suspend fun changeSelectedProductOfPendingMember(request: BackfillMemberProduct): Response =
        withBackfillEnvironment {
            memberService.get(request.memberId)
                .flatMap { if (it.isPending) it.success() else Exception("Não utilizar o backfill com o membro em estado diferente de PENDING").failure() }
                .flatMapPair { productService.getProduct(request.productId) }
                .flatMap { (product, member) ->
                    val currentProductPriceListing =
                        productService.getCurrentProductPriceListing(product.id).getOrNullIfNotFound()
                    memberService.update(
                        member.copy(
                            selectedProduct = MemberProduct(
                                product.id,
                                product.prices,
                                product.type,
                                product.priceListing,
                                currentProductPriceListing?.id,
                            )
                        )
                    )
                }
                .foldResponse()
        }

    suspend fun sendProductAttributesToBraze(request: SendProductAttributesToBrazeRequest): Response =
        withBackfillEnvironment {
            val successCount = AtomicInteger(0)
            val errorsCount = AtomicInteger(0)
            val errors = mutableMapOf<String, String>()

            personService.findByNationalIds(request.nationalIds).mapEach { person ->
                if (person.productInfo == null) {
                    errors[person.nationalId] = "person do not have productInfo information"
                    errorsCount.getAndIncrement()
                } else {
                    crmAnalyticsTracker.createOrUpdateUserProfile(
                        person.nationalId, UserProfile(
                            copaymentType = person.productInfo?.coPayment?.name,
                            refundType = person.productInfo?.refund?.name
                        )
                    ).also {
                        if (it.success) {
                            successCount.getAndIncrement()
                        } else {
                            errors[person.nationalId] = it.message ?: "ERROR"
                            errorsCount.getAndIncrement()
                        }
                    }
                }
            }

            Response(
                HttpStatusCode.OK,
                BackfillResponse(
                    successCount = successCount.get(),
                    errorsCount = errorsCount.get(),
                    errorInfo = mapOf("errors" to errors)
                )
            )
        }

    suspend fun updateStatusHistory(request: MemberUpdateStatusHistoryRequest): Response =
        withBackfillEnvironment {
            val successCount = AtomicInteger(0)
            val errorsCount = AtomicInteger(0)
            val errors = mutableMapOf<String, String>()

            memberService.findByIds(request.ids).pmapEach {
                val member = it.copy(
                    statusHistory = it.statusHistory?.plus(
                        MemberStatusHistoryEntry(request.status, LocalDateTime.now().toString())
                    )
                )

                memberService.update(member)
                    .then { successCount.getAndIncrement() }
                    .thenError { errorsCount.getAndIncrement() }
                    .flatMapError { ex ->
                        errors[member.id.toString()] = ex.message ?: "ERROR"
                        true.success()
                    }
            }

            Response(
                HttpStatusCode.OK,
                BackfillResponse(
                    successCount = successCount.get(),
                    errorsCount = errorsCount.get(),
                    errorInfo = mapOf("errors" to errors)
                )
            )
        }

    suspend fun removeCanceledAt(request: MemberRemoveCanceledAtRequest): Response =
        withBackfillEnvironment {
            val successCount = AtomicInteger(0)
            val errorsCount = AtomicInteger(0)
            val errors = mutableMapOf<String, String>()

            memberService.findByIds(request.ids).pmapEach {
                val member = it.copy(canceledAt = null)

                memberService.update(member)
                    .then { successCount.getAndIncrement() }
                    .thenError { errorsCount.getAndIncrement() }
                    .flatMapError { ex ->
                        errors[member.id.toString()] = ex.message ?: "ERROR"
                        true.success()
                    }
            }

            Response(
                HttpStatusCode.OK,
                BackfillResponse(
                    successCount = successCount.get(),
                    errorsCount = errorsCount.get(),
                    errorInfo = mapOf("errors" to errors)
                )
            )
        }

    suspend fun updateMembershipActiviationDate(request: UpdateMembershipActivationDateRequest): Response =
        withBackfillEnvironment {
            val successCount = AtomicInteger(0)
            val errorsCount = AtomicInteger(0)
            val errors = mutableMapOf<String, String>()

            memberService.findByIds(request.ids).pmapEach { member ->
                val member = member.copy(
                    activationDate = request.newActivationDate,
                    statusHistory = member.statusHistory?.filterNot { it.status == MemberStatus.ACTIVE }?.plus(
                        MemberStatusHistoryEntry(MemberStatus.ACTIVE, request.newActivationDate.toString())
                    )
                )

                memberService.update(member)
                    .then { successCount.getAndIncrement() }
                    .thenError { errorsCount.getAndIncrement() }
                    .flatMapError { ex ->
                        errors[member.id.toString()] = ex.message ?: "ERROR"
                        true.success()
                    }
            }

            Response(
                HttpStatusCode.OK,
                BackfillResponse(
                    successCount = successCount.get(),
                    errorsCount = errorsCount.get(),
                    errorInfo = mapOf("errors" to errors)
                )
            )
        }

    suspend fun removeMembershipActivationDate(request: RemoveMembershipActivationDateRequest): Response =
        withBackfillEnvironment {
            val successCount = AtomicInteger(0)
            val errorsCount = AtomicInteger(0)
            val errors = mutableMapOf<String, String>()

            memberService.findByIds(request.ids).pmapEach { member ->
                val updatedMember = member.copy(
                    activationDate = null,
                    statusHistory = member.statusHistory?.filter { it.status != MemberStatus.ACTIVE }
                )

                memberService.update(updatedMember)
                    .then { successCount.getAndIncrement() }
                    .thenError { errorsCount.getAndIncrement() }
                    .flatMapError { ex ->
                        errors[member.id.toString()] = ex.message ?: "ERROR"
                        true.success()
                    }
            }

            Response(
                HttpStatusCode.OK,
                BackfillResponse(
                    successCount = successCount.get(),
                    errorsCount = errorsCount.get(),
                    errorInfo = mapOf("errors" to errors)
                )
            )
        }

    suspend fun updateBeneficiarySubcontractId(request: UpdateSubContractIdRequest): Response =
        withBackfillEnvironment {
            val successCount = AtomicInteger(0)
            val errorsCount = AtomicInteger(0)
            val errors = mutableMapOf<String, String>()

            beneficiaryService.findByIds(request.ids).pmapEach { beneficiary ->
                val beneficiary = beneficiary.copy(
                    companySubContractId = request.subContractId
                )

                beneficiaryService.update(beneficiary)
                    .then { successCount.getAndIncrement() }
                    .thenError { errorsCount.getAndIncrement() }
                    .flatMapError { ex ->
                        errors[beneficiary.id.toString()] = ex.message ?: "ERROR"
                        true.success()
                    }
            }

            Response(
                HttpStatusCode.OK,
                BackfillResponse(
                    successCount = successCount.get(),
                    errorsCount = errorsCount.get(),
                    errorInfo = mapOf("errors" to errors)
                )
            )
        }

    suspend fun removeMemberActivationDate(request: RemoveMemberActivationDateRequest): Response =
        withBackfillEnvironment {
            val successCount = AtomicInteger(0)
            val errorsCount = AtomicInteger(0)
            val errors = mutableMapOf<String, String>()
            val membershipsToUpdated = mutableListOf<Member>()

            memberService.findByCreatedAtPeriodAndStatusWithActivationDate(
                createdAtStart = request.createdAtStart,
                createdAtEnd = request.createdAtEnd,
                statusParam = listOf(MemberStatus.PENDING, MemberStatus.CANCELED)
            ).flatMap { members ->

                members.forEach { member ->

                    val statusHistory = member.statusHistory!!.map { it.status }

                    if (member.status == MemberStatus.PENDING && statusHistory.size == 1 && statusHistory.first() == MemberStatus.PENDING) {
                        membershipsToUpdated.add(member)
                    }

                    if (member.status == MemberStatus.CANCELED && statusHistory.any { it == MemberStatus.PENDING || it == MemberStatus.CANCELED } &&
                        statusHistory.none { it == MemberStatus.ACTIVE }) {
                        membershipsToUpdated.add(member)
                    }

                }

                members.success()
            }

            membershipsToUpdated.success().pmapEach { member ->
                memberService.update(member.copy(activationDate = null))
                    .then { successCount.getAndIncrement() }
                    .thenError { errorsCount.getAndIncrement() }
                    .flatMapError { ex ->
                        errors[member.id.toString()] = ex.message ?: "ERROR"
                        true.success()
                    }
            }

            Response(
                HttpStatusCode.OK,
                BackfillResponse(
                    successCount = successCount.get(),
                    errorsCount = errorsCount.get(),
                    errorInfo = mapOf("errors" to errors)
                )
            )
        }

    suspend fun getMemberContractTerm(personId: String) = withBackfillEnvironment {
        memberService.findByPerson(personId.toPersonId())
            .then {
                logger.info("Memberships found", it.map { member -> mapOf("id" to member.id) })
            }
            .thenError { logger.info("Something wrong happened when trying to get the person", it) }
            .pmapEach { member ->
                memberContractTermService.findByMemberIdAndTermType(member.id, TermType.HEALTH_DECLARATION)
                    .thenError { logger.info("Something wrong happened when trying to get the term", it) }
                    .map {
                        if (it.isEmpty()) {
                            null
                        } else {
                            val term = it.first()

                            HealthDeclarationLink(
                                not_assigned = fileVaultStorage.getFileUrl(term.documentFileId.toString()) + "/redirect",
                                assigned = fileVaultStorage.getFileUrl(term.signedDocumentFileId.toString()) + "/redirect",
                            )
                        }
                    }
            }.map { links -> links.map { it.get() } }
            .toResponse()
    }

    suspend fun updateMembershipCanceledAt(request: UpdateMembershipCanceledAtRequest): Response =
        withBackfillEnvironment {
            val successCount = AtomicInteger(0)
            val errorsCount = AtomicInteger(0)
            val errors = mutableMapOf<String, String>()

            memberService.findByIds(request.ids).pmapEach { member ->
                if (member.status != MemberStatus.CANCELED) {
                    errorsCount.getAndIncrement()
                    errors[member.id.toString()] = "Member is not canceled"
                    return@pmapEach
                }
                val updatedMember = member.copy(
                    canceledAt = request.newCanceledAt,
                    statusHistory = member.statusHistory?.filterNot { it.status == MemberStatus.CANCELED }?.plus(
                        MemberStatusHistoryEntry(MemberStatus.CANCELED, request.newCanceledAt.toString())
                    )
                )

                memberService.update(updatedMember)
                    .then { successCount.getAndIncrement() }
                    .thenError { errorsCount.getAndIncrement() }
                    .flatMapError { ex ->
                        errors[updatedMember.id.toString()] = ex.message ?: "ERROR"
                        true.success()
                    }
            }

            Response(
                HttpStatusCode.OK,
                BackfillResponse(
                    successCount = successCount.get(),
                    errorsCount = errorsCount.get(),
                    errorInfo = mapOf("errors" to errors)
                )
            )
        }

    suspend fun clearParentMember(request: MemberToClearParentRequest): Response = withBackfillEnvironment {
        val successCount = AtomicInteger(0)
        val errorsCount = AtomicInteger(0)
        val errors = mutableMapOf<String, String>()

        memberService.findByIds(request.ids).pmapEach { member ->

            val updatedMember = member.copy(
                parentPerson = null,
                parentMember = null,
            )

            memberService.update(updatedMember)
                .then { successCount.getAndIncrement() }
                .thenError { errorsCount.getAndIncrement() }
                .flatMapError { ex ->
                    errors[updatedMember.id.toString()] = ex.message ?: "ERROR"
                    true.success()
                }
        }


        Response(
            HttpStatusCode.OK,
            BackfillResponse(
                successCount = successCount.get(),
                errorsCount = errorsCount.get(),
                errorInfo = mapOf("errors" to errors)
            )
        )
    }

    suspend fun setCanceledAtToMember(request: SetCanceledAtToMemberRequest): Response = withBackfillEnvironment {
        logger.info("BackfillController::setCanceledAtToMember")

        val successCount = AtomicInteger(0)
        val errorsCount = AtomicInteger(0)
        val errors = mutableMapOf<String, String>()

        memberService.findByCanceledWithoutCanceledAtDatetimeFromCreatedAt(
            createdAtStart = LocalDateTime.of(2020, 7, 31, 0, 0, 0),
            request.limit
        ).pmapEach { member ->

            val canceledAt =
                member.statusHistory!!.first { it.status == MemberStatus.CANCELED }.createdAt.toLocalDateTime()

            memberService.update(member.copy(canceledAt = canceledAt))
                .then { successCount.getAndIncrement() }
                .thenError { errorsCount.getAndIncrement() }
                .flatMapError { ex ->
                    errors[member.id.toString()] = ex.message ?: "ERROR"
                    true.success()
                }

            member
        }

        Response(
            HttpStatusCode.OK,
            BackfillResponse(
                successCount = successCount.get(),
                errorsCount = errorsCount.get(),
                errorInfo = mapOf("errors" to errors)
            )
        )
    }

    suspend fun copyCPTsToPersonGracePeriodToHealthDeclaration(request: CopyCPTsToPersonGracePeriodToHealthDeclarationRequest): Response =
        withBackfillEnvironment {
            coroutineScope {
                val personId = request.personId.toPersonId()
                val personGracePeriodListDeferred =
                    async { personGracePeriodService.findByPersonId(personId).get() }
                val healthDeclarationDeferred = async { healthDeclarationForm.findByPerson(personId).get() }

                val personGracePeriodList = personGracePeriodListDeferred.await()
                val healthDeclaration = healthDeclarationDeferred.await()

                val healthConditions = healthConditionService
                    .findByCodesAndType(
                        codes = personGracePeriodList.map { it.value },
                        type = HealthConditionCodeType.CID_10
                    )
                    .get()

                val cpts = healthConditions.map { Cpt(condition = it.name, cids = listOf(it.code!!)) }

                healthDeclarationForm.updateCpts(healthDeclaration, cpts, false)
                    .fold({ true }, { it.message ?: it.toString() })
                    .toResponse()
            }
        }
}

data class CopyCPTsToPersonGracePeriodToHealthDeclarationRequest(
    val personId: UUID,
)

data class SetCanceledAtToMemberRequest(val limit: Int = 20)

data class MemberToClearParentRequest(
    val ids: List<UUID>
)

data class MemberUpdateStatusHistoryRequest(
    val status: MemberStatus,
    val ids: List<UUID>,
)

data class MemberRemoveCanceledAtRequest(
    val ids: List<UUID>,
)

data class UpdateMemberContractResponse(val documentUrl: String)

data class UpdatePersonFromSus(val nationalId: String, val susPersonInfo: SUSPersonInfo)

data class CancelMembershipByIdRequest(
    val memberId: UUID,
    val sendEvent: Boolean = true,
    val canceledAt: LocalDate? = null,
)

data class MakePendingMembershipByIdsRequest(val memberIds: List<UUID>)

data class BackFillMGMResquest(val offset: Int, val limit: Int, val value: String)

data class BackfillMemberProduct(val memberId: UUID, val productId: UUID)

data class SendProductAttributesToBrazeRequest(val nationalIds: List<String>)

data class BackfillResponse(
    val successCount: Int,
    val errorsCount: Int,
    val errorInfo: Map<String, Any?>? = null,
)

data class ForceMemberActivationRequest(
    val activationDate: LocalDateTime? = LocalDateTime.now(),
    val ids: List<UUID>,
)

data class UpdateMembershipActivationDateRequest(
    val newActivationDate: LocalDateTime,
    val ids: List<UUID>
)

data class RemoveMembershipActivationDateRequest(
    val ids: List<UUID>
)

data class IdRequest(
    val id: UUID
)

data class UpdateMembershipCanceledAtRequest(
    val newCanceledAt: LocalDateTime,
    val ids: List<UUID>
)

data class UpdateSubContractIdRequest(
    val subContractId: UUID,
    val ids: List<UUID>
)

data class RemoveMemberActivationDateRequest(
    val createdAtStart: LocalDateTime,
    val createdAtEnd: LocalDateTime,
)

data class HealthDeclarationLink(val not_assigned: String? = null, val assigned: String? = null)
