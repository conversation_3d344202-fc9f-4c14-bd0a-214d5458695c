package br.com.alice.dbintegrationservice.services

import br.com.alice.common.core.exceptions.DuplicatedItemException
import br.com.alice.common.extensions.coFlatMapError
import br.com.alice.common.extensions.mapPair
import br.com.alice.common.extensions.then
import br.com.alice.common.kafka.interfaces.KafkaProducerService
import br.com.alice.common.logging.logger
import br.com.alice.data.layer.events.DbAttendanceResult
import br.com.alice.data.layer.events.DbLaboratoryTestResultProcessCreatedEvent
import br.com.alice.data.layer.events.FailedProcedure
import br.com.alice.data.layer.events.FailureReason
import br.com.alice.data.layer.models.DbLaboratoryTestResultProcess
import br.com.alice.data.layer.models.Person
import br.com.alice.data.layer.services.DbLaboratoryTestResultProcessDataService
import br.com.alice.dbintegrationclient.exceptions.ProceduresEmptyReceberAtendimentoRequestException
import br.com.alice.dbintegrationservice.adapters.AttendanceAdapter
import br.com.alice.dbintegrationservice.adapters.ClaimRequestAdapter
import br.com.alice.dbintegrationservice.clients.DBClient
import br.com.alice.dbintegrationservice.metrics.RecebeAtendimentoMetrics
import br.com.alice.dbintegrationservice.models.Attendance
import br.com.alice.dbintegrationservice.models.ClaimRequestResponse
import br.com.alice.dbintegrationservice.models.DbLabProcedureRequest
import br.com.alice.person.client.PersonService
import com.github.kittinunf.result.Result
import com.github.kittinunf.result.failure
import com.github.kittinunf.result.flatMap
import com.github.kittinunf.result.success
import kotlinx.coroutines.async
import kotlinx.coroutines.coroutineScope

class DbClaimService(
    private val dbLaboratoryTestResultProcessDataService: DbLaboratoryTestResultProcessDataService,
    private val dbClient: DBClient,
    private val personService: PersonService,
    private val testCodeService: TestCodeService,
    private val kafkaService: KafkaProducerService,
) {
    suspend fun processAttendance(attendance: Attendance): Result<DbAttendanceResult, Throwable> {
        return coroutineScope {
            val proceduresDeferred = async { testCodeService.getDbLabProcedures(attendance) }
            val personDeferred = async { personService.get(attendance.personId) }
            val dbProcedures = proceduresDeferred.await().get()
            val person = personDeferred.await().get()

            sendAndStoreAttendance(attendance, dbProcedures, person)
                .coFlatMapError { exception ->
                    if (exception is ProceduresEmptyReceberAtendimentoRequestException) {
                        logger.error(
                            "ProceduresEmpty ReceberAtendimento",
                            "attendance_id" to attendance.id,
                            "attendance" to attendance,
                            "reason" to "procedures_empty",
                        )
                        repeat(attendance.procedures.size) {
                            RecebeAtendimentoMetrics.incrementRecebeAtendimentoFailure(FailureReason.PROCEDURE_NOT_FOUND)
                        }
                        AttendanceAdapter.attendanceToErrorDbAttendanceResult(
                            attendance,
                            attendance.procedures.map {
                                AttendanceAdapter.buildFailedProceduresNotFound(it.aliceCode)
                            }).success()
                    } else {
                        exception.failure()
                    }
                }.then {
                    publishDbLabTestResultProcess(it)
                }
        }
    }

    suspend fun reprocessAttendance(attendance: Attendance): Result<DbAttendanceResult, Throwable> {
        return coroutineScope {
            val proceduresDeferred = async { testCodeService.getDbLabProcedures(attendance) }
            val personDeferred = async { personService.get(attendance.personId) }
            val dbProcedures = proceduresDeferred.await().get()
            val person = personDeferred.await().get()

            sendAndStoreAttendance(attendance, dbProcedures, person)
                .then {
                    logger.info(
                        "reprocess Attendance response",
                        "attendance_id" to it.attendanceId,
                        "person_id" to it.personId,
                        "response" to it
                    )
                }
        }
    }


    suspend fun consultaTagsAtDB(attendanceId: String): Result<List<String>, Throwable> =
        dbClient.consultaTagsAtendimento(attendanceId)

    private suspend fun sendAndStoreAttendance(
        attendance: Attendance,
        dbLabProcedures: List<DbLabProcedureRequest>,
        person: Person,
    ): Result<DbAttendanceResult, Throwable> =
        sendClaimRequestToDb(attendance, dbLabProcedures, person)
            .mapPair { AttendanceAdapter.attendanceToDbLabTestResultProcess(attendance, it.tags) }
            .flatMap {
                val process = it.first
                val errors = converterErrorDbToErrorAlice(dbLabProcedures, it.second.failedProcedures)
                val successes = converterCodeDbToCodeAlice(dbLabProcedures, it.second.codesSuccessful)

                process.failedProcedures.addAll(errors)

                addCodesNotFoundInFailureList(dbLabProcedures, attendance, process)
                saveTestResultProcess(process)
                AttendanceAdapter.responseToDbAttendanceResult(process, successes).success()
            }

    private fun addCodesNotFoundInFailureList(
        dbProcedures: List<DbLabProcedureRequest>,
        attendance: Attendance,
        process: DbLaboratoryTestResultProcess
    ) {
        val codesFound = dbProcedures.map { it.aliceCode }
        val codesNotFound = attendance.procedures.map { it.aliceCode }.filter { procedureId ->
            !codesFound.contains(procedureId)
        }

        if (codesNotFound.isNotEmpty()) {
            logger.warn(
                "Request has at least one procedureId not found.",
                "codes_found" to codesFound,
                "codes_not_found" to codesNotFound,
                "db_lab_procedure_code" to dbProcedures.map { it.code },
                "alice_procedure" to attendance.procedures.map { it.aliceCode },
                "attendance_id" to attendance.id
            )
            process.failedProcedures
                .addAll(codesNotFound.map { AttendanceAdapter.buildFailedProceduresNotFound(it) })
        }

        repeat(codesNotFound.size) {
            RecebeAtendimentoMetrics.incrementRecebeAtendimentoFailure(FailureReason.PROCEDURE_NOT_FOUND)
        }
    }

    private fun converterCodeDbToCodeAlice(request: List<DbLabProcedureRequest>, codesDB: List<String>): List<String> {
        val mapCode = request.associateBy({ it.code }, { it.aliceCode })
        return codesDB.map {
            mapCode[it]!!
        }
    }

    private fun converterErrorDbToErrorAlice(
        request: List<DbLabProcedureRequest>,
        response: List<FailedProcedure>,
    ): List<FailedProcedure> {
        val mapCode = request.associateBy({ it.code }, { it.aliceCode })
        return response.map {
            it.copy(code = mapCode[it.code]!!)
        }
    }

    private suspend fun sendClaimRequestToDb(
        attendance: Attendance,
        dbLabProcedures: List<DbLabProcedureRequest>,
        person: Person,
    ): Result<ClaimRequestResponse, Throwable> {
        logger.info(
            "Sending attendance request to DB",
            "attendance_id" to attendance.id,
            "person_id" to attendance.personId,
            "db_procedures" to dbLabProcedures
        )
        if (dbLabProcedures.isEmpty())
            return ProceduresEmptyReceberAtendimentoRequestException().failure()

        return ClaimRequestAdapter.personToRequestPerson(person)
            .success()
            .then {
                logger.info(
                    "Sending Attendance to laboratory",
                    "person_id" to attendance.personId,
                    "db_lab_procedure_ids" to dbLabProcedures,
                    "attendance_id" to attendance.id
                )
            }.flatMap {
                dbClient.requestLaboratoryTest(
                    it,
                    testCodeService.excludeDuplicatedItens(dbLabProcedures),
                    attendance.id
                )
            }
    }

    private suspend fun saveTestResultProcess(
        dbTestResultProcess: DbLaboratoryTestResultProcess,
    ): Result<DbLaboratoryTestResultProcess, Throwable> =
        dbLaboratoryTestResultProcessDataService.add(dbTestResultProcess).fold(
            { addedDbTestResultProcess ->
                logger.info(
                    "DbLaboratoryTestResultProcess successfully added",
                    "db_test_result_process" to addedDbTestResultProcess,
                    "operation" to "register_process",
                    "result" to "success"
                )
                addedDbTestResultProcess.success()
            },
            {
                if (it is DuplicatedItemException) {
                    logger.info(
                        "Duplicated DbLaboratoryTestResultProcess",
                        "db_test_result_process" to dbTestResultProcess,
                        "operation" to "register_process",
                        "result" to "failure",
                        "reason" to "duplicated"
                    )
                    dbTestResultProcess.success()
                } else {
                    logger.error(
                        "Error DbClaimService.handle adding LaboratoryTestResultProcess",
                        "db_test_result_process" to dbTestResultProcess,
                        "operation" to "register_process",
                        "result" to "failure",
                        "reason" to "exception",
                        "exception" to it
                    )
                    it.failure()
                }
            }
        )

    private suspend fun publishDbLabTestResultProcess(
        dbAttendanceResult: DbAttendanceResult,
    ) {
        logger.info(
            "Publishing DbLabTestResultProcess",
            "db_attendance" to dbAttendanceResult,
            "attendance_id" to dbAttendanceResult.attendanceId,
            "person_id" to dbAttendanceResult.personId
        )

        kafkaService.produce(DbLaboratoryTestResultProcessCreatedEvent(dbAttendanceResult))
    }
}
