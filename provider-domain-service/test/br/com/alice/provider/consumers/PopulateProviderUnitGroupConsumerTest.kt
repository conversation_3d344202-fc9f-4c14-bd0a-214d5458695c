package br.com.alice.provider.consumers

import br.com.alice.common.core.exceptions.NotFoundException
import br.com.alice.common.data.dsl.matchers.ResultAssert.Companion.assertThat
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.provider.client.ProviderUnitGroupService
import br.com.alice.provider.client.ProviderUnitService
import br.com.alice.provider.model.ProviderUnitCreatedEvent
import br.com.alice.provider.model.ProviderUnitUpdatedEvent
import com.github.kittinunf.result.failure
import com.github.kittinunf.result.success
import io.mockk.clearAllMocks
import io.mockk.coEvery
import io.mockk.mockk
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.TestInstance
import kotlin.test.AfterTest
import kotlin.test.Test

@TestInstance(TestInstance.Lifecycle.PER_CLASS)
class PopulateProviderUnitGroupConsumerTest : ConsumerTest() {

    @AfterTest
    fun setup() = clearAllMocks()

    private val providerUnitService = mockk<ProviderUnitService>()
    private val providerUnitGroupService = mockk<ProviderUnitGroupService>()

    private val consumer = PopulateProviderUnitGroupConsumer(providerUnitGroupService, providerUnitService)

    @Test
    fun `#populateProviderUnitGroupByCreate should populate provider unit group with existing provider unit group`() = runBlocking {
        val providerUnit = TestModelFactory.buildProviderUnit(cnpj = "123")
        val providerUnitGroup = TestModelFactory.buildProviderUnitGroup()
        val event = ProviderUnitCreatedEvent(providerUnit)

        coEvery { providerUnitService.get(providerUnit.id) } returns providerUnit.success()
        coEvery { providerUnitGroupService.getByCnpj(providerUnit.cnpj!!) } returns providerUnitGroup.success()
        coEvery {
            providerUnitService.update(
                providerUnit.copy(providerUnitGroupId = providerUnitGroup.id),
                false
            )
        } returns providerUnit.success()

        val result = consumer.populateProviderUnitGroupByCreate(event)
        assertThat(result).isSuccess()

        coVerifyOnce { providerUnitGroupService.getByCnpj(providerUnit.cnpj!!) }
        coVerifyOnce { providerUnitService.update(any(), false) }
    }

    @Test
    fun `#populateProviderUnitGroupByCreate should populate provider unit group with new provider unit group if there is no group for cnpj`() = runBlocking {
        val providerUnit = TestModelFactory.buildProviderUnit(cnpj = "123")
        val providerUnitGroup = TestModelFactory.buildProviderUnitGroup()
        val event = ProviderUnitCreatedEvent(providerUnit)

        coEvery { providerUnitService.get(providerUnit.id) } returns providerUnit.success()
        coEvery { providerUnitGroupService.getByCnpj(providerUnit.cnpj!!) } returns NotFoundException("").failure()
        coEvery { providerUnitGroupService.add(match {
            it.cnpj == "123" &&
                    it.title == providerUnit.name
        }) } returns providerUnitGroup.success()
        coEvery {
            providerUnitService.update(
                providerUnit.copy(providerUnitGroupId = providerUnitGroup.id),
                false
            )
        } returns providerUnit.success()

        val result = consumer.populateProviderUnitGroupByCreate(event)
        assertThat(result).isSuccess()

        coVerifyOnce { providerUnitGroupService.getByCnpj(providerUnit.cnpj!!) }
        coVerifyOnce { providerUnitGroupService.add(any()) }
        coVerifyOnce { providerUnitService.update(any(), false) }
    }

    @Test
    fun `#populateProviderUnitGroupByUpdate should populate provider unit group with existing provider unit group`() = runBlocking {
        val providerUnit = TestModelFactory.buildProviderUnit(cnpj = "123")
        val providerUnitGroup = TestModelFactory.buildProviderUnitGroup()
        val event = ProviderUnitUpdatedEvent(providerUnit)

        coEvery { providerUnitService.get(providerUnit.id) } returns providerUnit.success()
        coEvery { providerUnitGroupService.getByCnpj(providerUnit.cnpj!!) } returns providerUnitGroup.success()
        coEvery {
            providerUnitService.update(
                providerUnit.copy(providerUnitGroupId = providerUnitGroup.id),
                false
            )
        } returns providerUnit.success()

        val result = consumer.populateProviderUnitGroupByUpdate(event)
        assertThat(result).isSuccess()

        coVerifyOnce { providerUnitGroupService.getByCnpj(providerUnit.cnpj!!) }
        coVerifyOnce { providerUnitService.update(any(), false) }
    }

    @Test
    fun `#populateProviderUnitGroupByUpdate should populate provider unit group with new provider unit group if there is no group for cnpj`() = runBlocking {
        val providerUnit = TestModelFactory.buildProviderUnit(cnpj = "123")
        val providerUnitGroup = TestModelFactory.buildProviderUnitGroup()
        val event = ProviderUnitUpdatedEvent(providerUnit)

        coEvery { providerUnitService.get(providerUnit.id) } returns providerUnit.success()
        coEvery { providerUnitGroupService.getByCnpj(providerUnit.cnpj!!) } returns NotFoundException("").failure()
        coEvery { providerUnitGroupService.add(match {
            it.cnpj == "123" &&
                    it.title == providerUnit.name
        }) } returns providerUnitGroup.success()
        coEvery {
            providerUnitService.update(
                providerUnit.copy(providerUnitGroupId = providerUnitGroup.id),
                false
            )
        } returns providerUnit.success()

        val result = consumer.populateProviderUnitGroupByUpdate(event)
        assertThat(result).isSuccess()

        coVerifyOnce { providerUnitGroupService.getByCnpj(providerUnit.cnpj!!) }
        coVerifyOnce { providerUnitGroupService.add(any()) }
        coVerifyOnce { providerUnitService.update(any(), false) }
    }

    @Test
    fun `#populateProviderUnitGroupByUpdate fails`() = runBlocking {
        val providerUnit = TestModelFactory.buildProviderUnit(cnpj = "123")
        val providerUnitGroup = TestModelFactory.buildProviderUnitGroup()
        val event = ProviderUnitUpdatedEvent(providerUnit)

        coEvery { providerUnitService.get(providerUnit.id) } returns providerUnit.success()
        coEvery { providerUnitGroupService.getByCnpj(providerUnit.cnpj!!) } returns NotFoundException("").failure()
        coEvery { providerUnitGroupService.add(match {
            it.cnpj == "123" &&
                    it.title == providerUnit.name
        }) } returns providerUnitGroup.success()
        coEvery {
            providerUnitService.update(
                providerUnit.copy(providerUnitGroupId = providerUnitGroup.id),
                false
            )
        } returns Exception("").failure()

        val result = consumer.populateProviderUnitGroupByUpdate(event)
        assertThat(result).isFailure()

        coVerifyOnce { providerUnitGroupService.getByCnpj(providerUnit.cnpj!!) }
        coVerifyOnce { providerUnitGroupService.add(any()) }
        coVerifyOnce { providerUnitService.update(any(), false) }
    }
}
