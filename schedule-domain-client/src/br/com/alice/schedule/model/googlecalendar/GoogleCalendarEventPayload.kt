package br.com.alice.schedule.model.googlecalendar

import br.com.alice.common.core.PersonId
import br.com.alice.data.layer.models.ExternalCalendarEvent
import br.com.alice.data.layer.models.ExternalCalendarRecurrentEvent
import br.com.alice.data.layer.models.ExternalEventResponseStatus
import br.com.alice.data.layer.models.ExternalEventStatus
import br.com.alice.data.layer.models.ExternalEventTransparency
import java.time.LocalDateTime
import java.util.UUID

data class GoogleCalendarEventPayload(
    val status: String,
    val startTime: LocalDateTime,
    val endTime: LocalDateTime,
    val id: String,
    val externalUpdatedAt: LocalDateTime,
    val recurrence: List<String>? = emptyList(),
    val recurringEventId: String? = null,
    val personId: PersonId? = null,
    val appointmentScheduleId: UUID? = null,
    val transparency: ExternalEventTransparency,
    val staffScheduleId: UUID? = null,
    val summary: String? = null,
    val responseStatus: ExternalEventResponseStatus? = ExternalEventResponseStatus.ACCEPTED,
    val googleMeetLink: String? = null
) {
    companion object {

        fun fromExternalCalendarEvent(
            externalCalendarEvent: ExternalCalendarEvent,
            recurringEventId: String? = null,
        ): GoogleCalendarEventPayload =
            GoogleCalendarEventPayload(
                status = getGoogleStatusFromEventStatus(externalCalendarEvent.status),
                startTime = externalCalendarEvent.startTime,
                endTime = externalCalendarEvent.endTime,
                id = externalCalendarEvent.externalEventId,
                externalUpdatedAt = externalCalendarEvent.externalUpdatedAt,
                appointmentScheduleId = externalCalendarEvent.appointmentScheduleId,
                transparency = externalCalendarEvent.transparency,
                recurringEventId = recurringEventId ?: externalCalendarEvent.externalCalendarRecurrentEventId.toString()
            )

        fun fromExternalCalendarRecurrentEvent(
            externalCalendarRecurrentEvent: ExternalCalendarRecurrentEvent,
        ): GoogleCalendarEventPayload =
            GoogleCalendarEventPayload(
                status = getGoogleStatusFromEventStatus(externalCalendarRecurrentEvent.status),
                startTime = externalCalendarRecurrentEvent.firstEventStartTime ?: LocalDateTime.now(),
                // This property is ignored
                endTime = LocalDateTime.now(),
                id = externalCalendarRecurrentEvent.externalEventId,
                externalUpdatedAt = externalCalendarRecurrentEvent.externalUpdatedAt ?: LocalDateTime.now(),
                transparency = externalCalendarRecurrentEvent.transparency,
                recurrence = externalCalendarRecurrentEvent.recurrence,
            )

        private fun getGoogleStatusFromEventStatus(status: ExternalEventStatus): String =
            when (status) {
                ExternalEventStatus.CANCELLED -> ExternalEventStatus.CANCELLED
                ExternalEventStatus.CONFIRMED -> ExternalEventStatus.CONFIRMED
                ExternalEventStatus.TENTATIVE -> ExternalEventStatus.TENTATIVE
                else -> ExternalEventStatus.CONFIRMED
            }.value
    }
}
