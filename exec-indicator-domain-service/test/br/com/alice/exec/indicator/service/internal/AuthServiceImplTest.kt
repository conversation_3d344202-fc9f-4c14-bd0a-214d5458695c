package br.com.alice.exec.indicator.service.internal

import br.com.alice.authentication.Authenticator
import br.com.alice.authentication.sendSignInEmailLink
import br.com.alice.common.Brand
import br.com.alice.common.RangeUUID
import br.com.alice.common.core.Role
import br.com.alice.common.core.StaffType
import br.com.alice.common.core.exceptions.NotFoundException
import br.com.alice.common.core.extensions.toSafeUUID
import br.com.alice.common.data.dsl.matchers.ResultAssert
import br.com.alice.common.featureflag.withFeatureFlag
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.FeatureNamespace
import br.com.alice.data.layer.models.ProviderUnit
import br.com.alice.exec.indicator.client.AuthorizerService
import br.com.alice.exec.indicator.client.ExecIndicatorAuthorizerService
import br.com.alice.exec.indicator.client.MagicNumbersService
import br.com.alice.exec.indicator.models.ActiveClinicalUserResponse
import br.com.alice.exec.indicator.models.Authorizer
import br.com.alice.exec.indicator.models.EitaUserType
import br.com.alice.exec.indicator.models.SignInAuthorizersResponse
import br.com.alice.exec.indicator.service.AuthServiceImpl
import br.com.alice.provider.client.ProviderUnitService
import br.com.alice.staff.client.StaffService
import com.github.kittinunf.result.Result
import com.github.kittinunf.result.failure
import com.github.kittinunf.result.success
import com.google.firebase.auth.FirebaseAuth
import com.google.firebase.auth.FirebaseToken
import io.mockk.called
import io.mockk.clearAllMocks
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.every
import io.mockk.mockk
import io.mockk.mockkObject
import io.mockk.mockkStatic
import kotlin.test.AfterTest
import kotlin.test.BeforeTest
import kotlin.test.Test
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.TestInstance

@TestInstance(TestInstance.Lifecycle.PER_CLASS)
class AuthServiceImplTest {

    private val emailService: EmailService = mockk()
    private val staffService: StaffService = mockk()
    private val execIndicatorAuthorizerService: ExecIndicatorAuthorizerService = mockk()
    private val authorizerService: AuthorizerService = mockk()
    private val magicNumbersService: MagicNumbersService = mockk()
    private val providerUnitService: ProviderUnitService = mockk()

    private val authService: AuthServiceImpl =
        AuthServiceImpl(
            emailService,
            staffService,
            authorizerService,
            magicNumbersService,
            providerUnitService
        )

    private val staffEmail = "<EMAIL>"
    private val staffEmailDomain = "alice.com.br"
    private val memberCommunityEmail = "<EMAIL>"
    private val memberCommunityEmailDomain = "delbony.com.br"
    private val url = "https://www.alice.com.br"
    private val firebaseToken: FirebaseToken = mockk()
    private val firebaseAuth: FirebaseAuth = mockk()
    private val emailSendResponse = "successful"
    private val authorizerId = "mock-authorizer-alice".toSafeUUID()
    private val accessToken = "idToken"
    private val firebaseAccessLink = "https://www.google.com"
    private val magicNumber = TestModelFactory.buildMagicNumbers(generateTo = staffEmail, link = firebaseAccessLink)

    private val staffExaminer = TestModelFactory.buildStaff(role = Role.HEALTH_OPS_LEAD)

    private val authorizers = listOf(
        Authorizer(
            id = authorizerId,
            name = "Alice - Casa Alice",
            providerUnitId = RangeUUID.generate(),
            unitType = ProviderUnit.Type.LABORATORY,
            brand = Brand.ALICE,
        )
    )

    private val signinSuccess =
        SignInAuthorizersResponse(allow = true, authorizers = authorizers, domain = staffEmailDomain)
    private val signinFailure =
        SignInAuthorizersResponse(allow = false, authorizers = emptyList(), domain = staffEmailDomain)


    @BeforeTest
    fun setup() {

        mockkStatic(FirebaseAuth::class)

        every { FirebaseAuth.getInstance() } returns firebaseAuth
        every { firebaseAuth.createCustomToken(any(), any()) } returns "idToken"
        every { firebaseAuth.generateSignInWithEmailLink(any(), any()) } returns "verificationLink"
        every { firebaseAuth.setCustomUserClaims(any(), any()) }

        mockkObject(Authenticator)

        every { firebaseToken.uid } returns accessToken
        every { firebaseToken.isEmailVerified } returns true
        every { Authenticator.generateCustomToken(any(), any<String>()) } returns "customToken"
        every { Authenticator.verifyIdToken(any()) } returns firebaseToken
        every { Authenticator.generateRootServiceToken(any()) } returns "environmentToken"
        every { firebaseToken.claims } returns mapOf(
            Authenticator.USER_TYPE_KEY to "String", Authenticator.USER_ID_KEY to accessToken,
            Authenticator.ROOT_SERVICE_KEY to mapOf(Authenticator.ROOT_SERVICE_NAME_KEY to "scheduler-bff-api")
        )

        mockkStatic(::sendSignInEmailLink)
        every { sendSignInEmailLink(any(), any()) } returns "https://some.link"
    }

    @AfterTest
    fun afterTest() {
        clearAllMocks()
    }

    @Test
    fun `#createAuthLink should create firebase link successfully`() =
        runBlocking {
            val result = authService.createAuthLink(email = staffEmail, url = url)
            ResultAssert.assertThat(result).isSuccessWithData("https://some.link")
        }

    @Test
    fun `#createPinAuthentication should authenticate in firebase and create pin code (Sending e-mail) successfully`() =
        runBlocking {
            val sendEmail = true

            coEvery { magicNumbersService.add(any()) } returns magicNumber.success()
            coEvery {
                emailService.sendPinAuthenticationEmail(
                    email = staffEmail,
                    recipient = staffEmail,
                    pin = any(),
                    app = any()
                )
            } returns Result.success(emailSendResponse)


            val result = authService.createPinAuthentication(email = staffEmail, url = url, sendEmail = sendEmail)
            ResultAssert.assertThat(result).isSuccessWithData("OK")

            coVerifyOnce {
                sendSignInEmailLink(any(), any())
                magicNumbersService.add(any())
                emailService.sendPinAuthenticationEmail(any(), any(), any(), any())
            }
        }

    @Test
    fun `#createPinAuthentication should authenticate in firebase and create pin code (No sending e-mail) successfully`() =
        runBlocking {
            val sendEmail = false

            coEvery { magicNumbersService.add(any()) } returns magicNumber.success()

            val result = authService.createPinAuthentication(email = staffEmail, url = url, sendEmail = sendEmail)
            ResultAssert.assertThat(result).isSuccessWithData("OK")

            coVerifyOnce {
                sendSignInEmailLink(any(), any())
                magicNumbersService.add(any())
            }

            coVerify { emailService wasNot called }
        }

    @Test
    fun `#signIn should confirm authentication when user are alice staff health ops`() = runBlocking {
        val staff = TestModelFactory.buildStaff(role = Role.HEALTH_OPS_LEAD)

        every { firebaseToken.email } returns staffEmail
        coEvery { staffService.findActiveByEmail(staffEmail) } returns staff.success()
        coEvery { authorizerService.authorizersByDomain(staffEmailDomain) } returns authorizers.success()

        val result = authService.signIn(staffEmail)
        ResultAssert.assertThat(result).isSuccessWithData(signinSuccess.copy(staff = staff, userId = staff.id))

        coVerify(exactly = 2) { staffService.findActiveByEmail(any()) }
        coVerify(exactly = 1) { authorizerService.authorizersByDomain(any()) }

        coVerify { execIndicatorAuthorizerService.getByDomain(staffEmail) wasNot called }
    }

    @Test
    fun `#signIn should confirm authentication when user are health professional`() = runBlocking {
        val staff = TestModelFactory.buildStaff(
            email = staffEmail,
            role = Role.HEALTH_COMMUNITY,
            type = StaffType.COMMUNITY_SPECIALIST
        )
        val healthSpecialistSigninSuccess =
            SignInAuthorizersResponse(
                allow = true,
                authorizers = authorizers,
                domain = staffEmailDomain,
                userType = EitaUserType.HEALTH_SPECIALIST,
                userId = staff.id,
                staff = staff
            )

        every { firebaseToken.email } returns staffEmail
        coEvery { staffService.findActiveByEmail(staffEmail) } returns staff.success()
        coEvery {
            authorizerService.authorizersByEmployee(
                employeeRole = EitaUserType.HEALTH_SPECIALIST,
                employeeId = staff.id
            )
        } returns authorizers.distinctBy { it.id }.success()

        val result = authService.signIn(staffEmail)
        ResultAssert.assertThat(result).isSuccessWithData(healthSpecialistSigninSuccess)

        coVerify(exactly = 1) { authorizerService.authorizersByEmployee(any(), any()) }
        coVerify(exactly = 1) { staffService.findActiveByEmail(any()) }
        coVerify(exactly = 0) { authorizerService.authorizersByDomain(any()) }

        coVerify { execIndicatorAuthorizerService.getByDomain(staffEmail) wasNot called }
    }

    @Test
    fun `#getAvailableAuthorizersForStaff should return all provider units`() = runBlocking {
        val partnerEmail = "<EMAIL>"
        val staff = TestModelFactory.buildStaff(role = Role.NAVIGATOR)

        every { firebaseToken.email } returns partnerEmail
        coEvery { staffService.findActiveByEmail(partnerEmail) } returns staff.success()
        coEvery { authorizerService.all() } returns authorizers.success()
        coEvery { providerUnitService.findAllClinicalDuquesa() } returns emptyList<ProviderUnit>().success()

        withFeatureFlag(FeatureNamespace.EXEC_INDICATOR, "eita_access_to_units_switch", listOf("NAVIGATOR")) {
            val result = authService.getAvailableAuthorizersForStaff(partnerEmail, true)
            ResultAssert.assertThat(result)
                .isSuccessWithData(
                    signinSuccess.copy(
                        staff = staff,
                        userId = staff.id,
                        domain = "parceiro.alice.com.br"
                    )
                )
        }

        coVerify(exactly = 1) { staffService.findActiveByEmail(any()) }
        coVerify(exactly = 1) { authorizerService.all() }

        coVerify { execIndicatorAuthorizerService.getByDomain(staffEmail) wasNot called }
        coVerifyOnce { providerUnitService.findAllClinicalDuquesa() }
    }

    @Test
    fun `#getAvailableAuthorizersForStaff should return all provider units with new roles format`() = runBlocking {
        val staff = TestModelFactory.buildStaff(role = Role.INSURANCE_OPS_HEALTH_INSTITUTION_OPS)

        every { firebaseToken.email } returns staffEmail
        coEvery { staffService.findActiveByEmail(staffEmail) } returns staff.success()
        coEvery { authorizerService.all() } returns authorizers.success()

        withFeatureFlag(
            FeatureNamespace.EXEC_INDICATOR,
            "eita_access_to_units_switch",
            listOf("INSURANCE_OPS_HEALTH_INSTITUTION_OPS")
        ) {
            val result = authService.getAvailableAuthorizersForStaff(staffEmail, true)
            ResultAssert.assertThat(result).isSuccessWithData(signinSuccess.copy(staff = staff, userId = staff.id))
        }

        coVerify(exactly = 1) { staffService.findActiveByEmail(any()) }
        coVerify(exactly = 1) { authorizerService.all() }

        coVerify { execIndicatorAuthorizerService.getByDomain(staffEmail) wasNot called }

    }

    @Test
    fun `#signIn should confirm authentication when user are alice staff digital care nurse`() = runBlocking {
        val staff = TestModelFactory.buildStaff(role = Role.DIGITAL_CARE_NURSE)

        every { firebaseToken.email } returns staffEmail
        coEvery { staffService.findActiveByEmail(staffEmail) } returns staff.success()
        coEvery { staffService.findActiveByEmail(staffEmail) } returns staff.success()
        coEvery { authorizerService.authorizersByDomain(staffEmailDomain) } returns authorizers.success()

        val result = authService.signIn(staffEmail)
        ResultAssert.assertThat(result).isSuccessWithData(signinSuccess.copy(staff = staff, userId = staff.id))

        coVerify(exactly = 2) { staffService.findActiveByEmail(any()) }
        coVerify(exactly = 1) { authorizerService.authorizersByDomain(any()) }

        coVerify { execIndicatorAuthorizerService.getByDomain(staffEmail) wasNot called }
    }

    @Test
    fun `#signIn should confirm authentication when user are alice staff care coord`() = runBlocking {
        val staff = TestModelFactory.buildStaff(role = Role.CARE_COORD_NURSE)

        every { firebaseToken.email } returns staffEmail
        coEvery { staffService.findActiveByEmail(staffEmail) } returns staff.success()
        coEvery { staffService.findActiveByEmail(staffEmail) } returns staff.success()
        coEvery { authorizerService.authorizersByDomain(staffEmailDomain) } returns authorizers.success()

        val result = authService.signIn(staffEmail)
        ResultAssert.assertThat(result).isSuccessWithData(signinSuccess.copy(staff = staff, userId = staff.id))

        coVerify(exactly = 2) { staffService.findActiveByEmail(any()) }
        coVerify(exactly = 1) { authorizerService.authorizersByDomain(any()) }

        coVerify { execIndicatorAuthorizerService.getByDomain(staffEmail) wasNot called }
    }

    @Test
    fun `#signIn should confirm authentication when user are alice staff health care team nurse`() = runBlocking {
        val staff = TestModelFactory.buildStaff(role = Role.HEALTHCARE_TEAM_NURSE)

        every { firebaseToken.email } returns staffEmail
        coEvery { staffService.findActiveByEmail(staffEmail) } returns staff.success()
        coEvery { staffService.findActiveByEmail(staffEmail) } returns staff.success()
        coEvery { authorizerService.authorizersByDomain(staffEmailDomain) } returns authorizers.success()

        val result = authService.signIn(staffEmail)
        ResultAssert.assertThat(result).isSuccessWithData(signinSuccess.copy(staff = staff, userId = staff.id))

        coVerify(exactly = 2) { staffService.findActiveByEmail(any()) }
        coVerify(exactly = 1) { authorizerService.authorizersByDomain(any()) }

        coVerify { execIndicatorAuthorizerService.getByDomain(staffEmail) wasNot called }
    }


    @Test
    fun `#signIn should confirm authentication when user are alice staff`() = runBlocking {
        every { firebaseToken.email } returns staffEmail
        coEvery { staffService.findActiveByEmail(staffEmail) } returns staffExaminer.success()
        coEvery { staffService.findActiveByEmail(staffEmail) } returns staffExaminer.success()
        coEvery { authorizerService.authorizersByDomain(staffEmailDomain) } returns authorizers.success()

        val result = authService.signIn(staffEmail)
        ResultAssert.assertThat(result)
            .isSuccessWithData(signinSuccess.copy(staff = staffExaminer, userId = staffExaminer.id))

        coVerify(exactly = 2) { staffService.findActiveByEmail(any()) }
        coVerify(exactly = 1) { authorizerService.authorizersByDomain(any()) }

        coVerify { execIndicatorAuthorizerService.getByDomain(staffEmail) wasNot called }
    }

    @Test
    fun `#signIn should not confirm authentication when user are alice staff and a NotFoundException error occurs`() =
        runBlocking {
            every { firebaseToken.email } returns staffEmail
            coEvery { staffService.findActiveByEmail(staffEmail) } returns NotFoundException().failure()
            coEvery { staffService.findActiveByEmail(staffEmail) } returns NotFoundException().failure()
            coEvery { authorizerService.authorizersByDomain(any()) } returns emptyList<Authorizer>().success()

            val result = authService.signIn(staffEmail)
            ResultAssert.assertThat(result).isSuccessWithData(signinFailure)

            coVerify(exactly = 2) { staffService.findActiveByEmail(any()) }
        }

    @Test
    fun `#signIn should confirm authentication when user are community member`() = runBlocking {
        coEvery { staffService.findActiveByEmail(memberCommunityEmail) } returns staffExaminer.success()
        coEvery { staffService.findActiveByEmail(memberCommunityEmail) } returns staffExaminer.success()
        every { firebaseToken.email } returns memberCommunityEmail
        coEvery { authorizerService.authorizersByDomain(memberCommunityEmailDomain) } returns authorizers.success()

        val result = authService.signIn(memberCommunityEmail)
        ResultAssert.assertThat(result).isSuccessWithData(signinSuccess.copy(domain = memberCommunityEmailDomain))

        coVerify(exactly = 1) {
            authorizerService.authorizersByDomain(any())
        }

        coVerify { staffService.findActiveByEmail(memberCommunityEmail) wasNot called }
    }

    @Test
    fun `#signIn should not confirm authentication when user are community member and not exists information in database`() =
        runBlocking {
            coEvery { staffService.findActiveByEmail(memberCommunityEmail) } returns staffExaminer.success()
            coEvery { staffService.findActiveByEmail(memberCommunityEmail) } returns staffExaminer.success()
            every { firebaseToken.email } returns memberCommunityEmail
            coEvery { authorizerService.authorizersByDomain(memberCommunityEmailDomain) } returns emptyList<Authorizer>().success()

            val result = authService.signIn(memberCommunityEmail)
            ResultAssert.assertThat(result).isSuccessWithData(signinFailure.copy(domain = memberCommunityEmailDomain))

            coVerify(exactly = 1) {
                authorizerService.authorizersByDomain(any())
            }

            coVerify { staffService.findActiveByEmail(memberCommunityEmail) wasNot called }
        }

    @Test
    fun `#verifyActiveClinicalUserByEmail should return false validation when dont find staff (User not found)`() =
        runBlocking {
            val expectedResponse = ActiveClinicalUserResponse()

            coEvery { staffService.findActiveByEmail(staffEmail) } returns NotFoundException().failure()

            val result = authService.verifyActiveClinicalUserByEmail(staffEmail)
            ResultAssert.assertThat(result).isSuccessWithData(expectedResponse)

            coVerifyOnce { staffService.findActiveByEmail(any()) }
        }

    @Test
    fun `#verifyActiveClinicalUserByEmail should return true validation when find administrative staff`() =
        runBlocking {
            val staff = TestModelFactory.buildStaff(
                type = StaffType.HEALTH_ADMINISTRATIVE,
                role = Role.HEALTH_OPS_LEAD
            )

            val expectedResponse = ActiveClinicalUserResponse(
                exists = true,
                id = staff.id,
                staff = staff,
                type = EitaUserType.HEALTH_ADMINISTRATIVE
            )

            coEvery { staffService.findActiveByEmail(staffEmail) } returns staff.success()

            val result = authService.verifyActiveClinicalUserByEmail(staffEmail)
            ResultAssert.assertThat(result).isSuccessWithData(expectedResponse)

            coVerifyOnce { staffService.findActiveByEmail(any()) }
        }

    @Test
    fun `#verifyActiveClinicalUserByEmail should return true validation when find health professional staff`() =
        runBlocking {
            val staff = TestModelFactory.buildStaff(
                type = StaffType.COMMUNITY_SPECIALIST,
                role = Role.COMMUNITY
            )

            val expectedResponse = ActiveClinicalUserResponse(
                exists = true,
                id = staff.id,
                staff = staff,
                type = EitaUserType.HEALTH_SPECIALIST
            )

            coEvery { staffService.findActiveByEmail(staffEmail) } returns staff.success()

            val result = authService.verifyActiveClinicalUserByEmail(staffEmail)
            ResultAssert.assertThat(result).isSuccessWithData(expectedResponse)

            coVerifyOnce { staffService.findActiveByEmail(any()) }
        }

    @Test
    fun `#verifyPinAuthentication should return link on successful validation`() =
        runBlocking {
            coEvery { magicNumbersService.findAvailableCode(magicNumber.code) } returns magicNumber.success()
            coEvery {
                magicNumbersService.changeToAccessed(magicNumber)
            } returns magicNumber.success()

            val result = authService.verifyPinAuthentication(pin = magicNumber.code)
            ResultAssert.assertThat(result).isSuccessWithData(firebaseAccessLink)

            coVerifyOnce {
                magicNumbersService.findAvailableCode(any())
                magicNumbersService.changeToAccessed(any())
            }
        }

    @Test
    fun `#verifyPinAuthentication should return error when not find pin`() =
        runBlocking {
            coEvery { magicNumbersService.findAvailableCode(magicNumber.code) } returns NotFoundException().failure()
            val response = authService.verifyPinAuthentication(pin = magicNumber.code)
            ResultAssert.assertThat(response).isFailureOfType(NotFoundException::class)

            coVerifyOnce {
                magicNumbersService.findAvailableCode(any())
            }
        }

}
