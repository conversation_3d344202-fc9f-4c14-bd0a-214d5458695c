package br.com.alice.bottini.services

import br.com.alice.bottini.events.InviteRequestedEvent
import br.com.alice.bottini.events.LeadCreatedEvent
import br.com.alice.common.core.exceptions.DuplicatedItemException
import br.com.alice.common.core.exceptions.InvalidArgumentException
import br.com.alice.common.core.exceptions.NotFoundException
import br.com.alice.common.data.dsl.matchers.ResultAssert.Companion.assertThat
import br.com.alice.common.kafka.interfaces.KafkaProducerService
import br.com.alice.common.notification.LocalNotificationService
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.Lead
import br.com.alice.data.layer.services.LeadDataService
import com.github.kittinunf.result.failure
import com.github.kittinunf.result.success
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.mockk
import io.mockk.unmockkAll
import kotlinx.coroutines.runBlocking
import org.assertj.core.api.Assertions.assertThat
import java.time.LocalDateTime
import kotlin.test.AfterTest
import kotlin.test.Test

class LeadServiceImplTest {

    private val data: LeadDataService = mockk()
    private val acquisitionFunnel: AcquisitionFunnel = mockk()
    private val kafkaProducerService: KafkaProducerService = mockk()

    private val leadService = LeadServiceImpl(
        data,
        acquisitionFunnel,
        kafkaProducerService
    )

    private val lead = Lead(
        nationalId = "67295355391",
        firstName = "Felipe",
        lastName = "Benevides",
        email = "<EMAIL>",
        postalCode = "04570020",
        cnsNumber = "123",
        dateOfBirth = LocalDateTime.now().minusYears(30)
    )

    @AfterTest
    fun afterTest() {
        unmockkAll()
    }

    @Test
    fun `#create should publish a event to kafka`() = runBlocking {
        val nationalId = "67295355391"

        val lead = Lead(
            nationalId = nationalId,
            firstName = "Felipe",
            lastName = "Benevides",
            email = "<EMAIL>",
            postalCode = "04570020",
            dateOfBirth = LocalDateTime.now().minusYears(30)
        )

        val notFound = NotFoundException("Person not found for National Id '${nationalId}'")

        coEvery { acquisitionFunnel.findPersonByNationalId(nationalId) } returns notFound.failure()
        coEvery { data.findByNationalId(nationalId) } returns NotFoundException("Lead not found por PersonId '${nationalId}'").failure()
        coEvery { data.add(lead) } returns lead.success()
        coEvery { kafkaProducerService.produce(any(), any()) } returns mockk()


        val result = leadService.create(lead)

        assertThat(result).isSuccess()
        coVerify(exactly = 1) {
            kafkaProducerService.produce(match { it: LeadCreatedEvent ->
                it.payload.id == lead.id
            })
        }
    }

    @Test
    fun `#create should not add a new lead if it already exists`() = runBlocking {
        val nationalId = "67295355391"

        val newLead = Lead(
            nationalId = nationalId,
            firstName = "Felipe",
            lastName = "Benevides",
            email = "<EMAIL>",
            postalCode = "04570020",
            dateOfBirth = LocalDateTime.now().minusYears(30)
        )

        val notFound = NotFoundException("Person not found for National Id '${nationalId}'")
        coEvery { acquisitionFunnel.findPersonByNationalId(nationalId) } returns notFound.failure()
        coEvery { data.findByNationalId(nationalId) } returns lead.success()

        val result = leadService.create(newLead)

        coVerify(exactly = 0) { data.add(any()) }
        assertThat(result).isFailureOfType(DuplicatedItemException::class)
    }

    @Test
    fun `#create should not add a new a national id is invalid`() = runBlocking {
        val invalidLead = lead.copy(
            nationalId = "invalid_cpf"
        )

        val result = leadService.create(invalidLead)
        assertThat(result).isFailureOfType(InvalidArgumentException::class)
    }

    @Test
    fun `#updateLead should update a lead and send an LeadUpdateEvent when a person doesnt exist`(): Unit =
        runBlocking {
            val lead = TestModelFactory.buildLead()
            val updatedLead = lead.copy(phoneNumber = "1199322324")

            coEvery { data.update(updatedLead) } returns updatedLead.success()

            val result = leadService.updateLead(updatedLead)
            assertThat(result).isSuccess()
        }

    @Test
    fun `#updateLead should update a lead even if a person already exists`(): Unit = runBlocking{
        val lead = TestModelFactory.buildLead().copy(invitedAt = LocalDateTime.now())
        val updatedLead = lead.copy(phoneNumber = "1199322324")

        coEvery { data.update(updatedLead) } returns updatedLead.success()

        val result = leadService.updateLead(updatedLead)
        assertThat(result).isSuccess()
    }



    @Test
    fun `#resendLeadToCrm should publish a event`() = runBlocking {
        val lead1 = TestModelFactory.buildLead()
        val lead2 = TestModelFactory.buildLead()

        coEvery { data.findByIds(listOf(lead1.id, lead2.id)) } returns listOf(lead1, lead2).success()
        coEvery { kafkaProducerService.produce(any(), any()) } returns mockk()

        val result = leadService.resendLeadToCrm(listOf(lead1.id.toString(), lead2.id.toString()))

        assertThat(result).isSuccess()
        coVerify(exactly = 1) {
            kafkaProducerService.produce(match { it: InviteRequestedEvent ->
                it.payload.id == lead1.id
            })
        }
        coVerify(exactly = 1) {
            kafkaProducerService.produce(match { it: InviteRequestedEvent ->
                it.payload.id == lead2.id
            })
        }

    }

    @Test
    fun `#createPerson should start acquisition funnel and publish new event`() = runBlocking {
        val person = TestModelFactory.buildPerson(
            firstName = "José",
            socialName = "Joana"
        )

        coEvery { acquisitionFunnel.start(person) } returns person.success()

        val result = leadService.createPerson(person)

        assertThat(result).isSuccessWithData(person)

        coVerify(exactly = 1) { acquisitionFunnel.start(person) }
    }

    @Test
    fun `#createPerson should return error when email exists`(): Unit = runBlocking {
        val person = TestModelFactory.buildPerson()

        coEvery { acquisitionFunnel.start(person) } returns DuplicatedItemException().failure()

        val result = leadService.createPerson(person)

        coVerify(exactly = 1) { acquisitionFunnel.start(person) }
        assertThat(result).isFailureOfType(DuplicatedItemException::class)
        assertThat(LocalNotificationService.containsEvent("invite-sent")).isFalse()
    }

    @Test
    fun `#createLeadFromPerson should return previously created lead to person`() = runBlocking {
        val email = "<EMAIL>"
        val lead = TestModelFactory.buildLead(email = email)
        val person = TestModelFactory.buildPerson(email = email)

        coEvery { data.findByEmail(email) } returns lead.success()

        val result = leadService.createLeadFromPerson(person)

        assertThat(result).isSuccessWithData(lead)
    }

    @Test
    fun `#createLeadFromPerson should create lead start GAS`() = runBlocking {
        val email = "<EMAIL>"
        val lead = TestModelFactory.buildLead(email = email)
        val person = TestModelFactory.buildPerson(email = email)

        coEvery { kafkaProducerService.produce(any(), any()) } returns mockk()
        coEvery { data.findByEmail(email) } returns NotFoundException("not_found").failure()
        coEvery { data.add(match { it.email == email }) } returns lead.success()

        val result = leadService.createLeadFromPerson(person)
        assertThat(result).isSuccessWithData(lead)

        coVerify(exactly = 1) { data.add(match { it.email == email }) }
    }

}
