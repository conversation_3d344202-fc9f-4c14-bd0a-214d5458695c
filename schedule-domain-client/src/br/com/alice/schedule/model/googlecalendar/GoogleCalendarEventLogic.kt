package br.com.alice.schedule.model.googlecalendar

import br.com.alice.common.RangeUUID
import br.com.alice.common.logging.logger
import br.com.alice.data.layer.models.AppointmentSchedule
import br.com.alice.data.layer.models.Person
import br.com.alice.data.layer.models.StaffScheduleType
import com.google.api.client.util.DateTime
import com.google.api.services.calendar.model.ConferenceData
import com.google.api.services.calendar.model.ConferenceSolution
import com.google.api.services.calendar.model.ConferenceSolutionKey
import com.google.api.services.calendar.model.CreateConferenceRequest
import com.google.api.services.calendar.model.EntryPoint
import com.google.api.services.calendar.model.Event
import com.google.api.services.calendar.model.EventAttendee
import com.google.api.services.calendar.model.EventDateTime
import java.time.LocalDateTime
import java.time.ZoneOffset
import java.util.Date
import java.util.TimeZone

fun buildEvent(
    appointmentSchedule: AppointmentSchedule,
    endDateTime: LocalDateTime,
    staffName: String?,
    person: Person,
    appointmentScheduleEventTypeDescription: String?,
    timeZone: String? = null,
    recurrence: String? = null,
    isPreAppointmentEvent: Boolean? = false,
    useGoogleMeet: Boolean? = false
) = Event().let { event ->
    logger.info(
        "GoogleCalendarEventLogic.buildEvent",
        "appointment_schedule_id" to appointmentSchedule.id,
        "is_pre_appointment_event" to isPreAppointmentEvent,
        "staff_id" to appointmentSchedule.staffId,
        "person_id" to appointmentSchedule.personId,
        "event_type" to appointmentSchedule.appointmentScheduleEventTypeId.toString(),
        "use_google_meet" to useGoogleMeet
    )

    val extendedPropertiesMap = mapOf(
        "appointmentScheduleId" to appointmentSchedule.id.toString(),
        "staffScheduleType" to StaffScheduleType.HAD.name
    )
    val location = appointmentSchedule.location.orEmpty()
    val startDateTime = appointmentSchedule.startTime
    val preAppointmentStart = appointmentSchedule.getPreAppointmentStartTime()
    val preAppointmentEnd = appointmentSchedule.getPreAppointmentEndTime()
    val withDefaultDescription = true
    val eventName = appointmentSchedule.eventName

    val extendedProperties = Event.ExtendedProperties()
    extendedProperties.private = extendedPropertiesMap
    event.extendedProperties = extendedProperties
    event.location = location
    val (eventStart, eventEnd) = buildEventDates(
        isPreAppointmentEvent,
        preAppointmentStart,
        preAppointmentEnd,
        startDateTime,
        endDateTime
    )
    event.start = eventStart
    event.end = eventEnd
    timeZone?.let {
        event.start.timeZone = it
        event.end.timeZone = it
    }
    event.summary = buildEventSummary(
        appointmentSchedule = appointmentSchedule,
        staffName = staffName,
        isPreAppointmentEvent = isPreAppointmentEvent,
        person = person
    )
    event.attendees = getAttendeeInformation(person)

    if (appointmentScheduleEventTypeDescription != null) {
        event.description = appointmentScheduleEventTypeDescription
    } else if (isPreAppointmentEvent == true) {
        event.description = GoogleCalendarPreAppointmentEventDescription(
            memberName = person.fullSocialName,
            eventName = eventName,
            date = startDateTime
        ).fullDescription
    } else if (withDefaultDescription) {
        event.description = GoogleCalendarEventDescription(
            memberName = person.fullSocialName,
            eventName = eventName,
            date = startDateTime,
            zoomLink = location
        ).fullDescription
    }

    recurrence?.let { event.recurrence = listOf(it) }

    useGoogleMeet?.let {
        if (it) {
            val conferenceData = buildConferenceData(appointmentSchedule.id.toString())
            event.conferenceData = conferenceData
        }
    }

    event
}

fun buildEvent(
    start: LocalDateTime,
    end: LocalDateTime,
    withDefaultDescription: Boolean,
    extendedPropertiesMap: Map<String, String>?,
    attendee: Person? = null,
    eventName: String? = null,
    location: String? = null,
    summary: String? = null,
    timeZone: String? = null,
    recurrence: String? = null,
    description: String? = null,
    useGoogleMeet: Boolean? = false
): Event {
    val event = Event().setStart(
        EventDateTime().setDateTime(
            DateTime(
                Date.from(start.toInstant(ZoneOffset.UTC)),
                TimeZone.getTimeZone("UTC")
            )
        ).setTimeZone(timeZone ?: "UTC")
    ).setEnd(
        EventDateTime().setDateTime(
            DateTime(
                Date.from(end.toInstant(ZoneOffset.UTC)),
                TimeZone.getTimeZone("UTC")
            )
        ).setTimeZone(timeZone ?: "UTC")
    )

    extendedPropertiesMap?.let {
        event.setExtendedProperties(Event.ExtendedProperties().setPrivate(extendedPropertiesMap))
    }
    location?.let { event.setLocation(location) }
    summary?.let { event.setSummary(summary) }
    attendee?.let { event.setAttendees(getAttendeeInformation(attendee)) }

    if (description != null) {
        event.setDescription(description)
    } else if (withDefaultDescription && eventName != null && attendee != null) {
        event.setDescription(
            GoogleCalendarEventDescription(
                memberName = attendee.fullSocialName,
                eventName = eventName,
                date = start,
                zoomLink = location.orEmpty(),
            ).fullDescription
        )
    }

    recurrence?.let { event.setRecurrence(listOf(it)) }
    useGoogleMeet?.takeIf { it }?.let { event.setConferenceData(getConferenceData()) }

    return event
}

private fun getConferenceData(): ConferenceData =
    ConferenceData()
        .setCreateRequest(
            CreateConferenceRequest().setRequestId(RangeUUID.generate().toString())
        )

private fun buildConferenceData(conferenceId: String): ConferenceData {
    val createConferenceRequest = CreateConferenceRequest()
    createConferenceRequest.requestId = conferenceId

    val conferenceSolutionKey = ConferenceSolutionKey()
    conferenceSolutionKey.type = "hangoutsMeet"

    val conferenceSolution = ConferenceSolution()
    conferenceSolution.key = conferenceSolutionKey

    val entryPointType = "video"
    val entryPoints = listOf<EntryPoint>(
        EntryPoint().setEntryPointType(entryPointType)
    )

    val conferenceData = ConferenceData()
    conferenceData.conferenceSolution = conferenceSolution
    conferenceData.entryPoints = entryPoints
    conferenceData.createRequest = createConferenceRequest

    return conferenceData
}

private fun buildEventDates(
    isPreAppointmentEvent: Boolean?,
    preAppointmentStart: LocalDateTime,
    preAppointmentEnd: LocalDateTime,
    start: LocalDateTime,
    end: LocalDateTime
): Pair<EventDateTime?, EventDateTime?> {
    val timezone = TimeZone.getTimeZone("UTC")

    return when {
        isPreAppointmentEvent == null -> {
            val startDate = Date.from(preAppointmentStart.toInstant(ZoneOffset.UTC))
            val endDate = Date.from(preAppointmentEnd.toInstant(ZoneOffset.UTC))

            EventDateTime().setDateTime(DateTime(startDate, timezone)) to
                    EventDateTime().setDateTime(DateTime(endDate, timezone))
        }

        else -> {
            val startDate = Date.from(start.toInstant(ZoneOffset.UTC))
            val endDate = Date.from(end.toInstant(ZoneOffset.UTC))

            EventDateTime().setDateTime(DateTime(startDate, timezone)) to
                    EventDateTime().setDateTime(DateTime(endDate, timezone))
        }
    }
}

private fun buildEventSummary(
    appointmentSchedule: AppointmentSchedule,
    staffName: String?,
    isPreAppointmentEvent: Boolean?,
    person: Person
): String {
    val staffNameAppend = staffName?.let { " e $it" }.orEmpty()
    val eventSummary =
        if (isPreAppointmentEvent == true) "[${"Pré-atendimento"}] ${person.fullSocialName}$staffNameAppend"
        else "[${appointmentSchedule.eventName}] ${person.fullSocialName}$staffNameAppend"
    return eventSummary
}

private fun getAttendeeInformation(person: Person): List<EventAttendee> {
    val attendee = EventAttendee()
    attendee.displayName = person.fullSocialName
    attendee.email = person.email
    return listOf(attendee)
}
