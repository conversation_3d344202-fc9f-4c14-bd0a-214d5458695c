package br.com.alice.api.moneyin.controllers

import br.com.alice.api.moneyin.controllers.converters.InvoicePaymentCheckResponseConverter
import br.com.alice.api.moneyin.controllers.converters.InvoicePaymentResponseConverter
import br.com.alice.api.moneyin.controllers.converters.InvoiceResponseConverter
import br.com.alice.api.moneyin.model.InvoicePaymentCheckRequest
import br.com.alice.api.moneyin.model.InvoicePaymentCheckRequestV2
import br.com.alice.api.moneyin.model.InvoicesResponse
import br.com.alice.common.PaymentMethod
import br.com.alice.common.Response
import br.com.alice.common.core.exceptions.NotFoundException
import br.com.alice.common.extensions.flatMapPair
import br.com.alice.common.extensions.getOrNullIfNotFound
import br.com.alice.common.extensions.mapEach
import br.com.alice.common.extensions.then
import br.com.alice.common.extensions.thenError
import br.com.alice.common.foldResponse
import br.com.alice.common.logging.logger
import br.com.alice.common.toResponse
import br.com.alice.common.withUnauthenticatedTokenWithKey
import br.com.alice.data.layer.MONEY_IN_BFF_API_ROOT_SERVICE_NAME
import br.com.alice.data.layer.models.BillingAccountableParty
import br.com.alice.data.layer.models.InvoicePayment
import br.com.alice.data.layer.models.MemberInvoice
import br.com.alice.moneyin.client.BillingAccountablePartyService
import br.com.alice.moneyin.client.CompanyInvoiceDetailService
import br.com.alice.moneyin.client.InvoicePaymentService
import br.com.alice.moneyin.client.InvoicePdfService
import br.com.alice.moneyin.client.InvoicesService
import br.com.alice.moneyin.client.MemberInvoiceGroupService
import br.com.alice.moneyin.client.MoneyInResourceSignTokenService
import br.com.alice.moneyin.models.CompanyInvoiceModel
import br.com.alice.person.client.PersonService
import com.github.kittinunf.result.Result
import com.github.kittinunf.result.failure
import com.github.kittinunf.result.flatMap
import com.github.kittinunf.result.map
import com.github.kittinunf.result.success
import io.ktor.http.ContentType
import io.ktor.http.HttpStatusCode
import io.ktor.http.content.ByteArrayContent
import java.util.UUID

class PaymentController(
    private val personService: PersonService,
    private val memberInvoicesService: InvoicesService,
    private val invoicePaymentService: InvoicePaymentService,
    private val billingAccountablePartyService: BillingAccountablePartyService,
    private val memberInvoiceGroupService: MemberInvoiceGroupService,
    private val invoicePdfService: InvoicePdfService,
    private val companyInvoiceDetailService: CompanyInvoiceDetailService,
    private val moneyInResourceSignTokenService: MoneyInResourceSignTokenService
) {
    suspend fun getPaymentCheck(paymentId: UUID): Response =
        withUnauthenticatedTokenWithKey(MONEY_IN_BFF_API_ROOT_SERVICE_NAME) {
            invoicePaymentService
                .get(paymentId = paymentId, withPaymentDetails = false)
                .flatMapPair { invoicePayment -> billingAccountablePartyService.get(invoicePayment.billingAccountablePartyId!!) }
                .map { (billingAccountableParty, invoicePayment) ->
                    InvoicePaymentCheckResponseConverter.convert(
                        invoicePayment,
                        billingAccountableParty
                    )
                }
                .foldResponse()
        }

    suspend fun retrievePayment(paymentId: UUID, checkRequest: InvoicePaymentCheckRequest): Response =
        withUnauthenticatedTokenWithKey(MONEY_IN_BFF_API_ROOT_SERVICE_NAME) {
            invoicePaymentService
                .get(paymentId = paymentId, withPaymentDetails = true)
                .flatMapPair { invoicePayment -> billingAccountablePartyService.get(invoicePayment.billingAccountablePartyId!!) }
                .map { (billingAccountableParty, invoicePayment) ->
                    billingAccountableParty to validateInvoicePayment(
                        invoicePayment,
                        billingAccountableParty,
                        checkRequest
                    )
                }
                .map { (billingAccountableParty, invoicePayment) ->
                    val invoicePayment = invoicePayment.get()
                    InvoicePaymentResponseConverter.convert(
                        invoicePayment,
                        billingAccountableParty,
                        getMemberInvoiceGroup(invoicePayment)
                    )
                }
                .foldResponse()
        }

    suspend fun retrieveInvoices(paymentId: UUID, checkRequest: InvoicePaymentCheckRequest): Response =
        withUnauthenticatedTokenWithKey(MONEY_IN_BFF_API_ROOT_SERVICE_NAME) {
            invoicePaymentService
                .get(paymentId = paymentId, withPaymentDetails = false)
                .flatMapPair { invoicePayment -> billingAccountablePartyService.get(invoicePayment.billingAccountablePartyId!!) }
                .flatMap { (billingAccountableParty, invoicePayment) ->
                    validateInvoicePayment(
                        invoicePayment,
                        billingAccountableParty,
                        checkRequest
                    )
                }
                .map { invoicePayment -> invoicePayment.memberInvoiceIds.map { memberInvoicesService.get(it).get() } }
                .mapEach { memberInvoice -> Pair(memberInvoice, personService.get(memberInvoice.personId).get()) }
                .mapEach { (memberInvoice, person) -> InvoiceResponseConverter.convert(memberInvoice, person) }
                .map { InvoicesResponse(invoices = it) }
                .foldResponse()
        }

    suspend fun retrieveInvoicePaymentInfo(
        paymentId: UUID,
        checkRequest: InvoicePaymentCheckRequestV2
    ): Response =
        withUnauthenticatedTokenWithKey(MONEY_IN_BFF_API_ROOT_SERVICE_NAME) {
            val invoicePayment = invoicePaymentService
                .get(paymentId = paymentId, withPaymentDetails = true).get()

            validateInvoicePaymentV2(
                invoicePayment,
                checkRequest
            ).get()

            val billingAccountableParty =
                billingAccountablePartyService.get(invoicePayment.billingAccountablePartyId!!).get()

            val memberInvoiceGroup = getMemberInvoiceGroup(invoicePayment)

            InvoiceResponseConverter.convertToV2(
                invoicePayment,
                memberInvoiceGroup,
                billingAccountableParty,
            ).toResponse()
        }

    private fun checkNationalId(billingAccountableParty: BillingAccountableParty, checkNationalId: String): Boolean =
        billingAccountableParty.nationalId.takeLast(5) == checkNationalId


    suspend fun retrieveMemberInvoiceDetails(
        paymentId: UUID,
        checkRequest: InvoicePaymentCheckRequestV2
    ): Response =
        withUnauthenticatedTokenWithKey(MONEY_IN_BFF_API_ROOT_SERVICE_NAME) {
            val invoicePayment = invoicePaymentService
                .get(paymentId = paymentId, withPaymentDetails = true).get()

            validateInvoicePaymentV2(
                invoicePayment,
                checkRequest
            ).get()

            getModelAndIdByInvoicePayment(invoicePayment)?.let {
                getMemberInvoiceListByCompanyInvoiceModel(it)
                    .flatMapPair { memberInvoices -> personService.findByIds(memberInvoices.map { it.personId.id.toString() }) }
                    .map { (person, memberInvoice) ->
                        val personMap = person.associateBy { it.id }
                        memberInvoice.map { invoice ->
                            InvoiceResponseConverter.convert(
                                invoice,
                                personMap.getValue(invoice.personId)
                            )
                        }
                    }
            }?.foldResponse() ?: emptyList<InvoicesResponse>().toResponse()
        }

    suspend fun downloadInvoiceXlsx(
        paymentId: UUID,
        checkRequest: InvoicePaymentCheckRequestV2
    ): Response =
        withUnauthenticatedTokenWithKey(MONEY_IN_BFF_API_ROOT_SERVICE_NAME) {
            val invoicePayment = invoicePaymentService
                .get(paymentId = paymentId, withPaymentDetails = false).get()

            validateInvoicePaymentV2(
                invoicePayment,
                checkRequest
            ).get()

            val (model, id) = getModelAndIdByInvoicePayment(invoicePayment)
                ?: return@withUnauthenticatedTokenWithKey Response(
                    HttpStatusCode.NotFound,
                    "Invoice group not found"
                )

            companyInvoiceDetailService.getById(
                id,
                model
            ).map { ByteArrayContent(it, ContentType.Application.OctetStream, HttpStatusCode.OK) }
                .foldResponse()
        }

    private fun getModelAndIdByInvoicePayment(invoicePayment: InvoicePayment): Pair<CompanyInvoiceModel, UUID>? {
        if (invoicePayment.invoiceGroupId != null)
            return Pair(CompanyInvoiceModel.MEMBER_INVOICE_GROUP, invoicePayment.invoiceGroupId!!)

        if (invoicePayment.invoiceLiquidationId != null) {
            return Pair(CompanyInvoiceModel.INVOICE_LIQUIDATION, invoicePayment.invoiceLiquidationId!!)
        }

        if (invoicePayment.preActivationPaymentId != null) {
            return Pair(CompanyInvoiceModel.PRE_ACTIVATION_PAYMENT, invoicePayment.preActivationPaymentId!!)
        }

        return null
    }

    private suspend fun getMemberInvoiceListByCompanyInvoiceModel(modelAndId: Pair<CompanyInvoiceModel, UUID>): Result<List<MemberInvoice>, Throwable> {
        return when (modelAndId.first) {
            CompanyInvoiceModel.MEMBER_INVOICE_GROUP -> memberInvoicesService.listByMemberInvoiceGroupId(modelAndId.second)
            CompanyInvoiceModel.PRE_ACTIVATION_PAYMENT -> memberInvoicesService.listByPreActivationPaymentId(modelAndId.second)
            CompanyInvoiceModel.INVOICE_LIQUIDATION -> emptyList<MemberInvoice>().success()
        }
    }


    private fun validateInvoicePayment(
        invoicePayment: InvoicePayment,
        billingAccountableParty: BillingAccountableParty,
        checkRequest: InvoicePaymentCheckRequest
    ): Result<InvoicePayment, Throwable> {
        if (!checkNationalId(billingAccountableParty, checkRequest.nationalId))
            return NotFoundException("invoice_payment_not_found").failure()
        if (!paymentMethodAllowed(invoicePayment.method))
            return NotFoundException("invoice_payment_not_found").failure()

        return invoicePayment.success()
    }

    private suspend fun validateInvoicePaymentV2(
        invoicePayment: InvoicePayment,
        checkRequest: InvoicePaymentCheckRequestV2
    ): Result<InvoicePayment, Throwable> {
        if (!paymentMethodAllowed(invoicePayment.method)) {
            return Result.failure(IllegalArgumentException("Payment method not allowed"))
        }

        if (checkRequest.token == null) {
            //return Result.failure(IllegalArgumentException("Token is required"))
            return invoicePayment.success()
        }

        return moneyInResourceSignTokenService.isSignTokenValidForMoneyInBff(invoicePayment.id, checkRequest.token)
            .map {
                if (!it) {
                    return IllegalArgumentException("Token is invalid").failure()
                }
                invoicePayment
            }
    }


    private fun paymentMethodAllowed(method: PaymentMethod) =
        listOf(PaymentMethod.BOLETO, PaymentMethod.PIX, PaymentMethod.BOLEPIX).contains(method)

    private suspend fun getMemberInvoiceGroup(invoicePayment: InvoicePayment) =
        (invoicePayment.invoiceGroupId?.let { id ->
            memberInvoiceGroupService.get(id)
        } ?: invoicePayment.memberInvoiceId?.let {
            memberInvoiceGroupService.getByMemberInvoices(it)
        })?.getOrNullIfNotFound()

    suspend fun downloadInvoicePdf(invoicePaymentId: UUID) =
        withUnauthenticatedTokenWithKey(MONEY_IN_BFF_API_ROOT_SERVICE_NAME) {
            invoicePdfService.generateInvoice(invoicePaymentId)
                .map { ByteArrayContent(it, ContentType.Application.Pdf, HttpStatusCode.OK) }
                .foldResponse()
        }
}
