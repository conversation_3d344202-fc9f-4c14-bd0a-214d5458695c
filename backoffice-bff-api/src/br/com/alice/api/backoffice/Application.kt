package br.com.alice.api.backoffice

import br.com.alice.amas.ioc.AmasDomainClientModule
import br.com.alice.api.backoffice.controllers.AuthController
import br.com.alice.api.backoffice.controllers.company.CompanyController
import br.com.alice.api.backoffice.controllers.company.CompanyStaffController
import br.com.alice.api.backoffice.controllers.execIndicator.HealthSpecialistResourceBundleController
import br.com.alice.api.backoffice.controllers.execIndicator.HealthcareResourceController
import br.com.alice.api.backoffice.controllers.execIndicator.ResourceBundleSpecialistPricingController
import br.com.alice.api.backoffice.controllers.featureConfig.FeatureConfigController
import br.com.alice.api.backoffice.controllers.healthProfessionals.HealthProfessionalsController
import br.com.alice.api.backoffice.controllers.healthcareTeam.HealthcareTeamAssociationController
import br.com.alice.api.backoffice.controllers.healthcareTeam.HealthcareTeamController
import br.com.alice.api.backoffice.controllers.medicalSpecialty.MedicalSpecialtyController
import br.com.alice.api.backoffice.controllers.salesAgent.SalesAgentController
import br.com.alice.api.backoffice.controllers.salesFirm.SalesFirmController
import br.com.alice.api.backoffice.controllers.salesFirmStaff.SalesFirmStaffController
import br.com.alice.api.backoffice.controllers.siteAccreditedNetwork.SiteAccreditedNetworkController
import br.com.alice.api.backoffice.controllers.specialistEarnings.InvoiceController
import br.com.alice.api.backoffice.controllers.specialistEarnings.PreviewEarningSummaryController
import br.com.alice.api.backoffice.controllers.staff.StaffController
import br.com.alice.api.backoffice.controllers.structuredAddress.StructuredAddressController
import br.com.alice.api.backoffice.controllers.suggestedProcedure.SuggestedProcedureController
import br.com.alice.api.backoffice.controllers.vicProductOption.VicProductOptionController
import br.com.alice.api.backoffice.routes.apiRoutes
import br.com.alice.api.backoffice.services.AuthService
import br.com.alice.atlas.ioc.AtlasDomainClientModule
import br.com.alice.bottini.ioc.BottiniClientModule
import br.com.alice.business.ioc.BusinessDomainClientModule
import br.com.alice.clinicalaccount.ioc.ClinicalAccountDomainClientModule
import br.com.alice.common.PolicyRootServiceKey
import br.com.alice.common.application.plugin.RateLimitPlugin
import br.com.alice.common.application.setupBffApi
import br.com.alice.common.client.DefaultHttpClient
import br.com.alice.common.core.RunningMode.PRODUCTION
import br.com.alice.common.googlemaps.ioc.GoogleMapsModule
import br.com.alice.common.storage.FileStorage
import br.com.alice.common.storage.LocalFileStorage
import br.com.alice.common.storage.S3FileStorage
import br.com.alice.common.headerOpenTelemetryTraceId
import br.com.alice.common.headerTraceId
import br.com.alice.common.kafka.ioc.KafkaProducerModule
import br.com.alice.common.service.serialization.simpleGson
import br.com.alice.coverage.ioc.CoverageDomainClientModule
import br.com.alice.data.layer.BACKOFFICE_BFF_API_ROOT_SERVICE_NAME
import br.com.alice.data.layer.models.FeatureNamespace
import br.com.alice.exec.indicator.ioc.ExecIndicatorDomainClientModule
import br.com.alice.featureconfig.core.featureConfigBootstrap
import br.com.alice.featureconfig.ioc.FeatureConfigDomainClientModule
import br.com.alice.filevault.ioc.FileVaultClientModule
import br.com.alice.hippocrates.ioc.HippocratesDomainClientModule
import br.com.alice.hr.core.ioc.HRCoreDomainClientModule
import br.com.alice.person.ioc.PersonDomainClientModule
import br.com.alice.product.ioc.ProductDomainClientModule
import br.com.alice.provider.ioc.ProviderDomainClientModule
import br.com.alice.sales_channel.ioc.SalesChannelDomainClientModule
import br.com.alice.staff.ioc.StaffDomainClientModule
import com.google.gson.FieldNamingPolicy
import com.typesafe.config.ConfigFactory
import io.ktor.client.plugins.contentnegotiation.ContentNegotiation
import io.ktor.http.HttpHeaders
import io.ktor.http.HttpMethod
import io.ktor.server.application.Application
import io.ktor.server.config.HoconApplicationConfig
import io.ktor.server.plugins.cors.routing.CORS
import io.ktor.server.routing.routing
import org.koin.core.module.Module
import org.koin.core.module.dsl.singleOf
import org.koin.dsl.module
import java.time.Duration

fun main(args: Array<String>): Unit = io.ktor.server.netty.EngineMain.main(args)

object ApplicationModule {

    private val config = HoconApplicationConfig(ConfigFactory.load("application.conf"))

    val dependencyInjectionModules = listOf(
        FeatureConfigDomainClientModule,
        AtlasDomainClientModule,
        BusinessDomainClientModule,
        StaffDomainClientModule,
        SalesChannelDomainClientModule,
        KafkaProducerModule,
        ExecIndicatorDomainClientModule,
        BottiniClientModule,
        ProductDomainClientModule,
        ProviderDomainClientModule,
        CoverageDomainClientModule,
        GoogleMapsModule,
        ClinicalAccountDomainClientModule,
        PersonDomainClientModule,
        HippocratesDomainClientModule,
        HRCoreDomainClientModule,
        AmasDomainClientModule,
        FileVaultClientModule,

        module(createdAtStart = true) {

            when (ServiceConfig.runningMode) {
                PRODUCTION -> {
                    single<FileStorage> { S3FileStorage() }
                }
                else -> {
                    single<FileStorage> { LocalFileStorage() }
                }
            }

            // Services
            single { AuthService(get()) }
            single { br.com.alice.api.backoffice.services.StaffBackofficeService() }

            // Controllers
            single { AuthController(get()) }
            single { FeatureConfigController(get()) }
            single { CompanyStaffController(get(), get(), get()) }
            single { CompanyController(get()) }
            single { SalesAgentController(get(), get(), get(), get()) }
            single { SalesFirmController(get()) }
            single { SalesFirmStaffController(get(), get()) }
            single { HealthSpecialistResourceBundleController(get(), get(), get()) }
            single { HealthcareResourceController(get()) }
            single { VicProductOptionController(get(), get(), get(), get()) }
            single { SiteAccreditedNetworkController(get(), get(), get(), get()) }
            single { HealthcareTeamController(get(), get()) }
            single { StructuredAddressController(get(), get()) }
            single { MedicalSpecialtyController(get(), get()) }
            single { HealthcareTeamAssociationController(get(), get(), get(), get()) }
            single { StaffController(get(), get(), get(), get(), get(), get(), get()) }
            single { HealthProfessionalsController(get()) }
            single { ResourceBundleSpecialistPricingController(get()) }
            single { SuggestedProcedureController(get(), get()) }

            singleOf(::PreviewEarningSummaryController)
            singleOf(::InvoiceController)
            // Configuration
            single { config }
            single {
                DefaultHttpClient({ install(ContentNegotiation) {
                    simpleGson()
                } }, timeoutInMillis = 15_000)
            }

        }
    )
}

@JvmOverloads
fun Application.module(
    dependencyInjectionModules: List<Module> = ApplicationModule.dependencyInjectionModules,
) {
    setupBffApi(dependencyInjectionModules, FieldNamingPolicy.IDENTITY, withOriginalErrorMessage = true) {
        install(CORS) {
            allowMethod(HttpMethod.Get)
            allowMethod(HttpMethod.Post)
            allowMethod(HttpMethod.Put)
            allowMethod(HttpMethod.Delete)
            allowMethod(HttpMethod.Patch)
            allowMethod(HttpMethod.Options)
            allowHeader(HttpHeaders.Authorization)
            allowHeader(HttpHeaders.ContentType)
            allowHeader(HttpHeaders.ContentRange)
            allowHeadersPrefixed("X-Datadog-")
            allowHeader("traceparent")
            exposeHeader(HttpHeaders.ContentRange)
            exposeHeader(headerTraceId)
            exposeHeader(headerOpenTelemetryTraceId)

            anyHost()
            maxAgeInSeconds = Duration.ofDays(7).seconds
        }

        routing {
            application.attributes.put(PolicyRootServiceKey, BACKOFFICE_BFF_API_ROOT_SERVICE_NAME)

            apiRoutes()
        }

        install(RateLimitPlugin)

        featureConfigBootstrap(FeatureNamespace.BACKOFFICE)
    }
}
