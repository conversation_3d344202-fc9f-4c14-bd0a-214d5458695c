package br.com.alice.fhir.consumers

import br.com.alice.common.core.PersonId
import br.com.alice.common.core.exceptions.NotFoundException
import br.com.alice.common.data.dsl.matchers.ResultAssert
import br.com.alice.common.featureflag.withFeatureFlag
import br.com.alice.common.helpers.coVerifyNone
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.common.helpers.mockRangeUUID
import br.com.alice.common.models.State
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.ExternalReferral
import br.com.alice.data.layer.models.ExternalReferralSource
import br.com.alice.data.layer.models.FeatureNamespace
import br.com.alice.data.layer.models.ProviderIntegration
import br.com.alice.ehr.client.ExternalReferralService
import br.com.alice.fhir.event.FhirServiceRequestEvent
import br.com.alice.provider.client.MedicalSpecialtyService
import com.github.kittinunf.result.failure
import com.github.kittinunf.result.success
import io.mockk.clearAllMocks
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.mockk
import kotlinx.coroutines.runBlocking
import java.time.LocalDateTime
import kotlin.test.BeforeTest
import kotlin.test.Test

class ServiceRequestConsumerTest(): ConsumerTest() {
    private val medFisio = TestModelFactory.buildMedicalSpecialty(name = "Fisioterapia")
    private val personId = PersonId()
    private val externalReferralService: ExternalReferralService = mockk()
    private val medicalSpecialtyService: MedicalSpecialtyService = mockk()

    private val consumer = ServiceRequestConsumer(externalReferralService, medicalSpecialtyService)

    @BeforeTest
    fun setup() {
        clearAllMocks()
        super.before()
    }

    @Test
    fun `#consumeServiceRequest should add external service request`() = runBlocking {
        val event = FhirServiceRequestEvent(
            provider = ProviderIntegration.EINSTEIN_CLINIC,
            personId = personId,
            reason = "",
            cboCode = "222",
            referredAt = LocalDateTime.now(),
            referredByName = "Joe Doe",
            referredByCouncilName = "CRM",
            referredByCouncilState = State.SP,
            referredByCouncilNumber = "123321",
        )

        val expected = ExternalReferral(
            personId = personId,
            reason = "",
            specialty = ExternalReferral.Specialty(medFisio.id, medFisio.name),
            referredAt = event.payload.referredAt,
            referredByName = "Joe Doe",
            referredByCouncilName = "CRM",
            referredByCouncilState = State.SP,
            referredByCouncilNumber = "123321",
            dueDate = event.payload.referredAt.plusYears(1),
            source = ExternalReferralSource.EINSTEIN,
        )

        coEvery { medicalSpecialtyService.getByCBOCode(event.payload.cboCode) } returns medFisio.success()
        coEvery {
            externalReferralService.add(match {
                it.personId == expected.personId &&
                it.referredByName == expected.referredByName &&
                        it.referredByCouncilName == expected.referredByCouncilName &&
                        it.referredByCouncilState == expected.referredByCouncilState &&
                        it.referredByCouncilNumber == expected.referredByCouncilNumber &&
                        it.specialty.id == medFisio.id &&
                        it.specialty.name == medFisio.name &&
                        it.source == expected.source
            })
        } returns expected.success()

        val result = consumer.consumeServiceRequest(event)
        ResultAssert.assertThat(result).isSuccess()

        coVerifyOnce { externalReferralService.add(any()) }
    }

    @Test
    fun `#consumeServiceRequest should add external service request with source BACKFILL`() = runBlocking {
        val event = FhirServiceRequestEvent(
            provider = ProviderIntegration.BP,
            personId = personId,
            reason = "",
            cboCode = "222",
            referredAt = LocalDateTime.now(),
            referredByName = "Joe Doe",
            referredByCouncilName = "CRM",
            referredByCouncilState = State.SP,
            referredByCouncilNumber = "123321",
        )

        val expected = ExternalReferral(
            personId = personId,
            reason = "",
            specialty = ExternalReferral.Specialty(medFisio.id, medFisio.name),
            referredAt = event.payload.referredAt,
            referredByName = "Joe Doe",
            referredByCouncilName = "CRM",
            referredByCouncilState = State.SP,
            referredByCouncilNumber = "123321",
            dueDate = event.payload.referredAt.plusYears(1),
            source = ExternalReferralSource.BACKFILL,
        )

        coEvery { medicalSpecialtyService.getByCBOCode(event.payload.cboCode) } returns medFisio.success()
        coEvery {
            externalReferralService.add(match {
                it.personId == expected.personId &&
                        it.referredByName == expected.referredByName &&
                        it.referredByCouncilName == expected.referredByCouncilName &&
                        it.referredByCouncilState == expected.referredByCouncilState &&
                        it.referredByCouncilNumber == expected.referredByCouncilNumber &&
                        it.specialty.id == medFisio.id &&
                        it.specialty.name == medFisio.name &&
                        it.source == expected.source
            })
        } returns expected.success()

        val result = consumer.consumeServiceRequest(event)
        ResultAssert.assertThat(result).isSuccess()

        coVerifyOnce { externalReferralService.add(any()) }
    }

    @Test
    fun `#consumeServiceRequest should add external service request with source FLEURY`() = runBlocking {
        val event = FhirServiceRequestEvent(
            provider = ProviderIntegration.FLEURY,
            personId = personId,
            reason = "",
            cboCode = "222",
            referredAt = LocalDateTime.now(),
            referredByName = "Joe Doe",
            referredByCouncilName = "CRM",
            referredByCouncilState = State.SP,
            referredByCouncilNumber = "123321",
        )

        val expected = ExternalReferral(
            personId = personId,
            reason = "",
            specialty = ExternalReferral.Specialty(medFisio.id, medFisio.name),
            referredAt = event.payload.referredAt,
            referredByName = "Joe Doe",
            referredByCouncilName = "CRM",
            referredByCouncilState = State.SP,
            referredByCouncilNumber = "123321",
            dueDate = event.payload.referredAt.plusYears(1),
            source = ExternalReferralSource.FLEURY,
        )

        coEvery { medicalSpecialtyService.getByCBOCode(event.payload.cboCode) } returns medFisio.success()
        coEvery {
            externalReferralService.add(match {
                it.personId == expected.personId &&
                        it.referredByName == expected.referredByName &&
                        it.referredByCouncilName == expected.referredByCouncilName &&
                        it.referredByCouncilState == expected.referredByCouncilState &&
                        it.referredByCouncilNumber == expected.referredByCouncilNumber &&
                        it.specialty.id == medFisio.id &&
                        it.specialty.name == medFisio.name &&
                        it.source == expected.source
            })
        } returns expected.success()

        val result = consumer.consumeServiceRequest(event)
        ResultAssert.assertThat(result).isSuccess()

        coVerifyOnce { externalReferralService.add(any()) }
    }

    @Test
    fun `#consumeServiceRequest add external service request with source FLEURY retry find CBO when it has 5 digits`() = mockRangeUUID { uuid ->
        val event = FhirServiceRequestEvent(
            provider = ProviderIntegration.FLEURY,
            personId = personId,
            reason = "",
            cboCode = "22222",
            referredAt = LocalDateTime.now(),
            referredByName = "Joe Doe",
            referredByCouncilName = "CRM",
            referredByCouncilState = State.SP,
            referredByCouncilNumber = "123321",
        )

        val expected = ExternalReferral(
            id = uuid,
            personId = personId,
            reason = "",
            specialty = ExternalReferral.Specialty(medFisio.id, medFisio.name),
            referredAt = event.payload.referredAt,
            referredByName = "Joe Doe",
            referredByCouncilName = "CRM",
            referredByCouncilState = State.SP,
            referredByCouncilNumber = "123321",
            dueDate = event.payload.referredAt.plusYears(1),
            source = ExternalReferralSource.FLEURY,
        )

        coEvery { medicalSpecialtyService.getByCBOCode("22222") } returns NotFoundException().failure()
        coEvery { medicalSpecialtyService.getByCBOCode("222220") } returns medFisio.success()
        coEvery { externalReferralService.add(expected) } returns expected.success()

        val result = consumer.consumeServiceRequest(event)
        ResultAssert.assertThat(result).isSuccessWithData(expected)

        coVerify(exactly = 2) { medicalSpecialtyService.getByCBOCode(any()) }
        coVerifyOnce { externalReferralService.add(any()) }
    }

    @Test
    fun `#consumeServiceRequest should ignore external service request`() = runBlocking {
        withFeatureFlag(FeatureNamespace.FHIR, "service_request_cbos_to_ignore", listOf("222")) {
            val event = FhirServiceRequestEvent(
                provider = ProviderIntegration.EINSTEIN_CLINIC,
                personId = personId,
                reason = "",
                cboCode = "222",
                referredAt = LocalDateTime.now(),
                referredByName = "Joe Doe",
                referredByCouncilName = "CRM",
                referredByCouncilState = State.SP,
                referredByCouncilNumber = "123321",
            )

            val result = consumer.consumeServiceRequest(event)
            ResultAssert.assertThat(result).isSuccess()

            coVerifyNone { externalReferralService.add(any()) }
        }
    }
}
