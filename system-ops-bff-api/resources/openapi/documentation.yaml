openapi: 3.0.0  # version of the OpenAPI Specification
info:
  title: System Ops BFF API  # title of the API
  description: System Ops BFF API  # description of the API
  version: 1.0.0  # version of the API
servers:
  - url: https://system-ops-bff-api-dev2.dev.wonderland.engineering  # URL of the server
    description: Dev2 Environment  # description of the server
  - url: https://system-ops-bff-api-dev1.dev.wonderland.engineering  # URL of the server
    description: Dev1 Environment  # description of the server
  - url: https://system-ops-bff-api.staging.wonderland.engineering  # URL of the server
    description: Staging Environment  # description of the server
  - url: https://system-ops-bff-api.wonderland.engineering  # URL of the server
    description: Production Environment  # description of the server
tags:
    - name: Corretores  # name of the tag
paths:
    /sales_agent:  # path of the endpoint
        get:
            summary: Listar corretores cadastrados  # summary of the endpoint
            description: |  # description of the endpoint
                Endpoint de busca de corretores.
            operationId: listSalesAgent  # operationId of the endpoint
            parameters:
              - name: range
                in: query
                description: |
                  Range de resultados a serem retornados. Exemplo: [0, 9]
                required: false
                schema:
                  type: array
                  items:
                    type: integer
              - name: q
                in: query
                description: |
                  filtro de busca. Exemplo: "João"
                required: false
                schema:
                  type: string
            responses:
                '200':
                  description: OK
                  content:
                    application/json:
                      schema:
                        allOf:
                          - type: object
                            properties:
                              results:
                                type: array
                                items:
                                  $ref: '#/components/schemas/SalesAgent'
                '400':
                    description: Bad Request  # description of the response
                '401':
                    description: Unauthorized  # description of the response

    /sales_agent/:id: # path of the endpoint
      get:
        summary: Buscar corretor cadastrado  # summary of the endpoint
        description: |  # description of the endpoint
          Endpoint de busca de corretores.
        operationId: getSalesAgent  # operationId of the endpoint
        parameters:
          - name: id
            in: parameter
            description: |
              Busca corretor dado um id.
            required: true
            schema:
              type: string
        responses:
          responses:
            '200':
              description: OK
              content:
                application/json:
                  schema:
                    allOf:
                      - type: object
                        properties:
                          results:
                            type: '#/components/schemas/SalesAgent'
          '400':
            description: Bad Request  # description of the response
          '401':
            description: Unauthorized  # description of the response
      put:
        summary: Atualizar corretor cadastrado  # summary of the endpoint
        description: |  # description of the endpoint
          Endpoint de atualização das informações de corretore.
        operationId: updateSalesAgent  # operationId of the endpoint
        parameters:
          - name: id
            in: parameter
            description: |
              Busca corretor dado um id.
            required: true
            schema:
              type: string
        requestBody:
          description: Optional description in *Markdown*
          required: true
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SalesAgent'
        responses:
          '200':
            description: OK  # description of the response
          '400':
            description: Bad Request  # description of the response
          '401':
            description: Unauthorized  # description of the response

components:
    schemas: # definition of the schemas
        SalesAgent:  # name of the schema
            type: object  # type of the schema
            properties:  # properties of the schema
                id:  # name of the property
                    type: string  # type of the property
                name:  # name of the property
                    type: string  # type of the property
                email:  # name of the property
                    type: string  # type of the property
                phone:  # name of the property
                    type: string  # type of the property
                document:  # name of the property
                    type: string  # type of the property
                status:  # name of the property
                    type: string  # type of the property
                created_at:  # name of the property
                    type: string  # type of the property
                updated_at:  # name of the property
                    type: string  # type of the property
                deleted_at:  # name of the property
                    type: string  # type of the property
                sales_firm_id:  # name of the property
                    type: string  # type of the property
