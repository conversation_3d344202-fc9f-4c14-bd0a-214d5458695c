package br.com.alice.member.api.converters

import br.com.alice.common.helpers.mockRangeUUID
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.AccommodationType
import br.com.alice.data.layer.models.CoPaymentChargeType
import br.com.alice.data.layer.models.CoPaymentTierPrice
import br.com.alice.data.layer.models.CoPaymentType
import br.com.alice.data.layer.models.ProviderType
import br.com.alice.data.layer.models.RefundEventType
import br.com.alice.data.layer.models.RefundTierPrice
import br.com.alice.data.layer.models.RefundType
import br.com.alice.data.layer.models.TierType
import br.com.alice.member.api.models.InfoValue
import br.com.alice.member.api.models.InfoWrapper
import br.com.alice.member.api.models.MemberOnboardingProductDetailsResponse
import br.com.alice.member.api.models.MemberProductDetails
import br.com.alice.member.api.models.ProductDetailsPerk
import br.com.alice.product.client.PersonCoPaymentCostInfo
import br.com.alice.product.client.PersonRefundCostInfo
import br.com.alice.product.transport_model.RefundCost
import br.com.alice.product.transport_model.RefundCostPrice
import java.math.BigDecimal
import java.text.NumberFormat
import java.util.Locale
import kotlin.test.Test
import kotlin.test.assertEquals

class MemberOnboardingProductConverterTest {

    private val person = TestModelFactory.buildPerson()

    private val product = TestModelFactory.buildProduct(
        displayName = "Produto",
        accommodation = AccommodationType.ROOM,
        tier = TierType.TIER_1,
        coPayment = CoPaymentType.FULL,
        refund = RefundType.FULL,
        hasNationalCoverage = true
    )
    private val hospitalProvider = TestModelFactory.buildProvider(
        flagship = true,
        type = ProviderType.HOSPITAL
    )
    private val laboratoryProvider = TestModelFactory.buildProvider(
        flagship = true,
        type = ProviderType.LABORATORY
    )
    private val coPaymentCostInfo = PersonCoPaymentCostInfo(
        event = "Consulta",
        chargeType = CoPaymentChargeType.FIXED_VALUE,
        price = CoPaymentTierPrice(
            tier = product.tier!!,
            value = BigDecimal.TEN
        )
    )
    private val personRefundCostInfo = PersonRefundCostInfo(
        event = "Consulta",
        type = RefundEventType.APPOINTMENT,
        price = RefundTierPrice(
            tier = product.tier!!,
            value = BigDecimal.TEN
        )
    )

    @Test
    fun `#convert should convert successfully when there is co payment and refund`() = mockRangeUUID {
        val medicalFeeRefund = PersonRefundCostInfo(
            event = "Honorários médicos",
            type = RefundEventType.MEDICAL_FEES,
            price = null
        )
        val details = MemberProductDetails(
            product = product,
            providers = listOf(laboratoryProvider, hospitalProvider),
            coPayment = listOf(coPaymentCostInfo),
            refund = listOf(personRefundCostInfo, medicalFeeRefund)
        )

        val expected = MemberOnboardingProductDetailsResponse(
            title = "${person.firstName}, seu plano atual é ${product.displayName}",
            perks = listOf(
                ProductDetailsPerk(
                    icon = "bed",
                    title = "Acomodação",
                    description = product.accommodation!!.title,
                ),
                ProductDetailsPerk(
                    icon = "airplane",
                    title = "Cobertura",
                    description = "Nacional",
                ),
                ProductDetailsPerk(
                    icon = "hospital",
                    title = "Hospitais",
                    description = "${hospitalProvider.name} e outros",
                ),
                ProductDetailsPerk(
                    icon = "lab",
                    title = "Laboratórios",
                    description = "${laboratoryProvider.name} e outros",
                ),
                ProductDetailsPerk(
                    icon = "doctor",
                    title = "Médico de Família",
                    description = "Isento de coparticipação",
                ),
                ProductDetailsPerk(
                    icon = "heart_outlined",
                    title = "Alice Agora - Saúde 24h",
                    description = "Isento de coparticipação",
                ),
            ),
            copay = InfoWrapper(
                icon = "money",
                title = "Coparticipação",
                subtitle = "Confira os valores na tabela",
                description = "Seu plano possui coparticipação, com **valor fixo para consultas e internação e de 30% " +
                        "do custo dos demais serviços.**",
                values = listOf(
                    InfoValue(
                        title = "${coPaymentCostInfo.event} (valor fixo)",
                        value = coPaymentCostInfo.price.value.toMoneyString()
                    )
                )
            ),
            refund = InfoWrapper(
                icon = "refund_money",
                title = "Reembolso",
                subtitle = "Confira os valores na tabela",
                description = "Seu plano tem reembolso para consultas, terapias e honorários médicos (cirurgias, " +
                        "procedimentos ambulatoriais e visita hospitalar).",
                values = listOf(
                    InfoValue(
                        title = personRefundCostInfo.event,
                        value = personRefundCostInfo.price!!.value.toMoneyString()
                    ),
                    InfoValue(
                        title = medicalFeeRefund.event,
                        value = "sob análise*"
                    )
                )
            ),
        )

        val result = MemberOnboardingProductConverter.convert(person, details)

        assertEquals(expected, result)
    }

    @Test
    fun `#convert should convert successfully when there is co payment but has no refund`() = mockRangeUUID {
        val details = MemberProductDetails(
            product = product.copy(refund = RefundType.NONE),
            providers = listOf(laboratoryProvider, hospitalProvider),
            coPayment = listOf(coPaymentCostInfo),
            refund = emptyList()
        )

        val expected = MemberOnboardingProductDetailsResponse(
            title = "${person.firstName}, seu plano atual é ${product.displayName}",
            perks = listOf(
                ProductDetailsPerk(
                    icon = "bed",
                    title = "Acomodação",
                    description = product.accommodation!!.title,
                ),
                ProductDetailsPerk(
                    icon = "airplane",
                    title = "Cobertura",
                    description = "Nacional",
                ),
                ProductDetailsPerk(
                    icon = "hospital",
                    title = "Hospitais",
                    description = "${hospitalProvider.name} e outros",
                ),
                ProductDetailsPerk(
                    icon = "lab",
                    title = "Laboratórios",
                    description = "${laboratoryProvider.name} e outros",
                ),
                ProductDetailsPerk(
                    icon = "doctor",
                    title = "Médico de Família",
                    description = "Isento de coparticipação",
                ),
                ProductDetailsPerk(
                    icon = "heart_outlined",
                    title = "Alice Agora - Saúde 24h",
                    description = "Isento de coparticipação",
                ),
            ),
            copay = InfoWrapper(
                icon = "money",
                title = "Coparticipação",
                subtitle = "Confira os valores na tabela",
                description = "Seu plano possui coparticipação, com **valor fixo para consultas e internação e de 30% " +
                        "do custo dos demais serviços.**",
                values = listOf(
                    InfoValue(
                        title = "${coPaymentCostInfo.event} (valor fixo)",
                        value = coPaymentCostInfo.price.value.toMoneyString()
                    )
                )
            ),
            refund = InfoWrapper(
                icon = "refund_money",
                title = "Reembolso",
                subtitle = "Seu plano não possui",
                description = null,
                values = emptyList()
            ),
        )

        val result = MemberOnboardingProductConverter.convert(person, details)

        assertEquals(expected, result)
    }

    @Test
    fun `#convert should convert successfully when there is no co payment but it has refund`() = mockRangeUUID {
        val medicalFeeRefund = PersonRefundCostInfo(
            event = "Honorários médicos",
            type = RefundEventType.MEDICAL_FEES,
            price = null
        )

        val details = MemberProductDetails(
            product = product.copy(coPayment = CoPaymentType.NONE),
            providers = listOf(laboratoryProvider, hospitalProvider),
            coPayment = emptyList(),
            refund = listOf(personRefundCostInfo, medicalFeeRefund)
        )

        val expected = MemberOnboardingProductDetailsResponse(
            title = "${person.firstName}, seu plano atual é ${product.displayName}",
            perks = listOf(
                ProductDetailsPerk(
                    icon = "bed",
                    title = "Acomodação",
                    description = product.accommodation!!.title,
                ),
                ProductDetailsPerk(
                    icon = "airplane",
                    title = "Cobertura",
                    description = "Nacional",
                ),
                ProductDetailsPerk(
                    icon = "hospital",
                    title = "Hospitais",
                    description = "${hospitalProvider.name} e outros",
                ),
                ProductDetailsPerk(
                    icon = "lab",
                    title = "Laboratórios",
                    description = "${laboratoryProvider.name} e outros",
                ),
                ProductDetailsPerk(
                    icon = "doctor",
                    title = "Médico de Família",
                    description = "Isento de coparticipação",
                ),
                ProductDetailsPerk(
                    icon = "heart_outlined",
                    title = "Alice Agora - Saúde 24h",
                    description = "Isento de coparticipação",
                ),
            ),
            copay = InfoWrapper(
                icon = "money",
                title = "Coparticipação",
                subtitle = "Seu plano não possui",
                description = null,
                values = emptyList()
            ),
            refund = InfoWrapper(
                icon = "refund_money",
                title = "Reembolso",
                subtitle = "Confira os valores na tabela",
                description = "Seu plano tem reembolso para consultas, terapias e honorários médicos (cirurgias, " +
                        "procedimentos ambulatoriais e visita hospitalar).",
                values = listOf(
                    InfoValue(
                        title = personRefundCostInfo.event,
                        value = personRefundCostInfo.price!!.value.toMoneyString()
                    ),
                    InfoValue(
                        title = medicalFeeRefund.event,
                        value = "sob análise*"
                    )
                )
            ),
        )

        val result = MemberOnboardingProductConverter.convert(person, details)

        assertEquals(expected, result)
    }

    @Test
    fun `#convert should convert successfully when there is no co payment but it has refundCost`() = mockRangeUUID {
        val refundCost = RefundCost(
            tier = product.tier!!,
            prices = listOf(
                RefundCostPrice(
                    value = BigDecimal.TEN,
                    eventType = RefundEventType.APPOINTMENT,
                    event = "Consulta"
                ),
                RefundCostPrice(
                    value = BigDecimal.ONE,
                    eventType = RefundEventType.THERAPY,
                    event = "Fisioterapia"
                )
            )
        )
        val details = MemberProductDetails(
            product = product.copy(coPayment = CoPaymentType.NONE),
            providers = listOf(laboratoryProvider, hospitalProvider),
            coPayment = emptyList(),
            refundCost = refundCost
        )

        val expected = MemberOnboardingProductDetailsResponse(
            title = "${person.firstName}, seu plano atual é ${product.displayName}",
            perks = listOf(
                ProductDetailsPerk(
                    icon = "bed",
                    title = "Acomodação",
                    description = product.accommodation!!.title,
                ),
                ProductDetailsPerk(
                    icon = "airplane",
                    title = "Cobertura",
                    description = "Nacional",
                ),
                ProductDetailsPerk(
                    icon = "hospital",
                    title = "Hospitais",
                    description = "${hospitalProvider.name} e outros",
                ),
                ProductDetailsPerk(
                    icon = "lab",
                    title = "Laboratórios",
                    description = "${laboratoryProvider.name} e outros",
                ),
                ProductDetailsPerk(
                    icon = "doctor",
                    title = "Médico de Família",
                    description = "Isento de coparticipação",
                ),
                ProductDetailsPerk(
                    icon = "heart_outlined",
                    title = "Alice Agora - Saúde 24h",
                    description = "Isento de coparticipação",
                ),
            ),
            copay = InfoWrapper(
                icon = "money",
                title = "Coparticipação",
                subtitle = "Seu plano não possui",
                description = null,
                values = emptyList()
            ),
            refund = InfoWrapper(
                icon = "refund_money",
                title = "Reembolso",
                subtitle = "Confira os valores na tabela",
                description = "Seu plano tem reembolso para consultas, terapias e honorários médicos (cirurgias, " +
                        "procedimentos ambulatoriais e visita hospitalar).",
                values = refundCost.prices.map{
                    InfoValue(
                        title = it.event,
                        value = it.value?.toMoneyString() ?: "sob análise*"
                    )
                }
            ),
        )

        val result = MemberOnboardingProductConverter.convert(person, details)

        assertEquals(expected, result)
    }

    private fun BigDecimal.toMoneyString(): String {
        val formatter = NumberFormat.getCurrencyInstance(Locale.forLanguageTag("pt-BR"))
        formatter.maximumFractionDigits = 0

        return formatter.format(this.toDouble())
    }

}
