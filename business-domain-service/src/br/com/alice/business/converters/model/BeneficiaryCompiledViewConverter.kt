package br.com.alice.business.converters.model

import br.com.alice.common.Converter
import br.com.alice.data.layer.models.BeneficiaryCompiledView
import br.com.alice.data.layer.models.BeneficiaryCompiledViewModel

object BeneficiaryCompiledViewConverter : Converter<BeneficiaryCompiledViewModel, BeneficiaryCompiledView>(
    BeneficiaryCompiledViewModel::class,
    BeneficiaryCompiledView::class,
)

fun BeneficiaryCompiledView.toModel() = BeneficiaryCompiledViewConverter.unconvert(this)
fun BeneficiaryCompiledViewModel.toTransport() = BeneficiaryCompiledViewConverter.convert(this)
