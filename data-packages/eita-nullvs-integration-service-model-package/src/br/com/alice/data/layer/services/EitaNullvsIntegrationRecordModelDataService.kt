package br.com.alice.data.layer.services

import br.com.alice.common.rfc.RemoteService
import br.com.alice.common.rfc.Service
import br.com.alice.common.service.data.client.Adder
import br.com.alice.common.service.data.client.Deleter
import br.com.alice.common.service.data.client.Finder
import br.com.alice.common.service.data.client.Getter
import br.com.alice.common.service.data.client.Updater
import br.com.alice.common.service.data.dsl.Field
import br.com.alice.common.service.data.dsl.Predicate
import br.com.alice.common.service.data.dsl.Query
import br.com.alice.common.service.data.dsl.QueryBuilder
import br.com.alice.data.layer.models.EitaNullvsExternalModelType
import br.com.alice.data.layer.models.EitaNullvsIntegrationRecordModel
import br.com.alice.data.layer.models.EitaNullvsInternalModelType
import com.github.kittinunf.result.Result
import java.util.UUID

@RemoteService
interface EitaNullvsIntegrationRecordModelDataService : Service,
    Finder<EitaNullvsIntegrationRecordModelDataService.FieldOptions, EitaNullvsIntegrationRecordModelDataService.OrderingOptions, EitaNullvsIntegrationRecordModel>,
    Updater<EitaNullvsIntegrationRecordModel>,
    Getter<EitaNullvsIntegrationRecordModel>,
    Deleter<EitaNullvsIntegrationRecordModel>,
    Adder<EitaNullvsIntegrationRecordModel> {

    override val namespace: String
        get() = "eita_nullvs_integration"
    override val serviceName: String
        get() = "eita_nullvs_integration_record"

    class IdField : Field.UUIDField(EitaNullvsIntegrationRecordModel::id) {
        fun eq(value: UUID) = Predicate.eq(this, value)
        fun inList(value: List<UUID>) = Predicate.inList(this, value)
    }

    class InternalIdField : Field.UUIDField(EitaNullvsIntegrationRecordModel::internalId) {
        fun eq(value: UUID) = Predicate.eq(this, value)
    }

    class InternalModelNameField : Field.TextField(EitaNullvsIntegrationRecordModel::internalModelName) {
        fun eq(value: EitaNullvsInternalModelType) = Predicate.eq(this, value)
    }

    class ExternalIdField : Field.TextField(EitaNullvsIntegrationRecordModel::externalId) {
        fun inList(value: List<String>) = Predicate.inList(this, value)
    }

    class ExternalModelNameField : Field.TextField(EitaNullvsIntegrationRecordModel::externalModelName) {
        fun eq(value: EitaNullvsExternalModelType) = Predicate.eq(this, value)
    }

    class CreatedAt : Field.DateTimeField(EitaNullvsIntegrationRecordModel::createdAt)

    class FieldOptions {
        val id = IdField()
        val internalId = InternalIdField()
        val internalModelName = InternalModelNameField()
        val externalId = ExternalIdField()
        val externalModelName = ExternalModelNameField()
    }

    class OrderingOptions {
        val id = IdField()
        val createdAt = CreatedAt()
    }

    override fun queryBuilder() = QueryBuilder(
        FieldOptions(),
        OrderingOptions()
    )

    override suspend fun add(model: EitaNullvsIntegrationRecordModel): Result<EitaNullvsIntegrationRecordModel, Throwable>

    override suspend fun findByQuery(query: Query): Result<List<EitaNullvsIntegrationRecordModel>, Throwable>

    override suspend fun get(id: UUID): Result<EitaNullvsIntegrationRecordModel, Throwable>

    override suspend fun update(model: EitaNullvsIntegrationRecordModel): Result<EitaNullvsIntegrationRecordModel, Throwable>

    override suspend fun delete(model: EitaNullvsIntegrationRecordModel): Result<Boolean, Throwable>
}
