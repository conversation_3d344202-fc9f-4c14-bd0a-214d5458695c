package br.com.alice.exec.indicator.client

import br.com.alice.common.rfc.RemoteService
import br.com.alice.common.rfc.Service
import br.com.alice.data.layer.models.MvAuthorizedProcedure
import br.com.alice.exec.indicator.models.GuiaAttachmentChemotherapyRequest
import com.github.kittinunf.result.Result

@RemoteService
interface GuiaAttachmentChemotherapyService : Service {

    override val namespace get() = "exec_indicator"
    override val serviceName get() = "guia_attachment_chemotherapy_service"

    suspend fun create(
        request: GuiaAttachmentChemotherapyRequest,
    ): Result<List<MvAuthorizedProcedure>, Throwable>

}
