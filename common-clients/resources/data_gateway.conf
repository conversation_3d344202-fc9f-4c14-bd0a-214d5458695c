systemEnv = "test"
systemEnv = ${?SYSTEM_ENV}

development {
  apiUrl = "https://data-gtw.staging.datalake.alice.tools"
  apiUrl = ${?DATA_GATEWAY_API_URL}

  timeout = 60000
  timeout = ${?DATA_GATEWAY_API_TIMEOUT}
}

test {
  apiUrl = "https://data-gtw.datalake.alice.tools"
  timeout = 60000
}

production {
  apiUrl = "https://data-gtw.staging.datalake.alice.tools"
  apiUrl = ${?DATA_GATEWAY_API_URL}

  timeout = 60000
  timeout = ${?DATA_GATEWAY_API_TIMEOUT}
}
