package br.com.alice.api.ops.services

import br.com.alice.api.ops.models.ProcessTussProcedureSpecialtyError
import br.com.alice.common.MultipartRequest
import br.com.alice.common.RangeUUID
import br.com.alice.common.core.extensions.toUUID
import br.com.alice.common.featureflag.withFeatureFlag
import br.com.alice.common.helpers.coVerifyNone
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.common.models.SpecialistTier
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.FeatureNamespace
import br.com.alice.data.layer.models.HealthSpecialistResourceBundle
import br.com.alice.data.layer.models.HealthSpecialistScoreEnum
import br.com.alice.data.layer.models.TussProcedureSpecialty
import br.com.alice.exec.indicator.client.FindExistingTussProcedureSpecialtyParams
import br.com.alice.exec.indicator.client.HealthSpecialistResourceBundleService
import br.com.alice.exec.indicator.client.TussProcedureSpecialtyService
import com.github.kittinunf.result.success
import io.mockk.clearAllMocks
import io.mockk.coEvery
import io.mockk.mockk
import kotlinx.coroutines.runBlocking
import org.assertj.core.api.Assertions.assertThat
import java.io.File
import java.math.BigDecimal
import java.time.LocalDate
import kotlin.test.BeforeTest
import kotlin.test.Test
import kotlin.test.assertEquals

class ProcessTussProcedureSpecialtyFileServiceTest {

    private val tussProcedureSpecialtyService: TussProcedureSpecialtyService = mockk()
    private val healthSpecialistResourceBundleService: HealthSpecialistResourceBundleService = mockk()

    private val service = ProcessTussProcedureSpecialtyFileService(
        tussProcedureSpecialtyService,
        healthSpecialistResourceBundleService
    )


    private val procedureOne = TestModelFactory.buildTussProcedureSpecialty(
        aliceCode = "0001",
        tussCode = "10101012",
        medicalSpecialtyId = "897de186-9d7d-4780-ab89-c06287985c00".toUUID(),
        healthSpecialistScore = HealthSpecialistScoreEnum.IS_RAISING_THE_BAR,
        tier = SpecialistTier.SUPER_EXPERT,
    )
    private val procedureTwo = TestModelFactory.buildTussProcedureSpecialty(
        aliceCode = "9812345",
        tussCode = "10101012",
        medicalSpecialtyId = "897de186-9d7d-4780-ab89-c06287985c00".toUUID(),
        healthSpecialistScore = HealthSpecialistScoreEnum.IS_RAISING_THE_BAR,
        tier = SpecialistTier.SUPER_EXPERT,
    )

    private val healthSpecialistResourceBundle = TestModelFactory.buildHealthSpecialistResourceBundle(
        id = "897de186-9d7d-4780-ab89-c06287985c00".toUUID(),
        code = "0001",
        primaryTuss = "10101012"
    )


    @BeforeTest
    fun setup() {
        clearAllMocks()
    }

    @Test
    fun `#execute should update old and create new procedure in batch without problems`() = runBlocking {
        val file = File(javaClass.classLoader.getResource("csv-procedure-specialty-successful.csv")!!.path)
        val multipartRequest = MultipartRequest(file = file, parameters = emptyMap(), fileContent = file.inputStream())

        coEvery {
            tussProcedureSpecialtyService
                .findByAliceCodeScoreTierSpecialtyIdBrandAndProductTier(
                    FindExistingTussProcedureSpecialtyParams(
                        aliceCode = procedureOne.aliceCode!!,
                        score = procedureOne.healthSpecialistScore,
                        tier = procedureOne.tier,
                        specialtyId = procedureOne.medicalSpecialtyId!!,
                        brand = procedureOne.brand!!,
                        productTier = procedureOne.productTier!!
                    )
                )
        } returns listOf(procedureOne).success()

        coEvery {
            tussProcedureSpecialtyService.updateList(any())
        } returns listOf(procedureOne.copy(endAt = LocalDate.now().minusDays(1))).success()

        coEvery {
            tussProcedureSpecialtyService.addList(any())
        } returns listOf(procedureOne.copy(price = BigDecimal(1))).success()

        val result = service.execute(multipartRequest, RangeUUID.generate())

        assertThat(result.linesProcessed).isEqualTo(1)
        assertThat(result.errors).isEqualTo(emptyList<ProcessTussProcedureSpecialtyError>())

        coVerifyOnce {
            tussProcedureSpecialtyService
                .updateList(
                    models = match {
                        it.first().id == procedureOne.id && it.first().endAt == LocalDate.now().minusDays(1)
                    })
        }
        coVerifyOnce {
            tussProcedureSpecialtyService
                .addList(
                    models = match {
                        it.first().price == BigDecimal(1) && it.first().beginAt == LocalDate.now() && it.first().aliceCode == procedureOne.aliceCode
                    })
        }
    }


    @Test
    fun `#execute should create new procedure in batch without problems`() = runBlocking {
        val file = File(javaClass.classLoader.getResource("csv-procedure-specialty-successful.csv")!!.path)
        val multipartRequest = MultipartRequest(file = file, parameters = emptyMap(), fileContent = file.inputStream())

        coEvery {
            tussProcedureSpecialtyService
                .findByAliceCodeScoreTierSpecialtyIdBrandAndProductTier(
                    FindExistingTussProcedureSpecialtyParams(
                        aliceCode = procedureOne.aliceCode!!,
                        score = procedureOne.healthSpecialistScore,
                        tier = procedureOne.tier,
                        specialtyId = procedureOne.medicalSpecialtyId!!,
                        brand = procedureOne.brand!!,
                        productTier = procedureOne.productTier!!
                    )
                )
        } returns emptyList<TussProcedureSpecialty>().success()

        coEvery {
            tussProcedureSpecialtyService.addList(any())
        } returns listOf(procedureOne.copy(price = BigDecimal(1))).success()

        val result = service.execute(multipartRequest, RangeUUID.generate())

        assertThat(result.linesProcessed).isEqualTo(1)
        assertThat(result.errors).isEqualTo(emptyList<ProcessTussProcedureSpecialtyError>())

        coVerifyNone { tussProcedureSpecialtyService.updateList(any()) }
        coVerifyOnce {
            tussProcedureSpecialtyService
                .addList(
                    models = match {
                        it.first().price == BigDecimal(1) && it.first().beginAt == LocalDate.now() && it.first().aliceCode == procedureOne.aliceCode
                    })
        }
    }


    @Test
    fun `#execute should get error if any column was not sent`() = runBlocking {
        val file = File(javaClass.classLoader.getResource("csv-procedure-specialty-column-error.csv")!!.path)
        val multipartRequest = MultipartRequest(file = file, parameters = emptyMap(), fileContent = file.inputStream())

        val result = service.execute(multipartRequest, RangeUUID.generate())

        assertThat(result.linesProcessed).isEqualTo(0)
        assertThat(result.errors).isNotEqualTo(emptyList<ProcessTussProcedureSpecialtyError>())

        coVerifyNone {
            tussProcedureSpecialtyService.findByAliceCodeScoreTierSpecialtyIdBrandAndProductTier(
                any()
            )
        }
        coVerifyNone { tussProcedureSpecialtyService.updateList(any()) }
        coVerifyNone { tussProcedureSpecialtyService.addList(any()) }
    }

    @Test
    fun `#execute should get error if any column is null or empty`() = runBlocking {
        val file = File(javaClass.classLoader.getResource("csv-procedure-specialty-column-null-error.csv")!!.path)
        val multipartRequest = MultipartRequest(file = file, parameters = emptyMap(), fileContent = file.inputStream())

        val result = service.execute(multipartRequest, RangeUUID.generate())

        assertThat(result.linesProcessed).isEqualTo(0)
        assertThat(result.errors).isNotEqualTo(emptyList<ProcessTussProcedureSpecialtyError>())

        coVerifyNone {
            tussProcedureSpecialtyService.findByAliceCodeScoreTierSpecialtyIdBrandAndProductTier(
                any()
            )
        }
        coVerifyNone { tussProcedureSpecialtyService.updateList(any()) }
        coVerifyNone { tussProcedureSpecialtyService.addList(any()) }
    }

    @Test
    fun `#execute should get error if any column is invalid`() = runBlocking {
        val file = File(javaClass.classLoader.getResource("csv-procedure-specialty-column-invalid-error.csv")!!.path)
        val multipartRequest = MultipartRequest(file = file, parameters = emptyMap(), fileContent = file.inputStream())

        val result = service.execute(multipartRequest, RangeUUID.generate())

        assertThat(result.linesProcessed).isEqualTo(0)
        assertThat(result.errors).isNotEqualTo(emptyList<ProcessTussProcedureSpecialtyError>())

        coVerifyNone {
            tussProcedureSpecialtyService.findByAliceCodeScoreTierSpecialtyIdBrandAndProductTier(
                any()
            )
        }
        coVerifyNone { tussProcedureSpecialtyService.updateList(any()) }
        coVerifyNone { tussProcedureSpecialtyService.addList(any()) }
    }

    @Test
    fun `#execute should update old and create new procedure in batch without problems when feature flag to check if alice code is valid is active`() = runBlocking {
        val file = File(javaClass.classLoader.getResource("csv-procedure-specialty-successful.csv")!!.path)
        val multipartRequest = MultipartRequest(file = file, parameters = emptyMap(), fileContent = file.inputStream())

        coEvery {
            healthSpecialistResourceBundleService.findByCodes(listOf("0001"))
        } returns listOf(healthSpecialistResourceBundle).success()

        coEvery {
            tussProcedureSpecialtyService
                .findByAliceCodeScoreTierSpecialtyIdBrandAndProductTier(
                    FindExistingTussProcedureSpecialtyParams(
                        aliceCode = procedureOne.aliceCode!!,
                        score = procedureOne.healthSpecialistScore,
                        tier = procedureOne.tier,
                        specialtyId = procedureOne.medicalSpecialtyId!!,
                        brand = procedureOne.brand!!,
                        productTier = procedureOne.productTier!!
                    )
                )
        } returns listOf(procedureOne).success()

        coEvery {
            tussProcedureSpecialtyService.updateList(any())
        } returns listOf(procedureOne.copy(endAt = LocalDate.now().minusDays(1))).success()

        coEvery {
            tussProcedureSpecialtyService.addList(match {
                it.first().healthSpecialistResourceBundleId == healthSpecialistResourceBundle.id
            })
        } returns listOf(procedureOne.copy(
            price = BigDecimal(1)
        )).success()

        withFeatureFlag(FeatureNamespace.EXEC_INDICATOR, "validate_tuss_procedure_specialty_code", true) {
            val result = service.execute(multipartRequest, RangeUUID.generate())

            assertThat(result.linesProcessed).isEqualTo(1)
            assertThat(result.errors).isEqualTo(emptyList<ProcessTussProcedureSpecialtyError>())
        }

        coVerifyOnce {
            tussProcedureSpecialtyService
                .updateList(
                    models = match {
                        it.first().id == procedureOne.id && it.first().endAt == LocalDate.now().minusDays(1)
                    })
        }
        coVerifyOnce {
            tussProcedureSpecialtyService
                .addList(
                    models = match {
                        it.first().price == BigDecimal(1) && it.first().beginAt == LocalDate.now() && it.first().aliceCode == procedureOne.aliceCode
                                && it.first().healthSpecialistResourceBundleId == healthSpecialistResourceBundle.id
                    })
        }
        coVerifyOnce {
            healthSpecialistResourceBundleService.findByCodes(listOf("0001"))
        }
    }

    @Test
    fun `#execute should update old and create new procedure in batch without problems when feature flag to check if alice code is valid is active with redundancy for old alice codes`() = runBlocking {
        val file = File(javaClass.classLoader.getResource("csv-procedure-specialty-successful.csv")!!.path)
        val multipartRequest = MultipartRequest(file = file, parameters = emptyMap(), fileContent = file.inputStream())

        coEvery {
            healthSpecialistResourceBundleService.findByCodes(listOf("0001"))
        } returns listOf(healthSpecialistResourceBundle.copy(tempOldAliceCode = "9812345")).success()

        coEvery {
            tussProcedureSpecialtyService
                .findByAliceCodeScoreTierSpecialtyIdBrandAndProductTier(
                    FindExistingTussProcedureSpecialtyParams(
                        aliceCode = procedureOne.aliceCode!!,
                        score = procedureOne.healthSpecialistScore,
                        tier = procedureOne.tier,
                        specialtyId = procedureOne.medicalSpecialtyId!!,
                        brand = procedureOne.brand!!,
                        productTier = procedureOne.productTier!!
                    )
                )
        } returns listOf(procedureOne).success()

        coEvery {
            tussProcedureSpecialtyService
                .findByAliceCodeScoreTierSpecialtyIdBrandAndProductTier(
                    FindExistingTussProcedureSpecialtyParams(
                        aliceCode = "9812345",
                        score = procedureOne.healthSpecialistScore,
                        tier = procedureOne.tier,
                        specialtyId = procedureOne.medicalSpecialtyId!!,
                        brand = procedureOne.brand!!,
                        productTier = procedureOne.productTier!!
                    )
                )
        } returns listOf(procedureTwo).success()

        coEvery {
            tussProcedureSpecialtyService.updateList(match { it.first().aliceCode == "9812345" })
        } returns listOf(procedureOne.copy(endAt = LocalDate.now().minusDays(1))).success()

        coEvery {
            tussProcedureSpecialtyService.updateList(match { it.first().aliceCode == procedureOne.aliceCode })
        } returns listOf(procedureOne.copy(endAt = LocalDate.now().minusDays(1))).success()

        coEvery {
            tussProcedureSpecialtyService.addList(match {
                it.first().healthSpecialistResourceBundleId == healthSpecialistResourceBundle.id &&
                        it.first().aliceCode == procedureOne.aliceCode
            })
        } returns listOf(procedureOne.copy(
            price = BigDecimal(1)
        )).success()

        coEvery {
            tussProcedureSpecialtyService.addList(match {
                it.first().healthSpecialistResourceBundleId == null &&
                        it.first().aliceCode == "9812345"
            })
        } returns listOf(procedureOne.copy(
            price = BigDecimal(1)
        )).success()

        withFeatureFlag(FeatureNamespace.EXEC_INDICATOR, "validate_tuss_procedure_specialty_code", true) {
            val result = service.execute(multipartRequest, RangeUUID.generate())

            assertThat(result.linesProcessed).isEqualTo(2)
            assertThat(result.errors).isEqualTo(emptyList<ProcessTussProcedureSpecialtyError>())
        }

        coVerifyOnce {
            tussProcedureSpecialtyService
                .updateList(
                    models = match {
                        it.first().id == procedureOne.id && it.first().endAt == LocalDate.now().minusDays(1) &&
                                it[1].id == procedureTwo.id && it[1].endAt == LocalDate.now().minusDays(1)
                    })
        }
        coVerifyOnce {
            tussProcedureSpecialtyService
                .addList(
                    models = match {
                        it.first().price == BigDecimal(1) && it.first().beginAt == LocalDate.now() && it.first().aliceCode == procedureOne.aliceCode
                                && it.first().healthSpecialistResourceBundleId == healthSpecialistResourceBundle.id &&
                                it[1].price == BigDecimal(1)
                                && it[1].beginAt == LocalDate.now() && it[1].aliceCode == "9812345"
                                && it[1].healthSpecialistResourceBundleId == null
                    })
        }
        coVerifyOnce {
            healthSpecialistResourceBundleService.findByCodes(listOf("0001"))
        }
    }

    @Test
    fun `#execute should get error if flag to add only items that code is on database is active and resource code is not on the database`() = runBlocking {
        val file = File(javaClass.classLoader.getResource("csv-procedure-specialty-successful.csv")!!.path)
        val multipartRequest = MultipartRequest(file = file, parameters = emptyMap(), fileContent = file.inputStream())

        coEvery {
            healthSpecialistResourceBundleService.findByCodes(listOf("0001"))
        } returns emptyList<HealthSpecialistResourceBundle>().success()

        withFeatureFlag(FeatureNamespace.EXEC_INDICATOR, "validate_tuss_procedure_specialty_code", true) {
            val result = service.execute(multipartRequest, RangeUUID.generate())

            assertThat(result.linesProcessed).isEqualTo(0)
            assertThat(result.errors).isNotEqualTo(emptyList<ProcessTussProcedureSpecialtyError>())
            assertEquals(result.errors.first().message, "Código/pacote de especialista 0001 não encontrado no banco de dados, verifique se está cadastrado")
            assertEquals(result.errors.first().line, 1)
        }

        coVerifyNone {
            tussProcedureSpecialtyService.findByAliceCodeScoreTierSpecialtyIdBrandAndProductTier(
                any()
            )
        }
        coVerifyNone { tussProcedureSpecialtyService.updateList(any()) }
        coVerifyNone { tussProcedureSpecialtyService.addList(any()) }
        coVerifyOnce {
            healthSpecialistResourceBundleService.findByCodes(listOf("0001"))
        }
    }
}
