package br.com.alice.api.backoffice.mappers.staff

import br.com.alice.api.backoffice.transfers.staff.StaffFullResponse
import br.com.alice.api.backoffice.transfers.staff.StaffShortResponse
import br.com.alice.common.transfers.ListPaginatedResponse
import br.com.alice.common.convertTo
import br.com.alice.data.layer.models.Staff
import io.ktor.http.Parameters
import br.com.alice.common.mappers.CommonOutputMapper

object StaffOutputMapper {
    fun toFullResponse(staff: Staff): StaffFullResponse =
        staff.convertTo(StaffFullResponse::class)

    fun toPaginatedResponse(staffs: List<Staff>, total: Int, queryParams: Parameters): ListPaginatedResponse<StaffShortResponse> =
        ListPaginatedResponse(
            pagination = CommonOutputMapper.toPaginationResponse(queryParams, total),
            results = staffs.map { it.convertTo(StaffShortResponse::class) }
        )
}
