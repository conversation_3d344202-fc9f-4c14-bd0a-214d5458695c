package br.com.alice.onboarding.client

import br.com.alice.common.core.PersonId
import br.com.alice.common.core.exceptions.BadRequestException
import br.com.alice.common.rfc.RemoteService
import br.com.alice.common.rfc.Service
import br.com.alice.data.layer.models.ChecklistItem
import br.com.alice.data.layer.models.OnboardingBackgroundCheck
import com.github.kittinunf.result.Result
import java.util.UUID

@RemoteService
interface BackgroundCheckReporter : Service {

    override val namespace get() = "onboarding"
    override val serviceName get() = "background_check_reporter"

    suspend fun startProcess(personId: PersonId): Result<OnboardingBackgroundCheck, Throwable>

    suspend fun generateReport(reportId: String): Result<OnboardingBackgroundCheck, Throwable>

    suspend fun findByPerson(personId: PersonId): Result<OnboardingBackgroundCheck, Throwable>

    suspend fun finish(personId: PersonId, notes: String, checklist: List<ChecklistItem>): Result<OnboardingBackgroundCheck, Throwable>
}

class UnprocessedBackgroundCheckException(
    message: String,
    code: String = "unprocessed_background_check_error",
    cause: Throwable? = null
) : BadRequestException(message, code, cause) {
    constructor(backgroundCheckId: UUID) : this(
        message = "background check $backgroundCheckId not processed"
    )
}
