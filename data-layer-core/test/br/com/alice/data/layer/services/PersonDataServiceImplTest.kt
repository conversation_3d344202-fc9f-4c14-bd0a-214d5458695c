package br.com.alice.data.layer.services

import br.com.alice.common.core.Model
import br.com.alice.common.core.exceptions.DuplicatedItemException
import br.com.alice.common.data.dsl.matchers.ResultAssert.Companion.assertThat
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.common.helpers.verifyOnce
import br.com.alice.data.layer.helpers.DataLayerTestModelFactory
import br.com.alice.data.layer.models.PersonModel
import br.com.alice.data.layer.pipelines.DatabasePipeline
import br.com.alice.data.layer.pipelines.DatabasePipelineFactory
import br.com.alice.data.layer.tables.PersonTable
import br.com.alice.data.layer.tables.Table
import com.github.kittinunf.result.failure
import com.github.kittinunf.result.success
import io.mockk.called
import io.mockk.coEvery
import io.mockk.confirmVerified
import io.mockk.every
import io.mockk.mockk
import io.mockk.verify
import kotlinx.coroutines.runBlocking
import kotlin.reflect.KClass
import kotlin.test.AfterTest
import kotlin.test.Test
import br.com.alice.data.layer.exceptions.DuplicatedItemException as DBDuplicatedItemException

class PersonDataServiceImplTest {

    private val factory: DatabasePipelineFactory = mockk()
    private val personTokenService: PersonTokenService = mockk()

    private val dataBasePipeline: DatabasePipeline<PersonModel> = mockk()
    private val personToken: PersonToken = mockk()
    private val personModel: PersonModel = mockk()

    private val person = DataLayerTestModelFactory.buildPerson()
    private val personToReturn = DataLayerTestModelFactory.buildPerson()

    @AfterTest
    fun confirmMocks() = confirmVerified(
        factory,
        personTokenService,
        dataBasePipeline,
        personToken,
        personModel
    )

    @Test
    fun `#add sanitize model before add ann created person token`() = runBlocking {
        every { factory.get(PersonModel::class, PersonTable::class) } returns dataBasePipeline
        every { personModel.sanitize() } returns person

        every { personTokenService.createForPersonId(person.id) } returns personToken.success()
        coEvery { dataBasePipeline.add(person) } returns personToReturn.success()

        val personDataService = PersonDataServiceImpl(factory, personTokenService)

        val result = personDataService.add(personModel)
        assertThat(result).isSuccessWithData(personToReturn)

        verifyOnce { personModel.sanitize() }
        verifyOnce { factory.get(any<KClass<Model>>(), any<KClass<Table<*>>>()) }
        verifyOnce { personTokenService.createForPersonId(any()) }
        coVerifyOnce { dataBasePipeline.add(any()) }
    }

    @Test
    fun `#add convert and return duplicated exception`() = runBlocking {
        every { factory.get(PersonModel::class, PersonTable::class) } returns dataBasePipeline
        every { personModel.sanitize() } returns person

        every { personTokenService.createForPersonId(person.id) } returns personToken.success()
        coEvery { dataBasePipeline.add(person) } returns DBDuplicatedItemException("my duplicated").failure()

        val personDataService = PersonDataServiceImpl(factory, personTokenService)

        val result = personDataService.add(personModel)
        assertThat(result)
            .fails()
            .withMessage("Duplicated personId=${person.id}")
            .ofType(DuplicatedItemException::class)

        verifyOnce { personModel.sanitize() }
        verifyOnce { factory.get(any<KClass<Model>>(), any<KClass<Table<*>>>()) }
        verifyOnce { personTokenService.createForPersonId(any()) }
        coVerifyOnce { dataBasePipeline.add(any()) }
    }

    @Test
    fun `#add returns exception when it not is a DuplicatedItemException`() = runBlocking {
        every { factory.get(PersonModel::class, PersonTable::class) } returns dataBasePipeline
        every { personModel.sanitize() } returns person

        every { personTokenService.createForPersonId(person.id) } returns personToken.success()
        coEvery { dataBasePipeline.add(person) } returns Exception("my duplicated").failure()

        val personDataService = PersonDataServiceImpl(factory, personTokenService)

        val result = personDataService.add(personModel)
        assertThat(result)
            .fails()
            .withMessage("my duplicated")
            .ofType(Exception::class)

        verifyOnce { personModel.sanitize() }
        verifyOnce { factory.get(any<KClass<Model>>(), any<KClass<Table<*>>>()) }
        verifyOnce { personTokenService.createForPersonId(any()) }
        coVerifyOnce { dataBasePipeline.add(any()) }
    }

    @Test
    fun `#update sanitize model before update`() = runBlocking {
        every { factory.get(PersonModel::class, PersonTable::class) } returns dataBasePipeline
        every { personModel.sanitize() } returns person

        coEvery { dataBasePipeline.update(person) } returns personToReturn.success()

        val personDataService = PersonDataServiceImpl(factory, personTokenService)

        val result = personDataService.update(personModel)
        assertThat(result).isSuccessWithData(personToReturn)

        verifyOnce { personModel.sanitize() }
        verifyOnce { factory.get(any<KClass<Model>>(), any<KClass<Table<*>>>()) }
        coVerifyOnce { dataBasePipeline.update(any()) }
        verify { personTokenService wasNot called }
    }

    @Test
    fun `#get wrap id into PersonId to get model`() = runBlocking {
        every { factory.get(PersonModel::class, PersonTable::class) } returns dataBasePipeline

        coEvery { dataBasePipeline.get(person.id) } returns personToReturn.success()

        val personDataService = PersonDataServiceImpl(factory, personTokenService)

        val result = personDataService.get(person.id.id)
        assertThat(result).isSuccessWithData(personToReturn)

        verifyOnce { factory.get(any<KClass<Model>>(), any<KClass<Table<*>>>()) }
        coVerifyOnce { dataBasePipeline.get(any()) }
        verify { personTokenService wasNot called }
    }
}
