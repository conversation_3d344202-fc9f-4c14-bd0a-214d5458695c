package br.com.alice.person.services

import br.com.alice.authentication.UserType
import br.com.alice.authentication.UserType.MEMBER
import br.com.alice.authentication.UserType.NON_MEMBER
import br.com.alice.business.client.BeneficiaryService
import br.com.alice.common.core.exceptions.NotFoundException
import br.com.alice.common.data.dsl.matchers.ResultAssert.Companion.assertThat
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.Beneficiary
import br.com.alice.data.layer.models.BeneficiaryOnboardingPhaseType
import br.com.alice.data.layer.models.Member
import br.com.alice.data.layer.models.MemberStatus
import br.com.alice.person.client.MemberService
import com.github.kittinunf.result.failure
import com.github.kittinunf.result.success
import io.mockk.clearAllMocks
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.mockk
import kotlinx.coroutines.runBlocking
import kotlin.test.AfterTest
import kotlin.test.BeforeTest
import kotlin.test.Test

class UserTypeServiceImplTest {

    private val memberService: MemberService = mockk()
    private val beneficiaryService: BeneficiaryService = mockk()
    private val service = UserTypeServiceImpl(memberService, beneficiaryService)

    private val person = TestModelFactory.buildPerson()
    private val personIds = listOf(person.id)

    @BeforeTest
    fun setup() {
        clearAllMocks()
    }

    @AfterTest
    fun clear() {
        clearAllMocks()
    }

    @Test
    fun `#get should return UserType MEMBER when Person has an active Membership and it is a Beneficiary that finished onboarding and FF is true`() = runBlocking {
        val userType = MEMBER

        val member = TestModelFactory.buildMember(personId = person.id, status = MemberStatus.ACTIVE)
        val finishedOnboarding = TestModelFactory.buildBeneficiaryOnboarding(phases = listOf(TestModelFactory.buildBeneficiaryOnboardingPhase(phase = BeneficiaryOnboardingPhaseType.FINISHED)))
        val beneficiary = TestModelFactory.buildBeneficiary(personId = person.id).copy(onboarding = finishedOnboarding)

        coEvery { memberService.findActiveOrPendingMembership(personId = person.id) } returns member.success()
        coEvery { beneficiaryService.findByMemberId(member.id, BeneficiaryService.FindOptions(withOnboarding = true)) } returns beneficiary.success()

        val result = service.get(person.id)
        assertThat(result).isSuccessWithData(userType)

        coVerify(exactly = 1) { memberService.findActiveOrPendingMembership(personId = person.id) }
        coVerify(exactly = 1) { beneficiaryService.findByMemberId(member.id,
            BeneficiaryService.FindOptions(withOnboarding = true)
        ) }
    }

    @Test
    fun `#get should return UserType BENEFICIARY when Person has an active Membership and it is a Beneficiary that did not finished onboarding and FF is true`() = runBlocking {
        val userType = UserType.BENEFICIARY

        val member = TestModelFactory.buildMember(personId = person.id, status = MemberStatus.ACTIVE)
        val onboarding = TestModelFactory.buildBeneficiaryOnboarding(phases = listOf(TestModelFactory.buildBeneficiaryOnboardingPhase(phase = BeneficiaryOnboardingPhaseType.REGISTRATION)))
        val beneficiary = TestModelFactory.buildBeneficiary(personId = person.id).copy(onboarding = onboarding)

        coEvery { memberService.findActiveOrPendingMembership(personId = person.id) } returns member.success()
        coEvery { beneficiaryService.findByMemberId(member.id, BeneficiaryService.FindOptions(withOnboarding = true)) } returns beneficiary.success()

        val result = service.get(person.id)
        assertThat(result).isSuccessWithData(userType)

        coVerify(exactly = 1) { memberService.findActiveOrPendingMembership(personId = person.id) }
        coVerify(exactly = 1) { beneficiaryService.findByMemberId(member.id,
            BeneficiaryService.FindOptions(withOnboarding = true)
        ) }
    }

    @Test
    fun `#get should return UserType NON_MEMBER when Person has no Membership and it is not a Beneficiary`() = runBlocking {
        val userType = NON_MEMBER

        coEvery { memberService.findActiveOrPendingMembership(personId = person.id) } returns NotFoundException("Memberhsip not found").failure()

        val result = service.get(person.id)
        assertThat(result).isSuccessWithData(userType)

        coVerify(exactly = 1) { memberService.findActiveOrPendingMembership(personId = person.id) }
        coVerify(exactly = 0) { beneficiaryService.findByMemberId(any()) }
    }

    @Test
    fun `#get should return UserType NON_MEMBER when Person has an Membership but inactive and it is not a Beneficiary`() = runBlocking {
        val userType = NON_MEMBER
        val member = TestModelFactory.buildMember(personId = person.id, status = MemberStatus.PENDING)

        coEvery { memberService.findActiveOrPendingMembership(personId = person.id) } returns member.success()
        coEvery { beneficiaryService.findByMemberId(member.id, BeneficiaryService.FindOptions(withOnboarding = true)) } returns NotFoundException().failure()

        val result = service.get(person.id)
        assertThat(result).isSuccessWithData(userType)

        coVerify(exactly = 1) { memberService.findActiveOrPendingMembership(personId = person.id) }
        coVerify(exactly = 1) { beneficiaryService.findByMemberId(member.id,
            BeneficiaryService.FindOptions(withOnboarding = true)
        ) }
    }

    @Test
    fun `#get should return UserType BENEFICIARY when Person has an Membership but inactive and it is a Beneficiary`() = runBlocking {
        val userType = UserType.BENEFICIARY
        val member = TestModelFactory.buildMember(personId = person.id, status = MemberStatus.PENDING)
        val finishedOnboarding = TestModelFactory.buildBeneficiaryOnboarding(phases = listOf(TestModelFactory.buildBeneficiaryOnboardingPhase()))
        val beneficiary = TestModelFactory.buildBeneficiary(personId = person.id).copy(onboarding = finishedOnboarding)

        coEvery { memberService.findActiveOrPendingMembership(personId = person.id) } returns member.success()
        coEvery { beneficiaryService.findByMemberId(member.id, BeneficiaryService.FindOptions(withOnboarding = true)) } returns beneficiary.success()

        val result = service.get(person.id)
        assertThat(result).isSuccessWithData(userType)

        coVerify(exactly = 1) { memberService.findActiveOrPendingMembership(personId = person.id) }
        coVerify(exactly = 1) { beneficiaryService.findByMemberId(member.id,
            BeneficiaryService.FindOptions(withOnboarding = true)
        ) }
    }

    @Test
    fun `#get should return UserType NON_MEMBER when Person has no Membership and it is a Beneficiary`() = runBlocking {
        val userType = NON_MEMBER

        coEvery { memberService.findActiveOrPendingMembership(personId = person.id) } returns NotFoundException("Membership not found").failure()

        val result = service.get(person.id)
        assertThat(result).isSuccessWithData(userType)

        coVerify(exactly = 1) { memberService.findActiveOrPendingMembership(personId = person.id) }
        coVerify(exactly = 0) { beneficiaryService.findByMemberId(any()) }
    }

    @Test
    fun `#getByIds should return UserType MEMBER when Person has an active Membership and it is a Beneficiary that finished onboarding`() = runBlocking {
        val userType = MEMBER
        val expected = listOf(person.id to userType)
        val member = TestModelFactory.buildMember(personId = person.id, status = MemberStatus.ACTIVE)
        val finishedOnboarding = TestModelFactory.buildBeneficiaryOnboarding(phases = listOf(TestModelFactory.buildBeneficiaryOnboardingPhase(phase = BeneficiaryOnboardingPhaseType.FINISHED)))
        val beneficiary = TestModelFactory.buildBeneficiary(personId = person.id).copy(onboarding = finishedOnboarding)

        coEvery {
            memberService.findActiveOrPendingMembersByPersonIds(personIds)
        } returns listOf(member).success()
        coEvery {
            beneficiaryService.findByMemberIds(listOf(member.id), BeneficiaryService.FindOptions(withOnboarding = true))
        } returns listOf(beneficiary).success()

        val result = service.getByIds(personIds)
        assertThat(result).isSuccessWithData(expected)

        coVerify(exactly = 1) { memberService.findActiveOrPendingMembersByPersonIds(personIds) }
        coVerify(exactly = 1) { beneficiaryService.findByMemberIds(listOf(member.id),
            BeneficiaryService.FindOptions(withOnboarding = true)
        ) }
    }

    @Test
    fun `#getByIds should return UserType NON_MEMBER when Person has no Membership and it is not a Beneficiary`() = runBlocking {
        val userType = NON_MEMBER
        val expected = listOf(person.id to userType)

        coEvery {
            memberService.findActiveOrPendingMembersByPersonIds(personIds)
        } returns emptyList<Member>().success()

        val result = service.getByIds(personIds)
        assertThat(result).isSuccessWithData(expected)

        coVerify(exactly = 1) { memberService.findActiveOrPendingMembersByPersonIds(personIds) }
        coVerify(exactly = 0) { beneficiaryService.findByMemberIds(any()) }
    }

    @Test
    fun `#getByIds should return UserType NON_MEMBER when Person has an Membership but inactive and it is not a Beneficiary`() = runBlocking {
        val userType = NON_MEMBER
        val expected = listOf(person.id to userType)

        val member = TestModelFactory.buildMember(personId = person.id, status = MemberStatus.PENDING)
        coEvery {
            memberService.findActiveOrPendingMembersByPersonIds(personIds)
        } returns listOf(member).success()
        coEvery {
            beneficiaryService.findByMemberIds(listOf(member.id), BeneficiaryService.FindOptions(withOnboarding = true))
        } returns emptyList<Beneficiary>().success()

        val result = service.getByIds(personIds)
        assertThat(result).isSuccessWithData(expected)

        coVerify(exactly = 1) { memberService.findActiveOrPendingMembersByPersonIds(personIds) }
        coVerify(exactly = 1) { beneficiaryService.findByMemberIds(listOf(member.id),
            BeneficiaryService.FindOptions(withOnboarding = true)
        ) }
    }

    @Test
    fun `#getByIds should return UserType BENEFICIARY when Person has an Membership but inactive and it is a Beneficiary`() = runBlocking {
        val userType = UserType.BENEFICIARY
        val expected = listOf(person.id to userType)
        val finishedOnboarding = TestModelFactory.buildBeneficiaryOnboarding(phases = listOf(TestModelFactory.buildBeneficiaryOnboardingPhase()))
        val beneficiary = TestModelFactory.buildBeneficiary(personId = person.id).copy(onboarding = finishedOnboarding)

        val member = TestModelFactory.buildMember(personId = person.id, status = MemberStatus.PENDING)
        coEvery {
            memberService.findActiveOrPendingMembersByPersonIds(personIds)
        } returns listOf(member).success()
        coEvery {
            beneficiaryService.findByMemberIds(listOf(member.id), BeneficiaryService.FindOptions(withOnboarding = true))
        } returns listOf(beneficiary).success()

        val result = service.getByIds(personIds)
        assertThat(result).isSuccessWithData(expected)

        coVerify(exactly = 1) { memberService.findActiveOrPendingMembersByPersonIds(personIds) }
        coVerify(exactly = 1) { beneficiaryService.findByMemberIds(listOf(member.id),
            BeneficiaryService.FindOptions(withOnboarding = true)
        ) }
    }

    @Test
    fun `#getByIds should return UserType BENEFICIARY when Person has no Membership and it is a Beneficiary`() = runBlocking {
        val userType = UserType.BENEFICIARY
        val expected = listOf(person.id to userType)
        val finishedOnboarding = TestModelFactory.buildBeneficiaryOnboarding(phases = listOf(TestModelFactory.buildBeneficiaryOnboardingPhase()))
        val beneficiary = TestModelFactory.buildBeneficiary(personId = person.id).copy(onboarding = finishedOnboarding)

        val member = TestModelFactory.buildMember(personId = person.id, status = MemberStatus.PENDING)
        coEvery {
            memberService.findActiveOrPendingMembersByPersonIds(personIds)
        } returns listOf(member).success()
        coEvery {
            beneficiaryService.findByPersonIds(personIds)
        } returns emptyList<Beneficiary>().success()
        coEvery {
            beneficiaryService.findByMemberIds(listOf(member.id), BeneficiaryService.FindOptions(withOnboarding = true))
        } returns listOf(beneficiary).success()

        val result = service.getByIds(personIds)
        assertThat(result).isSuccessWithData(expected)

        coVerify(exactly = 1) { memberService.findActiveOrPendingMembersByPersonIds(personIds) }
        coVerify(exactly = 1) { beneficiaryService.findByMemberIds(listOf(member.id),
            BeneficiaryService.FindOptions(withOnboarding = true)
        ) }
    }
}
