package br.com.alice.staff.consumers

import br.com.alice.clinicalaccount.event.PersonClinicalAccountDeleteEvent
import br.com.alice.clinicalaccount.event.PersonHealthcareTeamAssociationUpdatedEvent
import br.com.alice.common.data.dsl.matchers.ResultAssert.Companion.assertThat
import br.com.alice.common.featureflag.withFeatureFlag
import br.com.alice.common.helpers.coVerifyNone
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.common.models.Sex
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.DemographicCount
import br.com.alice.data.layer.models.FeatureNamespace
import br.com.alice.person.client.PersonService
import br.com.alice.staff.client.HealthcareTeamFilters
import br.com.alice.staff.client.HealthcareTeamService
import com.github.kittinunf.result.failure
import com.github.kittinunf.result.success
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.mockk
import kotlinx.coroutines.runBlocking
import java.time.LocalDateTime
import kotlin.test.Test

class PersonClinicalAccountConsumerTest : ConsumerTest() {

    private val healthcareTeamService: HealthcareTeamService = mockk()
    private val personService: PersonService = mockk()

    private val consumer = PersonClinicalAccountConsumer(healthcareTeamService, personService)

    private val person = TestModelFactory.buildPerson(sex = Sex.MALE)
    private val healthcareTeam1 = TestModelFactory.buildHealthcareTeam()
    private val healthcareTeam2 = TestModelFactory.buildHealthcareTeam()
        .copy(
            demographicCount = DemographicCount(
                male = 1,
                female = 0
            )
        )
    private val personClinicalAccount = TestModelFactory.buildPersonClinicalAccount()


    @Test
    fun `#addMemberCount - should increment in new HealthcareTeam`() = runBlocking {
        withFeatureFlag(
            FeatureNamespace.EHR,
            "allow_person_clinical_account_consumer",
            true
        ) {
            val event = PersonHealthcareTeamAssociationUpdatedEvent(
                personId = person.id,
                newHealthcareTeamId = healthcareTeam1.id,
                updatedAt = LocalDateTime.now(),
            )

            val expected = healthcareTeam1.copy(
                demographicCount = DemographicCount(
                    male = 1,
                    female = 0
                )
            )

            coEvery {
                healthcareTeamService.findBy(
                    HealthcareTeamFilters(
                        ids = listOf(healthcareTeam1.id),
                        active = true
                    )
                )
            } returns listOf(healthcareTeam1).success()

            coEvery { personService.get(person.id) } returns person.success()

            coEvery { healthcareTeamService.update(expected) } returns expected.success()

            val result = consumer.addMemberCount(event)
            assertThat(result).isSuccessWithData(expected)

            coVerifyOnce {
                healthcareTeamService.findBy(any())
                personService.get(any())
                healthcareTeamService.update(any())
            }
        }
    }

    @Test
    fun `#addMemberCount - should increment in new HealthcareTeam and decrement in old`() = runBlocking {
        withFeatureFlag(
            FeatureNamespace.EHR,
            "allow_person_clinical_account_consumer",
            true
        ) {
            val event = PersonHealthcareTeamAssociationUpdatedEvent(
                personId = person.id,
                newHealthcareTeamId = healthcareTeam1.id,
                previousHealthcareTeamId = healthcareTeam2.id,
                updatedAt = LocalDateTime.now(),
            )

            val expected = healthcareTeam1.copy(
                demographicCount = DemographicCount(
                    male = 1,
                    female = 0
                )
            )

            val expected2 = healthcareTeam2.copy(
                demographicCount = DemographicCount(
                    male = 0,
                    female = 0
                )
            )

            coEvery {
                healthcareTeamService.findBy(
                    HealthcareTeamFilters(
                        ids = listOf(healthcareTeam1.id, healthcareTeam2.id),
                        active = true
                    )
                )
            } returns listOf(healthcareTeam1, healthcareTeam2).success()

            coEvery { personService.get(person.id) } returns person.success()

            coEvery { healthcareTeamService.update(expected2) } returns expected2.success()
            coEvery { healthcareTeamService.update(expected) } returns expected.success()

            val result = consumer.addMemberCount(event)
            assertThat(result).isSuccessWithData(expected)

            coVerifyOnce {
                healthcareTeamService.findBy(any())
                personService.get(any())
            }

            coVerify(exactly = 2) {
                healthcareTeamService.update(any())
            }
        }
    }

    @Test
    fun `#addMemberCount - should increment from FEMALE in new HealthcareTeam`() = runBlocking {
        withFeatureFlag(
            FeatureNamespace.EHR,
            "allow_person_clinical_account_consumer",
            true
        ) {
            val event = PersonHealthcareTeamAssociationUpdatedEvent(
                personId = person.id,
                newHealthcareTeamId = healthcareTeam1.id,
                updatedAt = LocalDateTime.now(),
            )
            val person = person.copy(sex = Sex.FEMALE)
            val expected = healthcareTeam1.copy(
                demographicCount = DemographicCount(
                    male = 0,
                    female = 1
                )
            )

            coEvery {
                healthcareTeamService.findBy(
                    HealthcareTeamFilters(
                        ids = listOf(healthcareTeam1.id),
                        active = true
                    )
                )
            } returns listOf(healthcareTeam1).success()

            coEvery { personService.get(person.id) } returns person.success()

            coEvery { healthcareTeamService.update(expected) } returns expected.success()

            val result = consumer.addMemberCount(event)
            assertThat(result).isSuccessWithData(expected)

            coVerifyOnce {
                healthcareTeamService.findBy(any())
                personService.get(any())
                healthcareTeamService.update(any())
            }
        }
    }

    @Test
    fun `#addMemberCount - should increment by FEMALE in new HealthcareTeam and decrement in old`() = runBlocking {
        withFeatureFlag(
            FeatureNamespace.EHR,
            "allow_person_clinical_account_consumer",
            true
        ) {
            val event = PersonHealthcareTeamAssociationUpdatedEvent(
                personId = person.id,
                newHealthcareTeamId = healthcareTeam1.id,
                previousHealthcareTeamId = healthcareTeam2.id,
                updatedAt = LocalDateTime.now(),
            )

            val healthcareTeam2 = healthcareTeam2.copy(
                demographicCount = DemographicCount(
                    male = 0,
                    female = 1
                )
            )

            val person = person.copy(sex = Sex.FEMALE)

            val expected = healthcareTeam1.copy(
                demographicCount = DemographicCount(
                    male = 0,
                    female = 1
                )
            )

            val expected2 = healthcareTeam2.copy(
                demographicCount = DemographicCount(
                    male = 0,
                    female = 0
                )
            )

            coEvery {
                healthcareTeamService.findBy(
                    HealthcareTeamFilters(
                        ids = listOf(healthcareTeam1.id, healthcareTeam2.id),
                        active = true
                    )
                )
            } returns listOf(healthcareTeam1, healthcareTeam2).success()

            coEvery { personService.get(person.id) } returns person.success()

            coEvery { healthcareTeamService.update(expected2) } returns expected2.success()
            coEvery { healthcareTeamService.update(expected) } returns expected.success()

            val result = consumer.addMemberCount(event)
            assertThat(result).isSuccessWithData(expected)

            coVerifyOnce {
                healthcareTeamService.findBy(any())
                personService.get(any())
            }

            coVerify(exactly = 2) {
                healthcareTeamService.update(any())
            }
        }
    }

    @Test
    fun `#addMemberCount - not should decrement in old if value is ZERO`() = runBlocking {
        withFeatureFlag(
            FeatureNamespace.EHR,
            "allow_person_clinical_account_consumer",
            true
        ) {
            val event = PersonHealthcareTeamAssociationUpdatedEvent(
                personId = person.id,
                newHealthcareTeamId = healthcareTeam1.id,
                previousHealthcareTeamId = healthcareTeam2.id,
                updatedAt = LocalDateTime.now(),
            )
            val healthcareTeamZero = healthcareTeam2.copy(
                demographicCount = DemographicCount(
                    male = 0,
                    female = 0
                )
            )


            val expected = healthcareTeam1.copy(
                demographicCount = DemographicCount(
                    male = 1,
                    female = 0
                )
            )

            coEvery {
                healthcareTeamService.findBy(
                    HealthcareTeamFilters(
                        ids = listOf(healthcareTeam1.id, healthcareTeam2.id),
                        active = true
                    )
                )
            } returns listOf(healthcareTeam1, healthcareTeam2).success()

            coEvery { personService.get(person.id) } returns person.success()

            coEvery { healthcareTeamService.update(healthcareTeamZero) } returns healthcareTeamZero.success()
            coEvery { healthcareTeamService.update(expected) } returns expected.success()

            val result = consumer.addMemberCount(event)
            assertThat(result).isSuccessWithData(expected)

            coVerifyOnce {
                healthcareTeamService.findBy(any())
                personService.get(any())
            }

            coVerify(exactly = 2) {
                healthcareTeamService.update(any())
            }
        }
    }

    @Test
    fun `#addMemberCount - not should decrement if return error`() = runBlocking {
        withFeatureFlag(
            FeatureNamespace.EHR,
            "allow_person_clinical_account_consumer",
            true
        ) {
            val event = PersonHealthcareTeamAssociationUpdatedEvent(
                personId = person.id,
                newHealthcareTeamId = healthcareTeam1.id,
                previousHealthcareTeamId = healthcareTeam2.id,
                updatedAt = LocalDateTime.now(),
            )
            val healthcareTeamZero = healthcareTeam2.copy(
                demographicCount = DemographicCount(
                    male = 0,
                    female = 0
                )
            )

            coEvery {
                healthcareTeamService.findBy(
                    HealthcareTeamFilters(
                        ids = listOf(healthcareTeam1.id, healthcareTeam2.id),
                        active = true
                    )
                )
            } returns listOf(healthcareTeam1, healthcareTeam2).success()

            coEvery { personService.get(person.id) } returns person.success()

            coEvery { healthcareTeamService.update(healthcareTeamZero) } throws IllegalArgumentException("")

            val result = consumer.addMemberCount(event)
            assertThat(result).isFailureOfType(IllegalArgumentException::class)

            coVerifyOnce {
                healthcareTeamService.findBy(any())
                personService.get(any())
                healthcareTeamService.update(any())
            }
        }
    }

    @Test
    fun `#decrementMemberCount - should decrement count from person clinical account deleted`() = runBlocking {
        withFeatureFlag(
            FeatureNamespace.EHR,
            "allow_person_clinical_account_consumer",
            true
        ) {
            val event = PersonClinicalAccountDeleteEvent(
                personClinicalAccount = personClinicalAccount,
                deletedAt = LocalDateTime.now()
            )
            val expected = healthcareTeam1.copy(
                demographicCount = DemographicCount(
                    male = 0,
                    female = 0
                )
            )

            coEvery { healthcareTeamService.get(personClinicalAccount.healthcareTeamId) } returns healthcareTeam1.success()
            coEvery { personService.get(personClinicalAccount.personId) } returns person.success()
            coEvery { healthcareTeamService.update(expected) } returns expected.success()

            val result = consumer.decrementMemberCount(event)
            assertThat(result).isSuccessWithData(expected)

            coVerifyOnce {
                healthcareTeamService.get(any())
                personService.get(any())
                healthcareTeamService.update(any())
            }
        }
    }

    @Test
    fun `#decrementMemberCount - should return error when update`() = runBlocking {
        withFeatureFlag(
            FeatureNamespace.EHR,
            "allow_person_clinical_account_consumer",
            true
        ) {
            val event = PersonClinicalAccountDeleteEvent(
                personClinicalAccount = personClinicalAccount,
                deletedAt = LocalDateTime.now()
            )
            val expected = healthcareTeam1.copy(
                demographicCount = DemographicCount(
                    male = 0,
                    female = 0
                )
            )

            coEvery { healthcareTeamService.get(personClinicalAccount.healthcareTeamId) } returns healthcareTeam1.success()
            coEvery { personService.get(personClinicalAccount.personId) } returns person.success()
            coEvery { healthcareTeamService.update(expected) } returns IllegalArgumentException("").failure()

            val result = consumer.decrementMemberCount(event)
            assertThat(result).isFailureOfType(IllegalArgumentException::class)

            coVerifyOnce {
                healthcareTeamService.get(any())
                personService.get(any())
                healthcareTeamService.update(any())
            }
        }
    }

    @Test
    fun `#decrementMemberCount - should false if ff is off`() = runBlocking {
        withFeatureFlag(
            FeatureNamespace.EHR,
            "allow_person_clinical_account_consumer",
            false
        ) {
            val event = PersonClinicalAccountDeleteEvent(
                personClinicalAccount = personClinicalAccount,
                deletedAt = LocalDateTime.now()
            )

            val result = consumer.decrementMemberCount(event)
            assertThat(result).isSuccessWithData(false)

            coVerifyNone {
                healthcareTeamService.get(any())
                personService.get(any())
                healthcareTeamService.update(any())
            }
        }
    }

    @Test
    fun `#addMemberCount - should return false if allowConsumer is off`() = runBlocking {
        withFeatureFlag(
            FeatureNamespace.EHR,
            "allow_person_clinical_account_consumer",
            false
        ) {
            val event = PersonHealthcareTeamAssociationUpdatedEvent(
                personId = person.id,
                newHealthcareTeamId = healthcareTeam1.id,
                previousHealthcareTeamId = healthcareTeam1.id,
                updatedAt = LocalDateTime.now(),
            )

            val result = consumer.addMemberCount(event)
            assertThat(result).isSuccessWithData(false)

            coVerifyNone {
                healthcareTeamService.findBy(any())
                personService.get(any())
                healthcareTeamService.update(any())
            }
        }
    }

    @Test
    fun `#addMemberCount - should return false if new healthcareTeam and previous healthcareTeam is equals`() = runBlocking {
        withFeatureFlag(
            FeatureNamespace.EHR,
            "allow_person_clinical_account_consumer",
            true
        ) {
            val event = PersonHealthcareTeamAssociationUpdatedEvent(
                personId = person.id,
                newHealthcareTeamId = healthcareTeam1.id,
                previousHealthcareTeamId = healthcareTeam1.id,
                updatedAt = LocalDateTime.now(),
            )

            val result = consumer.addMemberCount(event)
            assertThat(result).isSuccessWithData(false)

            coVerifyNone {
                healthcareTeamService.findBy(any())
                personService.get(any())
                healthcareTeamService.update(any())
            }
        }
    }

}
