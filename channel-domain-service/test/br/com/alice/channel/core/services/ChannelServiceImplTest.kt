package br.com.alice.channel.core.services

import br.com.alice.channel.administrative.services.internal.ChannelThemeService
import br.com.alice.channel.assistance.services.internal.VirtualClinicQueueService
import br.com.alice.channel.client.ReRoutingExtraInfo
import br.com.alice.channel.client.RoutingDashboardResponse
import br.com.alice.channel.converters.ChannelBackupConverter
import br.com.alice.channel.core.FirestoreTransactionalTestHelper
import br.com.alice.channel.core.context.FirestoreContextUsage
import br.com.alice.channel.core.mockTimestamp
import br.com.alice.channel.core.services.internal.ChannelBackupService
import br.com.alice.channel.core.services.internal.ChannelBackupServiceFilters
import br.com.alice.channel.core.services.internal.ChannelCreateService
import br.com.alice.channel.core.services.internal.ChannelNotificationService
import br.com.alice.channel.core.services.internal.ChannelService
import br.com.alice.channel.core.services.internal.firestore.ChannelFirestoreService
import br.com.alice.channel.core.services.internal.logics.ChannelMergeService
import br.com.alice.channel.extensions.toLocalDateTime
import br.com.alice.channel.models.ChannelDocument
import br.com.alice.channel.models.ChannelMessageQuestionOptions
import br.com.alice.channel.models.ChannelResponse
import br.com.alice.channel.models.ChannelStaffInfo
import br.com.alice.channel.models.ChatMessageRequest
import br.com.alice.channel.models.CreateChatRequest
import br.com.alice.channel.models.MessageDocument
import br.com.alice.channel.models.MessageType.TEXT
import br.com.alice.channel.models.Origin
import br.com.alice.channel.models.Origin.HEALTH_PLAN_ITEM
import br.com.alice.channel.models.SendMessageRequest
import br.com.alice.common.RangeUUID
import br.com.alice.common.core.PersonId
import br.com.alice.common.core.Role
import br.com.alice.common.core.Role.DIGITAL_CARE_NURSE
import br.com.alice.common.core.Role.PRODUCT_TECH
import br.com.alice.common.core.exceptions.AccessForbiddenException
import br.com.alice.common.core.exceptions.AuthorizationException
import br.com.alice.common.core.exceptions.ConflictException
import br.com.alice.common.core.exceptions.InvalidArgumentException
import br.com.alice.common.core.exceptions.NotFoundException
import br.com.alice.common.core.extensions.toUUID
import br.com.alice.common.data.dsl.matchers.ResultAssert.Companion.assertThat
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.CaseSeverity
import br.com.alice.data.layer.models.ChannelArchivedReason
import br.com.alice.data.layer.models.ChannelAssessment
import br.com.alice.data.layer.models.ChannelCategory
import br.com.alice.data.layer.models.ChannelChangeAction
import br.com.alice.data.layer.models.ChannelKind
import br.com.alice.data.layer.models.ChannelStatus.ACTIVE
import br.com.alice.data.layer.models.ChannelStatus.ARCHIVED
import br.com.alice.data.layer.models.ChannelSubCategory
import br.com.alice.data.layer.models.ChannelSubCategoryClassifier
import br.com.alice.data.layer.models.ChannelType
import br.com.alice.data.layer.models.ChannelType.ASSISTANCE_CARE
import br.com.alice.data.layer.models.ChannelType.HEALTH_PLAN
import br.com.alice.data.layer.models.ChannelType.NUTRITION
import br.com.alice.data.layer.models.Demand
import br.com.alice.common.Disease
import br.com.alice.data.layer.models.HealthPlanTaskType
import br.com.alice.data.layer.models.copy
import br.com.alice.healthplan.client.HealthPlanTaskService
import br.com.alice.staff.client.StaffService
import com.github.kittinunf.result.failure
import com.github.kittinunf.result.success
import com.google.cloud.Timestamp
import com.google.cloud.firestore.CollectionReference
import com.google.cloud.firestore.Query
import io.mockk.called
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.confirmVerified
import io.mockk.every
import io.mockk.mockk
import io.mockk.slot
import io.mockk.verify
import kotlinx.coroutines.runBlocking
import java.math.RoundingMode
import java.time.LocalDateTime
import kotlin.test.AfterTest
import kotlin.test.Test

class ChannelServiceImplTest : FirestoreTransactionalTestHelper() {

    private val channelNotificationService: ChannelNotificationService = mockk()
    private val healthPlanTaskService: HealthPlanTaskService = mockk()
    private val channelService: ChannelService = mockk()
    private val staffService: StaffService = mockk()
    private val channelFirestoreService: ChannelFirestoreService = mockk()
    private val channelMergeService: ChannelMergeService = mockk()
    private val channelBackupService: ChannelBackupService = mockk()
    private val virtualClinicQueueService: VirtualClinicQueueService = mockk()
    private val channelCreateService: ChannelCreateService = mockk()
    private val channelThemeService: ChannelThemeService = mockk()

    private val service = ChannelServiceImpl(
        channelNotificationService,
        healthPlanTaskService,
        channelService,
        staffService,
        channelFirestoreService,
        channelMergeService,
        channelBackupService,
        virtualClinicQueueService,
        channelCreateService,
        channelThemeService
    )

    private val personId = PersonId()
    private val timestampNow = Timestamp.now()
    private val personReference = TestModelFactory.buildPersonInternalReference(personId)
    private val channelPersonId = personReference.channelPersonId.toString()
    private val ticketId = "ticketId"
    private val staffOwner = ChannelStaffInfo(
        id = staffId,
        name = "staff 1 Name",
        firstName = "staff",
        lastName = "1 Name",
        description = "Med AA",
        profileImageUrl = "",
        owner = true,
        roles = listOf(DIGITAL_CARE_NURSE),
        lastSync = Timestamp.now()
    )
    private val channelName = "channelName"
    private val staffParticipant =
        ChannelStaffInfo(
            id = RangeUUID.generate().toString(),
            name = "staff 2 Name",
            firstName = "staff",
            lastName = "2 Name",
            description = "Med AA",
            profileImageUrl = "",
            owner = false,
            roles = listOf(PRODUCT_TECH)
        )
    private val channelDocument = ChannelDocument(
        id = channelId,
        name = channelName,
        personId = personId.toString(),
        channelPersonId = channelPersonId,
        kind = ChannelKind.CHANNEL,
        category = ChannelCategory.ADMINISTRATIVE,
        status = ACTIVE,
        staffIds = listOf(staffOwner.id),
        staff = mutableMapOf(staffOwner.id to staffOwner),
        createdAt = timestampNow,
        timeLastMessage = timestampNow,
        lastPreviewableMessage = MessageDocument(
            aliceId = "alice-id",
            content = "lorem ipsum",
            createdAt = timestampNow,
            userId = channelPersonId,
            type = TEXT
        ),
        demands = listOf(
            Demand(
                caseId = RangeUUID.generate(),
                startedAt = LocalDateTime.now(),
                description = Disease(
                    type = Disease.Type.CID_10,
                    value = "A10",
                    description = "CID A10"
                ),
                severity = CaseSeverity.DECOMPENSATED,
            )
        )
    ).populateTypes()
    private val channelResponse = ChannelResponse(
        id = channelDocument.id!!,
        channelPersonId = channelDocument.channelPersonId,
        personId = channelDocument.personId,
        name = channelDocument.name!!,
        type = channelDocument.type!!,
        kind = channelDocument.kind,
        category = channelDocument.category,
        subCategory = channelDocument.subCategory,
        subCategoryClassifier = channelDocument.subCategoryClassifier,
        ownerStaffId = channelDocument.getOwner(),
        status = channelDocument.status,
        staffIds = channelDocument.staffIds,
        staff = channelDocument.staff,
        tags = channelDocument.tags,
        isArchived = channelDocument.status.isArchived(),
        createdAt = channelDocument.createdAt.toLocalDateTime(),
        timeLastMessage = channelDocument.timeLastMessage?.toLocalDateTime(),
        demands = channelDocument.demands
    )

    private val healthPlanTask = TestModelFactory.buildHealthPlanTask(
        personId = personReference.personId,
        type = HealthPlanTaskType.SLEEP
    )
    private val staff = TestModelFactory.buildStaff(id = staffId.toUUID())

    private val archivedReason = ChannelArchivedReason.STAFF_ACTION

    private val channelTheme = TestModelFactory.buildChannelTheme(
        channelId = channelId,
        staffId = staffOwner.id.toUUID()
    )

    @AfterTest
    fun confirmMocks() = confirmVerified(
        channelNotificationService,
        healthPlanTaskService,
        channelService,
        staffService,
        channelFirestoreService,
        channelMergeService,
        channelBackupService,
        virtualClinicQueueService,
        channelCreateService
    )

    @Test
    fun `#add returns created channel document`() = runBlocking {
        coEvery {
            channelCreateService.createChannel(channelDocument)
        } returns channelDocument.success()

        val result = service.add(channelDocument)
        assertThat(result).isSuccessWithData(channelDocument)

        coVerifyOnce { channelCreateService.createChannel(any()) }
    }

    @OptIn(FirestoreContextUsage::class)
    @Test
    fun `#addChatV2 returns created channel document`() = runBlocking {
        val request = CreateChatRequest(
            personId = personId,
            allowOnCallFlow = true,
            origin = "test",
            message = ChatMessageRequest(
                content = "Oi",
                type = TEXT,
                appVersion = "appVersion",
                source = "messageSource"
            ),
            category = ChannelCategory.ASSISTANCE,
            subCategory = ChannelSubCategory.LONGITUDINAL,
            subCategoryClassifier = null,
            tags = listOf("Tag1")
        )

        coEvery { channelCreateService.createChat(request) } returns channelDocument.success()

        val result = mockFirestoreContextCommit { service.addChatV2(request) }
        assertThat(result).isSuccessWithData(channelDocument)

        coVerifyOnce { channelCreateService.createChat(any()) }
    }

    @OptIn(FirestoreContextUsage::class)
    @Test
    fun `#addStaff returns channel id and add staff on channel`() = runBlocking {
        coEvery { staffService.get(staffId.toUUID()) } returns staff.success()
        coEvery {
            channelService.addStaff(
                channelId = channelId,
                staff = staff,
                requesterStaffId = "requester_staff_id",
                owner = false,
                force = true
            )
        } returns channelId.success()

        val result = mockFirestoreContextCommit {
            service.addStaff(channelId, staffId, "requester_staff_id", force = true)
        }
        assertThat(result).isSuccessWithData(channelId)

        coVerifyOnce { staffService.get(any()) }
        coVerifyOnce { channelService.addStaff(any(), any(), any(), any(), any()) }

    }

    @OptIn(FirestoreContextUsage::class)
    @Test
    fun `#addStaff should add herself as owner when first staff on channel`() = runBlocking {
        val expectedStaff = staff.copy(
            id = staffParticipant.id.toUUID(),
            firstName = staffParticipant.name.orEmpty(),
        )

        coEvery { staffService.get(staffParticipant.id.toUUID()) } returns expectedStaff.success()
        coEvery {
            channelService.addStaff(channelId, expectedStaff, staffParticipant.id, false)
        } returns channelId.success()

        val result = mockFirestoreContextCommit {
            service.addStaff(channelId, staffParticipant.id, staffParticipant.id)
        }
        assertThat(result).isSuccessWithData(channelId)

        coVerifyOnce { staffService.get(any()) }
        coVerifyOnce { channelService.addStaff(any(), any(), any(), any()) }
    }

    @OptIn(FirestoreContextUsage::class)
    @Test
    fun `#addStaff should return failure`() = runBlocking {
        coEvery { staffService.get(staffId.toUUID()) } returns staff.success()
        coEvery { channelService.addStaff(channelId, staff, staffOwner.id, false) } returns Exception().failure()

        val result = mockFirestoreContextCommit { service.addStaff(channelId, staffId, staffOwner.id) }
        assertThat(result).isFailureOfType(Exception::class)

        coVerifyOnce { staffService.get(any()) }
        coVerifyOnce { channelService.addStaff(any(), any(), any(), any()) }
    }

    @Test
    fun `#addStaff should return failure when staff is not an Alice Health Professional`() =
        runBlocking {
            coEvery { staffService.get(staff.id) } returns staff.copy(role = PRODUCT_TECH).success()

            val result = mockFirestoreContextCommit {
                service.addStaff(channelId, staff.id.toString(), staffOwner.id)
            }
            assertThat(result).isFailureOfType(InvalidArgumentException::class)

            coVerifyOnce { staffService.get(any()) }

        }

    @OptIn(FirestoreContextUsage::class)
    @Test
    fun `#removeStaff returns channel id and process in new removeStaff function`() = runBlocking {
        coEvery {
            channelService.removeStaff(channelId, staffParticipant.id, staffOwner.id)
        } returns channelId.success()

        val result = mockFirestoreContextCommit {
            service.removeStaff(channelId, staffParticipant.id, staffOwner.id)
        }
        assertThat(result).isSuccessWithData(channelId)

        coVerifyOnce { channelService.removeStaff(any<String>(), any(), any(), any(), any()) }

    }

    @OptIn(FirestoreContextUsage::class)
    @Test
    fun `#removeStaff returns error and process in new removeStaff function`() = runBlocking {
        coEvery {
            channelService.removeStaff(channelId, staffParticipant.id, staffOwner.id)
        } returns Exception().failure()

        val result = mockFirestoreContextCommit {
            service.removeStaff(channelId, staffParticipant.id, staffOwner.id)
        }
        assertThat(result).isFailureOfType(Exception::class)

        coVerifyOnce { channelService.removeStaff(any<String>(), any(), any(), any(), any()) }

    }

    @OptIn(FirestoreContextUsage::class)
    @Test
    fun `#archiveChannel returns channel id and archive channel`() = runBlocking {
        coEvery {
            channelService.archiveChannel(channelId, staffOwner.id, archivedReason)
        } returns channelId.success()

        val result = mockFirestoreContextCommit {
            service.archiveChannel(channelId, staffOwner.id)
        }
        assertThat(result).isSuccessWithData(channelId)

        coVerifyOnce { channelService.archiveChannel(any<String>(), any(), any(), any()) }
    }

    @OptIn(FirestoreContextUsage::class)
    @Test
    fun `#archiveChannel returns error when an error occurs to archive channel`() = runBlocking {
        coEvery {
            channelService.archiveChannel(channelId, staffOwner.id, archivedReason)
        } returns Exception().failure()

        val result = mockFirestoreContextCommit {
            service.archiveChannel(channelId, staffOwner.id)
        }
        assertThat(result).isFailureOfType(Exception::class)

        coVerifyOnce { channelService.archiveChannel(any<String>(), any(), any(), any()) }
    }

    @FirestoreContextUsage
    @Test
    fun `#archiveAdministrativeChannel returns channel id and archive channel`() = runBlocking {
        coEvery {
            channelThemeService.archiveAdministrativeChannel(channelTheme)
        } returns channelTheme.channelId.success()

        val result = service.archiveAdministrativeChannel(channelTheme)
        assertThat(result).isSuccessWithData(channelId)

        coVerifyOnce { channelThemeService.archiveAdministrativeChannel(any()) }
    }

    @OptIn(FirestoreContextUsage::class)
    @Test
    fun `#giveOwnership returns channel id and change ownership`() = runBlocking {
        coEvery {
            channelService.giveOwnership(channelId, staffParticipant.id, staffOwner.id)
        } returns channelId.success()

        val result = mockFirestoreContextCommit {
            service.giveOwnership(channelId, staffParticipant.id, staffOwner.id)
        }
        assertThat(result).isSuccessWithData(channelId)

        coVerifyOnce { channelService.giveOwnership(any(), any(), any()) }

    }

    @OptIn(FirestoreContextUsage::class)
    @Test
    fun `#giveOwnership returns error when an error occurs to give ownership`() = runBlocking {
        coEvery {
            channelService.giveOwnership(channelId, staffParticipant.id, staffOwner.id)
        } returns Exception().failure()

        val result = mockFirestoreContextCommit {
            service.giveOwnership(channelId, staffParticipant.id, staffOwner.id)
        }
        assertThat(result).isFailureOfType(Exception::class)

        coVerifyOnce { channelService.giveOwnership(any(), any(), any()) }

    }

    @OptIn(FirestoreContextUsage::class)
    @Test
    fun `#setTags should add tags to channel`() = runBlocking {
        val tags = listOf("TAG1", "TAG2")
        coEvery { channelService.setTags(channelId, tags) } returns channelId.success()

        val result = mockFirestoreContextCommit { service.setTags(channelId, tags) }
        assertThat(result).isSuccessWithData(channelId)

        coVerifyOnce { channelService.setTags(any(), any()) }

    }

    @OptIn(FirestoreContextUsage::class)
    @Test
    fun `#setTags should return failure`() = runBlocking {
        val tags = listOf("TAG1", "TAG2")

        coEvery { channelService.setTags(channelId, tags) } returns Exception().failure()

        val result = mockFirestoreContextCommit { service.setTags(channelId, tags) }
        assertThat(result).isFailureOfType(Exception::class)

        coVerifyOnce { channelService.setTags(any(), any()) }

    }

    @OptIn(FirestoreContextUsage::class)
    @Test
    fun `#highlight returns channel id and set highlight as true on staff`() = runBlocking {
        coEvery { channelFirestoreService.getChannel(channelId) } returns channelDocument.success()

        coEvery {
            channelFirestoreService.updateFields(
                channelId,
                mapOf(
                    ChannelDocument::staff to mapOf(
                        staffId to mapOf(ChannelStaffInfo::highlight to true)
                    )
                )
            )
        } returns channelId.success()

        coEvery {
            channelNotificationService.notify(channelDocument, ChannelChangeAction.HIGHLIGHT)
        } returns mockk()

        val result = mockFirestoreContextCommit {
            service.highlight(channelId, staffId)
        }
        assertThat(result).isSuccessWithData(channelId)

        coVerifyOnce { channelFirestoreService.getChannel(any()) }
        coVerifyOnce { channelFirestoreService.updateFields(any(), any()) }
        coVerifyOnce { channelNotificationService.notify(any<ChannelDocument>(), any(), any(), any()) }


    }

    @OptIn(FirestoreContextUsage::class)
    @Test
    fun `#highlight returns channel id and set highlight as false on staff`() = runBlocking {
        val channelDocument = channelDocument.copy(
            staff = mutableMapOf(
                staffOwner.id to staffOwner.copy(highlight = true)
            )
        )

        coEvery { channelFirestoreService.getChannel(channelId) } returns channelDocument.success()

        coEvery {
            channelFirestoreService.updateFields(
                channelId,
                mapOf(
                    ChannelDocument::staff to mapOf(
                        staffId to mapOf(ChannelStaffInfo::highlight to false)
                    )
                )
            )
        } returns channelId.success()

        coEvery {
            channelNotificationService.notify(channelDocument, ChannelChangeAction.HIGHLIGHT)
        } returns mockk()

        val result = mockFirestoreContextCommit {
            service.highlight(channelId, staffId)
        }
        assertThat(result).isSuccessWithData(channelId)

        coVerifyOnce { channelFirestoreService.getChannel(any()) }
        coVerifyOnce { channelFirestoreService.updateFields(any(), any()) }
        coVerifyOnce { channelNotificationService.notify(any<ChannelDocument>(), any(), any(), any()) }

    }

    @Test
    fun `#highlight returns error when staff not found in channel`() = runBlocking {
        coEvery { channelFirestoreService.getChannel(channelId) } returns channelDocument.success()

        val result = mockFirestoreContextCommit {
            service.highlight(channelId, RangeUUID.generate().toString())
        }
        assertThat(result).isFailureOfType(java.lang.IllegalArgumentException::class)

        coVerifyOnce { channelFirestoreService.getChannel(any()) }
    }

    @Test
    fun `#highlight returns error when channel does not exists`() = runBlocking {
        coEvery { channelFirestoreService.getChannel(channelId) } returns NotFoundException(channelId).failure()

        val result = mockFirestoreContextCommit {
            service.highlight(channelId, staffId)
        }
        assertThat(result).isFailureOfType(NotFoundException::class)

        coVerifyOnce { channelFirestoreService.getChannel(any()) }

    }

    @Test
    fun `#highlight returns error when channel is not active`() = runBlocking {
        coEvery {
            channelFirestoreService.getChannel(channelId)
        } returns channelDocument.copy(status = ARCHIVED).success()

        val result = mockFirestoreContextCommit {
            service.highlight(channelId, staffId)
        }
        assertThat(result).isFailureOfType(IllegalArgumentException::class)

        coVerifyOnce { channelFirestoreService.getChannel(any()) }

    }

    @Test
    fun `#setZendeskTicketId returns error when channel does not exists`() = runBlocking {
        coEvery { channelFirestoreService.getChannel(channelId) } returns NotFoundException(channelId).failure()

        val result = mockFirestoreContextCommit {
            service.highlight(channelId, ticketId)
        }
        assertThat(result).isFailureOfType(NotFoundException::class)

        coVerifyOnce { channelFirestoreService.getChannel(any()) }

    }

    @Test
    fun `#get should return NotFoundException when channel does not exists`() = runBlocking {
        coEvery { channelService.getChannel(channelId) } returns NotFoundException(channelId).failure()

        val result = service.get(channelId)
        assertThat(result).isFailureOfType(NotFoundException::class)

        coVerifyOnce { channelService.getChannel(any()) }

    }

    @Test
    fun `#get returns error`() = runBlocking {
        coEvery { channelService.getChannel(channelId) } returns Exception().failure()

        val result = service.get(channelId)
        assertThat(result).isFailureOfType(Exception::class)

        coVerifyOnce { channelService.getChannel(any()) }

    }

    @Test
    fun `#get returns channel get by id`() = runBlocking {
        coEvery { channelService.getChannel(channelId) } returns channelDocument.success()

        val result = service.get(channelId)
        assertThat(result).isSuccessWithData(channelResponse)

        coVerifyOnce { channelService.getChannel(any()) }

    }

    @Test
    fun `#get should return channel when it exists`() = runBlocking {
        val channelDocument =
            ChannelDocument(
                personId = personId.toString(),
                staff = mutableMapOf(
                    channelId to ChannelStaffInfo(
                        id = staffOwner.id,
                        owner = true,
                        firstName = staffOwner.firstName,
                        lastName = staffOwner.lastName,
                        description = staffOwner.description,
                        profileImageUrl = staffOwner.profileImageUrl,
                    )
                ),
                channelPersonId = "channelPersonId",
                type = ASSISTANCE_CARE
            )
        val channelName = channelDocument.name ?: ""
        val channelCreatedAt = channelDocument.createdAt.toSqlTimestamp().toLocalDateTime()
        val channelTimeLastMessage = channelDocument.timeLastMessage?.toSqlTimestamp()?.toLocalDateTime()
        val expectedResponse = ChannelResponse(
            id = channelId,
            channelPersonId = channelDocument.channelPersonId,
            personId = channelDocument.personId,
            name = channelName,
            type = ASSISTANCE_CARE,
            status = channelDocument.status,
            ownerStaffId = staffOwner.id.toUUID(),
            staffIds = channelDocument.staffIds,
            staff = channelDocument.staff,
            isArchived = false,
            createdAt = channelCreatedAt,
            timeLastMessage = channelTimeLastMessage,
        )

        coEvery { channelService.getChannel(channelId) } returns channelDocument.success()

        val result = service.get(channelId)
        assertThat(result).isSuccessWithData(expectedResponse)

        coVerifyOnce { channelService.getChannel(any()) }

    }

    @Test
    @OptIn(FirestoreContextUsage::class)
    fun `#merge returns destination channel id in new flow`() = runBlocking {
        coEvery { channelMergeService.merge(channelId, channelId, staffOwner.id) } returns channelId.success()

        val result = mockFirestoreContextCommit {
            service.merge(channelId, channelId, staffOwner.id)
        }
        assertThat(result).isSuccessWithData(channelId)

        coVerifyOnce { channelMergeService.merge(any(), any(), any()) }

    }

    @OptIn(FirestoreContextUsage::class)
    @Test
    fun `#deleteMessage should return unauthorized`() = runBlocking {
        coEvery {
            channelService.deleteMessage(channelId, messageId, staffOwner.id, false)
        } returns AuthorizationException("$staffId not authorized to perform this").failure()

        val result = mockFirestoreContextCommit {
            service.deleteMessage(channelId, messageId, staffOwner.id, false)
        }
        assertThat(result).isFailureOfType(AuthorizationException::class)

        coVerifyOnce { channelService.deleteMessage(any(), any(), any(), any()) }

    }

    @OptIn(FirestoreContextUsage::class)
    @Test
    fun `#deleteMessage should delete`() = runBlocking {
        coEvery {
            channelService.deleteMessage(
                channelId,
                messageId,
                staffOwner.id,
                false
            )
        } returns messageId.success()

        val result = mockFirestoreContextCommit {
            service.deleteMessage(channelId, messageId, staffOwner.id, false)
        }
        assertThat(result).isSuccessWithData(messageId)

        coVerifyOnce { channelService.deleteMessage(any(), any(), any(), any()) }

    }

    @Test
    fun `#getByPersonIdAndStatuses returns channel response from channel backup found by filters`() = runBlocking {
        val channel = TestModelFactory.buildChannel()
        val expected = listOf(ChannelBackupConverter.convert(channel))

        coEvery {
            channelBackupService.findByFilters(
                ChannelBackupServiceFilters(
                    personId = personId,
                    statuses = listOf(ACTIVE)
                )
            )
        } returns listOf(channel).success()

        val result = service.getByPersonAndStatuses(personId, listOf(ACTIVE))
        assertThat(result).isSuccessWithData(expected)

        coVerifyOnce { channelBackupService.findByFilters(any()) }
    }


    @Test
    fun `#sendMessage should create a channel when HealthPlanTaskType is of type PRESCRIPTION`() =
        testChatCreationFromTask(
            healthPlanTaskType = HealthPlanTaskType.PRESCRIPTION,
            expectedChatName = "Medicamento - ${healthPlanTask.title!!}",
            expectedChatCategory = ChannelCategory.ASSISTANCE,
            expectedChatSubCategory = ChannelSubCategory.SCREENING
        )

    @Test
    fun `#sendMessage should create a channel when HealthPlanTaskType is of type EATING`() =
        testChatCreationFromTask(
            healthPlanTaskType = HealthPlanTaskType.EATING,
            expectedChatName = healthPlanTask.title!!,
            expectedChatCategory = ChannelCategory.ASSISTANCE,
            expectedChatSubCategory = ChannelSubCategory.SCREENING
        )

    @Test
    fun `#sendMessage should create a channel when HealthPlanTaskType is of type PHYSICAL_ACTIVITY`() =
        testChatCreationFromTask(
            healthPlanTaskType = HealthPlanTaskType.PHYSICAL_ACTIVITY,
            expectedChatName = healthPlanTask.title!!,
            expectedChatCategory = ChannelCategory.ASSISTANCE,
            expectedChatSubCategory = ChannelSubCategory.SCREENING
        )

    @Test
    fun `#sendMessage should create a channel when HealthPlanTaskType is of type SLEEP`() =
        testChatCreationFromTask(
            healthPlanTaskType = HealthPlanTaskType.SLEEP,
            expectedChatName = healthPlanTask.title!!,
            expectedChatCategory = ChannelCategory.ASSISTANCE,
            expectedChatSubCategory = ChannelSubCategory.SCREENING
        )

    @Test
    fun `#sendMessage should create a channel when HealthPlanTaskType is of type MOOD`() =
        testChatCreationFromTask(
            healthPlanTaskType = HealthPlanTaskType.MOOD,
            expectedChatName = healthPlanTask.title!!,
            expectedChatCategory = ChannelCategory.ASSISTANCE,
            expectedChatSubCategory = ChannelSubCategory.SCREENING
        )

    @Test
    fun `#sendMessage should create a channel when HealthPlanTaskType is of type OTHERS`() =
        testChatCreationFromTask(
            healthPlanTaskType = HealthPlanTaskType.OTHERS,
            expectedChatName = healthPlanTask.title!!,
            expectedChatCategory = ChannelCategory.ASSISTANCE,
            expectedChatSubCategory = ChannelSubCategory.SCREENING
        )

    @Test
    fun `#sendMessage should create a channel when HealthPlanTaskType is of type TEST_REQUEST`() =
        testChatCreationFromTask(
            healthPlanTaskType = HealthPlanTaskType.TEST_REQUEST,
            expectedChatName = healthPlanTask.title!!,
            expectedChatCategory = ChannelCategory.ASSISTANCE,
            expectedChatSubCategory = ChannelSubCategory.SCREENING
        )

    @Test
    fun `#sendMessage should create a channel when HealthPlanTaskType is of type REFERRAL`() =
        testChatCreationFromTask(
            healthPlanTaskType = HealthPlanTaskType.REFERRAL,
            expectedChatName = healthPlanTask.title!!,
            expectedChatCategory = ChannelCategory.ADMINISTRATIVE,
            expectedChatSubCategory = null
        )

    @Test
    fun `#sendMessage should create a channel when HealthPlanTaskType is of type EMERGENCY`() =
        testChatCreationFromTask(
            healthPlanTaskType = HealthPlanTaskType.EMERGENCY,
            expectedChatName = healthPlanTask.title!!,
            expectedChatCategory = ChannelCategory.ADMINISTRATIVE,
            expectedChatSubCategory = null
        )

    @Test
    fun `#sendMessage should create a channel when HealthPlanTaskType is of type SCHEDULING`() =
        testChatCreationFromTask(
            healthPlanTaskType = HealthPlanTaskType.SCHEDULING,
            expectedChatName = healthPlanTask.title!!,
            expectedChatCategory = ChannelCategory.ASSISTANCE,
            expectedChatSubCategory = ChannelSubCategory.SCREENING
        )

    @Test
    fun `#sendMessage should return error when message source is invalid`() = runBlocking {
        val chatId = "chat-id"
        val request = SendMessageRequest(
            healthPlanItemId = chatId,
            type = TEXT,
            source = null,
            content = "Content",
            appVersion = "Android X.Y.Z"
        )

        val result = service.sendMessage(personId, request)

        assertThat(result).isFailureOfType(InvalidArgumentException::class)

    }

    @Test
    fun `#sendMessage should return error when message source is from health plan task and health plan item id is null`() =
        runBlocking {
            val request = SendMessageRequest(
                healthPlanItemId = null,
                type = TEXT,
                origin = HEALTH_PLAN_ITEM,
                content = "Content",
                appVersion = "Android X.Y.Z"
            )

            val result = service.sendMessage(personId, request)

            assertThat(result).isFailureOfType(InvalidArgumentException::class)

        }

    @Test
    fun `#sendMessage should return error when message source is from health plan task and health plan item id is invalid`() =
        runBlocking {
            val request = SendMessageRequest(
                healthPlanItemId = " ",
                type = TEXT,
                origin = HEALTH_PLAN_ITEM,
                content = "Content",
                appVersion = "Android X.Y.Z"
            )

            val result = service.sendMessage(personId, request)

            assertThat(result).isFailureOfType(InvalidArgumentException::class)

        }

    @OptIn(FirestoreContextUsage::class)
    @Test
    fun `#sendMessage should create new help center chat by origin and send new message into it`() =
        runBlocking {
            val chatId = "chat-id"
            val chatOrigin = Origin.HELP_CENTER.description
            val request = SendMessageRequest(
                healthPlanItemId = null,
                type = TEXT,
                origin = Origin.HELP_CENTER,
                content = "Content",
                appVersion = "Android X.Y.Z"
            )

            val channelDocument = channelDocument.copy(
                id = chatId,
                type = HEALTH_PLAN,
                status = ACTIVE
            )

            val expectedToCreate = CreateChatRequest(
                personId,
                false,
                chatOrigin,
                ChatMessageRequest(
                    content = request.content,
                    type = request.type,
                    appVersion = request.appVersion,
                    source = Origin.HELP_CENTER.description
                ),
                category = ChannelCategory.ADMINISTRATIVE,
                chatName = request.chatName
            )

            coEvery { channelCreateService.createChat(expectedToCreate) } returns channelDocument.success()

            val result = mockFirestoreContextCommit { service.sendMessage(personId, request) }
            assertThat(result).isSuccessWithData(chatId)

            coVerifyOnce { channelCreateService.createChat(any()) }

        }

    @OptIn(FirestoreContextUsage::class)
    @Test
    fun `#setChannelAssessment should produce event to set channel assessment`() = runBlocking {
        coEvery {
            channelService.setChannelAssessment(channelId, ChannelAssessment.EMERGENCY)
        } returns channelId.success()

        val result = mockFirestoreContextCommit {
            service.setChannelAssessment(channelId, ChannelAssessment.EMERGENCY)
        }
        assertThat(result).isSuccessWithData(channelId)

        coVerifyOnce { channelService.setChannelAssessment(any(), any()) }

    }

    @OptIn(FirestoreContextUsage::class)
    @Test
    fun `#channelClassification return channel id`() = runBlocking {
        coEvery {
            channelService.channelClassification(
                channelId,
                channelDocument.kind!!,
                channelDocument.category,
                channelDocument.subCategory,
                channelDocument.subCategoryClassifier
            )
        } returns channelId.success()

        val result = mockFirestoreContextCommit {
            service.channelClassification(
                channelId,
                channelDocument.kind!!,
                channelDocument.category,
                channelDocument.subCategory,
                channelDocument.subCategoryClassifier
            )
        }
        assertThat(result).isSuccessWithData(channelId)

        coVerifyOnce { channelService.channelClassification(any<String>(), any(), any(), any(), any()) }
    }


    @OptIn(FirestoreContextUsage::class)
    @Test
    fun `#rename returns channel id and call rename function from internal service`() = runBlocking {
        coEvery { channelService.rename(channelId, "newChannelName", staffOwner.id) } returns channelId.success()

        val result = mockFirestoreContextCommit {
            service.rename(channelId, "newChannelName", staffOwner.id)
        }
        assertThat(result).isSuccessWithData(channelId)

        coVerifyOnce { channelService.rename(any<String>(), any(), any()) }
    }

    @Test
    fun `#findChannels should return list of channels filtered only by person id`() = runBlocking {
        val expected = listOf(channelResponse)

        coEvery {
            channelService.findChannels(
                personId = personId.toString(),
                onlyActives = false,
                category = null,
                subCategories = null,
                createdAfter = null
            )
        } returns listOf(channelDocument).success()

        val result = service.findChannels(
            personId = personId.toString(),
            onlyActives = false,
            category = null,
            subCategories = null,
            createdAfter = null
        )
        assertThat(result).isSuccessWithData(expected)

        coVerifyOnce { channelService.findChannels(any()) }

    }

    @Test
    fun `#findChannelsById should return list of channels filtered by its ids`() = runBlocking {
        val expected = listOf(channelResponse)
        val channelIds = listOf(channelId)

        coEvery { channelService.getChannelsById(channelIds) } returns listOf(channelDocument).success()

        val result = service.findChannelsById(channelIds)
        assertThat(result).isSuccessWithData(expected)

        coVerifyOnce { channelService.getChannelsById(any()) }

    }

    @Test
    fun `#getOrCreateByMultiTeamClassifier returns channel id when channel exists`() = runBlocking {
        val functionSlot = slot<suspend (CollectionReference) -> Query>()

        every { collectionReference.whereEqualTo("channelPersonId", channelPersonId) } returns collectionReference
        every {
            collectionReference.whereEqualTo(
                "subCategoryClassifier",
                ChannelSubCategoryClassifier.NUTRITION
            )
        } returns collectionReference
        every { collectionReference.whereEqualTo("status", ACTIVE) } returns collectionReference

        coEvery { channelFirestoreService.find(capture(functionSlot)) } returns listOf(channelDocument).success()

        val result = service.getOrCreateByMultiTeamClassifier(
            classifier = ChannelSubCategoryClassifier.NUTRITION,
            personId = personId.toString(),
            channelPersonId = channelPersonId,
            staffIdsToAdd = listOf(staffId)
        )
        assertThat(result).isSuccessWithData(channelId)

        functionSlot.captured.invoke(collectionReference)

        verify(exactly = 3) { collectionReference.whereEqualTo(any<String>(), any()) }
        coVerifyOnce { channelFirestoreService.find(any()) }

    }

    @Test
    fun `#getOrCreateByMultiTeamClassifier returns channel id and create new nutrition channel and not add staff`() =
        mockTimestamp(timestampNow) {
            val expectedChannelDocument = channelDocument.copy(type = NUTRITION)

            val expectedToCreate = ChannelDocument(
                name = "Nutrição & você",
                kind = ChannelKind.CHANNEL,
                category = ChannelCategory.ASSISTANCE,
                subCategory = ChannelSubCategory.MULTI,
                subCategoryClassifier = ChannelSubCategoryClassifier.NUTRITION,
                personId = personId.toString(),
                channelPersonId = channelPersonId,
                lastSync = timestampNow,
                updatedAt = timestampNow,
                createdAt = timestampNow
            ).populateTypes()

            val functionSlot = slot<suspend (CollectionReference) -> Query>()

            every { collectionReference.whereEqualTo("channelPersonId", channelPersonId) } returns collectionReference
            every {
                collectionReference.whereEqualTo(
                    "subCategoryClassifier",
                    ChannelSubCategoryClassifier.NUTRITION
                )
            } returns collectionReference
            every { collectionReference.whereEqualTo("status", ACTIVE) } returns collectionReference

            coEvery { channelFirestoreService.find(capture(functionSlot)) } returns emptyList<ChannelDocument>().success()
            coEvery { channelCreateService.createChannel(expectedToCreate) } returns expectedChannelDocument.success()

            val result = service.getOrCreateByMultiTeamClassifier(
                classifier = ChannelSubCategoryClassifier.NUTRITION,
                personId = personId.toString(),
                channelPersonId = channelPersonId,
                staffIdsToAdd = listOf(staffId)
            )
            assertThat(result).isSuccessWithData(expectedChannelDocument.id!!)

            functionSlot.captured.invoke(collectionReference)

            verify(exactly = 3) { collectionReference.whereEqualTo(any<String>(), any()) }
            coVerifyOnce { channelFirestoreService.find(any()) }
            coVerifyOnce { channelCreateService.createChannel(any()) }

        }

    @Test
    fun `#getOrCreateByMultiTeamClassifier returns error when channel classifier is invalid`() = runBlocking {
        val result = service.getOrCreateByMultiTeamClassifier(
            classifier = ChannelSubCategoryClassifier.HEALTH_TEAM,
            personId = personId.toString(),
            channelPersonId = channelPersonId,
            staffIdsToAdd = listOf(staffId)
        )

        assertThat(result).isFailureOfType(IllegalArgumentException::class)

    }

    @OptIn(FirestoreContextUsage::class)
    @Test
    fun `#getOrCreateByMultiTeamClassifier returns channel id create new physical activity channel`() =
        mockTimestamp(timestampNow) {
            val expectedChannelDocument =
                channelDocument.copy(type = ChannelType.PHYSICAL_ACTIVITY, staff = mutableMapOf())

            val expectedToCreate = ChannelDocument(
                name = "Preparação física & você",
                kind = ChannelKind.CHANNEL,
                category = ChannelCategory.ASSISTANCE,
                subCategory = ChannelSubCategory.MULTI,
                subCategoryClassifier = ChannelSubCategoryClassifier.PHYSICAL_PREPARATION,
                personId = personId.toString(),
                channelPersonId = channelPersonId,
                lastSync = timestampNow,
                updatedAt = timestampNow,
                createdAt = timestampNow
            ).populateTypes()

            val functionSlot = slot<suspend (CollectionReference) -> Query>()

            every {
                collectionReference.whereEqualTo(
                    "channelPersonId",
                    channelPersonId
                )
            } returns collectionReference
            every {
                collectionReference.whereEqualTo(
                    "subCategoryClassifier",
                    ChannelSubCategoryClassifier.PHYSICAL_PREPARATION
                )
            } returns collectionReference
            every { collectionReference.whereEqualTo("status", ACTIVE) } returns collectionReference

            coEvery { channelFirestoreService.find(capture(functionSlot)) } returns emptyList<ChannelDocument>().success()
            coEvery { channelCreateService.createChannel(expectedToCreate) } returns expectedChannelDocument.success()
            coEvery { staffService.get(staffId.toUUID()) } returns staff.success()
            coEvery { channelService.addStaff(channelId, staff, staffId, true) } returns channelId.success()

            val result = mockFirestoreContextCommit {
                service.getOrCreateByMultiTeamClassifier(
                    classifier = ChannelSubCategoryClassifier.PHYSICAL_PREPARATION,
                    personId = personId.toString(),
                    channelPersonId = channelPersonId,
                    staffIdsToAdd = listOf(staffId)
                )
            }
            assertThat(result).isSuccessWithData(expectedChannelDocument.id!!)

            functionSlot.captured.invoke(collectionReference)

            verify(exactly = 3) { collectionReference.whereEqualTo(any<String>(), any()) }
            coVerifyOnce { channelFirestoreService.find(any()) }
            coVerifyOnce { channelCreateService.createChannel(any()) }
            coVerifyOnce { staffService.get(any()) }
            coVerifyOnce { channelService.addStaff(any(), any(), any(), any()) }

        }

    @Test
    fun `#getDashboardCounters returns RoutingDashboardResponse with counters`() = runBlocking {
        val routingDashboardResponse = RoutingDashboardResponse(
            chats = 1,
            simultaneity = 1.toBigDecimal().setScale(2, RoundingMode.HALF_DOWN)
        )

        val channel = TestModelFactory.buildChannel()

        coEvery { channelBackupService.getActivesChats() } returns listOf(channel).success()

        val result = service.getDashboardCounters()
        assertThat(result).isSuccessWithData(routingDashboardResponse)

        coVerifyOnce { channelBackupService.getActivesChats() }

    }

    @OptIn(FirestoreContextUsage::class)
    private fun testChatCreationFromTask(
        healthPlanTaskType: HealthPlanTaskType,
        expectedChatName: String,
        expectedChatCategory: ChannelCategory,
        expectedChatSubCategory: ChannelSubCategory?
    ) = runBlocking {
        val request = SendMessageRequest(
            healthPlanItemId = healthPlanTask.id.toString(),
            type = TEXT,
            origin = HEALTH_PLAN_ITEM,
            content = "Content",
            appVersion = "Android X.Y.Z"
        )

        val healthPlanTask = healthPlanTask.copy(type = healthPlanTaskType)

        coEvery { healthPlanTaskService.get(healthPlanTask.id) } returns healthPlanTask.success()
        coEvery {
            channelCreateService.createChat(
                CreateChatRequest(
                    personId = personId,
                    allowOnCallFlow = false,
                    origin = HEALTH_PLAN_ITEM.description,
                    message = ChatMessageRequest(
                        content = "<strong>#$expectedChatName</strong> <br /><br /> ${request.content}",
                        type = request.type,
                        appVersion = request.appVersion,
                        source = healthPlanTaskType.name.lowercase()
                    ),
                    category = expectedChatCategory,
                    subCategory = expectedChatSubCategory,
                    chatName = expectedChatName
                )
            )
        } returns channelDocument.success()

        val result = mockFirestoreContextCommit { service.sendMessage(personId, request) }
        assertThat(result).isSuccessWithData(channelId)

        coVerifyOnce { healthPlanTaskService.get(any()) }
        coVerifyOnce { channelCreateService.createChat(any()) }
    }

    @OptIn(FirestoreContextUsage::class)
    @Test
    fun `#reRouting returns channel id and re-routing chat`() = runBlocking {
        coEvery {
            channelService.reRouting(channelId, ChannelCategory.ASSISTANCE, ChannelSubCategory.SCREENING)
        } returns channelId.success()

        val result = mockFirestoreContextCommit {
            service.reRouting(channelId, ChannelCategory.ASSISTANCE, ChannelSubCategory.SCREENING)
        }
        assertThat(result).isSuccessWithData(channelId)

        coVerifyOnce { channelService.reRouting(any(), any(), any()) }

    }

    @OptIn(FirestoreContextUsage::class)
    @Test
    fun `#reRouting returns channel id and re-routing chat with extra info`() = runBlocking {
        val extraInfo = ReRoutingExtraInfo(reason = "Motivo", currentStaffId = staffId.toUUID())
        coEvery {
            channelService.reRouting(channelId, ChannelCategory.ASSISTANCE, ChannelSubCategory.SCREENING, extraInfo)
        } returns channelId.success()

        val result = mockFirestoreContextCommit {
            service.reRouting(channelId, ChannelCategory.ASSISTANCE, ChannelSubCategory.SCREENING, extraInfo)
        }
        assertThat(result).isSuccessWithData(channelId)

        coVerifyOnce { channelService.reRouting(any(), any(), any(), any()) }

    }

    @OptIn(FirestoreContextUsage::class)
    @Test
    fun `#reRouting returns error when an error occurs to re-routing chat`() = runBlocking {
        coEvery {
            channelService.reRouting(channelId, ChannelCategory.ASSISTANCE, null)
        } returns Exception().failure()

        val result = mockFirestoreContextCommit {
            service.reRouting(channelId, ChannelCategory.ASSISTANCE, null)
        }
        assertThat(result).isFailureOfType(Exception::class)

        coVerifyOnce { channelService.reRouting(any(), any(), any()) }

    }

    @OptIn(FirestoreContextUsage::class)
    @Test
    fun `#dischargeChannel returns channel id and discharge chat`() = runBlocking {
        coEvery {
            channelService.dischargeChannel(
                channelId = channelId,
                name = channelDocument.name!!,
                kind = channelDocument.kind!!,
                staffId = staffId,
                category = channelDocument.category,
                subCategory = channelDocument.subCategory
            )
        } returns channelId.success()

        val result = mockFirestoreContextCommit {
            service.dischargeChannel(
                channelId = channelId,
                name = channelDocument.name!!,
                kind = channelDocument.kind!!,
                staffId = staffId,
                category = channelDocument.category,
                subCategory = channelDocument.subCategory
            )
        }
        assertThat(result).isSuccessWithData(channelId)

        coVerifyOnce { channelService.dischargeChannel(any(), any(), any(), any(), any(), any()) }

        coVerify {
            channelNotificationService wasNot called
            healthPlanTaskService wasNot called
            staffService wasNot called
            channelFirestoreService wasNot called
            channelMergeService wasNot called
            channelBackupService wasNot called
        }
    }

    @OptIn(FirestoreContextUsage::class)
    @Test
    fun `#getNextVirtualClinic returns channel id and call addGroup function from virtual clinic queue service`() =
        runBlocking {
            coEvery { virtualClinicQueueService.getNext(staffId.toUUID()) } returns channelId.success()

            val result = mockFirestoreContextCommit { service.getNextVirtualClinic(staffId.toUUID()) }
            assertThat(result).isSuccessWithData(channelId)

            coVerifyOnce { virtualClinicQueueService.getNext(any()) }
        }

    @Test
    fun `#getByScreeningNavigationId returns channel response from channel backup found by filters`() = runBlocking {
        val screeningNavigationId = RangeUUID.generate()
        val channel = TestModelFactory.buildChannel(screeningNavigationId = screeningNavigationId)
        val expected = listOf(ChannelBackupConverter.convert(channel))

        coEvery {
            channelBackupService.findByFilters(
                ChannelBackupServiceFilters(
                    personId = personId,
                    screeningNavigationId = screeningNavigationId,
                    statuses = listOf(ACTIVE)
                )
            )
        } returns listOf(channel).success()

        val result = service.getByScreeningNavigationId(personId, screeningNavigationId, listOf(ACTIVE))
        assertThat(result).isSuccessWithData(expected)

        coVerifyOnce { channelBackupService.findByFilters(any()) }
    }

    @OptIn(FirestoreContextUsage::class)
    @Test
    fun `#assignToN2Staff should assign to staff n2`() =
        runBlocking {
            val expectedStaff = staff.copy(role = Role.NAVIGATOR)

            coEvery { channelService.getChannel(channelId) } returns channelDocument.success()

            coEvery { staffService.get(expectedStaff.id) } returns expectedStaff.success()

            coEvery { channelService.assignToN2Staff(channelDocument, expectedStaff) } returns channelId.success()

            val result = mockFirestoreContextCommit {
                service.assignToN2Staff(channelDocument.id(), expectedStaff.id.toString())
            }
            assertThat(result).isSuccessWithData(channelId)

            coVerifyOnce {
                channelService.getChannel(any())
                staffService.get(any())
                channelService.assignToN2Staff(any(), any())
            }
        }

    @Test
    fun `#assignToN2Staff should return ConflictException when channel already has the tag`() =
        runBlocking {
            val tag = listOf("redirecionado_n2")

            coEvery { channelService.getChannel(channelId) } returns channelDocument.copy(tags = tag).success()

            val result = mockFirestoreContextCommit {
                service.assignToN2Staff(channelDocument.id(), staff.id.toString())
            }
            assertThat(result).isFailureOfType(ConflictException::class)

            coVerifyOnce {
                channelService.getChannel(any())
            }
        }

    @Test
    fun `#assignToN2Staff should return AccessForbidden when staff has not navigator role`() =
        runBlocking {
            coEvery { channelService.getChannel(channelId) } returns channelDocument.success()

            coEvery { staffService.get(staff.id) } returns staff.success()

            val result = mockFirestoreContextCommit {
                service.assignToN2Staff(channelDocument.id(), staff.id.toString())
            }
            assertThat(result).isFailureOfType(AccessForbiddenException::class)

            coVerifyOnce {
                channelService.getChannel(any())
                staffService.get(any())
            }
        }

    @OptIn(FirestoreContextUsage::class)
    @Test
    fun `#sendQuestionMessage - should send question message`() = runBlocking {
        val content = "Olá Mundo!"
        val channelOptions = listOf(ChannelMessageQuestionOptions.YES_ARCHIVE_CHANNEL, ChannelMessageQuestionOptions.DO_NOT_ARCHIVE_CHANNEL)

        coEvery { channelService.sendQuestionMessage(channelId, content, channelOptions) } returns messageId.success()

        val result = channelService.sendQuestionMessage(channelId, content, channelOptions)

        assertThat(result).isSuccessWithData(messageId)

        coVerifyOnce { channelService.sendQuestionMessage(any(), any(), any()) }
    }

}
