package br.com.alice.api.ehr.controllers

import br.com.alice.api.ehr.ControllerTestHelper
import br.com.alice.common.RangeUUID
import br.com.alice.common.core.PersonId
import br.com.alice.common.core.extensions.toPersonId
import br.com.alice.common.core.extensions.toUUID
import br.com.alice.common.data.dsl.matchers.ResponseAssert.Companion.assertThat
import br.com.alice.common.helpers.bodyAsJson
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.BudNode
import br.com.alice.data.layer.models.HealthPlanTaskGroupTemplate
import br.com.alice.data.layer.models.HealthPlanTaskTemplate
import br.com.alice.data.layer.models.Protocol
import br.com.alice.data.layer.models.ScriptAction
import br.com.alice.data.layer.models.ScriptActionType
import br.com.alice.data.layer.models.ServiceScriptAction
import br.com.alice.data.layer.models.ServiceScriptAction.ActionType.HEALTH_CONDITION
import br.com.alice.data.layer.models.ServiceScriptAction.ActionType.HEALTH_PLAN_TASK_GROUP_TEMPLATE
import br.com.alice.data.layer.models.ServiceScriptAction.ActionType.HEALTH_PLAN_TASK_TEMPLATE
import br.com.alice.data.layer.models.ServiceScriptNavigationGroup
import br.com.alice.data.layer.models.ServiceScriptNavigationSource
import br.com.alice.data.layer.models.ServiceScriptNavigationSourceType
import br.com.alice.data.layer.models.ServiceScriptStatus
import br.com.alice.healthlogic.client.BudService
import br.com.alice.healthlogic.models.bud.BudNavigation
import br.com.alice.healthlogic.models.bud.BudNavigationHistory
import br.com.alice.healthlogic.models.bud.NodeWithRelationships
import br.com.alice.healthlogic.models.bud.ProtocolAggregate
import br.com.alice.healthplan.client.HealthPlanTaskGroupTemplateService
import br.com.alice.healthplan.client.HealthPlanTaskTemplateService
import br.com.alice.staff.client.StaffService
import com.github.kittinunf.result.success
import io.mockk.coEvery
import io.mockk.mockk
import org.assertj.core.api.Assertions.assertThat
import kotlin.test.BeforeTest
import kotlin.test.Test


internal class BudControllerTest : ControllerTestHelper() {

    private val budService: BudService = mockk()
    private val staffService: StaffService = mockk()
    private val healthPlanTaskGroupTemplateService: HealthPlanTaskGroupTemplateService = mockk()
    private val healthPlanTaskTemplateService: HealthPlanTaskTemplateService = mockk()

    private val controller = BudController(
        staffService,
        budService,
        healthPlanTaskGroupTemplateService,
        healthPlanTaskTemplateService
    )

    @BeforeTest
    override fun setup() {
        super.setup()

        module.single { controller }
    }

    @Test
    fun `#getCategories call domain service`() {
        val budNode = BudNode(
            name = "name",
            content = "content",
            type = BudNode.BudNodeType.CATEGORY,
            status = ServiceScriptStatus.ACTIVE,
        )
        val expected = listOf(
            BudNodeResponse(
                id = budNode.id,
                actions = budNode.actions,
                privateOrientation = budNode.privateOrientation,
                status = budNode.status,
                type = budNode.type,
                content = budNode.content,
                internalOrientation = budNode.internalOrientation,
                name = budNode.name,
                serviceScriptActionIds = budNode.serviceScriptActionIds
            )
        )
        coEvery { budService.getCategories() } returns listOf(budNode).success()

        authenticatedAs(idToken, staffTest) {
            get("/ehr/bud/categories") { response ->
                assertThat(response).isSuccessfulJson()
                val content: List<BudNodeResponse> = response.bodyAsJson()
                assertThat(content).isEqualTo(expected)
            }
        }
    }

    @Test
    fun `#searchProtocols call domain service`() {
        val healthCondition = TestModelFactory.buildHealthCondition()
        val budNode = BudNode(
            name = "name",
            content = "content",
            type = BudNode.BudNodeType.SCRIPT,
            status = ServiceScriptStatus.ACTIVE,
        )
        val protocol = TestModelFactory.buildProtocol(
            healthConditionIds = listOf(healthCondition.id),
            rootNodeId = budNode.id,
            attributes = Protocol.ProtocolAttributes(
                healthConditions = listOf(
                    Protocol.HealthConditionLight(
                        code = healthCondition.code!!,
                        codeType = healthCondition.codeType,
                        name = healthCondition.name
                    )
                )
            )
        )
        val aggregate = ProtocolAggregate(
            budNode = budNode,
            protocol = protocol
        )

        val expected = listOf(
            BudNodeResponse(
                id = budNode.id,
                actions = budNode.actions,
                privateOrientation = budNode.privateOrientation,
                status = budNode.status,
                type = budNode.type,
                content = budNode.content,
                internalOrientation = budNode.internalOrientation,
                name = budNode.name,
                healthConditions = protocol.attributes.healthConditions,
                serviceScriptActionIds = budNode.serviceScriptActionIds
            )
        )
        coEvery { budService.searchProtocolsV2("name", any()) } returns listOf(aggregate).success()

        authenticatedAs(idToken, staffTest) {
            get("/ehr/bud/protocols?name=name") { response ->
                assertThat(response).isSuccessfulJson()
                val content: List<BudNodeResponse> = response.bodyAsJson()
                assertThat(content).isEqualTo(expected)
            }
        }
    }

    @Test
    fun `#getLastNavigation call domain service`() {
        val personId = PersonId()
        val navigationSource = ServiceScriptNavigationSource(ServiceScriptNavigationSourceType.CHANNEL, "channelId")
        val mockedNavigationHistory = mockk<BudNavigationHistory>()

        coEvery { budService.lastNavigation(personId, navigationSource) } returns mockedNavigationHistory.success()

        authenticatedAs(idToken, staffTest) {
            get("/ehr/bud/$personId/lastNavigation?source=${gson.toJson(navigationSource)}") { response ->
                assertThat(response).isOKWithData(mockedNavigationHistory)
            }
        }
    }

    @Test
    fun `#navigate call domain service`() {
        val nodeId = RangeUUID.generate()
        val navigationSource = ServiceScriptNavigationSource(ServiceScriptNavigationSourceType.CHANNEL, "channelId")
        val budNode = BudNode(
            name = "name",
            content = "content",
            type = BudNode.BudNodeType.SCRIPT,
            status = ServiceScriptStatus.ACTIVE,
        )
        val relationship = TestModelFactory.buildServiceScriptRelationship(parentId = budNode.id)

        val mockedNodeWithRelationships = NodeWithRelationships(
            actions = emptyList(),
            serviceScriptNode = budNode,
            serviceScriptRelationship = listOf(relationship)
        )

        val expectedResponse = NodeWithRelationshipsResponse(
            actions = emptyList(),
            serviceScriptNode = mockedNodeWithRelationships.serviceScriptNode,
            serviceScriptRelationship = mockedNodeWithRelationships.serviceScriptRelationship
        )

        val request = BudNavigationRequest(
            personId = PersonId(),
            scriptNodeId = RangeUUID.generate(),
            previousNodeId = RangeUUID.generate(),
            relationshipId = RangeUUID.generate(),
            source = navigationSource
        )

        val budNavigation = BudNavigation(
            personId = request.personId!!,
            scriptNodeId = request.scriptNodeId!!,
            previousNodeId = request.previousNodeId,
            staffId = staff.id,
            relationshipId = request.relationshipId!!,
            source = request.source
        )
        coEvery { budService.navigateTo(nodeId, budNavigation) } returns mockedNodeWithRelationships.success()

        authenticatedAs(idToken, staffTest) {
            post("/ehr/bud/navigate/$nodeId", request) { response ->
                assertThat(response).isOK()
                val content: NodeWithRelationshipsResponse = response.bodyAsJson()
                assertThat(content).isEqualTo(expectedResponse)
            }
        }
    }

    @Test
    fun `#navigate returns with formatted actions`() {
        val nodeId = RangeUUID.generate()
        val navigationSource = ServiceScriptNavigationSource(ServiceScriptNavigationSourceType.CHANNEL, "channelId")
        val budNode = BudNode(
            name = "name",
            content = "content",
            type = BudNode.BudNodeType.SCRIPT,
            status = ServiceScriptStatus.ACTIVE,
        )
        val relationship = TestModelFactory.buildServiceScriptRelationship(parentId = budNode.id)
        val action = TestModelFactory.buildServiceScriptAction()

        val mockedNodeWithRelationships = NodeWithRelationships(
            actions = listOf(action),
            serviceScriptNode = budNode,
            serviceScriptRelationship = listOf(relationship)
        )
        val taskTemplate1 = TestModelFactory.buildHealthPlanTaskTemplate()
        val taskTemplate2 = TestModelFactory.buildHealthPlanTaskTemplate()
        val taskGroupTemplate1 = HealthPlanTaskGroupTemplate(
            id = action.externalId,
            name = "Exames de rotina",
            taskTemplateIds = listOf(taskTemplate1.id),
            active = true
        )
        val taskGroupTemplate2 = HealthPlanTaskGroupTemplate(
            name = "Exames de rotina",
            taskTemplateIds = listOf(taskTemplate2.id),
            active = true
        )

        val expectedResponse = NodeWithRelationshipsResponse(
            actions = listOf(
                ActionsResponse(
                    actionId = action.id,
                    healthPlanTaskGroupTemplate = taskGroupTemplate1,
                    healthPlanTaskTemplates = listOf(taskTemplate1)
                )
            ),
            serviceScriptNode = mockedNodeWithRelationships.serviceScriptNode,
            serviceScriptRelationship = mockedNodeWithRelationships.serviceScriptRelationship
        )

        val request = BudNavigationRequest(
            personId = PersonId(),
            scriptNodeId = RangeUUID.generate(),
            previousNodeId = RangeUUID.generate(),
            relationshipId = RangeUUID.generate(),
            source = navigationSource
        )

        val budNavigation = BudNavigation(
            personId = request.personId!!,
            scriptNodeId = request.scriptNodeId!!,
            previousNodeId = request.previousNodeId,
            staffId = staff.id,
            relationshipId = request.relationshipId!!,
            source = request.source
        )
        coEvery { budService.navigateTo(nodeId, budNavigation) } returns mockedNodeWithRelationships.success()

        coEvery {
            healthPlanTaskGroupTemplateService.findByIds(listOf(action.externalId))
        } returns listOf(taskGroupTemplate1, taskGroupTemplate2).success()

        coEvery {
            healthPlanTaskTemplateService.findByIds(listOf(taskTemplate1.id, taskTemplate2.id))
        } returns listOf(taskTemplate1, taskTemplate2).success()

        authenticatedAs(idToken, staffTest) {
            post("/ehr/bud/navigate/$nodeId", request) { response ->
                assertThat(response).isOK()
                val content: NodeWithRelationshipsResponse = response.bodyAsJson()
                assertThat(content).isEqualTo(expectedResponse)
            }
        }
    }

    @Test
    fun `#navigate returns with formatted actions for task template and default group`() {
        val nodeId = RangeUUID.generate()
        val navigationSource = ServiceScriptNavigationSource(ServiceScriptNavigationSourceType.CHANNEL, "channelId")
        val budNode = BudNode(
            name = "name",
            content = "content",
            type = BudNode.BudNodeType.SCRIPT,
            status = ServiceScriptStatus.ACTIVE,
        )
        val relationship = TestModelFactory.buildServiceScriptRelationship(parentId = budNode.id)
        val taskAction = TestModelFactory.buildServiceScriptAction(
            type = HEALTH_PLAN_TASK_TEMPLATE
        )

        val groupAction = TestModelFactory.buildServiceScriptAction(
            type = HEALTH_PLAN_TASK_GROUP_TEMPLATE
        )

        val mockedNodeWithRelationships = NodeWithRelationships(
            actions = listOf(taskAction, groupAction),
            serviceScriptNode = budNode,
            serviceScriptRelationship = listOf(relationship)
        )
        val taskTemplateFromTaskAction = TestModelFactory.buildHealthPlanTaskTemplate()
            .copy(id = taskAction.externalId)
        val taskTemplateFromGroupTemplate = TestModelFactory.buildHealthPlanTaskTemplate()
        val taskGroupTemplate1 = HealthPlanTaskGroupTemplate(
            id = groupAction.externalId,
            name = "Exames de rotina",
            taskTemplateIds = listOf(taskTemplateFromGroupTemplate.id),
            active = true
        )
        val defaultGroup = TestModelFactory.buildHealthPlanTaskGroupTemplate(
            name = "Grupo Default",
            id = "8dd06f08-e5de-43e3-b68e-df15c2f36800".toUUID()
        )

        val expectedResponse = NodeWithRelationshipsResponse(
            actions = listOf(
                ActionsResponse(
                    actionId = taskAction.id,
                    healthPlanTaskGroupTemplate = defaultGroup,
                    healthPlanTaskTemplates = listOf(taskTemplateFromTaskAction)
                ),
                ActionsResponse(
                    actionId = groupAction.id,
                    healthPlanTaskGroupTemplate = taskGroupTemplate1,
                    healthPlanTaskTemplates = listOf(taskTemplateFromGroupTemplate)
                ),
            ),
            serviceScriptNode = mockedNodeWithRelationships.serviceScriptNode,
            serviceScriptRelationship = mockedNodeWithRelationships.serviceScriptRelationship
        )

        val request = BudNavigationRequest(
            personId = PersonId(),
            scriptNodeId = RangeUUID.generate(),
            previousNodeId = RangeUUID.generate(),
            relationshipId = RangeUUID.generate(),
            source = navigationSource
        )

        val budNavigation = BudNavigation(
            personId = request.personId!!,
            scriptNodeId = request.scriptNodeId!!,
            previousNodeId = request.previousNodeId,
            staffId = staff.id,
            relationshipId = request.relationshipId!!,
            source = request.source
        )
        coEvery { budService.navigateTo(nodeId, budNavigation) } returns mockedNodeWithRelationships.success()

        coEvery {
            healthPlanTaskGroupTemplateService.findByIds(listOf(groupAction.externalId))
        } returns listOf(taskGroupTemplate1).success()

        coEvery {
            healthPlanTaskGroupTemplateService.get(defaultGroup.id)
        } returns defaultGroup.success()

        coEvery {
            healthPlanTaskTemplateService.findByIds(
                listOf(
                    taskTemplateFromGroupTemplate.id,
                    taskTemplateFromTaskAction.id
                )
            )
        } returns listOf(taskTemplateFromGroupTemplate, taskTemplateFromTaskAction).success()

        authenticatedAs(idToken, staffTest) {
            post("/ehr/bud/navigate/$nodeId", request) { response ->
                assertThat(response).isOK()
                val content: NodeWithRelationshipsResponse = response.bodyAsJson()
                assertThat(content).isEqualTo(expectedResponse)
            }
        }
    }

    @Test
    fun `#navigate returns with empty actions if action externalId differs from template id`() {
        val nodeId = RangeUUID.generate()
        val navigationSource = ServiceScriptNavigationSource(ServiceScriptNavigationSourceType.CHANNEL, "channelId")
        val budNode = BudNode(
            name = "name",
            content = "content",
            type = BudNode.BudNodeType.SCRIPT,
            status = ServiceScriptStatus.ACTIVE,
        )
        val relationship = TestModelFactory.buildServiceScriptRelationship(parentId = budNode.id)
        val action = TestModelFactory.buildServiceScriptAction(
            type = HEALTH_CONDITION
        )

        val mockedNodeWithRelationships = NodeWithRelationships(
            actions = listOf(action),
            serviceScriptNode = budNode,
            serviceScriptRelationship = listOf(relationship)
        )

        val expectedResponse = NodeWithRelationshipsResponse(
            actions = listOf(
                ActionsResponse(
                    actionId = action.id,
                    healthPlanTaskGroupTemplate = null,
                    healthPlanTaskTemplates = emptyList()
                )
            ),
            serviceScriptNode = mockedNodeWithRelationships.serviceScriptNode,
            serviceScriptRelationship = mockedNodeWithRelationships.serviceScriptRelationship
        )

        val request = BudNavigationRequest(
            personId = PersonId(),
            scriptNodeId = RangeUUID.generate(),
            previousNodeId = RangeUUID.generate(),
            relationshipId = RangeUUID.generate(),
            source = navigationSource
        )

        val budNavigation = BudNavigation(
            personId = request.personId!!,
            scriptNodeId = request.scriptNodeId!!,
            previousNodeId = request.previousNodeId,
            staffId = staff.id,
            relationshipId = request.relationshipId!!,
            source = request.source
        )
        coEvery { budService.navigateTo(nodeId, budNavigation) } returns mockedNodeWithRelationships.success()

        coEvery {
            healthPlanTaskGroupTemplateService.findByIds(emptyList())
        } returns emptyList<HealthPlanTaskGroupTemplate>().success()

        coEvery {
            healthPlanTaskTemplateService.findByIds(emptyList())
        } returns emptyList<HealthPlanTaskTemplate>().success()

        authenticatedAs(idToken, staffTest) {
            post("/ehr/bud/navigate/$nodeId", request) { response ->
                assertThat(response).isOK()
                val content: NodeWithRelationshipsResponse = response.bodyAsJson()
                assertThat(content).isEqualTo(expectedResponse)
            }
        }
    }

    @Test
    fun `#finishNavigation call domain service`() {
        val personId = PersonId()
        val scriptNodeId = RangeUUID.generate()
        val navigationSource = ServiceScriptNavigationSource(ServiceScriptNavigationSourceType.CHANNEL, "channelId")
        val mockedServiceScriptNavigationGroup = mockk<ServiceScriptNavigationGroup>()

        coEvery {
            budService.finishNavigation(personId, scriptNodeId, navigationSource)
        } returns mockedServiceScriptNavigationGroup.success()

        authenticatedAs(idToken, staffTest) {
            post("/ehr/bud/$personId/finishNavigation/$scriptNodeId?source=${gson.toJson(navigationSource)}") { response ->
                assertThat(response).isOK()
            }
        }
    }

    @Test
    fun `#triggerAction returns ok and trigger action`() {
        val action = ScriptAction(type = ScriptActionType.QUESTIONNAIRE, content = emptyMap())
        val nodeId = RangeUUID.generate()
        val request = TriggerActionRequest(PersonId().id, "channel_id")

        coEvery {
            budService.triggerAction(
                nodeId,
                action.id,
                request.personId.toPersonId(),
                request.sourceId,
                staff.id
            )
        } returns action.success()

        authenticatedAs(idToken, staffTest) {
            post("/ehr/bud/$nodeId/action/${action.id}", body = request) { response ->
                assertThat(response).isOK()

                val parsedResponse = response.bodyAsJson<ScriptAction>()
                assertThat(parsedResponse).usingRecursiveComparison().isEqualTo(action)
            }
        }
    }

    @Test
    fun `#triggerActionV2 returns ok and trigger action`() {
        val action = TestModelFactory.buildServiceScriptAction()
        val nodeId = RangeUUID.generate()
        val request = TriggerActionRequest(PersonId().id, "channel_id")

        coEvery {
            budService.triggerActionV2(
                nodeId,
                action.id,
                request.personId.toPersonId(),
                request.sourceId,
                staff.id
            )
        } returns action.success()

        authenticatedAs(idToken, staffTest) {
            post("/ehr/bud/$nodeId/action/v2/${action.id}", body = request) { response ->
                assertThat(response).isOK()

                val parsedResponse = response.bodyAsJson<ServiceScriptAction>()
                assertThat(parsedResponse).usingRecursiveComparison().isEqualTo(action)
            }
        }
    }
}
