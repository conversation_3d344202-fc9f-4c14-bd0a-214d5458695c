package br.com.alice.exec.indicator.service

import br.com.alice.common.RangeUUID
import br.com.alice.common.data.dsl.matchers.ResultAssert.Companion.assertThat
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.HealthSpecialistProcedureExecutionEnvironment
import br.com.alice.data.layer.models.HealthSpecialistResourceBundleServiceType
import br.com.alice.exec.indicator.client.HealthSpecialistResourceBundleService
import br.com.alice.exec.indicator.client.HealthcareResourceService
import br.com.alice.exec.indicator.client.HealthcareResourceService.Companion.DEFAULT_PRIMARY_TUSS_TABLE
import br.com.alice.exec.indicator.client.TussCodeWithAliceCodeRequest
import br.com.alice.exec.indicator.client.TussProcedureSpecialtyService
import br.com.alice.exec.indicator.models.HealthSpecialistProcedure
import br.com.alice.exec.indicator.models.HealthSpecialistProcedureServiceType
import com.github.kittinunf.result.success
import io.mockk.clearAllMocks
import io.mockk.coEvery
import io.mockk.mockk
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.TestInstance
import kotlin.test.AfterTest
import kotlin.test.Test

@TestInstance(TestInstance.Lifecycle.PER_CLASS)
class HealthSpecialistProcedureServiceImplTest {
    private val healthSpecialistResourceBundleService: HealthSpecialistResourceBundleService = mockk()
    private val healthcareResourceService: HealthcareResourceService = mockk()
    private val tussProcedureSpecialtyService: TussProcedureSpecialtyService = mockk()

    private val healthSpecialistProcedureService = HealthSpecialistProcedureServiceImpl(
        healthSpecialistResourceBundleService,
        healthcareResourceService,
        tussProcedureSpecialtyService,
    )
    val medicalSpecialtyId = RangeUUID.generate()
    val otherMedicalSpecialtyId = RangeUUID.generate()

    private val healthcareResources = listOf(
        TestModelFactory.buildHealthcareResource(
            description = "description",
            code = "1",
        ),
        TestModelFactory.buildHealthcareResource(
            description = "description",
            code = "2",
        ),
        TestModelFactory.buildHealthcareResource(
            description = "description",
            code = "3",
        ),
        TestModelFactory.buildHealthcareResource(
            description = "description",
            code = "4",
        ),
    )

    private val healthSpecialistResourceBundles = listOf(
        TestModelFactory.buildHealthSpecialistResourceBundle(
            description = "description",
            primaryTuss = "1",
            code = "11",
            serviceType = HealthSpecialistResourceBundleServiceType.CONSULTATION
        ),
        TestModelFactory.buildHealthSpecialistResourceBundle(
            description = "description",
            primaryTuss = "2",
            code = "22",
            serviceType = HealthSpecialistResourceBundleServiceType.PROCEDURE,
            executionEnvironment = HealthSpecialistProcedureExecutionEnvironment.OUTPATIENT
        ),
        TestModelFactory.buildHealthSpecialistResourceBundle(
            description = "description",
            primaryTuss = "3",
            code = "33",
            serviceType = HealthSpecialistResourceBundleServiceType.PROCEDURE,
            executionEnvironment = HealthSpecialistProcedureExecutionEnvironment.SURGICAL
        ),
    )

    private val tussProcedureSpecialties = listOf(
        TestModelFactory.buildTussProcedureSpecialty(
            aliceCode = "11",
            medicalSpecialtyId = medicalSpecialtyId,
        ),
        TestModelFactory.buildTussProcedureSpecialty(
            aliceCode = "22",
            medicalSpecialtyId = otherMedicalSpecialtyId,
        ),
    )

    @AfterTest
    fun setup() = clearAllMocks()

    @Test
    fun `#getProceduresForExecution should get only priced procedure codes for appointment successfully`() = runBlocking<Unit> {
        val query = "testing"
        val shouldGetOnlyPriced = true

        val expectedResponse = listOf(
            HealthSpecialistProcedure(
                tussCode = "1",
                description = "description",
                aliceCode = "11",
                hasPrice = true,
                serviceType = HealthSpecialistProcedureServiceType.CONSULTATION
            ),
            HealthSpecialistProcedure(
                tussCode = "2",
                description = "description",
                aliceCode = "22",
                hasPrice = false,
                serviceType = HealthSpecialistProcedureServiceType.OUTPATIENT_PROCEDURE
            ),
            HealthSpecialistProcedure(
                tussCode = "3",
                description = "description",
                aliceCode = "33",
                hasPrice = false,
                serviceType = HealthSpecialistProcedureServiceType.SURGICAL_PROCEDURE
            ),
        )

        coEvery {
            healthSpecialistResourceBundleService.search(query, 30)
        } returns healthSpecialistResourceBundles.success()

        coEvery {
            tussProcedureSpecialtyService.findActiveByAliceCodesAndSpeciality(
                medicalSpecialtyId,
                healthSpecialistResourceBundles.map { it.code }
            )
        } returns tussProcedureSpecialties.take(1).success()

        val result = healthSpecialistProcedureService.getProceduresForExecution(
            medicalSpecialtyId,
            query,
            shouldGetOnlyPriced
        )

        assertThat(result).isSuccessWithData(expectedResponse)
    }

    @Test
    fun `#getProceduresForExecution should get only priced procedure codes for appointment successfully, filtering if priced by specialty`() = runBlocking<Unit> {
        val query = "testing"
        val shouldGetOnlyPriced = true

        val expectedResponse = listOf(
            HealthSpecialistProcedure(
                tussCode = "1",
                description = "description",
                aliceCode = "11",
                hasPrice = true,
                serviceType = HealthSpecialistProcedureServiceType.CONSULTATION
            ),
        )

        coEvery {
            healthSpecialistResourceBundleService.search(query, 30)
        } returns healthSpecialistResourceBundles.success()

        coEvery {
            tussProcedureSpecialtyService.findActiveByAliceCodesAndSpeciality(
                medicalSpecialtyId,
                healthSpecialistResourceBundles.map { it.code }
            )
        } returns tussProcedureSpecialties.take(1).success()

        val result = healthSpecialistProcedureService.getProceduresForExecution(
            medicalSpecialtyId,
            query,
            shouldGetOnlyPriced,
            filterBySpecialty = true
        )

        assertThat(result).isSuccessWithData(expectedResponse)
    }

    @Test
    fun `#getProceduresForExecution should get not only priced procedures, querying also on healthcare resource for appointment successfully`() = runBlocking<Unit> {
        val query = "testing"
        val shouldGetOnlyPriced = false

        val expectedResponse = listOf(
            HealthSpecialistProcedure(
                tussCode = "1",
                description = "description",
                aliceCode = "11",
                hasPrice = true,
                serviceType = HealthSpecialistProcedureServiceType.CONSULTATION
            ),
            HealthSpecialistProcedure(
                tussCode = "2",
                description = "description",
                aliceCode = "22",
                hasPrice = false,
                serviceType = HealthSpecialistProcedureServiceType.OUTPATIENT_PROCEDURE
            ),
            HealthSpecialistProcedure(
                tussCode = "3",
                description = "description",
                aliceCode = "33",
                hasPrice = false,
                serviceType = HealthSpecialistProcedureServiceType.SURGICAL_PROCEDURE
            ),
            HealthSpecialistProcedure(
                tussCode = "4",
                description = "description",
                aliceCode = "4",
                hasPrice = false,
                serviceType = null
            ),
        )

        coEvery {
            healthSpecialistResourceBundleService.search(query, 30)
        } returns healthSpecialistResourceBundles.success()

        coEvery {
            tussProcedureSpecialtyService.findActiveByAliceCodesAndSpeciality(
                medicalSpecialtyId,
                healthSpecialistResourceBundles.map { it.code }
            )
        } returns tussProcedureSpecialties.take(1).success()

        coEvery { healthcareResourceService.findBySearchTokensAndTableTypesPaginated(
            query,
            limit = 30,
            tableTypes = listOf(DEFAULT_PRIMARY_TUSS_TABLE)
        ) } returns healthcareResources.success()

        val result = healthSpecialistProcedureService.getProceduresForExecution(
            medicalSpecialtyId,
            query,
            shouldGetOnlyPriced
        )

        assertThat(result).isSuccessWithData(expectedResponse)
    }

    @Test
    fun `#getProceduresForReferral should get all procedures successfully`() = runBlocking<Unit> {
        val query = "testing"
        val limit = 30

        val expectedResponse = listOf(
            HealthSpecialistProcedure(
                tussCode = "1",
                description = "description",
                aliceCode = "1",
                hasPrice = false,
            ),
            HealthSpecialistProcedure(
                tussCode = "2",
                description = "description",
                aliceCode = "2",
                hasPrice = false,
            ),
            HealthSpecialistProcedure(
                tussCode = "3",
                description = "description",
                aliceCode = "3",
                hasPrice = false,
            ),
            HealthSpecialistProcedure(
                tussCode = "4",
                description = "description",
                aliceCode = "4",
                hasPrice = false,
            ),
        )

        coEvery { healthcareResourceService.findBySearchTokensAndTableTypesPaginated(
            query,
            limit = 30,
            tableTypes = listOf(DEFAULT_PRIMARY_TUSS_TABLE)
        ) } returns healthcareResources.success()

        val result = healthSpecialistProcedureService.getProceduresForReferral(
            query,
            limit
        )

        assertThat(result).isSuccessWithData(expectedResponse)
    }

    @Test
    fun `#findByTussCodesOrHealthSpecialistResourceBundleCodes should find by tuss codes or health specialist resource bundle codes successfully`() = runBlocking<Unit> {
        val request = listOf(
            TussCodeWithAliceCodeRequest(
                tussCode = "1",
                aliceCode = "11",
            ),
            TussCodeWithAliceCodeRequest(
                tussCode = "2",
                aliceCode = "22",
            ),
            TussCodeWithAliceCodeRequest(
                tussCode = "3",
                aliceCode = "33",
            ),
            TussCodeWithAliceCodeRequest(
                tussCode = "4",
                aliceCode = "44",
            ),
        )

        val expectedResponse = listOf(
            HealthSpecialistProcedure(
                tussCode = "1",
                description = "description",
                aliceCode = "11",
                hasPrice = false,
                serviceType = HealthSpecialistProcedureServiceType.CONSULTATION
            ),
            HealthSpecialistProcedure(
                tussCode = "2",
                description = "description",
                aliceCode = "22",
                hasPrice = false,
                serviceType = HealthSpecialistProcedureServiceType.OUTPATIENT_PROCEDURE
            ),
            HealthSpecialistProcedure(
                tussCode = "3",
                description = "description",
                aliceCode = "33",
                hasPrice = false,
                serviceType = HealthSpecialistProcedureServiceType.SURGICAL_PROCEDURE
            ),
            HealthSpecialistProcedure(
                tussCode = "4",
                description = "description",
                aliceCode = "4",
                hasPrice = false,
                serviceType = HealthSpecialistProcedureServiceType.EXAM
            ),
        )

        coEvery {
            healthSpecialistResourceBundleService.findByCodes(request.mapNotNull { it.aliceCode })
        } returns healthSpecialistResourceBundles.success()

        coEvery {
            healthcareResourceService.findByCodesAndTableType(request.map { it.tussCode }, DEFAULT_PRIMARY_TUSS_TABLE)
        } returns healthcareResources.success()

        val result = healthSpecialistProcedureService.findByTussCodesOrHealthSpecialistResourceBundleCodes(request)

        assertThat(result).isSuccessWithData(expectedResponse)
    }
}
