package br.com.alice.person.services

import br.com.alice.common.core.extensions.atBeginningOfTheDay
import br.com.alice.common.core.extensions.atBeginningOfTheMonth
import br.com.alice.common.core.extensions.atEndOfTheDay
import br.com.alice.common.core.extensions.atEndOfTheMonth
import br.com.alice.common.extensions.mapEach
import br.com.alice.common.service.data.dsl.and
import br.com.alice.data.layer.models.Member
import br.com.alice.data.layer.models.MemberModel
import br.com.alice.data.layer.models.MemberProductPriceAdjustment
import br.com.alice.data.layer.models.MemberProductPriceAdjustmentModel
import br.com.alice.data.layer.models.MemberStatus
import br.com.alice.data.layer.services.MemberModelDataService
import br.com.alice.data.layer.services.MemberProductPriceAdjustmentModelDataService
import br.com.alice.person.client.MemberProductPriceAdjustmentService
import br.com.alice.person.converters.toModel
import br.com.alice.person.converters.toTransport
import br.com.alice.person.model.MemberPriceAdjustmentWithProduct
import br.com.alice.product.client.ProductPriceAdjustmentService
import br.com.alice.product.client.ProductService
import com.github.kittinunf.result.Result
import com.github.kittinunf.result.map
import com.github.kittinunf.result.success
import java.time.LocalDate
import java.util.UUID

class MemberProductPriceAdjustmentServiceImpl(
    private val memberDataService: MemberModelDataService,
    private val dataService: MemberProductPriceAdjustmentModelDataService,
    private val productService: ProductService,
    private val productPriceAdjustmentService: ProductPriceAdjustmentService,
): MemberProductPriceAdjustmentService {
    override suspend fun add(memberProductPriceAdjustment: MemberProductPriceAdjustment): Result<MemberProductPriceAdjustment, Throwable> =
        dataService.add(memberProductPriceAdjustment.toModel()).map { it.toTransport() }

    override suspend fun findEligibleMembers(referenceDate: LocalDate): Result<List<Member>, Throwable> {
        val monthYearToSearch = referenceDate.minusYears(1L)
        val eligibleMembers = memberDataService.find {
            where {
                this.status.eq(MemberStatus.ACTIVE)
                    .and(this.activationDate.greaterEq(monthYearToSearch.atBeginningOfTheMonth().atBeginningOfTheDay()))
                    .and(this.activationDate.lessEq(monthYearToSearch.atEndOfTheMonth().atEndOfTheDay()))
            }
        }

        return eligibleMembers.mapEach { it.toTransport() }
    }

    override suspend fun findByProductPriceAdjustment(productPriceAdjustmentId: UUID): Result<List<MemberProductPriceAdjustment>, Throwable> =
        dataService.find {
            where { this.productPriceAdjustmentId.eq(productPriceAdjustmentId) }
        }.mapEach { it.toTransport() }

    override suspend fun findByMemberIdsAndAdjustedDate(
        memberIds: List<UUID>,
        adjustedDate: LocalDate
    ): Result<List<MemberPriceAdjustmentWithProduct>, Throwable> {
        val memberWithAdjustment = queryByMemberIdsAndAdjustedDate(memberIds, adjustedDate)
                .map { memberDataService.get(it.memberId).get() to it }

        if (memberWithAdjustment.isEmpty()) return emptyList<MemberPriceAdjustmentWithProduct>().success()

        val adjustments = findAdjustedMembersAdjustment(memberWithAdjustment)
        val products = findAdjustedMembersProduct(memberWithAdjustment)

        return memberWithAdjustment.map { (member, memberAdjustment) ->
            MemberPriceAdjustmentWithProduct(
                memberProductPriceAdjustment = memberAdjustment.toTransport(),
                member = member.toTransport(),
                priceAdjustment = adjustments.find { it.id == memberAdjustment.productPriceAdjustmentId }!!,
                product = products.find { it.id == member.productId }!!
            )
        }.success()
    }

    private suspend fun queryByMemberIdsAndAdjustedDate(memberIds: List<UUID>, adjustedDate: LocalDate) =
        dataService.find {
            where {
                this.memberId.inList(memberIds)
                    .and(this.createdAt.greaterEq(adjustedDate.atBeginningOfTheMonth().atBeginningOfTheDay()))
                    .and(this.createdAt.lessEq(adjustedDate.atEndOfTheMonth().atEndOfTheDay()))
            }
        }.get()

    private suspend fun findAdjustedMembersAdjustment(memberWithAdjustment: List<Pair<MemberModel, MemberProductPriceAdjustmentModel>>) =
        productPriceAdjustmentService.findByIds(memberWithAdjustment.map { it.second.productPriceAdjustmentId }.distinct()).get()

    private suspend fun findAdjustedMembersProduct(memberWithAdjustment: List<Pair<MemberModel, MemberProductPriceAdjustmentModel>>) =
        productService.findByIds(memberWithAdjustment.map { it.first.productId }.distinct()).get()

}
