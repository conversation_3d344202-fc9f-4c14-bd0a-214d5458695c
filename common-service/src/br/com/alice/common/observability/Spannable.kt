package br.com.alice.common.observability

import br.com.alice.common.core.Model
import br.com.alice.common.core.extensions.classSimpleName
import br.com.alice.common.extensions.then
import br.com.alice.common.extensions.thenError
import br.com.alice.common.observability.opentelemetry.CommonServerAttributes.serviceName
import br.com.alice.common.observability.opentelemetry.Tracer
import com.github.kittinunf.result.Result
import io.ktor.http.Parameters
import io.opentelemetry.api.trace.Span
import io.opentelemetry.api.trace.SpanKind
import io.opentelemetry.api.trace.StatusCode
import kotlin.reflect.full.memberProperties

interface Spannable {

    suspend fun <T> span(
        methodName: String,
        kind: SpanKind = SpanKind.INTERNAL,
        block: suspend (Span) -> T
    ) =
        Tracer.span("${this.classSimpleName()}.$methodName", kind) { span ->
            span.setAttribute("class", this.classSimpleName())
            span.setAttribute("method", methodName)
            block(span)
        }

}

fun <T: Any> Result<T, Throwable>.recordResult(span: Span) =
    recordResult("result", span)

fun <T: Any> Result<T, Throwable>.recordResult(attributeName: String, span: Span) =
    this.then { value ->
        span.setStatus(StatusCode.OK)
        span.setAttribute(attributeName, value)
    }.thenError { error ->
        span.setStatus(StatusCode.ERROR)
        span.recordException(error)
    }

fun Span.setAttribute(key: String, value: Any?) {
    val valueParsed = when {
        value == null -> "null_value"
        value is Model -> value.id.toString()
        value::class.isData ->
            value::class.memberProperties.firstOrNull { it.name == "id" }?.getter?.call(value) ?: value.classSimpleName()
        value is Collection<*> -> value.size
        value is Map<*, *> -> value.size
        else -> value
    }.toString()

    this.setAttribute(key, valueParsed)
}

fun Span.setParameters(parameters: Parameters) {
    parameters.entries().forEach { entry ->
        this.setAttribute(entry.key, entry.value.toString())
    }
}
