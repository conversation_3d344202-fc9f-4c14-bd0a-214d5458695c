package app.secondary_attention_domain_service_test

import rego.v1
import data.app.secondary_attention_domain_service

test_view_HealthProfessionalModel_allowed if {
    1 in secondary_attention_domain_service.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "view",
                "resource": {
                    "opaType": "HealthProfessionalModel"
                }
            }
        ]
    }
}

test_count_HealthProfessionalModel_allowed if {
    1 in secondary_attention_domain_service.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "count",
                "resource": {
                    "opaType": "HealthProfessionalModel"
                }
            }
        ]
    }
}

test_view_StaffModel_allowed if {
    1 in secondary_attention_domain_service.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "view",
                "resource": {
                    "opaType": "StaffModel"
                }
            }
        ]
    }
}

test_count_StaffModel_allowed if {
    1 in secondary_attention_domain_service.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "count",
                "resource": {
                    "opaType": "StaffModel"
                }
            }
        ]
    }
}

test_view_TussProcedureSpecialty_allowed if {
    1 in secondary_attention_domain_service.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "view",
                "resource": {
                    "opaType": "TussProcedureSpecialtyModel"
                }
            }
        ]
    }
}

test_count_TussProcedureSpecialty_allowed if {
    1 in secondary_attention_domain_service.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "count",
                "resource": {
                    "opaType": "TussProcedureSpecialtyModel"
                }
            }
        ]
    }
}

test_create_TussProcedureSpecialty_allowed if {
    1 in secondary_attention_domain_service.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "create",
                "resource": {
                    "opaType": "TussProcedureSpecialtyModel"
                }
            }
        ]
    }
}

test_update_TussProcedureSpecialty_allowed if {
    1 in secondary_attention_domain_service.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "update",
                "resource": {
                    "opaType": "TussProcedureSpecialtyModel"
                }
            }
        ]
    }
}

test_unauth_view_FileVault_allowed if {
    1 in secondary_attention_domain_service.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {
                    "opaType": "Unauthenticated"
                },
                "resource": {
                    "opaType": "FileVault"
                }
            }
        ]
    }
}

test_unauth_count_FileVault_allowed if {
    1 in secondary_attention_domain_service.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "count",
                "subject": {
                    "opaType": "Unauthenticated"
                },
                "resource": {
                    "opaType": "FileVault"
                }
            }
        ]
    }
}

test_unauth_update_FileVault_allowed if {
    1 in secondary_attention_domain_service.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "update",
                "subject": {
                    "opaType": "Unauthenticated"
                },
                "resource": {
                    "opaType": "FileVault"
                }
            }
        ]
    }
}

test_unauth_count_GenericFileVault_allowed if {
    1 in secondary_attention_domain_service.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "count",
                "subject": {
                    "opaType": "Unauthenticated"
                },
                "resource": {
                    "opaType": "GenericFileVault"
                }
            }
        ]
    }
}

test_unauth_view_GenericFileVault_allowed if {
    1 in secondary_attention_domain_service.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {
                    "opaType": "Unauthenticated"
                },
                "resource": {
                    "opaType": "GenericFileVault"
                }
            }
        ]
    }
}

test_unauth_update_GenericFileVault_allowed if {
    1 in secondary_attention_domain_service.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "update",
                "subject": {
                    "opaType": "Unauthenticated"
                },
                "resource": {
                    "opaType": "GenericFileVault"
                }
            }
        ]
    }
}

test_unauth_count_Timeline_allowed if {
    1 in secondary_attention_domain_service.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "count",
                "subject": {
                    "opaType": "Unauthenticated"
                },
                "resource": {
                    "opaType": "Timeline"
                }
            }
        ]
    }
}

test_unauth_view_Timeline_allowed if {
    1 in secondary_attention_domain_service.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {
                    "opaType": "Unauthenticated"
                },
                "resource": {
                    "opaType": "Timeline"
                }
            }
        ]
    }
}

test_unauth_update_Timeline_allowed if {
    1 in secondary_attention_domain_service.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "update",
                "subject": {
                    "opaType": "Unauthenticated"
                },
                "resource": {
                    "opaType": "Timeline"
                }
            }
        ]
    }
}

test_unauth_view_CounterReferral_allowed if {
    1 in secondary_attention_domain_service.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {
                    "opaType": "Unauthenticated"
                },
                "resource": {
                    "opaType": "CounterReferral"
                }
            }
        ]
    }
}

test_unauth_count_CounterReferral_allowed if {
    1 in secondary_attention_domain_service.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "count",
                "subject": {
                    "opaType": "Unauthenticated"
                },
                "resource": {
                    "opaType": "CounterReferral"
                }
            }
        ]
    }
}

test_unauth_count_HealthPlan_allowed if {
    1 in secondary_attention_domain_service.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "count",
                "subject": {
                    "opaType": "Unauthenticated"
                },
                "resource": {
                    "opaType": "HealthPlan"
                }
            }
        ]
    }
}

test_unauth_view_HealthPlan_allowed if {
    1 in secondary_attention_domain_service.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {
                    "opaType": "Unauthenticated"
                },
                "resource": {
                    "opaType": "HealthPlan"
                }
            }
        ]
    }
}

test_unauth_view_HealthCommunitySpecialistModel_allowed if {
    1 in secondary_attention_domain_service.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {
                    "opaType": "Unauthenticated"
                },
                "resource": {
                    "opaType": "HealthCommunitySpecialistModel"
                }
            }
        ]
    }
}

test_unauth_count_HealthCommunitySpecialistModel_allowed if {
    1 in secondary_attention_domain_service.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "count",
                "subject": {
                    "opaType": "Unauthenticated"
                },
                "resource": {
                    "opaType": "HealthCommunitySpecialistModel"
                }
            }
        ]
    }
}

test_unauth_view_MedicalSpecialtyModel_allowed if {
    1 in secondary_attention_domain_service.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {
                    "opaType": "Unauthenticated"
                },
                "resource": {
                    "opaType": "MedicalSpecialtyModel"
                }
            }
        ]
    }
}

test_unauth_count_MedicalSpecialtyModel_allowed if {
    1 in secondary_attention_domain_service.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "count",
                "subject": {
                    "opaType": "Unauthenticated"
                },
                "resource": {
                    "opaType": "MedicalSpecialtyModel"
                }
            }
        ]
    }
}

test_unauth_view_SpecialistOpinion_allowed if {
    1 in secondary_attention_domain_service.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {
                    "opaType": "Unauthenticated"
                },
                "resource": {
                    "opaType": "SpecialistOpinion"
                }
            }
        ]
    }
}

test_unauth_count_SpecialistOpinion_allowed if {
    1 in secondary_attention_domain_service.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "count",
                "subject": {
                    "opaType": "Unauthenticated"
                },
                "resource": {
                    "opaType": "SpecialistOpinion"
                }
            }
        ]
    }
}

test_unauth_create_SpecialistOpinion_allowed if {
    1 in secondary_attention_domain_service.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "create",
                "subject": {
                    "opaType": "Unauthenticated"
                },
                "resource": {
                    "opaType": "SpecialistOpinion"
                }
            }
        ]
    }
}

test_unauth_update_SpecialistOpinion_allowed if {
    1 in secondary_attention_domain_service.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "update",
                "subject": {
                    "opaType": "Unauthenticated"
                },
                "resource": {
                    "opaType": "SpecialistOpinion"
                }
            }
        ]
    }
}

test_unauth_view_HealthPlanTaskReferrals_allowed if {
    1 in secondary_attention_domain_service.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {
                    "opaType": "Unauthenticated"
                },
                "resource": {
                    "opaType": "HealthPlanTaskReferrals"
                }
            }
        ]
    }
}

test_unauth_count_HealthPlanTaskReferrals_allowed if {
    1 in secondary_attention_domain_service.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "count",
                "subject": {
                    "opaType": "Unauthenticated"
                },
                "resource": {
                    "opaType": "HealthPlanTaskReferrals"
                }
            }
        ]
    }
}

test_unauth_create_HealthPlanTaskReferrals_allowed if {
    1 in secondary_attention_domain_service.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "create",
                "subject": {
                    "opaType": "Unauthenticated"
                },
                "resource": {
                    "opaType": "HealthPlanTaskReferrals"
                }
            }
        ]
    }
}

test_unauth_update_HealthPlanTaskReferrals_allowed if {
    1 in secondary_attention_domain_service.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "update",
                "subject": {
                    "opaType": "Unauthenticated"
                },
                "resource": {
                    "opaType": "HealthPlanTaskReferrals"
                }
            }
        ]
    }
}

test_unauth_view_HealthPlanTask_allowed if {
    1 in secondary_attention_domain_service.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {
                    "opaType": "Unauthenticated"
                },
                "resource": {
                    "opaType": "HealthPlanTask"
                }
            }
        ]
    }
}

test_unauth_count_HealthPlanTask_allowed if {
    1 in secondary_attention_domain_service.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "count",
                "subject": {
                    "opaType": "Unauthenticated"
                },
                "resource": {
                    "opaType": "HealthPlanTask"
                }
            }
        ]
    }
}

test_unauth_create_HealthPlanTask_allowed if {
    1 in secondary_attention_domain_service.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "create",
                "subject": {
                    "opaType": "Unauthenticated"
                },
                "resource": {
                    "opaType": "HealthPlanTask"
                }
            }
        ]
    }
}

test_unauth_update_HealthPlanTask_allowed if {
    1 in secondary_attention_domain_service.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "update",
                "subject": {
                    "opaType": "Unauthenticated"
                },
                "resource": {
                    "opaType": "HealthPlanTask"
                }
            }
        ]
    }
}

test_unauth_view_HealthPlanTaskGroup_allowed if {
    1 in secondary_attention_domain_service.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {
                    "opaType": "Unauthenticated"
                },
                "resource": {
                    "opaType": "HealthPlanTaskGroup"
                }
            }
        ]
    }
}

test_unauth_count_HealthPlanTaskGroup_allowed if {
    1 in secondary_attention_domain_service.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "count",
                "subject": {
                    "opaType": "Unauthenticated"
                },
                "resource": {
                    "opaType": "HealthPlanTaskGroup"
                }
            }
        ]
    }
}

test_unauth_create_HealthPlanTaskGroup_allowed if {
    1 in secondary_attention_domain_service.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "create",
                "subject": {
                    "opaType": "Unauthenticated"
                },
                "resource": {
                    "opaType": "HealthPlanTaskGroup"
                }
            }
        ]
    }
}

test_unauth_update_HealthPlanTaskGroup_allowed if {
    1 in secondary_attention_domain_service.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "update",
                "subject": {
                    "opaType": "Unauthenticated"
                },
                "resource": {
                    "opaType": "HealthPlanTaskGroup"
                }
            }
        ]
    }
}
