package br.com.alice.api.ops.controllers

import br.com.alice.common.MultipartRequest
import br.com.alice.common.Response
import br.com.alice.common.core.extensions.toUUID
import br.com.alice.common.extensions.flatMapPair
import br.com.alice.common.foldResponse
import br.com.alice.common.service.serialization.isoDateWithoutFieldPolicyGson
import br.com.alice.data.layer.models.FormType
import br.com.alice.data.layer.models.HealthForm
import br.com.alice.data.layer.models.HealthFormQuestion
import br.com.alice.data.layer.models.HealthFormSection
import br.com.alice.healthlogic.client.ClinicalOutcomesConsolidatedCalculatorConfService
import br.com.alice.healthlogic.client.HealthFormOutcomeCalculatorConfService
import br.com.alice.questionnaire.client.HealthFormManagementService
import com.github.kittinunf.result.Result
import com.github.kittinunf.result.map
import io.ktor.http.ContentType
import io.ktor.http.HttpHeaders.ContentRange
import io.ktor.http.HttpStatusCode.Companion.OK
import io.ktor.http.Parameters
import io.ktor.http.content.ByteArrayContent
import java.util.UUID

class HealthFormController(
    private val healthFormManagementService: HealthFormManagementService,
    private val healthFormOutcomeCalculatorConfService: HealthFormOutcomeCalculatorConfService,
    private val clinicalOutcomesConsolidatedCalculatorConfService: ClinicalOutcomesConsolidatedCalculatorConfService
): BaseController<HealthFormRequest, HealthForm, HealthForm>() {

    private val gson = isoDateWithoutFieldPolicyGson

    override suspend fun getByQueryAndRange(query: String, range: IntRange): Result<List<HealthForm>, Throwable> =
        healthFormManagementService.findFormByName(query, null)

    override suspend fun getAll(ids: List<String>): Result<List<HealthForm>, Throwable> =
        healthFormManagementService.getFormByIds(ids.map { it.toUUID() }, null)

    override suspend fun getByRange(range: IntRange): Result<List<HealthForm>, Throwable> =
        healthFormManagementService.getFormByRange(range, null)

    override suspend fun count(): Result<Int, Throwable> = healthFormManagementService.countForms()

    override suspend fun get(id: String): Result<HealthForm, Throwable> =
        healthFormManagementService.getFormById(id.toUUID())

    override suspend fun add(model: HealthForm): Result<HealthForm, Throwable> =
        healthFormManagementService.createForm(model)

    override suspend fun update(model: HealthForm): Result<HealthForm, Throwable> =
        healthFormManagementService.updateForm(model)

    override suspend fun formatRequest(request: HealthFormRequest): HealthForm =
        HealthForm(
            name = request.name,
            key = request.key,
            type = request.type
        )

    override suspend fun formatRequestToUpdate(model: HealthForm, request: HealthFormRequest): HealthForm =
        model.copy(
            name = request.name,
            key = request.key,
            type = request.type
        )

    override suspend fun index(queryParams: Parameters): Response {
        val ids = parseIds(queryParams)
        val filterQuery = parseQuery(queryParams)
        val sort = parseSort(queryParams)
        val outcomeConfId = parseFilter<String?>(queryParams, "outcome_conf_id")?.toUUID()

        val filteredElements = getFilteredElements(ids, filterQuery, sort, outcomeConfId).map { formatResponse(it) }

        if (filterQuery != null || (ids != null && ids.isNotEmpty()) || outcomeConfId != null) {
            return Response(OK, filteredElements, mapOf(ContentRange to filteredElements.size.toString()))
        }

        val range = parseRange(queryParams)

        val allElements = healthFormManagementService.getFormByRange(range, sort).get().map { formatResponse(it) }

        val totalCount = count().get()

        return Response(OK, allElements, mapOf(ContentRange to totalCount.toString()))
    }

    suspend fun listFormKey(): Response {
        val formKeys = healthFormManagementService.getAllForms().map {
            it.map { healthForm -> healthForm.key }
        }.get()

        return Response(OK, formKeys, mapOf(ContentRange to formKeys.size.toString()))
    }

    fun listFormType(): Response =
        FormType.values().map {
            ValueItemResponse(
                id = it.name,
                name = it.name,
                value = it.name
            )
        }.let {
            Response(OK, it, mapOf(ContentRange to it.size.toString()))
        }

    suspend fun generateDocument(formId: String): Response {
        val healthForm = healthFormManagementService.getFormById(formId.toUUID()).get()
        val response = healthFormManagementService.getSectionByFormId(healthForm.id).flatMapPair {
            healthFormManagementService.getAllQuestion(listOf(healthForm.id))
        }.map { (questions, sections) ->
            FullHealthFormTransport(
                healthForm = healthForm,
                sections = sections,
                questions = questions
            )
        }.get()

        val doc = gson.toJson(response).toByteArray()

        return Response(OK, ByteArrayContent(doc, ContentType.Application.OctetStream, OK))
    }

    suspend fun upload(multipartRequest: MultipartRequest): Response {
        val content = multipartRequest.fileContent!!.bufferedReader().use { it.readText() }
        val form = gson.fromJson(content, FullHealthFormTransport::class.java)

        return healthFormManagementService.createFullForm(
            healthForm = form.healthForm,
            sections = form.sections,
            questions = form.questions
        ).foldResponse()
    }

    private suspend fun getFilteredElements (
        ids: List<String>?,
        filterQuery: String?,
        sort: Pair<String, String>?,
        outcomeConfId: UUID?
    ): List<HealthForm> {
        if (filterQuery != null) {
            return healthFormManagementService.findFormByName(filterQuery, sort).get()
        }

        if (ids != null && ids.isNotEmpty()) {
            return healthFormManagementService.getFormByIds(ids.map { it.toUUID() }, sort).get()
        }

        if (outcomeConfId != null) {
            val clinicalOutcomesConsolidatedCalculatorConfOutcomeConfIds =
                clinicalOutcomesConsolidatedCalculatorConfService.getByOutcomeConfId(outcomeConfId).get()
                    .flatMap { it.outcomeConfIds }

            val outcomeConfIds = clinicalOutcomesConsolidatedCalculatorConfOutcomeConfIds.plus(outcomeConfId).distinct()

            val healthFormIds: List<UUID> =
                healthFormOutcomeCalculatorConfService.getByOutcomeConfIds(outcomeConfIds).get()
                    .map { it.healthFormId }

            return healthFormManagementService.getFormByIds(healthFormIds, sort).get()
        }

        return emptyList()
    }
}

data class HealthFormRequest(
    val name: String,
    val key: String,
    val type: FormType,
)

data class FullHealthFormTransport(
    val healthForm: HealthForm,
    val sections: List<HealthFormSection>,
    val questions: List<HealthFormQuestion>
)
