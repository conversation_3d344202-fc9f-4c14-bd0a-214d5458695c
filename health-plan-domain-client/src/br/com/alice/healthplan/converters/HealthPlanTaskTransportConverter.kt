package br.com.alice.healthplan.converters

import br.com.alice.common.logging.logger
import br.com.alice.coverage.client.City
import br.com.alice.data.layer.models.Emergency
import br.com.alice.data.layer.models.FollowUpRequest
import br.com.alice.data.layer.models.GenericTask
import br.com.alice.data.layer.models.HealthPlanTask
import br.com.alice.data.layer.models.HealthPlanTaskType
import br.com.alice.data.layer.models.Prescription
import br.com.alice.data.layer.models.Referral
import br.com.alice.data.layer.models.Staff
import br.com.alice.data.layer.models.SurgeryPrescription
import br.com.alice.data.layer.models.TaskSourceType
import br.com.alice.data.layer.models.TestRequest
import br.com.alice.healthlogic.models.adherence.AdherenceResponse
import br.com.alice.healthplan.converters.taskProgress.TaskProgressInfoBuilderImpl
import br.com.alice.healthplan.extensions.onValidInitState
import br.com.alice.healthplan.models.EmergencyTransport
import br.com.alice.healthplan.models.FollowUpRequestTransport
import br.com.alice.healthplan.models.GenericTaskTransport
import br.com.alice.healthplan.models.HLAdherenceValidationResponse
import br.com.alice.healthplan.models.HealthPlanTaskGroupTransport
import br.com.alice.healthplan.models.HealthPlanTaskTransport
import br.com.alice.healthplan.models.PrescriptionTransport
import br.com.alice.healthplan.models.ReferralTransport
import br.com.alice.healthplan.models.SurgeryPrescriptionTransport
import br.com.alice.healthplan.models.TaskInitiatorRequest
import br.com.alice.healthplan.models.TaskRequesterRequest
import br.com.alice.healthplan.models.TestRequestTransport
import br.com.alice.healthplan.models.ValidatedDemand
import java.time.LocalDateTime
import java.util.UUID

object HealthPlanTaskTransportConverter {
    @Suppress("UNCHECKED_CAST")
    fun <O : HealthPlanTask, D : HealthPlanTaskTransport> convert(
        task: O,
        staffsById: Map<UUID, Staff>,
        groups: Map<UUID, HealthPlanTaskGroupTransport>,
        cities: Map<String, City> = emptyMap(),
        adherenceValidation: AdherenceResponse? = null
    ): D = when (task.type) {
        HealthPlanTaskType.PRESCRIPTION -> convertPrescriptionTask(
            task as Prescription,
            staffsById,
            groups,
            adherenceValidation
        )

        HealthPlanTaskType.TEST_REQUEST -> convertTestRequestTask(
            task as TestRequest,
            staffsById,
            groups,
            cities,
            adherenceValidation
        )

        HealthPlanTaskType.REFERRAL -> convertReferralTask(
            task as Referral,
            staffsById,
            groups,
            cities,
            adherenceValidation
        )

        HealthPlanTaskType.EMERGENCY -> convertEmergencyTask(
            task as Emergency,
            staffsById,
            groups,
            cities,
            adherenceValidation
        )

        HealthPlanTaskType.FOLLOW_UP_REQUEST -> convertFollowUpRequestTask(
            task as FollowUpRequest,
            staffsById,
            groups,
            adherenceValidation
        )

        HealthPlanTaskType.SURGERY_PRESCRIPTION -> convertSurgeryPrescriptionTask(
            task as SurgeryPrescription,
            staffsById,
            groups,
            adherenceValidation
        )

        else -> convertGenericTask(task as GenericTask, staffsById, groups, adherenceValidation)
    } as D

    fun convertToTransport(
        task: HealthPlanTask,
        staffsById: Map<UUID, Staff>,
        groups: Map<UUID, HealthPlanTaskGroupTransport> = emptyMap(),
        cities: Map<String, City> = emptyMap(),
        adherenceValidation: AdherenceResponse? = null
    ) =
        when (task.type) {
            HealthPlanTaskType.PRESCRIPTION -> convertPrescriptionTask(
                task.specialize(),
                staffsById,
                groups,
                adherenceValidation
            )

            HealthPlanTaskType.TEST_REQUEST -> convertTestRequestTask(
                task.specialize(),
                staffsById,
                groups,
                cities,
                adherenceValidation
            )

            HealthPlanTaskType.REFERRAL -> convertReferralTask(
                task.specialize(),
                staffsById,
                groups,
                cities,
                adherenceValidation
            )

            HealthPlanTaskType.EMERGENCY -> convertEmergencyTask(
                task.specialize(),
                staffsById,
                groups,
                cities,
                adherenceValidation
            )

            HealthPlanTaskType.FOLLOW_UP_REQUEST -> convertFollowUpRequestTask(
                task.specialize(),
                staffsById,
                groups,
                adherenceValidation,
            )

            HealthPlanTaskType.SURGERY_PRESCRIPTION -> convertSurgeryPrescriptionTask(
                task.specialize(),
                staffsById,
                groups,
                adherenceValidation,
            )

            else -> convertGenericTask(task.specialize(), staffsById, groups, adherenceValidation)
        }

    private fun convertGenericTask(
        task: HealthPlanTask,
        staffsById: Map<UUID, Staff>,
        groups: Map<UUID, HealthPlanTaskGroupTransport>,
        adherenceValidation: AdherenceResponse?
    ): GenericTaskTransport {
        val progressInfoBuilder = TaskProgressInfoBuilderImpl()

        val finishedBy = task.finishedBy?.let { finisher ->
            val finisherStaff = staffsById[finisher.id]
            TaskRequesterRequest(
                staffId = finisher.id,
                name = finisherStaff?.fullName ?: finisher.source.name,
                profileImageUrl = finisherStaff?.profileImageUrl,
                requestedAt = task.finishedAt?.toString()
            )
        }

        val initiatedBy = task.initiatedByMemberAt?.let {
            TaskInitiatorRequest(
                memberId = task.personId.id, name = TaskSourceType.MEMBER.name, requestedAt = it.toString()
            )
        }

        val releasedBy =
            createTaskRequesterAndLogger(task.releasedByStaffId, staffsById, task, "releasedByStaffId", task.releasedAt)
        val lastRequester = createTaskRequesterAndLogger(
            task.lastRequesterStaffId,
            staffsById,
            task,
            "lastRequesterStaffId",
            task.updatedAt
        )

        val requesters = task.requestersStaffIds.mapNotNull {
            createTaskRequesterAndLogger(
                it,
                staffsById,
                task,
                "requestersStaffIds"
            )
        }.toSet()

        return GenericTaskTransport(
            healthPlanId = task.healthPlanId,
            attachments = task.attachments,
            appointmentId = task.appointmentId,
            personId = task.personId.id,
            title = task.title ?: "",
            description = task.description ?: "",
            dueDate = task.dueDate?.toString(),
            status = task.status,
            lastRequester = lastRequester,
            requesters = requesters,
            type = task.type,
            createdAt = task.createdAt.toString(),
            releasedAt = task.releasedAt?.toString(),
            finishedAt = task.finishedAt?.toString(),
            acknowledgedAt = task.acknowledgedAt?.toString(),
            version = task.version,
            id = task.id,
            frequency = task.frequency,
            deadline = task.deadline,
            start = task.start,
            group = groups[task.groupId],
            sentence = task.fullSentence(),
            initiatedBy = initiatedBy,
            releasedBy = releasedBy,
            finishedBy = finishedBy,
            createdBy = task.createdBy,
            memberCanInit = task.onValidInitState() && progressInfoBuilder.canBuild(task),
            progressInfo = progressInfoBuilder.build(task),
            favorite = task.favorite,
            caseRecordDetails = task.caseRecordDetails,
            hlAdherenceValidation = adherenceValidation?.let { buildHLAdherenceValidation(it, task) },
            demand = task.caseId?.let { id -> task.caseRecordDetails.find { it.caseId == id } } ?: task.caseRecordDetails.firstOrNull()
        )
    }

    private fun convertPrescriptionTask(
        task: Prescription,
        staffsById: Map<UUID, Staff>,
        groups: Map<UUID, HealthPlanTaskGroupTransport>,
        adherenceValidation: AdherenceResponse?
    ): PrescriptionTransport =
        PrescriptionTransport(
            convertGenericTask(task, staffsById, groups, adherenceValidation),
            task.dose,
            task.action,
            task.routeOfAdministration,
            task.medicine,
            task.packing,
            task.digitalPrescription
        )

    private fun convertTestRequestTask(
        task: TestRequest,
        staffsById: Map<UUID, Staff>,
        groups: Map<UUID, HealthPlanTaskGroupTransport>,
        cities: Map<String, City>,
        adherenceValidation: AdherenceResponse?
    ): TestRequestTransport =
        TestRequestTransport(
            convertGenericTask(task, staffsById, groups, adherenceValidation).withLocation(cities[task.cityId]),
            task.code,
            task.memberGuidance
        )

    private fun convertReferralTask(
        task: Referral,
        staffsById: Map<UUID, Staff>,
        groups: Map<UUID, HealthPlanTaskGroupTransport>,
        cities: Map<String, City>,
        adherenceValidation: AdherenceResponse?
    ): ReferralTransport =
        ReferralTransport(
            convertGenericTask(task, staffsById, groups, adherenceValidation).withLocation(cities[task.cityId]),
            task.suggestedSpecialist,
            task.diagnosticHypothesis,
            task.specialty,
            task.subSpecialty,
            task.sessionsQuantity,
            task.followUpMaxQuantity
        )

    private fun convertEmergencyTask(
        task: Emergency,
        staffsById: Map<UUID, Staff>,
        groups: Map<UUID, HealthPlanTaskGroupTransport>,
        cities: Map<String, City>,
        adherenceValidation: AdherenceResponse?
    ): EmergencyTransport =
        EmergencyTransport(
            convertGenericTask(task, staffsById, groups, adherenceValidation).withLocation(cities[task.cityId]),
            task.diagnosticHypothesis,
            task.description,
            task.specialty,
            task.cityId
        )

    private fun convertFollowUpRequestTask(
        task: FollowUpRequest,
        staffsById: Map<UUID, Staff>,
        groups: Map<UUID, HealthPlanTaskGroupTransport>,
        adherenceValidation: AdherenceResponse?,
    ): FollowUpRequestTransport = FollowUpRequestTransport(
        convertGenericTask(task, staffsById, groups, adherenceValidation),
        task.providerType,
        task.followUpInterval,
    )

    private fun convertSurgeryPrescriptionTask(
        task: SurgeryPrescription,
        staffsById: Map<UUID, Staff>,
        groups: Map<UUID, HealthPlanTaskGroupTransport>,
        adherenceValidation: AdherenceResponse?
    ): SurgeryPrescriptionTransport =
        SurgeryPrescriptionTransport(
            convertGenericTask(task, staffsById, groups, adherenceValidation),
            task.reason,
            task.expectedDate,
            task.provider,
            task.procedures
        )

    private fun createTaskRequesterAndLogger(
        staffId: UUID?,
        staff: Map<UUID, Staff>,
        task: HealthPlanTask,
        fieldName: String,
        requestedAt: LocalDateTime? = null
    ) = runCatching {
        staffId?.let { createTaskRequester(staffId, staff.getValue(staffId), requestedAt) }
    }.onFailure {
        logger.error(
            "Error while converting staffs by task",
            "staff_id" to staffId,
            "person_id" to task.personId.id,
            "task_id" to task.id,
            "field_name" to fieldName,
            it
        )
    }.getOrNull()

    private fun createTaskRequester(
        staffId: UUID,
        staff: Staff,
        requestedAt: LocalDateTime? = null
    ) = TaskRequesterRequest(
        staffId = staffId,
        name = staff.fullName,
        profileImageUrl = staff.profileImageUrl,
        requestedAt = requestedAt.toString(),
        role = staff.role.description
    )

    private fun buildHLAdherenceValidation(
        adherenceValidation: AdherenceResponse,
        task: HealthPlanTask
    ) = HLAdherenceValidationResponse(
        result = adherenceValidation.result,
        model = adherenceValidation.adherenceRecommendedActions?.firstOrNull()?.model,
        validatedDemand = adherenceValidation.adherenceRecommendedActions?.mapNotNull { adherenceRecommendedAction ->
            task.caseRecordDetails.firstOrNull()
                ?.takeIf { it.description.id != null }
                ?.let { demand ->
                    ValidatedDemand(
                        id = adherenceRecommendedAction.id,
                        healthLogicId = adherenceRecommendedAction.healthLogicId,
                        healthConditionId = demand.description.id!!,
                        healthConditionCode = demand.description.value
                    )
                }
        },
        healthCondition = task.caseRecordDetails.firstOrNull()?.description,
        testRequestCollectedAt = adherenceValidation.testRequestCollectedAt
    )

}
