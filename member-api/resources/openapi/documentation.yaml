openapi: 3.0.3
info:
  title: Swagger Member API - OpenApi
  description: |-
    This is the Member API documentation
  version: 1.0.11
servers:
  - url: https://member-api.wonderland.engineering
tags:
  - name: Onboarding
  - name: Procedure authorizations
  - name: Member onboarding V2
  - name: Channels
  - name: Feature Discovery
  - name: Csat
  - name: Video call
  - name: Bud
  - name: Favorites
paths:
  /onboarding/first_payment:
    get:
      tags:
        - Onboarding
      summary: Get first payment
      description: Get first payment
      responses:
        '200':
          description: successful operation
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/Test'
        '400':
          description: Invalid status value
  /procedure_authorizations:
    get:
      tags:
        - Procedure authorizations
      summary: Get procedure authorizations
      description: Get procedure authorizations
      responses:
        '200':
          description: successful operation
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ProcedureAuthorizationsResponse'
        '400':
          description: Invalid status value
  /procedure_authorizations/{id}:
    get:
      tags:
        - Procedure authorizations
      summary: Get procedure authorization detail by id
      description: Get procedure authorizations
      parameters:
        - name: id
          in: path
          description: Authorization Id
          required: true
          schema:
            type: string
      responses:
        '200':
          description: successful operation
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ProcedureAuthorizationDetail'
        '400':
          description: Invalid status value
  /v2/member_onboarding/steps/:
    get:
      tags:
        - Member onboarding V2
      summary: Get member onboarding
      description: Get member onboarding
      responses:
        '200':
          description: successful operation
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/MemberOnboardingResponse'
        '400':
          description: Invalid status value
  /v2/member_onboarding/alice_info/:
    get:
      tags:
        - Member onboarding V2
      summary: Get alice experience screen information
      description: Get alice experience screen information (V1)
      responses:
        '200':
          description: successful operation
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AliceExperienceTemplate'
        '400':
          description: Invalid status value
  /v2/member_onboarding/v2/alice_info/:
    get:
      tags:
        - Member onboarding V2
      summary: Get alice experience screen information
      description: Get alice experience screen information (V2)
      responses:
        '200':
          description: successful operation
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AliceExperienceTemplateV2'
        '400':
          description: Invalid status value
  /v2/member_onboarding/v2/health_declaration_cover/:
    get:
      tags:
        - Member onboarding V2
      summary: Get cover screen information
      description: Get cover screen information
      responses:
        '200':
          description: successful operation
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/OnboardingCoverTemplate'
        '400':
          description: Invalid status value
  /v2/member_onboarding/v2/conclusion_screen/:
    get:
      tags:
        - Member onboarding V2
      summary: Get conclusion screen information
      description: Get conclusion screen information
      responses:
        '200':
          description: successful operation
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/OnboardingConclusionTemplate'
        '400':
          description: Invalid status value
  /v2/member_onboarding/v2/screen/call_to_complete/:
    get:
      tags:
        - Member onboarding V2
      summary: Get call to complete screen information
      description: Get call to complete screen information
      responses:
        '200':
          description: successful operation
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ScreensResponse'
        '400':
          description: Invalid status value
  /v2/member_onboarding/v2/{memberOnboardingId}/step_finished/{stepType}/:
    put:
      tags:
        - Member onboarding V2
      summary: Finish member onboarding step
      description: Finish member onboarding step
      parameters:
        - name: memberOnboardingId
          in: path
          description: Member onboarding id
          required: true
          schema:
            type: string
        - name: stepType
          in: path
          description: Step to finish
          required: true
          schema:
            type: string
      responses:
        '200':
          description: successful operation
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/MemberOnboardingResponse'
        '400':
          description: Invalid status value
  /channels/upload_file/:
    post:
      tags:
        - Channels
      summary: Upload file
      description: Upload file
      requestBody:
        required: true
        content:
          multipart/form-data:
            schema:
              type: object
      responses:
        '200':
          description: successful operation
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AliceFile'
        '400':
          description: Invalid status value
  /channels/availability/:
    get:
      tags:
        - Channels
      summary: Get chat availability
      description: Get chat availability
      responses:
        '200':
          description: successful operation
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ChatAvailabilityResponse'
        '400':
          description: Invalid status value
  /channels/v2/demands/:
    get:
      tags:
        - Channels
      summary: Get channel demands
      description: Get channel demands
      responses:
        '200':
          description: successful operation
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DemandsChannels'
        '400':
          description: Invalid status value
  /channels/v2/demands/enabled/:
    get:
      tags:
        - Channels
      summary: Check if channel demands module is enabled
      description: Check if channel demands module is enabled
      responses:
        '200':
          description: successful operation
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ChannelsDemandsEnabledResponse'
        '400':
          description: Invalid status value
  /channels/v2/demands/{channelId}/action/:
    get:
      tags:
        - Channels
      summary: Get channel demands follow up action
      description: Get channel demands follow up action
      parameters:
        - name: channelId
          in: path
          description: Channel id
          required: true
          schema:
            type: string
      responses:
        '200':
          description: successful operation
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ChannelDemandsFollowUpAction'
        '400':
          description: Invalid status value
  /channels/v2/demands/{demandId}/:
    put:
      tags:
        - Channels
      summary: Get or create channel by demands
      description: Get or create channel by demands
      parameters:
        - name: demandId
          in: path
          description: Demand id
          required: true
          schema:
            type: string
      responses:
        '200':
          description: successful operation
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/RemoteAction'
        '400':
          description: Invalid status value
    get:
      tags:
        - Channels
      summary: Get channels by demand
      description: Get channels by demand
      parameters:
        - name: demandId
          in: path
          description: Demand id
          required: true
          schema:
            type: string
      responses:
        '200':
          description: successful operation
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ChannelsResponse'
        '400':
          description: Invalid status value
  /channels/v2/:
    get:
      tags:
        - Channels
      summary: Get channel details
      description: Get channel details
      responses:
        '200':
          description: successful operation
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ChannelsResponse'
        '400':
          description: Invalid status value
  /channels/{messageSource}/send_message/:
    post:
      tags:
        - Channels
      summary: Send message
      description: Send message
      parameters:
        - name: messageSource
          in: path
          description: Message source
          required: true
          schema:
            type: string
      responses:
        '200':
          description: successful operation
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ChatResponse'
        '400':
          description: Invalid status value
  /channels/{channelId}/fup/{fupId}/send_feedback/:
    post:
      tags:
        - Channels
      summary: Process member feedback
      description: Process member feedback
      parameters:
        - name: channelId
          in: path
          description: Chanel id
          required: true
          schema:
            type: string
        - name: fupId
          in: path
          description: Fup id
          required: true
          schema:
            type: string
      responses:
        '200':
          description: successful operation
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ChatResponse'
        '400':
          description: Invalid status value
  /channels/{channelId}/fup/{fupId}/acknowledge/:
    put:
      tags:
        - Channels
      summary: Follow up acknowledgement
      description: Follow up acknowledgement
      parameters:
        - name: channelId
          in: path
          description: Chanel id
          required: true
          schema:
            type: string
        - name: fupId
          in: path
          description: Fup id
          required: true
          schema:
            type: string
      responses:
        '200':
          description: successful operation
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ChatResponse'
        '400':
          description: Invalid status value
  /feature_discovery/alice_agora/:
    get:
      tags:
        - Feature Discovery
      summary: Get feature discovery content
      description: Get feature discovery content
      responses:
        '200':
          description: successful operation
          content:
            application/json:
              schema:
                type: object
        '400':
          description: Invalid status value
  /csat/{type}/:
    get:
      tags:
        - Csat
      summary: Get csat template by type
      description: Get csat template by type
      parameters:
        - name: type
          in: path
          description: Csat type
          required: true
          schema:
            type: string
      responses:
        '200':
          description: successful operation
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CsatTemplateResponse'
        '400':
          description: Invalid status value
  /csat/{context_type}/{context_id}/:
    post:
      tags:
        - Csat
      summary: Create csat
      description: Create csat
      parameters:
        - name: context_type
          in: path
          description: Context type
          required: true
          schema:
            type: string
        - name: context_id
          in: path
          description: Context id
          required: true
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CsatAnsweredRequest'
      responses:
        '200':
          description: successful operation
          content:
            application/json:
              schema:
                type: boolean
        '400':
          description: Invalid status value
  /video_call/meetings/{meetingId}/join/:
    post:
      tags:
        - Video call
      summary: Join video call
      description: Join video call
      parameters:
        - name: meetingId
          in: path
          description: Meeting id
          required: true
          schema:
            type: string
      responses:
        '200':
          description: successful operation
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/VideoCallMeetingResponse'
        '400':
          description: Invalid status value
  /video_call/meetings/{videoCallId}/quit/:
    post:
      tags:
        - Video call
      summary: Quit video call
      description: Quit video call
      parameters:
        - name: videoCallId
          in: path
          description: Video call id
          required: true
          schema:
            type: string
      responses:
        '200':
          description: successful operation
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/VideoCallMeetingResponse'
        '400':
          description: Invalid status value
  /video_call/reject_reasons/:
    get:
      tags:
        - Video call
      summary: Get video call reject reasons
      description: Get video call reject reasons
      responses:
        '200':
          description: successful operation
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/RejectVideoCallReason'
        '400':
          description: Invalid status value
  /video_call/virtual_clinic/{channelId}/drop/:
    post:
      tags:
        - Video call
      summary: Drop video call
      description: Drop video call
      parameters:
        - name: channelId
          in: path
          description: Channel id
          required: true
          schema:
            type: string
      responses:
        '200':
          description: successful operation
          content:
            application/json:
              schema:
                type: string
        '400':
          description: Invalid status value
  /bud/start/{rootNodeId}/:
    post:
      tags:
        - Bud
      summary: Start bud navigation
      description: Start bud navigation
      parameters:
        - name: rootNodeId
          in: path
          description: Root node id
          required: true
          schema:
            type: string
      responses:
        '200':
          description: successful operation
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ScreensTransport'
        '400':
          description: Invalid status value
  /bud/{node_id}/:
    get:
      deprecated: true
      tags:
        - Bud
      summary: Navigate (deprecated - use post endpoint)
      description: Navigate (deprecated - use post endpoint)
      parameters:
        - name: node_id
          in: path
          description: Node id
          required: true
          schema:
            type: string
        - in: query
          name: selected_pills
          schema:
            type: string
        - in: query
          name: clicked_option
          schema:
            type: string
        - in: query
          name: selected_date
          schema:
            type: string
        - in: query
          name: close_button_pressed
          schema:
            type: boolean
        - in: query
          name: chat_button_pressed
          schema:
            type: boolean
        - in: query
          name: back_button_pressed
          schema:
            type: boolean
        - in: query
          name: screening_navigation_id
          schema:
            type: string
        - in: query
          name: screen_id
          schema:
            type: string
      responses:
        '200':
          description: successful operation
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ScreensTransport'
        '400':
          description: Invalid status value
    post:
      tags:
        - Bud
      summary: Navigate
      description: Navigate
      parameters:
        - name: node_id
          in: path
          description: Node id
          required: true
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/NavigateTriageRequest'
      responses:
        '200':
          description: successful operation
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ScreensTransport'
        '400':
          description: Invalid status value
  /bud/{node_id}/{screeningNavigationId}/open_chat/:
    post:
      deprecated: true
      tags:
        - Bud
      summary: Finish screening and open chat (deprecated - use v2 endpoint)
      description: Finish screening and open chat (deprecated - use v2 endpoint)
      parameters:
        - name: node_id
          in: path
          description: Node id
          required: true
          schema:
            type: string
        - name: screeningNavigationId
          in: path
          description: Node id
          required: true
          schema:
            type: string
        - in: query
          name: kind
          schema:
            type: string
            enum: [ CHAT, CHANNEL ]
        - in: query
          name: category
          schema:
            type: string
            enum: [ ASSISTANCE, ADMINISTRATIVE ]
        - in: query
          name: subCategory
          schema:
            type: string
            enum: [ ACUTE, LONGITUDINAL, SCREENING, VIRTUAL_CLINIC, MULTI ]
        - in: query
          name: tags
          schema:
            type: string
        - in: query
          name: content
          schema:
            type: string
      responses:
        '200':
          description: successful operation
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/RemoteAction'
        '400':
          description: Invalid status value
  /bud/{node_id}/{screeningNavigationId}/v2/open_chat/:
    post:
      tags:
        - Bud
      summary: Finish screening and open chat
      description: Finish screening and open chat
      parameters:
        - name: node_id
          in: path
          description: Node id
          required: true
          schema:
            type: string
        - name: screeningNavigationId
          in: path
          description: Node id
          required: true
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/FinishTriageRequest'
      responses:
        '200':
          description: successful operation
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/RemoteAction'
        '400':
          description: Invalid status value
  /bud/{node_id}/{screeningNavigationId}/close/:
    post:
      tags:
        - Bud
      summary: Finish screening and close
      description: Finish screening and close
      parameters:
        - name: node_id
          in: path
          description: Node id
          required: true
          schema:
            type: string
        - name: screeningNavigationId
          in: path
          description: Node id
          required: true
          schema:
            type: string
      responses:
        '200':
          description: successful operation
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/RemoteAction'
        '400':
          description: Invalid status value
  /accredited_network/favorites:
    get:
      tags:
        - Favorites
      summary: Get all favorite providers
      description: Get all favorite providers
      parameters:
        - name: lat
          in: query
          description: Member's latitude
          required: true
          schema:
            type: number
            format: double
        - name: lng
          in: query
          description: Member's longitude
          required: true
          schema:
            type: number
            format: double
      responses:
        '200':
          description: A list of favorite providers
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ProviderResponse'
        '400':
          description: Invalid request parameters
    post:
      tags:
        - Favorites
      summary: Add a provider to favorites
      description: Add a provider to favorites
      parameters:
        - name: provider_id
          in: query
          description: ID of the provider to add to favorites
          required: true
          schema:
            type: string
            format: uuid
      responses:
        '200':
          description: Provider added to favorites
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/FavoriteInfoTransport'
        '400':
          description: Invalid request body
  /accredited_network/favorites/{favorite_id}:
    delete:
      tags:
        - Favorites
      summary: Remove a provider from favorites
      description: Remove a provider from favorites
      parameters:
        - name: favorite_id
          in: path
          required: true
          schema:
            type: string
            format: uuid
      responses:
        '200':
          description: Provider removed from favorites
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/FavoriteInfoTransport'
        '400':
          description: Invalid request parameters


components:
  schemas:
    Test:
      type: object
      properties:
        primitive_required:
          type: string
        primitive_optional:
          type: number
          format: double
        primitive_with_default:
          type: string
          default: test
        internal_required:
          $ref: '#/components/schemas/TestInternal'
        internal_optional:
          $ref: '#/components/schemas/TestInternal'
        list_primitive_required:
          type: array
          items:
            type: string
        list_primitive_optional:
          type: array
          items:
            type: string
        list_internal_required:
          type: array
          items:
            $ref: '#/components/schemas/TestInternal'
        list_internal_optional:
          type: array
          items:
            $ref: '#/components/schemas/TestInternal'
        list_enum:
          type: array
          items:
            type: string
            default: list_enum1
            enum: [ list_enum1, list_enum2 ]
        enum_shared:
          $ref: '#/components/schemas/TestSharedEnum'
        enum_optional:
          type: string
          default: test1
          enum: [ test1, test2 ]
        enum_required:
          type: string
          default: test1
          enum: [ test1, test2 ]
        date_time_required:
          type: string
          format: date-time
        date_time_optional:
          type: string
          format: date-time
      required:
        - primitive_required
        - internal_required
        - list_primitive_required
        - list_internal_required
        - enum_required
        - date_time_required
    TestInternal:
      type: object
      properties:
        id:
          type: string
      required:
        - id
    TestSharedEnum:
      type: string
      enum: [ sharedEnum1, sharedEnum2 ]
    AliceAgoraAvailability:
      type: object
      properties:
        hub:
          $ref: '#/components/schemas/AliceAgoraHub'
        hub_administrative:
          $ref: '#/components/schemas/AliceAgoraHub'
        header:
          $ref: '#/components/schemas/AliceAgoraChatHeader'
      required:
        - hub
        - header
    AliceAgoraHub:
      type: object
      properties:
        is_available:
          type: boolean
        description:
          type: string
        availability:
          type: string
        action:
          $ref: '#/components/schemas/AliceAgoraChatAction'
      required:
        - is_available
    AliceAgoraChatAction:
      type: object
      properties:
        action:
          type: string
        parameter:
          type: string
        description:
          type: string
      required:
        - action
    AliceAgoraChatHeader:
      type: object
      properties:
        description:
          type: string
        staff:
          type: array
          items:
            $ref: '#/components/schemas/AliceAgoraChatStaff'
      required:
        - description
        - staff
    AliceAgoraChatStaff:
      type: object
      properties:
        profile_image_url:
          type: string
        is_available:
          type: boolean
      required:
        - profile_image_url
        - is_available
    AliceAgoraSortingOptions:
      type: object
      properties:
        options:
          type: array
          items:
            $ref: '#/components/schemas/AliceAgoraSortingOption'
      required:
        - options
    AliceAgoraSortingOption:
      type: object
      properties:
        label:
          type: string
        value:
          type: string
      required:
        - label
        - value
    Account:
      type: object
      properties:
        id:
          type: string
        nickname:
          type: string
        bank:
          type: string
        branch:
          type: string
        account_number:
          type: string
      required:
        - id
        - nickname
        - bank
        - branch
        - account_number
    AccountHolder:
      type: object
      properties:
        id:
          type: string
          default: ""
        name:
          type: string
        details:
          type: string
        national_id:
          type: string
        image_url:
          type: string
        accounts:
          type: array
          default: [ ]
          items:
            $ref: '#/components/schemas/Account'
      required:
        - id
        - name
        - details
        - national_id
        - accounts
    Bank:
      type: object
      properties:
        code:
          type: string
        description:
          type: string
      required:
        - code
        - description
    BankList:
      type: object
      properties:
        banks:
          type: array
          items:
            $ref: '#/components/schemas/Bank'
      required:
        - banks
    Action:
      type: object
      properties:
        type:
          type: string
        data:
          type: object
      required:
        - type
    ActionAsyncRequest:
      type: object
      properties:
        method:
          type: string
        endpoint:
          type: string
        screen_id:
          type: string
      required:
        - method
        - endpoint
        - screen_id
    AppBar:
      type: object
      properties:
        title:
          type: string
        accessory_filter:
          $ref: '#/components/schemas/AppBarFilter'
        left_items:
          type: array
          items:
            type: string
        left_remote_action_items:
          type: array
          items:
            $ref: '#/components/schemas/AppBarRemoteActionItem'
        right_items:
          type: array
          items:
            type: string
        right_remote_action_items:
          type: array
          items:
            $ref: '#/components/schemas/AppBarRemoteActionItem'
    AppBarFilter:
      type: object
      properties:
        type:
          type: string
        reload_base_url:
          type: string
        manual_filter:
          $ref: '#/components/schemas/AppBarManualFilter'
        buttons_filter:
          type: array
          items:
            $ref: '#/components/schemas/AppBarButtonFilter'
    AppBarManualFilter:
      type: object
      properties:
        hint:
          type: string
        param_name:
          type: string
    AppBarButtonFilter:
      type: object
      properties:
        id:
          type: string
        icon:
          type: string
        label:
          type: string
        param_value:
          type: string
        param_name:
          type: string
    AppBarRemoteActionItem:
      type: object
      properties:
        icon:
          type: string
        action:
          $ref: '#/components/schemas/RemoteAction'
      required:
        - icon
        - action
    BottomTab:
      type: object
      properties:
        id:
          type: string
        name:
          type: string
        icon:
          $ref: '#/components/schemas/BottomTabIcon'
        action:
          $ref: '#/components/schemas/Action'
      required:
        - id
        - name
        - icon
        - action
    BottomTabIcon:
      type: object
      properties:
        selected:
          type: string
        unselected:
          type: string
      required:
        - selected
        - unselected
    Button:
      type: object
      properties:
        id:
          type: string
        label:
          $ref: '#/components/schemas/ButtonLabel'
        variant:
          type: string
        size:
          type: string
        enabled:
          type: boolean
        loading:
          type: boolean
        shrink_wrap:
          type: boolean
        on_tap_action:
          $ref: '#/components/schemas/RemoteAction'
      required:
        - label
        - on_tap_action
    ButtonLabel:
      type: object
      properties:
        text:
          type: string
        icon:
          type: string
        accessory:
          type: string
    Image:
      type: object
      properties:
        url:
          type: string
        type:
          type: string
        size:
          type: string
      required:
        - url
        - type
    LinkNavigation:
      type: object
      properties:
        id:
          type: string
        size:
          type: string
        label:
          $ref: '#/components/schemas/LinkNavigationLabel'
        shrink_wrap:
          type: boolean
        on_tap_action:
          $ref: '#/components/schemas/RemoteAction'
      required:
        - label
        - on_tap_action
    LinkNavigationLabel:
      type: object
      properties:
        text:
          type: string
        icon:
          $ref: '#/components/schemas/LinkNavigationIcon'
      required:
        - text
    LinkNavigationIcon:
      type: object
      properties:
        name:
          type: string
        position:
          type: string
      required:
        - name
        - position
    ListCard:
      type: object
      properties:
        content:
          type: array
          items:
            $ref: '#/components/schemas/Section'
      required:
        - content
    ListMenu:
      type: object
      properties:
        content:
          type: array
          items:
            $ref: '#/components/schemas/Section'
      required:
        - content
    Screen:
      type: object
      properties:
        id:
          type: string
        layout:
          type: object
        properties:
          $ref: '#/components/schemas/ScreenProperties'
        type:
          type: string
      required:
        - id
        - layout
    ScreenProperties:
      type: object
      properties:
        track_user_behavior:
          type: boolean
          default: false
        safe_area:
          $ref: '#/components/schemas/ScreenSafeArea'
        alignment:
          $ref: '#/components/schemas/ScreenAlignment'
        close_behavior:
          $ref: '#/components/schemas/ScreenCloseBehavior'
    ScreenSafeArea:
      type: object
      properties:
        top:
          type: boolean
          default: true
        right:
          type: boolean
          default: true
        bottom:
          type: boolean
          default: true
        left:
          type: boolean
          default: true
      required:
        - top
        - right
        - bottom
        - left
    ScreenAlignment:
      type: object
      properties:
        vertical:
          type: string
        horizontal:
          type: string
    ScreenCloseBehavior:
      type: object
      properties:
        strategy:
          type: string
        action:
          $ref: '#/components/schemas/RemoteAction'
    SingleColumnLayout:
      type: object
      properties:
        app_bar:
          $ref: '#/components/schemas/AppBar'
        body:
          type: array
          items:
            $ref: '#/components/schemas/Section'
        footer:
          $ref: '#/components/schemas/Section'
      required:
        - body
    Section:
      type: object
      properties:
        id:
          type: string
        type:
          type: string
        data:
          type: object
        expires_cache_at:
          type: string
        min_app_version:
          type: string
        required_sections:
          type: array
          items:
            type: string
      required:
        - id
        - type
        - data
        - min_app_version
    BridgeSectionData:
      type: object
      properties:
        sections:
          type: array
          items:
            $ref: '#/components/schemas/Section'
        rules:
          type: array
          items:
            $ref: '#/components/schemas/BridgeRule'
      required:
        - sections
        - rules
    BridgeRule:
      type: object
      properties:
        on_action:
          $ref: '#/components/schemas/BridgeRuleOn'
        then:
          $ref: '#/components/schemas/BridgeRuleThen'
      required:
        - on_action
        - then
    BridgeRuleOn:
      type: object
      properties:
        key:
          type: string
        action:
          type: string
          enum: [ pressed, loaded, contentSelected ]
      required:
        - action
        - key
    BridgeRuleThen:
      type: object
      properties:
        make:
          type: string
        property:
          type: string
          enum: [ enabled, text ]
        value:
          oneOf:
            - type: string
            - type: boolean
            - type: integer
      required:
        - make
        - property
        - value
    CalendarSection:
      type: object
      properties:
        variant:
          type: string
        initial_date:
          type: string
          format: date-time
        first_date:
          type: string
          format: date-time
        last_date:
          type: string
          format: date-time
        current_date:
          type: string
          format: date-time
        available_days:
          type: string
        initial_calendar_mode:
          type: string
        confirm_button:
          $ref: '#/components/schemas/Button'
      required:
        - initial_date
        - first_date
        - last_date
        - confirm_button
    CalloutSection:
      type: object
      properties:
        title:
          type: string
        callout_body:
          type: string
        callout_variant:
          type: string
        avatar_url:
          type: string
        callout_action:
          $ref: '#/components/schemas/CalloutAction'
      required:
        - title
        - callout_body
        - callout_variant
    CalloutAction:
      type: object
      properties:
        label:
          type: string
        on_click_action:
          $ref: '#/components/schemas/RemoteAction'
        type:
          type: string
      required:
        - type
    CardSection:
      type: object
      properties:
        type:
          type: string
        status:
          type: string
        state:
          type: string
        header:
          $ref: '#/components/schemas/CardHeader'
        title:
          type: string
        description:
          type: string
        background_image:
          type: string
        accessory_image:
          type: string
        alignment:
          type: string
        background_color:
          type: string
        content_action:
          $ref: '#/components/schemas/RemoteAction'
        on_card_click:
          $ref: '#/components/schemas/RemoteAction'
        main_action:
          $ref: '#/components/schemas/Button'
        button_primary:
          $ref: '#/components/schemas/Button'
        button_secondary:
          $ref: '#/components/schemas/Button'
      required:
        - header
    CardHeader:
      type: object
      properties:
        title:
          type: string
        description:
          type: string
        icon:
          type: string
        scheduled_date:
          type: string
        info_tag:
          $ref: '#/components/schemas/CardInfoTag'
    CardInfoTag:
      type: object
      properties:
        type:
          type: string
        title:
          type: string
        icon:
          type: string
      required:
        - type
        - title
    CheckboxSection:
      type: object
      properties:
        items:
          type: array
          items:
            type: string
        on_status_change:
          $ref: '#/components/schemas/RemoteAction'
      required:
        - items
    GroupedColumnSection:
      type: object
      properties:
        title:
          type: string
        details_button:
          $ref: '#/components/schemas/Button'
        body:
          type: array
          items:
            $ref: '#/components/schemas/Section'
      required:
        - title
        - body
    GridItemSection:
      type: object
      properties:
        child:
          $ref: '#/components/schemas/Section'
        span:
          type: integer
      required:
        - child
        - span
    GridSection:
      type: object
      properties:
        children:
          type: array
          $ref: '#/components/schemas/Section'
        gutter:
          type: string
        grid_variant:
          type: string
      required:
        - children
        - gutter
        - grid_variant
    ImageSection:
      type: object
      properties:
        content:
          $ref: '#/components/schemas/ContentImageSection'
      required:
        - content
    ContentImageSection:
      type: object
      properties:
        image:
          $ref: '#/components/schemas/Image'
        alignment:
          type: string
        vertical_padding:
          type: string
        horizontal_padding:
          type: string
      required:
        - image
    ListCardSection:
      type: object
      properties:
        orientation:
          type: string
        title:
          type: string
        description:
          type: string
        icon:
          type: string
        details_button:
          $ref: '#/components/schemas/Button'
        content_action:
          $ref: '#/components/schemas/RemoteAction'
    ListMenuSection:
      type: object
      properties:
        content_action:
          $ref: '#/components/schemas/RemoteAction'
        body:
          type: array
          items:
            $ref: '#/components/schemas/Section'
        has_divider:
          type: boolean
    MenuSection:
      type: object
      properties:
        menu_variant:
          type: string
        icon:
          type: string
        avatar_url:
          type: string
        title:
          type: string
        label:
          type: string
        caption:
          type: string
        tag:
          $ref: '#/components/schemas/Tag'
        click_affordance:
          type: boolean
        on_tap_action:
          $ref: '#/components/schemas/RemoteAction'
      required:
        - title
    PillResponseSection:
      type: object
      properties:
        options:
          type: array
          items:
            $ref: '#/components/schemas/PillResponseOption'
        stack:
          type: string
        selection_algorithm:
          type: string
        confirm_button:
          $ref: '#/components/schemas/Button'
        no_selection_button:
          $ref: '#/components/schemas/Button'
      required:
        - options
        - confirm_button
    PillResponseOption:
      type: object
      properties:
        id:
          type: string
        title:
          type: string
        body:
          type: string
        accessory:
          $ref: '#/components/schemas/PillResponseAccessory'
      required:
        - id
        - title
    PillResponseAccessory:
      type: object
      properties:
        type:
          type: string
        url:
          type: string
      required:
        - type
        - url
    SectionButton:
      type: object
      properties:
        button:
          $ref: '#/components/schemas/Button'
        alignment:
          type: string
      required:
        - button
    SectionChatInput:
      type: object
      properties:
        placeholder:
          type: string
        text:
          type: string
        on_send_action:
          $ref: '#/components/schemas/RemoteAction'
      required:
        - on_send_action
    SectionLink:
      type: object
      properties:
        link:
          $ref: '#/components/schemas/LinkNavigation'
        alignment:
          type: string
        vertical_padding:
          type: string
        horizontal_padding:
          type: string
      required:
        - link
    SectionModule:
      type: object
      properties:
        module_name:
          type: string
      required:
        - module_name
    SectionQuestionnaire:
      type: object
      properties:
        staff:
          $ref: '#/components/schemas/SectionQuestionnaireStaff'
        feedback:
          $ref: '#/components/schemas/SectionQuestionnaireFeedback'
        question:
          $ref: '#/components/schemas/QuestionnaireQuestion'
    SectionQuestionnaireStaff:
      type: object
      properties:
        name:
          type: string
        description:
          type: string
        profile_image_url:
          type: string
      required:
        - name
        - description
        - profile_image_url
    SectionQuestionnaireFeedback:
      type: object
      properties:
        message:
          type: string
        image:
          $ref: '#/components/schemas/Image'
        button:
          $ref: '#/components/schemas/Button'
        duration_in_seconds:
          type: integer
          default: 0
        is_closeable:
          type: boolean
          default: false
      required:
        - message
        - duration_in_seconds
        - is_closeable
    SectionText:
      type: object
      properties:
        content_action:
          $ref: '#/components/schemas/RemoteAction'
        content:
          $ref: '#/components/schemas/Text'
    SheetSection:
      type: object
      properties:
        illustration_url:
          type: string
        title:
          type: string
        content:
          type: object
        confirmation_button:
          $ref: '#/components/schemas/Button'
        cancel_button:
          $ref: '#/components/schemas/Button'
      required:
        - illustration_url
        - title
        - content
        - confirmation_button
    TabBarSection:
      type: object
      properties:
        tab_bar_variant:
          type: string
        tab_bar_items:
          type: array
          items:
            $ref: '#/components/schemas/TabBarSectionItem'
      required:
        - tab_bar_items
    TabBarSectionItem:
      type: object
      properties:
        content:
          type: string
        action:
          $ref: '#/components/schemas/RemoteAction'
      required:
        - content
        - action
    SingleCard:
      type: object
      properties:
        content:
          $ref: '#/components/schemas/Section'
      required:
        - content
    Tag:
      type: object
      properties:
        icon:
          type: string
        text:
          type: string
        color_scheme:
          type: string
    TextContent:
      type: object
      properties:
        content:
          $ref: '#/components/schemas/Text'
      required:
        - content
    Text:
      type: object
      properties:
        title:
          type: string
        alignment:
          type: string
        layout:
          type: string
      required:
        - title
    Partner:
      type: object
      properties:
        id:
          type: string
        provider_id:
          type: string
        name:
          type: string
        type:
          type: string
        education:
          type: array
          items:
            type: string
        crm:
          type: string
        curiosity:
          type: string
        qualifications:
          type: array
          items:
            $ref: '#/components/schemas/PartnerQualification'
        specialties:
          type: array
          items:
            type: string
        cnpj:
          type: string
        site:
          type: string
        sub_specialties:
          type: array
          items:
            type: string
        addresses:
          type: array
          items:
            $ref: '#/components/schemas/PartnerAddress'
        simplified_addresses:
          type: array
          items:
            $ref: '#/components/schemas/PartnerAddress'
        phones:
          type: array
          items:
            $ref: '#/components/schemas/PartnerPhone'
        contact_call_out:
          $ref: '#/components/schemas/PartnerCallout'
        working_hours:
          $ref: '#/components/schemas/WorkingHours'
        working_hours_status:
          type: string
        icon_url:
          type: string
        image_url:
          type: string
        schedule_availability_days:
          type: integer
        appointment_types:
          type: array
          items:
            type: string
        attendance_age:
          type: string
        scheduling_url:
          type: string
        main_action:
          $ref: '#/components/schemas/PartnerMainAction'
      required:
        - id
        - name
    PartnerQualification:
      type: object
      properties:
        image_url:
          type: string
        description:
          type: string
      required:
        - image_url
        - description
    PartnerAddress:
      type: object
      properties:
        title:
          type: string
        address:
          type: string
      required:
        - address
    PartnerPhone:
      type: object
      properties:
        title:
          type: string
        phone:
          type: string
        phone_url:
          type: string
        type:
          type: string
      required:
        - phone
    WorkingHours:
      type: object
      properties:
        description:
          type: string
        always_open:
          type: boolean
        weekday_text:
          type: array
          items:
            type: string
      required:
        - always_open
        - weekday_text
    PartnerMainAction:
      type: object
      properties:
        label:
          type: string
        navigation:
          $ref: '#/components/schemas/ServerNavigation'
      required:
        - label
        - navigation
    PartnerCallout:
      type: object
      properties:
        title:
          type: string
        body:
          type: string
        variant:
          type: string
        avatar_url:
          type: string
        action:
          $ref: '#/components/schemas/CalloutAction'
      required:
        - title
        - body
        - variant
    ActionLink:
      type: object
      properties:
        href:
          type: string
        rel:
          type: string
      required:
        - href
    AppointmentIntro:
      type: object
      properties:
        header:
          $ref: '#/components/schemas/AppointmentIntroHeader'
        appointment_rules:
          type: array
          items:
            $ref: '#/components/schemas/AppointmentIntroRule'
        start_appointment:
          $ref: '#/components/schemas/AppointmentIntroStart'
      required:
        - header
        - appointment_rules
        - start_appointment
    AppointmentIntroHeader:
      type: object
      properties:
        title:
          type: string
        image_url:
          type: string
      required:
        - title
        - image_url
    AppointmentIntroRule:
      type: object
      properties:
        icon:
          type: string
        description:
          type: string
      required:
        - icon
        - description
    AppointmentIntroStart:
      type: object
      properties:
        button_label:
          type: string
        manual_label:
          type: string
      required:
        - button_label
        - manual_label
    AppointmentLink:
      type: object
      properties:
        link:
          type: string
      required:
        - link
    AppointmentScheduleEstablishmentGroups:
      type: object
      properties:
        groups:
          type: array
          items:
            $ref: '#/components/schemas/AppointmentScheduleEstablishmentGroup'
      required:
        - groups
    AppointmentScheduleEstablishmentGroup:
      type: object
      properties:
        name:
          type: string
        establishments:
          type: array
          items:
            $ref: '#/components/schemas/AppointmentScheduleEstablishment'
      required:
        - name
        - establishments
    AppointmentScheduleEstablishment:
      type: object
      properties:
        name:
          type: string
        address:
          type: string
        navigation:
          $ref: '#/components/schemas/AppointmentScheduleNavigation'
      required:
        - name
        - address
        - navigation
    AppointmentScheduleNavigation:
      type: object
      properties:
        mobile_route:
          $ref: '#/components/schemas/NavigationRoute'
        endpoint:
          type: string
        method:
          $ref: '#/components/schemas/RemoteActionAsyncRequestMethod'
        body:
          type: object
      required:
        - endpoint
    AppointmentScheduleAvailability:
      type: object
      properties:
        availability:
          type: array
          items:
            $ref: '#/components/schemas/AppointmentScheduleAvailabilityItem'
        next:
          $ref: '#/components/schemas/AppointmentScheduleAvailabilityNext'
      required:
        - availability
    AppointmentScheduleAvailabilityItem:
      type: object
      properties:
        title:
          type: string
        professionals:
          type: array
          items:
            $ref: '#/components/schemas/AppointmentScheduleProfessional'
      required:
        - title
        - professionals
    AppointmentScheduleProfessional:
      type: object
      properties:
        name:
          type: string
        description:
          type: string
        navigation:
          $ref: '#/components/schemas/AppointmentScheduleNavigation'
      required:
        - name
        - description
    AppointmentScheduleAvailabilityNext:
      type: object
      properties:
        title:
          type: string
        navigation:
          $ref: '#/components/schemas/AppointmentScheduleNavigation'
      required:
        - title
        - navigation
    AppointmentScheduleAvailabilityProfessional:
      type: object
      properties:
        professional:
          $ref: '#/components/schemas/AppointmentScheduleProfessional'
        availability:
          type: array
          items:
            $ref: '#/components/schemas/AppointmentScheduleProfessionalAvailabilityItem'
        next:
          $ref: '#/components/schemas/AppointmentScheduleAvailabilityNext'
      required:
        - availability
    AppointmentScheduleProfessionalAvailabilityItem:
      type: object
      properties:
        title:
          type: string
        slots:
          type: array
          items:
            $ref: '#/components/schemas/AppointmentScheduleSlot'
      required:
        - title
        - slots
    AppointmentScheduleSlot:
      type: object
      properties:
        time:
          type: string
        navigation:
          $ref: '#/components/schemas/AppointmentScheduleNavigation'
      required:
        - time
        - navigation
    AppointmentScheduleDetail:
      type: object
      properties:
        app_bar_title:
          type: string
          default: ""
        image_url:
          type: string
        headline:
          type: string
          default: ""
        scheduled_date:
          type: string
          format: date-time
        descriptions:
          type: array
          default: [ ]
          items:
            $ref: '#/components/schemas/AppointmentScheduleDetailDescription'
        presential_instructions:
          $ref: '#/components/schemas/AppointmentScheduleDetailPresential'
        remote_instructions:
          $ref: '#/components/schemas/AppointmentScheduleDetailRemote'
        confirm_action:
          $ref: '#/components/schemas/AppointmentScheduleDetailAction'
        cancel_action:
          $ref: '#/components/schemas/AppointmentScheduleDetailAction'
      required:
        - image_url
        - scheduled_date
        - descriptions
    AppointmentScheduleDetailDescription:
      type: object
      properties:
        title:
          type: string
        body:
          type: string
      required:
        - title
        - body
    AppointmentScheduleDetailPresential:
      type: object
      properties:
        location_image_url:
          type: string
        location_description:
          type: string
          default: ""
        location_url:
          type: string
    AppointmentScheduleDetailRemote:
      type: object
      properties:
        label:
          type: string
        link:
          type: string
      required:
        - label
        - link
    AppointmentScheduleDetailAction:
      type: object
      properties:
        label:
          type: string
        navigation:
          $ref: '#/components/schemas/AppointmentScheduleNavigation'
      required:
        - label
        - navigation
    ChannelsDemand:
      type: object
      properties:
        title:
          type: string
        description:
          type: string
        started_at:
          type: string
          format: date-time
        channels:
          type: array
          items:
            type: string
      required:
        - title
        - description
        - started_at
        - channels
    ChannelsDemandList:
      type: object
      properties:
        demands:
          type: array
          items:
            $ref: '#/components/schemas/ChannelsDemand'
        resume_count:
          type: integer
        time:
          type: string
      required:
        - demands
    DemandAction:
      type: object
      properties:
        action:
          $ref: '#/components/schemas/ServerNavigation'
      required:
        - action
    DemandChannelList:
      type: object
      properties:
        started_at:
          type: string
          format: date-time
        channels:
          type: array
          items:
            $ref: '#/components/schemas/DemandChannel'
      required:
        - channels
    DemandChannel:
      type: object
      properties:
        title:
          type: string
        time_last_message:
          type: string
          format: date-time
        archived_at:
          type: string
          format: date-time
        created_at:
          type: string
          format: date-time
        staff_name:
          type: string
        staff_picture:
          type: string
        navigation:
          $ref: '#/components/schemas/ServerNavigation'
      required:
        - title
    DemandsEnabled:
      type: object
      properties:
        show:
          default: false
          type: boolean
      required:
        - show
    HealthPlanEmergencyCollection:
      type: object
      properties:
        recommendation:
          $ref: '#/components/schemas/Partner'
        providers:
          type: array
          items:
            $ref: '#/components/schemas/Partner'
      required:
        - providers
    HealthPlanItemAction:
      type: object
      properties:
        action_label:
          type: string
        title:
          type: string
        image_url:
          type: string
        description:
          type: string
        confirm_label:
          type: string
        cancel_label:
          type: string
      required:
        - action_label
        - title
        - image_url
    HealthPlanItemAttachment:
      type: object
      properties:
        name:
          type: string
        description:
          type: string
        image_url:
          type: string
        link:
          type: string
      required:
        - name
        - link
    HealthPlanItemDateLimit:
      type: object
      properties:
        type:
          type: string
        date:
          type: string
          format: date-time
        description:
          type: string
    HealthPlanItemDetails:
      type: object
      properties:
        health_plan_task_id:
          type: string
        description:
          type: string
        created_at:
          type: string
          format: date-time
        content:
          type: string
        infos:
          type: array
          items:
            type: string
        navigations:
          type: array
          items:
            $ref: '#/components/schemas/HealthPlanItemNavigation'
        finish_task:
          $ref: '#/components/schemas/HealthPlanItemAction'
        delete_task:
          $ref: '#/components/schemas/HealthPlanItemAction'
        related_task_ids:
          type: array
          items:
            type: string
        schedule_info:
          $ref: '#/components/schemas/HealthPlanItemScheduleInfo'
        recurrent_schedule_info:
          $ref: '#/components/schemas/HealthPlanItemRecurrentScheduleInfo'
        preparation:
          $ref: '#/components/schemas/HealthPlanItemPreparations'
        frequency:
          $ref: '#/components/schemas/HealthPlanItemFrequency'
        deadline:
          $ref: '#/components/schemas/HealthPlanItemDateLimit'
        start:
          $ref: '#/components/schemas/HealthPlanItemDateLimit'
        status:
          type: string
        favorite:
          type: boolean
        attachments:
          type: array
          items:
            $ref: '#/components/schemas/HealthPlanItemAttachment'
        group:
          $ref: '#/components/schemas/HealthPlanItemGroup'
        staff_info:
          $ref: '#/components/schemas/HealthPlanItemStaffInfo'
        main_action_navigation:
          $ref: '#/components/schemas/HealthPlanItemMainAction'
        can_ask_health_team:
          type: boolean
        task_alert:
          $ref: '#/components/schemas/HealthPlanItemTaskAlert'
        copay_info:
          $ref: '#/components/schemas/HealthPlanItemLink'
      required:
        - health_plan_task_id
        - description
        - status
    HealthPlanItemMainAction:
      type: object
      properties:
        label:
          type: string
        navigation:
          $ref: '#/components/schemas/ServerNavigation'
      required:
        - label
        - navigation
    HealthPlanItem:
      type: object
      properties:
        health_plan_task_id:
          type: string
        description:
          type: string
        section:
          type: string
        schedule_info:
          $ref: '#/components/schemas/HealthPlanItemScheduleInfo'
        recurrent_schedule_info:
          $ref: '#/components/schemas/HealthPlanItemRecurrentScheduleInfo'
        preparation:
          $ref: '#/components/schemas/HealthPlanItemPreparations'
        frequency:
          $ref: '#/components/schemas/HealthPlanItemFrequency'
        deadline:
          $ref: '#/components/schemas/HealthPlanItemDateLimit'
        start:
          $ref: '#/components/schemas/HealthPlanItemDateLimit'
        acknowledged_at:
          type: string
        status:
          type: string
        favorite:
          type: boolean
        attachments:
          type: array
          items:
            $ref: '#/components/schemas/HealthPlanItemAttachment'
        calendar:
          $ref: '#/components/schemas/HealthPlanItemScheduleRequest'
        group:
          $ref: '#/components/schemas/HealthPlanItemGroup'
        specialty:
          type: string
        specialist_id:
          type: string
        schedule_info_path:
          type: string
        hide_schedule_options:
          type: boolean
      required:
        - health_plan_task_id
        - description
        - section
        - status
    HealthPlanItemFrequency:
      type: object
      properties:
        description:
          type: string
      required:
        - description
    HealthPlanItemGroup:
      type: object
      properties:
        id:
          type: string
        name:
          type: string
      required:
        - id
        - name
    HealthPlanItemLink:
      type: object
      properties:
        label:
          type: string
        icon:
          type: string
        navigation:
          $ref: '#/components/schemas/ServerNavigation'
      required:
        - label
        - icon
        - navigation
    HealthPlanItemNavigation:
      type: object
      properties:
        name:
          type: string
        description:
          type: string
        image_url:
          type: string
        navigation:
          $ref: '#/components/schemas/ServerNavigation'
        tag:
          $ref: '#/components/schemas/HealthPlanItemNavigationTag'
      required:
        - name
        - navigation
    HealthPlanItemNavigationTag:
      type: object
      properties:
        icon:
          type: string
        text:
          type: string
        color:
          type: string
          enum: [ red, magenta, violet, blue, green, yellow, orange, gray ]
      required:
        - text
        - color
    HealthPlanItemPreparations:
      type: object
      properties:
        warning:
          type: string
        preparations:
          type: array
          items:
            $ref: '#/components/schemas/HealthPlanItemPreparation'
      required:
        - preparations
    HealthPlanItemPreparation:
      type: object
      properties:
        title:
          type: string
        description:
          type: string
      required:
        - title
        - description
    HealthPlanItemRecurrentScheduleInfo:
      type: object
      properties:
        scheduled_at:
          type: string
        details:
          type: string
    HealthPlanItemScheduleData:
      type: object
      properties:
        section:
          type: string
        calendar:
          $ref: '#/components/schemas/HealthPlanItemScheduleRequest'
        specialty:
          type: string
        specialist_id:
          type: string
        schedule_info_path:
          type: string
        hide_schedule_options:
          type: boolean
      required:
        - section
    HealthPlanItemScheduleInfo:
      type: object
      properties:
        scheduled_at:
          type: string
          format: date-time
        details:
          type: string
      required:
        - scheduled_at
    HealthPlanItemScheduleRequest:
      type: object
      properties:
        navigation:
          $ref: '#/components/schemas/ServerNavigation'
        calendar_url:
          type: string
        schedule_options:
          type: array
          items:
            $ref: '#/components/schemas/ScheduleOptionItem'
    HealthPlanItemStaffInfo:
      type: object
      properties:
        name:
          type: string
        profile_image_url:
          type: string
        role:
          type: string
      required:
        - name
    HealthPlanItemTaskAlert:
      type: object
      properties:
        id:
          type: string
        title:
          type: string
        description:
          type: string
        illustration:
          type: string
        navigation:
          $ref: '#/components/schemas/ServerNavigation'
      required:
        - id
        - title
        - description
    HealthPlanResume:
      type: object
      properties:
        items:
          type: array
          items:
            $ref: '#/components/schemas/HealthPlanItem'
        cta_button:
          $ref: '#/components/schemas/HealthPlanResumeCta'
        card_info:
          $ref: '#/components/schemas/HealthPlanResumePresentationInfo'
        total:
          type: integer
    HealthPlanResumeCta:
      type: object
      properties:
        label:
          type: string
        navigation:
          $ref: '#/components/schemas/ServerNavigation'
      required:
        - label
        - navigation
    HealthPlanResumePresentationInfo:
      type: object
      properties:
        title:
          type: string
        description:
          type: string
        images:
          type: array
          items:
            type: string
      required:
        - title
        - images
    HealthPlanUnacknowledgedCount:
      type: object
      properties:
        task_count:
          type: integer
      required:
        - task_count
    HealthTeamCard:
      type: object
      properties:
        title:
          type: string
        description:
          type: string
        images:
          type: array
          items:
            type: string
      required:
        - title
        - images
    HealthTeamDetails:
      type: object
      properties:
        header:
          $ref: '#/components/schemas/HealthTeamDetailsHeader'
        body:
          type: array
          items:
            $ref: '#/components/schemas/HealthTeamDetailsSection'
        bottom:
          $ref: '#/components/schemas/HealthTeamDetailsBottom'
      required:
        - header
        - body
        - bottom
    HealthTeamDetailsHeader:
      type: object
      properties:
        title:
          type: string
        images:
          type: array
          items:
            type: string
      required:
        - title
        - images
    HealthTeamDetailsSection:
      type: object
      properties:
        icon:
          type: string
        title:
          type: string
        description:
          type: string
        images:
          type: array
          items:
            type: string
      required:
        - icon
        - title
        - description
    HealthTeamDetailsBottom:
      type: object
      properties:
        title:
          type: string
        description:
          type: string
      required:
        - title
        - description
    MemberOnboardingStep:
      type: object
      properties:
        key:
          type: string
        type:
          type: string
        folder:
          $ref: '#/components/schemas/MemberOnboardingStepFolder'
        accessory_image:
          $ref: '#/components/schemas/MemberOnboardingStepAccessoryImage'
        tag:
          $ref: '#/components/schemas/MemberOnboardingStepTag'
        show_emergency_button:
          type: boolean
        video:
          $ref: '#/components/schemas/MemberOnboardingStepVideo'
        image_url:
          type: string
        title:
          type: string
        text:
          type: string
        navigation:
          $ref: '#/components/schemas/ServerNavigation'
        actions:
          type: array
          items:
            $ref: '#/components/schemas/MemberOnboardingStepAction'
        opt_ins:
          type: array
          items:
            $ref: '#/components/schemas/MemberOnboardingStepOptIn'
        progress:
          type: array
          items:
            $ref: '#/components/schemas/MemberOnboardingStepProgressItem'
        widget_component:
          type: string
    MemberOnboardingStepAction:
      type: object
      properties:
        label:
          default: ""
          type: string
        icon:
          type: string
        action_url:
          type: string
        is_back_visible:
          default: false
          type: boolean
        listen_app_state:
          type: string
      required:
        - action_url
    MemberOnboardingStepOptIn:
      type: object
      properties:
        text:
          type: string
        link:
          type: string
          default: ""
      required:
        - text
    MemberOnboardingStepFolder:
      type: object
      properties:
        title:
          type: string
        text:
          type: string
        image_url:
          type: string
        show_emergency_button:
          type: boolean
          default: false
      required:
        - title
        - text
    MemberOnboardingStepAccessoryImage:
      type: object
      properties:
        image_url:
          type: string
        start_time:
          type: integer
          default: 0
      required:
        - image_url
    MemberOnboardingStepTag:
      type: object
      properties:
        icon:
          type: string
        text:
          type: string
      required:
        - text
    MemberOnboardingStepVideo:
      type: object
      properties:
        video_url:
          type: string
        caption:
          type: string
          default: ""
        emergency_button_end_time:
          type: integer
      required:
        - video_url
    MemberOnboardingStepProgressItem:
      type: object
      properties:
        image_url:
          type: string
        title:
          type: string
        description:
          type: string
      required:
        - image_url
        - title
        - description
    Mgm:
      type: object
      properties:
        title:
          type: string
        description:
          type: string
        code:
          type: string
        link:
          type: string
        link_title:
          type: string
        share_text:
          type: string
        share_action_title:
          type: string
    Invoices:
      type: object
      properties:
        invoices:
          type: array
          items:
            $ref: '#/components/schemas/Invoice'
      required:
        - invoices
    Invoice:
      type: object
      properties:
        status:
          type: string
        reference_date:
          type: string
        due_date:
          type: string
        breakdown:
          type: array
          items:
            $ref: '#/components/schemas/InvoiceBreakdown'
        payment_info:
          $ref: '#/components/schemas/InvoicePaymentInfo'
        total_amount:
          type: number
          format: double
        can_generate_second_copy:
          type: boolean
      required:
        - reference_date
        - due_date
        - total_amount
        - can_generate_second_copy
    InvoiceBreakdown:
      type: object
      properties:
        type:
          type: string
        title:
          type: string
        amount:
          type: number
          format: double
        info:
          $ref: '#/components/schemas/ServerNavigation'
      required:
        - title
        - amount
    InvoicePaymentInfo:
      type: object
      properties:
        id:
          type: string
        title:
          type: string
        code:
          type: string
        share_text:
          type: string
        document_url:
          type: string
        payment_url:
          type: string
      required:
        - id
    Payment:
      type: object
      properties:
        id:
          type: string
        amount:
          type: number
          format: double
        due_date:
          type: string
        status:
          type: string
          default: other
          enum: [ approved, pending, declined, canceled, error, other ]
        reason:
          type: string
        method:
          type: string
          default: other
          enum: [ pix, boleto, creditCard, other ]
        payment_code:
          type: string
        payment_url:
          type: string
        national_id:
          type: string
        invoice:
          $ref: '#/components/schemas/PaymentInvoice'
      required:
        - id
        - amount
        - due_date
        - status
        - reason
        - method
        - national_id
        - invoice
    PaymentInvoice:
      type: object
      properties:
        first_name:
          type: string
        last_name:
          type: string
        amount:
          type: number
          format: double
        reference_date:
          type: string
        national_id:
          type: string
        invoice_items:
          type: array
          items:
            $ref: '#/components/schemas/PaymentInvoiceItem'
      required:
        - first_name
        - last_name
        - amount
        - reference_date
        - national_id
        - invoice_items
    PaymentInvoiceItem:
      type: object
      properties:
        title:
          type: string
        amount:
          type: number
          format: double
      required:
        - title
        - amount
    RefundAction:
      type: object
      properties:
        label:
          type: string
        type:
          type: string
          default: none
          enum: [ updateRefund, selfie, createFinancialData, close, navigation, none ]
        remove_previous_steps:
          type: boolean
          default: false
        navigation:
          $ref: '#/components/schemas/RemoteAction'
      required:
        - label
        - type
        - remove_previous_steps
    RefundDocument:
      type: object
      properties:
        title:
          type: string
        description:
          type: string
          default: ""
        action:
          type: string
          default: ""
        details:
          type: string
          default: ""
        max_size:
          type: integer
          default: 10000
        type:
          type: string
          default: ""
        files:
          type: array
          default: [ ]
          items:
            $ref: '#/components/schemas/RefundFile'
      required:
        - title
        - description
        - action
        - details
        - max_size
        - type
        - files
    RefundFile:
      type: object
      properties:
        id:
          type: string
        name:
          type: string
          default: ""
        details:
          type: string
          default: ""
        extension:
          type: string
          default: ""
        size:
          type: string
          default: ""
        file_url:
          type: string
        image_url:
          type: string
        deleted:
          type: boolean
          default: false
      required:
        - id
        - details
        - name
        - extension
        - size
        - deleted
    RefundFileDelete:
      type: object
      properties:
        success:
          type: boolean
      required:
        - success
    RefundScreenInfo:
      type: object
      properties:
        title:
          type: string
        image:
          type: string
        description:
          type: string
          default: ""
        instructions:
          type: string
          default: ""
        step_progress:
          type: integer
        primary_action:
          $ref: '#/components/schemas/RefundAction'
        secondary_action:
          $ref: '#/components/schemas/RefundAction'
      required:
        - title
        - description
        - instructions
    RefundStep:
      type: object
      properties:
        id:
          type: string
        step:
          type: string
          default: error
          enum: [ selfieVerification, documentsAttachment, paymentInfo, confirmation, requested, error ]
        screen_info:
          $ref: '#/components/schemas/RefundScreenInfo'
        documents:
          type: array
          default: [ ]
          items:
            $ref: '#/components/schemas/RefundDocument'
        account_holders:
          type: array
          default: [ ]
          items:
            $ref: '#/components/schemas/AccountHolder'
      required:
        - id
        - step
        - screen_info
        - documents
        - account_holders
    ScheduleTimeline:
      type: object
      properties:
        timeline:
          type: array
          items:
            $ref: '#/components/schemas/ScheduleTimelineItem'
      required:
        - timeline
    ScheduleTimelineItem:
      type: object
      properties:
        title:
          type: string
        description:
          type: string
        date:
          type: string
          format: date-time
        icon:
          type: string
        status:
          type: string
      required:
        - title
        - description
    # ActionDuplicate:
    #   type: object
    #   properties:
    #     text:
    #       type: string
    #     navigation:
    #       $ref: '#/components/schemas/ServerNavigation'
    AppointmentScheduleOptionsCollection:
      type: object
      properties:
        price_info:
          $ref: '#/components/schemas/AppointmentScheduleOptionPriceInfo'
        option_groups:
          type: array
          items:
            $ref: '#/components/schemas/AppointmentScheduleGroup'
      required:
        - option_groups
    AppointmentScheduleOptionPriceInfo:
      type: object
      properties:
        title:
          type: string
        description:
          type: string
      required:
        - title
        - description
    AppointmentScheduleGroup:
      type: object
      properties:
        title:
          type: string
        subtitle:
          type: string
        label:
          type: string
        image_urls:
          type: array
          items:
            type: string
        health_plan_options:
          type: array
          items:
            $ref: '#/components/schemas/HealthPlanScheduleTask'
        options:
          type: array
          items:
            $ref: '#/components/schemas/AppointmentScheduleOption'
        type:
          type: string
          enum:
            - healthDeclaration
            - immersion
            - nutritionist
            - test
            - healthcareTeam
            - physiotherapist
            - physicalEducator
            - community
            - followUp
            - connection
            - other
      required:
        - title
        - image_urls
        - options
    HealthPlanScheduleTask:
      type: object
      properties:
        section:
          type: string
        health_plan_task_id:
          type: string
        title:
          type: string
        description:
          type: string
        frequency:
          $ref: '#/components/schemas/HealthPlanItemFrequencyV0'
        deadline:
          $ref: '#/components/schemas/HealthPlanItemDeadlineV0'
        start:
          $ref: '#/components/schemas/HealthPlanItemStartV0'
        calendar:
          $ref: '#/components/schemas/HealthPlanScheduleRequestV0'
        acknowledged_at:
          type: string
        group:
          $ref: '#/components/schemas/HealthPlanItemGroupV0'
      required:
        - section
        - title
    AppointmentScheduleOption:
      type: object
      properties:
        title:
          type: string
        description:
          type: string
        calendar:
          $ref: '#/components/schemas/AppointmentScheduleOptionCalendar'
        image_url:
          type: string
        schedule_options:
          type: array
          items:
            $ref: '#/components/schemas/ScheduleOptionItem'
      required:
        - title
    AppointmentScheduleOptionCalendar:
      type: object
      properties:
        navigation:
          $ref: '#/components/schemas/ServerNavigation'
        calendar_url:
          type: string
    B2bOnboardingBeneficiary:
      type: object
      properties:
        id:
          type: string
        parent_beneficiary_id:
          type: string
        type:
          type: string
          default: unknown
          enum: [ employee, dependent, unknown ]
        contract_type:
          type: string
          enum: [ clt, pj ]
        parent_beneficiary_relation_type:
          type: string
          enum: [ child, spouse, grandchild, greatgrandchild, stepchild, partner ]
        activated_at:
          type: string
          format: date-time
        current_phase:
          type: string
          enum: [ readyToOnboard, sendHealthDeclaration, healthDeclaration, sendHealthDeclarationAppointment, healthDeclarationAppointment, healthDeclarationAppointmentScheduled, waitingCptsApplication, sendCpts, cptsConfirmation, contractSignature, sendRegistration, registration, waitingForReview, healthCareTeamSelection, finished ]
        flow_type:
          type: string
          enum: [ fullRiskFlow, partialRiskFlow, noRiskFlow ]
        person_info:
          $ref: '#/components/schemas/BeneficiaryPersonInfo'
        company_info:
          $ref: '#/components/schemas/BeneficiaryCompanyInfo'
        is_member_active:
          type: boolean
        product_name:
          type: string
        show_cpts_confirmation_card:
          type: boolean
        show_activation_date:
          type: boolean
        dependents:
          type: array
          items:
            $ref: '#/components/schemas/B2bOnboardingBeneficiary'
      required:
        - id
        - type
        - activated_at
        - person_info
        - company_info
        - is_member_active
        - dependents
    BeneficiaryPersonInfo:
      type: object
      properties:
        id:
          type: string
        first_name:
          type: string
        last_name:
          type: string
        national_id:
          type: string
        email:
          type: string
        phone_number:
          type: string
        nick_name:
          type: string
        gender_identity:
          type: string
          enum: [ cisgender, transsexualWoman, transsexualMan, travesti, nonBinary, ratherNotAnswer ]
        accepted_terms_at:
          type: string
      required:
        - id
        - first_name
        - last_name
        - national_id
        - email
    BeneficiaryCompanyInfo:
      type: object
      properties:
        id:
          type: string
        name:
          type: string
        legal_name:
          type: string
        cnpj:
          type: string
      required:
        - id
        - name
        - legal_name
        - cnpj
    B2bOnboardingVideoCall:
      type: object
      properties:
        schedule_url:
          type: string
        success_redirect_url:
          type: string
        schedule:
          $ref: '#/components/schemas/VideoCall'
    CptRestriction:
      type: object
      properties:
        title:
          type: string
        description:
          type: string
      required:
        - title
        - description
    CptItem:
      type: object
      properties:
        condition:
          type: string
        cids:
          type: array
          items:
            type: string
        restrictions:
          type: array
          items:
            $ref: '#/components/schemas/CptRestriction'
      required:
        - condition
        - cids
        - restrictions
    CptList:
      type: object
      properties:
        cpts:
          type: array
          items:
            $ref: '#/components/schemas/CptItem'
      required:
        - cpts
    BeneficiaryContractTerm:
      type: object
      properties:
        id:
          type: string
        type:
          type: string
          enum: [ healthDeclaration, ans_orientation_letter ]
        title:
          type: string
        description:
          type: string
        document_url:
          type: string
        signed:
          type: boolean
      required:
        - id
        - type
        - title
        - description
        - document_url
        - signed
    BeneficiaryContract:
      type: object
      properties:
        id:
          type: string
        signed:
          type: boolean
        terms:
          type: array
          items:
            $ref: '#/components/schemas/BeneficiaryContractTerm'
      required:
        - id
        - signed
        - terms
    BenefitCollection:
      type: object
      properties:
        benefits:
          type: array
          items:
            $ref: '#/components/schemas/Benefit'
    Benefit:
      type: object
      properties:
        braze_campaign_id:
          type: string
        partner:
          type: string
        title:
          type: string
        description:
          type: string
        image_url:
          type: string
        viewed:
          type: boolean
        removed:
          type: boolean
        clicked:
          type: boolean
        url:
          type: string
        lgpd_url:
          type: string
        created_at_unix:
          type: integer
        expires_at_unix:
          type: integer
        instructions:
          type: array
          items:
            type: string
        promocode:
          type: string
        terms:
          type: string
        braze_card:
          type: string
      required:
        - title
        - description
    BenefitOptInStatus:
      type: object
      properties:
        partner:
          type: string
        status:
          type: string
          default: unknown
          enum: [ accepted, revoked, unknown ]
      required:
        - partner
        - status
    # ChannelFileResponse:
    #   type: object
    #   properties:
    #     id:
    #       type: string
    #     type:
    #       type: string
    #     url:
    #       type: string
    #   required:
    #     - id
    #     - type
    #     - url
    ContractDetails:
      type: object
      properties:
        sections:
          type: array
          items:
            $ref: '#/components/schemas/ContractDetailsSection'
      required:
        - sections
    ContractDetailsSection:
      type: object
      properties:
        id:
          type: string
          default: contractPreConditionMember
          enum: [ contractPreConditionMember, contractPreConditionGracePeriod, contractGeneralGracePeriod ]
        headline:
          type: string
        description:
          type: string
        caption:
          type: string
        button_label:
          type: string
        footnote:
          type: string
        content:
          type: array
          items:
            $ref: '#/components/schemas/ContractDetailsSectionContent'
        actions:
          type: array
          items:
            $ref: '#/components/schemas/ContractDetailsSectionAction'
      required:
        - id
        - headline
        - button_label
        - content
    ContractDetailsSectionContent:
      type: object
      properties:
        title:
          type: string
        subtitle:
          type: string
      required:
        - title
    ContractDetailsSectionAction:
      type: object
      properties:
        title:
          type: string
        action:
          $ref: '#/components/schemas/ServerNavigation'
      required:
        - title
        - action
    ContractPersonalInfo:
      type: object
      properties:
        address:
          $ref: '#/components/schemas/UserAddress'
        identity:
          $ref: '#/components/schemas/UserIdentity'
        gender:
          $ref: '#/components/schemas/UserGender'
        social_first_name:
          type: string
        social_last_name:
          type: string
        biological_sex:
          $ref: '#/components/schemas/UserSex'
    ContractSummary:
      type: object
      properties:
        card:
          $ref: '#/components/schemas/OnboardingSummaryCard'
        next_phases:
          type: array
          items:
            $ref: '#/components/schemas/OnboardingSummaryPhase'
    ContractSigningParts:
      type: object
      properties:
        document_url:
          type: string
        parts:
          type: array
          items:
            $ref: '#/components/schemas/ContractSigningPart'
      required:
        - document_url
        - parts
    ContractSigningPart:
      type: object
      properties:
        part:
          type: string
          default: ansOrientationLetter
          enum: [ ansOrientationLetter, healthDeclaration, contract ]
        start_page:
          type: integer
        end_page:
          type: integer
        signed:
          type: boolean
      required:
        - part
        - signed
    CptAndGraces:
      type: object
      properties:
        cpt:
          $ref: '#/components/schemas/Cpt'
        grace:
          $ref: '#/components/schemas/Graces'
      required:
        - grace
    Cpt:
      type: object
      properties:
        remaining_time:
          $ref: '#/components/schemas/ConditionRemainingTime'
        cpts:
          type: array
          items:
            type: string
        procedures:
          type: array
          items:
            $ref: '#/components/schemas/CptProcedure'
        all_procedures_url:
          type: string
      required:
        - remaining_time
        - cpts
        - procedures
        - all_procedures_url
    CptProcedure:
      type: object
      properties:
        title:
          type: string
        description:
          type: string
        remaining_time:
          $ref: '#/components/schemas/ConditionRemainingTime'
      required:
        - title
        - remaining_time
    Graces:
      type: object
      properties:
        signature_date:
          type: string
        graces:
          type: array
          items:
            $ref: '#/components/schemas/Grace'
      required:
        - signature_date
        - graces
    Grace:
      type: object
      properties:
        title:
          type: string
        remaining_time:
          $ref: '#/components/schemas/ConditionRemainingTime'
      required:
        - title
        - remaining_time
    ConditionRemainingTime:
      type: object
      properties:
        total:
          type: number
          format: double
        remaining:
          type: number
          format: double
        unit:
          type: string
          enum: [ months, days, years ]
      required:
        - total
        - remaining
    Emergency:
      type: object
      properties:
        phone:
          type: string
    FileVaultResponse:
      type: object
      properties:
        url:
          type: string
      required:
        - url
    ForceUpdate:
      type: object
      properties:
        blocker:
          $ref: '#/components/schemas/ForceUpdateAppVersion'
        current:
          $ref: '#/components/schemas/ForceUpdateAppVersion'
        needs_logout:
          type: boolean
        info:
          $ref: '#/components/schemas/ForceUpdateInfo'
    ForceUpdateAppVersion:
      type: object
      properties:
        version:
          type: string
        platform:
          type: string
      required:
        - version
        - platform
    ForceUpdateInfo:
      type: object
      properties:
        title:
          type: string
        text:
          type: string
        emergency_text:
          type: string
        emergency_url:
          type: string
        action_title:
          type: string
        action_url:
          type: string
      required:
        - title
        - text
        - emergency_text
        - action_title
        - action_url
    FreeInputConfig:
      type: object
      properties:
        options:
          type: array
          items:
            $ref: '#/components/schemas/FreeInputConfigOptions'
        keyboard_type:
          type: string
          default: alphanumeric
          enum: [ alphanumeric, phone, email, numeric, datetime, none ]
        capitalization_type:
          type: string
          default: sentences
          enum: [ none, words, sentences, allCharacters ]
        auto_correct_type:
          type: string
          default: yes
          enum: [ yes, no ]
        hint:
          type: string
        confirmation_text:
          type: string
        mask:
          type: string
        regex_mask:
          type: string
        block:
          type: boolean
        default_value:
          type: boolean
        toggle_label:
          type: string
        button_label:
          type: string
        free_text_label:
          type: string
        url:
          type: string
        default_direction:
          type: string
          default: front
          enum: [ front, back, external ]
    FreeInputConfigOptions:
      type: object
      properties:
        label:
          type: string
        value:
          type: string
        primary:
          type: boolean
      required:
        - label
        - value
        - primary
    HealthCareTeam:
      type: object
      properties:
        nurse:
          $ref: '#/components/schemas/Nurse'
        physician:
          $ref: '#/components/schemas/Physician'
      required:
        - nurse
        - physician
    # HealthCommunityCollection:
    #   type: object
    #   properties:
    #     categories:
    #       type: array
    #       items:
    #         $ref: '#/components/schemas/HealthCommunityCategory'
    #   required:
    #     - categories
    # HealthCommunityCategory:
    #   type: object
    #   properties:
    #     category:
    #       type: string
    #     alert_text:
    #       type: string
    #     partners:
    #       type: array
    #       items:
    #         $ref: '#/components/schemas/PartnerV0'
    #     categories:
    #       type: array
    #       items:
    #         $ref: '#/components/schemas/HealthCommunityCategory'
    #     tag:
    #       $ref: '#/components/schemas/Tag'
    #   required:
    #     - category
    # LinearHealthCommunityCollection:
    #   type: object
    #   properties:
    #     sections:
    #       type: array
    #       items:
    #         $ref: '#/components/schemas/LinearHealthCommunitySection'
    #   required:
    #     - sections
    # LinearHealthCommunitySection:
    #   type: object
    #   properties:
    #     name:
    #       type: string
    #     alert_text:
    #       type: string
    #     level:
    #       type: integer
    #     partners:
    #       type: array
    #       items:
    #         $ref: '#/components/schemas/PartnerV0'
    #     tag:
    #       $ref: '#/components/schemas/Tag'
    #   required:
    #     - name
    #     - level
    # HealthDeclarationProperties:
    #   type: object
    #   properties:
    #     show_emergency_bar:
    #       type: boolean
    HealthDeclarationQuestion:
      type: object
      properties:
        id:
          type: string
        image_url:
          type: string
        question:
          type: string
        required:
          type: boolean
        progress:
          type: integer
        input:
          $ref: '#/components/schemas/HealthDeclarationQuestionInput'
      required:
        - id
        - question
        - progress
        - input
    HealthDeclarationQuestionInput:
      type: object
      properties:
        type:
          type: string
          default: multipleOptions
          enum: [ imc, freeText, multipleOptions, matrix ]
        action:
          $ref: '#/components/schemas/ActionLink'
        allow_other:
          type: boolean
        options:
          type: array
          items:
            $ref: '#/components/schemas/HealthDeclarationInputOption'
        groups:
          type: array
          items:
            $ref: '#/components/schemas/HealthDeclarationInputGroup'
        config:
          $ref: '#/components/schemas/FreeInputConfig'
      required:
        - type
        - action
    HealthDeclarationInputOption:
      type: object
      properties:
        label:
          type: string
        value:
          type: string
        tooltip:
          type: string
        exclusive:
          type: boolean
      required:
        - label
        - value
    HealthDeclarationInputGroup:
      type: object
      properties:
        title:
          type: string
        options:
          type: array
          items:
            $ref: '#/components/schemas/HealthDeclarationInputOption'
      required:
        - title
        - options
    HealthDeclarationAnswers:
      type: object
      properties:
        height:
          type: number
          format: double
        weight:
          type: number
          format: double
        answers:
          type: array
          items:
            $ref: '#/components/schemas/HealthDeclarationAnswer'
    HealthDeclarationAnswer:
      type: object
      properties:
        condition:
          type: string
        value:
          type: string
      required:
        - value
    # HealthDocumentsCollection:
    #   type: object
    #   properties:
    #     health_documents:
    #       type: array
    #       items:
    #         $ref: '#/components/schemas/HealthDocument'
    # HealthDocument:
    #   type: object
    #   properties:
    #     id:
    #       type: string
    #     uploaded_at:
    #       type: string
    #     performed_at:
    #       type: string
    #     document_name:
    #       type: string
    #     document_url:
    #       type: string
    #     thumbnail_url:
    #       type: string
    #     document_size:
    #       type: string
    #   required:
    #     - id
    #     - uploaded_at
    #     - document_name
    #     - document_url
    # HealthGoals:
    #   type: object
    #   properties:
    #     health_goals:
    #       type: array
    #       items:
    #         $ref: '#/components/schemas/HealthGoal'
    # HealthGoal:
    #   type: object
    #   properties:
    #     id:
    #       type: string
    #     name:
    #       type: string
    #     image_url:
    #       type: string
    #   required:
    #     - id
    #     - name
    #     - image_url
    # HealthPlanSpecialistProfile:
    #   type: object
    #   properties:
    #     title:
    #       type: string
    #     profile:
    #       $ref: '#/components/schemas/PartnerV0'
    HealthPlanV0:
      type: object
      properties:
        title:
          type: string
        description:
          type: string
        created_at:
          type: string
          format: date-time
        healthcare_team:
          $ref: '#/components/schemas/HealthCareTeam'
        items:
          type: array
          items:
            $ref: '#/components/schemas/HealthPlanItemV0'
        cta_button:
          $ref: '#/components/schemas/HealthPlanCtaV0'
        card_info:
          $ref: '#/components/schemas/PresentationCardInfo'
        invalid:
          type: boolean
    HealthPlanItemV0:
      type: object
      properties:
        section:
          type: string
          default: other
          enum:
            - physical_activity
            - sleep
            - mood
            - eating
            - other
            - test_requests
            - prescriptions
            - referrals
            - schedulings
        health_plan_task_id:
          type: string
        related_task_ids:
          type: array
          items:
            type: string
        description:
          type: string
        created_at:
          type: string
          format: date-time
        schedule_info:
          $ref: '#/components/schemas/ScheduleInfo'
        recurrent_schedule_info:
          $ref: '#/components/schemas/RecurrentScheduleInfo'
        preparation:
          $ref: '#/components/schemas/HealthPlanItemPreparationsV0'
        frequency:
          $ref: '#/components/schemas/HealthPlanItemFrequencyV0'
        deadline:
          $ref: '#/components/schemas/HealthPlanItemDeadlineV0'
        acknowledged_at:
          type: string
        start:
          $ref: '#/components/schemas/HealthPlanItemStartV0'
        content:
          type: string
        status:
          type: string
          default: to_do
          enum: [ to_do, done, deleted_by_member, expired ]
        favorite:
          type: boolean
        attachments:
          type: array
          items:
            $ref: '#/components/schemas/HealthPlanItemAttachmentV0'
        infos:
          type: array
          items:
            type: string
        navigations:
          type: array
          items:
            $ref: '#/components/schemas/HealthPlanItemNavigationV0'
        calendar:
          $ref: '#/components/schemas/HealthPlanScheduleRequestV0'
        group:
          $ref: '#/components/schemas/HealthPlanItemGroupV0'
        specialty:
          type: string
        specialist_id:
          type: string
        schedule_info_path:
          type: string
        staff_info:
          $ref: '#/components/schemas/HealthPlanItemStaffInfoV0'
        hide_schedule_options:
          type: boolean
        finish_task:
          $ref: '#/components/schemas/HealthPlanItemActionV0'
        delete_task:
          $ref: '#/components/schemas/HealthPlanItemActionV0'
      required:
        - section
        - description
        - status
    HealthPlanItemStaffInfoV0:
      type: object
      properties:
        name:
          type: string
        profile_image_url:
          type: string
        role:
          type: string
    HealthPlanItemNavigationV0:
      type: object
      properties:
        name:
          type: string
        image_url:
          type: string
        navigation:
          $ref: '#/components/schemas/ServerNavigation'
    HealthPlanItemAttachmentV0:
      type: object
      properties:
        name:
          type: string
        link:
          type: string
      required:
        - name
        - link
    HealthPlanScheduleRequestV0:
      type: object
      properties:
        navigation:
          $ref: '#/components/schemas/ServerNavigation'
        calendar_url:
          type: string
        schedule_options:
          type: array
          items:
            $ref: '#/components/schemas/ScheduleOptionItem'
    HealthPlanCtaV0:
      type: object
      properties:
        label:
          type: string
        navigation:
          $ref: '#/components/schemas/ServerNavigation'
    HealthPlanItemStartV0:
      type: object
      properties:
        description:
          type: string
        type:
          type: string
          enum: [ Conditional, Timestamp ]
        date:
          type: string
    HealthPlanItemFrequencyV0:
      type: object
      properties:
        description:
          type: string
    HealthPlanItemDeadlineV0:
      type: object
      properties:
        type:
          type: string
          enum: [ Periodic, Timestamp, Continuous ]
        date:
          type: string
          format: date-time
        description:
          type: string
    HealthPlanItemGroupV0:
      type: object
      properties:
        id:
          type: string
        name:
          type: string
      required:
        - id
        - name
    HealthPlanItemPreparationsV0:
      type: object
      properties:
        warning:
          type: string
        preparations:
          type: array
          items:
            $ref: '#/components/schemas/HealthPlanItemPreparationV0'
      required:
        - preparations
    HealthPlanItemPreparationV0:
      type: object
      properties:
        title:
          type: string
        description:
          type: string
      required:
        - title
        - description
    PresentationCardInfo:
      type: object
      properties:
        title:
          type: string
        description:
          type: string
        images:
          type: array
          items:
            type: string
      required:
        - title
    HealthPlanItemActionV0:
      type: object
      properties:
        action_label:
          type: string
        title:
          type: string
        image_url:
          type: string
        description:
          type: string
        confirm_label:
          type: string
        cancel_label:
          type: string
      required:
        - action_label
        - title
        - image_url
    # HealthTeamRecommendations:
    #   type: object
    #   properties:
    #     recommendations:
    #       type: array
    #       items:
    #         $ref: '#/components/schemas/HealthTeamRecommendation'
    # HealthTeamRecommendation:
    #   type: object
    #   properties:
    #     id:
    #       type: string
    #     title:
    #       type: string
    #     recommended:
    #       type: boolean
    #     description:
    #       type: string
    #     staff:
    #       type: array
    #       items:
    #         $ref: '#/components/schemas/Staff'
    #     calendar:
    #       $ref: '#/components/schemas/CalendarSummary'
    #   required:
    #     - recommended
    #     - calendar
    # CalendarSummary:
    #   type: object
    #   properties:
    #     title:
    #       type: string
    #     locations:
    #       type: array
    #       items:
    #         $ref: '#/components/schemas/CalendarLocation'
    #   required:
    #     - title
    #     - locations
    # CalendarLocation:
    #   type: object
    #   properties:
    #     title:
    #       type: string
    #     description:
    #       type: string
    #     location:
    #       type: string
    #     location_url:
    #       type: string
    #     photo_urls:
    #       type: array
    #       items:
    #         type: string
    #     calendar_url:
    #       type: string
    #   required:
    #     - title
    #     - calendar_url
    # HelpCenterCollection:
    #   type: object
    #   properties:
    #     groups:
    #       type: array
    #       items:
    #         $ref: '#/components/schemas/HelpCenterGroup'
    # HelpCenterGroup:
    #   type: object
    #   properties:
    #     title:
    #       type: string
    #     featured:
    #       type: boolean
    #     items:
    #       type: array
    #       items:
    #         $ref: '#/components/schemas/HelpCenterItem'
    #   required:
    #     - title
    #     - featured
    #     - items
    # HelpCenterItem:
    #   type: object
    #   properties:
    #     id:
    #       type: string
    #     title:
    #       type: string
    #     description:
    #       type: string
    #     image_url:
    #       type: string
    #   required:
    #     - id
    #     - title
    #     - description
    # HelpCenterFeedbackResponse:
    #   type: object
    #   properties:
    #     id:
    #       type: string
    #     useful:
    #       type: boolean
    #     show_user_input:
    #       type: boolean
    #     feedback_message:
    #       type: string
    #   required:
    #     - id
    #     - useful
    # KidsInformationRequest:
    #   type: object
    #   properties:
    #     name:
    #       type: string
    #     cpf:
    #       type: string
    #     videos:
    #       type: array
    #       items:
    #         $ref: '#/components/schemas/KidsInformationRequestVideo'
    #   required:
    #     - name
    #     - cpf
    #     - videos
    # KidsInformationRequestVideo:
    #   type: object
    #   properties:
    #     description:
    #       type: string
    #     url:
    #       type: string
    #   required:
    #     - description
    #     - url
    # Meeting:
    #   type: object
    #   properties:
    #     id:
    #       type: string
    #     name:
    #       type: string
    #     status:
    #       type: string
    #     health_professional:
    #       $ref: '#/components/schemas/Staff'
    #     amazon_chime_config:
    #       $ref: '#/components/schemas/MeetingConfig'
    #   required:
    #     - id
    #     - name
    #     - status
    # MeetingConfig:
    #   type: object
    #   properties:
    #     meeting_response:
    #       $ref: '#/components/schemas/MeetingResponse'
    #     attendee_response:
    #       $ref: '#/components/schemas/AttendeeResponse'
    #   required:
    #     - meeting_response
    # MeetingResponse:
    #   type: object
    #   properties:
    #     meeting:
    #       $ref: '#/components/schemas/MeetingObj'
    #   required:
    #     - meeting
    # MeetingObj:
    #   type: object
    #   properties:
    #     meeting_id:
    #       type: string
    #     external_meeting_id:
    #       type: string
    #     media_region:
    #       type: string
    #     media_placement:
    #       $ref: '#/components/schemas/MediaPlacement'
    #   required:
    #     - meeting_id
    #     - external_meeting_id
    #     - media_region
    #     - media_placement
    # MediaPlacement:
    #   type: object
    #   properties:
    #     audio_host_url:
    #       type: string
    #     audio_fallback_url:
    #       type: string
    #     screen_data_url:
    #       type: string
    #     screen_sharing_url:
    #       type: string
    #     screen_viewing_url:
    #       type: string
    #     signaling_url:
    #       type: string
    #     turn_control_url:
    #       type: string
    #   required:
    #     - audio_host_url
    #     - audio_fallback_url
    #     - screen_data_url
    #     - screen_sharing_url
    #     - screen_viewing_url
    #     - signaling_url
    #     - turn_control_url
    # AttendeeResponse:
    #   type: object
    #   properties:
    #     attendee:
    #       $ref: '#/components/schemas/AttendeeObj'
    #   required:
    #     - attendee
    # AttendeeObj:
    #   type: object
    #   properties:
    #     attendee_id:
    #       type: string
    #     external_user_id:
    #       type: string
    #     join_token:
    #       type: string
    #   required:
    #     - attendee_id
    #     - external_user_id
    #     - join_token
    # MeetingParticipant:
    #   type: object
    #   properties:
    #     id:
    #       type: string
    #     name:
    #       type: string
    #     profile_picture_url:
    #       type: string
    #     is_local:
    #       type: boolean
    #     has_mic_open:
    #       type: boolean
    #     has_camera_open:
    #       type: boolean
    #   required:
    #     - id
    #     - name
    #     - is_local
    #     - has_mic_open
    #     - has_camera_open
    Membership:
      type: object
      properties:
        card:
          $ref: '#/components/schemas/MembershipCard'
        cards:
          type: array
          items:
            $ref: '#/components/schemas/MembershipCard'
        contract:
          $ref: '#/components/schemas/MembershipContract'
        product:
          $ref: '#/components/schemas/MembershipProduct'
        show_cpt_with_graces:
          type: boolean
      required:
        - card
        - cards
        - contract
        - product
    MembershipCard:
      type: object
      properties:
        number:
          type: string
        type:
          type: string
          default: unknown
          enum: [ alice, duquesa, cassi, unknown ]
        start_date:
          type: string
          format: date-time
        product_display_name:
          type: string
        expiration_date:
          type: string
          format: date-time
        ans_number:
          type: string
        footer:
          type: string
      required:
        - number
        - type
    MembershipContract:
      type: object
      properties:
        document_url:
          type: string
      required:
        - document_url
    MembershipProduct:
      type: object
      properties:
        title:
          type: string
        price:
          type: number
          format: double
        type:
          type: string
          default: b2c
          enum: [ b2b, b2c ]
      required:
        - title
    # NewTestResultCollection:
    #   type: object
    #   properties:
    #     results:
    #       type: array
    #       items:
    #         $ref: '#/components/schemas/NewTestResult'
    # NewTestResult:
    #   type: object
    #   properties:
    #     date:
    #       type: string
    #     provider:
    #       $ref: '#/components/schemas/NewTestResultProvider'
    #     results:
    #       type: array
    #       items:
    #         $ref: '#/components/schemas/NewTestResultItem'
    # NewTestResultProvider:
    #   type: object
    #   properties:
    #     name:
    #       type: string
    #     description:
    #       type: string
    #     image_url:
    #       type: string
    # NewTestResultItem:
    #   type: object
    #   properties:
    #     id:
    #       type: string
    #     description:
    #       type: string
    #     attachment_url:
    #       type: string
    #   required:
    #     - id
    #     - description
    #     - attachment_url
    # NewTestResultFeedbacks:
    #   type: object
    #   properties:
    #     feedbacks:
    #       type: array
    #       items:
    #         $ref: '#/components/schemas/NewTestResultFeedbackItem'
    # NewTestResultFeedbackItem:
    #   type: object
    #   properties:
    #     id:
    #       type: string
    #     added_at:
    #       type: string
    #     description:
    #       type: string
    #   required:
    #     - id
    #     - added_at
    #     - description
    # NewTestResultFeedbackItemDetails:
    #   type: object
    #   properties:
    #     content:
    #       type: string
    #     added_at:
    #       type: string
    #     staff_info:
    #       $ref: '#/components/schemas/NewTestResultFeedbackItemStaffInfo'
    #     results:
    #       type: array
    #       items:
    #         $ref: '#/components/schemas/NewTestResultItem'
    #     tasks:
    #       type: array
    #       items:
    #         $ref: '#/components/schemas/HealthPlanItemV0'
    #   required:
    #     - content
    #     - added_at
    #     - staff_info
    # NewTestResultFeedbackItemStaffInfo:
    #   type: object
    #   properties:
    #     id:
    #       type: string
    #     firs_name:
    #       type: string
    #     last_name:
    #       type: string
    #     profile_image_url:
    #       type: string
    #     role:
    #       type: string
    #     description:
    #       type: string
    #   required:
    #     - id
    #     - role
    Nurse:
      type: object
      properties:
        first_name:
          type: string
        profile_image_url:
          type: string
      required:
        - first_name
        - image_url
    OnboardingResult:
      type: object
      properties:
        navigation:
          $ref: '#/components/schemas/ServerNavigation'
      required:
        - navigation
    OnboardingStep:
      type: object
      properties:
        id:
          type: string
        local_id:
          type: string
        input:
          $ref: '#/components/schemas/OnboardingInput'
        question:
          type: string
        title:
          type: string
        details:
          type: string
        hide_next_button:
          type: boolean
      required:
        - input
    OnboardingInput:
      type: object
      properties:
        type:
          type: string
          default: freeText
          enum: [ freeText, optionButtons, webview ]
        action:
          $ref: '#/components/schemas/ActionLink'
        config:
          $ref: '#/components/schemas/FreeInputConfig'
      required:
        - type
        - action
    OnboardingSummaryCard:
      type: object
      properties:
        title:
          type: string
        text:
          type: string
        action_title:
          type: string
        action:
          $ref: '#/components/schemas/ServerNavigation'
        schedule:
          $ref: '#/components/schemas/VideoCall'
      required:
        - title
    OnboardingSummaryPhase:
      type: object
      properties:
        title:
          type: string
        subtitle:
          type: string
      required:
        - title
    # PartnerV0:
    #   type: object
    #   properties:
    #     id:
    #       type: string
    #     provider_id:
    #       type: string
    #     name:
    #       type: string
    #     type:
    #       type: string
    #     education:
    #       type: array
    #       items:
    #         type: string
    #     crm:
    #       type: string
    #     curiosity:
    #       type: string
    #     qualifications:
    #       type: array
    #       items:
    #         $ref: '#/components/schemas/PartnerQualificationV0'
    #     specialties:
    #       type: array
    #       items:
    #         type: string
    #     cnpj:
    #       type: string
    #     site:
    #       type: string
    #     sub_specialties:
    #       type: array
    #       items:
    #         type: string
    #     addresses:
    #       type: array
    #       items:
    #         $ref: '#/components/schemas/PartnerAddressV0'
    #     simplified_addresses:
    #       type: array
    #       items:
    #         $ref: '#/components/schemas/PartnerAddressV0'
    #     contact_call_out:
    #       $ref: '#/components/schemas/PartnerCalloutV0'
    #     phones:
    #       type: array
    #       items:
    #         $ref: '#/components/schemas/PartnerPhoneV0'
    #     working_hours:
    #       $ref: '#/components/schemas/WorkingHours'
    #     working_hours_status:
    #       type: string
    #     icon_url:
    #       type: string
    #     image_url:
    #       type: string
    #     schedule_availability_days:
    #       type: integer
    #     appointment_types:
    #       type: array
    #       items:
    #         type: string
    #     attendance_age:
    #       type: string
    #     scheduling_url:
    #       type: string
    #     main_action:
    #       $ref: '#/components/schemas/PartnerMainAction'
    #   required:
    #     - id
    #     - name
    # PartnerQualificationV0:
    #   type: object
    #   properties:
    #     image_url:
    #       type: string
    #     description:
    #       type: string
    #   required:
    #     - image_url
    #     - description
    # PartnerAddressV0:
    #   type: object
    #   properties:
    #     title:
    #       type: string
    #     address:
    #       type: string
    #   required:
    #     - address
    # PartnerPhoneV0:
    #   type: object
    #   properties:
    #     title:
    #       type: string
    #     phone:
    #       type: string
    #     phone_url:
    #       type: string
    #     type:
    #       type: string
    #   required:
    #     - phone
    # PartnerOfficeV0:
    #   type: object
    #   properties:
    #     name:
    #       type: string
    #     address:
    #       $ref: '#/components/schemas/PartnerAddressV0'
    #     phones:
    #       type: array
    #       items:
    #         $ref: '#/components/schemas/PartnerPhoneV0'
    # PartnerCalloutV0:
    #   type: object
    #   properties:
    #     title:
    #       type: string
    #     body:
    #       type: string
    #     variant:
    #       type: string
    #     avatar_url:
    #       type: string
    #     action:
    #       $ref: '#/components/schemas/CalloutAction'
    #   required:
    #     - title
    #     - body
    #     - variant
    PaymentSummary:
      type: object
      properties:
        card:
          $ref: '#/components/schemas/OnboardingSummaryCard'
        next_phases:
          type: array
          items:
            $ref: '#/components/schemas/OnboardingSummaryPhase'
    Physician:
      type: object
      properties:
        first_name:
          type: string
        profile_image_url:
          type: string
      required:
        - first_name
        - image_url
    # PortabilityDocument:
    #   type: object
    #   properties:
    #     name:
    #       type: string
    #     path:
    #       type: string
    #   required:
    #     - name
    #     - path
    # PortabilityHealthInsurances:
    #   type: object
    #   properties:
    #     health_insurances:
    #       type: array
    #       items:
    #         $ref: '#/components/schemas/PortabilityHealthInsuranceItem'
    #     default_steps:
    #       type: array
    #       items:
    #         $ref: '#/components/schemas/PortabilityHealthInsuranceStep'
    #   required:
    #     - health_insurances
    #     - default_steps
    # PortabilityHealthInsuranceItem:
    #   type: object
    #   properties:
    #     name:
    #       type: string
    #     color:
    #       type: string
    #     steps:
    #       type: array
    #       items:
    #         $ref: '#/components/schemas/PortabilityHealthInsuranceStep'
    #   required:
    #     - name
    #     - color
    #     - steps
    # PortabilityHealthInsuranceStep:
    #   type: object
    #   properties:
    #     order:
    #       type: integer
    #     description:
    #       type: string
    #     icon_url:
    #       type: string
    #   required:
    #     - order
    #     - description
    PortabilityQuestion:
      type: object
      properties:
        question:
          type: string
        detail:
          type: string
        input:
          $ref: '#/components/schemas/PortabilityQuestionInput'
        product_order:
          $ref: '#/components/schemas/ProductOrder'
        navigation:
          $ref: '#/components/schemas/ServerNavigation'
    PortabilityQuestionInput:
      type: object
      properties:
        type:
          type: string
        action:
          $ref: '#/components/schemas/ActionLink'
        options:
          type: array
          items:
            $ref: '#/components/schemas/PortabilityQuestionInputOption'
        config:
          $ref: '#/components/schemas/FreeInputConfig'
      required:
        - type
        - action
    PortabilityQuestionInputOption:
      type: object
      properties:
        label:
          type: string
        value:
          type: string
        exclusive:
          type: boolean
      required:
        - label
        - value
    PortabilityAnswer:
      type: object
      properties:
        value:
          type: string
      required:
        - value
    PortabilityStatus:
      type: object
      properties:
        image_url:
          type: string
        title:
          type: string
        subtitle:
          type: string
        detail:
          type: string
        product_order:
          $ref: '#/components/schemas/ProductOrder'
        action_button:
          $ref: '#/components/schemas/PortabilityStatusActionButton'
    PortabilityStatusActionButton:
      type: object
      properties:
        title:
          type: string
        navigation:
          $ref: '#/components/schemas/ServerNavigation'
    PortabilityStep:
      type: object
      properties:
        current:
          type: string
          default: other
          enum: [ analysis, finished, guideline, preLetterExplanation, letterExplanation, documentUploadLetter, documentUploadLetterPaymentReceipt, other ]
        title:
          type: string
        detail:
          type: string
        more:
          $ref: '#/components/schemas/PortabilityStepMore'
        input:
          $ref: '#/components/schemas/PortabilityStepInput'
        navigation:
          $ref: '#/components/schemas/ServerNavigation'
      required:
        - current
        - title
        - input
    PortabilityStepMore:
      type: object
      properties:
        hint:
          type: string
        title:
          type: string
        description:
          type: string
      required:
        - hint
        - title
        - description
    PortabilityStepInput:
      type: object
      properties:
        type:
          type: string
          default: acknowledge
          enum: [ optionButtons, acknowledge ]
        action:
          $ref: '#/components/schemas/PortabilityStepInputAction'
        options:
          type: array
          items:
            $ref: '#/components/schemas/PortabilityStepInputOption'
      required:
        - type
        - action
    PortabilityStepInputAction:
      type: object
      properties:
        next:
          $ref: '#/components/schemas/PortabilityStepInputActionUrl'
        previous:
          $ref: '#/components/schemas/PortabilityStepInputActionUrl'
    PortabilityStepInputActionUrl:
      type: object
      properties:
        href:
          type: string
      required:
        - href
    PortabilityStepInputOption:
      type: object
      properties:
        title:
          type: string
        detail:
          type: string
        value:
          type: string
      required:
        - title
        - value
    PortabilityStepStatus:
      type: object
      properties:
        id:
          type: string
        status:
          type: string
          default: other
          enum: [ approved, declined, pending, other ]
        type:
          type: string
          default: other
          enum: [ specific, other ]
        notes:
          type: string
        declined_reasons:
          type: array
          items:
            type: string
            default: other
            enum:
              - approvedToOtherProduct
              - dropped
              - incompatibleHospitals
              - incompatibleProducts
              - minGracePeriodNotAchieved
              - missingDocuments
              - noCptFulfillment
              - noHealthInsurance
              - noPayment
              - other
        missing_documents:
          type: array
          items:
            $ref: '#/components/schemas/PortabilityMissingDocument'
      required:
        - id
        - status
    PortabilityMissingDocument:
      type: object
      properties:
        type:
          type: string
          default: other
          enum: [ letter, paymentReceipt, other ]
        month:
          type: integer
      required:
        - type
    ProductOrder:
      type: object
      properties:
        id:
          type: string
        title:
          type: string
        original_price:
          type: number
          format: double
        price:
          type: number
          format: double
        discount_percentage:
          type: number
          format: double
        promo_code:
          type: string
        product_details:
          $ref: '#/components/schemas/ProductOrderDetail'
    ProductOrderDetail:
      type: object
      properties:
        sections:
          type: array
          items:
            $ref: '#/components/schemas/ProductOrderSection'
      required:
        - sections
    ProductOrderSection:
      type: object
      properties:
        title:
          type: string
        items:
          type: array
          items:
            $ref: '#/components/schemas/ProductOrderSectionItem'
      required:
        - title
        - items
    ProductOrderSectionItem:
      type: object
      properties:
        title:
          type: string
        detail:
          type: string
      required:
        - title
    QuestionnaireProperties:
      type: object
      properties:
        type:
          type: string
        source_type:
          type: string
        source_id:
          type: string
      required:
        - type
    QuestionnaireQuestion:
      type: object
      properties:
        id:
          type: string
        question:
          type: string
        input:
          $ref: '#/components/schemas/QuestionnaireQuestionInput'
        progress:
          type: integer
        progress_index:
          $ref: '#/components/schemas/QuestionnaireQuestionProgressIndex'
        section:
          $ref: '#/components/schemas/QuestionnaireQuestionSection'
        selected_answer:
          $ref: '#/components/schemas/QuestionnaireQuestionAnswer'
        required:
          default: true
          type: boolean
      required:
        - question
        - input
        - progress
        - required
    QuestionnaireQuestionInput:
      type: object
      properties:
        type:
          type: string
        hint:
          type: string
        action:
          $ref: '#/components/schemas/ActionLink'
        back_action:
          $ref: '#/components/schemas/ActionLink'
        options:
          type: array
          items:
            $ref: '#/components/schemas/QuestionnaireInputOption'
        display_attributes:
          $ref: '#/components/schemas/QuestionnaireQuestionDisplayAttributes'
        input_configurations:
          $ref: '#/components/schemas/QuestionnaireQuestionInputConfigurations'
        image_url:
          type: string
      required:
        - type
        - action
    QuestionnaireInputOption:
      type: object
      properties:
        label:
          type: string
        value:
          type: string
        next:
          type: integer
    QuestionnaireQuestionDisplayAttributes:
      type: object
      properties:
        view_type:
          type: string
        options_orientation:
          type: string
    QuestionnaireQuestionInputConfigurations:
      type: object
      properties:
        range:
          $ref: '#/components/schemas/QuestionnaireValidationRange'
        step_size:
          type: number
          format: double
        decimal_digits:
          type: integer
        unit:
          type: string
        helper_text:
          type: string
    QuestionnaireValidationRange:
      type: object
      properties:
        begin:
          type: number
          format: double
        end:
          type: number
          format: double
      required:
        - begin
        - end
    QuestionnaireQuestionSection:
      type: object
      properties:
        title:
          type: string
        details:
          type: string
        back_cover:
          $ref: '#/components/schemas/QuestionnaireQuestionSectionBackCover'
        image_url:
          type: string
        next_button_text:
          type: string
        estimated_time_text:
          type: string
      required:
        - title
    QuestionnaireQuestionSectionBackCover:
      type: object
      properties:
        title:
          type: string
        properties:
          type: array
          items:
            $ref: '#/components/schemas/QuestionnaireQuestionSectionBackCoverProperties'
    QuestionnaireQuestionSectionBackCoverProperties:
      type: object
      properties:
        title:
          type: string
        description:
          type: string
        image_url:
          type: string
      required:
        - title
        - description
        - image_url
    QuestionnaireQuestionProgressIndex:
      type: object
      properties:
        current:
          type: integer
        total:
          type: integer
      required:
        - current
        - total
    QuestionnaireQuestionAnswer:
      type: object
      properties:
        next:
          type: integer
        value:
          type: string
      required:
        - next
        - value
    # QuestionnaireResponse:
    #   type: object
    #   properties:
    #     value:
    #       type: string
    #     next:
    #       type: integer
    #     questionnaire_id:
    #       type: string
    #     question_id:
    #       type: string
    #     staff_id:
    #       type: string
    #     is_multiple_options:
    #       type: boolean
    #     labels:
    #       type: array
    #       items:
    #         type: string
    #   required:
    #     - value
    #     - is_multiple_options
    #     - labels
    RecurrentScheduleInfo:
      type: object
      properties:
        scheduled_at:
          type: string
          format: date-time
        details:
          type: string
    # ReferralScheduleInfoCollection:
    #   type: object
    #   properties:
    #     title:
    #       type: string
    #     suggestions:
    #       type: array
    #       items:
    #         $ref: '#/components/schemas/PartnerV0'
    #     specialists:
    #       type: array
    #       items:
    #         $ref: '#/components/schemas/PartnerV0'
    #     others:
    #       type: array
    #       items:
    #         $ref: '#/components/schemas/PartnerV0'
    # RegistrationPerson:
    #   type: object
    #   properties:
    #     first_name:
    #       type: string
    #     last_name:
    #       type: string
    #     nick_name:
    #       type: string
    #     email:
    #       type: string
    #     phone:
    #       type: string
    #     date_of_birth:
    #       type: string
    #     postal_code:
    #       type: string
    #     national_id:
    #       type: string
    #     terms_accepted:
    #       type: boolean
    #     data_and_communication_opt_in:
    #       type: boolean
    # Reminders:
    #   type: object
    #   properties:
    #     reminders:
    #       type: array
    #       items:
    #         $ref: '#/components/schemas/Reminder'
    #     empty:
    #       $ref: '#/components/schemas/Reminders'
    #   required:
    #     - empty
    # Reminder:
    #   type: object
    #   properties:
    #     title:
    #       type: string
    #     message:
    #       type: string
    #     style:
    #       type: string
    #     image:
    #       $ref: '#/components/schemas/ReminderImage'
    #     action:
    #       $ref: '#/components/schemas/Action'
    #     text_theme:
    #       type: string
    #     completion:
    #       $ref: '#/components/schemas/ReminderCompletion'
    #   required:
    #     - title
    #     - message
    #     - style
    #     - image
    #     - action
    # ReminderImage:
    #   type: object
    #   properties:
    #     url:
    #       type: string
    #     type:
    #       type: string
    #   required:
    #     - url
    #     - type
    # ReminderCompletion:
    #   type: object
    #   properties:
    #     message:
    #       type: string
    #   required:
    #     - message
    RemoteImage:
      type: object
      properties:
        url:
          type: string
        description:
          type: string
      required:
        - url
        - description
    ScheduleInfo:
      type: object
      properties:
        scheduled_at:
          type: string
          format: date-time
        details:
          type: string
      required:
        - scheduled_at
    # ScheduleInfoPartnerCollection:
    #   type: object
    #   properties:
    #     title:
    #       type: string
    #     more:
    #       $ref: '#/components/schemas/Action'
    #     providers:
    #       type: array
    #       items:
    #         $ref: '#/components/schemas/PartnerV0'
    ScheduleOptionItem:
      type: object
      properties:
        name:
          type: string
        calendar_url:
          type: string
        address:
          type: string
      required:
        - name
        - calendar_url
    # ScheduledAppointmentCollection:
    #   type: object
    #   properties:
    #     values:
    #       type: array
    #       items:
    #         $ref: '#/components/schemas/ScheduledAppointment'
    #     show_schedule_button:
    #       type: boolean
    #       default: true
    # ScheduledAppointment:
    #   type: object
    #   properties:
    #     id:
    #       type: string
    #     name:
    #       type: string
    #     start_time:
    #       type: string
    #     cancel_url:
    #       type: string
    #     reschedule_url:
    #       type: string
    #     location:
    #       type: string
    #     staff:
    #       type: array
    #       items:
    #         $ref: '#/components/schemas/Staff'
    #     location_url:
    #       type: string
    #     pricing_info:
    #       $ref: '#/components/schemas/ScheduledAppointmentPricing'
    #   required:
    #     - id
    #     - name
    #     - start_time
    #     - cancel_url
    #     - reschedule_url
    #     - location
    #     - staff
    # ScheduledAppointmentPricing:
    #   type: object
    #   properties:
    #     title:
    #       type: string
    #     body:
    #       type: string
    #     footer_button_text:
    #       type: string
    #     price:
    #       type: string
    #   required:
    #     - title
    #     - body
    #     - footer_button_text
    #     - price
    # ScoreMagentaResult:
    #   type: object
    #   properties:
    #     result:
    #       type: array
    #       items:
    #         $ref: '#/components/schemas/ScoreMagentaResultItem'
    #     recommendation:
    #       $ref: '#/components/schemas/ScoreMagentaResultRecommendation'
    #     score_magenta_filter:
    #       type: array
    #       items:
    #         $ref: '#/components/schemas/ScoreMagentaFilter'
    #     score_magenta_banner:
    #       $ref: '#/components/schemas/ScoreMagentaBanner'
    #     cta:
    #       $ref: '#/components/schemas/ScoreMagentaAction'
    #   required:
    #     - result
    #     - recommendation
    #     - score_magenta_filter
    # ScoreMagentaResultItem:
    #   type: object
    #   properties:
    #     id:
    #       type: string
    #     dimension:
    #       type: string
    #     pillar:
    #       type: string
    #     detail_image_url:
    #       type: string
    #     header_image_url:
    #       type: string
    #     list_image_url:
    #       type: string
    #     score:
    #       $ref: '#/components/schemas/ScoreMagentaResultValue'
    #     about:
    #       $ref: '#/components/schemas/ScoreMagentaResultAbout'
    #     date:
    #       $ref: '#/components/schemas/ResultDate'
    #   required:
    #     - dimension
    #     - pillar
    #     - score
    #     - about
    # ScoreMagentaResultValue:
    #   type: object
    #   properties:
    #     value:
    #       type: integer
    #     max:
    #       type: integer
    #     delta:
    #       type: integer
    #     reference_delta_id:
    #       type: string
    #   required:
    #     - value
    #     - max
    # ScoreMagentaResultAbout:
    #   type: object
    #   properties:
    #     description:
    #       type: string
    #     ranges:
    #       type: array
    #       items:
    #         $ref: '#/components/schemas/ScoreMagentaResultAboutRange'
    #   required:
    #     - description
    #     - ranges
    # ScoreMagentaResultAboutRange:
    #   type: object
    #   properties:
    #     image_url:
    #       type: string
    #     title:
    #       type: string
    #     interval:
    #       type: string
    #   required:
    #     - title
    #     - interval
    # ScoreMagentaResultRecommendation:
    #   type: object
    #   properties:
    #     messages:
    #       type: array
    #       items:
    #         type: string
    #     healthcare_team:
    #       type: array
    #       items:
    #         $ref: '#/components/schemas/ScoreMagentaResultHealcareTeamItem'
    #   required:
    #     - messages
    #     - healthcare_team
    # ScoreMagentaResultHealcareTeamItem:
    #   type: object
    #   properties:
    #     image_url:
    #       type: string
    #   required:
    #     - image_url
    # ResultDate:
    #   type: object
    #   properties:
    #     chart_value:
    #       type: string
    #     formatted_value:
    #       type: string
    #     original_value:
    #       type: string
    #   required:
    #     - chart_value
    #     - formatted_value
    #     - original_value
    # ScoreMagentaFilter:
    #   type: object
    #   properties:
    #     filter_key:
    #       type: string
    #     filter_values:
    #       type: object
    #       additionalProperties:
    #         type: string
    #   required:
    #     - filter_key
    #     - filter_values
    # ScoreMagentaWelcome:
    #   type: object
    #   properties:
    #     welcome_list:
    #       type: array
    #       items:
    #         $ref: '#/components/schemas/WelcomeItem'
    #     button:
    #       $ref: '#/components/schemas/Button'
    #   required:
    #     - welcome_list
    #     - button
    # WelcomeItem:
    #   type: object
    #   properties:
    #     title:
    #       type: string
    #     text:
    #       type: string
    # ButtonDuplicate:
    #   type: object
    #   properties:
    #     label:
    #       type: string
    # ScoreMagentaBanner:
    #   type: object
    #   properties:
    #     style:
    #       type: string
    #     result_action:
    #       $ref: '#/components/schemas/ScoreMagentaAction'
    #     answer_action:
    #       $ref: '#/components/schemas/ScoreMagentaAction'
    #   required:
    #     - style
    # ScoreMagentaAction:
    #   type: object
    #   properties:
    #     title:
    #       type: string
    #     description:
    #       type: string
    #     navigation:
    #       $ref: '#/components/schemas/ServerNavigation'
    #   required:
    #     - title
    #     - navigation
    # ServerMessage:
    #   type: object
    #   properties:
    #     key:
    #       type: string
    #     value:
    #       type: string
    #   required:
    #     - key
    #     - value
    ServerNavigation:
      type: object
      properties:
        link:
          $ref: '#/components/schemas/ActionLink'
        mobile_route:
          type: string
        root:
          type: boolean
          default: false
        navigation:
          $ref: '#/components/schemas/ServerNavigation'
        properties:
          type: object
    # ChannelNavigationProperties:
    #   type: object
    #   properties:
    #     channel_id:
    #       type: string
    #     message:
    #       type: string
    #     file_url:
    #       type: string
    #     type:
    #       type: string
    #     category:
    #       type: string
    # MemberVideoCallProperties:
    #   type: object
    #   properties:
    #     meeting_id:
    #       type: string
    # HealthCommunityNavigationProperties:
    #   type: object
    #   properties:
    #     type:
    #       type: string
    # DataRegistrationNavigationProperties:
    #   type: object
    #   properties:
    #     missing_data:
    #       type: array
    #       items:
    #         type: string
    #     person:
    #       $ref: '#/components/schemas/RegistrationPerson'
    # TestResultFeedbackDetailsProperties:
    #   type: object
    #   properties:
    #     test_result_feedback_id:
    #       type: string
    #   required:
    #     - test_result_feedback_id
    # Session:
    #   type: object
    #   properties:
    #     id:
    #       type: string
    #     links:
    #       type: array
    #       items:
    #         $ref: '#/components/schemas/ActionLink'
    #     messages:
    #       type: array
    #       items:
    #         $ref: '#/components/schemas/ServerMessage'
    #     navigation:
    #       $ref: '#/components/schemas/ServerNavigation'
    #     alice_tracker_disabled:
    #       type: boolean
    #     crm_key:
    #       type: string
    #     person_brand:
    #       type: string
    #     bottom_tabs:
    #       type: array
    #       items:
    #         $ref: '#/components/schemas/BottomTab'
    #   required:
    #     - bottom_tabs
    ShoppingBundle:
      type: object
      properties:
        sections:
          type: array
          items:
            $ref: '#/components/schemas/ShoppingBundleSection'
        info:
          type: array
          items:
            $ref: '#/components/schemas/ShoppingBundleInfo'
        product_order:
          $ref: '#/components/schemas/ProductOrder'
        order_action:
          $ref: '#/components/schemas/ActionLink'
        contact_action:
          type: string
        age_range:
          type: string
      required:
        - sections
        - info
        - product_order
        - order_action
    ShoppingBundleSection:
      type: object
      properties:
        icon:
          type: string
        title:
          type: string
        is_mutable:
          type: boolean
        content:
          $ref: '#/components/schemas/ShoppingBundleSectionContent'
        sub_section:
          $ref: '#/components/schemas/ShoppingBundleSubSection'
      required:
        - icon
        - title
        - is_mutable
        - content
    ShoppingBundleSubSection:
      type: object
      properties:
        title:
          type: string
        content:
          $ref: '#/components/schemas/ShoppingBundleSectionContent'
      required:
        - title
        - content
    ShoppingBundleSectionContent:
      type: object
      properties:
        thumbnail:
          $ref: '#/components/schemas/RemoteImage'
        options:
          $ref: '#/components/schemas/ShoppingBundleOptions'
        detail:
          $ref: '#/components/schemas/ShoppingBundleDetail'
        observation:
          type: array
          items:
            type: string
      required:
        - observation
    ShoppingBundleOptions:
      type: object
      properties:
        update_action:
          $ref: '#/components/schemas/ActionLink'
        bundles:
          type: array
          items:
            $ref: '#/components/schemas/ShoppingBundleItem'
      required:
        - bundles
    ShoppingBundleItem:
      type: object
      properties:
        name:
          type: string
        price_delta:
          type: number
          format: double
        price:
          type: number
          format: double
        type:
          type: string
          enum: [ hospital, laboratory, other ]
        is_selected:
          type: boolean
        components:
          type: array
          items:
            $ref: '#/components/schemas/ShoppingBundleItemComponent'
        providers:
          type: array
          items:
            $ref: '#/components/schemas/ShoppingBundleItemComponent'
        accommodation:
          $ref: '#/components/schemas/ShoppingAccommodation'
        ids:
          type: array
          items:
            type: string
      required:
        - is_selected
        - components
        - providers
        - ids
    ShoppingBundleItemComponent:
      type: object
      properties:
        id:
          type: string
        name:
          type: string
        description:
          type: string
        image_url:
          type: string
        icon:
          type: string
        type:
          type: string
          enum: [ accommodation, hospital, children, other, maternity, laboratory ]
    ShoppingBundleDetail:
      type: object
      properties:
        title:
          type: string
        action:
          $ref: '#/components/schemas/ServerNavigation'
      required:
        - title
    ShoppingBundleInfo:
      type: object
      properties:
        title:
          type: string
        description:
          type: string
        action:
          $ref: '#/components/schemas/ServerNavigation'
      required:
        - title
        - description
        - action
    ShoppingAccommodation:
      type: object
      properties:
        type:
          type: string
        name:
          type: string
      required:
        - type
        - name
    # ShoppingResult:
    #   type: object
    #   properties:
    #     order:
    #       $ref: '#/components/schemas/ShoppingOrder'
    #     navigation:
    #       $ref: '#/components/schemas/ServerNavigation'
    #   required:
    #     - order
    #     - navigation
    # ShoppingOrder:
    #   type: object
    #   properties:
    #     id:
    #       type: string
    #   required:
    #     - id
    # SignInResponse:
    #   type: object
    #   properties:
    #     custom_token:
    #       type: string
    #     crm_key:
    #       type: string
    #   required:
    #     - custom_token
    Staff:
      type: object
      properties:
        id:
          type: string
        first_name:
          type: string
        last_name:
          type: string
        profile_image_url:
          type: string
        description:
          type: string
        profile_bio:
          type: string
        role:
          type: string
      required:
        - id
        - first_name
    # TestRequestScheduling:
    #   type: object
    #   properties:
    #     navigation:
    #       $ref: '#/components/schemas/ServerNavigation'
    #     calendar_url:
    #       type: string
    #     schedule_options:
    #       type: array
    #       items:
    #         $ref: '#/components/schemas/ScheduleOptionItem'
    #     file_path:
    #       type: string
    # TestRequestPersonTask:
    #   type: object
    #   properties:
    #     title:
    #       type: string
    #     description:
    #       type: string
    #     id:
    #       type: string
    # TrialOptions:
    #   type: object
    #   properties:
    #     title:
    #       type: string
    #     options:
    #       type: array
    #       items:
    #         $ref: '#/components/schemas/TrialOption'
    #   required:
    #     - title
    #     - options
    # TrialOption:
    #   type: object
    #   properties:
    #     title:
    #       type: string
    #     description:
    #       type: string
    #     navigation:
    #       $ref: '#/components/schemas/ServerNavigation'
    #   required:
    #     - title
    #     - description
    #     - navigation
    User:
      type: object
      properties:
        first_name:
          type: string
        last_name:
          type: string
        social_name:
          type: string
        email:
          type: string
        national_id:
          type: string
        phone_number:
          type: string
        nick_name:
          type: string
        date_of_birth:
          type: string
          format: date-time
        profile_picture_url:
          type: string
        sex:
          $ref: '#/components/schemas/UserSex'
        gender:
          $ref: '#/components/schemas/UserGender'
        non_binary_identity:
          type: string
        pronoun:
          type: string
          default: neutral
          enum: [ male, female, neutral ]
        address:
          $ref: '#/components/schemas/UserAddress'
        friendly_address:
          type: string
      required:
        - first_name
        - last_name
        - email
        - national_id
        - pronoun
    UserAddress:
      type: object
      properties:
        state:
          type: string
        city:
          type: string
        street:
          type: string
        number:
          type: string
        complement:
          type: string
        neighbourhood:
          type: string
        postal_code:
          type: string
    UserIdentity:
      type: object
      properties:
        document_number:
          type: string
        issuing_body:
          type: string
    UserType:
      type: object
      properties:
        is_member:
          type: boolean
      required:
        - is_member
    UserUpdate:
      type: object
      properties:
        first_name:
          type: string
        last_name:
          type: string
        nick_name:
          type: string
        email:
          type: string
        phone_number:
          type: string
        date_of_birth:
          type: string
        postal_code:
          type: string
        national_id:
          type: string
    ValidUser:
      type: object
      properties:
        email:
          type: string
        obfuscated_email:
          type: string
        national_id:
          type: string
        phone_number:
          type: string
        obfuscated_phone_number:
          type: string
    VideoCall:
      type: object
      properties:
        staff:
          $ref: '#/components/schemas/Staff'
        start_time:
          type: string
          format: date-time
        location:
          type: string
        cancel_url:
          type: string
        reschedule_url:
          type: string
      required:
        - staff
        - start_time
        - location
        - cancel_url
        - reschedule_url
    VideoCallSummary:
      type: object
      properties:
        card:
          $ref: '#/components/schemas/OnboardingSummaryCard'
        next_phases:
          type: array
          items:
            $ref: '#/components/schemas/OnboardingSummaryPhase'
    NavigationRoute:
      type: string
      enum:
        - benefits
        - membershipConditions
        - pdf
        - healthTeam
        - personalData
        - onbudsman
        - shopping
        - channel
        - health
        - schedule
        - registration
        - healthDeclarationAppointment
        - gasPayment
        - home
        - webView
        - contractSigning
        - appointmentSchedule
        - portabilitySteps
        - healthCommunity
        - preImmersion
        - externalApp
        - immersionSchedule
        - healthDocuments
        - invoices
        - healthPlanDetails
        - questionnaire
        - helpCenter
        - emergencyCare
        - screenedEmergencyCare
        - medCare
        - aliceAgora
        - dataRegistration
        - scoreMagentaInput
        - scoreMagentaResult
        - videoCall
        - b2bOnboarding
        - b2bTerms
        - legalGuardianRegister
        - kidsInfoRequest
        - legalGuardianResponsibilityTerm
        - healthDeclaration
        - childVideosRequest
        - memberOnboarding
        - testResultFeedbackDetails
        - featureOnboarding
        - healthPlanExternalSchedule
        - testPreparation
        - healthPlanSchedule
        - cheshireScreen
        - mainMenu
        - unifiedHealth
        - mgm
        - testResult
        - testResultFeedback
        - healthMeetingsList
        - duquesaHome
        - duquesaService
        - duquesaMainMenu
        - duquesaAppointmentIntro
        - emergencyCollection
        - duquesaScheduleEstablishmentGroups
        - duquesaScheduleAvailability
        - duquesaScheduleAvailabilityProfessional
        - duquesaScheduleDetail
        - duquesaScheduleConfirmation
        - refund
        - ignore
    RemoteActionAsyncRequestMethod:
      type: string
      enum: [ get, post, put, delete ]
    RemoteActionNavigationTransition:
      type: string
      enum:
        - regular
        - regularFullScreen
        - modal
        - modalWithHeader
        - modalWithFooter
        - modalWithHeaderAndFooter
        - input
        - inputWihoutConfirmationUndismissable
        - inputWihoutConfirmation
        - inputWithoutKeyboard
        - fade
        - slide
        - inverseSlide
        - bottomSheet
        - bottomSheetFixed
        - indicator
    UserGender:
      type: string
      enum: [ male, female, nonBinary ]
    UserSex:
      type: string
      enum: [ male, female, intersex ]
    MemberManagementAddresses:
      type: object
      properties:
        helper_text:
          type: string
        current_address:
          $ref: '#/components/schemas/Address'
        addresses:
          type: array
          items:
            $ref: '#/components/schemas/Address'
      required:
        - helper_text
        - addresses
    SearchAddresses:
      type: object
      properties:
        value:
          type: array
          items:
            $ref: '#/components/schemas/SearchAddress'
      required:
        - addresses
    SearchAddress:
      type: object
      properties:
        place_id:
          type: string
        description:
          type: string
        main_text:
          type: string
        secondary_text:
          type: string
      required:
        - place_id
        - description
        - main_text
        - secondary_text
    Address:
      type: object
      properties:
        nick_name:
          type: string
        country:
          type: string
        state:
          type: string
        city:
          type: string
        street:
          type: string
        number:
          type: string
        neighbourhood:
          type: string
        postal_code:
          type: string
        lat:
          type: number
          format: double
        lng:
          type: number
          format: double
      required:
        - nick_name
        - country
        - state
        - city
        - street
        - number
        - neighbourhood
        - postal_code
        - lat
        - lng
    CurrentPosition:
      type: object
      properties:
        lat:
          type: string
        lng:
          type: string
      required:
        - lat
        - lng
    ProcedureAuthorizationsResponse:
      type: object
      properties:
        title:
          type: string
        instructions:
          type: array
          items:
            $ref: '#/components/schemas/ProcedureAuthorizationInstruction'
        procedure_authorizations:
          type: array
          items:
            $ref: '#/components/schemas/ProcedureAuthorization'
      required:
        - title
    ProcedureAuthorizationInstruction:
      type: object
      properties:
        icon:
          type: string
        label:
          type: string
      required:
        - icon
        - label
    ProcedureAuthorization:
      type: object
      properties:
        id:
          type: string
        external_code:
          type: string
        status:
          type: string
          enum: [ cancelled, authorized, partiallyAuthorized, pending, unauthorized ]
        label:
          type: string
        requested_by:
          type: string
        requested_at:
          type: string
          format: date-time
        click_action:
          $ref: '#/components/schemas/RemoteAction'
      required:
        - id
        - external_code
        - status
        - label
        - requested_at
        - click_action
    ProcedureAuthorizationDetail:
      type: object
      properties:
        authorization:
          $ref: '#/components/schemas/ProcedureAuthorization'
        events:
          type: array
          items:
            $ref: '#/components/schemas/ProcedureAuthorizationDetailEvent'
      required:
        - authorization
        - events
    ProcedureAuthorizationDetailEvent:
      type: object
      properties:
        title:
          type: string
        disclaimer:
          type: string
        date:
          type: string
          format: date-time
        status:
          type: string
          enum: [ notStarted, inProgress, finished ]
        procedures:
          type: array
          items:
            $ref: '#/components/schemas/ProcedureAuthorizationDetailEventProcedure'
      required:
        - title
        - status
    ProcedureAuthorizationDetailEventProcedure:
      type: object
      properties:
        description:
          type: string
        tuss_code:
          type: string
        status:
          type: string
          enum: [ active, cancelled, pending, authorized, executed, unauthorized, preExecuted ]
      required:
        - tuss_code
        - status
    MemberOnboardingResponse:
      type: object
      properties:
        id:
          type: string
        title:
          type: string
        description:
          type: string
        steps_done:
          type: integer
        is_member_active:
          type: boolean
        steps:
          type: array
          items:
            $ref: '#/components/schemas/MemberOnboardingStepResponse'
      required:
        - id
        - title
        - description
        - steps_done
        - is_member_active
        - steps
    MemberOnboardingStepResponse:
      type: object
      properties:
        type:
          type: string
          enum: [ VIDEO, COVER, SCORE_MAGENTA, ALICE_INFO, CONCLUSION ]
        title:
          type: string
        url:
          type: string
        path:
          type: string
        time_to_complete:
          type: string
        bottom_sheet:
          $ref: '#/components/schemas/BottomSheetContent'
        background_image:
          type: string
        status:
          type: string
          enum: [ PENDING, COMPLETED, BLOCKED ]
        caption:
          type: string
        questionnaire_type:
          type: string
        data:
          $ref: '#/components/schemas/OnboardingTemplateData'
      required:
        - type
        - title
        - time_to_complete
        - status
    BottomSheetContent:
      type: object
      properties:
        title:
          type: string
        description:
          type: string
      required:
        - title
        - description
    OnboardingTemplateData:
      type: object
      properties:
        version:
          type: integer
    AliceExperienceTemplate:
      type: object
      properties:
        experience_section:
          $ref: '#/components/schemas/AliceExperienceSection'
        numbers_section:
          type: array
          items:
            $ref: '#/components/schemas/AliceExperienceNumbersSection'
      required:
        - experience_section
        - numbers_section
    AliceExperienceSection:
      type: object
      properties:
        title:
          type: string
        image_url:
          type: string
        sub_title:
          type: string
        description:
          type: string
      required:
        - title
        - image_url
        - sub_title
        - description
    AliceExperienceNumbersSection:
      type: object
      properties:
        title:
          type: string
        numbers:
          type: array
          items:
            $ref: '#/components/schemas/AliceExperienceNumbers'
      required:
        - title
        - numbers
    AliceExperienceNumbers:
      type: object
      properties:
        id:
          type: string
        title:
          type: string
        description:
          type: string
      required:
        - id
        - title
        - description
    AliceExperienceTemplateV2:
      type: object
      properties:
        header_section:
          $ref: '#/components/schemas/HeaderSection'
        info_sections:
          type: array
          items:
            $ref: '#/components/schemas/InfoSection'
      required:
        - header_section
        - info_sections
    HeaderSection:
      type: object
      properties:
        title:
          type: string
        image_url:
          type: string
        description:
          type: string
      required:
        - type
        - description
        - image_url
    InfoSection:
      type: object
      properties:
        title:
          $ref: '#/components/schemas/AliceExperienceText'
        subtitle:
          $ref: '#/components/schemas/AliceExperienceText'
        description:
          $ref: '#/components/schemas/AliceExperienceText'
        image:
          $ref: '#/components/schemas/AliceExperienceImage'
      required:
        - title
        - description
        - image
    AliceExperienceText:
      type: object
      properties:
        text:
          type: string
        text_style:
          type: string
          enum: [ H1, H2, H3, TEXT, TITLE_X_LARGE_HIGHLIGHT, TITLE_LARGE_HIGHLIGHT, TITLE_MEDIUM_HIGHLIGHT, TITLE_SMALL_HIGHLIGHT, TITLE_SMALL, BODY_MEDIUM, BODY_LARGE, LABEL_LARGE, DISPLAY_MEDIUM ]
      required:
        - text
        - text_style
    AliceExperienceImage:
      type: object
      properties:
        url:
          type: string
        position:
          type: string
          enum: [ TOP, BOTTOM ]
      required:
        - url
        - position
    OnboardingCoverTemplate:
      type: object
      properties:
        type:
          type: string
          enum: [ HEALTH_DECLARATION ]
        title:
          type: string
        description:
          type: string
        image_url:
          type: string
        agreement:
          type: string
        button:
          $ref: '#/components/schemas/OnboardingCoverButton'
      required:
        - type
        - title
        - description
        - image_url
        - agreement
        - button
    OnboardingCoverButton:
      type: object
      properties:
        label:
          type: string
        mobile_route:
          type: string
          enum: [ CHESHIRE_SCREEN, HEALTH_COMMUNITY, HEALTH_TEAM, HEALTH_DOCUMENTS, HEALTH_PLAN_DETAILS, EXTERNAL_APP, HELP_CENTER, ONBOARDING, DUQUESA_APPOINTMENT_INTRO, DUQUESA_SCHEDULE_ESTABLISHMENT_GROUPS, DUQUESA_SCHEDULE_AVAILABILITY, DUQUESA_SCHEDULE_DETAIL, DUQUESA_SCHEDULE_CONFIRMATION, ALICE_AGORA, REDESIGN_ALICE_AGORA, TEST_RESULT, TEST_RESULT_FEEDBACK, REFUND, PDF, CHANNEL, ACCREDITED_NETWORK, APPOINTMENT_SCHEDULE, PERSONAL_DATA, WEBVIEW, TEST_REQUEST_UPLOAD, HEALTH_DECLARATION, HEALTH_MEETINGS_LIST, BENEFITS, MEMBER_ONBOARDING_V2, SPECIALIST_DETAILS, DEMAND_CHANNELS_LIST, APPOINTMENT_DETAILS, SCORE_MAGENTA_INPUT, ARCHIVED_CHANNELS, QUESTIONNAIRE ]
        params:
          type: object
      required:
        - label
        - mobile_route
    OnboardingConclusionTemplate:
      type: object
      properties:
        title:
          type: string
        image_url:
          type: string
        description:
          type: string
        health_plan_tasks:
          type: array
          items:
            $ref: '#/components/schemas/OnboardingHealthPlanTask'
        health_infos:
          $ref: '#/components/schemas/HealthInfo'
      required:
        - title
        - image_url
        - health_plan_tasks
        - health_infos
    OnboardingHealthPlanTask:
      type: object
      properties:
        id:
          type: string
        icon:
          type: string
        title:
          type: string
        description:
          type: string
      required:
        - id
        - icon
        - title
    HealthInfo:
      type: object
      properties:
        title:
          type: string
        description:
          type: string
        physician_staff:
          $ref: '#/components/schemas/PhysicianStaff'
      required:
        - title
        - description
    PhysicianStaff:
      type: object
      properties:
        name:
          type: string
        council:
          type: string
        profileImageUrl:
          type: string
      required:
        - name
        - council
    ScreensResponse:
      type: object
      properties:
        id:
          type: string
        layout:
          $ref: '#/components/schemas/ScreenLayout'
        properties:
          $ref: '#/components/schemas/ScreenProperties'
      required:
        - id
        - layout
        - properties
    ScreenLayout:
      type: object
      properties:
        type:
          type: string
        appBar:
          $ref: '#/components/schemas/AppBar'
        body:
          type: array
          items:
            $ref: '#/components/schemas/Section'
        footer:
          $ref: '#/components/schemas/Section'
        list_item_has_padding:
          type: boolean
        background_type:
          type: string
          enum: [ ACTIVE, IDLE ]
          default: ACTIVE
      required:
        - type
        - body
        - background_type
    AliceFile:
      type: object
      properties:
        id:
          type: string
        file_name:
          type: string
        url:
          type: string
        type:
          type: string
        file_size:
          type: integer
      required:
        - id
        - file_name
        - url
        - type
    ChatAvailabilityResponse:
      type: object
      properties:
        hub:
          $ref: '#/components/schemas/ChatHubResponse'
        hub_administrative:
          $ref: '#/components/schemas/ChatHubResponse'
        header:
          $ref: '#/components/schemas/ChatHeaderResponse'
      required:
        - hub
        - header
    ChatHubResponse:
      type: object
      properties:
        is_available:
          type: boolean
        description:
          type: string
        availability:
          type: string
        action:
          $ref: '#/components/schemas/ChatAction'
      required:
        - is_available
        - description
        - availability
        - action
    ChatAction:
      type: object
      properties:
        description:
          type: string
        action:
          type: string
      required:
        - action
    ChatHeaderResponse:
      type: object
      properties:
        staff:
          type: array
          items:
            $ref: '#/components/schemas/StaffAvailableResponse'
        description:
          type: string
      required:
        - staff
        - description
    StaffAvailableResponse:
      type: object
      properties:
        profile_image_url:
          type: string
        is_available:
          type: boolean
      required:
        - profile_image_url
        - is_available
    DemandsChannels:
      type: object
      properties:
        demands:
          type: array
          items:
            $ref: '#/components/schemas/DemandChannels'
        resume_count:
          type: integer
        time:
          type: string
      required:
        - demands
        - resume_count
        - time
    DemandChannels:
      type: object
      properties:
        title:
          type: string
        description:
          type: string
        started_at:
          type: string
        channels:
          type: array
          items:
            type: string
      required:
        - title
        - description
        - started_at
        - channels
    ChannelsDemandsEnabledResponse:
      type: object
      properties:
        show:
          type: boolean
      required:
        - show
    ChannelDemandsFollowUpAction:
      type: object
      properties:
        action:
          $ref: '#/components/schemas/NavigationResponse'
      required:
        - action
    Navigation:
      type: object
      properties:
        method:
          $ref: '#/components/schemas/RemoteActionMethod'
        endpoint:
          type: string
        mobileRoute:
          $ref: '#/components/schemas/MobileRouting'
          nullable: true
        body:
          type: object
          nullable: true
        params:
          type: object
          nullable: true
    NavigationResponse:
      type: object
      properties:
        mobileRoute:
          $ref: '#/components/schemas/MobileRouting'
        link:
          $ref: '#/components/schemas/Link'
        navigation:
          $ref: '#/components/schemas/NavigationResponse'
        properties:
          type: object
          additionalProperties: true
          description: "A map of arbitrary properties"
        root:
          type: boolean
          nullable: true
          description: "Indicates whether this is the root element"
        params:
          type: object
          additionalProperties: true
          description: "A map of additional parameters"
      required:
        - mobileRoute
    Link:
      type: object
      properties:
        href:
          type: string
        rel:
          type: string
        type:
          type: string
          enum: [ GET, POST, PUT, DELETE ]
        parameters:
          type: array
          items:
            type: string
      required:
        - href
    ChannelsResponse:
      type: object
      properties:
        started_at:
          type: string
        channels:
          type: array
          items:
            $ref: '#/components/schemas/ChannelResponseDetails'
      required:
        - started_at
        - channels
    ChannelResponseDetails:
      type: object
      properties:
        id:
          type: string
        title:
          type: string
        time_last_message:
          type: string
        created_at:
          type: string
        archived_at:
          type: string
        staff_name:
          type: string
        staff_picture:
          type: string
        navigation:
          $ref: '#/components/schemas/NavigationResponse'
      required:
        - id
        - title
        - created_at
        - staff_name
        - staff_picture
        - navigation
    ChatResponse:
      type: object
      properties:
        id:
          type: string
      required:
        - id
    CsatTemplateResponse:
      type: object
      properties:
        id:
          type: string
        title:
          type: string
        rating:
          type: array
          items:
            $ref: '#/components/schemas/CsatRatingOption'
        questions:
          type: array
          items:
            $ref: '#/components/schemas/CsatQuestion'
        comments:
          $ref: '#/components/schemas/CsatComment'
        button_label:
          type: string
      required:
        - id
        - title
        - rating
        - questions
        - comments
        - button_label
    CsatRatingOption:
      type: object
      properties:
        value:
          type: integer
        label:
          type: string
        questions:
          type: array
          items:
            type: string
        showComments:
          type: boolean
          default: true
      required:
        - value
        - label
        - showComments
    CsatQuestion:
      type: object
      properties:
        key:
          type: string
        title:
          type: string
        type:
          type: string
          enum: [ SINGLE_SELECTION, MULTIPLE_SELECTION ]
          default: SINGLE_SELECTION
        tile_labels:
          type: array
          items:
            type: string
      required:
        - key
        - title
        - type
    CsatComment:
      type: object
      properties:
        title:
          type: string
        placeholder:
          type: string
      required:
        - title
        - placeholder
    CsatAnsweredRequest:
      type: object
      properties:
        feedback:
          $ref: '#/components/schemas/CsatFeedback'
        template_id:
          type: string
      required:
        - feedback
        - template_id
    CsatFeedback:
      type: object
      properties:
        title:
          type: string
        rate:
          type: integer
        answers:
          type: array
          items:
            $ref: '#/components/schemas/CsatAnswer'
        comment:
          type: string
      required:
        - title
        - rate
        - comment
    CsatAnswer:
      type: object
      properties:
        key:
          type: string
        title:
          type: string
        answers:
          type: array
          items:
            type: string
        comment:
          type: string
      required:
        - type
        - title
        - answers
    VideoCallMeetingResponse:
      type: object
      properties:
        id:
          type: string
        name:
          type: string
        type:
          type: string
          enum: [ ALICE_AGORA, NUTRITIONIST, PHYSICAL_PREPARATION, HEALTHCARE_TEAM, OTHERS ]
        status:
          type: string
          enum: [ NOT_STARTED, IN_PROGRESS, WAITING_HEALTH_PROFESSIONAL, WAITING_MEMBER, ENDED, CANCELLED ]
        amazon_chime_config:
          $ref: '#/components/schemas/AmazonChimeConfig'
        health_professional:
          $ref: '#/components/schemas/HealthProfessionalResponse'
      required:
        - id
        - name
        - type
        - status
    AmazonChimeConfig:
      type: object
      properties:
        meeting_response:
          $ref: '#/components/schemas/MeetingResponse'
        attendee_response:
          $ref: '#/components/schemas/AttendeeResponse'
    MeetingResponse:
      type: object
      properties:
        meeting:
          $ref: '#/components/schemas/Meeting'
      required:
        - meeting
    Meeting:
      type: object
      properties:
        meeting_id:
          type: string
        external_meeting_id:
          type: string
        media_region:
          type: string
        media_placement:
          $ref: '#/components/schemas/MediaPlacement'
      required:
        - meeting_id
        - external_meeting_id
        - media_region
        - media_placement
    MediaPlacement:
      type: object
      properties:
        audio_host_url:
          type: string
        audio_fall_back_url:
          type: string
        screen_data_url:
          type: string
        screen_sharing_url:
          type: string
        screen_viewing_url:
          type: string
        signaling_url:
          type: string
        turn_control_url:
          type: string
      required:
        - audio_host_url
        - audio_fall_back_url
        - screen_data_url
        - screen_sharing_url
        - screen_viewing_url
        - signaling_url
        - turn_control_url
    AttendeeResponse:
      type: object
      properties:
        attendee:
          $ref: '#/components/schemas/Attendee'
      required:
        - attendee
    Attendee:
      type: object
      properties:
        attendee_id:
          type: string
        external_user_id:
          type: string
        join_token:
          type: string
      required:
        - attendee_id
        - external_user_id
        - join_token
    HealthProfessionalResponse:
      type: object
      properties:
        id:
          type: string
        first_name:
          type: string
        last_name:
          type: string
        profile_image_url:
          type: string
        description:
          type: string
        on_vacation:
          type: boolean
          default: false
      required:
        - first_name
        - last_name
        - description
        - on_vacation
    RejectVideoCallReason:
      type: object
      properties:
        reasons:
          type: array
          items:
            $ref: '#/components/schemas/RejectVideoCallReasonItem'
      required:
        - reasons
    RejectVideoCallReasonItem:
      type: object
      properties:
        key:
          type: string
        description:
          type: string
      required:
        - key
        - description
    ScreensTransport:
      type: object
      properties:
        id:
          type: string
        layout:
          $ref: '#/components/schemas/ScreenLayout'
        properties:
          $ref: '#/components/schemas/ScreenProperties'
      required:
        - id
        - layout
    NavigateTriageRequest:
      type: object
      properties:
        selected_pills:
          type: string
        clicked_option:
          type: string
        selected_date:
          type: string
        input_text:
          type: string
        close_button_pressed:
          type: boolean
        chat_button_pressed:
          type: boolean
        back_button_pressed:
          type: boolean
        screen_id:
          type: string
        screening_navigation_id:
          type: string
    FinishTriageRequest:
      type: object
      properties:
        person_id:
          type: string
        content:
          type: string
        kind:
          type: string
          enum: [ CHAT, CHANNEL ]
        category:
          type: string
          enum: [ ASSISTANCE, ADMINISTRATIVE ]
        subCategory:
          type: string
          enum: [ ACUTE, LONGITUDINAL, SCREENING, VIRTUAL_CLINIC, MULTI ]
        tags:
          type: string
    ProviderResponse:
      type: object
      properties:
        title:
          type: string
        providersType:
          type: string
          nullable: true
        providersSubtypeId:
          type: string
          nullable: true
        emptyListTitle:
          type: string
          nullable: true
        emptyContent:
          $ref: '#/components/schemas/EmptyContentTransport'
        emptyListDescription:
          type: string
          nullable: true
        items:
          type: array
          items:
            $ref: '#/components/schemas/ProviderTransport'
        highlightsTitle:
          type: string
          nullable: true
        highlightsDescription:
          type: string
          nullable: true
        highlights:
          type: array
          items:
            $ref: '#/components/schemas/ProviderTransport'
        otherSectionTitle:
          type: string
        action:
          $ref: '#/components/schemas/ActionNavigation'
          nullable: true
        providers:
          type: array
          items:
            $ref: '#/components/schemas/ProviderSection'
        isDuquesa:
          type: boolean
        subSpecialties:
          type: array
          items:
            $ref: '#/components/schemas/SubSpecialtyTransport'
        callout:
          $ref: '#/components/schemas/CalloutSection'
          nullable: true
    EmptyContentTransport:
      type: object
      properties:
        title:
          type: string
          nullable: true
        description:
          type: string
          nullable: true
        action:
          $ref: '#/components/schemas/ActionNavigation'
          nullable: true
    ProviderTransport:
      type: object
      properties:
        id:
          type: string
          format: uuid
        staffId:
          type: string
          format: uuid
          nullable: true
        type:
          $ref: '#/components/schemas/ConsolidatedAccreditedNetworkType'
        name:
          type: string
        title:
          type: string
        subtitle:
          type: string
          nullable: true
        description:
          type: string
          nullable: true
        imageUrl:
          type: string
          nullable: true
        tag:
          $ref: '#/components/schemas/Tag'
          nullable: true
        captions:
          type: array
          items:
            $ref: '#/components/schemas/Caption'
          nullable: true
        distance:
          type: number
          format: double
          nullable: true
        deAccreditationDate:
          type: string
          format: date
          nullable: true
        hasHospitalHealthTeam:
          type: boolean
        navigation:
          $ref: '#/components/schemas/Navigation'
        action:
          $ref: '#/components/schemas/ActionNavigation'
          nullable: true
        gender:
          $ref: '#/components/schemas/Gender'
          nullable: true
        scheduleAvailabilityDays:
          type: integer
          nullable: true
        appointmentTypes:
          type: array
          items:
            $ref: '#/components/schemas/ConsolidatedAccreditedNetworkAppointmentType'
        subSpecialtyIds:
          type: array
          items:
            type: string
            format: uuid
        isFavorite:
          type: boolean
    ActionNavigation:
      type: object
      properties:
        label:
          type: string
        navigation:
          $ref: '#/components/schemas/NavigationResponse'
    ProviderSection:
      type: object
      properties:
        title:
          type: string
          nullable: true
        description:
          type: string
          nullable: true
        type:
          $ref: '#/components/schemas/ProviderSectionType'
        items:
          type: array
          items:
            $ref: '#/components/schemas/ProviderTransport'
    SubSpecialtyTransport:
      type: object
      properties:
        id:
          type: string
          format: uuid
        name:
          type: string
    ConsolidatedAccreditedNetworkType:
      type: string
      enum:
        - HOSPITAL
        - HOSPITAL_CHILDREN
        - LABORATORY
        - ALICE_HOUSE
        - EMERGENCY_UNITY
        - EMERGENCY_UNITY_CHILDREN
        - MATERNITY
        - CLINICAL
        - CLINICAL_COMMUNITY
        - CASSI_SPECIALIST
        - HEALTH_PROFESSIONAL
        - PARTNER_HEALTH_PROFESSIONAL
        - SPECIALIST_HEALTH_PROFESSIONAL
        - VACCINE
    Caption:
      type: object
      properties:
        text:
          type: string
        type:
          type: string
    RemoteAction:
      type: object
      properties:
        method:
          $ref: '#/components/schemas/RemoteActionMethod'
        endpoint:
          type: string
        mobileRoute:
          $ref: '#/components/schemas/MobileRouting'
          nullable: true
        params:
          type: object
          additionalProperties:
            type: string
          nullable: true
        type:
          $ref: '#/components/schemas/RemoteActionType'
          nullable: true
        transition:
          $ref: '#/components/schemas/RemoteActionTransition'
          nullable: true
        removeUntilRouteName:
          type: string
          nullable: true
    RemoteActionMethod:
      type: string
      enum:
        - GET
        - POST
        - PUT
        - DELETE
    MobileRouting:
      type: string
      enum:
        - ACCREDITED_NETWORK,
        - ACCREDITED_NETWORK_DETAIL,
        - ACCREDITED_NETWORK_FAVORITES,
        - ALICE_AGORA,
        - APPOINTMENT_DETAILS,
        - APPOINTMENT_SCHEDULE,
        - APPOINTMENT_TIMELINE,
        - ARCHIVED_CHANNELS,
        - B2B_TERMS,
        - BENEFITS,
        - CHANNEL,
        - CHANNEL_DEMAND,
        - CHESHIRE_SCREEN,
        - CHILD_VIDEOS_REQUEST,
        - CONTRACT,
        - CONTRACT_SIGNING,
        - DATA_REGISTRATION,
        - DEMAND_CHANNELS_LIST,
        - DUQUESA_APPOINTMENT_INTRO,
        - DUQUESA_HOME,
        - DUQUESA_MAIN_MENU,
        - DUQUESA_SCHEDULE_AVAILABILITY,
        - DUQUESA_SCHEDULE_AVAILABILITY_PROFESSIONAL,
        - DUQUESA_SCHEDULE_CONFIRMATION,
        - DUQUESA_SCHEDULE_DETAIL,
        - DUQUESA_SCHEDULE_ESTABLISHMENT_GROUPS,
        - DUQUESA_SERVICE,
        - EMERGENCY_CARE, // @deprecated use CHANNEL
        - EMERGENCY_CARE_PRICING,
        - EMERGENCY_CARE_STATUS,
        - EMERGENCY_COLLECTION,
        - EMERGENCY_LIST,
        - EXTERNAL_APP,
        - GAS_INIT,
        - GAS_PAYMENT,
        - HEALTCARE_TEAM_SCREENING,
        - HEALTH_COMMUNITY,
        - HEALTH_DECLARATION,
        - HEALTH_DECLARATION_APPOINTMENT,
        - HEALTH_DOCUMENTS,
        - HEALTH_MEETINGS_LIST,
        - HEALTH_PLAN_DETAILS,
        - HEALTH_PLAN_EXTERNAL_SCHEDULE,
        - HEALTH_PLAN_SCHEDULE,
        - HEALTH_TEAM,
        - HELP_CENTER,
        - HOME,
        - INVOICES,
        - LEGAL_GUARDIAN_REGISTER,
        - LEGAL_GUARDIAN_RESPONSIBILITY_TERM,
        - MEMBERSHIP_CONDITIONS,
        - MEMBER_ADDRESS_INPUT,
        - MEMBER_ONBOARDING,
        - MEMBER_ONBOARDING_V2,
        - ONBUDSMAN,
        - PDF,
        - PERSONAL_DATA,
        - PORTABILITY_QUESTIONS,
        - PORTABILITY_STATUS,
        - PORTABILITY_STEPS,
        - PROCEDURE_AUTHORIZATION_DETAIL,
        - PROCEDURE_AUTHORIZATION_LIST,
        - QUESTIONNAIRE,
        - QUEST_IMMERSION_PROFILE,
        - REDESIGN_ALICE_AGORA,
        - REFUND,
        - REGISTRATION,
        - SCHEDULED_APPOINTMENT_DETAIL,
        - SCORE_MAGENTA_INPUT,
        - SCORE_MAGENTA_RESULT,
        - SHOPPING,
        - SPECIALIST_DETAILS,
        - TASK_SCHEDULE,
        - TEST_PREPARATION,
        - TEST_REQUEST_UPLOAD,
        - TEST_RESULT,
        - TEST_RESULT_FEEDBACK,
        - TEST_RESULT_FEEDBACK_DETAILS,
        - WEBVIEW,
    RemoteActionType:
      type: string
      enum:
        - LOGOUT
    RemoteActionTransition:
      type: string
      enum:
        - REGULAR
        - REGULAR_FULL_SCREEN
        - MODAL
        - MODAL_WITH_HEADER
        - MODAL_WITH_FOOTER
        - MODAL_WITH_HEADER_AND_FOOTER
        - INPUT
        - INPUT_WIHOUT_CONFIRMATION_UNDISMISSABLE
        - INPUT_WIHOUT_CONFIRMATION
        - INPUT_WITHOUT_KEYBOARD
        - FADE
        - SLIDE
        - INVERSE_SLIDE
        - BOTTOM_SHEET
        - BOTTOM_SHEET_FIXED
        - INDICATOR
    Gender:
      type: string
      enum:
        - MALE
        - FEMALE
        - NON_BINARY
        - NO_ANSWER
    ConsolidatedAccreditedNetworkAppointmentType:
      type: string
      enum:
        - PRESENTIAL
        - REMOTE
    ProviderSectionType:
      type: string
      enum:
        - STANDARD
        - HIGHLIGHTS
    FavoriteInfoTransport:
      type: object
      properties:
        isFavorite:
          type: boolean
        action:
          $ref: '#/components/schemas/Navigation'
