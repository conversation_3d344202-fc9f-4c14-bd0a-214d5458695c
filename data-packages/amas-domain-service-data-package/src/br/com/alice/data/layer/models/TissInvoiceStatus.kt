package br.com.alice.data.layer.models

enum class TissInvoiceStatus(val description: String, val healthProfessionalDescription: String) {
    DRAFT("<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON> aberto"),
    RECEIVED("Recebido", "Disponível para revisão"),
    ANALYSIS("Em análise", "Em análise"),
    ERROR("Com erros", "Com erros"),

    @Deprecated("use WAITING_INVOICE instead")
    AUTHORIZED("Autorizado", "Autorizado"),
    REVISION_REQUESTED("Revisão solicitada", "Revisão solicitada"),
    WAITING_INVOICE("Aguardando NF", "Aguardando nota fiscal"),
    PAYMENT_ANALYSIS("Pagamento em análise", "Aguardando pagamento"),
    PAYMENT_ERROR("Pagamento com erro", "Pagamento com erro"),
    PAYMENT_DONE("Finalizado", "Pagamento concluído"),
    WAITING_RESEND_INVOICE("Aguardando reenvio de NF", "Aguardando reenvio de NF");
}
