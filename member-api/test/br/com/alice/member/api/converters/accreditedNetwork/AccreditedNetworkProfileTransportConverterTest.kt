package br.com.alice.member.api.converters.accreditedNetwork

import br.com.alice.app.content.model.ActionRouting
import br.com.alice.app.content.model.BackgroundColor
import br.com.alice.app.content.model.CalloutAction
import br.com.alice.app.content.model.CalloutVariant
import br.com.alice.app.content.model.RemoteAction
import br.com.alice.app.content.model.RemoteActionMethod
import br.com.alice.app.content.model.Tag
import br.com.alice.app.content.model.section.CalloutSection
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.ConsolidatedAccreditedNetworkType
import br.com.alice.member.api.builders.FavoriteInfoTransportBuilder
import br.com.alice.member.api.models.ContactCallOutResponse
import br.com.alice.member.api.models.MemberCardResponse
import br.com.alice.member.api.models.MemberCardType
import br.com.alice.member.api.models.MobileRouting
import br.com.alice.member.api.models.NavigationResponse
import br.com.alice.member.api.models.accreditedNetwork.AccreditedNetworkDetailsSection
import br.com.alice.member.api.models.accreditedNetwork.AccreditedNetworkProfileTransport
import br.com.alice.member.api.models.accreditedNetwork.ExpandableRatingCardDataTransport
import br.com.alice.member.api.models.accreditedNetwork.HeaderTransport
import br.com.alice.member.api.models.accreditedNetwork.InfoCardTransport
import br.com.alice.member.api.models.accreditedNetwork.MembershipCardTransport
import br.com.alice.member.api.models.accreditedNetwork.ProfileAction
import br.com.alice.member.api.models.accreditedNetwork.ProviderDetailsTransport
import br.com.alice.member.api.models.accreditedNetwork.RatingContentTransport
import br.com.alice.member.api.models.accreditedNetwork.ServiceLocation
import br.com.alice.member.api.models.accreditedNetwork.SpecialistDetailsTransport
import br.com.alice.member.api.models.accreditedNetwork.TabBarTransport
import br.com.alice.member.api.models.accreditedNetwork.TabContentItemTransport
import br.com.alice.member.api.models.accreditedNetwork.TabSelector
import br.com.alice.member.api.models.accreditedNetwork.TabTransport
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.TestInstance
import java.util.UUID
import kotlin.test.Test

class AccreditedNetworkProfileTransportExtensionsTest {
    val person = TestModelFactory.buildPerson()
    val product = TestModelFactory.buildProduct()

    @TestInstance(TestInstance.Lifecycle.PER_CLASS)
    @Nested
    inner class TestConvertProviderUnitResponse {
        @Test
        fun `ProviderDetailsTransport toAccreditedNetworkProfileTransport should convert basic fields correctly`() {
            val providerId = UUID.randomUUID()
            val providerType = ConsolidatedAccreditedNetworkType.CLINICAL_COMMUNITY
            val providerName = "Provider Name"
            val providerImageUrl = "https://example.com/image.jpg"
            val tag = Tag("TAG", "Blue")
            val description = "Provider description"

            val favoriteInfo = FavoriteInfoTransportBuilder.buildTransport(
                referenceId = providerId,
                isFavorite = true,
                favoriteId = UUID.randomUUID(),
            )

            val provider = ProviderDetailsTransport(
                id = providerId,
                type = providerType,
                title = "Test Provider",
                name = providerName,
                providerImageUrl = providerImageUrl,
                header = ProviderDetailsTransport.Header(
                    name = providerName,
                    providerImageUrl = providerImageUrl,
                    description = description,
                    tag = tag
                ),
                addresses = emptyList(),
                phoneNumbers = emptyList(),
                membershipCard = null,
                favoriteInfo = favoriteInfo
            )

            val expected = AccreditedNetworkProfileTransport(
                id = providerId,
                type = providerType,
                navbarTitle = "Test Provider",
                favoriteInfo = favoriteInfo,
                header = HeaderTransport(
                    imageUrl = providerImageUrl,
                    name = providerName,
                    description = description,
                    tag = tag
                ),
                tabBar = TabBarTransport(
                    selectedTabId = TabSelector.ABOUT.value,
                    tabs = emptyList()
                ),
                action = null
            )

            val result = provider.toAccreditedNetworkProfileTransport(null)

            assertThat(result).isEqualTo(expected)
        }

        @Test
        fun `ProviderDetailsTransport toAccreditedNetworkProfileTransport should create about tab when provider has information`() {
            val providerId = UUID.randomUUID()
            val providerName = "Provider Name"
            val providerImageUrl = "https://example.com/image.jpg"

            val infoCard = ProviderDetailsTransport.InfoCard(
                title = "Info Title",
                description = "Info Description",
                imageUrl = "https://example.com/info.jpg"
            )

            val providerInformation = ProviderDetailsTransport.ProviderInformation(
                imageUrl = "https://example.com/info.jpg",
                curiosity = null,
                education = listOf("Medical School", "Residency"),
                qualifications = listOf("Qualification 1", "Qualification 2")
            )

            val provider = ProviderDetailsTransport(
                id = providerId,
                type = ConsolidatedAccreditedNetworkType.CLINICAL_COMMUNITY,
                title = "Test Provider",
                name = providerName,
                header = ProviderDetailsTransport.Header(
                    name = providerName,
                    providerImageUrl = providerImageUrl,
                ),
                addresses = emptyList(),
                phoneNumbers = emptyList(),
                membershipCard = null,
                infoCard = infoCard,
                providerInformation = providerInformation
            )

            val aboutSections = buildList {
                providerInformation.education.takeIf { it.isNotEmpty() }?.let { education ->
                    add(
                        AccreditedNetworkDetailsSection(
                            title = "Educação",
                            content = listOf(
                                AccreditedNetworkDetailsSection.Content(
                                    text = education.joinToString(
                                        separator = "\n"
                                    )
                                )
                            ),
                            isExpandable = true,
                        )
                    )
                }
                providerInformation.qualifications.takeIf { it.isNotEmpty() }?.let { qualification ->
                    add(
                        AccreditedNetworkDetailsSection(
                            title = "Certificações e qualificações",
                            content = listOf(
                                AccreditedNetworkDetailsSection.Content(
                                    text = qualification.joinToString(
                                        separator = "\n"
                                    )
                                )
                            ),
                            isExpandable = false,
                        )
                    )
                }
            }

            val aboutTabContents = TabContentItemTransport(
                expandableRatingCard = null,
                expandableList = aboutSections,
                callout = null,
                infoCard = InfoCardTransport(
                    title = infoCard.title,
                    description = infoCard.description,
                    icon = "heart_outlined",
                ),
                contactList = null,
                membershipCard = null
            )

            val expected = AccreditedNetworkProfileTransport(
                id = providerId,
                type = ConsolidatedAccreditedNetworkType.CLINICAL_COMMUNITY,
                navbarTitle = "Test Provider",
                favoriteInfo = null,
                header = HeaderTransport(
                    imageUrl = providerImageUrl,
                    name = providerName,
                    description = null,
                    tag = null
                ),
                tabBar = TabBarTransport(
                    selectedTabId = TabSelector.ABOUT.value,
                    tabs = listOf(
                        TabTransport(
                            id = TabSelector.ABOUT.value,
                            title = "Sobre",
                            content = aboutTabContents
                        )
                    )
                ),
                action = null
            )

            val result = provider.toAccreditedNetworkProfileTransport(null)

            assertThat(result).isEqualTo(expected)
        }

        @Test
        fun `ProviderDetailsTransport toAccreditedNetworkProfileTransport should create contact tab with address and phone info`() {
            val providerId = UUID.randomUUID()
            val providerName = "Provider Name"
            val providerImageUrl = "https://example.com/image.jpg"

            val infoCard = ProviderDetailsTransport.InfoCard(
                title = "Info Title",
                description = "Info Description",
                imageUrl = "https://example.com/info.jpg"
            )

            val address = ProviderDetailsTransport.Address(
                label = "Office",
                address = "123 Main St, City",
                distance = "2km"
            )

            val phoneNumbers = listOf(
                ProviderDetailsTransport.PhoneNumber(
                    label = "Phone",
                    type = ProviderDetailsTransport.PhoneNumber.Type.PHONE,
                    phoneNumber = "**********",
                    phoneUrl = "tel:**********"
                ),
                ProviderDetailsTransport.PhoneNumber(
                    label = "WhatsApp",
                    type = ProviderDetailsTransport.PhoneNumber.Type.WHATSAPP,
                    phoneNumber = "**********",
                    phoneUrl = "https://wa.me/**********"
                )
            )

            val membershipCard = MemberCardResponse(
                number = person.nationalId,
                type = MemberCardType.ALICE,
                productDisplayName = product.title,
                displayName = product.displayName,
                complementName = product.complementName,
                ansNumber = "-",
            )

            val provider = ProviderDetailsTransport(
                id = providerId,
                type = ConsolidatedAccreditedNetworkType.HOSPITAL,
                title = "Test Provider",
                name = providerName,
                header = ProviderDetailsTransport.Header(
                    name = providerName,
                    providerImageUrl = providerImageUrl,
                ),
                addresses = listOf(address),
                phoneNumbers = phoneNumbers,
                membershipCard = membershipCard,
                infoCard = infoCard,
            )

            val serviceLocation = ServiceLocation(
                title = address.label,
                address = address.address,
                details = "${address.distance}",
                contacts = phoneNumbers.map { phone ->
                    ServiceLocation.ServiceContact(
                        text = phone.phoneNumber,
                        type = when (phone.type) {
                            ProviderDetailsTransport.PhoneNumber.Type.WHATSAPP -> ServiceLocation.ServiceContactType.WHATSAPP
                            ProviderDetailsTransport.PhoneNumber.Type.MOBILE -> ServiceLocation.ServiceContactType.MOBILE
                            ProviderDetailsTransport.PhoneNumber.Type.PHONE -> ServiceLocation.ServiceContactType.PHONE
                        },
                        url = phone.phoneUrl
                    )
                } + ServiceLocation.ServiceContact(
                    type = ServiceLocation.ServiceContactType.SHARE_BUTTON,
                    text = "Compartilhe sua carteirinha",
                    url = null,
                )
            )

            val contactTabContents = TabContentItemTransport(
                expandableRatingCard = null,
                expandableList = null,
                callout = null,
                infoCard = InfoCardTransport(
                    title = infoCard.title,
                    description = infoCard.description,
                    icon = "heart_outlined",
                ),
                contactList = listOf(serviceLocation),
                membershipCard = MembershipCardTransport(
                    title = "Para atendimento, use a carteirinha Alice:",
                    data = membershipCard
                )
            )

            val expected = AccreditedNetworkProfileTransport(
                id = providerId,
                type = ConsolidatedAccreditedNetworkType.HOSPITAL,
                navbarTitle = "Test Provider",
                favoriteInfo = null,
                header = HeaderTransport(
                    imageUrl = providerImageUrl,
                    name = providerName,
                    description = null,
                    tag = null
                ),
                tabBar = TabBarTransport(
                    selectedTabId = TabSelector.CONTACT.value,
                    tabs = listOf(
                        TabTransport(
                            id = TabSelector.CONTACT.value,
                            title = "Contato",
                            content = contactTabContents
                        )
                    )
                ),
                action = null
            )

            val result = provider.toAccreditedNetworkProfileTransport(null)

            assertThat(result).isEqualTo(expected)
        }

        @Test
        fun `ProviderDetailsTransport toAccreditedNetworkProfileTransport should create both about and contact tabs when all info is available`() {
            val providerId = UUID.randomUUID()
            val providerName = "Provider Name"
            val providerImageUrl = "https://example.com/image.jpg"

            val address = ProviderDetailsTransport.Address(
                label = "Office",
                address = "123 Main St, City",
                distance = "2km"
            )

            val phoneNumber = ProviderDetailsTransport.PhoneNumber(
                label = "Phone",
                type = ProviderDetailsTransport.PhoneNumber.Type.PHONE,
                phoneNumber = "**********",
                phoneUrl = "tel:**********"
            )

            val membershipCard = MemberCardResponse(
                number = person.nationalId,
                type = MemberCardType.ALICE,
                productDisplayName = product.title,
                displayName = product.displayName,
                complementName = product.complementName,
                ansNumber = "-",
            )

            val infoCard = ProviderDetailsTransport.InfoCard(
                title = "Info Title",
                description = "Info Description",
                imageUrl = "https://example.com/info.jpg"
            )

            val providerInformation = ProviderDetailsTransport.ProviderInformation(
                imageUrl = "https://example.com/info.jpg",
                curiosity = "Interesting fact about the provider",
                education = listOf("Medical School"),
                qualifications = listOf("Qualification 1")
            )

            val provider = ProviderDetailsTransport(
                id = providerId,
                type = ConsolidatedAccreditedNetworkType.CLINICAL_COMMUNITY,
                title = "Test Provider",
                name = providerName,
                header = ProviderDetailsTransport.Header(
                    name = providerName,
                    providerImageUrl = providerImageUrl,
                ),
                addresses = listOf(address),
                phoneNumbers = listOf(phoneNumber),
                membershipCard = membershipCard,
                infoCard = infoCard,
                providerInformation = providerInformation,
                subSpecialties = emptyList(),
            )

            val aboutSections = mutableListOf<AccreditedNetworkDetailsSection>()
            providerInformation.education.forEach { education ->
                aboutSections.add(
                    AccreditedNetworkDetailsSection(
                        title = "Educação",
                        content = listOf(AccreditedNetworkDetailsSection.Content(text = education)),
                        isExpandable = true,
                    )
                )
            }
            providerInformation.qualifications.forEach { qualification ->
                aboutSections.add(
                    AccreditedNetworkDetailsSection(
                        title = "Certificações e qualificações",
                        content = listOf(AccreditedNetworkDetailsSection.Content(text = qualification)),
                        isExpandable = false,
                    )
                )
            }
            aboutSections.add(
                AccreditedNetworkDetailsSection(
                    title = "Curiosidades",
                    content = listOf(AccreditedNetworkDetailsSection.Content(text = providerInformation.curiosity!!)),
                    isExpandable = true,
                )
            )

            val aboutTabContents = TabContentItemTransport(
                expandableRatingCard = null,
                expandableList = aboutSections,
                callout = null,
                infoCard = InfoCardTransport(
                    title = infoCard.title,
                    description = infoCard.description,
                    icon = "heart_outlined",
                ),
                contactList = null,
                membershipCard = null
            )

            val serviceLocation = ServiceLocation(
                title = address.label,
                address = address.address,
                details = "${address.distance}",
                contacts = listOf(
                    ServiceLocation.ServiceContact(
                        text = phoneNumber.phoneNumber,
                        type = ServiceLocation.ServiceContactType.PHONE,
                        url = phoneNumber.phoneUrl
                    ),
                    ServiceLocation.ServiceContact(
                        type = ServiceLocation.ServiceContactType.SHARE_BUTTON,
                        text = "Compartilhe sua carteirinha",
                        url = null,
                    )
                )
            )

            val contactTabContents = TabContentItemTransport(
                expandableRatingCard = null,
                expandableList = null,
                callout = null,
                infoCard = null,
                contactList = listOf(serviceLocation),
                membershipCard = MembershipCardTransport(
                    title = "Para atendimento, use a carteirinha Alice:",
                    data = membershipCard
                )
            )

            val expected = AccreditedNetworkProfileTransport(
                id = providerId,
                type = ConsolidatedAccreditedNetworkType.CLINICAL_COMMUNITY,
                navbarTitle = "Test Provider",
                favoriteInfo = null,
                header = HeaderTransport(
                    imageUrl = providerImageUrl,
                    name = providerName,
                    description = null,
                    tag = null
                ),
                tabBar = TabBarTransport(
                    selectedTabId = TabSelector.ABOUT.value,
                    tabs = listOf(
                        TabTransport(
                            id = TabSelector.ABOUT.value,
                            title = "Sobre",
                            content = aboutTabContents
                        ),
                        TabTransport(
                            id = TabSelector.CONTACT.value,
                            title = "Agendamento",
                            content = contactTabContents
                        )
                    )
                ),
                action = null
            )

            val result = provider.toAccreditedNetworkProfileTransport(null)

            assertThat(result).isEqualTo(expected)
        }

        @Test
        fun `ProviderDetailsTransport toAccreditedNetworkProfileTransport should include specialty and subspecialties in about tab`() {
            val providerId = UUID.randomUUID()
            val providerName = "Provider Name"
            val providerImageUrl = "https://example.com/image.jpg"
            val specialty = "Cardiology"
            val subSpecialties = listOf("Interventional Cardiology", "Echocardiography")

            val provider = ProviderDetailsTransport(
                id = providerId,
                type = ConsolidatedAccreditedNetworkType.CLINICAL_COMMUNITY,
                title = "Test Provider",
                name = providerName,
                header = ProviderDetailsTransport.Header(
                    name = providerName,
                    providerImageUrl = providerImageUrl,
                ),
                specialty = specialty,
                subSpecialties = subSpecialties,
                addresses = emptyList(),
                phoneNumbers = emptyList(),
                membershipCard = null,
                favoriteInfo = null
            )

            val aboutTabContents = TabContentItemTransport(
                expandableRatingCard = null,
                expandableList = listOf(
                    AccreditedNetworkDetailsSection(
                        title = "Especialidade",
                        content = listOf(AccreditedNetworkDetailsSection.Content(text = specialty)),
                        isExpandable = false
                    ),
                    AccreditedNetworkDetailsSection(
                        title = "Subespecialidade",
                        content = listOf(AccreditedNetworkDetailsSection.Content(text = subSpecialties.joinToString()))
                    )
                ),
                callout = null,
                infoCard = null,
                contactList = null,
                membershipCard = null
            )

            val expected = AccreditedNetworkProfileTransport(
                id = providerId,
                type = ConsolidatedAccreditedNetworkType.CLINICAL_COMMUNITY,
                navbarTitle = "Test Provider",
                favoriteInfo = null,
                header = HeaderTransport(
                    imageUrl = providerImageUrl,
                    name = providerName,
                    description = null,
                    tag = null
                ),
                tabBar = TabBarTransport(
                    selectedTabId = TabSelector.ABOUT.value,
                    tabs = listOf(
                        TabTransport(
                            id = TabSelector.ABOUT.value,
                            title = "Sobre",
                            content = aboutTabContents
                        )
                    )
                ),
                action = null
            )

            val result = provider.toAccreditedNetworkProfileTransport(null)

            assertThat(result).isEqualTo(expected)
        }

        @Test
        fun `ProviderDetailsTransport toAccreditedNetworkProfileTransport should include action navigation`() {
            val providerId = UUID.randomUUID()
            val physicianStaffId = UUID.randomUUID()
            val providerName = "Provider Name"
            val providerImageUrl = "https://example.com/image.jpg"

            val action = ProviderDetailsTransport.ActionNavigation(
                label = "Schedule Appointment",
                icon = "calendar",
                navigation = NavigationResponse(
                    mobileRoute = MobileRouting.ACCREDITED_NETWORK_PROFILE,
                    properties = mapOf(
                        "method" to RemoteActionMethod.GET,
                        "endpoint" to "/accredited_network/profile/${physicianStaffId}"
                    )
                )
            )

            val provider = ProviderDetailsTransport(
                id = providerId,
                type = ConsolidatedAccreditedNetworkType.CLINICAL_COMMUNITY,
                title = "Test Provider",
                name = providerName,
                header = ProviderDetailsTransport.Header(
                    name = providerName,
                    providerImageUrl = providerImageUrl,
                ),
                addresses = emptyList(),
                phoneNumbers = emptyList(),
                membershipCard = null,
                favoriteInfo = null,
                action = action
            )

            val expected = AccreditedNetworkProfileTransport(
                id = providerId,
                type = ConsolidatedAccreditedNetworkType.CLINICAL_COMMUNITY,
                navbarTitle = "Test Provider",
                favoriteInfo = null,
                header = HeaderTransport(
                    imageUrl = providerImageUrl,
                    name = providerName,
                    description = null,
                    tag = null
                ),
                tabBar = TabBarTransport(
                    selectedTabId = TabSelector.ABOUT.value,
                    tabs = emptyList()
                ),
                action = ProfileAction(
                    label = action.label,
                    icon = action.icon,
                    navigation = action.navigation
                )
            )

            val result = provider.toAccreditedNetworkProfileTransport(null)

            assertThat(result).isEqualTo(expected)
        }

        @Test
        fun `ProviderDetailsTransport toAccreditedNetworkProfileTransport should handle Cassi membership card`() {
            val providerId = UUID.randomUUID()
            val providerName = "Provider Name"
            val providerImageUrl = "https://example.com/image.jpg"

            val membershipCard = MemberCardResponse(
                number = person.nationalId,
                type = MemberCardType.CASSI,
                productDisplayName = product.title,
                displayName = product.displayName,
                complementName = product.complementName,
                ansNumber = "-",
            )

            val provider = ProviderDetailsTransport(
                id = providerId,
                type = ConsolidatedAccreditedNetworkType.CLINICAL_COMMUNITY,
                title = "Test Provider",
                name = providerName,
                header = ProviderDetailsTransport.Header(
                    name = providerName,
                    providerImageUrl = providerImageUrl,
                ),
                addresses = emptyList(),
                phoneNumbers = emptyList(),
                membershipCard = membershipCard,
                favoriteInfo = null
            )

            val contactTabContents = TabContentItemTransport(
                expandableRatingCard = null,
                expandableList = null,
                callout = null,
                infoCard = null,
                contactList = null,
                membershipCard = MembershipCardTransport(
                    title = "Para atendimento, use a carteirinha Cassi:",
                    data = membershipCard
                )
            )

            val expected = AccreditedNetworkProfileTransport(
                id = providerId,
                type = ConsolidatedAccreditedNetworkType.CLINICAL_COMMUNITY,
                navbarTitle = "Test Provider",
                favoriteInfo = null,
                header = HeaderTransport(
                    imageUrl = providerImageUrl,
                    name = providerName,
                    description = null,
                    tag = null
                ),
                tabBar = TabBarTransport(
                    selectedTabId = TabSelector.CONTACT.value,
                    tabs = listOf(
                        TabTransport(
                            id = TabSelector.CONTACT.value,
                            title = "Agendamento",
                            content = contactTabContents
                        )
                    )
                ),
                action = null
            )

            val result = provider.toAccreditedNetworkProfileTransport(null)

            assertThat(result).isEqualTo(expected)
        }

        @Test
        fun `ProviderDetailsTransport toAccreditedNetworkProfileTransport should transform disclaimer to callout in contact tab`() {
            val providerId = UUID.randomUUID()
            val providerName = "Provider Name"
            val providerImageUrl = "https://example.com/image.jpg"

            val disclaimer = ContactCallOutResponse(
                title = "Important Notice",
                body = "This provider requires advance scheduling",
                variant = ContactCallOutResponse.Variant.WARNING,
                action = ContactCallOutResponse.Action(
                    label = "Learn More",
                    onClickAction = RemoteAction(
                        mobileRoute = ActionRouting.ALICE_AGORA,
                    )
                )
            )

            val provider = ProviderDetailsTransport(
                id = providerId,
                type = ConsolidatedAccreditedNetworkType.CLINICAL_COMMUNITY,
                title = "Test Provider",
                name = providerName,
                header = ProviderDetailsTransport.Header(
                    name = providerName,
                    providerImageUrl = providerImageUrl,
                ),
                addresses = emptyList(),
                phoneNumbers = emptyList(),
                membershipCard = null,
                favoriteInfo = null,
                disclaimer = disclaimer
            )

            val contactTabContents = TabContentItemTransport(
                expandableRatingCard = null,
                expandableList = null,
                callout = CalloutSection(
                    title = disclaimer.title,
                    calloutBody = disclaimer.body,
                    calloutVariant = CalloutVariant.WARNING,
                    calloutAction = CalloutAction(
                        type = disclaimer.action?.type,
                        label = disclaimer.action?.label,
                        onClickAction = disclaimer.action?.onClickAction
                    )
                ),
                infoCard = null,
                contactList = null,
                membershipCard = null
            )

            val expected = AccreditedNetworkProfileTransport(
                id = providerId,
                type = ConsolidatedAccreditedNetworkType.CLINICAL_COMMUNITY,
                navbarTitle = "Test Provider",
                favoriteInfo = null,
                header = HeaderTransport(
                    imageUrl = providerImageUrl,
                    name = providerName,
                    description = null,
                    tag = null
                ),
                tabBar = TabBarTransport(
                    selectedTabId = TabSelector.CONTACT.value,
                    tabs = listOf(
                        TabTransport(
                            id = TabSelector.CONTACT.value,
                            title = "Agendamento",
                            content = contactTabContents
                        )
                    )
                ),
                action = null
            )

            val result = provider.toAccreditedNetworkProfileTransport(null)

            assertThat(result).isEqualTo(expected)
        }

        @Test
        fun `ProviderDetailsTransport toAccreditedNetworkProfileTransport should handle null provider information`() {
            val providerId = UUID.randomUUID()
            val providerName = "Provider Name"
            val providerImageUrl = "https://example.com/image.jpg"

            val infoCard = ProviderDetailsTransport.InfoCard(
                title = "Info Title",
                description = "Info Description",
                imageUrl = "https://example.com/info.jpg"
            )

            val provider = ProviderDetailsTransport(
                id = providerId,
                type = ConsolidatedAccreditedNetworkType.CLINICAL_COMMUNITY,
                title = "Test Provider",
                name = providerName,
                header = ProviderDetailsTransport.Header(
                    name = providerName,
                    providerImageUrl = providerImageUrl,
                ),
                addresses = emptyList(),
                phoneNumbers = emptyList(),
                membershipCard = null,
                infoCard = infoCard,
                providerInformation = null
            )

            val contactTabContents = TabContentItemTransport(
                expandableRatingCard = null,
                expandableList = null,
                callout = null,
                infoCard = InfoCardTransport(
                    title = infoCard.title,
                    description = infoCard.description,
                    icon = "heart_outlined",
                ),
                contactList = null,
                membershipCard = null
            )

            val expected = AccreditedNetworkProfileTransport(
                id = providerId,
                type = ConsolidatedAccreditedNetworkType.CLINICAL_COMMUNITY,
                navbarTitle = "Test Provider",
                favoriteInfo = null,
                header = HeaderTransport(
                    imageUrl = providerImageUrl,
                    name = providerName,
                    description = null,
                    tag = null
                ),
                tabBar = TabBarTransport(
                    selectedTabId = TabSelector.CONTACT.value,
                    tabs = listOf(
                        TabTransport(
                            id = TabSelector.CONTACT.value,
                            title = "Agendamento",
                            content = contactTabContents
                        )
                    )
                ),
                action = null
            )

            val result = provider.toAccreditedNetworkProfileTransport(null)

            assertThat(result).isEqualTo(expected)
        }

        @Test
        fun `ProviderDetailsTransport toAccreditedNetworkProfileTransport should handle empty education and qualifications`() {
            val providerId = UUID.randomUUID()
            val providerName = "Provider Name"
            val providerImageUrl = "https://example.com/image.jpg"

            val providerInformation = ProviderDetailsTransport.ProviderInformation(
                imageUrl = "https://example.com/info.jpg",
                curiosity = "Interesting fact about the provider",
                education = emptyList(),
                qualifications = emptyList()
            )

            val provider = ProviderDetailsTransport(
                id = providerId,
                type = ConsolidatedAccreditedNetworkType.CLINICAL_COMMUNITY,
                title = "Test Provider",
                name = providerName,
                header = ProviderDetailsTransport.Header(
                    name = providerName,
                    providerImageUrl = providerImageUrl,
                ),
                addresses = emptyList(),
                phoneNumbers = emptyList(),
                membershipCard = null,
                providerInformation = providerInformation
            )

            val aboutTabContents = TabContentItemTransport(
                expandableRatingCard = null,
                expandableList = listOf(
                    AccreditedNetworkDetailsSection(
                        title = "Curiosidades",
                        content = listOf(AccreditedNetworkDetailsSection.Content(text = providerInformation.curiosity!!)),
                        isExpandable = true
                    )
                ),
                callout = null,
                infoCard = null,
                contactList = null,
                membershipCard = null
            )

            val expected = AccreditedNetworkProfileTransport(
                id = providerId,
                type = ConsolidatedAccreditedNetworkType.CLINICAL_COMMUNITY,
                navbarTitle = "Test Provider",
                favoriteInfo = null,
                header = HeaderTransport(
                    imageUrl = providerImageUrl,
                    name = providerName,
                    description = null,
                    tag = null
                ),
                tabBar = TabBarTransport(
                    selectedTabId = TabSelector.ABOUT.value,
                    tabs = listOf(
                        TabTransport(
                            id = TabSelector.ABOUT.value,
                            title = "Sobre",
                            content = aboutTabContents
                        )
                    )
                ),
                action = null
            )

            val result = provider.toAccreditedNetworkProfileTransport(null)

            assertThat(result).isEqualTo(expected)
        }
    }

    @TestInstance(TestInstance.Lifecycle.PER_CLASS)
    @Nested
    inner class TestConvertSpecialistResponse {
        @Test
        fun `SpecialistDetailsTransport toAccreditedNetworkProfileTransport should convert basic fields correctly`() {
            val specialistId = UUID.randomUUID()
            val specialistType = ConsolidatedAccreditedNetworkType.HEALTH_PROFESSIONAL
            val specialistName = "Dr. Specialist"
            val specialistImageUrl = "https://example.com/specialist.jpg"
            val specialistCrm = "CRM 12345"
            val tag = Tag("SPECIALITY", "Green")

            val favoriteInfo = FavoriteInfoTransportBuilder.buildTransport(
                referenceId = specialistId,
                isFavorite = true,
                favoriteId = UUID.randomUUID(),
            )

            val specialist = SpecialistDetailsTransport(
                id = specialistId,
                providerType = specialistType,
                title = "Specialist Title",
                name = specialistName,
                imageUrl = specialistImageUrl,
                crm = specialistCrm,
                tag = tag,
                services = emptyList(),
                subspecialties = emptyList(),
                sections = emptyList(),
                showMembershipCardShareButton = false,
                favoriteInfo = favoriteInfo
            )

            val expected = AccreditedNetworkProfileTransport(
                id = specialistId,
                type = specialistType,
                navbarTitle = "Specialist Title",
                favoriteInfo = favoriteInfo,
                header = HeaderTransport(
                    imageUrl = specialistImageUrl,
                    name = specialistName,
                    description = specialistCrm,
                    tag = tag
                ),
                tabBar = TabBarTransport(
                    selectedTabId = TabSelector.ABOUT.value,
                    tabs = emptyList()
                ),
                action = null
            )

            val result = specialist.toAccreditedNetworkProfileTransport(null)

            assertThat(result).isEqualTo(expected)
        }

        @Test
        fun `SpecialistDetailsTransport toAccreditedNetworkProfileTransport should create about tab with rating and subspecialties`() {
            val specialistId = UUID.randomUUID()
            val specialistName = "Dr. Specialist"
            val specialistImageUrl = "https://example.com/specialist.jpg"
            val specialistCrm = "CRM 12345"

            val rating = SpecialistDetailsTransport.SpecialistRating(
                backgroundColor = BackgroundColor.BLUE,
                aiGeneratedComment = "Excellent professional",
                appointmentCount = 50,
                evaluationsCount = 45,
                trustScore = 4.8,
                recommendationScore = 4.7
            )

            val subspecialties = listOf("Cardiology", "Sports Medicine")

            val section = AccreditedNetworkDetailsSection(
                title = "Experience",
                content = listOf(
                    AccreditedNetworkDetailsSection.Content(text = "10 years of experience")
                )
            )

            val callout = CalloutSection(
                title = "Subespecialidade incorreta",
                calloutBody = "Não perca viagem! Esse profissional pode não atender a demanda específica do seu encaminhamento.",
                calloutVariant = CalloutVariant.TUTORIAL,
            )

            val specialist = SpecialistDetailsTransport(
                id = specialistId,
                providerType = ConsolidatedAccreditedNetworkType.HEALTH_PROFESSIONAL,
                title = "Specialist Title",
                name = specialistName,
                imageUrl = specialistImageUrl,
                crm = specialistCrm,
                rating = rating,
                subspecialties = subspecialties,
                sections = listOf(section),
                services = emptyList(),
                showMembershipCardShareButton = false,
                favoriteInfo = null,
                callout = callout
            )

            val aboutTabContents = TabContentItemTransport(
                expandableRatingCard = ExpandableRatingCardDataTransport(
                    backgroundColor = BackgroundColor.BLUE,
                    ratingContent = RatingContentTransport(
                        aiGeneratedComment = "Excellent professional",
                        appointmentCount = 50,
                        evaluationsCount = 45,
                        trustScore = 4.8,
                        recommendationScore = 4.7
                    ),
                    emptyRatingComment = null
                ),
                expandableList = listOf(
                    AccreditedNetworkDetailsSection(
                        title = "Subespecialidades",
                        icon = "doctor",
                        content = listOf(
                            AccreditedNetworkDetailsSection.Content(
                                text = "Cardiology, Sports Medicine",
                            )
                        ),
                        isExpandable = true,
                    ),
                    section
                ),
                callout = null,
                infoCard = null,
                contactList = null,
                membershipCard = null
            )

            val expected = AccreditedNetworkProfileTransport(
                id = specialistId,
                type = ConsolidatedAccreditedNetworkType.HEALTH_PROFESSIONAL,
                navbarTitle = "Specialist Title",
                favoriteInfo = null,
                header = HeaderTransport(
                    imageUrl = specialistImageUrl,
                    name = specialistName,
                    description = specialistCrm,
                    tag = null
                ),
                tabBar = TabBarTransport(
                    selectedTabId = TabSelector.ABOUT.value,
                    tabs = listOf(
                        TabTransport(
                            id = TabSelector.ABOUT.value,
                            title = "Sobre",
                            content = aboutTabContents
                        )
                    )
                ),
                action = null
            )

            val result = specialist.toAccreditedNetworkProfileTransport(null)

            assertThat(result).isEqualTo(expected)
        }

        @Test
        fun `SpecialistDetailsTransport toAccreditedNetworkProfileTransport should create contacts tab with services and membership card`() {
            val specialistId = UUID.randomUUID()
            val specialistName = "Dr. Specialist"
            val specialistImageUrl = "https://example.com/specialist.jpg"
            val specialistCrm = "CRM 12345"

            val service = ServiceLocation(
                title = "Main Office",
                address = "123 Main St, City",
                details = "2km away",
                contacts = listOf(
                    ServiceLocation.ServiceContact(
                        type = ServiceLocation.ServiceContactType.PHONE,
                        text = "**********",
                        url = "tel:**********"
                    ),
                )
            )

            val membershipCard = MemberCardResponse(
                number = person.nationalId,
                type = MemberCardType.ALICE,
                productDisplayName = product.title,
                displayName = product.displayName,
                complementName = product.complementName,
                ansNumber = "-",
            )

            val specialist = SpecialistDetailsTransport(
                id = specialistId,
                providerType = ConsolidatedAccreditedNetworkType.HEALTH_PROFESSIONAL,
                title = "Specialist Title",
                name = specialistName,
                imageUrl = specialistImageUrl,
                crm = specialistCrm,
                services = listOf(service),
                membershipCard = membershipCard,
                subspecialties = emptyList(),
                sections = emptyList(),
                showMembershipCardShareButton = true,
                favoriteInfo = null
            )

            val contactTabContents = TabContentItemTransport(
                expandableRatingCard = null,
                expandableList = null,
                callout = null,
                infoCard = null,
                contactList = listOf(
                    service.copy(
                        contacts = service.contacts + ServiceLocation.ServiceContact(
                            type = ServiceLocation.ServiceContactType.SHARE_BUTTON,
                            text = "Compartilhe sua carteirinha",
                            url = null,
                        )
                    )
                ),
                membershipCard = MembershipCardTransport(
                    title = "Para atendimento, use a carteirinha Alice:",
                    data = membershipCard
                )
            )

            val expected = AccreditedNetworkProfileTransport(
                id = specialistId,
                type = ConsolidatedAccreditedNetworkType.HEALTH_PROFESSIONAL,
                navbarTitle = "Specialist Title",
                favoriteInfo = null,
                header = HeaderTransport(
                    imageUrl = specialistImageUrl,
                    name = specialistName,
                    description = specialistCrm,
                    tag = null
                ),
                tabBar = TabBarTransport(
                    selectedTabId = TabSelector.CONTACT.value,
                    tabs = listOf(
                        TabTransport(
                            id = TabSelector.CONTACT.value,
                            title = "Agendamento",
                            content = contactTabContents
                        )
                    )
                ),
                action = null
            )

            val result = specialist.toAccreditedNetworkProfileTransport(null)

            assertThat(result).isEqualTo(expected)
        }

        @Test
        fun `SpecialistDetailsTransport toAccreditedNetworkProfileTransport should create both about and contacts tabs when all info is available`() {
            val specialistId = UUID.randomUUID()
            val specialistName = "Dr. Specialist"
            val specialistImageUrl = "https://example.com/specialist.jpg"
            val specialistCrm = "CRM 12345"

            val rating = SpecialistDetailsTransport.SpecialistRating(
                backgroundColor = BackgroundColor.BLUE,
                aiGeneratedComment = "Excellent professional",
                appointmentCount = 50,
                evaluationsCount = 45,
                trustScore = 4.8,
                recommendationScore = 4.7
            )

            val subspecialties = listOf("Cardiology")

            val section = AccreditedNetworkDetailsSection(
                title = "Experience",
                content = listOf(
                    AccreditedNetworkDetailsSection.Content(text = "10 years of experience")
                )
            )

            val service = ServiceLocation(
                title = "Main Office",
                address = "123 Main St, City",
                details = "2km away",
                contacts = listOf(
                    ServiceLocation.ServiceContact(
                        text = "**********",
                        type = ServiceLocation.ServiceContactType.PHONE,
                        url = "tel:**********"
                    ),
                    ServiceLocation.ServiceContact(
                        type = ServiceLocation.ServiceContactType.SHARE_BUTTON,
                        text = "Compartilhe sua carteirinha",
                        url = null,
                    ),
                )
            )

            val membershipCard = MemberCardResponse(
                number = person.nationalId,
                type = MemberCardType.ALICE,
                productDisplayName = product.title,
                displayName = product.displayName,
                complementName = product.complementName,
                ansNumber = "-",
            )

            val specialist = SpecialistDetailsTransport(
                id = specialistId,
                providerType = ConsolidatedAccreditedNetworkType.HEALTH_PROFESSIONAL,
                title = "Specialist Title",
                name = specialistName,
                imageUrl = specialistImageUrl,
                crm = specialistCrm,
                rating = rating,
                subspecialties = subspecialties,
                sections = listOf(section),
                services = listOf(service),
                membershipCard = membershipCard,
                showMembershipCardShareButton = true,
                favoriteInfo = null
            )

            val aboutTabContents = TabContentItemTransport(
                expandableRatingCard = ExpandableRatingCardDataTransport(
                    backgroundColor = BackgroundColor.BLUE,
                    ratingContent = RatingContentTransport(
                        aiGeneratedComment = "Excellent professional",
                        appointmentCount = 50,
                        evaluationsCount = 45,
                        trustScore = 4.8,
                        recommendationScore = 4.7
                    ),
                    emptyRatingComment = null
                ),
                expandableList = listOf(
                    AccreditedNetworkDetailsSection(
                        title = "Subespecialidades",
                        icon = "doctor",
                        content = listOf(
                            AccreditedNetworkDetailsSection.Content(
                                text = "Cardiology",
                            )
                        ),
                        isExpandable = true,
                    ),
                    section
                ),
                callout = null,
                infoCard = null,
                contactList = null,
                membershipCard = null
            )

            val contactTabContents = TabContentItemTransport(
                expandableRatingCard = null,
                expandableList = null,
                callout = null,
                infoCard = null,
                contactList = listOf(
                    service.copy(
                        contacts = service.contacts + listOf(
                            ServiceLocation.ServiceContact(
                                type = ServiceLocation.ServiceContactType.SHARE_BUTTON,
                                text = "Compartilhe sua carteirinha",
                                url = null,
                            )
                        )
                    )
                ),
                membershipCard = MembershipCardTransport(
                    title = "Para atendimento, use a carteirinha Alice:",
                    data = membershipCard
                )
            )

            val expected = AccreditedNetworkProfileTransport(
                id = specialistId,
                type = ConsolidatedAccreditedNetworkType.HEALTH_PROFESSIONAL,
                navbarTitle = "Specialist Title",
                favoriteInfo = null,
                header = HeaderTransport(
                    imageUrl = specialistImageUrl,
                    name = specialistName,
                    description = specialistCrm,
                    tag = null
                ),
                tabBar = TabBarTransport(
                    selectedTabId = TabSelector.ABOUT.value,
                    tabs = listOf(
                        TabTransport(
                            id = TabSelector.ABOUT.value,
                            title = "Sobre",
                            content = aboutTabContents
                        ),
                        TabTransport(
                            id = TabSelector.CONTACT.value,
                            title = "Agendamento",
                            content = contactTabContents
                        )
                    )
                ),
                action = null
            )

            val result = specialist.toAccreditedNetworkProfileTransport(null)

            assertThat(result).isEqualTo(expected)
        }

        @Test
        fun `SpecialistDetailsTransport toAccreditedNetworkProfileTransport should handle empty rating with emptyRating field`() {
            val specialistId = UUID.randomUUID()
            val specialistName = "Dr. Specialist"
            val specialistImageUrl = "https://example.com/specialist.jpg"
            val specialistCrm = "CRM 12345"

            val emptyRating = SpecialistDetailsTransport.EmptySpecialistRating(
                value = "Not enough evaluations yet",
                backgroundColor = BackgroundColor.BACKGROUND_DEFAULT
            )

            val specialist = SpecialistDetailsTransport(
                id = specialistId,
                providerType = ConsolidatedAccreditedNetworkType.HEALTH_PROFESSIONAL,
                title = "Specialist Title",
                name = specialistName,
                imageUrl = specialistImageUrl,
                crm = specialistCrm,
                emptyRating = emptyRating,
                subspecialties = emptyList(),
                sections = emptyList(),
                services = emptyList(),
                showMembershipCardShareButton = false,
                favoriteInfo = null
            )

            val aboutTabContents = TabContentItemTransport(
                expandableRatingCard = ExpandableRatingCardDataTransport(
                    backgroundColor = BackgroundColor.BACKGROUND_DEFAULT,
                    ratingContent = null,
                    emptyRatingComment = "Not enough evaluations yet"
                ),
                expandableList = null,
                callout = null,
                infoCard = null,
                contactList = null,
                membershipCard = null
            )

            val expected = AccreditedNetworkProfileTransport(
                id = specialistId,
                type = ConsolidatedAccreditedNetworkType.HEALTH_PROFESSIONAL,
                navbarTitle = "Specialist Title",
                favoriteInfo = null,
                header = HeaderTransport(
                    imageUrl = specialistImageUrl,
                    name = specialistName,
                    description = specialistCrm,
                    tag = null
                ),
                tabBar = TabBarTransport(
                    selectedTabId = TabSelector.ABOUT.value,
                    tabs = listOf(
                        TabTransport(
                            id = TabSelector.ABOUT.value,
                            title = "Sobre",
                            content = aboutTabContents
                        )
                    )
                ),
                action = null
            )

            val result = specialist.toAccreditedNetworkProfileTransport(null)

            assertThat(result).isEqualTo(expected)
        }

        @Test
        fun `SpecialistDetailsTransport toAccreditedNetworkProfileTransport should include action navigation`() {
            val specialistId = UUID.randomUUID()
            val physicianStaffId = UUID.randomUUID()
            val specialistName = "Dr. Specialist"
            val specialistImageUrl = "https://example.com/specialist.jpg"
            val specialistCrm = "CRM 12345"

            val action = SpecialistDetailsTransport.Action(
                label = "Schedule Appointment",
                icon = "calendar",
                navigation = NavigationResponse(
                    mobileRoute = MobileRouting.ACCREDITED_NETWORK_PROFILE,
                    properties = mapOf(
                        "method" to RemoteActionMethod.GET,
                        "endpoint" to "/accredited_network/profile/${physicianStaffId}"
                    )
                )
            )

            val specialist = SpecialistDetailsTransport(
                id = specialistId,
                providerType = ConsolidatedAccreditedNetworkType.HEALTH_PROFESSIONAL,
                title = "Specialist Title",
                name = specialistName,
                imageUrl = specialistImageUrl,
                crm = specialistCrm,
                services = emptyList(),
                subspecialties = emptyList(),
                sections = emptyList(),
                showMembershipCardShareButton = false,
                favoriteInfo = null,
                action = action
            )

            val expected = AccreditedNetworkProfileTransport(
                id = specialistId,
                type = ConsolidatedAccreditedNetworkType.HEALTH_PROFESSIONAL,
                navbarTitle = "Specialist Title",
                favoriteInfo = null,
                header = HeaderTransport(
                    imageUrl = specialistImageUrl,
                    name = specialistName,
                    description = specialistCrm,
                    tag = null
                ),
                tabBar = TabBarTransport(
                    selectedTabId = TabSelector.ABOUT.value,
                    tabs = emptyList()
                ),
                action = ProfileAction(
                    label = action.label,
                    icon = action.icon,
                    navigation = action.navigation
                )
            )

            val result = specialist.toAccreditedNetworkProfileTransport(null)

            assertThat(result).isEqualTo(expected)
        }

        @Test
        fun `SpecialistDetailsTransport toAccreditedNetworkProfileTransport should handle null rating and emptyRating`() {
            val specialistId = UUID.randomUUID()
            val specialistName = "Dr. Specialist"
            val specialistImageUrl = "https://example.com/specialist.jpg"
            val specialistCrm = "CRM 12345"

            val specialist = SpecialistDetailsTransport(
                id = specialistId,
                providerType = ConsolidatedAccreditedNetworkType.HEALTH_PROFESSIONAL,
                title = "Specialist Title",
                name = specialistName,
                imageUrl = specialistImageUrl,
                crm = specialistCrm,
                rating = null,
                emptyRating = null,
                subspecialties = emptyList(),
                sections = emptyList(),
                services = emptyList(),
                showMembershipCardShareButton = false,
                favoriteInfo = null
            )

            val expected = AccreditedNetworkProfileTransport(
                id = specialistId,
                type = ConsolidatedAccreditedNetworkType.HEALTH_PROFESSIONAL,
                navbarTitle = "Specialist Title",
                favoriteInfo = null,
                header = HeaderTransport(
                    imageUrl = specialistImageUrl,
                    name = specialistName,
                    description = specialistCrm,
                    tag = null
                ),
                tabBar = TabBarTransport(
                    selectedTabId = TabSelector.ABOUT.value,
                    tabs = emptyList()
                ),
                action = null
            )

            val result = specialist.toAccreditedNetworkProfileTransport(null)

            assertThat(result).isEqualTo(expected)
        }

        @Test
        fun `SpecialistDetailsTransport toAccreditedNetworkProfileTransport should handle empty services list`() {
            val specialistId = UUID.randomUUID()
            val specialistName = "Dr. Specialist"
            val specialistImageUrl = "https://example.com/specialist.jpg"
            val specialistCrm = "CRM 12345"

            val specialist = SpecialistDetailsTransport(
                id = specialistId,
                providerType = ConsolidatedAccreditedNetworkType.HEALTH_PROFESSIONAL,
                title = "Specialist Title",
                name = specialistName,
                imageUrl = specialistImageUrl,
                crm = specialistCrm,
                services = emptyList(),
                subspecialties = emptyList(),
                sections = emptyList(),
                showMembershipCardShareButton = false,
                favoriteInfo = null
            )

            val expected = AccreditedNetworkProfileTransport(
                id = specialistId,
                type = ConsolidatedAccreditedNetworkType.HEALTH_PROFESSIONAL,
                navbarTitle = "Specialist Title",
                favoriteInfo = null,
                header = HeaderTransport(
                    imageUrl = specialistImageUrl,
                    name = specialistName,
                    description = specialistCrm,
                    tag = null
                ),
                tabBar = TabBarTransport(
                    selectedTabId = TabSelector.ABOUT.value,
                    tabs = emptyList()
                ),
                action = null
            )

            val result = specialist.toAccreditedNetworkProfileTransport(null)

            assertThat(result).isEqualTo(expected)
        }
    }
}
