package br.com.alice.data.layer.services

import br.com.alice.data.layer.models.AppointmentScheduleOptionModel
import br.com.alice.data.layer.pipelines.DatabasePipelineFactory
import br.com.alice.data.layer.tables.AppointmentScheduleOptionTable

class AppointmentScheduleOptionModelDataServiceImpl internal constructor(factory: DatabasePipelineFactory) :
    BaseDataServiceImpl<AppointmentScheduleOptionModel>(factory.get(AppointmentScheduleOptionModel::class, AppointmentScheduleOptionTable::class)),
    AppointmentScheduleOptionModelDataService
