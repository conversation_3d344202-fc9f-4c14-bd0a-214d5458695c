
package br.com.alice.tiss.totvs.v4_01_00.guia;

import jakarta.xml.ws.*;

import javax.xml.namespace.QName;
import java.net.MalformedURLException;
import java.net.URL;


/**
 * This class was generated by the JAX-WS RI.
 * JAX-WS RI 2.2.9-b130926.1035
 * Generated source version: 2.2
 * 
 */
@WebServiceClient(name = "tissCancelaGuia", targetNamespace = "http://www.ans.gov.br/tiss/ws/tipos/tisscancelaguia/v40100", wsdlLocation = "https://alicetecnologia153313.protheus.cloudtotvs.com.br:1232/pp01/tissCancelaGuiaV4_01_00.apw?wsdl")
public class TissCancelaGuia
    extends Service
{

    private final static URL TISSCANCELAGUIA_WSDL_LOCATION;
    private final static WebServiceException TISSCANCELAGUIA_EXCEPTION;
    private final static QName TISSCANCELAGUIA_QNAME = new QName("http://www.ans.gov.br/tiss/ws/tipos/tisscancelaguia/v40100", "tissCancelaGuia");

    static {
        URL url = null;
        WebServiceException e = null;
        try {
            url = new URL("https://alicetecnologia153313.protheus.cloudtotvs.com.br:1232/pp01/tissCancelaGuiaV4_01_00.apw?wsdl");
        } catch (MalformedURLException ex) {
            e = new WebServiceException(ex);
        }
        TISSCANCELAGUIA_WSDL_LOCATION = url;
        TISSCANCELAGUIA_EXCEPTION = e;
    }

    public TissCancelaGuia() {
        super(__getWsdlLocation(), TISSCANCELAGUIA_QNAME);
    }

    public TissCancelaGuia(WebServiceFeature... features) {
        super(__getWsdlLocation(), TISSCANCELAGUIA_QNAME, features);
    }

    public TissCancelaGuia(URL wsdlLocation) {
        super(wsdlLocation, TISSCANCELAGUIA_QNAME);
    }

    public TissCancelaGuia(URL wsdlLocation, WebServiceFeature... features) {
        super(wsdlLocation, TISSCANCELAGUIA_QNAME, features);
    }

    public TissCancelaGuia(URL wsdlLocation, QName serviceName) {
        super(wsdlLocation, serviceName);
    }

    public TissCancelaGuia(URL wsdlLocation, QName serviceName, WebServiceFeature... features) {
        super(wsdlLocation, serviceName, features);
    }

    /**
     * 
     * @return
     *     returns TissCancelaGuiaPortType
     */
    @WebEndpoint(name = "tissCancelaGuia_Port")
    public TissCancelaGuiaPortType getTissCancelaGuiaPort() {
        return super.getPort(new QName("http://www.ans.gov.br/tiss/ws/tipos/tisscancelaguia/v40100", "tissCancelaGuia_Port"), TissCancelaGuiaPortType.class);
    }

    /**
     * 
     * @param features
     *     A list of {@link javax.xml.ws.WebServiceFeature} to configure on the proxy.  Supported features not in the <code>features</code> parameter will have their default values.
     * @return
     *     returns TissCancelaGuiaPortType
     */
    @WebEndpoint(name = "tissCancelaGuia_Port")
    public TissCancelaGuiaPortType getTissCancelaGuiaPort(WebServiceFeature... features) {
        return super.getPort(new QName("http://www.ans.gov.br/tiss/ws/tipos/tisscancelaguia/v40100", "tissCancelaGuia_Port"), TissCancelaGuiaPortType.class, features);
    }

    private static URL __getWsdlLocation() {
        if (TISSCANCELAGUIA_EXCEPTION!= null) {
            throw TISSCANCELAGUIA_EXCEPTION;
        }
        return TISSCANCELAGUIA_WSDL_LOCATION;
    }

}
