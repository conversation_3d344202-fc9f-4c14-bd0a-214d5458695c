package br.com.alice.data.layer.policies.features

import br.com.alice.data.layer.authorization.Create
import br.com.alice.data.layer.authorization.Delete
import br.com.alice.data.layer.authorization.Update
import br.com.alice.data.layer.authorization.View
import br.com.alice.data.layer.authorization.policySet
import br.com.alice.data.layer.models.FileVault
import br.com.alice.data.layer.models.GenericFileVault
import br.com.alice.data.layer.policies.herDependent
import br.com.alice.data.layer.policies.herOwn
import br.com.alice.data.layer.policies.testPersonResource
import br.com.alice.data.layer.subjects.Unauthenticated

val viewAnyRefundFile = policySet {
    match("can view", { action is View}) {
        match("any File") { resource is FileVault && resource.domain == "refund" }
    }
}

val viewAndUpdateAnyFile = policySet {
    match("can view and update and create", { action is View || action is Update || action is Create }) {
        match("any File") { resource is FileVault }
    }
}

val viewAndUpdateAndDeleteHerOwnFiles = policySet {
    match("can view, update, create and delete", { action is View || action is Update || action is Create || action is Delete }) {
        match("her Files") { herOwn(FileVault::class) }
    }
}

val viewAndUpdateAndDeleteHerDependentsFiles = policySet {
    match("can view, update, create and delete", { action is View || action is Update || action is Create || action is Delete }) {
        match("her Dependents Files") { herDependent(FileVault::class) }
    }
}

val viewAndUpdateTestPersonFiles = policySet {
    match("can view and update and create", { action is View || action is Update || action is Create }) {
        match("her Files") { herOwn(FileVault::class) }
        match("test person Files") { testPersonResource(FileVault::class) }
    }
}

val viewAnyMemberDocuments = policySet {
    match("can view", { action is View }) {
        match("any member documents") {
            resource is FileVault &&
                    resource.domain == "member" &&
                    resource.namespace == "documents"
        }
    }
}

val viewAnyPersonHealthDocuments = policySet {
    match("can view", { action is View }) {
        match("any member documents") {
            resource is FileVault &&
                    resource.domain == "personHealthDocument"
        }
    }
}

val viewAnyPrescriptionFiles = policySet {
    match("can view", { action is View }) {
        match("any prescription file") {
            resource is FileVault &&
                    resource.domain == "ehr" &&
                    resource.namespace == "signed_prescription" &&
                    resource.personId.toString() == (subject as Unauthenticated).key
        }
    }
}

val viewAnyReferralFiles = policySet {
    match("can view", { action is View }) {
        match("any referral file") {
            resource is FileVault &&
                    resource.domain == "ehr" &&
                    resource.namespace == "signed_referral" &&
                    resource.personId.toString() == (subject as Unauthenticated).key
        }
    }
}

val createAmasInvoiceBatch = policySet {
    match("can view and create", { action is Create || action is View }) {
        match("any file vault") {
            (resource is FileVault) &&
                    resource.domain == "amas" &&
                    resource.namespace == "provider_invoice_batch"
        }
        match("any generic file") {
            (resource is GenericFileVault)
        }
    }
}

val viewAnyExcuseNoteFiles = policySet {
    match("can view", { action is View }) {
        match("any excuse note file") {
            resource is FileVault &&
                    resource.domain == "ehr" &&
                    resource.namespace == "signed_excuse_notes" &&
                    resource.personId.toString() == (subject as Unauthenticated).key
        }
    }
}

val viewAndUpdateAnyContractFiles = policySet {
    match("can view and update and create", { action is View || action is Update || action is Create }) {
        match("any contract file") {
            resource is FileVault &&
                    resource.domain == "member" &&
                    resource.namespace == "contract"
        }
    }
}

val viewAndUpdateAnyTermFiles = policySet {
    match("can view and update and create", { action is View || action is Update || action is Create }) {
        match("any term file") {
            resource is FileVault &&
                    resource.domain == "member" &&
                    resource.namespace == "term"
        }
    }
}

val viewAnyMemberPhoto = policySet {
    match("can view", { action is View }) {
        match("any member photo") {
            resource is FileVault &&
                    resource.namespace == "member-photo"
        }
    }
}

val viewAndUpdateAnyInsurancePortabilityFile = policySet {
    match("can view", { action is View || action is Update || action is Create }) {
        match("any portability file") {
            resource is FileVault &&
                    resource.namespace == "portability"
        }
    }
}

val viewEhrFiles = policySet {
    match("can view", { action is View }) {
        match("any ehr file") {
            resource is FileVault &&
                    resource.domain == "ehr"
        }
    }
}

val viewUpdateCreateEhrFiles = policySet {
    match("can view, update and create", { action is View || action is Update || action is Create }) {
        match("any ehr file") {
            resource is FileVault &&
                    resource.domain == "ehr"
        }
    }
}

val viewCreateUpdateDependentFiles = policySet {
    match("can view and create and update", { action is View  || action is Create || action is Update }) {
        match("any dependent file") {
            resource is FileVault &&
                    resource.domain == "business" && resource.namespace == "dependent_files"
        }
    }
}

val deleteDependentFiles = policySet {
    match("can delete", { action is Delete }) {
        match("any dependent file") {
            resource as FileVault
            resourceIs(FileVault::class) &&
                   resource.domain == "business" && resource.namespace == "dependent_files"
        }
    }
}
