package br.com.alice.member.api.converters

import br.com.alice.common.core.extensions.isNotNullOrEmpty
import br.com.alice.data.layer.models.MemberOnboardingAction
import br.com.alice.data.layer.models.MemberOnboardingOptIn
import br.com.alice.data.layer.models.MemberOnboardingStep
import br.com.alice.data.layer.models.MemberOnboardingStep.MemberOnboardingStepHealthFormEnum
import br.com.alice.data.layer.models.MemberOnboardingStep.MemberOnboardingStepHealthFormEnum.ADDRESS
import br.com.alice.data.layer.models.MemberOnboardingStep.MemberOnboardingStepHealthFormEnum.HEALTH_DECLARATION
import br.com.alice.data.layer.models.MemberOnboardingStep.MemberOnboardingStepHealthFormEnum.HOME
import br.com.alice.data.layer.models.MemberOnboardingStep.MemberOnboardingStepHealthFormEnum.IMMERSION_SCHEDULE_LEAGUE
import br.com.alice.data.layer.models.MemberOnboardingStep.MemberOnboardingStepHealthFormEnum.PRE_IMMERSION
import br.com.alice.data.layer.models.MemberOnboardingStep.MemberOnboardingStepHealthFormEnum.SCORE_MAGENTA_RESULT
import br.com.alice.data.layer.models.MemberOnboardingTemplate
import br.com.alice.data.layer.models.Person
import br.com.alice.member.api.models.MemberOnboardingAccessoryImageInformation
import br.com.alice.member.api.models.MemberOnboardingActionResponse
import br.com.alice.member.api.models.MemberOnboardingOptInResponse
import br.com.alice.member.api.models.MemberOnboardingStepFolder
import br.com.alice.member.api.models.MemberOnboardingStepProgressResponse
import br.com.alice.member.api.models.MemberOnboardingStepResponse
import br.com.alice.member.api.models.MemberOnboardingStepTag
import br.com.alice.member.api.models.MemberOnboardingStepVideoInformation
import br.com.alice.member.api.models.MemberOnboardingWidgetComponent
import br.com.alice.member.api.models.MobileRouting
import br.com.alice.member.api.models.NavigationResponse

const val DEFAULT_ACCESSORY_IMAGE =
    "https://alice-member-app-assets.s3.amazonaws.com/member_onboarding/videos/onboarding_video_bottom_navigation.png"
const val DEFAULT_PREVIEW_VIDEO_IMAGE =
    "https://alice-member-app-assets.s3.amazonaws.com/member_onboarding/videos/onboarding_video_preview.mp4"
const val TIME_IN_SECONDS_LOW_RISK_SHOW_ACCESSORY_IMAGE = 19
const val TIME_IN_SECONDS_LOW_RISK_SHOW_ACCESSORY_IMAGE_NEW_PORTFOLIO = 47
const val TIME_IN_SECONDS_ENDING_VIDEO_SHOW_ACCESSORY_IMAGE = 3
const val TIME_IN_SECONDS_ENDING_VIDEO_SHOW_ACCESSORY_IMAGE_NEW_PORTFOLIO = 1
const val DEFAULT_FOLDER_DESCRIPTION = "Para começar, aperte o play e descubra como a Alice funciona."

data class MemberOnboardingStepResponseProviders(
    val memberOnboardingActions: List<MemberOnboardingAction>,
    val person: Person? = null,
    val hasFormResult: Boolean = false,
    val hasDroppedOnboarding: Boolean = false,
    val immersionSchedulingUrl: String = "",
    val isBegin: Boolean = false,
)

object MemberOnboardingStepResponseConverter {

    fun convert(
        source: MemberOnboardingStep,
        providers: MemberOnboardingStepResponseProviders,
        template: MemberOnboardingTemplate,
    ): MemberOnboardingStepResponse {
        return MemberOnboardingStepResponse(
            type = source.format.toString(),
            folder = getDefaultFolder(source, providers),
            accessoryImage = getDefaultAccessoryImage(source, template),
            video = getVideoInformation(source, template),
            showEmergencyButton = source.showEmergencyButton,
            forceVideoFolder = source.forceVideoFolder,
            imageUrl = checkIfStringIsBlankOrNull(source.imageUrl),
            title = checkIfExistsTextVar(providers.person, checkIfStringIsBlankOrNull(source.title)),
            key = checkIfStringIsBlankOrNull(source.key),
            text = checkIfStringIsBlankOrNull(source.description),
            optIns = buildOptIns(source.optIns),
            actions = buildActions(providers.memberOnboardingActions),
            navigation = buildNavigation(
                source.healthFormNavigation,
                providers.hasFormResult,
                providers.hasDroppedOnboarding,
                providers.immersionSchedulingUrl,
            ),
            widgetComponent = buildWidgetComponent(source.widgetComponent),
            tag = buildTag(source),
            progress = buildProgress(source),
        )
    }

    private fun buildProgress(step: MemberOnboardingStep): List<MemberOnboardingStepProgressResponse>? =
        step.progress.takeIf { it.isNotNullOrEmpty() }?.map { progress ->
            MemberOnboardingStepProgressResponse(
                title = progress.title,
                description = progress.description,
                imageUrl = progress.image
            )
        }

    private fun buildTag(step: MemberOnboardingStep): MemberOnboardingStepTag? =
        if (step.isInitStep() && step.estimatedTimeText != null) {
            MemberOnboardingStepTag(
                step.estimatedTimeText,
                step.estimatedTimeIcon
            )
        } else {
            null
        }

    private fun checkIfExistsTextVar(person: Person?, str: String?): String? =
        person?.let { it ->
            val allowedList = mapOf(
                "{nome}" to (it.nickName ?: it.socialFirstName ?: it.firstName),
            )

            val mutableList = mutableListOf(str)
            allowedList.map {
                mutableList.add(mutableList.last()?.replace(it.key, it.value))
            }

            mutableList.last()
        } ?: str

    private fun checkIfStringIsBlankOrNull(str: String): String? = str.ifBlank { null }

    private fun buildOptIns(optIns: List<MemberOnboardingOptIn>?): List<MemberOnboardingOptInResponse> =
        optIns?.let { it ->
            val optInsResponse = mutableListOf<MemberOnboardingOptInResponse>()
            it.forEach {
                optInsResponse.add(MemberOnboardingOptInsResponseConverter.convert(it))
            }
            optInsResponse
        } ?: emptyList()

    private fun buildActions(actions: List<MemberOnboardingAction>): List<MemberOnboardingActionResponse> {
        val actionsResponse = mutableListOf<MemberOnboardingActionResponse>()
        actions.forEach { actionsResponse.add(MemberOnboardingActionResponseConverter.convert(it)) }

        return actionsResponse
    }

    private fun buildNavigation(
        navigation: MemberOnboardingStepHealthFormEnum?,
        hasFormResult: Boolean = false,
        hasDroppedOnboarding: Boolean = false,
        immersionSchedulingUrl: String,
    ): NavigationResponse? = when (navigation) {
        HEALTH_DECLARATION -> if (hasFormResult) null else NavigationResponse(
            mobileRoute = MobileRouting.HEALTH_DECLARATION,
            properties = mapOf(
                buildShowEmergencyBar(hasDroppedOnboarding)
            )
        )

        PRE_IMMERSION -> if (hasFormResult) null else NavigationResponse(
            mobileRoute = MobileRouting.QUESTIONNAIRE,
            properties = mapOf(
                "type" to "QUEST_IMMERSION_PROFILE",
                buildShowEmergencyBar(hasDroppedOnboarding)
            )
        )

        HOME -> NavigationResponse(
            mobileRoute = MobileRouting.HOME
        )

        IMMERSION_SCHEDULE_LEAGUE -> {
            if (hasFormResult) null
            else buildNavigationSchedule(hasDroppedOnboarding, immersionSchedulingUrl)
        }

        SCORE_MAGENTA_RESULT -> NavigationResponse(
            mobileRoute = MobileRouting.SCORE_MAGENTA_RESULT,
            properties = mapOf(
                "is_welcome" to true,
                buildShowEmergencyBar(hasDroppedOnboarding)
            )
        )

        ADDRESS -> NavigationResponse(mobileRoute = MobileRouting.MEMBER_ADDRESS_INPUT)
        else -> null
    }

    private fun buildNavigationSchedule(
        hasDroppedOnboarding: Boolean,
        immersionSchedulingUrl: String,
    ) = NavigationResponse(
        mobileRoute = MobileRouting.WEBVIEW,
        properties = mapOf(
            "link" to immersionSchedulingUrl,
            buildShowEmergencyBar(hasDroppedOnboarding), // TODO: delete this field after app base install update to 3.55.0 version
            buildWebviewAppBarType(hasDroppedOnboarding),
            "token" to "true"
        )
    )

    private fun buildShowEmergencyBar(hasDroppedOnboarding: Boolean = false) =
        "show_emergency_bar" to !hasDroppedOnboarding

    private fun buildWebviewAppBarType(hasDroppedOnboarding: Boolean = false) =
        "app_bar_type" to if (hasDroppedOnboarding) "none" else "emergency"

    private fun buildWidgetComponent(widgetComponent: MemberOnboardingStep.MemberOnboardingWidgetComponent?): MemberOnboardingWidgetComponent? =
        when (widgetComponent) {
            MemberOnboardingStep.MemberOnboardingWidgetComponent.SCORE_MAGENTA -> MemberOnboardingWidgetComponent.SCORE_MAGENTA
            MemberOnboardingStep.MemberOnboardingWidgetComponent.LOADING -> MemberOnboardingWidgetComponent.LOADING
            else -> null
        }

    private fun getDefaultFolder(
        step: MemberOnboardingStep,
        providers: MemberOnboardingStepResponseProviders
    ) =
        if ((providers.isBegin || step.forceVideoFolder) && step.isVideo() && step.healthFormNavigation == null) {
            MemberOnboardingStepFolder(
                title = "Boas vindas, ${(providers.person?.nickName ?: providers.person?.socialFirstName ?: providers.person?.firstName)}!",
                text = DEFAULT_FOLDER_DESCRIPTION,
                imageUrl = step.videoPreview ?: DEFAULT_PREVIEW_VIDEO_IMAGE,
                showEmergencyButton = true
            )
        } else null

    private fun getVideoInformation(
        step: MemberOnboardingStep,
        template: MemberOnboardingTemplate
    ) =
        if (step.isVideo()) {
            MemberOnboardingStepVideoInformation(
                videoUrl = step.videoUrl,
                caption = step.videoCaptionUrl,
                emergencyButtonEndTime = getEmergencyButtonHideTime(step, template)
            )
        } else null

    private fun getEmergencyButtonHideTime(
        step: MemberOnboardingStep,
        template: MemberOnboardingTemplate
    ): Int? =
        getDefaultAccessoryImageExhibitionTimeInSeconds(step, template)

    private fun getDefaultAccessoryImage(
        step: MemberOnboardingStep,
        template: MemberOnboardingTemplate
    ) =
        if (isLastVideo(step)) {
            MemberOnboardingAccessoryImageInformation(
                imageUrl = DEFAULT_ACCESSORY_IMAGE,
                startTime = getDefaultAccessoryImageExhibitionTimeInSeconds(step, template)
            )
        } else null

    private fun isLastVideo(step: MemberOnboardingStep) =
        step.isVideo() && (step.isLowRiskStep() || step.isSchedulingStep())

    private fun getDefaultAccessoryImageExhibitionTimeInSeconds(
        step: MemberOnboardingStep,
        template: MemberOnboardingTemplate
    ): Int? =
        if (step.isVideo() && step.isLowRiskStep()) {
            template.lowRiskAccessoryExhibitionTime()
        } else if (step.isSchedulingStep()) {
            template.schedulingAccessoryExhibitionTime()
        } else {
            null
        }
}

private fun MemberOnboardingTemplate.lowRiskAccessoryExhibitionTime() =
    if (this.isFromNewPortfolio()) TIME_IN_SECONDS_LOW_RISK_SHOW_ACCESSORY_IMAGE_NEW_PORTFOLIO
    else TIME_IN_SECONDS_LOW_RISK_SHOW_ACCESSORY_IMAGE

private fun MemberOnboardingTemplate.schedulingAccessoryExhibitionTime() =
    if (this.isFromNewPortfolio()) TIME_IN_SECONDS_ENDING_VIDEO_SHOW_ACCESSORY_IMAGE_NEW_PORTFOLIO
    else TIME_IN_SECONDS_ENDING_VIDEO_SHOW_ACCESSORY_IMAGE

object MemberOnboardingActionResponseConverter {
    fun convert(source: MemberOnboardingAction): MemberOnboardingActionResponse {
        return MemberOnboardingActionResponse(
            name = source.name,
            label = source.label.ifBlank { null },
            icon = source.icon,
            actionUrl = source.actionUrl,
            isBackVisible = source.isBackVisible,
            listenAppState = source.listenAppState,
        )
    }
}

object MemberOnboardingOptInsResponseConverter {
    fun convert(source: MemberOnboardingOptIn): MemberOnboardingOptInResponse {
        return MemberOnboardingOptInResponse(
            text = source.text,
            link = source.link
        )
    }
}
