package app.product_domain_service_test

import rego.v1

import data.app.product_domain_service

test_view_models_allowed if {
	{1, 2, 3, 4, 5, 6, 7, 8} == product_domain_service.allow with input as {"cases": [
		{
			"index": 1,
			"action": "view",
			"subject": {"opaType": "Unauthenticated"},
			"resource": {"opaType": "ProviderModel"},
		},
		{
			"index": 2,
			"action": "view",
			"subject": {"opaType": "Unauthenticated"},
			"resource": {"opaType": "ProviderUnitModel"},
		},
		{
			"index": 3,
			"action": "view",
			"subject": {"opaType": "Unauthenticated"},
			"resource": {"opaType": "StructuredAddress"},
		},
		{
			"index": 4,
			"action": "view",
			"subject": {"opaType": "Unauthenticated"},
			"resource": {"opaType": "ContactModel"},
		},
		{
			"index": 5,
			"action": "view",
			"subject": {"opaType": "Unauthenticated"},
			"resource": {"opaType": "ProductPriceListingModel"},
		},
		{
			"index": 6,
			"action": "view",
			"subject": {"opaType": "Unauthenticated"},
			"resource": {"opaType": "PriceListingModel"},
		},
		{
			"index": 7,
			"action": "view",
			"subject": {"opaType": "Unauthenticated"},
			"resource": {"opaType": "ProductGroupModel"},
		},
		{
			"index": 8,
			"action": "view",
			"subject": {"opaType": "Unauthenticated"},
			"resource": {"opaType": "ProductBundleModel"},
		},
	]}
}

test_CRU_CoPaymentCostInfo_allowed if {
	{1, 2, 3} == product_domain_service.allow with input as {"cases": [
		{
			"index": 1,
			"action": "create",
			"subject": {"opaType": "Unauthenticated"},
			"resource": {"opaType": "CoPaymentCostInfoModel"},
		},
		{
			"index": 2,
			"action": "view",
			"subject": {"opaType": "Unauthenticated"},
			"resource": {"opaType": "CoPaymentCostInfoModel"},
		},
		{
			"index": 3,
			"action": "update",
			"subject": {"opaType": "Unauthenticated"},
			"resource": {"opaType": "CoPaymentCostInfoModel"},
		},
	]}
}

test_CRU_RefundCostInfo_allowed if {
	{1, 2, 3} == product_domain_service.allow with input as {"cases": [
		{
			"index": 1,
			"action": "create",
			"subject": {"opaType": "Unauthenticated"},
			"resource": {"opaType": "RefundCostInfoModel"},
		},
		{
			"index": 2,
			"action": "view",
			"subject": {"opaType": "Unauthenticated"},
			"resource": {"opaType": "RefundCostInfoModel"},
		},
		{
			"index": 3,
			"action": "update",
			"subject": {"opaType": "Unauthenticated"},
			"resource": {"opaType": "RefundCostInfoModel"},
		},
	]}
}

test_RU_ProductModel_allowed if {
	{1, 2} == product_domain_service.allow with input as {"cases": [
		{
			"index": 1,
			"action": "view",
			"subject": {"opaType": "Unauthenticated"},
			"resource": {"opaType": "ProductModel"},
		},
		{
			"index": 2,
			"action": "update",
			"subject": {"opaType": "Unauthenticated"},
			"resource": {"opaType": "ProductModel"},
		},
	]}
}
