package br.com.alice.dragonradar.consumers

import br.com.alice.common.RangeUUID
import br.com.alice.common.core.PersonId
import br.com.alice.common.data.dsl.matchers.ResultAssert
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.EmergencyRecommendation
import br.com.alice.data.layer.models.EmergencySpecialty
import br.com.alice.data.layer.models.ProviderUnit
import br.com.alice.dragonradar.client.CalculateEmergencyRecommendationService
import br.com.alice.dragonradar.client.EmergencyRecommendationService
import br.com.alice.dragonradar.event.CalculateEmergencyRecommendationEvent
import com.github.kittinunf.result.success
import io.mockk.coEvery
import io.mockk.mockk
import kotlinx.coroutines.runBlocking
import kotlin.test.Test


internal class CalculateEmergencyRecommendationConsumerTest : ConsumerTest() {

    private val recommendationService: EmergencyRecommendationService = mockk()
    private val calculateRecommendationService: CalculateEmergencyRecommendationService = mockk()

    private val consumer = CalculateEmergencyRecommendationConsumer(
        recommendationService,
        calculateRecommendationService
    )

    @Test
    fun `#consume should calculate recommendation and save successfully`() = runBlocking {
        val providerUnit = TestModelFactory.buildProviderUnit()
        val recommendation = EmergencyRecommendation(
            personId = PersonId(),
            healthPlanTaskId = RangeUUID.generate(),
            cityId = "cityId",
            emergencySpecialty = EmergencySpecialty(name = "Especialidade", id = RangeUUID.generate()),
        )

        val recommendationWithResult = recommendation.copy(recommendedProviderUnitId = providerUnit.id)
        coEvery {
            recommendationService.upsert(
                match { r: EmergencyRecommendation ->
                    r.id == recommendation.id
                }
            )
        } returns recommendationWithResult.success()

        coEvery {
            calculateRecommendationService.calculate(recommendation)
        } returns listOf(providerUnit).success()

        val result = consumer.consume(CalculateEmergencyRecommendationEvent(recommendation))

        ResultAssert.assertThat(result).isSuccessWithData(recommendationWithResult)

        coVerifyOnce { recommendationService.upsert(any()) }
        coVerifyOnce { calculateRecommendationService.calculate(any()) }
    }

    @Test
    fun `#consume should calculate and save successfully when no provider unit is returned`() = runBlocking {
        val recommendation = EmergencyRecommendation(
            personId = PersonId(),
            healthPlanTaskId = RangeUUID.generate(),
            cityId = "cityId",
            emergencySpecialty = EmergencySpecialty(name = "Especialidade", id = RangeUUID.generate()),
        )

        coEvery {
            recommendationService.upsert(
                match { r: EmergencyRecommendation ->
                    r.id == recommendation.id
                }
            )
        } returns recommendation.success()

        coEvery {
            calculateRecommendationService.calculate(recommendation)
        } returns emptyList<ProviderUnit>().success()

        val result = consumer.consume(CalculateEmergencyRecommendationEvent(recommendation))

        ResultAssert.assertThat(result).isSuccessWithData(recommendation)

        coVerifyOnce { recommendationService.upsert(any()) }
        coVerifyOnce { calculateRecommendationService.calculate(any()) }
    }



}
