package br.com.alice.data.layer.services

import br.com.alice.common.core.PersonId
import br.com.alice.common.data.dsl.matchers.ResultAssert.Companion.assertThat
import br.com.alice.common.service.data.dsl.OrPredicateUsage
import br.com.alice.common.service.data.dsl.or
import br.com.alice.data.layer.helpers.DataLayerTestModelFactory
import br.com.alice.data.layer.helpers.DataServiceTestHelper
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.helpers.TestTableFactory
import br.com.alice.data.layer.models.CounterReferralGenericTask
import br.com.alice.data.layer.models.CounterReferralTypeOfService.APPOINTMENT_ONLY
import br.com.alice.data.layer.models.CounterReferralTypeOfService.SURGICAL_PROCEDURE
import br.com.alice.data.layer.repositories.JdbiRepositoryFactory
import br.com.alice.data.layer.tables.AppointmentTable
import br.com.alice.data.layer.tables.PersonTable
import kotlinx.coroutines.runBlocking
import kotlin.test.Test

class CounterReferralDataServiceTest : DataServiceTestHelper() {

    private val healthCommunitySpecialistDataServiceClient = HealthCommunitySpecialistModelDataServiceClient(httpInvoker)
    private val staffDataServiceClient = StaffModelDataServiceClient(httpInvoker)

    private val repoFactory = JdbiRepositoryFactory(mainJdbi)
    private val personRepo = repoFactory.get(PersonTable::class)
    private val personTokenService = PersonTokenServiceImpl(tokenJdbi) as PersonTokenService
    private val counterReferralDataService = CounterReferralDataServiceClient(httpInvoker)
    private val appointmentRepo = repoFactory.get(AppointmentTable::class)

    @OptIn(OrPredicateUsage::class)
    @Test
    fun `should add new entity when it doesn't exist`() = runBlocking {
        val personId = PersonId()
        val personToken = personTokenService.createForPersonId(personId).get()
        val personEntity = TestTableFactory.buildPersonTable(personToken.personPiiToken)
        personRepo.add(personEntity)
        val staff = DataLayerTestModelFactory.buildStaff()
        val healthCommunitySpecialist = DataLayerTestModelFactory.buildHealthCommunitySpecialist(staffId = staff.id)
        staffDataServiceClient.add(staff).get()
        healthCommunitySpecialistDataServiceClient.add(healthCommunitySpecialist).get()

        val counterReferral = TestModelFactory.buildCounterReferral(
            personId = personId,
            staffId = healthCommunitySpecialist.staffId,
            healthCommunitySpecialistId = healthCommunitySpecialist.id,
            surgicalReferrals = listOf(CounterReferralGenericTask()),
            typeOfService = SURGICAL_PROCEDURE
        )

        val addResult = counterReferralDataService.add(counterReferral)
        assertThat(addResult)
            .isSuccessWithDataIgnoringGivenFields(counterReferral, "createdAt", "updatedAt")

        val retrieved = counterReferralDataService.get(counterReferral.id)
        assertThat(retrieved)
            .isSuccessWithDataIgnoringGivenFields(counterReferral, "createdAt", "updatedAt")

        val retrievedBySurgery =
            counterReferralDataService.find { where { surgicalReferral.notEmpty() } }
        assertThat(retrievedBySurgery)
            .isSuccessWithDataIgnoringGivenFields(listOf(counterReferral), "createdAt", "updatedAt")

        val retrievedBySurgeryWithOR =
            counterReferralDataService.find {
                where {
                    typeOfService.eq(SURGICAL_PROCEDURE).or(surgicalReferral.notEmpty())
                }
            }
        assertThat(retrievedBySurgeryWithOR)
            .isSuccessWithDataIgnoringGivenFields(listOf(counterReferral), "createdAt", "updatedAt")
    }

    @Test
    fun `should add new entity when it doesn't exist with appointment id`() = runBlocking {
        val personId = PersonId()
        val personToken = personTokenService.createForPersonId(personId).get()
        val personEntity = TestTableFactory.buildPersonTable(personToken.personPiiToken, nationalId = "12345678901")
        personRepo.add(personEntity)

        val staff = DataLayerTestModelFactory.buildStaff(
            email = "<EMAIL>", nationalId = "12345678901"
        )
        val healthCommunitySpecialist = DataLayerTestModelFactory.buildHealthCommunitySpecialist(
            staffId = staff.id, email = staff.email
        )
        val appointment = TestTableFactory.buildAppointmentTable(staff.id, personToken.personHiToken)

        staffDataServiceClient.add(staff).get()
        healthCommunitySpecialistDataServiceClient.add(healthCommunitySpecialist).get()
        appointmentRepo.add(appointment)

        val counterReferral = TestModelFactory.buildCounterReferral(
            personId = personId,
            healthCommunitySpecialistId = healthCommunitySpecialist.id,
            staffId = staff.id,
            typeOfService = APPOINTMENT_ONLY,
            appointmentId = appointment.id
        )

        val addResult = counterReferralDataService.add(counterReferral)
        assertThat(addResult)
            .isSuccessWithDataIgnoringGivenFields(counterReferral, "createdAt", "updatedAt")
    }
}
