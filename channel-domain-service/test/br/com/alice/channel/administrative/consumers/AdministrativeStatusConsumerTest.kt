package br.com.alice.channel.administrative.consumers

import br.com.alice.channel.administrative.services.internal.AdministrativeStateService
import br.com.alice.channel.core.consumers.ConsumerTest
import br.com.alice.channel.core.context.FirestoreContextUsage
import br.com.alice.channel.core.services.internal.ChannelService
import br.com.alice.channel.models.ChannelDocument
import br.com.alice.channel.notifier.ChannelUpsertedEvent
import br.com.alice.channel.notifier.ChannelUpsertedPayload
import br.com.alice.channel.notifier.StaffsRemovedByInactivityEvent
import br.com.alice.common.data.dsl.matchers.ResultAssert
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.AdministrativeStatus
import br.com.alice.data.layer.models.ChannelCategory
import br.com.alice.data.layer.models.ChannelChangeAction
import br.com.alice.data.layer.models.ChannelKind
import br.com.alice.data.layer.models.ChannelType
import com.github.kittinunf.result.success
import io.mockk.Called
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.confirmVerified
import io.mockk.mockk
import kotlinx.coroutines.runBlocking
import kotlin.test.AfterTest
import kotlin.test.Test

class AdministrativeStatusConsumerTest  : ConsumerTest() {

    private val channelService: ChannelService = mockk()
    private val administrativeStateService: AdministrativeStateService = mockk()

    private val service = AdministrativeStatusConsumer(
        channelService,
        administrativeStateService
    )

    private val channelId = "channelId"
    private val person = TestModelFactory.buildPerson()
    private val channelPersonId = person.id.toString()

    private val channelDocument = ChannelDocument(
        id = channelId,
        channelPersonId = channelPersonId,
        personId = channelPersonId,
        type = ChannelType.ADMINISTRATIVE,
        kind = ChannelKind.CHANNEL,
        category = ChannelCategory.ADMINISTRATIVE,
    )

    private val upsertedEventPayload = ChannelUpsertedPayload(
        channelId = channelDocument.id!!,
        personId = person.id,
        name = channelDocument.name,
        type = channelDocument.type!!,
        status = channelDocument.status,
        action = ChannelChangeAction.CREATE_CHAT,
        kind = channelDocument.kind,
        category = channelDocument.category,
        subCategory = channelDocument.subCategory,
        subCategoryClassifier = channelDocument.subCategoryClassifier,
        tags = channelDocument.tags,
        appVersion = "3.0.0"
    )

    private val inactivityEvent = StaffsRemovedByInactivityEvent(
        channelId = channelDocument.id!!,
        personId = person.id,
        staffIds = listOf(),
        channel = channelDocument
    )

    @FirestoreContextUsage
    @Test
    fun `#processUpsertEvent should return false and ignore when channel is not administrative`() = runBlocking {
        val event = ChannelUpsertedEvent(upsertedEventPayload.copy(category = ChannelCategory.ASSISTANCE))

        val result = service.processUpsertEvent(event)
        ResultAssert.assertThat(result).isSuccessWithData(false)

        coVerify { channelService wasNot Called }
        coVerify { administrativeStateService wasNot Called }
    }

    @FirestoreContextUsage
    @Test
    fun `#processUpsertEvent should return true when event payload is valid to determine and change status`() = runBlocking {
        val event = ChannelUpsertedEvent(upsertedEventPayload.copy(action = ChannelChangeAction.CREATE_CHANNEL))

        coEvery {
            channelService.getChannel(channelId)
        } returns channelDocument.success()

        coEvery {
            administrativeStateService.defineAndSetStatus(channelDocument, null, ChannelChangeAction.CREATE_CHANNEL)
        } returns true.success()

        val result = service.processUpsertEvent(event)
        ResultAssert.assertThat(result).isSuccessWithData(true)

        coVerify(exactly = 1) { channelService.getChannel(any()) }
        coVerify(exactly = 1) { administrativeStateService.defineAndSetStatus(any(), any(), any()) }
    }

    @FirestoreContextUsage
    @Test
    fun `#processInactivityEvent should return false and ignore when channel is not administrative`() = runBlocking {
        val inactivityEvent = inactivityEvent.copy(channel = channelDocument.copy(category = ChannelCategory.ASSISTANCE))

        val result = service.processInactivityEvent(inactivityEvent)
        ResultAssert.assertThat(result).isSuccessWithData(false)
        
        coVerify { administrativeStateService wasNot Called }
    }

    @FirestoreContextUsage
    @Test
    fun `#processInactivityEvent when event received change administrative status to INACTIVE`() = runBlocking {
        coEvery {
            administrativeStateService.setStatus(channelDocument, AdministrativeStatus.INACTIVE)
        } returns true.success()

        val result = service.processInactivityEvent(inactivityEvent)
        ResultAssert.assertThat(result).isSuccessWithData(true)

        coVerify(exactly = 1) { administrativeStateService.setStatus(any(), any()) }
    }

    @AfterTest
    fun confirmrunBlocking() = confirmVerified(
        channelService,
        administrativeStateService
    )
}
