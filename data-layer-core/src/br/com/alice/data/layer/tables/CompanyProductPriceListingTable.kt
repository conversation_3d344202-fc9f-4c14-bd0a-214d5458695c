package br.com.alice.data.layer.tables

import br.com.alice.common.RangeUUID
import br.com.alice.common.UpdatedBy
import br.com.alice.data.layer.models.PriceListingItem
import java.time.LocalDate
import java.time.LocalDateTime
import java.util.UUID

internal data class CompanyProductPriceListingTable(
    override val id: UUID = RangeUUID.generate(),
    val priceListingId: UUID?,
    val productId: UUID,
    val ansNumber: String,
    val companyId: UUID,
    val companySubContractId: UUID,
    val startDate: LocalDate,
    val endDate: LocalDate? = null,
    val priceListItems: List<PriceListingItem>,
    val isBlockedForSale: Boolean? = null,
    override val version: Int = 0,
    override val createdAt: LocalDateTime = LocalDateTime.now(),
    override val updatedAt: LocalDateTime = LocalDateTime.now(),
    val updatedBy: UpdatedBy? = null
) : Table<CompanyProductPriceListingTable> {

    override fun copyTable(version: Int, updatedAt: LocalDateTime, createdAt: LocalDateTime) =
        copy(version = version, createdAt = createdAt, updatedAt = updatedAt)

}
