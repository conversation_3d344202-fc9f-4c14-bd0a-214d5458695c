package br.com.alice.api.backoffice.services

import br.com.alice.common.extensions.coFoldNotFound
import br.com.alice.common.extensions.getOrNullIfNotFound
import br.com.alice.data.layer.models.HealthProfessionalOpsProfile
import br.com.alice.exec.indicator.client.HealthProfessionalOpsProfileService
import com.github.kittinunf.result.Result
import com.github.kittinunf.result.flatMap
import java.util.UUID

class HealthProfessionalOpsProfileInternalService(
    private val healthProfessionalOpsProfileService: HealthProfessionalOpsProfileService,
) {

    suspend fun upsert(healthProfessionalOpsProfile: HealthProfessionalOpsProfile): Result<HealthProfessionalOpsProfile, Throwable> =
        healthProfessionalOpsProfileService.getByHealthProfessionalId(healthProfessionalOpsProfile.healthProfessionalId)
            .flatMap { existingHealthProfessionalOpsProfile ->
                healthProfessionalOpsProfileService.update(
                    existingHealthProfessionalOpsProfile.copy(
                        attendsToOnCall = healthProfessionalOpsProfile.attendsToOnCall,
                        onCallPaymentMethod = healthProfessionalOpsProfile.onCallPaymentMethod
                    )
                )
            }.coFoldNotFound {
                healthProfessionalOpsProfileService.add(healthProfessionalOpsProfile)
            }

    suspend fun getByHealthProfessionalId(healthProfessionalId: UUID): HealthProfessionalOpsProfile? =
        healthProfessionalOpsProfileService.getByHealthProfessionalId(healthProfessionalId).getOrNullIfNotFound()

}
