package br.com.alice.data.layer.models

import br.com.alice.common.RangeUUID
import br.com.alice.common.UpdatedBy
import br.com.alice.common.core.Model
import br.com.alice.common.core.PersonId
import br.com.alice.common.core.PersonReference
import br.com.alice.common.core.exceptions.InvalidArgumentException
import br.com.alice.common.core.extensions.asMap
import br.com.alice.common.logging.logger
import br.com.alice.common.models.HealthInformation
import br.com.alice.common.models.SpecialistTier
import br.com.alice.common.serialization.JsonSerializable
import br.com.alice.common.serialization.LocalDateAdapter
import br.com.alice.common.serialization.LocalDateTimeAdapter
import br.com.alice.data.layer.models.HealthPlanTaskStatus.ACTIVE
import br.com.alice.data.layer.models.HealthPlanTaskStatus.DELETED
import br.com.alice.data.layer.models.HealthPlanTaskStatus.DELETED_BY_MEMBER
import br.com.alice.data.layer.models.HealthPlanTaskStatus.DELETED_BY_STAFF
import br.com.alice.data.layer.models.HealthPlanTaskStatus.DONE
import br.com.alice.data.layer.models.HealthPlanTaskStatus.DRAFT
import br.com.alice.data.layer.services.UpdateRequest
import com.google.gson.Gson
import com.google.gson.GsonBuilder
import com.google.gson.JsonArray
import com.google.gson.JsonElement
import com.google.gson.JsonObject
import com.google.gson.annotations.Expose
import java.time.LocalDate
import java.time.LocalDateTime
import java.util.UUID
import kotlin.reflect.KClass
import kotlin.reflect.full.primaryConstructor

val gsonHealthPlanTask: Gson = GsonBuilder()
    .registerTypeAdapter(LocalDate::class.java, LocalDateAdapter())
    .registerTypeAdapter(LocalDateTime::class.java, LocalDateTimeAdapter())
    .setObjectToNumberStrategy { reader ->
        reader.nextString().let { value ->
            if (value.contains(".")) value.toDouble()
            else value.toInt()
        }
    }
    .create()!!

open class HealthPlanTask(
    @Expose
    override val personId: PersonId,
    val healthPlanId: UUID? = null,
    val appointmentId: UUID? = null,
    val title: String? = null,
    val description: String? = null,
    val content: Map<String, Any>? = null,
    val dueDate: LocalDate? = null,
    val status: HealthPlanTaskStatus = DRAFT,
    val lastRequesterStaffId: UUID,
    val requestersStaffIds: Set<UUID> = emptySet(),
    val type: HealthPlanTaskType,
    val releasedAt: LocalDateTime? = null,
    val finishedAt: LocalDateTime? = null,
    val createdAt: LocalDateTime = LocalDateTime.now(),
    val updatedAt: LocalDateTime = LocalDateTime.now(),
    val acknowledgedAt: LocalDateTime? = null,
    val frequency: Frequency? = null,
    val deadline: Deadline? = null,
    val start: Start? = null,
    val attachments: List<Attachment> = emptyList(),
    val groupId: UUID? = null,
    val originTaskId: UUID? = null,
    val releasedByStaffId: UUID? = null,
    val createdBy: TaskUpdatedBy? = null,
    val finishedBy: TaskUpdatedBy? = null,
    val initiatedByMemberAt: LocalDateTime? = null,
    val favorite: Boolean,
    val caseRecordDetails: List<CaseRecordDetails> = emptyList(),
    val caseId: UUID? = null,
    override val version: Int = 0,
    override val id: UUID = RangeUUID.generate(),
    override var updatedBy: UpdatedBy? = null,
) : Model, HealthInformation, PersonReference, UpdatedByReference, SentenceReference, UpdateRequest {

    companion object {
        private const val TASK_FIELD_NAME = "task"
        private val adherenceValidTypes = listOf(HealthPlanTaskType.REFERRAL, HealthPlanTaskType.TEST_REQUEST)
    }

    constructor(task: HealthPlanTask) : this(
        personId = task.personId,
        healthPlanId = task.healthPlanId,
        appointmentId = task.appointmentId,
        title = task.title,
        description = task.description,
        content = task.content,
        dueDate = task.dueDate,
        status = task.status,
        lastRequesterStaffId = task.lastRequesterStaffId,
        requestersStaffIds = task.requestersStaffIds,
        type = task.type,
        releasedAt = task.releasedAt,
        finishedAt = task.finishedAt,
        createdAt = task.createdAt,
        updatedAt = task.updatedAt,
        acknowledgedAt = task.acknowledgedAt,
        version = task.version,
        id = task.id,
        updatedBy = task.updatedBy,
        frequency = task.frequency,
        deadline = task.deadline,
        start = task.start,
        attachments = task.attachments,
        groupId = task.groupId,
        originTaskId = task.originTaskId,
        releasedByStaffId = task.releasedByStaffId,
        createdBy = task.createdBy,
        finishedBy = task.finishedBy,
        initiatedByMemberAt = task.initiatedByMemberAt,
        favorite = task.favorite,
        caseRecordDetails = task.caseRecordDetails,
        caseId = task.caseId
    )

    @Suppress("UNCHECKED_CAST")
    fun <T : HealthPlanTask> specialize(): T =
        try {
            val jsonObject = gsonHealthPlanTask.toJsonTree(this).asJsonObject

            val task = jsonObject.deepCopy()
            jsonObject.add(TASK_FIELD_NAME, task)

            this.content?.let {
                processParameters(jsonObject, it)
            }

            task.remove(HealthPlanTask::content.name)
            jsonObject.remove(HealthPlanTask::content.name)

            gsonHealthPlanTask.getAdapter(this.type.kClass.java).fromJsonTree(jsonObject) as T
        } catch (ex: Exception) {
            logger.error(
                "HealthPlanTask::specialize: Error to specialize task",
                "health_plan_task_id" to this.id,
                "health_plan_task_type" to this.type,
                "content" to this.content?.map { it.key to it.value },
                ex
            )
            throw ex
        }

    fun generalize(): HealthPlanTask =
        try {
            val jsonElement = gsonHealthPlanTask.toJsonTree(this)
            val jsonObject = jsonElement.asJsonObject

            val task = jsonObject.get(TASK_FIELD_NAME)?.deepCopy()?.asJsonObject ?: jsonObject.deepCopy()
            val content = jsonObject.get(HealthPlanTask::content.name)

            jsonObject.remove(TASK_FIELD_NAME)
            task.remove(TASK_FIELD_NAME)

            if (content == null) {
                HealthPlanTask::class.primaryConstructor?.parameters?.forEach {
                    jsonObject.remove(it.name!!)
                }
            }

            val finalContent = if (content == null || content.isJsonNull) jsonObject else content
            task.add(HealthPlanTask::content.name, finalContent)

            gsonHealthPlanTask.getAdapter(HealthPlanTask::class.java).fromJsonTree(task) as HealthPlanTask
        } catch (ex: Exception) {
            logger.error(
                "HealthPlanTask::generalize: Error to generalize task",
                "health_plan_task_id" to this.id,
                "health_plan_task_type" to this.type,
                "content" to this.content?.map { it.key to it.value },
                ex
            )
            throw ex
        }

    fun fillDate(): HealthPlanTask {
        val newTask = if (this.status == HealthPlanTaskStatus.CREATE) {
            this.copy(status = DRAFT)
        } else this

        val releasedAt = if (newTask.status == ACTIVE) {
            newTask.releasedAt ?: LocalDateTime.now()
        } else newTask.releasedAt

        val finishedAt = if (newTask.status == DONE) {
            newTask.finishedAt ?: LocalDateTime.now()
        } else null

        val start = (newTask.start ?: Start(StartType.IMMEDIATE)).fillDate(releasedAt)

        val deadline = if (newTask.deadline?.date != null) {
            newTask.deadline
        } else {
            newTask.deadline?.fillDate(start.date)
        } ?: Deadline(
            unit = PeriodUnit.DAY,
            quantity = 30
        ).fillDate(start.date ?: LocalDateTime.now())

        val dueDate = newTask.dueDate ?: deadline.date?.toLocalDate()

        return newTask.copy(
            releasedAt = releasedAt,
            start = start,
            deadline = deadline,
            finishedAt = finishedAt,
            dueDate = dueDate
        )
    }

    protected open fun hasRequiredFields() {
        if (caseRecordDetails.isEmpty()) throw InvalidArgumentException(
            "only tasks with case record details can be published",
            "invalid_task_state"
        )
    }

    fun checkRequiredField() {
        when (type) {
            HealthPlanTaskType.PRESCRIPTION -> specialize<Prescription>()
            HealthPlanTaskType.EATING -> specialize<GenericTask>()
            HealthPlanTaskType.PHYSICAL_ACTIVITY -> specialize<GenericTask>()
            HealthPlanTaskType.SLEEP -> specialize<GenericTask>()
            HealthPlanTaskType.MOOD -> specialize<GenericTask>()
            HealthPlanTaskType.OTHERS -> specialize<GenericTask>()
            HealthPlanTaskType.TEST_REQUEST -> specialize<TestRequest>()
            HealthPlanTaskType.REFERRAL -> specialize<Referral>()
            HealthPlanTaskType.EMERGENCY -> specialize<Emergency>()
            HealthPlanTaskType.SCHEDULING -> specialize<Scheduling>()
            HealthPlanTaskType.FOLLOW_UP_REQUEST -> specialize<FollowUpRequest>()
            HealthPlanTaskType.SURGERY_PRESCRIPTION -> specialize<SurgeryPrescription>()
        }.hasRequiredFields()
    }

    private fun processParameters(jsonObject: JsonObject, map: Map<String, Any?>) =
        map.forEach { (fieldName, value) ->
            jsonObject.add(fieldName, parameterValue(value))
        }

    @Suppress("UNCHECKED_CAST")
    private fun parameterValue(value: Any?): JsonElement? =
        when {
            value == null -> null
            value::class.isData -> {
                JsonObject().let { nested ->
                    processParameters(nested, value.asMap())
                    nested
                }
            }

            value is Map<*, *> -> {
                JsonObject().let { nested ->
                    processParameters(nested, value as Map<String, Any?>)
                    nested
                }
            }

            value is List<*> -> {
                JsonArray().let { array ->
                    value.map { arrayValue ->
                        parameterValue(arrayValue)?.let { array.add(it) }
                    }
                    array
                }
            }

            else -> gsonHealthPlanTask.toJsonTree(value)
        }

    fun isDraft() = status == DRAFT
    fun isActive() = status == ACTIVE
    fun isDone() = status == DONE
    fun isDeleted() = status == DELETED
    fun isDeletedByMember() = status == DELETED_BY_MEMBER
    fun isDeletedByStaff() = status == DELETED_BY_STAFF
    fun isFinalStatus() = isDeleted() || isDeletedByMember() || isDone() || isDeletedByStaff()

    fun isExpired() = dueDate?.isBefore(LocalDate.now()) ?: false

    fun shouldCheckAdherence() = type in adherenceValidTypes

    fun isReferral() = type == HealthPlanTaskType.REFERRAL

    fun isEmergency() = type == HealthPlanTaskType.EMERGENCY

    fun isSurgeryPrescription() = type == HealthPlanTaskType.SURGERY_PRESCRIPTION

    fun isSleep() = type == HealthPlanTaskType.SLEEP

    fun isOther() = type == HealthPlanTaskType.OTHERS

    fun isMood() = type == HealthPlanTaskType.MOOD

    fun isEating() = type == HealthPlanTaskType.EATING

    fun isPhysicalActivity() = type == HealthPlanTaskType.PHYSICAL_ACTIVITY

    fun isTestRequest() = type == HealthPlanTaskType.TEST_REQUEST

    fun isPrescription() = type == HealthPlanTaskType.PRESCRIPTION

    fun isFollowUpRequest() = type == HealthPlanTaskType.FOLLOW_UP_REQUEST

    fun getStaffIds() =
        (this.requestersStaffIds + listOfNotNull(lastRequesterStaffId, releasedByStaffId, finishedBy?.id)).distinct()

    override fun fullSentence() =
        SentenceReference.fullSentence(
            title = this.title,
            frequency = this.frequency,
            deadline = this.deadline,
            start = this.start
        )

    override fun compactSentence() = ""

    override fun equals(other: Any?): Boolean {
        if (this === other) return true
        if (javaClass != other?.javaClass) return false

        other as HealthPlanTask

        if (id != other.id) return false

        if (mapHasDifference(content, other.content)) return false

        if (title != other.title) return false
        if (description != other.description) return false
        if (dueDate != other.dueDate) return false
        if (status != other.status) return false
        if (frequency != other.frequency) return false
        if (deadline != other.deadline) return false
        if (start != other.start) return false
        if (attachments != other.attachments) return false
        if (initiatedByMemberAt != other.initiatedByMemberAt) return false
        if (caseId != other.caseId) return false
        if (caseRecordDetails != other.caseRecordDetails) return false

        return true
    }

    override fun hashCode(): Int {
        var result = personId.hashCode()
        result = 31 * result + (healthPlanId?.hashCode() ?: 0)
        result = 31 * result + (title?.hashCode() ?: 0)
        result = 31 * result + (description?.hashCode() ?: 0)
        result = 31 * result + (content?.map { it.key.hashCode() + it.value.hashCode() }?.sum()?.hashCode() ?: 0)
        result = 31 * result + (dueDate?.hashCode() ?: 0)
        result = 31 * result + status.hashCode()
        result = 31 * result + type.hashCode()
        result = 31 * result + (frequency?.hashCode() ?: 0)
        result = 31 * result + (deadline?.hashCode() ?: 0)
        result = 31 * result + (start?.hashCode() ?: 0)
        result = 31 * result + attachments.hashCode()
        result = 31 * result + id.hashCode()
        result = 31 * result + (groupId?.hashCode() ?: 0)
        result = 31 * result + (caseId?.hashCode() ?: 0)
        return result
    }

    private fun mapHasDifference(current: Map<*, *>?, other: Map<*, *>?): Boolean {
        if (current == null && other == null) return false
        if (current == null && other?.isEmpty() == true) return false
        if (current?.isEmpty() == true && other == null) return false
        if (current == null && other != null) return true
        if (current != null && other == null) return true
        if (current?.keys?.size != other?.keys?.size) return true

        return current?.map {
            val currentValue = it.value
            val otherValue = other?.get(it.key)
            if (currentValue is Map<*, *>) {
                mapHasDifference(currentValue, otherValue?.asMap())
            } else currentValue != otherValue
        }?.find { it } ?: false
    }

}

fun HealthPlanTask.copy(
    personId: PersonId = this.personId,
    healthPlanId: UUID? = this.healthPlanId,
    appointmentId: UUID? = this.appointmentId,
    title: String? = this.title,
    description: String? = this.description,
    content: Map<String, Any>? = this.content,
    dueDate: LocalDate? = this.dueDate,
    status: HealthPlanTaskStatus = this.status,
    lastRequesterStaffId: UUID = this.lastRequesterStaffId,
    requestersStaffIds: Set<UUID> = this.requestersStaffIds,
    type: HealthPlanTaskType = this.type,
    releasedAt: LocalDateTime? = this.releasedAt,
    finishedAt: LocalDateTime? = this.finishedAt,
    createdAt: LocalDateTime = this.createdAt,
    updatedAt: LocalDateTime = this.updatedAt,
    acknowledgedAt: LocalDateTime? = this.acknowledgedAt,
    version: Int = this.version,
    id: UUID = this.id,
    updatedBy: UpdatedBy? = this.updatedBy,
    frequency: Frequency? = this.frequency,
    deadline: Deadline? = this.deadline,
    start: Start? = this.start,
    attachments: List<Attachment> = this.attachments,
    groupId: UUID? = this.groupId,
    originTaskId: UUID? = this.originTaskId,
    releasedByStaffId: UUID? = this.releasedByStaffId,
    createdBy: TaskUpdatedBy? = this.createdBy,
    finishedBy: TaskUpdatedBy? = this.finishedBy,
    initiatedByMemberAt: LocalDateTime? = this.initiatedByMemberAt,
    favorite: Boolean = this.favorite,
    caseRecordDetails: List<CaseRecordDetails> = this.caseRecordDetails,
    caseId: UUID? = this.caseId
): HealthPlanTask =
    HealthPlanTask(
        personId = personId,
        healthPlanId = healthPlanId,
        appointmentId = appointmentId,
        title = title,
        description = description,
        content = content,
        dueDate = dueDate,
        status = status,
        lastRequesterStaffId = lastRequesterStaffId,
        requestersStaffIds = requestersStaffIds,
        type = type,
        releasedAt = releasedAt,
        finishedAt = finishedAt,
        createdAt = createdAt,
        updatedAt = updatedAt,
        acknowledgedAt = acknowledgedAt,
        version = version,
        id = id,
        updatedBy = updatedBy,
        frequency = frequency,
        deadline = deadline,
        start = start,
        attachments = attachments,
        groupId = groupId,
        originTaskId = originTaskId,
        releasedByStaffId = releasedByStaffId,
        createdBy = createdBy,
        finishedBy = finishedBy,
        initiatedByMemberAt = initiatedByMemberAt,
        favorite = favorite,
        caseRecordDetails = caseRecordDetails,
        caseId = caseId
    )

enum class HealthPlanTaskStatus {
    CREATE, DRAFT, ACTIVE, DONE, DELETED, DELETED_BY_MEMBER, DELETED_BY_STAFF
}

enum class HealthPlanTaskType(val kClass: KClass<out HealthPlanTask>) {
    PRESCRIPTION(Prescription::class),
    EATING(GenericTask::class),
    PHYSICAL_ACTIVITY(GenericTask::class),
    SLEEP(GenericTask::class),
    MOOD(GenericTask::class),
    OTHERS(GenericTask::class),
    TEST_REQUEST(TestRequest::class),
    REFERRAL(Referral::class),
    EMERGENCY(Emergency::class),
    SCHEDULING(Scheduling::class),
    FOLLOW_UP_REQUEST(FollowUpRequest::class),
    SURGERY_PRESCRIPTION(SurgeryPrescription::class)
}

enum class ProfessionalType(val description: String) {
    HEALTH_CARE_TEAM_PHYSICIAN("Médica(o) do time de saúde"),
    HEALTH_CARE_TEAM_NURSE("Enfermeira(o) do time de saúde"),
    NUTRITIONIST("Nutricionista"),
    PHYSICAL_EDUCATOR("Preparador Físico"),
    PHYSIOTHERAPIST("Fisioterapeuta")
}

data class Prescription(
    val dose: Dose?,
    val action: ActionType?,
    val routeOfAdministration: RouteOfAdministration?,
    val medicine: PrescriptionMedicine?,
    val packing: Int,
    val shortId: String? = null,
    val token: String? = null,
    val task: HealthPlanTask,
    val sentenceEdited: Boolean? = null,
    val medicineEndAt: LocalDateTime? = null,
    val digitalPrescription: DigitalPrescription? = null,
) : HealthPlanTask(task) {
    override fun fullSentence() =
        SentenceReference.fullSentence(
            action = this.action,
            dose = this.dose?.quantity,
            unit = this.dose?.unit,
            routeOfAdministration = this.routeOfAdministration,
            frequency = this.frequency,
            deadline = this.deadline,
            start = this.start,
            description = this.task.description
        )

    override fun compactSentence() =
        SentenceReference.compactSentence(
            dose = this.dose?.quantity,
            action = this.action,
            unit = this.dose?.unit,
            frequency = this.frequency,
        )

    fun formattedTitle() = "$title - $packing ${packing.takeIf { it > 1 }?.let { "embalagens" } ?: "embalagem"}"
}

data class Dose(
    val unit: MedicineUnit,
    val quantity: Float
)

data class PrescriptionMedicine(
    val id: UUID? = null,
    val drugId: Int? = null,
    val name: String,
    val unit: MedicineUnit? = null,
    val quantity: String? = null,
    val concentration: String? = null,
    val concentrationQuantity: String? = null,
    val drug: String? = null,
    val type: PrescriptionMedicineType
) {
    fun getFullName() = "$name $concentrationQuantity$concentration, $quantity ${getUnit()?.capitalize()}"

    fun getUnit() = quantity?.let {
        val value = it.replace(Regex("\\D"), "").toFloat()
        unit?.getDescription(value)
    }

}

data class DigitalPrescription(
    val link: String,
    val digits: String,
    val documentLink: String,
    val signed: Int = 0,
    val unit: String? = null,
    val quantity: Int? = null,
    val receituarioId: String? = null,
)

data class GenericTask(
    val task: HealthPlanTask
) : HealthPlanTask(task)

data class TestRequest(
    val code: String?,
    val task: HealthPlanTask,
    val shortId: String? = null,
    val token: String? = null,
    val cityId: String? = null,
    val memberGuidance: String? = null
) : HealthPlanTask(task) {
    fun formattedTitle() =
        "$title${if (description.isNullOrBlank()) "" else " - $description"}"
}

// MedicalSpecialty.name goes on HealthPlanTask.title
data class Referral(
    val suggestedSpecialist: SuggestedSpecialist? = null,
    val diagnosticHypothesis: String?,
    val referenceLetterSentDate: LocalDateTime? = null,
    val genericReferral: Boolean? = null,
    val specialty: ReferralSpecialty? = null,
    val subSpecialty: ReferralSpecialty? = null,
    val cityId: String? = null,
    val sessionsQuantity: Int? = null,
    val followUpMaxQuantity: Int? = null,
    val isAdvancedAccess: Boolean? = null,
    val task: HealthPlanTask,
    val shortId: String? = null,
    val token: String? = null,
) : HealthPlanTask(task) {
    fun therapySentence() =
        "Encaminho para ${specialty?.name} ${subSpecialty?.name} para $sessionsQuantity sessões${task.caseRecordDetails.getOrNull(0)
            ?.let { ", por ${it.description.getDiseaseDescription()}." }
            ?: "."}"

    fun getSessions(): Int =
        if (followUpMaxQuantity != null && sessionsQuantity != null) minOf(followUpMaxQuantity, sessionsQuantity)
        else followUpMaxQuantity ?: sessionsQuantity ?: 1
}

data class ReferralSpecialty(
    val name: String,
    val id: UUID? = null
) : JsonSerializable

data class SuggestedSpecialist(
    val id: UUID,
    val name: String? = null,
    val speciality: String? = null,
    val type: SpecialistType,
    val origin: SuggestionOrigin? = SuggestionOrigin.STAFF,
    val tier: SpecialistTier? = null,
    val memberTier: ReferralMemberTier? = null
) : JsonSerializable

data class ReferralMemberProduct(
    val id: UUID,
    val name: String
)

data class ReferralSuggestedSpecialistReason(
    val reason: ReferralSuggestedSpecialistReasonType,
    val description: String?
)

data class ReferralMemberTier(
    val isInMemberTier: Boolean = true,
    val memberProduct: ReferralMemberProduct?,
    val suggestedReason: ReferralSuggestedSpecialistReason? = null
)

enum class ReferralSuggestedSpecialistReasonType {
    POST_OP_HOSPITALIZATION_CARE,
    MEMBER_SECOND_OPINION,
    SPECIALIST_SECOND_OPINION,
    ALICE_SECOND_OPINION,
    OTHER
}

data class TaskUpdatedBy(
    val id: UUID? = null,
    val source: TaskSourceType
) : JsonSerializable

enum class TaskSourceType(val description: String) {
    STAFF("Staff"),
    MEMBER("Membro"),
    SYSTEM("Sistema"),
    TEMPLATE("Modelo de Tarefa"),
    COUNTER_REFERRAL("Tarefas automáticas geradas por contra referência"),
    EXTERNAL_REFERRAL("Tarefas automáticas geradas por encaminhamento externo"),
}

data class EmergencySpecialty(
    val name: String,
    val id: UUID? = null
) : JsonSerializable

data class Emergency(
    val diagnosticHypothesis: String?,
    val referenceLetterSentDate: LocalDateTime? = null,
    val specialty: EmergencySpecialty? = null,
    val cityId: String? = null,
    val task: HealthPlanTask
) : HealthPlanTask(task) {
    override fun hasRequiredFields() {
        super.hasRequiredFields()

        if (cityId == null) throw InvalidArgumentException("City ID is null", "invalid_task_state")
        if (specialty?.id == null) throw InvalidArgumentException("Medical specialty is null", "invalid_task_state")
    }
}

data class Scheduling(
    val professional: ProfessionalType?,
    val task: HealthPlanTask
) : HealthPlanTask(task)

data class FollowUpRequest(
    val providerType: FollowUpProviderType?,
    val followUpInterval: FollowUpInterval?,
    val task: HealthPlanTask,
) : HealthPlanTask(task) {
    override fun hasRequiredFields() {
        super.hasRequiredFields()

        if (followUpInterval == null) throw InvalidArgumentException("Follow up interval is null", "invalid_task_state")
    }
}

data class SurgeryPrescription(
    val reason: String?,
    val expectedDate: String?,
    val provider: Hospital?,
    val procedures: List<SurgicalProcedure>,
    val task: HealthPlanTask,
) : HealthPlanTask(task) {
    override fun hasRequiredFields() {
        super.hasRequiredFields()

        when {
            expectedDate == null -> throw InvalidArgumentException("Expected Date is null", "invalid_task_state")
            provider == null -> throw InvalidArgumentException("Provider is null", "invalid_task_state")
            procedures.isEmpty() -> throw InvalidArgumentException("Procedure list is empty", "invalid_task_state")
        }
    }
}

data class Hospital(
    val name: String,
    val id: UUID,
)

data class SurgicalProcedure(
    val description: String,
    val tussCode: String,
)

enum class SuggestionOrigin {
    STAFF, SYSTEM
}

enum class FollowUpProviderType(val description: String) {
    REMOTE("Digital"), ON_SITE("Presencial"),
}

data class FollowUpInterval(
    val type: FollowUpIntervalType,
    val unit: PeriodUnit? = null,
    val quantity: Int? = null,
)

enum class FollowUpIntervalType {
    DATE_INTERVAL,
    AFTER_THERAPY_SESSIONS,
    AFTER_FISIOTHERAPY_SESSIONS,
    AFTER_MEDICAL_TREATMENT,
    AFTER_MEDICAL_DATE_INTERVAL,
    AFTER_TEST_RESULTS,
}
