package br.com.alice.data.layer.services

import br.com.alice.common.core.PersonId
import br.com.alice.common.core.PersonReference
import br.com.alice.common.service.data.client.TsVector
import br.com.alice.data.layer.models.BeneficiaryCompiledViewModel
import br.com.alice.data.layer.models.BeneficiaryModel
import br.com.alice.data.layer.models.BudNode
import br.com.alice.data.layer.models.LegalGuardianAssociationModel
import br.com.alice.data.layer.models.MemberModel
import br.com.alice.data.layer.models.PersonModel
import br.com.alice.data.layer.models.PromoCodeModel
import br.com.alice.data.layer.models.ServiceScriptNodeType
import br.com.alice.data.layer.tables.AiAssistantTable
import br.com.alice.data.layer.tables.AiModelTable
import br.com.alice.data.layer.tables.AiSetupTable
import br.com.alice.data.layer.tables.AiTextInferenceFeedbackTable
import br.com.alice.data.layer.tables.AiTextInferenceTable
import br.com.alice.data.layer.tables.ConsolidatedHrCompanyReportTable
import br.com.alice.data.layer.tables.HealthDataOverviewTable
import br.com.alice.data.layer.tables.HealthLogicActionRecommendationTable
import br.com.alice.data.layer.tables.HealthLogicAdherenceTable
import br.com.alice.data.layer.tables.MemberTable
import br.com.alice.data.layer.tables.PersonNonPiiReference
import br.com.alice.data.layer.tables.PersonPiiReference
import br.com.alice.data.layer.tables.ScreenDetailTable
import br.com.alice.data.layer.tables.ServiceScriptNodeTable
import br.com.alice.data.layer.tables.Table
import kotlinx.coroutines.runBlocking
import kotlinx.serialization.Transient
import org.assertj.core.api.Assertions.assertThat
import org.reflections.Reflections
import org.reflections.scanners.SubTypesScanner
import kotlin.reflect.KClass
import kotlin.reflect.KProperty1
import kotlin.reflect.KType
import kotlin.reflect.full.createType
import kotlin.reflect.full.declaredMemberProperties
import kotlin.reflect.full.findAnnotation
import kotlin.reflect.full.isSubclassOf
import kotlin.reflect.jvm.jvmErasure
import kotlin.test.Test

class DataServiceTest {

    private val servicesPackageName = "br.com.alice.data.layer.services"
    private val tablePackageName = "br.com.alice.data.layer.tables"
    private val classDefaultSuffix = "DataServiceImpl"

    private val excludedServices = listOf(
        PolicyDescriptionDataServiceImpl::class.qualifiedName!!,
        BaseDataServiceImpl::class.qualifiedName!!
    )

    // to override names or lightweight models
    private val overrideTables = mapOf(
        "AI" to AiModelTable::class,
        "AIAssistant" to AiAssistantTable::class,
        "AISetup" to AiSetupTable::class,
        "AITextInference" to AiTextInferenceTable::class,
        "AITextInferenceFeedback" to AiTextInferenceFeedbackTable::class,
        "AppContentScreenDetail" to ScreenDetailTable::class,
        "BudNode" to ServiceScriptNodeTable::class,
        "ConsolidatedHRCompanyReport" to ConsolidatedHrCompanyReportTable::class,
        "HDataOverview" to HealthDataOverviewTable::class,
        "HLActionRecommendation" to HealthLogicActionRecommendationTable::class,
        "HLAdherence" to HealthLogicAdherenceTable::class,
        "MemberLightweight" to MemberTable::class
    )

    private val overrideFields = mapOf<KProperty1<out Any, *>, Pair<KType, KType>>(
        BeneficiaryModel::parentPerson to (PersonId::class.createType(nullable = true) to PersonPiiToken::class.createType(nullable = true)),
        MemberModel::parentPerson to (PersonId::class.createType(nullable = true) to PersonPiiToken::class.createType(nullable = true)),
        LegalGuardianAssociationModel::guardianId to (PersonId::class.createType(nullable = false) to PersonPiiToken::class.createType(nullable = false)),
        PromoCodeModel::ownerPersonId to (PersonId::class.createType(nullable = true) to PersonPiiToken::class.createType(nullable = true)),
        BeneficiaryCompiledViewModel::parentPersonId to (PersonId::class.createType(nullable = true) to PersonPiiToken::class.createType(nullable = true)),
        BudNode::type to (BudNode.BudNodeType::class.createType() to ServiceScriptNodeType::class.createType()),
        PersonModel::id to (PersonId::class.createType() to PersonPiiToken::class.createType())
    )

    private val reflections = Reflections(servicesPackageName, SubTypesScanner(false))
    private val dataServices = reflections.getSubTypesOf(BaseDataServiceImpl::class.java)

    @Test
    fun `#validate if all data service is a extension of BaseDataServiceImpl`() {
        val dataServiceList =
            reflections
                .allTypes
                .filter { it.contains(classDefaultSuffix) && !excludedServices.contains(it) }
                .sorted()

        val dataServiceImplList = dataServices.map { it.name }.sorted()

        assertThat(dataServiceList).usingRecursiveComparison().isEqualTo(dataServiceImplList)
    }

    @Test
    fun `#validates compatibility between models and tables`() = runBlocking {
        val kotlinDataServices = dataServices.map { it.kotlin }

        val models = kotlinDataServices
            .map { it.supertypes.first().arguments.first().type!!.jvmErasure }
            .sortedBy { it.simpleName }

        val tables = Reflections(tablePackageName)
            .getSubTypesOf(Table::class.java)
            .map { it.kotlin }
            .associateBy { it.replacedTableName() }

        models.forEach { model ->

            val name = model.replacedModelName()

            val table = tables[name] ?: overrideTables.getValue(name)

            val modelParameters = model.declaredMemberProperties.filterNot { it.findAnnotation<Transient>() != null }

            val tableParameters = table.declaredMemberProperties.associate { it.name to it.returnType }

            modelParameters.forEach { modelParameter ->
                val tableParameterType = tableParameters[modelParameter.name]
                    ?: throw Exception("${model.simpleName}.${modelParameter.name} missing in ${table.simpleName}. Annotate field `${model.simpleName}.${modelParameter.name}` with `@kotlinx.serialization.Transient`")

                compareModels(model, modelParameter, tableParameterType, table)
            }
        }
    }

    private fun compareModels(
        model: KClass<*>,
        modelParameter: KProperty1<out Any, *>,
        tableParameterType: KType,
        table: KClass<out Table<*>>
    ) {

        overrideFields[modelParameter]?.let { (overrideModelField, overrideTableField) ->
            assertThat(modelParameter.returnType)
                .`as`("Custom comparing field `${modelParameter.name}` from ${model.simpleName}")
                .isEqualTo(overrideModelField)
            assertThat(tableParameterType)
                .`as`("Custom comparing field `${modelParameter.name}` from ${table.simpleName}")
                .isEqualTo(overrideTableField)
        } ?: comparePersonId(model, modelParameter, tableParameterType, table) ?: compareSearchTokens(
            model,
            modelParameter,
            tableParameterType
        ) ?: assertThat(modelParameter.returnType)
            .`as`("Comparing field `${modelParameter.name}` from ${model.simpleName} and ${table.simpleName}")
            .isEqualTo(tableParameterType)
    }

    private fun comparePersonId(
        model: KClass<*>,
        modelParameter: KProperty1<out Any, *>,
        tableParameterType: KType,
        table: KClass<out Table<*>>
    ) = if (modelParameter.name == PersonReference::personId.name) {

        if (model.isSubclassOf(PersonReference::class))
            assertThat(modelParameter.returnType)
                .`as`("`personId` field from `PersonReference` always must be not nullable `PersonId`")
                .isEqualTo(PersonId::class.createType())
        else assertThat(modelParameter.returnType.isMarkedNullable)
            .`as`("`personId` field of a Model that is not `PersonReference` always must be nullable")
            .isTrue()

        val type = if (table.isSubclassOf(PersonPiiReference::class)) PersonPiiToken::class
        else if (table.isSubclassOf(PersonNonPiiReference::class)) PersonNonPiiToken::class
        else null

        if (type != null) assertThat(tableParameterType)
            .`as`("`personId` field from `PersonPiiReference/PersonNonPiiReference` always must be not nullable `PersonPiiToken/PersonNonPiiToken`")
            .isEqualTo(type.createType())
        else assertThat(tableParameterType.isMarkedNullable)
            .`as`("`personId` field of a Table that is not `PersonPiiReference/PersonNonPiiReference` always must be nullable")
            .isTrue()
    } else null

    private fun compareSearchTokens(
        model: KClass<*>,
        modelParameter: KProperty1<out Any, *>,
        tableParameterType: KType
    ) = if (modelParameter.name == "searchTokens") {
        assertThat(modelParameter.returnType)
            .`as`("${model.simpleName}: the `searchTokens` in model must to be a `String?`")
            .isEqualTo(String::class.createType(nullable = true))

        assertThat(tableParameterType)
            .`as`("The `searchTokens` in table must to be a `TsVector?`")
            .isEqualTo(TsVector::class.createType(nullable = true))
    } else null


    private fun KClass<*>.replacedModelName() = simpleName!!.replace("Model", "")
    private fun KClass<*>.replacedTableName() = simpleName!!.replace("Table", "")
}
