package br.com.alice.data.layer.services

import br.com.alice.common.BeneficiaryType
import br.com.alice.common.Brand
import br.com.alice.common.RangeUUID
import br.com.alice.common.UpdatedBy
import br.com.alice.common.core.PersonId
import br.com.alice.common.data.dsl.matchers.ResultAssert.Companion.assertThat
import br.com.alice.common.service.data.dsl.and
import br.com.alice.data.layer.helpers.DataServiceTestHelper
import br.com.alice.data.layer.helpers.TestTableFactory
import br.com.alice.data.layer.models.BeneficiaryCancelationReason
import br.com.alice.data.layer.models.BeneficiaryContractType
import br.com.alice.data.layer.models.BeneficiaryModel
import br.com.alice.data.layer.models.BeneficiaryOnboardingModel
import br.com.alice.data.layer.models.GracePeriodType
import br.com.alice.data.layer.models.GracePeriodTypeReason
import br.com.alice.data.layer.models.MemberStatus
import br.com.alice.data.layer.models.ParentBeneficiaryRelationType
import br.com.alice.data.layer.repositories.JdbiRepository
import br.com.alice.data.layer.tables.BeneficiaryTable
import br.com.alice.data.layer.tables.CompanyContractTable
import br.com.alice.data.layer.tables.CompanySubContractTable
import br.com.alice.data.layer.tables.CompanyTable
import com.github.kittinunf.result.map
import kotlinx.coroutines.runBlocking
import java.time.LocalDate
import java.time.LocalDateTime
import java.util.UUID
import kotlin.test.BeforeTest
import kotlin.test.Test

class BeneficiaryDataServiceTest : DataServiceTestHelper() {
    private val beneficiaryRepo = JdbiRepository(mainJdbi, BeneficiaryTable::class)
    private val subContractRepo = JdbiRepository(mainJdbi, CompanySubContractTable::class)
    private val contractRepo = JdbiRepository(mainJdbi, CompanyContractTable::class)
    private val companyRepo = JdbiRepository(mainJdbi, CompanyTable::class)

    private val beneficiaryDataService = BeneficiaryModelDataServiceClient(httpInvoker)

    private val personTokenService = PersonTokenServiceImpl(tokenJdbi) as PersonTokenService
    private val personId = PersonId()
    private val company = TestTableFactory.buildCompanyTable()
    private val contract = TestTableFactory.buildCompanyContractTable()
    private val subContract = TestTableFactory.buildCompanySubContractTable(companyId = company.id, contractId = contract.id)
    private val personToken = personTokenService.createForPersonId(personId).get()

    @BeforeTest
    fun setup() {
        beneficiaryRepo.truncate()
        subContractRepo.truncate()
        contractRepo.truncate()
        companyRepo.truncate()
        companyRepo.add(company)
        contractRepo.add(contract)
        subContractRepo.add(subContract)
    }

    @Test
    fun `#add new beneficiary`() = runBlocking<Unit> {
        val beneficiary = buildBeneficiaryModel(
            personId = personToken.personId,
            companyId = company.id,
            type = BeneficiaryType.EMPLOYEE,
            companySubContractId = subContract.id,
        )
        assertThat(beneficiaryDataService.add(beneficiary)).isSuccess()
    }

    @Test
    fun `#get beneficiary`() = runBlocking {
        val companyId = RangeUUID.generate()

        val beneficiary = buildBeneficiaryModel(
            personId = personToken.personId,
            companyId = companyId,
            type = BeneficiaryType.EMPLOYEE,
            companySubContractId = subContract.id,
            updatedBy = UpdatedBy(userType = "Unauthenticated", userId = "undefined", environmentName = "undefined")
        )

        beneficiaryDataService.add(beneficiary)
        val retrievedBeneficiary = beneficiaryDataService.get(beneficiary.id)

        assertThat(retrievedBeneficiary).isSuccessWithDataIgnoringGivenFields(beneficiary, "createdAt", "updatedAt")
    }

    @Test
    fun `#countUnique beneficiaries by company`() = runBlocking {
        val companyId = RangeUUID.generate()

        val personId2 = PersonId()
        val personToken2 = personTokenService.createForPersonId(personId2).get()

        val personId3 = PersonId()
        val personToken3 = personTokenService.createForPersonId(personId3).get()

        val beneficiary1 = buildBeneficiaryModel(
            personId = personToken.personId,
            companyId = companyId,
            type = BeneficiaryType.EMPLOYEE,
            companySubContractId = subContract.id,
        )
        val beneficiary2 = buildBeneficiaryModel(
            personId = personToken.personId,
            companyId = companyId,
            type = BeneficiaryType.EMPLOYEE,
            companySubContractId = subContract.id,
        )
        val beneficiary3 = buildBeneficiaryModel(
            personId = personToken2.personId,
            companyId = companyId,
            type = BeneficiaryType.EMPLOYEE,
            companySubContractId = subContract.id,
        )
        val beneficiary4 = buildBeneficiaryModel(
            personId = personToken2.personId,
            companyId = companyId,
            type = BeneficiaryType.EMPLOYEE,
            companySubContractId = subContract.id,
        )
        val beneficiary5 = buildBeneficiaryModel(
            personId = personToken3.personId,
            companyId = companyId,
            type = BeneficiaryType.EMPLOYEE,
            companySubContractId = subContract.id,
        )

        beneficiaryDataService.add(beneficiary1)
        beneficiaryDataService.add(beneficiary2)
        beneficiaryDataService.add(beneficiary3)
        beneficiaryDataService.add(beneficiary4)
        beneficiaryDataService.add(beneficiary5)

        val countUniqueBeneficiaries = beneficiaryDataService
            .countGrouped { where { this.companyId.eq(companyId) }.groupBy { listOf(this.personId) } }
            .map { it.size }

        assertThat(countUniqueBeneficiaries).isSuccessWithData(3)
    }

    @Test
    fun `#update beneficiary`() = runBlocking {
        val companyId = RangeUUID.generate()

        val beneficiary = buildBeneficiaryModel(
            personId = personToken.personId,
            companyId = companyId,
            type = BeneficiaryType.EMPLOYEE,
            companySubContractId = subContract.id,
            updatedBy = UpdatedBy(userType = "Unauthenticated", userId = "undefined", environmentName = "undefined")
        )
        val updatedBeneficiary = beneficiary.copy(
            type = BeneficiaryType.DEPENDENT,
            gracePeriodBaseDate = LocalDate.now()
        )

        val resultAdd = beneficiaryDataService.add(beneficiary)
        assertThat(resultAdd).isSuccessWithDataIgnoringGivenFields(beneficiary, "createdAt", "updatedAt")

        val resultUpdate = beneficiaryDataService.update(updatedBeneficiary)
        assertThat(resultUpdate).isSuccessWithDataIgnoringGivenFields(updatedBeneficiary, "createdAt", "updatedAt", "version")
    }

    @Test
    fun `#adds duplicated beneficiary should allow`() = runBlocking<Unit> {
        val companyId = RangeUUID.generate()

        val beneficiary = buildBeneficiaryModel(
            personId = personToken.personId,
            companyId = companyId,
            type = BeneficiaryType.EMPLOYEE,
            companySubContractId = subContract.id,
        )

        val duplicatedBeneficiary = buildBeneficiaryModel(
            personId = personToken.personId,
            companyId = companyId,
            type = BeneficiaryType.EMPLOYEE,
            companySubContractId = subContract.id,
        )

        assertThat(beneficiaryDataService.add(beneficiary)).isSuccess()
        assertThat(beneficiaryDataService.add(duplicatedBeneficiary)).isSuccess()
    }

    @Test
    fun `#ActivatedAtField - should return the expected`() = runBlocking {
        val expectedBeneficiary = buildBeneficiaryModel(
            personId = personToken.personId,
            activatedAt = LocalDateTime.of(2021, 10,10,0, 0, 0),
            companySubContractId = subContract.id,
            updatedBy = UpdatedBy(userType = "Unauthenticated", userId = "undefined", environmentName = "undefined")
        )

        val futureBeneficiary = buildBeneficiaryModel(
            activatedAt = LocalDateTime.now().plusDays(1),
            companySubContractId = subContract.id,
            updatedBy = UpdatedBy(userType = "Unauthenticated", userId = "undefined", environmentName = "undefined")
        )

        beneficiaryDataService.add(expectedBeneficiary)
        beneficiaryDataService.add(futureBeneficiary)

        val actual = beneficiaryDataService.find { where { this.activatedAt.lessEq(LocalDateTime.now()) } }
        assertThat(actual).isSuccessWithDataIgnoringGivenFields(listOf(expectedBeneficiary), "createdAt", "updatedAt")
    }

    @Test
    fun `#CanceledAt - should return the expected`() = runBlocking {
        val expectedBeneficiary = buildBeneficiaryModel(
            personId = personToken.personId,
            canceledAt = LocalDateTime.of(2021, 10,10,0, 0, 0),
            companySubContractId = subContract.id,
            updatedBy = UpdatedBy(userType = "Unauthenticated", userId = "undefined", environmentName = "undefined")
        )

        val futureBeneficiary = buildBeneficiaryModel(
            canceledAt = LocalDateTime.now().plusDays(1),
            companySubContractId = subContract.id,
            updatedBy = UpdatedBy(userType = "Unauthenticated", userId = "undefined", environmentName = "undefined")
        )

        val beneficiaryWithoutCanceledAt = buildBeneficiaryModel(
            canceledAt = null,
            companySubContractId = subContract.id,
            updatedBy = UpdatedBy(userType = "Unauthenticated", userId = "undefined", environmentName = "undefined")
        )

        beneficiaryDataService.add(expectedBeneficiary)
        beneficiaryDataService.add(futureBeneficiary)
        beneficiaryDataService.add(beneficiaryWithoutCanceledAt)

        val actual = beneficiaryDataService.find { where { this.canceledAt.lessEq(LocalDateTime.now()) } }
        assertThat(actual).isSuccessWithDataIgnoringGivenFields(listOf(expectedBeneficiary), "createdAt", "updatedAt")
    }

    @Test
    fun `#FindByCompanyIdPersonIdsAndBeneficiaryType - should return the expected`() = runBlocking {
        val companyId = RangeUUID.generate()

        val personId2 = PersonId()
        val personToken2 = personTokenService.createForPersonId(personId2).get()

        val personId3 = PersonId()
        val personToken3 = personTokenService.createForPersonId(personId3).get()

        val personIds = listOf(personId, personId2, personId3)

        val beneficiary1 = buildBeneficiaryModel(
            personId = personToken.personId,
            companyId = companyId,
            type = BeneficiaryType.EMPLOYEE,
            canceledAt = null,
            companySubContractId = subContract.id,
        )
        val beneficiary2 = buildBeneficiaryModel(
            personId = personToken2.personId,
            companyId = companyId,
            type = BeneficiaryType.EMPLOYEE,
            canceledAt = LocalDateTime.now().minusDays(1),
            companySubContractId = subContract.id,
        )
        val beneficiary3 = buildBeneficiaryModel(
            personId = personToken3.personId,
            companyId = companyId,
            type = BeneficiaryType.DEPENDENT,
            canceledAt = null,
            companySubContractId = subContract.id,
        )

        beneficiaryDataService.add(beneficiary1)
        beneficiaryDataService.add(beneficiary2)
        beneficiaryDataService.add(beneficiary3)

        val findByCompanyIdPersonIdsAndBeneficiaryType = beneficiaryDataService.find { where {
            this.companyId.eq(companyId) and this.personId.inList(personIds) and this.archived.eq(false) and
                    this.type.eq(BeneficiaryType.EMPLOYEE) and this.canceledAt.isNull() }
        }.map { it.size }

        assertThat(findByCompanyIdPersonIdsAndBeneficiaryType).isSuccessWithData(1)

        val findByCompanyIds = beneficiaryDataService.find { where {
            this.companyId.inList(listOf(companyId)) and this.archived.eq(false)  }
        }.map { it.size }

        assertThat(findByCompanyIds).isSuccessWithData(3)
    }

    @Test
    fun `#find beneficiary by gracePeriodType null`() = runBlocking {
        val companyId = RangeUUID.generate()

        val beneficiary = buildBeneficiaryModel(
            personId = personToken.personId,
            companyId = companyId,
            type = BeneficiaryType.EMPLOYEE,
            companySubContractId = subContract.id,
            updatedBy = UpdatedBy(userType = "Unauthenticated", userId = "undefined", environmentName = "undefined")
        )

        val resultAdd = beneficiaryDataService.add(beneficiary)
        assertThat(resultAdd).isSuccessWithDataIgnoringGivenFields(beneficiary, "createdAt", "updatedAt")

        val resultFind = beneficiaryDataService.find {
            where { this.gracePeriodType.isNull() }
                .limit { 100 }
        }
        assertThat(resultFind).isSuccessWithDataIgnoringGivenFields(listOf(beneficiary), "createdAt", "updatedAt")
    }

    private fun buildBeneficiaryModel(
        id: UUID = RangeUUID.generate(),
        parentBeneficiary: UUID? = null,
        personId: PersonId = PersonId(),
        memberId: UUID = RangeUUID.generate(),
        companyId: UUID = RangeUUID.generate(),
        companySubContractId: UUID? = RangeUUID.generate(),
        type: BeneficiaryType = BeneficiaryType.EMPLOYEE,
        contractType: BeneficiaryContractType? = null,
        parentBeneficiaryRelationType: ParentBeneficiaryRelationType? = null,
        activatedAt: LocalDateTime = LocalDateTime.now(),
        canceledAt: LocalDateTime? = null,
        hiredAt: LocalDateTime? = LocalDateTime.now(),
        parentBeneficiaryRelatedAt: LocalDateTime? = null,
        cnpj: String? = null,
        hasContributed: Boolean? = null,
        dependents: List<BeneficiaryModel>? = null,
        version: Int = 0,
        onboarding: BeneficiaryOnboardingModel? = null,
        canceledReason: BeneficiaryCancelationReason? = null,
        canceledDescription: String? = null,
        archived: Boolean = false,
        brand: Brand? = Brand.ALICE,
        memberStatus: MemberStatus? = MemberStatus.PENDING,
        parentPerson: PersonId? = null,
        gracePeriodType: GracePeriodType? = null,
        gracePeriodTypeReason: GracePeriodTypeReason? = null,
        updatedBy: UpdatedBy? = null
    ) = BeneficiaryModel(
        id = id,
        parentBeneficiary = parentBeneficiary,
        personId = personId,
        memberId = memberId,
        companyId = companyId,
        companySubContractId = companySubContractId,
        type = type,
        contractType = contractType,
        parentBeneficiaryRelationType = parentBeneficiaryRelationType,
        activatedAt = activatedAt,
        canceledAt = canceledAt,
        hiredAt = hiredAt,
        parentBeneficiaryRelatedAt = parentBeneficiaryRelatedAt,
        dependents = dependents,
        version = version,
        onboarding = onboarding,
        canceledReason = canceledReason,
        canceledDescription = canceledDescription,
        archived = archived,
        cnpj = cnpj,
        hasContributed = hasContributed,
        brand = brand,
        memberStatus = memberStatus,
        parentPerson = parentPerson,
        gracePeriodType = gracePeriodType,
        gracePeriodTypeReason = gracePeriodTypeReason,
        updatedBy = updatedBy
    )
}
