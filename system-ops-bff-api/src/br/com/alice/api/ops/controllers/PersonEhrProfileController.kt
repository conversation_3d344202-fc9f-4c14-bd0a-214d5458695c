package br.com.alice.api.ops.controllers

import br.com.alice.api.ops.controllers.ServicedByResponse.Companion.buildByProvider
import br.com.alice.api.ops.controllers.ServicedByResponse.Companion.buildByStaff
import br.com.alice.api.ops.services.checkAuthorization
import br.com.alice.api.ops.services.formatPerson
import br.com.alice.api.ops.utils.Logging
import br.com.alice.appointment.client.AttendantsTimelineService
import br.com.alice.appointment.client.TimelineFilter
import br.com.alice.appointment.client.TimelineService
import br.com.alice.appointment.models.AttendantsTimeline
import br.com.alice.appointment.models.ProviderTimelineResponse
import br.com.alice.appointment.models.StaffTimelineResponse
import br.com.alice.common.Response
import br.com.alice.common.controllers.Controller
import br.com.alice.common.core.PersonId
import br.com.alice.common.core.extensions.toBrazilianDateTimeFormat
import br.com.alice.common.core.extensions.toPersonId
import br.com.alice.common.core.extensions.toUUID
import br.com.alice.common.extensions.flatMapPair
import br.com.alice.common.logging.logger
import br.com.alice.data.layer.models.Person
import br.com.alice.data.layer.models.Timeline
import br.com.alice.data.layer.models.TimelineReferenceModel
import br.com.alice.data.layer.models.TimelineType
import br.com.alice.person.client.PersonService
import br.com.alice.staff.client.StaffService
import com.github.kittinunf.result.map
import io.ktor.http.HttpHeaders
import io.ktor.http.HttpStatusCode
import io.ktor.http.Parameters
import java.time.LocalDateTime

class PersonEhrProfileController(
    private val personService: PersonService,
    private val staffService: StaffService,
    private val timelineService: TimelineService,
    private val attendantsTimeline: AttendantsTimelineService,
) : Controller() {
    companion object {
        val excludeTypes =
            listOf(TimelineType.APPOINTMENT_ANNOTATION, TimelineType.APPOINTMENT_ANNOTATION_HEALTH_COMMUNITY)
    }

    suspend fun index(queryParams: Parameters): Response {
        logger.info(
            "PersonEhrProfileController::index",
            "params" to queryParams.entries(),
            "current_staff_id" to currentUserIdKey()
        )
        val ids = parseIds(queryParams)

        if (ids != null) {
            Logging.logPIISearch(currentUserIdKey(), "personIds", ids)
            val people = if (ids.isNotEmpty()) {
                personService.findByIds(ids).get()
            } else {
                emptyList()
            }
            val responses = people.map { PersonResponseConverter.convert(it) }
            return Response(HttpStatusCode.OK, responses, mapOf(HttpHeaders.ContentRange to responses.size.toString()))
        }

        val filterQuery = parseQuery(queryParams)
        if (filterQuery != null) {
            Logging.logPIISearch(currentUserIdKey(), "filter_query", filterQuery)
            val responses =
                personService.findBySearchTokens(filterQuery).get().map { PersonResponseConverter.convert(it) }
            return Response(HttpStatusCode.OK, responses, mapOf(HttpHeaders.ContentRange to responses.size.toString()))
        }

        val range = parseRange(queryParams)

        val result = personService.findPaginated(range.first, range.count())

        val totalCount = personService.countAll().get()

        val personResponses = result.get().map { PersonResponseConverter.convert(it) }

        return Response(HttpStatusCode.OK, personResponses, mapOf(HttpHeaders.ContentRange to totalCount.toString()))
    }

    suspend fun get(personId: String): Response {
        val staff = staffService.get(currentUserIdKey().toString().toUUID()).get()

        checkAuthorization(staff)

        Logging.logPIISearch(currentUserIdKey(), "personId", personId)
        logger.info("Getting EHR profile", "current_staff_id" to currentUserIdKey(), "person_id" to personId)
        val person = personService.get(personId.toPersonId()).get()

        val appointmentsText = generateContentByTimeline(person)

        return Response(
            HttpStatusCode.OK, PersonEhrProfileResponse(
                id = person.id.toString(),
                fullSocialName = person.fullSocialName,
                personalData = formatPerson(person),
                appointments = appointmentsText
            )
        )
    }

    private suspend fun generateContentByTimeline(person: Person): String =
        timelineService.findBy(TimelineFilter(personId = person.id))
            .map { timelineList -> timelineList.filter { !excludeTypes.contains(it.type) } }
            .flatMapPair { timelineList -> attendantsTimeline.getAttendantsTimeline(timelineList, true) }
            .map { (attendant, timelineItems) ->
                timelineItems.map { item ->
                    TimelineResponse(
                        personId = item.personId,
                        servicedBy = getServicedBy(item, attendant),
                        title = item.title,
                        description = buildDescription(item, attendant),
                        type = item.type,
                        createdAt = item.referencedModelDate

                    )
                }
            }.get()
            .sortedByDescending { it.createdAt }
            .joinToString("<br/><br/>") { formatTimeline(it) }

    private fun buildDescription(timelineItem: Timeline, attendant: AttendantsTimeline): String =
        (
                StringBuilder().append(
                    """
                Descrição
                ${timelineItem.description}
            """.trimIndent()
                ).toString()
                        +

                        timelineItem.evolutions.map { evolution ->
                            val staffName = attendant.staffs[evolution.staffId]?.let { buildByStaff(it) }
                            StringBuilder().append(
                                """        
                Registrado por ${staffName?.name.orEmpty()}
                Criado em ${evolution.createdAt.toBrazilianDateTimeFormat()}
                            
                <strong>Evolução</strong>
                ${evolution.description}
            """.trimIndent()
                            ).toString()
                        }
                )
            .replace("\n", "<br/>")
            .replace("[", "<br/>")
            .replace("]", "<br/>")

    private fun formatTimeline(timelineItem: TimelineResponse): String {
        return StringBuilder().append(
            """
            <strong>${timelineItem.title}</strong>
            Atendido por ${timelineItem.servicedBy?.name.orEmpty()}
            Criado em ${timelineItem.createdAt.toBrazilianDateTimeFormat()}

            ${timelineItem.description}
        """.trimIndent()
        ).toString().replace("\n", "<br/>")
    }

    private fun getServicedBy(timeline: Timeline, attendantsTimeline: AttendantsTimeline) =
        when (timeline.referencedModelClass) {
            TimelineReferenceModel.HAOC_PRONTO_ATENDIMENTO_RESULT,
            TimelineReferenceModel.HAOC_SUMMARIO_DE_ALTA_RESULT,
            TimelineReferenceModel.EINSTEIN_ATENDIMENTO,
            TimelineReferenceModel.FHIR_BUNDLE -> ServicedByResponse(timeline.description)

            TimelineReferenceModel.APPOINTMENT, TimelineReferenceModel.COUNTER_REFERRAL -> attendantsTimeline.staffs[timeline.staffId]?.let {
                buildByStaff(
                    it
                )
            }

            TimelineReferenceModel.TERTIARY_INTENTION_TOUCH_POINT -> attendantsTimeline.providers[timeline.providerUnitId]?.let {
                buildByProvider(
                    it
                )
            }

            TimelineReferenceModel.HEALTH_FORM_ANSWER_GROUP -> null
            TimelineReferenceModel.REFUND_COUNTER_REFERRAL -> null
        }

}

data class PersonEhrProfileResponse(
    val id: String,
    val fullSocialName: String,
    val personalData: String,
    val appointments: String
)

data class TimelineResponse(
    val personId: PersonId,
    val servicedBy: ServicedByResponse?,
    val title: String? = null,
    val description: String,
    val type: TimelineType? = null,
    val createdAt: LocalDateTime = LocalDateTime.now(),
)

data class ServicedByResponse(
    val name: String,
    val description: String? = null,
    val specialtyName: String? = null,
) {
    companion object {
        fun buildByStaff(staff: StaffTimelineResponse) = ServicedByResponse(
            name = staff.fullName,
            description = staff.description
        )

        fun buildByProvider(staff: ProviderTimelineResponse) = ServicedByResponse(
            name = staff.name.orEmpty()
        )
    }
}
