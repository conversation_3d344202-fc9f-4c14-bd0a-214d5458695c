package br.com.alice.hr.core.services.useCases

import br.com.alice.business.client.CompanyStaffService
import br.com.alice.common.core.exceptions.BadRequestException
import br.com.alice.common.core.exceptions.NotFoundException
import br.com.alice.common.data.dsl.matchers.ResultAssert.Companion.assertThat
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.data.layer.models.CompanyStaff
import br.com.alice.data.layer.models.CompanyStaffAccessLevel
import br.com.alice.data.layer.models.CompanyStaffRole
import com.github.kittinunf.result.Result
import com.github.kittinunf.result.failure
import com.github.kittinunf.result.success
import io.mockk.coEvery
import io.mockk.mockk
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.Test
import java.time.LocalDateTime
import java.util.UUID
import kotlin.test.assertEquals

class AddOrReactivateCompanyStaffUseCaseTest {
    private val companyStaffService: CompanyStaffService = mockk()
    private val addOrReactivateCompanyStaffUseCase = AddOrReactivateCompanyStaffUseCase(companyStaffService)

    val companyStaff = CompanyStaff(
        id = UUID.randomUUID(),
        companyId = UUID.randomUUID(),
        email = "<EMAIL>",
        firstName = "Test",
        lastName = "Test",
        archivedAt = null,
        role = CompanyStaffRole.MAIN_COMPANY_STAFF,
        accessLevel = CompanyStaffAccessLevel.ADMIN
    )

    @Test
    fun `#run should add a new company staff successfully`() = runBlocking {
        coEvery { companyStaffService.getByEmail(companyStaff.email) } returns NotFoundException().failure()

        coEvery { companyStaffService.add(companyStaff) } returns companyStaff.success()

        val result = addOrReactivateCompanyStaffUseCase.run(companyStaff).get()

        assertEquals(result, companyStaff)

        coVerifyOnce { companyStaffService.add(any()) }
        coVerifyOnce { companyStaffService.getByEmail(any()) }
    }

    @Test
    fun `#run should return error when email is already in use`() = runBlocking {
        val existentActiveCompanyStaff = CompanyStaff(
            id = UUID.randomUUID(),
            companyId = UUID.randomUUID(),
            email = "<EMAIL>",
            firstName = "Existent Test",
            lastName = "Existent Test",
            archivedAt = null,
            role = CompanyStaffRole.MAIN_COMPANY_STAFF,
            accessLevel = CompanyStaffAccessLevel.ADMIN
        )

        coEvery {
            companyStaffService.getByEmail(companyStaff.email)
        } returns existentActiveCompanyStaff.success()

        val result = addOrReactivateCompanyStaffUseCase.run(companyStaff)

        assertThat(result).isFailureOfType(BadRequestException::class)

        coVerifyOnce { companyStaffService.getByEmail(any()) }
    }

    @Test
    fun `#run should format first name and last name`() = runBlocking {
        val companyStaffExpected = companyStaff.copy(firstName = "Firstname", lastName = "Lastname")

        val companyStaffRequest = companyStaff.copy(firstName = "    firstname", lastName = "lastname  ")

        coEvery {
            companyStaffService.getByEmail(companyStaff.email)
        } returns NotFoundException().failure()
        coEvery { companyStaffService.add(companyStaffExpected) } returns companyStaffExpected.success()

        val result = addOrReactivateCompanyStaffUseCase.run(companyStaffRequest)

        assertThat(result).isSuccessWithData(companyStaffExpected)

        coVerifyOnce { companyStaffService.add(any()) }
        coVerifyOnce { companyStaffService.getByEmail(any()) }
    }

    @Test
    fun `#run should reactivate company staff if email is already in use`() = runBlocking {
        val existentInactiveCompanyStaff = CompanyStaff(
            id = UUID.randomUUID(),
            companyId = UUID.randomUUID(),
            email = "<EMAIL>",
            firstName = "Existent Test",
            lastName = "Existent Test",
            archivedAt = LocalDateTime.now(),
            role = CompanyStaffRole.MAIN_COMPANY_STAFF,
            accessLevel = CompanyStaffAccessLevel.ADMIN
        )

        val companyStaffExpected = existentInactiveCompanyStaff.copy(
            companyId = companyStaff.companyId,
            firstName = companyStaff.firstName,
            lastName = companyStaff.lastName,
            role = companyStaff.role,
            accessLevel = companyStaff.accessLevel,
            archivedAt = null
        )

        coEvery {
            companyStaffService.getByEmail(companyStaff.email)
        } returns existentInactiveCompanyStaff.success()
        coEvery { companyStaffService.update(companyStaffExpected) } returns companyStaffExpected.success()

        val result = addOrReactivateCompanyStaffUseCase.run(companyStaff)

        assertThat(result).isSuccessWithData(companyStaffExpected)

        coVerifyOnce { companyStaffService.update(any()) }
        coVerifyOnce { companyStaffService.getByEmail(any()) }
    }

    @Test
    fun `#run should return error if company staff insert fails`() = runBlocking {
        coEvery {
            companyStaffService.getByEmail(companyStaff.email)
        } returns NotFoundException().failure()
        coEvery {
            companyStaffService.add(companyStaff)
        } returns Result.failure(Exception("Error"))

        val result = addOrReactivateCompanyStaffUseCase.run(companyStaff)

        assertThat(result).isFailureOfType(Exception::class)

        coVerifyOnce { companyStaffService.add(any()) }
        coVerifyOnce { companyStaffService.getByEmail(any()) }
    }
}
