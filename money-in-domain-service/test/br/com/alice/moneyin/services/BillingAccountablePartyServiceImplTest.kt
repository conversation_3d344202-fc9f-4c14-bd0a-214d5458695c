package br.com.alice.moneyin.services

import br.com.alice.common.RangeUUID
import br.com.alice.common.core.exceptions.DuplicatedItemException
import br.com.alice.common.core.exceptions.InvalidArgumentException
import br.com.alice.common.data.dsl.matchers.ResultAssert.Companion.assertThat
import br.com.alice.common.data.dsl.matchers.queryEq
import br.com.alice.common.helpers.coVerifyNone
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.common.helpers.returns
import br.com.alice.common.kafka.internals.LocalProducer
import br.com.alice.common.models.State.SP
import br.com.alice.common.service.data.dsl.OrPredicateUsage
import br.com.alice.common.service.data.dsl.QueryAllUsage
import br.com.alice.common.service.data.dsl.and
import br.com.alice.common.service.data.dsl.or
import br.com.alice.common.service.data.dsl.scope
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.Address
import br.com.alice.data.layer.models.BillingAccountablePartyType
import br.com.alice.data.layer.models.BillingAccountablePartyTypeModel
import br.com.alice.data.layer.services.BillingAccountablePartyModelDataService
import br.com.alice.data.layer.services.PersonBillingAccountablePartyModelDataService
import br.com.alice.moneyin.converters.toModel
import br.com.alice.moneyin.event.BillingAccountablePartyAssignedEvent
import br.com.alice.moneyin.event.BillingAccountablePartyCreatedEvent
import br.com.alice.moneyin.event.BillingAccountablePartyUpdatedEvent
import br.com.alice.moneyin.model.InvalidBillingAccountablePartyIdException
import br.com.alice.moneyin.services.internal.BillingAccountablePartyIdService
import br.com.alice.person.client.PersonService
import com.github.kittinunf.result.failure
import com.github.kittinunf.result.success
import io.mockk.clearAllMocks
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.mockk
import io.mockk.spyk
import kotlinx.coroutines.runBlocking
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.TestInstance
import kotlin.test.AfterTest
import kotlin.test.Test

@TestInstance(TestInstance.Lifecycle.PER_CLASS)
class BillingAccountablePartyServiceImplTest {

    private val dataService: BillingAccountablePartyModelDataService = mockk()
    private val personBillingAccountablePartyDataService: PersonBillingAccountablePartyModelDataService = mockk()
    private val personService: PersonService = mockk()
    private val billingAccountablePartyIdService: BillingAccountablePartyIdService = mockk()
    private val service = BillingAccountablePartyServiceImpl(
        dataService,
        personBillingAccountablePartyDataService,
        personService,
        billingAccountablePartyIdService,
        LocalProducer
    )

    private val spyService = spyk(service)

    private val person = TestModelFactory.buildPerson()
    private val billingAccountablePartyId = RangeUUID.generate()
    private val billingAccountableParty = TestModelFactory.buildBillingAccountableParty()
    private val billingAccountablePartyModel = billingAccountableParty.toModel()
    private val personBillingAccountableParty =
        TestModelFactory.buildPersonBillingAccountableParty(billingAccountablePartyId = billingAccountableParty.id)
    private val personBillingAccountablePartyModel = personBillingAccountableParty.toModel()

    @AfterTest
    fun clear() {
        LocalProducer.clearMessages()
        clearAllMocks()
    }

    @Test
    fun `#createForPerson should create a billing accountable party produce event and assign it to the person`() =
        runBlocking {
            val person = TestModelFactory.buildPerson()
            val billingAccountableParty = TestModelFactory.buildBillingAccountableParty()
            val billingAccountablePartyModel = billingAccountableParty.toModel()
            val personBillingAccountableParty = TestModelFactory.buildPersonBillingAccountableParty()
            val personBillingAccountablePartyModel = personBillingAccountableParty.toModel()

            coEvery {
                dataService.add(match {
                    it.firstName == person.firstName &&
                            it.lastName == person.lastName &&
                            it.email == person.email &&
                            it.address == person.addresses.last().toModel() &&
                            it.nationalId == person.nationalId &&
                            it.type == BillingAccountablePartyTypeModel.NATURAL_PERSON
                })
            } returns billingAccountablePartyModel.success()

            coEvery {
                personBillingAccountablePartyDataService.add(match {
                    it.billingAccountablePartyId == billingAccountableParty.id &&
                            it.personId == person.id &&
                            it.sequence == 0

                })
            } returns personBillingAccountablePartyModel.success()

            val result = service.createForPerson(person)

            assertThat(LocalProducer.hasEvent(BillingAccountablePartyCreatedEvent.name)).isTrue
            assertThat(result).isSuccessWithData(personBillingAccountableParty)
        }

    @Nested
    inner class Update {
        @Test
        fun `should update the billing accountable party and produce an event when some data is not equal`() =
            runBlocking {
                val billingAccountableParty = TestModelFactory.buildBillingAccountableParty()
                val billingAccountablePartyModel = billingAccountableParty.toModel()
                val newBillingAccountablePartyData = billingAccountableParty.copy(nationalId = "654321")
                val newBillingAccountablePartyDataModel = newBillingAccountablePartyData.toModel()

                coEvery { dataService.get(billingAccountableParty.id) } returns billingAccountablePartyModel
                coEvery { dataService.update(newBillingAccountablePartyDataModel) } returns newBillingAccountablePartyDataModel

                val result = service.update(newBillingAccountablePartyData)
                assertThat(LocalProducer.hasEvent(BillingAccountablePartyUpdatedEvent.name)).isTrue
                assertThat(result).isSuccessWithData(newBillingAccountablePartyData)
            }

        @Test
        fun `should update the billing accountable party and not produce an event when any data is not equal`() =
            runBlocking {
                val billingAccountableParty = TestModelFactory.buildBillingAccountableParty()
                val billingAccountablePartyModel = billingAccountableParty.toModel()
                val newBillingAccountablePartyData = billingAccountableParty.copy()
                val newBillingAccountablePartyDataModel = newBillingAccountablePartyData.toModel()

                coEvery { dataService.get(billingAccountableParty.id) } returns billingAccountablePartyModel
                coEvery { dataService.update(newBillingAccountablePartyDataModel) } returns newBillingAccountablePartyDataModel

                val result = service.update(newBillingAccountablePartyData)
                assertThat(LocalProducer.hasEvent(BillingAccountablePartyUpdatedEvent.name)).isFalse
                assertThat(result).isSuccessWithData(newBillingAccountablePartyData)
            }
    }

    @Test
    fun `#updateForPerson should update the billing accountable if the person and the party are the same`() =
        runBlocking {
            val person = TestModelFactory.buildPerson(nationalId = "1")
            val billingAccountableParty = TestModelFactory.buildBillingAccountableParty(nationalId = "1")
            val billingAccountablePartyModel = billingAccountableParty.toModel()

            coEvery {
                dataService.findOne(queryEq { where { this.nationalId.eq(person.nationalId) } })
            } returns billingAccountablePartyModel.success()

            coEvery { dataService.get(billingAccountableParty.id) } returns billingAccountablePartyModel

            coEvery {
                dataService.update(match {
                    it.firstName == person.firstName &&
                            it.lastName == person.lastName &&
                            it.email == person.email &&
                            it.address == person.addresses.last().toModel() &&
                            it.nationalId == person.nationalId &&
                            it.type == BillingAccountablePartyTypeModel.NATURAL_PERSON
                })
            } returns billingAccountablePartyModel.success()

            val result = service.updateForPerson(person)

            assertThat(LocalProducer.hasEvent(BillingAccountablePartyUpdatedEvent.name)).isTrue
            assertThat(result).isSuccessWithData(billingAccountableParty)
        }

    @Test
    fun `#getCurrentAssign should call data with expected query`() = runBlocking {
        val person = TestModelFactory.buildPerson()
        val personBillingAccountableParty = TestModelFactory.buildPersonBillingAccountableParty()
        val personBillingAccountablePartyModel = personBillingAccountableParty.toModel()

        coEvery {
            personBillingAccountablePartyDataService.findOne(queryEq {
                where { this.personId.eq(person.id).and(this.endDate.isNull()) }
            })
        } returns personBillingAccountablePartyModel.success()

        val result = service.getCurrentAssign(person.id)

        assertThat(result).isSuccessWithData(personBillingAccountableParty)
    }

    @Test
    fun `#getCurrent should call data with expected query`() = runBlocking {

        coEvery {
            billingAccountablePartyIdService.getFromPersonId(person.id)
        } returns billingAccountablePartyId.success()

        coEvery {
            dataService.get(billingAccountablePartyId)
        } returns billingAccountablePartyModel.success()

        val result = service.getCurrent(person.id)

        assertThat(result).isSuccessWithData(billingAccountableParty)
    }

    @Nested
    inner class Assign {
        @Test
        fun `#should create an billing accountable assignment`() = runBlocking {
            val person = TestModelFactory.buildPerson()
            val billingAccountableParty = TestModelFactory.buildBillingAccountableParty()
            val personBillingAccountableParty = TestModelFactory.buildPersonBillingAccountableParty()
            val personBillingAccountablePartyModel = personBillingAccountableParty.toModel()

            coEvery {
                personBillingAccountablePartyDataService.add(match {
                    it.personId == person.id &&
                            it.billingAccountablePartyId == billingAccountableParty.id &&
                            it.sequence == 0 &&
                            it.endDate == null
                })
            } returns personBillingAccountablePartyModel.success()

            val result = service.assign(person.id, billingAccountableParty)
            assertThat(LocalProducer.hasEvent(BillingAccountablePartyAssignedEvent.name)).isTrue
            assertThat(result).isSuccessWithData(personBillingAccountableParty)
        }

        @Test
        fun `#should finish current assignment and create a new one`() = runBlocking<Unit> {
            val person = TestModelFactory.buildPerson()
            val billingAccountableParty = TestModelFactory.buildBillingAccountableParty()
            val currentPersonBillingAccountableParty =
                TestModelFactory.buildPersonBillingAccountableParty(endDate = null)
            val currentPersonBillingAccountablePartyModel = currentPersonBillingAccountableParty.toModel()
            val personBillingAccountableParty = TestModelFactory.buildPersonBillingAccountableParty()
            val personBillingAccountablePartyModel = personBillingAccountableParty.toModel()

            coEvery {
                personBillingAccountablePartyDataService.add(match {
                    it.personId == person.id &&
                            it.billingAccountablePartyId == billingAccountableParty.id &&
                            it.sequence == 0 &&
                            it.endDate == null
                })
            } returns DuplicatedItemException("").failure()

            coEvery {
                personBillingAccountablePartyDataService.findOne(queryEq {
                    where { this.personId.eq(person.id).and(this.endDate.isNull()) }
                })
            } returns currentPersonBillingAccountablePartyModel

            coEvery {
                personBillingAccountablePartyDataService.update(match {
                    it.id == currentPersonBillingAccountableParty.id &&
                            it.personId == currentPersonBillingAccountableParty.personId &&
                            it.startDate == currentPersonBillingAccountableParty.startDate &&
                            it.sequence == currentPersonBillingAccountableParty.sequence &&
                            it.billingAccountablePartyId == currentPersonBillingAccountableParty.billingAccountablePartyId &&
                            it.endDate != null
                })
            } returns currentPersonBillingAccountablePartyModel

            coEvery {
                personBillingAccountablePartyDataService.add(match {
                    it.personId == person.id &&
                            it.billingAccountablePartyId == billingAccountableParty.id &&
                            it.sequence == 1 &&
                            it.endDate == null
                })
            } returns personBillingAccountablePartyModel

            val result = service.assign(person.id, billingAccountableParty)
            assertThat(LocalProducer.hasEvent(BillingAccountablePartyAssignedEvent.name)).isTrue
            assertThat(result).isSuccessWithData(personBillingAccountableParty)
        }

        @Test
        fun `#should do nothing when the current is already assigned and neither produce an event`() =
            runBlocking<Unit> {
                val person = TestModelFactory.buildPerson()
                val billingAccountableParty = TestModelFactory.buildBillingAccountableParty()
                val currentPersonBillingAccountableParty = TestModelFactory.buildPersonBillingAccountableParty(
                    endDate = null,
                    billingAccountablePartyId = billingAccountableParty.id
                )
                val currentPersonBillingAccountablePartyModel = currentPersonBillingAccountableParty.toModel()

                coEvery {
                    personBillingAccountablePartyDataService.add(match {
                        it.personId == person.id &&
                                it.billingAccountablePartyId == billingAccountableParty.id &&
                                it.sequence == 0 &&
                                it.endDate == null
                    })
                } returns DuplicatedItemException("").failure()

                coEvery {
                    personBillingAccountablePartyDataService.findOne(queryEq {
                        where { this.personId.eq(person.id).and(this.endDate.isNull()) }
                    })
                } returns currentPersonBillingAccountablePartyModel

                val result = service.assign(person.id, billingAccountableParty)
                assertThat(LocalProducer.hasEvent(BillingAccountablePartyAssignedEvent.name)).isFalse
                assertThat(result).isSuccessWithData(currentPersonBillingAccountableParty)
            }
    }

    @Test
    fun `#findAssignmentsByPerson should call data with expected query`() = runBlocking {
        val person = TestModelFactory.buildPerson()
        val personBillingAccountableParty = TestModelFactory.buildPersonBillingAccountableParty()
        val personBillingAccountablePartyModel = personBillingAccountableParty.toModel()

        coEvery {
            personBillingAccountablePartyDataService.find(queryEq { where { this.personId.eq(person.id) } })
        } returns listOf(personBillingAccountablePartyModel).success()

        val result = service.findAssignmentsByPerson(person.id)

        assertThat(result).isSuccessWithData(listOf(personBillingAccountableParty))
    }

    @OptIn(QueryAllUsage::class)
    @Test
    fun `#count should call data with expected query`() = runBlocking {
        coEvery { dataService.count(queryEq { all() }) } returns 10.success()

        val result = service.count()

        assertThat(result).isSuccessWithData(10)
    }

    @Test
    fun `#getByRange should call data with expected query`() = runBlocking {
        val range = IntRange(0, 19)
        val billingAccountableParty = TestModelFactory.buildBillingAccountableParty()
        val billingAccountablePartyModel = billingAccountableParty.toModel()

        coEvery {
            dataService.find(queryEq {
                orderBy { this.createdAt }
                    .sortOrder { desc }
                    .offset { range.first }
                    .limit { range.count() }
            })
        } returns listOf(billingAccountablePartyModel).success()

        val result = service.getByRange(range)

        assertThat(result).isSuccessWithData(listOf(billingAccountableParty))
    }

    @Test
    fun `#getCurrentOrCreateForPerson should just get the current billing accountable given a person`() = runBlocking {

        coEvery {
            billingAccountablePartyIdService.getFromPersonId(person.id)
        } returns billingAccountablePartyId.success()

        coEvery {
            dataService.get(billingAccountablePartyId)
        } returns billingAccountablePartyModel.success()

        val result = service.getCurrentOrCreateForPerson(person.id)

        assertThat(result).isSuccessWithData(billingAccountableParty)
    }

    @Test
    fun `#getCurrentOrCreateForPerson should assign the person itself as the accountable party `() = runBlocking {

        coEvery {
            billingAccountablePartyIdService.getFromPersonId(person.id)
        } returns InvalidBillingAccountablePartyIdException().failure()

        coEvery { dataService.findOneOrNull(queryEq { where { this.nationalId.eq(person.nationalId) } }) } returns billingAccountablePartyModel
        coEvery { personService.get(person.id) } returns person.success()
        coEvery { personBillingAccountablePartyDataService.add(match { it.billingAccountablePartyId == billingAccountableParty.id && it.personId == person.id }) } returns personBillingAccountablePartyModel.success()
        coEvery { dataService.get(billingAccountableParty.id) } returns billingAccountablePartyModel.success()

        val result = service.getCurrentOrCreateForPerson(person.id)

        assertThat(result).isSuccessWithData(billingAccountableParty)
    }

    @Test
    fun `#getCurrentOrCreateForPerson should create the billing accountable party with person data, produce event and assign itself `() =
        runBlocking {

            coEvery {
                billingAccountablePartyIdService.getFromPersonId(person.id)
            } returns InvalidBillingAccountablePartyIdException().failure()

            coEvery {
                dataService.findOneOrNull(queryEq { where { this.nationalId.eq(person.nationalId) } })
            } returns null

            coEvery { personService.get(person.id) } returns person.success()
            coEvery { dataService.add(match { it.nationalId == person.nationalId }) } returns billingAccountablePartyModel.success()
            coEvery { personBillingAccountablePartyDataService.add(match { it.billingAccountablePartyId == billingAccountableParty.id && it.personId == person.id }) } returns personBillingAccountablePartyModel.success()
            coEvery { dataService.get(billingAccountableParty.id) } returns billingAccountablePartyModel.success()

            val result = service.getCurrentOrCreateForPerson(person.id)

            assertThat(LocalProducer.hasEvent(BillingAccountablePartyCreatedEvent.name)).isTrue
            assertThat(result).isSuccessWithData(billingAccountableParty)
        }

    @Test
    fun `#findById should call data with expected query`() = runBlocking {
        val billingAccountableParty = TestModelFactory.buildBillingAccountableParty()
        val billingAccountablePartyList = listOf(TestModelFactory.buildBillingAccountableParty())
        val billingAccountablePartyModelList = billingAccountablePartyList.map { it.toModel() }

        coEvery {
            dataService.find(queryEq {
                where { this.id.inList(listOf(billingAccountableParty.id)) }
            })
        } returns billingAccountablePartyModelList.success()

        val result = service.findById(listOf(billingAccountableParty.id))

        assertThat(result).isSuccessWithData(billingAccountablePartyList)
    }

    @Test
    fun `#findCurrentAssignmentsByParty should call data with expected query`() = runBlocking {
        val billingAccountableParty = TestModelFactory.buildBillingAccountableParty()
        val personBillingAccountableParty = TestModelFactory.buildPersonBillingAccountableParty()
        val personBillingAccountablePartyModel = personBillingAccountableParty.toModel()

        coEvery {
            personBillingAccountablePartyDataService.find(queryEq {
                where {
                    this.billingAccountablePartyId.eq(billingAccountableParty.id) and this.endDate.isNull()
                }
            })
        } returns listOf(personBillingAccountablePartyModel).success()

        val result = service.findCurrentAssignmentsByParty(billingAccountableParty.id)
        assertThat(result).isSuccessWithData(listOf(personBillingAccountableParty))

        coVerify(exactly = 1) {
            personBillingAccountablePartyDataService.find(queryEq {
                where {
                    this.billingAccountablePartyId.eq(billingAccountableParty.id) and this.endDate.isNull()
                }
            })
        }
    }

    @Test
    fun `#getByRangeAndType should call data with expected query`() = runBlocking {
        val billingAccountableParty = TestModelFactory.buildBillingAccountableParty()
        val billingAccountablePartyModel = billingAccountableParty.toModel()
        val range = IntRange(0, 9)

        coEvery {
            dataService.find(queryEq {
                where { this.type.eq(BillingAccountablePartyTypeModel.NATURAL_PERSON) }
                    .orderBy { this.createdAt }
                    .sortOrder { desc }
                    .offset { 0 }
                    .limit { 10 }
            })
        } returns listOf(billingAccountablePartyModel).success()

        val result = service.getByRangeAndType(range, BillingAccountablePartyType.NATURAL_PERSON)
        assertThat(result).isSuccessWithData(listOf(billingAccountableParty))

        coVerify(exactly = 1) {
            dataService.find(queryEq {
                where { this.type.eq(BillingAccountablePartyTypeModel.NATURAL_PERSON) }
                    .orderBy { this.createdAt }
                    .sortOrder { desc }
                    .offset { 0 }
                    .limit { 10 }
            })
        }
    }

    @Test
    @OptIn(OrPredicateUsage::class)
    fun `#countByQuery should call data with expected query`() = runBlocking {
        val query = "name"

        coEvery {
            dataService.count(queryEq {
                where {
                    this.nationalId.like(query) or
                            this.firstName.like(query) or
                            this.lastName.like(query) or
                            this.email.like(query)
                }
            })
        } returns 10.success()

        val result = service.countByQuery(query)

        assertThat(result).isSuccessWithData(10)
    }

    @Test
    @OptIn(OrPredicateUsage::class)
    fun `#findByEmptyAddressPaginated should call data with expected query`() = runBlocking {
        coEvery {
            dataService.find(queryEq {
                where {
                    scope(this.address.isNull().or(this.address.isStreetEmpty())).and(
                        this.type.eq(
                            BillingAccountablePartyTypeModel.valueOf(BillingAccountablePartyType.LEGAL_PERSON.name)
                        )
                    )
                }
                    .offset { 0 }
                    .limit { 1 }
            })
        } returns listOf(billingAccountablePartyModel)

        val result = service.findByEmptyAddressPaginated(BillingAccountablePartyType.LEGAL_PERSON, 0, 1)

        assertThat(result).isSuccessWithData(listOf(billingAccountableParty))
    }

    @Test
    fun `#copyCompanyAddressToBillingAccountablePartyAddress - should sync address to billing accountable party`() =
        runBlocking {
            val billingAccountableParty = TestModelFactory.buildBillingAccountableParty(
                address = Address(
                    state = SP,
                    city = "",
                    street = "",
                    number = "",
                    complement = "",
                    postalCode = "01448",
                    neighbourhood = "",
                )
            )

            val expectedAddress = TestModelFactory.buildAddress(

            )
            val updatedBillingAccountableParty = billingAccountableParty.copy(address = expectedAddress)

            coEvery { spyService.get(billingAccountableParty.id) } returns billingAccountableParty
            coEvery { spyService.update(updatedBillingAccountableParty) } returns updatedBillingAccountableParty

            val result = spyService.copyCompanyAddressToBillingAccountablePartyAddress(
                billingAccountableParty.id,
                expectedAddress
            )

            assertThat(result).isSuccessWithData(updatedBillingAccountableParty)

            coVerifyOnce { spyService.get(any()) }
            coVerifyOnce { spyService.update(any()) }
        }

    @Test
    fun `#copyCompanyAddressToBillingAccountablePartyAddress - should not sync address and return error when billingAccountableParty address is equals`() =
        runBlocking {
            val expectedAddress = billingAccountableParty.address!!

            coEvery { spyService.get(billingAccountableParty.id) } returns billingAccountableParty

            val result = spyService.copyCompanyAddressToBillingAccountablePartyAddress(
                billingAccountableParty.id,
                expectedAddress
            )

            assertThat(result).isFailureOfType(InvalidArgumentException::class)

            coVerifyOnce { spyService.get(any()) }
            coVerifyNone { spyService.update(any()) }
        }
}
