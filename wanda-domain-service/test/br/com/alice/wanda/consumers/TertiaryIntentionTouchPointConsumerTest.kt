package br.com.alice.wanda.consumers

import br.com.alice.common.data.dsl.matchers.ResultAssert.Companion.assertThat
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.TertiaryIntentionType
import br.com.alice.ehr.event.TertiaryIntentionTouchPointCreatedEvent
import br.com.alice.ehr.event.TertiaryIntentionTouchPointUpdatedEvent
import br.com.alice.wanda.services.coordination.TertiaryAttentionEmergencyCoordinationServiceImpl
import br.com.alice.wanda.services.coordination.TertiaryAttentionHospitalizationCoordinationServiceImpl
import com.github.kittinunf.result.success
import io.mockk.called
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.mockk
import kotlinx.coroutines.runBlocking
import kotlin.test.Test

class TertiaryIntentionTouchPointConsumerTest : ConsumerTest() {

    private val emergencyCoordinationService: TertiaryAttentionEmergencyCoordinationServiceImpl = mockk()
    private val hospitalizationCoordinationServiceImpl: TertiaryAttentionHospitalizationCoordinationServiceImpl =
        mockk()

    private val consumer = TertiaryIntentionTouchPointConsumer(
        emergencyCoordinationService,
        hospitalizationCoordinationServiceImpl
    )

    private val emergencyTouchPoint = TestModelFactory.buildTertiaryIntentionEmergency()
    private val hospitalizationTouchPoint = TestModelFactory.buildTertiaryIntentionHospitalization()
    private val appointmentCoordination = TestModelFactory.buildAppointmentCoordination()

    @Test
    fun `#createAppointmentCoordination - should create appointment coordination by emergency`() = runBlocking {
        val event = TertiaryIntentionTouchPointCreatedEvent(emergencyTouchPoint)

        coEvery {
            emergencyCoordinationService.create(emergencyTouchPoint)
        } returns appointmentCoordination.success()

        val result = consumer.createAppointmentCoordination(event)

        assertThat(result).isSuccessWithData(appointmentCoordination)

        coVerifyOnce { emergencyCoordinationService.create(emergencyTouchPoint) }
        coVerify { hospitalizationCoordinationServiceImpl wasNot called }
    }

    @Test
    fun `#createAppointmentCoordination - should create appointment coordination by hospitalization`() = runBlocking {
        val event = TertiaryIntentionTouchPointCreatedEvent(hospitalizationTouchPoint)

        coEvery {
            hospitalizationCoordinationServiceImpl.create(hospitalizationTouchPoint)
        } returns appointmentCoordination.success()

        val result = consumer.createAppointmentCoordination(event)

        assertThat(result).isSuccessWithData(appointmentCoordination)

        coVerifyOnce { hospitalizationCoordinationServiceImpl.create(hospitalizationTouchPoint) }
        coVerify { emergencyCoordinationService wasNot called }
    }

    @Test
    fun `#createAppointmentCoordination - should ignore if type is null`() = runBlocking {
        val event = TertiaryIntentionTouchPointCreatedEvent(
            emergencyTouchPoint.copy(
                type = null
            )
        )

        val result = consumer.createAppointmentCoordination(event)

        assertThat(result).isSuccessWithData(true)

        coVerify { emergencyCoordinationService wasNot called }
        coVerify { hospitalizationCoordinationServiceImpl wasNot called }
    }

    @Test
    fun `#createAppointmentCoordination - should ignore if type is not allowed`() = runBlocking {
        val event = TertiaryIntentionTouchPointCreatedEvent(
            emergencyTouchPoint.copy(
                type = TertiaryIntentionType.TIT_SURGERY
            )
        )

        val result = consumer.createAppointmentCoordination(event)

        assertThat(result).isSuccessWithData(true)

        coVerify { emergencyCoordinationService wasNot called }
        coVerify { hospitalizationCoordinationServiceImpl wasNot called }
    }

    @Test
    fun `#createAppointmentCoordination - should ignore if coordinated field is null`() = runBlocking {
        val event = TertiaryIntentionTouchPointCreatedEvent(
            emergencyTouchPoint.copy(
                coordinated = null
            )
        )

        val result = consumer.createAppointmentCoordination(event)

        assertThat(result).isSuccessWithData(true)

        coVerify { emergencyCoordinationService wasNot called }
        coVerify { hospitalizationCoordinationServiceImpl wasNot called }
    }

    @Test
    fun `#updateAppointmentCoordination - should update appointment coordination by emergency`() = runBlocking {
        val event = TertiaryIntentionTouchPointUpdatedEvent(emergencyTouchPoint)

        coEvery {
            emergencyCoordinationService.checkTertiaryIntention(emergencyTouchPoint)
        } returns appointmentCoordination.success()

        val result = consumer.updateAppointmentCoordination(event)

        assertThat(result).isSuccessWithData(appointmentCoordination)

        coVerifyOnce { emergencyCoordinationService.checkTertiaryIntention(emergencyTouchPoint) }
        coVerify { hospitalizationCoordinationServiceImpl wasNot called }
    }

    @Test
    fun `#updateAppointmentCoordination - should ignore by hospitalization`() = runBlocking {
        val event = TertiaryIntentionTouchPointUpdatedEvent(hospitalizationTouchPoint)

        val result = consumer.updateAppointmentCoordination(event)

        assertThat(result).isSuccessWithData(true)

        coVerify { emergencyCoordinationService wasNot called }
        coVerify { hospitalizationCoordinationServiceImpl wasNot called }
    }

}
