package br.com.alice.api.ehr.services.internal.community_cockpit.v2

import br.com.alice.api.ehr.model.RecentMedicalCare
import br.com.alice.api.ehr.model.RecentMedicalCareStatus
import br.com.alice.appointment.client.AppointmentService
import br.com.alice.common.core.PersonId
import br.com.alice.common.core.extensions.toBrazilianDateFormat
import br.com.alice.common.core.extensions.toUUID
import br.com.alice.common.extensions.flatMapPair
import br.com.alice.common.logging.logger
import br.com.alice.data.layer.models.Appointment
import br.com.alice.data.layer.models.AppointmentDiscardedType
import br.com.alice.data.layer.models.AppointmentEventReferenceModel
import br.com.alice.data.layer.models.AppointmentStatus
import br.com.alice.data.layer.models.CounterReferral
import br.com.alice.data.layer.models.Person
import br.com.alice.ehr.client.CounterReferralService
import br.com.alice.person.client.PersonService
import com.github.kittinunf.result.Result
import com.github.kittinunf.result.getOrNull
import com.github.kittinunf.result.map
import com.github.kittinunf.result.onFailure
import com.github.kittinunf.result.runCatching
import com.github.kittinunf.result.success
import java.time.LocalDateTime
import java.util.UUID
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Deferred
import kotlinx.coroutines.async
import kotlinx.coroutines.coroutineScope

class RecentMedicalCareService(
    private val counterReferralService: CounterReferralService,
    private val appointmentService: AppointmentService,
    private val personService: PersonService,
) {
    private companion object {
        const val LIMIT: Int = 50
    }

    suspend fun getList(staffId: UUID): Result<List<RecentMedicalCare>, Throwable> = coroutineScope {
        val appointments = getRecentAppointment(staffId)
        val counterReferrals = getRecentCounterReferral(staffId)
        return@coroutineScope filterResponse(appointments.await(), counterReferrals.await())
    }

    private fun filterResponse(
        appointments: List<RecentMedicalCare>,
        crs: List<RecentMedicalCare>
    ): Result<List<RecentMedicalCare>, Throwable> {
        val appointmentIds = appointments.map { it.medicalCareId }
        val crsWithOutAppointment = crs.filter { it.medicalCareId !in appointmentIds }
        val all = (appointments + crsWithOutAppointment).sortedWith(
            compareByDescending<RecentMedicalCare> { it.status == RecentMedicalCareStatus.DRAFT }
                .thenByDescending { it.publishedDate }
        )
        return all.take(LIMIT).success()
    }


    private fun CoroutineScope.getRecentAppointment(staffId: UUID): Deferred<List<RecentMedicalCare>> =
        async {
            appointmentService.recentByStaff(staffId, LIMIT).flatMapPair { appointments ->
                val personIds = appointments.map { it.personId.toString() }.distinct()
                personService.findByIds(personIds)
            }.map { (persons, apps) ->
                val personsMap = persons.associateBy { it.id }
                buildRecentMedicalCareByAppointment(apps, personsMap)
            }.get()
        }

    private fun CoroutineScope.getRecentCounterReferral(staffId: UUID): Deferred<List<RecentMedicalCare>> =
        async {
            counterReferralService.listByStaffId(staffId).flatMapPair { counterReferrals ->
                val personIds = counterReferrals.map { it.personId.toString() }.distinct()
                personService.findByIds(personIds)
            }.map { (persons, crs) ->
                val personsMap = persons.associateBy { it.id }
                buildRecentMedicalCareByCounterReferral(crs, personsMap)
            }.get()
        }


    private fun buildRecentMedicalCareByCounterReferral(
        crs: List<CounterReferral>,
        persons: Map<PersonId, Person>
    ): List<RecentMedicalCare> = crs.map { cr ->
        val person = persons.getValue(cr.personId)
        RecentMedicalCare(
            medicalCareId = cr.appointmentId ?: cr.id,
            personId = cr.personId.toString(),
            name = person.fullSocialName,
            nationalId = person.nationalId,
            referralId = cr.referralId,
            appointmentDate = cr.appointmentDate.toBrazilianDateFormat(),
            publishedDate = cr.createdAt,
            status = cr.notOccurredReason?.let { RecentMedicalCareStatus.DISCARDED_NO_SHOW }
                ?: RecentMedicalCareStatus.FINISHED,
            allowDownload = false,
        )
    }

    private fun buildRecentMedicalCareByAppointment(
        appointments: List<Appointment>,
        persons: Map<PersonId, Person>
    ): List<RecentMedicalCare> = appointments.mapNotNull { app ->
        val person = persons.getValue(app.personId)
        runCatching {
            RecentMedicalCare(
                medicalCareId = app.id,
                personId = app.personId.toString(),
                name = person.fullSocialName,
                nationalId = person.nationalId,
                referralId = if (app.event?.referenceModel == AppointmentEventReferenceModel.HEALTH_PLAN_TASK) app.event?.referenceModelId?.toUUID() else null,
                appointmentDate = app.appointmentDate?.toBrazilianDateFormat() ?: app.createdAt.toLocalDate()
                    .toBrazilianDateFormat(),
                publishedDate = getPublishDate(app),
                allowDownload = app.status == AppointmentStatus.FINISHED,
                status = app.status.toTransportStatus(app.discardedType),
            )
        }.onFailure {
            logger.error(
                "Error building recent medical care by appointment",
                "appointment_id" to app.id, it
            )
        }.getOrNull()
    }

    private fun AppointmentStatus.toTransportStatus(discardedType: AppointmentDiscardedType?): RecentMedicalCareStatus =
        when (this) {
            AppointmentStatus.FINISHED -> RecentMedicalCareStatus.FINISHED
            AppointmentStatus.DRAFT -> RecentMedicalCareStatus.DRAFT
            AppointmentStatus.DISCARDED -> getReason(discardedType)
        }

    private fun getReason(discardedReason: AppointmentDiscardedType?): RecentMedicalCareStatus =
        if (discardedReason == AppointmentDiscardedType.NO_SHOW) RecentMedicalCareStatus.DISCARDED_NO_SHOW
        else throw IllegalArgumentException("invalid reason $discardedReason")

    private fun getPublishDate(app: Appointment): LocalDateTime? = when (app.status) {
        AppointmentStatus.FINISHED -> app.completedAt
        AppointmentStatus.DISCARDED -> app.createdAt
        AppointmentStatus.DRAFT -> null
    }
}
