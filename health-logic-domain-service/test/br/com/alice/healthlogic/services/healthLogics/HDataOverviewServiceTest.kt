package br.com.alice.healthlogic.services.healthLogics

import br.com.alice.common.RangeUUID
import br.com.alice.common.core.PersonId
import br.com.alice.common.core.exceptions.NotFoundException
import br.com.alice.common.core.extensions.atBeginningOfTheMonth
import br.com.alice.common.data.dsl.matchers.ResultAssert.Companion.assertThat
import br.com.alice.common.data.dsl.matchers.queryEq
import br.com.alice.common.helpers.coVerifyNone
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.common.kafka.interfaces.KafkaProducerService
import br.com.alice.common.kafka.interfaces.ProducerResult
import br.com.alice.common.models.Sex
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.CaseSeverity
import br.com.alice.data.layer.models.CaseSeverity.INACTIVE
import br.com.alice.data.layer.models.CaseStatus
import br.com.alice.data.layer.models.HDataOverview
import br.com.alice.data.layer.models.RiskDescription
import br.com.alice.data.layer.services.HDataOverviewDataService
import br.com.alice.healthlogic.event.HDataOverviewUpdatedEvent
import br.com.alice.person.client.PersonService
import com.github.kittinunf.result.failure
import com.github.kittinunf.result.success
import io.mockk.clearAllMocks
import io.mockk.coEvery
import io.mockk.confirmVerified
import io.mockk.mockk
import io.mockk.slot
import kotlinx.coroutines.runBlocking
import org.assertj.core.api.Assertions.assertThat
import java.math.BigDecimal
import java.time.LocalDateTime
import kotlin.test.AfterTest
import kotlin.test.Test


internal class HDataOverviewServiceTest {
    private val hDataOverviewDataService: HDataOverviewDataService = mockk()
    private val kafkaProducerService: KafkaProducerService = mockk()
    private val personService: PersonService = mockk()

    private val service = HDataOverviewService(
        hDataOverviewDataService,
        kafkaProducerService,
        personService
    )

    private val personId = PersonId()
    private val person = TestModelFactory.buildPerson(
        personId = personId,
        dateOfBirth = LocalDateTime.now(),
        sex = Sex.INTERSEX
    )
    private val hDataOverview = TestModelFactory.buildHDataOverview(
        personId = personId,
        monthYearBirth = person.dateOfBirth!!.toLocalDate().atBeginningOfTheMonth(),
        sex = person.sex
    )

    private val clinicalOutcome = HDataOverview.ClinicalOutcome(
        id = RangeUUID.generate(),
        addedAt = LocalDateTime.now(),
        outcomeConfId = RangeUUID.generate(),
        outcome = BigDecimal(0)
    )

    private val caseId = RangeUUID.generate()
    private val hDemand = HDataOverview.HDemand(
        id = RangeUUID.generate(),
        addedAt = LocalDateTime.now(),
        severity = CaseSeverity.COMPENSATED,
        status = CaseStatus.ACTIVE,
        healthConditionId = RangeUUID.generate()
    )

    private val risk = TestModelFactory.buildRisk(
        personId = personId,
        riskDescription = RiskDescription.HIGH_RISK
    )

    private val producerResult: ProducerResult = mockk()

    @AfterTest
    fun clear() {
        confirmVerified(hDataOverviewDataService, kafkaProducerService)
        clearAllMocks()
    }

    @Test
    fun `#get - get by person id`() = runBlocking {
        coEvery {
            hDataOverviewDataService.findOne(queryEq {
                where { this.personId.eq(hDataOverview.personId) }
            })
        } returns hDataOverview.success()

        val result = service.get(personId)
        assertThat(result).isSuccessWithData(hDataOverview)

        coVerifyOnce { hDataOverviewDataService.findOne(any()) }
    }

    @Test
    fun `#get - creates new if not found`() = runBlocking {
        coEvery {
            hDataOverviewDataService.findOne(queryEq {
                where { this.personId.eq(hDataOverview.personId) }
            })
        } returns NotFoundException().failure()

        coEvery {
            personService.get(personId)
        } returns person.success()

        val slot = slot<HDataOverview>()
        coEvery { hDataOverviewDataService.add(capture(slot)) } returns hDataOverview.success()

        val result = service.get(personId)
        val capturedHDataOverview = slot.captured

        assertThat(result).isSuccessWithData(hDataOverview)
        assertThat(capturedHDataOverview.personId).isEqualTo(personId)
        coVerifyOnce { hDataOverviewDataService.findOne(any()) }
        coVerifyOnce { hDataOverviewDataService.add(any()) }
    }


    @Test
    fun `#updateRisk - update risk field`() = runBlocking {
        coEvery {
            hDataOverviewDataService.findOne(queryEq {
                where { this.personId.eq(hDataOverview.personId) }
            })
        } returns hDataOverview.success()

        val newHDataOverview = hDataOverview.copy(
            risk = risk.riskDescription
        )
        val slot = slot<HDataOverview>()
        coEvery { hDataOverviewDataService.update(capture(slot)) } returns hDataOverview.success()

        coEvery {
            kafkaProducerService.produce(
                match { it: HDataOverviewUpdatedEvent ->
                    it.payload.hDataOverview == hDataOverview &&
                            it.payload.updatedId == risk.id &&
                            it.payload.updatedType == HDataOverviewUpdatedEvent.UpdatedType.RISK
                }
            )
        } returns producerResult

        val result = service.updateRisk(personId, risk)
        val capturedHDataOverview = slot.captured

        assertThat(result).isSuccessWithData(hDataOverview)
        assertThat(capturedHDataOverview).isEqualTo(newHDataOverview)
        coVerifyOnce { hDataOverviewDataService.findOne(any()) }
        coVerifyOnce { hDataOverviewDataService.update(any()) }
        coVerifyOnce { kafkaProducerService.produce(any()) }
    }

    @Test
    fun `#updateRisk - does not update risk field when it does not change`() = runBlocking {
        val hDataOverview = hDataOverview.copy(
            risk = risk.riskDescription
        )
        coEvery {
            hDataOverviewDataService.findOne(queryEq {
                where { this.personId.eq(hDataOverview.personId) }
            })
        } returns hDataOverview.success()


        val result = service.updateRisk(personId, risk)

        assertThat(result).isSuccessWithData(hDataOverview)
        coVerifyOnce { hDataOverviewDataService.findOne(any()) }
        coVerifyNone { hDataOverviewDataService.update(any()) }
        coVerifyNone { kafkaProducerService.produce(any()) }
    }

    @Test
    fun `#updateOutcome - update outcome map`() = runBlocking {
        coEvery {
            hDataOverviewDataService.findOne(queryEq {
                where { this.personId.eq(hDataOverview.personId) }
            })
        } returns hDataOverview.success()

        val newHDataOverview = hDataOverview.copy(
            outcomeRecords = hDataOverview.outcomeRecords.plus(clinicalOutcome.outcomeConfId to clinicalOutcome)
        )
        val slot = slot<HDataOverview>()
        coEvery { hDataOverviewDataService.update(capture(slot)) } returns hDataOverview.success()

        coEvery {
            kafkaProducerService.produce(
                match { it: HDataOverviewUpdatedEvent ->
                    it.payload.hDataOverview == hDataOverview &&
                            it.payload.updatedId == clinicalOutcome.outcomeConfId &&
                            it.payload.updatedType == HDataOverviewUpdatedEvent.UpdatedType.CLINICAL_OUTCOME
                }
            )
        } returns producerResult

        val result = service.updateOutcome(personId, clinicalOutcome)
        val capturedHDataOverview = slot.captured

        assertThat(result).isSuccessWithData(hDataOverview)
        assertThat(capturedHDataOverview).isEqualTo(newHDataOverview)
        coVerifyOnce { hDataOverviewDataService.findOne(any()) }
        coVerifyOnce { hDataOverviewDataService.update(any()) }
        coVerifyOnce { kafkaProducerService.produce(any()) }
    }

    @Test
    fun `#updateOutcome - update monthYearBirth when it is empty`() = runBlocking {
        val hDataOverviewWithoutBirth = hDataOverview.copy(monthYearBirth = null)
        coEvery {
            hDataOverviewDataService.findOne(queryEq {
                where { this.personId.eq(hDataOverviewWithoutBirth.personId) }
            })
        } returns hDataOverviewWithoutBirth.success()

        coEvery {
            personService.get(personId)
        } returns person.success()

        val newHDataOverview = hDataOverview.copy(
            outcomeRecords = hDataOverview.outcomeRecords.plus(clinicalOutcome.outcomeConfId to clinicalOutcome)
        )
        val slot = slot<HDataOverview>()
        coEvery { hDataOverviewDataService.update(capture(slot)) } returns hDataOverview.success()

        coEvery {
            kafkaProducerService.produce(
                match { it: HDataOverviewUpdatedEvent ->
                    it.payload.hDataOverview == hDataOverview &&
                            it.payload.updatedId == clinicalOutcome.outcomeConfId &&
                            it.payload.updatedType == HDataOverviewUpdatedEvent.UpdatedType.CLINICAL_OUTCOME
                }
            )
        } returns producerResult

        val result = service.updateOutcome(personId, clinicalOutcome)
        val capturedHDataOverview = slot.captured

        assertThat(result).isSuccessWithData(hDataOverview)
        assertThat(capturedHDataOverview).isEqualTo(newHDataOverview)
        coVerifyOnce { hDataOverviewDataService.findOne(any()) }
        coVerifyOnce { hDataOverviewDataService.update(any()) }
        coVerifyOnce { kafkaProducerService.produce(any()) }
        coVerifyOnce { personService.get(any()) }
    }

    @Test
    fun `#updateOutcome - update sex when it is empty`() = runBlocking {
            val hDataOverviewWithoutSex = hDataOverview.copy(sex = null)
        coEvery {
            hDataOverviewDataService.findOne(queryEq {
                where { this.personId.eq(hDataOverviewWithoutSex.personId) }
            })
        } returns hDataOverviewWithoutSex.success()

        coEvery {
            personService.get(personId)
        } returns person.success()

        val newHDataOverview = hDataOverview.copy(
            outcomeRecords = hDataOverview.outcomeRecords.plus(clinicalOutcome.outcomeConfId to clinicalOutcome)
        )
        val slot = slot<HDataOverview>()
        coEvery { hDataOverviewDataService.update(capture(slot)) } returns hDataOverview.success()

        coEvery {
            kafkaProducerService.produce(
                match { it: HDataOverviewUpdatedEvent ->
                    it.payload.hDataOverview == hDataOverview &&
                            it.payload.updatedId == clinicalOutcome.outcomeConfId &&
                            it.payload.updatedType == HDataOverviewUpdatedEvent.UpdatedType.CLINICAL_OUTCOME
                }
            )
        } returns producerResult

        val result = service.updateOutcome(personId, clinicalOutcome)
        val capturedHDataOverview = slot.captured

        assertThat(result).isSuccessWithData(hDataOverview)
        assertThat(capturedHDataOverview).isEqualTo(newHDataOverview)
        coVerifyOnce { hDataOverviewDataService.findOne(any()) }
        coVerifyOnce { hDataOverviewDataService.update(any()) }
        coVerifyOnce { kafkaProducerService.produce(any()) }
        coVerifyOnce { personService.get(any()) }
    }

    @Test
    fun `#updateOutcome - does nothing if new outcome is older than current`() = runBlocking {
        val hDataOverview = hDataOverview.copy(
            outcomeRecords = mapOf(
                clinicalOutcome.outcomeConfId to clinicalOutcome.copy(addedAt = LocalDateTime.now().plusDays(7))
            )
        )

        coEvery {
            hDataOverviewDataService.findOne(queryEq {
                where { this.personId.eq(hDataOverview.personId) }
            })
        } returns hDataOverview.success()

        val result = service.updateOutcome(personId, clinicalOutcome)

        assertThat(result).isSuccessWithData(hDataOverview)
        coVerifyOnce { hDataOverviewDataService.findOne(any()) }
    }

    @Test
    fun `#updateHDemand - update health demands map, adding new active demand`() = runBlocking {
        coEvery {
            hDataOverviewDataService.findOne(queryEq {
                where { this.personId.eq(hDataOverview.personId) }
            })
        } returns hDataOverview.success()

        val newHDataOverview = hDataOverview.copy(
            healthDemands = hDataOverview.healthDemands.plus(caseId to hDemand)
        )
        val slot = slot<HDataOverview>()
        coEvery { hDataOverviewDataService.update(capture(slot)) } returns newHDataOverview.success()

        coEvery {
            kafkaProducerService.produce(
                match { it: HDataOverviewUpdatedEvent ->
                    it.payload.hDataOverview == newHDataOverview &&
                            it.payload.updatedId == caseId &&
                            it.payload.updatedType == HDataOverviewUpdatedEvent.UpdatedType.H_DEMAND
                }
            )
        } returns producerResult

        val result = service.updateHDemand(personId, caseId, hDemand)
        val capturedHDataOverview = slot.captured

        assertThat(result).isSuccessWithData(newHDataOverview)
        assertThat(capturedHDataOverview).isEqualTo(newHDataOverview)
        coVerifyOnce { hDataOverviewDataService.findOne(any()) }
        coVerifyOnce { hDataOverviewDataService.update(any()) }
        coVerifyOnce { kafkaProducerService.produce(any()) }
    }

    @Test
    fun `#updateHDemand - update health demands map, deleting inactive demand`() = runBlocking {
        val hDataOverview = hDataOverview.copy(
            healthDemands = mapOf(caseId to hDemand.copy(addedAt = LocalDateTime.now().minusDays(3)))
        )
        val hDemand = hDemand.copy(
            status = CaseStatus.PENDING,
        )

        coEvery {
            hDataOverviewDataService.findOne(queryEq {
                where { this.personId.eq(hDataOverview.personId) }
            })
        } returns hDataOverview.success()

        val newHDataOverview = hDataOverview.copy(
            healthDemands = emptyMap()
        )

        val slot = slot<HDataOverview>()
        coEvery { hDataOverviewDataService.update(capture(slot)) } returns hDataOverview.success()

        coEvery {
            kafkaProducerService.produce(
                match { it: HDataOverviewUpdatedEvent ->
                    it.payload.hDataOverview == hDataOverview &&
                            it.payload.updatedId == caseId &&
                            it.payload.updatedType == HDataOverviewUpdatedEvent.UpdatedType.H_DEMAND
                }
            )
        } returns producerResult

        val result = service.updateHDemand(personId, caseId, hDemand)
        val capturedHDataOverview = slot.captured

        assertThat(result).isSuccessWithData(hDataOverview)
        assertThat(capturedHDataOverview).isEqualTo(newHDataOverview)
        coVerifyOnce { hDataOverviewDataService.findOne(any()) }
        coVerifyOnce { hDataOverviewDataService.update(any()) }
        coVerifyOnce { kafkaProducerService.produce(any()) }
    }

    @Test
    fun `#updateHDemand - update health demands map, deleting inactive severity demand`() = runBlocking {
        val hDataOverview = hDataOverview.copy(
            healthDemands = mapOf(caseId to hDemand.copy(addedAt = LocalDateTime.now().minusDays(3)))
        )
        val hDemand = hDemand.copy(
            severity = INACTIVE
        )

        coEvery {
            hDataOverviewDataService.findOne(queryEq {
                where { this.personId.eq(hDataOverview.personId) }
            })
        } returns hDataOverview.success()

        val newHDataOverview = hDataOverview.copy(
            healthDemands = emptyMap()
        )

        val slot = slot<HDataOverview>()
        coEvery { hDataOverviewDataService.update(capture(slot)) } returns hDataOverview.success()

        coEvery {
            kafkaProducerService.produce(
                match { it: HDataOverviewUpdatedEvent ->
                    it.payload.hDataOverview == hDataOverview &&
                            it.payload.updatedId == caseId &&
                            it.payload.updatedType == HDataOverviewUpdatedEvent.UpdatedType.H_DEMAND
                }
            )
        } returns producerResult

        val result = service.updateHDemand(personId, caseId, hDemand)
        val capturedHDataOverview = slot.captured

        assertThat(result).isSuccessWithData(hDataOverview)
        assertThat(capturedHDataOverview).isEqualTo(newHDataOverview)
        coVerifyOnce { hDataOverviewDataService.findOne(any()) }
        coVerifyOnce { hDataOverviewDataService.update(any()) }
        coVerifyOnce { kafkaProducerService.produce(any()) }
    }

    @Test
    fun `#updateHDemand - does nothing if new demand is older than current`() = runBlocking {
        val hDataOverview = hDataOverview.copy(
            healthDemands = mapOf(
                caseId to hDemand.copy(addedAt = LocalDateTime.now().plusDays(7))
            )
        )

        coEvery {
            hDataOverviewDataService.findOne(queryEq {
                where { this.personId.eq(hDataOverview.personId) }
            })
        } returns hDataOverview.success()

        val result = service.updateHDemand(personId, caseId, hDemand)

        assertThat(result).isSuccessWithData(hDataOverview)
        coVerifyOnce { hDataOverviewDataService.findOne(any()) }
    }

    @Test
    fun `#backfillMonthYearBirth - update monthYearBirth for hDataOverviews in list`() = runBlocking {
        val hDataOverviewWithoutBirth = hDataOverview.copy(monthYearBirth = null)
        val updatedHDataOverview = listOf(hDataOverview)
        val offset = 0
        val limit = 5
        coEvery {
            hDataOverviewDataService.find(queryEq {
                all().orderBy { createdAt }.sortOrder { asc }.offset{ offset }.limit{ limit }
            })
        } returns listOf(hDataOverviewWithoutBirth).success()

        coEvery {
            personService.findByIds(listOf(personId.toString()))
        } returns listOf(person).success()

        coEvery { hDataOverviewDataService.updateList(updatedHDataOverview) } returns updatedHDataOverview.success()

        coEvery {
            kafkaProducerService.produce(
                match { it: HDataOverviewUpdatedEvent ->
                    it.payload.hDataOverview == hDataOverview &&
                            it.payload.updatedId == clinicalOutcome.outcomeConfId &&
                            it.payload.updatedType == HDataOverviewUpdatedEvent.UpdatedType.CLINICAL_OUTCOME
                }
            )
        } returns producerResult

        val result = service.backfillMonthYearBirth(offset, limit)

        assertThat(result).isSuccessWithData(updatedHDataOverview)
        coVerifyOnce { hDataOverviewDataService.find(any()) }
        coVerifyOnce { hDataOverviewDataService.updateList(any()) }
        coVerifyNone { kafkaProducerService.produce(any()) }
        coVerifyOnce { personService.findByIds(any()) }
    }

}
