package br.com.alice.api.backoffice.mappers.execIndicator

import br.com.alice.api.backoffice.transfers.Colors
import br.com.alice.api.backoffice.transfers.FriendlyEnumResponse
import br.com.alice.api.backoffice.transfers.execIndicator.HealthSpecialistResourceBundleIndexResponse
import br.com.alice.api.backoffice.transfers.execIndicator.HealthSpecialistResourceBundleResponse
import br.com.alice.api.backoffice.transfers.execIndicator.HealthSpecialistResourceBundleType
import br.com.alice.api.backoffice.transfers.execIndicator.PricingForHealthSpecialistResourceBundleBffResponse
import br.com.alice.api.backoffice.transfers.execIndicator.ResourceBundleSpecialtyBffResponse
import br.com.alice.api.backoffice.transfers.execIndicator.SecondaryResourcesResponseTransport
import br.com.alice.common.mappers.CommonOutputMapper
import br.com.alice.common.transfers.ListPaginatedResponse
import br.com.alice.data.layer.models.HealthSpecialistResourceBundle
import br.com.alice.data.layer.models.HealthSpecialistResourceBundleServiceType
import br.com.alice.data.layer.models.HealthcareResource
import br.com.alice.exec.indicator.models.HealthSpecialistResourceBundlePricingStatus
import br.com.alice.exec.indicator.models.HealthSpecialistResourceBundleWithPricingData
import br.com.alice.exec.indicator.models.PricingForHealthSpecialistMedicalSpecialtyResponse
import br.com.alice.exec.indicator.models.PricingForHealthSpecialistResourceBundleResponse
import br.com.alice.exec.indicator.models.PricingForHealthSpecialistResourceBundleResponseStatus
import br.com.alice.exec.indicator.models.PricingForHealthSpecialistResourceBundleWithCountResponse
import br.com.alice.exec.indicator.models.ResourceBundleSpecialtyResponse
import io.ktor.http.Parameters
import java.util.UUID

object HealthSpecialistResourceBundleOutputMapper {
    fun toResponse(
        healthSpecialistResourceBundle: HealthSpecialistResourceBundle,
        secondaryResources: List<HealthcareResource> = emptyList(),
    ): HealthSpecialistResourceBundleResponse =
        HealthSpecialistResourceBundleResponse(
            id = healthSpecialistResourceBundle.id,
            primaryTuss = healthSpecialistResourceBundle.primaryTuss,
            secondaryResources = secondaryResources.map {
                SecondaryResourcesResponseTransport(
                    id = it.id,
                    code = it.code,
                    tableType = it.tableType ?: "",
                    tussCode = it.tussCode ?: "",
                    description = it.description
                )
            },
            executionAmount = healthSpecialistResourceBundle.executionAmount,
            executionEnvironment = healthSpecialistResourceBundle.executionEnvironment,
            aliceDescription = healthSpecialistResourceBundle.description,
            status = healthSpecialistResourceBundle.status,
            serviceType = healthSpecialistResourceBundle.serviceType,
            aliceCode = healthSpecialistResourceBundle.code
        )

    fun toPaginatedResponse(bundles: List<HealthSpecialistResourceBundleWithPricingData>, total: Int, queryParams: Parameters) =
        ListPaginatedResponse(
            pagination = CommonOutputMapper.toPaginationResponse(queryParams, total),
            results = bundles.map { it.toHealthSpecialistResourceBundleIndexResponse() }
        )

    fun toAssociatedResourceBundleSpecialtiesPaginatedResponse(
        specialties: List<ResourceBundleSpecialtyResponse>,
        total: Int,
        queryParams: Parameters
    ) = ListPaginatedResponse(
        pagination = CommonOutputMapper.toPaginationResponse(queryParams, total),
        results = specialties.map { it.toResourceBundleSpecialtyBffResponse() }
    )

    fun toPricingForHealthSpecialistResourceBundlePaginatedResponse(
        pricingForHealthSpecialistResourceBundleWithCount: PricingForHealthSpecialistResourceBundleWithCountResponse,
        queryParams: Parameters
    ) = ListPaginatedResponse(
        pagination = CommonOutputMapper.toPaginationResponse(queryParams, pricingForHealthSpecialistResourceBundleWithCount.count),
        results = pricingForHealthSpecialistResourceBundleWithCount.items.map {
            it.toBffResponse()
        }
    )

    private fun PricingForHealthSpecialistResourceBundleResponse.toBffResponse() =
        PricingForHealthSpecialistResourceBundleBffResponse(
            healthSpecialistResourceBundleId = healthSpecialistResourceBundleId,
            primaryTuss = primaryTuss,
            aliceCode = aliceCode,
            description = description,
            serviceType = serviceType,
            pendingNumber = pendingNumber,
            medicalSpecialties = medicalSpecialties,
            status = status.toFriendlyEnumResponse()
        )

    private fun PricingForHealthSpecialistResourceBundleResponseStatus.toFriendlyEnumResponse() =
        FriendlyEnumResponse(
            friendlyName = description,
            value = this,
            color = when (this) {
                PricingForHealthSpecialistResourceBundleResponseStatus.PRICED -> Colors.GREEN
                PricingForHealthSpecialistResourceBundleResponseStatus.PENDING -> Colors.RED
                PricingForHealthSpecialistResourceBundleResponseStatus.NO_ASSOCIATED_SPECIALTIES -> Colors.RED
            }
        )

    private fun HealthSpecialistResourceBundleWithPricingData.toHealthSpecialistResourceBundleIndexResponse() =
        HealthSpecialistResourceBundleIndexResponse(
            id = healthSpecialistResourceBundle.id,
            aliceCode = healthSpecialistResourceBundle.code,
            primaryTuss = healthSpecialistResourceBundle.primaryTuss,
            aliceDescription = healthSpecialistResourceBundle.description,
            type = healthSpecialistResourceBundle.toHealthSpecialistResourceBundleType().toFriendlyEnumResponse(),
            serviceType = healthSpecialistResourceBundle.serviceType.toFriendlyEnumResponse(),
            status = healthSpecialistResourceBundle.status,
            pricingStatus = pricingStatus.toFriendlyEnumResponse(),
            specialtiesText = getSpecialtiesText(specialtiesCount, allSpecialtiesCount),
        )

    private fun HealthSpecialistResourceBundle.toHealthSpecialistResourceBundleType() =
        if (this.secondaryResources.isNotEmpty() || this.executionAmount > 1) {
            HealthSpecialistResourceBundleType.BUNDLE
        } else HealthSpecialistResourceBundleType.SINGLE

    private fun HealthSpecialistResourceBundleType.toFriendlyEnumResponse() =
        FriendlyEnumResponse(
            friendlyName = description,
            value = this,
            color = when (this) {
                HealthSpecialistResourceBundleType.BUNDLE -> Colors.MAGENTA
                HealthSpecialistResourceBundleType.SINGLE -> Colors.GRAY
            }
        )

    private fun HealthSpecialistResourceBundlePricingStatus.toFriendlyEnumResponse() =
        FriendlyEnumResponse(
            friendlyName = description,
            value = this,
            color = when (this) {
                HealthSpecialistResourceBundlePricingStatus.PRICED -> Colors.GREEN
                HealthSpecialistResourceBundlePricingStatus.PENDING -> Colors.RED
            }
        )

    private fun HealthSpecialistResourceBundleServiceType.toFriendlyEnumResponse() =
        FriendlyEnumResponse(
            friendlyName = description,
            value = this,
            color = when (this) {
                HealthSpecialistResourceBundleServiceType.PROCEDURE -> Colors.VIOLET
                HealthSpecialistResourceBundleServiceType.EXAM -> Colors.YELLOW
                HealthSpecialistResourceBundleServiceType.CONSULTATION -> Colors.BLUE
                HealthSpecialistResourceBundleServiceType.UNDEFINED -> Colors.RED
            }
        )

    private fun getSpecialtiesText(
        specialtiesCount: Int,
        allSpecialtiesCount: Int
    ): String {
        return if (specialtiesCount == allSpecialtiesCount) {
            "Todas especialidades"
        } else if (specialtiesCount == 1) {
            "1 especialidade"
        } else if (specialtiesCount > 1) {
            "$specialtiesCount especialidades"
        } else {
            "Nenhuma especialidade"
        }
    }

    private fun ResourceBundleSpecialtyResponse.toResourceBundleSpecialtyBffResponse() =
        ResourceBundleSpecialtyBffResponse(
            id = id,
            name = name,
            isTherapy = isTherapy,
            pricingStatus = pricingStatus.toFriendlyEnumResponse(),
            currentBeginAt = currentBeginAt,
            currentEndAt = currentEndAt,
            hasScheduledPriceChange = hasScheduledPriceChange,
            medicalSpecialtyId = medicalSpecialtyId,
        )
}
