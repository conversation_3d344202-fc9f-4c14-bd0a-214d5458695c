package br.com.alice.nullvs.models

import br.com.alice.data.layer.models.ExternalModelType
import br.com.alice.data.layer.models.InternalModelType
import br.com.alice.nullvs.common.NullvsActionType
import com.google.gson.annotations.SerializedName
import java.util.UUID

data class MembersSyncRequest(
    val ids: List<UUID> = emptyList(),
    val action: NullvsActionType
)

data class ContractsSyncRequest(
    val ids: List<UUID> = emptyList(),
    val action: NullvsActionType
)

data class SubcontractsSyncRequest(
    val ids: List<UUID> = emptyList(),
    val action: NullvsActionType
)
data class InvoicesSyncRequest(
    val ids: List<UUID> = emptyList(),
)

data class RecurrentPaymentSyncRequest(
    val memberInvoiceGroupIds: List<UUID> = emptyList(),
)

data class ClientsSyncRequest(
    val billingAccountablePartyIds: List<UUID> = emptyList()
)

data class SyncStatus(
    val id: UUID,
    val success: Boolean
)

data class SyncResponse(
    val response: List<SyncStatus>
)

data class NullvsIntegrationRecordRequest(
    val nullvsIntegrationRecords: List<NullvsIntegrationRecordPayload>
)

data class DeleteNullvsIntegrationRecordRequest(
    val nullvsIntegrationRecordId: UUID
)
data class NullvsIntegrationRecordPayload(
    val internalId: UUID,
    val internalModelName: InternalModelType,
    val externalId: String,
    val externalModelName: ExternalModelType,
)

data class BindBeneficiaryToSubcontractRequest(
    val list: List<BeneficiaryAndContractAndSubcontract>
) {
    data class BeneficiaryAndContractAndSubcontract(
        @SerializedName("BA1_CODEMP")
        val groupCompany: String,
        @SerializedName("BA1_CONEMP")
        val contractNumber: String,
        @SerializedName("BA1_SUBCON")
        val subcontractNumber: String,
        @SerializedName("BA1_XIDALI")
        val memberId: UUID
    )
}

data class CreateContractSubcontractForCompanyRequest(
    val list: List<ContractSubcontract>
)

data class CancelWaitingLogsRequest(
    val ids: List<UUID>,
    val internalModelName: InternalModelType
)

data class UpdatePriceListingFromTotvsRequest(
    val memberId: UUID? = null,
    val subContractId: UUID? = null,
)

data class SyncInvoiceItemsFromStartMonthRequest(
    val ids: List<UUID>? = null,
    val type: String? = null,
    val startMonth: String? = null,
)

data class FailedOrPendingInvoiceItemsRequest(
    val ids: List<UUID>? = null,
    val isFirstPayment: Boolean = false,
    val type: String? = null,
    val startMonth: String? = null,
)

data class ContractSubcontract(
    @SerializedName("BT5_CODIGO")
    val contractGroupCompany: String,
    @SerializedName("BT5_NUMCON")
    val contractNumber: String,
    @SerializedName("BT5_DATCON")
    val contractStartedAt: String? = null,
    @SerializedName("BT5_NOME")
    val contractName: String? = null,
    @SerializedName("BT5_TIPCON")
    val contractType: String? = "3",
    @SerializedName("BT5_COBNIV")
    val contractIsBillingLevel: String,
    @SerializedName("BT5_CODCLI")
    val contractCodeClient: String? = null,
    @SerializedName("BT5_NATURE")
    val contractNature: String? = null,
    @SerializedName("BT5_VENCTO")
    val contractDueDate: Int? = null,
    @SerializedName("BT5_MODPAG")
    val contractMode: String = "2",
    @SerializedName("BT5_INTERC")
    val contractExchange: String = "0",
    @SerializedName("BT5_INFANS")
    val contractSendANS: String = "1",
    @SerializedName("BT5_PODREM")
    val contractRefund: String = "1",

    @SerializedName("BQC_SUBCON")
    val subcontractNumber: String,
    @SerializedName("BQC_DATCON")
    val subcontractStartedAt: String? = null,
    @SerializedName("BQC_CNPJ")
    val subcontractCnpj: String,
    @SerializedName("BQC_DESC")
    val subcontractDescription: String? = null,
    @SerializedName("BQC_NREDUZ")
    val subcontractShortDescription: String,
    @SerializedName("BQC_COBNIV")
    val subcontractIsBillingLevel: String,
    @SerializedName("BQC_CODCLI")
    val subcontractCodeClient: String? = null,
    @SerializedName("BQC_NATURE")
    val subcontractNature: String? = null,
    @SerializedName("BQC_TIPCON")
    val subcontractType: String? = "3",
    @SerializedName("BQC_VENCTO")
    val subcontractDueDate: Int,
    @SerializedName("BQC_GRPCOB")
    val subcontractBillingGroup: String? = null,
    @SerializedName("BQC_PODREM")
    val subcontractRefund: String? = "1",
    @SerializedName("BQC_MESREA")
    val subcontractReadjustmentMonth: String,
    @SerializedName("BQC_COBRAT")
    val subcontractHasProRata: String,
    @SerializedName("BQC_INFANS")
    val subcontractSendANS: String? = "1",
    @SerializedName("BQC_TPVCPP")
    val subcontractDueType: String = "1",
    @SerializedName("BQC_COBRET")
    val subcontractRetroactiveCharge: String? = "1",
)
