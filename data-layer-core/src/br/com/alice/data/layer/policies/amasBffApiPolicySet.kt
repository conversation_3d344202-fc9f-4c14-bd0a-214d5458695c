package br.com.alice.data.layer.policies

import br.com.alice.data.layer.AMAS_API_ROOT_SERVICE_NAME
import br.com.alice.data.layer.authorization.Count
import br.com.alice.data.layer.authorization.View
import br.com.alice.data.layer.authorization.policySet
import br.com.alice.data.layer.models.HealthSpecialistResourceBundleModel
import br.com.alice.data.layer.models.InvoiceCritiqueModel
import br.com.alice.data.layer.models.ResourceBundleSpecialtyModel
import br.com.alice.data.layer.models.ResourceBundleSpecialtyPricingModel
import br.com.alice.data.layer.models.StaffModel
import br.com.alice.data.layer.policies.features.createAmasInvoiceBatch
import br.com.alice.data.layer.policies.features.createOrUpdateTussProcedureProvider
import br.com.alice.data.layer.policies.features.createOrUpdateTussProcedureSpecialty
import br.com.alice.data.layer.policies.features.defaultAmasAccess
import br.com.alice.data.layer.policies.features.uploadPreviewEarningSummary
import br.com.alice.data.layer.policies.features.uploadTissBatch
import br.com.alice.data.layer.policies.features.viewAndCreateNationalReceipt
import br.com.alice.data.layer.subjects.EmailDomain

val amasBffApiPolicySet = policySet {
    match("at amas API", { rootService.name == AMAS_API_ROOT_SERVICE_NAME }) {
        includes(viewAndCreateNationalReceipt)
        match("EITA user", { subject is StaffModel && subject.isEitaUser() }) {
            includes(defaultAmasAccess)
            includes(createAmasInvoiceBatch)
            includes(uploadTissBatch)
            includes(uploadPreviewEarningSummary)
            includes(createOrUpdateTussProcedureProvider)
            includes(createOrUpdateTussProcedureSpecialty)
            allows(HealthSpecialistResourceBundleModel::class, View, Count)
            allows(ResourceBundleSpecialtyModel::class, View, Count)
            allows(ResourceBundleSpecialtyPricingModel::class, View, Count)
            allows(InvoiceCritiqueModel::class, View, Count)
        }
        match("AMAS External user", { subject is EmailDomain }) {
            includes(defaultAmasAccess)
        }
        match("EITA Authorizer User", { subject is StaffModel && subject.isEitaAuthorizerUser() }) {
            includes(defaultAmasAccess)
        }
    }
}
