package br.com.alice.schedule.services

import br.com.alice.common.RangeUUID
import br.com.alice.common.core.Status
import br.com.alice.common.core.exceptions.InvalidArgumentException
import br.com.alice.common.core.exceptions.NotFoundException
import br.com.alice.common.core.extensions.atBeginningOfTheDay
import br.com.alice.common.core.extensions.atEndOfTheDay
import br.com.alice.common.core.extensions.classSimpleName
import br.com.alice.common.core.extensions.isAfterEq
import br.com.alice.common.core.extensions.isBeforeEq
import br.com.alice.common.core.extensions.isNotNullOrBlank
import br.com.alice.common.core.extensions.toSafeUUID
import br.com.alice.common.core.extensions.toUUID
import br.com.alice.common.coroutine.pmap
import br.com.alice.common.extensions.catchResult
import br.com.alice.common.extensions.coFoldNotFound
import br.com.alice.common.extensions.coResultOf
import br.com.alice.common.extensions.flatMapPair
import br.com.alice.common.extensions.getOrNullIfNotFound
import br.com.alice.common.extensions.mapEach
import br.com.alice.common.extensions.resultOf
import br.com.alice.common.extensions.then
import br.com.alice.common.extensions.thenError
import br.com.alice.common.kafka.interfaces.KafkaProducerService
import br.com.alice.common.logging.logger
import br.com.alice.common.observability.recordResult
import br.com.alice.common.observability.setAttribute
import br.com.alice.data.layer.models.AppointmentSchedule
import br.com.alice.data.layer.models.AppointmentScheduleType
import br.com.alice.data.layer.models.ExternalCalendarEvent
import br.com.alice.data.layer.models.ExternalCalendarProvider
import br.com.alice.data.layer.models.ExternalCalendarRecurrentEvent
import br.com.alice.data.layer.models.ExternalEventResponseStatus
import br.com.alice.data.layer.models.ExternalEventStatus
import br.com.alice.data.layer.models.FeatureNamespace
import br.com.alice.data.layer.models.Person
import br.com.alice.data.layer.models.SchedulePreference
import br.com.alice.data.layer.models.Staff
import br.com.alice.data.layer.models.StaffSchedule
import br.com.alice.data.layer.models.Weekday
import br.com.alice.featureconfig.core.FeatureService
import br.com.alice.person.client.PersonService
import br.com.alice.schedule.ServiceConfig
import br.com.alice.schedule.client.AppointmentScheduleEventTypeService
import br.com.alice.schedule.client.AppointmentScheduleService
import br.com.alice.schedule.client.GoogleCalendarEventService
import br.com.alice.schedule.converters.GoogleCalendarEventPayloadConverter
import br.com.alice.schedule.exceptions.GoogleCalendarRefreshTokenNotFoundException
import br.com.alice.schedule.exceptions.GoogleCalendarServiceNotFoundException
import br.com.alice.schedule.extensions.shouldSkipQueryEvents
import br.com.alice.schedule.integrations.googlecalendar.GoogleCalendarApi
import br.com.alice.schedule.model.RecurrenceRule
import br.com.alice.schedule.model.RecurrenceRuleFrequency
import br.com.alice.schedule.model.events.FetchInstancesForRecurrentEventsType
import br.com.alice.schedule.model.events.FetchInstancesForRecurrentEventsType.FOR_WEEKDAY_WITHOUT_UNTIL_DATE
import br.com.alice.schedule.model.events.FetchInstancesForRecurrentEventsType.FOR_WEEKDAY_WITH_UNTIL_DATE
import br.com.alice.schedule.model.events.FetchInstancesForRecurrentEventsType.WITHOUT_UNTIL_DATE_WITHOUT_WEEKDAY
import br.com.alice.schedule.model.events.FetchInstancesForRecurrentEventsType.WITH_UNTIL_DATE_WITHOUT_WEEKDAY
import br.com.alice.schedule.model.events.GoogleCalendarEventCreationRequestedEvent
import br.com.alice.schedule.model.events.GoogleCalendarEventsFromRecurrenceRequestedEvent
import br.com.alice.schedule.model.events.GoogleCalendarFetchInstancesForRecurrentEventsForPageEvent
import br.com.alice.schedule.model.events.GoogleCalendarFinishedEventsQueryEvent
import br.com.alice.schedule.model.events.GoogleCalendarQueryEventsEvent
import br.com.alice.schedule.model.events.GoogleCalendarRecurringEventCreationRequestedEvent
import br.com.alice.schedule.model.events.GoogleCalendarSynchronizationRequestedEvent
import br.com.alice.schedule.model.events.GoogleCalendarWebhookSubscriptionRequestedEvent
import br.com.alice.schedule.model.googlecalendar.GoogleCalendarEventPayload
import br.com.alice.schedule.model.googlecalendar.GoogleCalendarEventPayload.Companion.fromExternalCalendarEvent
import br.com.alice.schedule.model.googlecalendar.GoogleCalendarEventPayload.Companion.fromExternalCalendarRecurrentEvent
import br.com.alice.schedule.model.googlecalendar.GoogleCalendarEventWebhookNotification
import br.com.alice.schedule.model.googlecalendar.GoogleCalendarEventsQueryFilters
import br.com.alice.schedule.model.googlecalendar.buildEvent
import br.com.alice.schedule.toGoogleLocalTime
import br.com.alice.staff.client.StaffService
import com.github.kittinunf.result.Result
import com.github.kittinunf.result.flatMap
import com.github.kittinunf.result.getOrElse
import com.github.kittinunf.result.map
import com.github.kittinunf.result.success
import com.google.api.client.googleapis.json.GoogleJsonResponseException
import com.google.api.client.util.DateTime
import com.google.api.services.calendar.Calendar
import com.google.api.services.calendar.model.Channel
import com.google.api.services.calendar.model.Event
import com.google.api.services.calendar.model.Events
import io.opentelemetry.api.trace.Span
import io.opentelemetry.api.trace.StatusCode
import kotlinx.coroutines.async
import kotlinx.coroutines.awaitAll
import kotlinx.coroutines.coroutineScope
import java.time.Instant
import java.time.LocalDate
import java.time.LocalDateTime
import java.time.ZoneId
import java.time.ZonedDateTime
import java.util.Date
import java.util.UUID

class GoogleCalendarEventServiceImpl(
    private val externalCalendarEventService: ExternalCalendarEventServiceImpl,
    private val schedulePreferenceService: SchedulePreferenceServiceImpl,
    private val staffService: StaffService,
    private val kafkaProducerService: KafkaProducerService,
    private val googleCalendarApi: GoogleCalendarApi,
    private val personService: PersonService,
    private val externalCalendarRecurrentEventService: ExternalCalendarRecurrentEventServiceImpl,
    private val appointmentScheduleService: AppointmentScheduleService,
    private val appointmentScheduleEventTypeService: AppointmentScheduleEventTypeService
) : GoogleCalendarEventService {

    private val defaultCalendarId = "primary"
    private val fetchFutureGoogleCalendarEventsForActiveRecurrencesPageSize = 50

    companion object {
        val SCHEDULE_BFF_API_URL = ServiceConfig.SchedulerBffApi.baseUrl()
        private const val DEFAULT_CALENDAR_ID = "primary"
    }

    override suspend fun getAndStoreRefreshToken(code: String, userEmail: String): Result<Boolean, Throwable> =
        span("getAndStoreRefreshToken") { span ->
            googleCalendarApi.getRefreshToken(code).flatMap { tokenResponse ->
                findStaffByEmail(userEmail)
                    .flatMap { staff -> getOrCreateSchedulePreferenceByStaffId(staff.id) }
                    .flatMap { schedulePreference ->
                        updateSchedulePreference(schedulePreference.copy(googleRefreshToken = tokenResponse.refreshToken))
                    }
            }.recordResult(span)
        }

    override suspend fun getAndStoreUserGoogleCalendarEvents(
        staffId: UUID,
        startDate: LocalDateTime?
    ): Result<Boolean, Throwable> = span("getAndStoreUserGoogleCalendarEvents") { span ->
        getSchedulePreferenceByStaffId(staffId).map { schedulePreference ->
            span.setSchedulePreference(schedulePreference)
            getGoogleRefreshTokenForStaff(schedulePreference = schedulePreference).get()
            if (!schedulePreference.isDoingFullSync) {
                schedulePreferenceService.update(
                    schedulePreference.copy(
                        isDoingFullSync = true,
                        googleNextSyncToken = null
                    )
                ).map {
                    kafkaProducerService.produce(GoogleCalendarSynchronizationRequestedEvent(it, startDate))
                    kafkaProducerService.produce(GoogleCalendarWebhookSubscriptionRequestedEvent(it))
                }.get()
            }

            true
        }
    }

    override suspend fun getFutureGoogleCalendarEventsForRecurrence(
        externalCalendarRecurrentEvent: ExternalCalendarRecurrentEvent,
        timeMin: LocalDateTime,
        timeMax: LocalDateTime,
    ): Result<ExternalCalendarRecurrentEvent, Throwable> = coResultOf {
        val from = DateTime(toDate(ZonedDateTime.of(timeMin, ZoneId.of("UTC"))))
        val until = DateTime(toDate(ZonedDateTime.of(timeMax, ZoneId.of("UTC"))))

        val queryFilters = GoogleCalendarEventsQueryFilters(
            calendarId = defaultCalendarId,
            showDeleted = true,
            maxResults = 10,
            singleEvents = false,
            timeMin = from,
            timeMax = until,
            recurringEventId = externalCalendarRecurrentEvent.externalEventId,
        )

        GoogleCalendarEventsFromRecurrenceRequestedEvent(
            externalCalendarRecurrentEvent.staffId,
            queryFilters,
        ).let { event ->
            kafkaProducerService.produce(event)
        }

        externalCalendarRecurrentEvent
    }

    override suspend fun reFetchRecurrentEvent(
        externalCalendarRecurrentEvent: ExternalCalendarRecurrentEvent
    ): Result<ExternalCalendarRecurrentEvent, Throwable> = span("reFetchRecurrentEvent") { span ->
        coroutineScope {
            span.setAttribute("external_calendar_recurrent_event_id", externalCalendarRecurrentEvent.id)
            span.setAttribute("staff_id", externalCalendarRecurrentEvent.staffId)

            val schedulePreferenceDeferred = getSchedulePreferenceAsync(externalCalendarRecurrentEvent.staffId)
            val staffDeferred = getStaffAsync(externalCalendarRecurrentEvent.staffId)

            val googleRefreshToken =
                getGoogleRefreshTokenForStaff(schedulePreference = schedulePreferenceDeferred.await()).get()

            handleGoogleRequest(externalCalendarRecurrentEvent, externalCalendarRecurrentEvent.staffId) {
                val event = getGoogleCalendarService(googleRefreshToken)
                    .get()
                    .get(DEFAULT_CALENDAR_ID, externalCalendarRecurrentEvent.externalEventId)
                    .execute()

                publishGoogleCalendarEvent(event, staffDeferred.await(), false)
            }

            coResultOf { externalCalendarRecurrentEvent }
        }
    }

    override suspend fun reFetchEvent(
        externalCalendarEvent: ExternalCalendarEvent
    ): Result<ExternalCalendarEvent, Throwable> = span("reFetchEvent") { span ->
        coroutineScope {
            span.setAttribute("external_calendar_event_id", externalCalendarEvent.id)

            val schedulePreferenceDeferred = getSchedulePreferenceAsync(externalCalendarEvent.staffId)
            val staffDeferred = getStaffAsync(externalCalendarEvent.staffId)

            val googleRefreshToken =
                getGoogleRefreshTokenForStaff(schedulePreference = schedulePreferenceDeferred.await()).get()

            handleGoogleRequest(externalCalendarEvent, externalCalendarEvent.staffId) {
                val event = getGoogleCalendarService(googleRefreshToken)
                    .get()
                    .get(DEFAULT_CALENDAR_ID, externalCalendarEvent.externalEventId)
                    .execute()

                publishGoogleCalendarEvent(event, staffDeferred.await(), false)
            }

            coResultOf { externalCalendarEvent }
        }
    }

    override suspend fun reFetchFutureGoogleCalendarEventsForRecurrence(
        externalCalendarRecurrentEvent: ExternalCalendarRecurrentEvent
    ): Result<List<ExternalCalendarEvent>, Throwable> = span("reFetchFutureGoogleCalendarEventsForRecurrence") { span ->
        coroutineScope {
            span.setAttribute("external_calendar_recurrent_event_id", externalCalendarRecurrentEvent.id)
            span.setAttribute("staff_id", externalCalendarRecurrentEvent.staffId)

            val externalCalendarEventsDeferred = getExternalCalendarEventsAsync(externalCalendarRecurrentEvent.id)
            val schedulePreferenceDeferred = getSchedulePreferenceAsync(externalCalendarRecurrentEvent.staffId)
            val staffDeferred = getStaffAsync(externalCalendarRecurrentEvent.staffId)

            val googleRefreshToken =
                getGoogleRefreshTokenForStaff(schedulePreference = schedulePreferenceDeferred.await()).get()

            getGoogleCalendarService(googleRefreshToken)
                .map { googleCalendarEventsService ->
                    val staff = staffDeferred.await()
                    val externalCalendarEvents = externalCalendarEventsDeferred.await()
                    span.setAttribute("events_count", externalCalendarEvents.size)
                    externalCalendarEvents
                        .pmap { externalCalendarEvent ->
                            handleGoogleRequest(
                                externalCalendarEvent,
                                externalCalendarRecurrentEvent.staffId,
                                externalCalendarRecurrentEvent.externalEventId
                            ) {
                                val event = googleCalendarEventsService
                                    .get(DEFAULT_CALENDAR_ID, externalCalendarEvent.externalEventId)
                                    .execute()

                                publishGoogleCalendarEvent(event, staff, false)
                                externalCalendarEvent
                            }
                        }
                }
        }
    }

    private suspend fun getExternalCalendarEventsAsync(externalCalendarRecurrentEventId: UUID) = coroutineScope {
        async {
            externalCalendarEventService.getFutureEventsForRecurrence(externalCalendarRecurrentEventId).get()
        }
    }

    private suspend fun getSchedulePreferenceAsync(staffId: UUID) = coroutineScope {
        async { getSchedulePreferenceByStaffId(staffId).get() }
    }

    private suspend fun getStaffAsync(staffId: UUID) = coroutineScope {
        async { staffService.get(staffId).get() }
    }

    suspend fun synchronizeGoogleCalendar(
        schedulePreference: SchedulePreference,
        startDate: LocalDateTime? = null
    ): Result<Boolean, Throwable> =
        getGoogleRefreshTokenForStaff(schedulePreference = schedulePreference).map { googleRefreshToken ->
            val hasDoneFullSync = schedulePreference.googleNextSyncToken != null

            val staffId = schedulePreference.staffId
            val queryFilters = GoogleCalendarEventsQueryFilters(
                calendarId = defaultCalendarId,
                showDeleted = hasDoneFullSync,
                maxResults = 50,
                singleEvents = false,
                syncToken = schedulePreference.googleNextSyncToken,
                pageToken = null,
                timeMin = startDate?.toGoogleLocalTime()
            )

            kafkaProducerService.produce(
                GoogleCalendarQueryEventsEvent(
                    staffId,
                    queryFilters,
                    googleRefreshToken
                )
            )
            true
        }

    suspend fun registerWebhookChannel(schedulePreference: SchedulePreference): Result<SchedulePreference, Throwable> =
        span("registerWebhookChannel") { span ->
            val referencePeriodEnd = LocalDate.now().plusDays(1).atEndOfTheDay()
            span.setAttribute("schedule_preference_id", schedulePreference.id)
            span.setAttribute("staff_id", schedulePreference.staffId)
            span.setAttribute("reference_period_end", referencePeriodEnd)
            if (schedulePreference.googleCalendarWebhookExpiration?.isAfterEq(referencePeriodEnd) == true) {
                logger.info(
                    "GoogleCalendarEventServiceImpl::registerWebhookChannel No need to update channel - not close to expiration",
                    "schedule_preference" to schedulePreference
                )
                return@span schedulePreference.success()
            }

            val googleRefreshToken = getGoogleRefreshTokenForStaff(schedulePreference = schedulePreference).get()

            val googleCalendarEventsService = getGoogleCalendarService(googleRefreshToken).get()

            val channel = buildChannel()
            span.setChannel(channel)
            val response = googleCalendarEventsService.watch(defaultCalendarId, channel).execute()
            val expiration = LocalDateTime.ofInstant(Instant.ofEpochMilli(response.expiration), ZoneId.of("UTC"))

            schedulePreferenceService
                .getByStaffId(schedulePreference.staffId)
                .flatMap {
                    schedulePreferenceService.update(
                        it.copy(
                            googleCalendarWebhookExpiration = expiration,
                            googleCalendarWebhookChannelId = channel.id.toUUID()
                        )
                    )
                }
        }

    suspend fun createOrUpdateEventFromCalendarWebhook(eventWebhookNotification: GoogleCalendarEventWebhookNotification): Result<Any, Throwable> =
        schedulePreferenceService.getByGoogleCalendarWebhookChannelId(eventWebhookNotification.channelId).flatMap {
            if (!it.isDoingFullSync) synchronizeGoogleCalendar(it)
            else {
                logger.info(
                    "GoogleCalendarEventServiceImpl::createOrUpdateEventFromCalendarWebhook already doing full sync",
                    "staff_id" to it.staffId
                )
                true.success()
            }
        }.coFoldNotFound {
            logger.info(
                "GoogleCalendarEventServiceImpl::createOrUpdateEventFromCalendarWebhook schedule preference not found for channel id. " +
                        "Probably the channel has been already renewed.",
                "event_notification" to eventWebhookNotification
            )
            true.success()
        }

    suspend fun updateSchedulePreferenceWithLastQueryInformation(
        staffId: UUID,
        lastUpdatedTime: LocalDateTime,
        nextSyncToken: String
    ): Result<Any, Throwable> =
        getSchedulePreferenceByStaffId(staffId)
            .flatMap {
                val schedulePreferenceToUpdate = it.copy(
                    googleCalendarLastUpdated = lastUpdatedTime,
                    googleNextSyncToken = nextSyncToken,
                    isDoingFullSync = false,
                )
                schedulePreferenceService.update(schedulePreferenceToUpdate)
            }
            .then { updatedSchedulePreference ->
                logger.info(
                    "GoogleCalendarEventServiceImpl::synchronize Google Calendar synchronized and schedule preference updated",
                    "updated" to updatedSchedulePreference
                )
            }

    suspend fun queryGoogleCalendarEvents(
        queryFilters: GoogleCalendarEventsQueryFilters,
        staffId: UUID,
        googleRefreshToken: String,
        alreadyRepublished: Boolean = false
    ): Result<Any, Throwable> = span("queryGoogleCalendarEvents") { span ->
        coResultOf<Boolean, Throwable> {
            coroutineScope {
                span.setAttribute("staff_id", staffId)
                span.setQueryFilters(queryFilters)

                val schedulePreference = schedulePreferenceService.getByStaffId(staffId).get()
                span.setAttribute("has_staff_schedules", schedulePreference.hasStaffSchedules)
                span.setAttribute("is_doing_full_sync", schedulePreference.isDoingFullSync)

                if (schedulePreference.shouldSkipQueryEvents()) return@coroutineScope false

                var eventsQuery = buildEventsQuery(googleRefreshToken, queryFilters).get()

                val emptyGoogleNextSyncToken = schedulePreference.googleNextSyncToken.isNullOrEmpty()
                span.setAttribute("has_google_next_sync_token", emptyGoogleNextSyncToken.not())
                if (!emptyGoogleNextSyncToken) eventsQuery =
                    eventsQuery.setSyncToken(schedulePreference.googleNextSyncToken)

                val pageToken = queryFilters.pageToken
                span.setAttribute("page_token", pageToken.orEmpty())
                if (!pageToken.isNullOrEmpty()) eventsQuery = eventsQuery.setPageToken(pageToken)

                val events = try {
                    eventsQuery.execute()
                } catch (ex: GoogleJsonResponseException) {
                    when {
                        ex.statusCode == 410 -> {
                            span.setInvalidSyncToken(ex)
                            schedulePreferenceService.update(schedulePreference.copy(googleNextSyncToken = null))
                            if (!alreadyRepublished) {
                                publishAndSetAlreadyRepublished(staffId, queryFilters, googleRefreshToken)
                                return@coroutineScope true
                            }
                        }

                        else -> throw ex
                    }
                    throw ex
                }

                val staffDeferred = async { staffService.get(staffId) }

                val items: List<Event> = events?.items as List<Event>
                span.setAttribute("events_count", items.size)

                val existingEvents = externalCalendarEventService
                    .getByEventIdsAndStaffId(items.map { it.id }, staffId).get()
                val existingRecurrentEvents = externalCalendarRecurrentEventService
                    .getByExternalIds(staffId, items.map { it.id }).get()
                val existingEventIds = existingEvents.map { it.externalEventId }.plus(
                    existingRecurrentEvents.map { it.externalEventId }
                )
                val staff = staffDeferred.await().get()

                items.map { event ->
                    val avoidPublishObsoleteEvents = isNewOrObsoleteEvent(existingEventIds, event, existingEvents)
                    publishGoogleCalendarEvent(event, staff, avoidPublishObsoleteEvents)
                }

                span.setAttribute("has_next_page_token", events.nextPageToken.isNotNullOrBlank())
                publishQueryEventsEvent(events, staffId, queryFilters, googleRefreshToken)
                true
            }
        }.recordResult(span)
    }

    private fun isNewOrObsoleteEvent(
        existingEventIds: List<String>,
        event: Event,
        existingEvents: List<ExternalCalendarEvent>
    ): Boolean {
        val isNewEvent = existingEventIds.any { it == event.id }.not()
        if (isNewEvent) return true

        return existingEvents.any { it.externalEventId == event.id && it.startTime.isBeforeEq(LocalDateTime.now()) }
    }

    suspend fun renewWebhookChannelsCloseToExpiration(referenceDate: LocalDate): Result<Any, Throwable> =
        schedulePreferenceService.getByGoogleCalendarWebhookCloseToExpiration(referenceDate).mapEach {
            kafkaProducerService.produce(GoogleCalendarWebhookSubscriptionRequestedEvent(it))
            true
        }

    suspend fun getFutureGoogleCalendarEventsActiveRecurrencesForOneDay(referenceDate: LocalDate): Result<Any, Throwable> =
        coResultOf {
            coroutineScope {

                val referenceDateWeekday = referenceDate.dayOfWeek.toString().substring(0, 2)
                Weekday.get(referenceDateWeekday)?.let { weekday ->
                    listOf(
                        async {
                            processFutureGoogleCalendarEvents(
                                referenceDate,
                                FOR_WEEKDAY_WITH_UNTIL_DATE,
                                externalCalendarRecurrentEventService.countActiveForWeekdayWithUntilDate(weekday)
                            )
                        },
                        async {
                            processFutureGoogleCalendarEvents(
                                referenceDate,
                                WITH_UNTIL_DATE_WITHOUT_WEEKDAY,
                                externalCalendarRecurrentEventService.countActiveWithUntilDateWithoutWeekday()
                            )
                        },
                        async {
                            processFutureGoogleCalendarEvents(
                                referenceDate,
                                FOR_WEEKDAY_WITHOUT_UNTIL_DATE,
                                externalCalendarRecurrentEventService.countActiveForWeekdayWithoutUntilDate(weekday)
                            )
                        },
                        async {
                            processFutureGoogleCalendarEvents(
                                referenceDate,
                                WITHOUT_UNTIL_DATE_WITHOUT_WEEKDAY,
                                externalCalendarRecurrentEventService.countActiveWithoutUntilDateWithoutWeekday()
                            )
                        }
                    )
                }?.awaitAll()

                true
            }
        }

    suspend fun getFutureGoogleCalendarEventsActiveRecurrencesForOneDayForPage(
        referenceDate: LocalDate,
        page: Int,
        type: FetchInstancesForRecurrentEventsType,
    ): Result<Any, Throwable> = coResultOf {
        val timeMin = referenceDate.atBeginningOfTheDay().plusDays(numberOfDaysInFutureToGetEvents - 1)
        // 1 day to add to make sure we get all events, sometimes there are recurrent events from a weekday
        // but that is meant to happen on the other, for example weekday TUESDAY, but happens at ~00:00 at wednesdays
        val timeMax = referenceDate.atEndOfTheDay().plusDays(numberOfDaysInFutureToGetEvents + 1)

        val referenceDateWeekday = referenceDate.dayOfWeek.toString().substring(0, 2)
        Weekday.get(referenceDateWeekday)?.let { weekDay ->
            logger.info(
                "GoogleCalendarEventServiceImpl::getFutureGoogleCalendarEventsActiveRecurrencesForOneDay",
                "weekday" to weekDay
            )

            val recurrentEvents = when (type) {
                FOR_WEEKDAY_WITHOUT_UNTIL_DATE ->
                    externalCalendarRecurrentEventService.getActiveForWeekdayWithoutUntilDate(
                        weekDay,
                        page,
                        fetchFutureGoogleCalendarEventsForActiveRecurrencesPageSize
                    )

                WITHOUT_UNTIL_DATE_WITHOUT_WEEKDAY ->
                    externalCalendarRecurrentEventService.getActiveWithoutUntilDateWithoutWeekday(
                        page,
                        fetchFutureGoogleCalendarEventsForActiveRecurrencesPageSize
                    )

                WITH_UNTIL_DATE_WITHOUT_WEEKDAY ->
                    externalCalendarRecurrentEventService.getActiveWithUntilDateWithoutWeekday(
                        page,
                        fetchFutureGoogleCalendarEventsForActiveRecurrencesPageSize
                    )

                FOR_WEEKDAY_WITH_UNTIL_DATE ->
                    externalCalendarRecurrentEventService.getActiveForWeekdayWithUntilDate(
                        weekDay,
                        page,
                        fetchFutureGoogleCalendarEventsForActiveRecurrencesPageSize
                    )
            }

            recurrentEvents
                .mapEach { externalCalendarRecurrentEvent ->
                    logger.info(
                        "GoogleCalendarEventServiceImpl::getFutureGoogleCalendarEventsActiveRecurrencesForOneDay will fetch events for recurrence",
                        "external_calendar_recurrent_event_id" to externalCalendarRecurrentEvent.id
                    )
                    getFutureGoogleCalendarEventsForRecurrence(externalCalendarRecurrentEvent, timeMin, timeMax)
                }
        } ?: run {
            logger.error(
                "GoogleCalendarEventServiceImpl::getFutureGoogleCalendarEventsActiveRecurrencesForOneDay cannot find weekday",
                "weekday_tried" to referenceDateWeekday
            )
            throw Exception("Cannot find weekday")
        }

        true
    }

    suspend fun createRecurrentEventAtGoogleCalendar(staffSchedule: StaffSchedule): Result<Any, Throwable> =
        if (staffSchedule.isDedicatedToAppointments()) true.success()
        else getSchedulePreferenceByStaffId(staffId = staffSchedule.staffId).map { schedulePreference ->
            val googleRefreshToken = getGoogleRefreshTokenForStaff(schedulePreference = schedulePreference).get()

            val googleCalendarEventsService = getGoogleCalendarService(googleRefreshToken).get()

            var dateToCreateFirstEvent = LocalDate.now()

            while (dateToCreateFirstEvent.dayOfWeek.toString() != staffSchedule.weekDay.toString()) {
                dateToCreateFirstEvent = dateToCreateFirstEvent.plusDays(1)
            }

            val startDateTime = dateToCreateFirstEvent.atTime(staffSchedule.startHour)
            var endDateTime = dateToCreateFirstEvent.atTime(staffSchedule.untilHour)

            if (endDateTime.isBeforeEq(startDateTime)) {
                endDateTime = endDateTime.plusDays(1)
            }

            val event = buildEvent(
                extendedPropertiesMap = mapOf(
                    "staffScheduleId" to staffSchedule.id.toString(),
                    "staffScheduleType" to staffSchedule.type.toString()
                ),
                start = startDateTime,
                end = endDateTime,
                timeZone = "UTC",
                withDefaultDescription = false,
                recurrence = RecurrenceRule(
                    byDay = listOf(Weekday.valueOf(staffSchedule.weekDay.toString())),
                    frequency = RecurrenceRuleFrequency.WEEKLY,
                    interval = 1,
                ).toString(),
                summary = staffSchedule.type.toString(),
            )

            googleCalendarEventsService.insert(defaultCalendarId, event)
                .setSendUpdates("all")
                .execute()

            true
        }

    suspend fun stopRecurrenceOfGoogleCalendarEvent(staffSchedule: StaffSchedule): Result<Any, Throwable> =
        getSchedulePreferenceByStaffId(staffId = staffSchedule.staffId)
            .flatMapPair {
                externalCalendarRecurrentEventService.getActiveRecurrentEventByStaffScheduleId(staffScheduleId = staffSchedule.id)
            }.map { (externalCalendarRecurrentEvent, schedulePreference) ->
                val googleRefreshToken = getGoogleRefreshTokenForStaff(schedulePreference = schedulePreference).get()

                val googleCalendarEventsService = getGoogleCalendarService(googleRefreshToken).get()

                val event = googleCalendarEventsService.get(
                    defaultCalendarId,
                    externalCalendarRecurrentEvent.externalEventId
                ).execute()

                val recurrence = RecurrenceRule.fromGoogleRecurrence(event.recurrence).get()

                val dateToStop = LocalDateTime.now()

                val recurrenceUpdated = recurrence.copy(until = dateToStop)

                event.recurrence = listOf(recurrenceUpdated.toString())

                externalCalendarEventService.getFirstEventForRecurrence(externalCalendarRecurrentEvent.id).map {
                    if (it.startTime.isBeforeEq(dateToStop)) {
                        event.status = "cancelled"
                    }
                }

                googleCalendarEventsService.update(defaultCalendarId, event.id, event)
                    .setSendUpdates("all")
                    .execute()

                true
            }.coFoldNotFound { true.success() }

    suspend fun queryGoogleCalendarEventsForRecurrence(
        queryFilters: GoogleCalendarEventsQueryFilters,
        staffId: UUID,
    ): Result<Any, Throwable> = span("queryGoogleCalendarEventsForRecurrence") { span ->
        span.setScheduleFilters(queryFilters, staffId)

        getSchedulePreferenceByStaffId(staffId = staffId)
            .flatMapPair { schedulePreference ->
                span.setAttribute("schedule_preference_id", schedulePreference.id)
                span.setAttribute("has_staff_schedules", schedulePreference.hasStaffSchedules)
                span.setAttribute("is_doing_full_sync", schedulePreference.isDoingFullSync)
                if (schedulePreference.shouldSkipQueryEvents()) return@span true.success()
                staffService.get(staffId)
            }
            .flatMap { (staff, schedulePreference) ->
                if (!staff.isHealthProfessionalOrNavigator() || !staff.active) return@flatMap false.success()
                if (schedulePreference.status != Status.ACTIVE) return@flatMap true.success()

                val googleRefreshToken = getGoogleRefreshTokenForStaff(schedulePreference = schedulePreference).get()

                getGoogleCalendarEvents(googleRefreshToken, queryFilters)
                    .map { events ->
                        val items: List<Event> = events.items as List<Event>

                        val existingEvents =
                            externalCalendarEventService.getByEventIdsAndStaffId(items.map { it.id }, staffId).get()
                        val existingRecurrentEvents = externalCalendarRecurrentEventService
                            .getByExternalIds(staffId, items.map { it.id }).get()

                        val existingEventIds = existingEvents.map { it.externalEventId }.plus(
                            existingRecurrentEvents.map { it.externalEventId }
                        )

                        items.map { event ->
                            val isCreate = existingEventIds.find { it == event.id } == null
                            publishGoogleCalendarEvent(event, staff, isCreate)
                        }

                        if (events.nextPageToken != null) {
                            GoogleCalendarEventsFromRecurrenceRequestedEvent(
                                staffId,
                                queryFilters.copy(pageToken = events.nextPageToken),
                            ).let { event ->
                                kafkaProducerService.produce(event)
                            }
                        }

                        true
                    }
            }
    }

    suspend fun createOrUpdateEvent(
        event: GoogleCalendarEventPayload,
        staffId: UUID
    ): Result<Any, Throwable> =
        event.recurrence.orEmpty()
            .takeIf { it.isNotEmpty() }
            ?.let { createRecurrentEvent(event, it, staffId) }
            ?: createSingleEvent(event, staffId)

    suspend fun removeFromGoogleCalendar(
        appointmentScheduleId: UUID
    ): Result<List<UUID>, Throwable> = span("removeFromGoogleCalendar") { span ->
        span.setAttribute("appointment_schedule_id", appointmentScheduleId)
        externalCalendarEventService.getByAppointmentScheduleId(appointmentScheduleId)
            .mapEach { externalCalendarEvent ->
                try {
                    span.setAttribute("external_calendar_event_id", externalCalendarEvent.id)
                    span.setAttribute("external_calendar_event_status", externalCalendarEvent.status)
                    span.setAttribute("transparency", externalCalendarEvent.transparency)
                    span.setAttribute("staff_id", externalCalendarEvent.staffId)
                    buildGoogleCalendarService(externalCalendarEvent.staffId)
                        .get()
                        .delete(DEFAULT_CALENDAR_ID, externalCalendarEvent.externalEventId)
                        .setSendUpdates("all")
                        .execute()
                    appointmentScheduleId
                } catch (throwable: GoogleJsonResponseException) {
                    span.setAttribute("google_json_response_exception", throwable.message.orEmpty())
                    span.setAttribute("google_json_response_exception_status_code", throwable.statusCode)
                    if (throwable.statusCode == 410 || throwable.statusCode == 404) appointmentScheduleId
                    else throw throwable
                } catch (throwable: Throwable) {
                    span.setAttribute("error", throwable.message.orEmpty())
                    throw throwable
                }
            }
    }

    suspend fun createEventAtGoogleCalendar(appointmentScheduleId: UUID): Result<AppointmentSchedule, Throwable> =
        span("createEventAtGoogleCalendar") { span ->
            coroutineScope {
                span.setAttribute("appointment_schedule_id", appointmentScheduleId)
                appointmentScheduleService.get(appointmentScheduleId)
                    .flatMap { appointmentSchedule ->
                        span.setAttribute("is_scheduled", appointmentSchedule.isScheduled)
                        span.setAttribute("staff_id", appointmentSchedule.staffId)
                        span.setAttribute("person_id", appointmentSchedule.personId)

                        if (appointmentSchedule.isScheduled.not()) {
                            span.setAttribute("error", "appointment_schedule_not_scheduled")
                            return@flatMap appointmentSchedule.success()
                        }

                        val staffIdFromAppointmentSchedule = appointmentSchedule.staffId ?: run {
                            span.setAttribute("error", "appointment_schedule_without_staff_id")
                            throw Exception("Appointment schedule without staff id")
                        }

                        val personDeferred = async { personService.get(appointmentSchedule.personId, false).get() }

                        // TODO change when it is possible to schedule with health professional
                        val staffDeferred =
                            async { staffService.get(staffIdFromAppointmentSchedule).getOrNullIfNotFound() }

                        schedulePreferenceService.getByStaffId(staffIdFromAppointmentSchedule)
                            .flatMap { schedulePreference ->
                                val googleRefreshToken = schedulePreference.googleRefreshToken
                                    ?: run {
                                        span.setAttribute("error", "google_calendar_refresh_token_not_found")
                                        throw NotFoundException("Google Calendar refresh token not found for user")
                                    }

                                googleCalendarApi.getCalendarService(googleRefreshToken)
                                    .map { googleCalendarEventsService ->
                                        val appointmentScheduleEventType =
                                            appointmentSchedule.appointmentScheduleEventTypeId?.let { eventTypeId ->
                                                getAppointmentScheduleEventType(eventTypeId)
                                            }

                                        val shouldCreateGoogleMeetLink =
                                            appointmentSchedule.providerUnitId?.toString().isNullOrBlank()

                                        appointmentSchedule.endTime?.let { endTime ->
                                            val event = buildEvent(
                                                appointmentSchedule = appointmentSchedule,
                                                endDateTime = endTime,
                                                staffName = staffDeferred.await()?.fullName,
                                                person = personDeferred.await(),
                                                appointmentScheduleEventTypeDescription = appointmentScheduleEventType?.description,
                                                useGoogleMeet = shouldCreateGoogleMeetLink
                                            )

                                            val conferenceDataVersion = if (shouldCreateGoogleMeetLink) 1 else 0

                                            googleCalendarEventsService.insert(defaultCalendarId, event)
                                                .setConferenceDataVersion(conferenceDataVersion)
                                                .setSendUpdates("all")
                                                .execute()

                                            appointmentSchedule
                                        } ?: run {
                                            span.setAttribute("error", "event_without_end_time")
                                            throw Exception("Event without endTime")
                                        }
                                    }
                                    .recordResult(span)
                            }
                    }
            }
        }

    suspend fun createPreAppointmentEventAtGoogleCalendar(appointmentScheduleId: UUID): Result<AppointmentSchedule, Throwable> =
        coroutineScope {
            appointmentScheduleService.get(appointmentScheduleId)
                .flatMap { appointmentSchedule ->
                    when {
                        appointmentSchedule.isScheduled.not() -> return@flatMap appointmentSchedule.success()
                        appointmentSchedule.providerUnitId == null -> return@flatMap appointmentSchedule.success()
                        appointmentScheduleTypeIsInvalidForPreAppointment(appointmentSchedule.type) -> return@flatMap appointmentSchedule.success()
                    }

                    val staffToCreatePreAppointmentEvents = FeatureService.get(
                        namespace = FeatureNamespace.SCHEDULE,
                        key = "staff_to_create_pre_appointment_events",
                        defaultValue = "93190bbe-e7b2-4d2d-a20e-4d4eaef9a900"
                    ).toSafeUUID()

                    val personDeferred = async { personService.get(appointmentSchedule.personId, false).get() }

                    schedulePreferenceService.getByStaffId(staffToCreatePreAppointmentEvents)
                        .flatMap { schedulePreference ->
                            val googleRefreshToken = schedulePreference.googleRefreshToken
                                ?: run {
                                    logger.error("${classSimpleName()}::createPreAppointmentEventAtGoogleCalendar Google Calendar refresh token not found for user")
                                    throw NotFoundException("Google Calendar refresh token not found for user")
                                }

                            googleCalendarApi.getCalendarService(googleRefreshToken)
                                .map { googleCalendarEventsService ->
                                    val appointmentScheduleEventType =
                                        appointmentSchedule.appointmentScheduleEventTypeId?.let { eventTypeId ->
                                            getAppointmentScheduleEventType(eventTypeId)
                                        }

                                    appointmentSchedule.endTime?.let { endTime ->
                                        val event = buildEvent(
                                            appointmentSchedule = appointmentSchedule,
                                            endDateTime = endTime,
                                            staffName = "Agendamentos Alice",
                                            person = personDeferred.await(),
                                            appointmentScheduleEventTypeDescription = appointmentScheduleEventType?.description,
                                            isPreAppointmentEvent = true,
                                        )

                                        googleCalendarEventsService.insert(defaultCalendarId, event)
                                            .setSendUpdates("all")
                                            .execute()

                                        appointmentSchedule
                                    } ?: run {
                                        logger.error("${classSimpleName()}::createPreAppointmentEventAtGoogleCalendar event without endTime")
                                        throw Exception("Event without endTime")
                                    }
                                }.thenError {
                                    logger.error("${classSimpleName()}::createPreAppointmentEventAtGoogleCalendar could not instantiate Google Calendar service")
                                }
                        }
                }
        }

    fun getGoogleStatus(value: String): ExternalEventStatus =
        when (value) {
            ExternalEventStatus.CANCELLED.value -> ExternalEventStatus.CANCELLED
            ExternalEventStatus.CONFIRMED.value -> ExternalEventStatus.CONFIRMED
            ExternalEventStatus.TENTATIVE.value -> ExternalEventStatus.TENTATIVE
            else -> ExternalEventStatus.CONFIRMED
        }

    private suspend fun getAppointmentScheduleEventType(appointmentScheduleEventTypeId: UUID) =
        appointmentScheduleEventTypeService.get(appointmentScheduleEventTypeId)
            .thenError {
                logger.error(
                    "GoogleCalendarEventServiceImpl::createEventAtGoogleCalendar error getting event type",
                    "appointment_schedule_event_type_id" to appointmentScheduleEventTypeId,
                    it
                )
            }.getOrElse { null }


    private fun appointmentScheduleTypeIsInvalidForPreAppointment(appointmentScheduleType: AppointmentScheduleType) =
        listOf(AppointmentScheduleType.PROC_ONSITE, AppointmentScheduleType.PROC_NURSE, AppointmentScheduleType.TEST)
            .contains(appointmentScheduleType)

    private fun toDate(zonedDateTime: ZonedDateTime) = Date.from(zonedDateTime.toInstant())

    private fun buildChannel(): Channel {
        val channel = Channel()
        channel.address = "$SCHEDULE_BFF_API_URL/webhooks/google/calendar/event_updates"
        channel.id = RangeUUID.generate().toString()
        channel.type = "web_hook"
        return channel
    }

    private suspend fun publishGoogleCalendarEvent(
        event: Event,
        staff: Staff,
        avoidPublishObsoleteEvents: Boolean = false
    ) = span("publishGoogleCalendarEvent") { span ->
        val eventHasStartTime = event.start != null
        val eventHasEndTime = event.end != null
        span.setAttribute("event_has_start_time", eventHasStartTime)
        span.setAttribute("event_has_end_time", eventHasEndTime)
        if (eventHasStartTime.not() || eventHasEndTime.not()) return@span

        val beginningOfToday = LocalDate.now().atBeginningOfTheDay()
        val recurrenceRule = event.recurrence?.let { RecurrenceRule.fromGoogleRecurrence(it).get() }
        val isObsoleteRecurrence = checkForObsoleteRecurrence(recurrenceRule, beginningOfToday)
        val attendeesEmails = getAttendeesEmails(event)
        val persons = getPersons(attendeesEmails)
        val googleCalendarEventPayload = GoogleCalendarEventPayloadConverter.convert(event, staff, persons)
        val isObsoleteEvent = googleCalendarEventPayload.endTime.isBeforeEq(beginningOfToday)
        val eventFromRecurrenceVeryFarInFuture =
            avoidPublishObsoleteEvents &&
                    event.recurringEventId != null &&
                    event.recurrence == null &&
                    googleCalendarEventPayload.startTime >= LocalDateTime.now()
                .plusDays(numberOfDaysInFutureToGetEvents + 10)

        val isCreatingAPastEvent = avoidPublishObsoleteEvents && isObsoleteEvent && isObsoleteRecurrence

        span.setGoogleCalendarEventInfo(
            staff.id,
            avoidPublishObsoleteEvents,
            recurrenceRule,
            isObsoleteRecurrence,
            isObsoleteEvent,
            persons,
            attendeesEmails,
            isCreatingAPastEvent,
            eventFromRecurrenceVeryFarInFuture
        )

        if (!isCreatingAPastEvent && !eventFromRecurrenceVeryFarInFuture) {
            publishGoogleCalendarEventCreationRequestedEvent(googleCalendarEventPayload, staff.id)
        }
    }

    private suspend fun getPersons(attendeesEmails: List<String>) =
        if (attendeesEmails.isNotEmpty()) personService.findByEmails(attendeesEmails).getOrElse { emptyList() }
        else emptyList()

    private fun checkForObsoleteRecurrence(
        recurrenceRule: RecurrenceRule?,
        beginningOfToday: LocalDateTime
    ) = (recurrenceRule == null || recurrenceRule.until?.isBeforeEq(beginningOfToday) == true)

    private fun getAttendeesEmails(event: Event) = (event.attendees
        ?.map { it.email }
        ?.filterNot { it.contains("alice.com.br") }
        ?.mapNotNull { it }
        ?: emptyList())


    private suspend fun createSingleEvent(
        event: GoogleCalendarEventPayload,
        staffId: UUID
    ): Result<ExternalCalendarEvent, Throwable> {
        externalCalendarRecurrentEventService.getByExternalId(staffId, event.id)
            .flatMap { externalCalendarRecurrentEvent ->
                externalCalendarRecurrentEventService.update(
                    externalCalendarRecurrentEvent.copy(
                        status = ExternalEventStatus.CANCELLED,
                        externalUpdatedAt = event.externalUpdatedAt
                    ),
                    externalCalendarRecurrentEvent,
                )
            }

        val googleCalendarEvent = ExternalCalendarEvent(
            startTime = event.startTime,
            endTime = event.endTime,
            staffId = staffId,
            status = getGoogleStatus(event.status),
            externalEventId = event.id,
            provider = ExternalCalendarProvider.GOOGLE,
            externalUpdatedAt = event.externalUpdatedAt,
            appointmentScheduleId = event.appointmentScheduleId,
            transparency = event.transparency,
            summary = event.summary,
            responseStatus = event.responseStatus ?: ExternalEventResponseStatus.ACCEPTED
        )

        val googleMeetLink = event.googleMeetLink
        val recurringEventId = event.recurringEventId?.trim().orEmpty()
        return if (recurringEventId.isBlank()) {
            externalCalendarEventService.upsert(googleCalendarEvent, googleMeetLink)
        } else {
            externalCalendarRecurrentEventService.getByExternalId(staffId, recurringEventId).flatMap {
                externalCalendarEventService.upsert(
                    googleCalendarEvent.copy(externalCalendarRecurrentEventId = it.id),
                    googleMeetLink
                )
            }.coFoldNotFound {
                externalCalendarEventService.upsert(googleCalendarEvent, googleMeetLink)
            }
        }
    }

    private suspend fun createRecurrentEvent(
        event: GoogleCalendarEventPayload,
        recurrence: List<String>,
        staffId: UUID
    ): Result<ExternalCalendarRecurrentEvent, Throwable> {
        externalCalendarEventService.getByEventIdAndStaffId(event.id, staffId).flatMap { externalCalendarEvent ->
            externalCalendarEventService.update(
                externalCalendarEvent.copy(
                    status = ExternalEventStatus.CANCELLED,
                    externalUpdatedAt = event.externalUpdatedAt,
                )
            )
        }

        val recurrenceRule = RecurrenceRule.fromGoogleRecurrence(recurrence).get()
        val googleCalendarRecurrentEvent = ExternalCalendarRecurrentEvent(
            staffId = staffId,
            status = getGoogleStatus(event.status),
            externalEventId = event.id,
            provider = ExternalCalendarProvider.GOOGLE,
            recurrence = recurrence,
            byDay = recurrenceRule.byDay,
            untilDateTime = recurrenceRule.until,
            externalUpdatedAt = event.externalUpdatedAt,
            transparency = event.transparency,
            staffScheduleId = event.staffScheduleId,
            firstEventStartTime = event.startTime,
            summary = event.summary,
        )

        return externalCalendarRecurrentEventService.upsert(googleCalendarRecurrentEvent)
    }

    private fun getGoogleRefreshTokenForStaff(schedulePreference: SchedulePreference): Result<String, Throwable> =
        resultOf {
            schedulePreference.googleRefreshToken ?: run {
                logger.error(
                    "GoogleCalendarEventServiceImpl::getGoogleRefreshTokenForUser Google Calendar refresh token not found for staff",
                    "staff_id" to schedulePreference.staffId
                )
                throw GoogleCalendarRefreshTokenNotFoundException()
            }
        }

    private suspend fun getGoogleCalendarService(googleRefreshToken: String): Result<Calendar.Events, Throwable> =
        googleCalendarApi.getCalendarService(googleRefreshToken).coFoldNotFound {
            logger.error("GoogleCalendarEventServiceImpl::getGoogleCalendarService could not instantiate Google Calendar service")
            throw GoogleCalendarServiceNotFoundException()
        }

    private suspend fun getSchedulePreferenceByStaffId(staffId: UUID): Result<SchedulePreference, Throwable> =
        schedulePreferenceService.getByStaffId(staffId = staffId)

    private suspend fun findStaffByEmail(staffEmail: String) = staffService.findByEmail(staffEmail)

    private suspend fun getOrCreateSchedulePreferenceByStaffId(staffId: UUID) =
        schedulePreferenceService.getOrCreate(staffId)
            .then { schedulePreference ->
                logger.info(
                    "GoogleCalendarEventServiceImpl::getOrCreateSchedulePreferenceByStaffId success",
                    "id" to schedulePreference.id,
                    "staff_id" to schedulePreference.staffId,
                    "zoom_link" to schedulePreference.zoomLink,
                    "google_calendar_last_updated" to schedulePreference.googleCalendarLastUpdated,
                    "google_calendar_webhook_expiration" to schedulePreference.googleCalendarWebhookExpiration
                )
            }
            .thenError {
                logger.error(
                    "GoogleCalendarEventServiceImpl::getOrCreateSchedulePreferenceByStaffId error",
                    "error" to it.message
                )
            }

    private suspend fun updateSchedulePreference(schedulePreference: SchedulePreference) =
        schedulePreferenceService.update(schedulePreference)
            .thenError {
                logger.error(
                    "GoogleCalendarEventServiceImpl::updateSchedulePreference error",
                    "error" to it.message
                )
            }
            .map { true }

    private suspend fun buildGoogleCalendarService(staffId: UUID): Result<Calendar.Events, Throwable> =
        span("buildGoogleCalendarService") { span ->
            coroutineScope {
                span.setAttribute("staff_id", staffId.toString())
                schedulePreferenceService.getByStaffId(staffId = staffId)
                    .map { schedulePreference ->
                        if (schedulePreference.status != Status.ACTIVE) {
                            span.setAttribute("error", "Staff is inactive")
                            throw NotFoundException("active_schedule_preference_not_found")
                        }
                        val googleRefreshToken = schedulePreference.googleRefreshToken
                        if (googleRefreshToken == null) {
                            span.setAttribute("error", "Google Calendar refresh token not found for user")
                            throw NotFoundException("google_refresh_token_not_found")
                        }
                        span.setAttribute(
                            "has_google_refresh_token",
                            googleRefreshToken.isNotNullOrBlank()
                        )
                        googleCalendarApi.getCalendarService(googleRefreshToken).get()
                    }
            }
        }

    private suspend fun getGoogleCalendarEvents(
        googleRefreshToken: String,
        queryFilters: GoogleCalendarEventsQueryFilters,
    ) = span("getGoogleCalendarEvents") { span ->
        catchResult<Events, Throwable> {
            span.setAttribute("external_calendar_recurrent_event_external_id", queryFilters.recurringEventId.toString())
            span.setAttribute("time_min", queryFilters.timeMin?.toStringRfc3339().toString())
            span.setAttribute("time_max", queryFilters.timeMax?.toStringRfc3339().toString())

            queryFilters.recurringEventId ?: throw Exception("No recurring event id to expand recurrence")

            getGoogleCalendarService(googleRefreshToken).map { googleCalendarEventsService ->
                googleCalendarEventsService.instances(queryFilters.calendarId, queryFilters.recurringEventId)
                    .setShowDeleted(queryFilters.showDeleted)
                    .setMaxResults(queryFilters.maxResults)
                    .setTimeMin(queryFilters.timeMin)
                    .setTimeMax(queryFilters.timeMax)
                    .let { query ->
                        if (queryFilters.pageToken.isNotNullOrBlank()) query.setPageToken(queryFilters.pageToken)
                        else query
                    }
                    .execute()
                    .also { span.setAttribute("events", it.size) }
            }
        }.recordResult(span)
    }

    private suspend fun processFutureGoogleCalendarEvents(
        referenceDate: LocalDate,
        type: FetchInstancesForRecurrentEventsType,
        count: Result<Int, Throwable>
    ) {
        count
            .map { it / fetchFutureGoogleCalendarEventsForActiveRecurrencesPageSize }
            .map {
                IntRange(0, it).forEach { pageNumber ->
                    kafkaProducerService.produce(
                        GoogleCalendarFetchInstancesForRecurrentEventsForPageEvent(
                            referenceDate = referenceDate,
                            page = pageNumber,
                            type = type
                        )
                    )
                }
            }
    }

    private suspend fun <T : Any> handleGoogleRequest(
        event: T,
        staffId: UUID,
        externalEventId: String? = null,
        block: suspend () -> T
    ) = try {
        block()
    } catch (exception: GoogleJsonResponseException) {
        logger.error(
            "GoogleCalendarEventServiceImpl::handleGoogleRequest not found",
            "status_code" to exception.statusCode,
            exception
        )
        if (exception.statusCode == 404) {
            when (event) {
                is ExternalCalendarEvent -> fromExternalCalendarEvent(event.cancel(), externalEventId)
                is ExternalCalendarRecurrentEvent -> fromExternalCalendarRecurrentEvent(event.cancel())
                else -> throw InvalidArgumentException("Invalid event type")
            }.let { payload ->
                publishGoogleCalendarEventCreationRequestedEvent(payload, staffId)
            }
        }
        event
    } catch (exception: Exception) {
        logger.error(
            "GoogleCalendarEventServiceImpl::handleGoogleRequest exception",
            "type" to exception.javaClass.toString(),
            exception
        )
        throw exception
    }

    private suspend fun publishGoogleCalendarEventCreationRequestedEvent(
        googleCalendarEventPayload: GoogleCalendarEventPayload,
        staffId: UUID
    ) = span("publishGoogleCalendarEventCreationRequestedEvent") { span ->
        val isRecurringEvent = googleCalendarEventPayload.recurringEventId.isNotNullOrBlank()
        span.setAttribute("staff_id", staffId.toString())
        span.setAttribute("is_recurring_event", isRecurringEvent)
        span.setAttribute("google_calendar_event_id", googleCalendarEventPayload.id)
        span.setAttribute(
            "google_calendar_event_recurring_event_id",
            googleCalendarEventPayload.recurringEventId.toString()
        )
        val eventToPublish = if (isRecurringEvent) {
            GoogleCalendarRecurringEventCreationRequestedEvent(
                googleCalendarEventPayload,
                staffId
            )
        } else {
            GoogleCalendarEventCreationRequestedEvent(
                googleCalendarEventPayload,
                staffId
            )
        }

        kafkaProducerService.produce(eventToPublish, staffId.toString())
    }

    private suspend fun buildEventsQuery(googleRefreshToken: String, queryFilters: GoogleCalendarEventsQueryFilters) =
        getGoogleCalendarService(googleRefreshToken)
            .map { googleCalendarEventsService ->
                googleCalendarEventsService.list(queryFilters.calendarId)
                    .setShowDeleted(queryFilters.showDeleted)
                    .setMaxResults(queryFilters.maxResults)
                    .setSingleEvents(queryFilters.singleEvents)
            }

    private suspend fun publishAndSetAlreadyRepublished(
        staffId: UUID,
        queryFilters: GoogleCalendarEventsQueryFilters,
        googleRefreshToken: String
    ) = kafkaProducerService.produce(
        GoogleCalendarQueryEventsEvent(
            staffId,
            queryFilters.copy(pageToken = null),
            googleRefreshToken,
            alreadyRepublished = true
        )
    )

    private fun Span.setInvalidSyncToken(ex: Throwable) {
        setAttribute("error", "Google Calendar sync token is invalid")
        recordException(ex)
        setStatus(StatusCode.ERROR, "Google Calendar sync token is invalid")
    }

    private suspend fun publishQueryEventsEvent(
        events: Events,
        staffId: UUID,
        queryFilters: GoogleCalendarEventsQueryFilters,
        googleRefreshToken: String
    ) =
        if (events.nextPageToken != null) {
            GoogleCalendarQueryEventsEvent(
                staffId,
                queryFilters.copy(pageToken = events.nextPageToken),
                googleRefreshToken
            )
        } else {
            GoogleCalendarFinishedEventsQueryEvent(
                LocalDateTime.now(),
                events.nextSyncToken,
                staffId
            )
        }.let { kafkaProducerService.produce(it) }

    private fun Span.setScheduleFilters(
        queryFilters: GoogleCalendarEventsQueryFilters,
        staffId: UUID
    ) {
        setAttribute("staff_id", staffId.toString())
        setAttribute("calendar_id", queryFilters.calendarId)
        setAttribute("single_events", queryFilters.singleEvents)
        setAttribute("max_results", queryFilters.maxResults)
        setAttribute("show_deleted", queryFilters.showDeleted)
        setAttribute("sync_token", queryFilters.syncToken.orEmpty())
        setAttribute("page_token", queryFilters.pageToken.orEmpty())
    }

    private fun Span.setChannel(channel: Channel) {
        setAttribute("channel_id", channel.id)
        setAttribute("channel_resource_id", channel.resourceId)
        setAttribute("channel_resource_uri", channel.resourceUri)
        setAttribute("channel_resource_address", channel.address)
    }

    private fun Span.setQueryFilters(queryFilters: GoogleCalendarEventsQueryFilters) {
        setAttribute("query_filter_calendar_id", queryFilters.calendarId)
        setAttribute("query_filter_show_deleted", queryFilters.showDeleted)
        setAttribute("query_filter_max_results", queryFilters.maxResults)
        setAttribute("query_filter_single_events", queryFilters.singleEvents)
        setAttribute("query_filter_time_min", queryFilters.timeMin)
        setAttribute("query_filter_time_max", queryFilters.timeMax)
        setAttribute("query_filter_recurring_event_id", queryFilters.recurringEventId.orEmpty())
        setAttribute("page_t", queryFilters.pageToken.toString())
        setAttribute("sync_t", queryFilters.syncToken.toString())
    }

    private fun Span.setSchedulePreference(schedulePreference: SchedulePreference) {
        val hasGoogleNextSyncToken = schedulePreference.googleNextSyncToken.orEmpty()
        val googleCalendarWebhookExpiration = schedulePreference.googleCalendarWebhookExpiration.toString()
        setAttribute("schedule_preference_id", schedulePreference.id)
        setAttribute("staff_id", schedulePreference.staffId)
        setAttribute("has_google_next_sync_token", hasGoogleNextSyncToken.isNotNullOrBlank())
        setAttribute("google_calendar_webhook_expiration", googleCalendarWebhookExpiration)
        setAttribute("is_doing_full_sync", schedulePreference.isDoingFullSync)
    }

    private fun Span.setGoogleCalendarEventInfo(
        staffId: UUID,
        avoidPublishObsoleteEvents: Boolean,
        recurrenceRule: RecurrenceRule?,
        isObsoleteRecurrence: Boolean,
        isObsoleteEvent: Boolean,
        persons: List<Person>,
        attendeesEmails: List<String>,
        isCreatingAPastEvent: Boolean,
        eventFromRecurrenceVeryFarInFuture: Boolean
    ) {
        setAttribute("staff_id", staffId)
        setAttribute("avoid_publish_obsolete_events", avoidPublishObsoleteEvents)
        setAttribute("recurrence_rule", recurrenceRule)
        setAttribute("is_obsolete_recurrence", isObsoleteRecurrence)
        setAttribute("is_obsolete_event", isObsoleteEvent)
        setAttribute("persons_found", persons.size)
        setAttribute("members_attendees", attendeesEmails.size)
        setAttribute("is_creating_a_past_event", isCreatingAPastEvent)
        setAttribute("event_from_recurrence_very_far_in_future", eventFromRecurrenceVeryFarInFuture)
    }

}
