package br.com.alice.api.backoffice.controllers.medicalSpecialty

import br.com.alice.api.backoffice.mappers.medicalSpecialty.MedicalSpecialtyInputMapper
import br.com.alice.api.backoffice.mappers.medicalSpecialty.MedicalSpecialtyOutputMapper
import br.com.alice.api.backoffice.transfers.medicalSpecialty.MedicalSpecialtyFullResponse
import br.com.alice.api.backoffice.transfers.medicalSpecialty.MedicalSpecialtyRequest
import br.com.alice.common.Response
import br.com.alice.common.coFoldResponse
import br.com.alice.common.controllers.Controller
import br.com.alice.common.core.exceptions.InvalidArgumentException
import br.com.alice.common.core.extensions.toUUID
import br.com.alice.common.extensions.then
import br.com.alice.common.extensions.thenError
import br.com.alice.common.foldResponse
import br.com.alice.common.logging.logger
import br.com.alice.common.mappers.CommonInputMapper
import br.com.alice.common.toResponse
import br.com.alice.data.layer.models.MedicalSpecialty
import br.com.alice.data.layer.models.MedicalSpecialtyType
import br.com.alice.data.layer.models.MedicalSpecialtyType.SPECIALTY
import br.com.alice.data.layer.models.MedicalSpecialtyType.SUBSPECIALTY
import br.com.alice.provider.client.Filter
import br.com.alice.provider.client.MedicalSpecialtyService
import br.com.alice.staff.client.HealthProfessionalService
import com.github.kittinunf.result.failure
import com.github.kittinunf.result.fanout
import com.github.kittinunf.result.flatMap
import com.github.kittinunf.result.map
import io.ktor.http.HttpStatusCode
import io.ktor.http.Parameters
import kotlinx.coroutines.async
import kotlinx.coroutines.coroutineScope

class MedicalSpecialtyController(
    private val medicalSpecialtyService: MedicalSpecialtyService,
    private val healthProfessionalService: HealthProfessionalService,
) : Controller() {

    suspend fun index(queryParams: Parameters): Response = coroutineScope {
        val range = CommonInputMapper.getPaginationParams(queryParams)
        val name = CommonInputMapper.getFilterParams<String>(queryParams, "q")
        val isAdvancedAccess = CommonInputMapper.getFilterParams<Boolean>(queryParams, "isAdvancedAccess")
        val active = CommonInputMapper.getFilterParams<Boolean>(queryParams, "active")

        val (items, count) = when {
            name != null || isAdvancedAccess != null || active != null -> {
                val filter = Filter(name = name, isAdvancedAccess = isAdvancedAccess, active = active)
                val countDeferred = async { medicalSpecialtyService.countBy(filter).get() }
                medicalSpecialtyService.findBy(filter, range).map { items ->
                    Pair(
                        items.filterNot { it.isOrphanSubSpecialty() },
                        countDeferred.await()
                    )
                }
            }

            else -> {
                val countDeferred = async { medicalSpecialtyService.count().get() }
                medicalSpecialtyService.getByRange(range, MedicalSpecialtyType.values().toList()).map { items ->
                    Pair(
                        items.filterNot { it.isOrphanSubSpecialty() },
                        countDeferred.await()
                    )
                }
            }
        }.get()

        Response(
            status = HttpStatusCode.OK,
            message = MedicalSpecialtyOutputMapper.toPaginatedResponse(items, count, queryParams),
        )
    }

    suspend fun createSpecialty(request: MedicalSpecialtyRequest): Response {
        val input = MedicalSpecialtyInputMapper.toCreate(request)
        val specialty = medicalSpecialtyService.add(input).get()
        logger.info("Added MedicalSpecialty", "current_staff_id" to currentUserIdKey(), "model" to specialty)

        if (specialty.type == SPECIALTY && specialty.generateGeneralistSubSpecialty) {
            medicalSpecialtyService.add(
                MedicalSpecialty(
                    name = "Generalista",
                    type = SUBSPECIALTY,
                    parentSpecialtyId = specialty.id,
                    urlSlug = ""
                )
            )
                .then {
                    logger.info("Added MedicalSpecialty", "current_staff_id" to currentUserIdKey(), "model" to it)
                }.thenError {
                    logger.error("Error an create subSpecialty", "error" to it)
                }
        }

        return specialty.toResponse()
    }

    suspend fun getById(id: String): Response = coroutineScope {
        val specialty = medicalSpecialtyService.getById(id.toUUID())
        val subSpecialties = medicalSpecialtyService.getByParentId(id.toUUID())

        Response(
            status = HttpStatusCode.OK,
            message = MedicalSpecialtyOutputMapper.toResponse(specialty.get(), subSpecialties.get())
        )
    }


    suspend fun update(
        id: String,
        request: MedicalSpecialtyFullResponse
    ): Response {
        return medicalSpecialtyService.getById(id.toUUID()).fanout {
            healthProfessionalService.findActivesByInternalAndExternalSpecialties(
                listOf(id.toUUID())
            )
        }.flatMap {
            val model = it.first
            val hasSpecialists = it.second.isNotEmpty()
            val deactivating = model.active && !request.active

            if (deactivating && hasSpecialists) {
                InvalidArgumentException(
                    code = "invalid_specialty_state",
                    message = "specialty with specialists associated"
                ).failure()
            } else {
                val inputOfSpecialty = MedicalSpecialtyInputMapper.toUpdateSpecialty(model, request)

                medicalSpecialtyService.update(
                    inputOfSpecialty
                ).then { updated ->
                    logger.info(
                        "Updated ${updated::class.simpleName}", "current_staff_id" to currentUserIdKey(),
                        "request" to request, "model" to updated
                    )

                    // Update or create sub-specialties
                    request.subSpecialties?.forEach { subSpecialty ->
                        if (subSpecialty.id == null) {
                            // Create new sub-specialty
                            val newSubSpecialty = MedicalSpecialty(
                                name = subSpecialty.name,
                                active = subSpecialty.active,
                                urlSlug = subSpecialty.urlSlug,
                                parentSpecialtyId = updated.id,
                                type = SUBSPECIALTY,
                                isAdvancedAccess = subSpecialty.isAdvancedAccess
                            )
                            medicalSpecialtyService.add(newSubSpecialty).then { newSub ->
                                logger.info(
                                    "Created new sub-specialty",
                                    "current_staff_id" to currentUserIdKey(),
                                    "request" to subSpecialty,
                                    "model" to newSub
                                )
                            }
                        } else {
                            // Update existing sub-specialty
                            medicalSpecialtyService.getById(subSpecialty.id).flatMap { existingSubSpecialty ->
                                val updatedSubSpecialty = existingSubSpecialty.copy(
                                    name = subSpecialty.name,
                                    active = subSpecialty.active,
                                    urlSlug = subSpecialty.urlSlug,
                                    parentSpecialtyId = updated.id,
                                    isAdvancedAccess = subSpecialty.isAdvancedAccess
                                )
                                medicalSpecialtyService.update(updatedSubSpecialty).then { updatedSub ->
                                    logger.info(
                                        "Updated sub-specialty",
                                        "current_staff_id" to currentUserIdKey(),
                                        "request" to subSpecialty,
                                        "model" to updatedSub
                                    )
                                }
                            }
                        }
                    }

                    val updatedSubSpecialties = medicalSpecialtyService.getByParentId(updated.id).get()

                    return Response(
                        status = HttpStatusCode.OK,
                        message = MedicalSpecialtyOutputMapper.toResponse(updated, updatedSubSpecialties)
                    )
                }
            }
        }.coFoldResponse(
            { it }
        )
    }

    suspend fun getHealthSpecialistResourceBundleMedicalSpecialties(): Response =
        medicalSpecialtyService.getActivesByType(type = SPECIALTY, excludeInternal = true)
            .map { MedicalSpecialtyOutputMapper.toMedicalAndTherapyResponse(it) }
            .foldResponse()

}

