package br.com.alice.fleuryintegrationservice.services

import br.com.alice.common.core.PersonId
import br.com.alice.common.core.exceptions.NotFoundException
import br.com.alice.common.extensions.coFoldDuplicated
import br.com.alice.common.extensions.coResultOf
import br.com.alice.common.extensions.flatMapPair
import br.com.alice.common.extensions.then
import br.com.alice.common.extensions.thenError
import br.com.alice.common.kafka.interfaces.KafkaProducerService
import br.com.alice.common.logging.logger
import br.com.alice.common.service.data.dsl.and
import br.com.alice.data.layer.models.FleuryTestResult
import br.com.alice.data.layer.services.FleuryTestResultDataService
import br.com.alice.fleuryintegrationclient.client.FleuryTestResultService
import br.com.alice.fleuryintegrationclient.events.FleuryTestResultUpsertedEvent
import br.com.alice.fleuryintegrationservice.clients.FleuryClient
import com.github.kittinunf.result.Result
import com.github.kittinunf.result.flatMap
import com.github.kittinunf.result.mapError
import java.util.UUID

class FleuryTestResultServiceImpl(
    private val fleuryTestResultDataService: FleuryTestResultDataService,
    private val fleuryAuthService: FleuryAuthService,
    private val fleuryClient: FleuryClient,
    private val kafkaProducer: KafkaProducerService,
) : FleuryTestResultService {

    override suspend fun findByPersonId(personId: PersonId): Result<List<FleuryTestResult>, Throwable> =
        fleuryTestResultDataService.find { where { this.personId.eq(personId) } }

    override suspend fun getImageResultLink(id: UUID): Result<String, Throwable> =
        fleuryTestResultDataService.get(id)
            .flatMapPair { coResultOf { fleuryAuthService.authenticate() } }
            .flatMap { fleuryClient.getImageLink(it.first, it.second.idFicha, it.second.idItem) }
            .mapError { NotFoundException(message = "Problem retrieving imageLink", cause = it) }

    override suspend fun getImageResultLinkByIdFichaAndItem(
        idFicha: String,
        idItem: String,
    ): Result<String, Throwable> =
        coResultOf<String, Throwable> { fleuryAuthService.authenticate() }
            .flatMap { fleuryClient.getImageLink(it, idFicha, idItem) }
            .mapError { NotFoundException(message = "Problem retrieving imageLink", cause = it) }

    override suspend fun get(resultId: UUID): Result<FleuryTestResult, Throwable> =
        fleuryTestResultDataService.get(resultId)

    override suspend fun getByIdFicha(idFicha: String): Result<List<FleuryTestResult>, Throwable> =
        fleuryTestResultDataService.find { where { ficha.eq(idFicha) } }

    override suspend fun getByIdFichaAndItem(idFicha: String, idItem: String): Result<FleuryTestResult, Throwable> =
        fleuryTestResultDataService.findOne {
            where { ficha.eq(idFicha).and(item.eq(idItem)) }
        }

    override suspend fun add(testResult: FleuryTestResult): Result<FleuryTestResult, Throwable> =
        fleuryTestResultDataService.add(testResult).then {
            kafkaProducer.produce(FleuryTestResultUpsertedEvent(it), it.idFicha)
            logger.info(
                "Test Result added",
                "test_result_id" to it.id,
                "id_ficha" to it.idFicha,
                "id_item" to it.idItem,
                "person_id" to it.personId
            )
        }.coFoldDuplicated {
            logger.info(
                "Test Result already exists",
                "test_result_id" to testResult.id,
                "id_ficha" to testResult.idFicha,
                "id_item" to testResult.idItem,
                "person_id" to testResult.personId
            )
            getByIdFichaAndItem(idFicha = testResult.idFicha, idItem = testResult.idItem)
        }.thenError {
            logger.error(
                "Exception while inserting new test result",
                "test_result_id" to testResult.id,
                "id_ficha" to testResult.idFicha,
                "id_item" to testResult.idItem,
                "person_id" to testResult.personId,
                it
            )
        }
}
