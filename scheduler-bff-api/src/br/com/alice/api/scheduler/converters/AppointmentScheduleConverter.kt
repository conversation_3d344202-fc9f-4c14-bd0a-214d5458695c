package br.com.alice.api.scheduler.converters

import br.com.alice.api.scheduler.controllers.model.AppointmentScheduleWithStaffResponse
import br.com.alice.common.Converter
import br.com.alice.common.core.extensions.toSaoPauloTimeZone
import br.com.alice.common.map
import br.com.alice.data.layer.models.AppointmentSchedule

object AppointmentScheduleWithStaffResponseConverter :
    Converter<AppointmentSchedule, AppointmentScheduleWithStaffResponse>(
        AppointmentSchedule::class,
        AppointmentScheduleWithStaffResponse::class
    ) {

    fun convert(
        source: AppointmentSchedule,
        staffEmail: String
    ): AppointmentScheduleWithStaffResponse {
        return super.convert(
            source,
            map(AppointmentScheduleWithStaffResponse::staffEmail) from staffEmail,
            map(AppointmentScheduleWithStaffResponse::startTime) from source.startTime.toSaoPauloTimeZone(),
            map(AppointmentScheduleWithStaffResponse::endTime) from source.endTime?.toSaoPauloTimeZone(),
            map(AppointmentScheduleWithStaffResponse::createdAt) from source.createdAt.toSaoPauloTimeZone(),
        )
    }
}
