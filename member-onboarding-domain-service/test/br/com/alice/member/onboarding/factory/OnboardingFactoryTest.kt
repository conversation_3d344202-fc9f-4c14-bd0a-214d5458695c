package br.com.alice.member.onboarding.factory

import br.com.alice.common.core.extensions.toBrazilianDateFormat
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.MemberOnboarding.MemberOnboardingStepStatus.BLOCKED
import br.com.alice.data.layer.models.MemberOnboarding.MemberOnboardingStepStatus.COMPLETED
import br.com.alice.data.layer.models.MemberOnboarding.MemberOnboardingStepStatus.PENDING
import br.com.alice.data.layer.models.MemberOnboarding.MemberOnboardingStepType.ALICE_INFO
import br.com.alice.data.layer.models.MemberOnboarding.MemberOnboardingStepType.CONCLUSION
import br.com.alice.data.layer.models.MemberOnboarding.MemberOnboardingStepType.COVER
import br.com.alice.data.layer.models.MemberOnboarding.MemberOnboardingStepType.COVER_MFC
import br.com.alice.data.layer.models.MemberOnboarding.MemberOnboardingStepType.SCORE_MAGENTA
import br.com.alice.data.layer.models.MemberOnboarding.MemberOnboardingStepType.VIDEO
import br.com.alice.data.layer.models.MemberOnboardingFlowType
import br.com.alice.data.layer.models.MemberOnboardingFlowType.ADULT
import br.com.alice.data.layer.models.MemberOnboardingFlowType.CHILD
import br.com.alice.data.layer.models.Step
import br.com.alice.member.onboarding.model.BottomSheetContent
import br.com.alice.member.onboarding.model.OnboardingTemplate
import br.com.alice.member.onboarding.model.OnboardingVersion
import br.com.alice.member.onboarding.model.OnboardingVersion.V1
import br.com.alice.member.onboarding.model.OnboardingVersion.V2
import br.com.alice.member.onboarding.model.OnboardingVersion.V3
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.TestInstance
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.Arguments
import org.junit.jupiter.params.provider.MethodSource
import java.time.LocalDateTime
import kotlin.test.Test

@TestInstance(TestInstance.Lifecycle.PER_CLASS)
internal class OnboardingFactoryTest {

    private val defaultChildSteps = listOf(
        Step(
            templateType = COVER,
            status = PENDING
        ),
        Step(
            templateType = CONCLUSION,
            status = BLOCKED
        )
    )

    private val defaultAdultSteps = listOf(
        Step(
            templateType = VIDEO,
            status = PENDING
        ),
        Step(
            templateType = COVER,
            status = PENDING
        ),
        Step(
            templateType = SCORE_MAGENTA,
            status = PENDING
        ),
        Step(
            templateType = ALICE_INFO,
            status = PENDING
        ),
        Step(
            templateType = CONCLUSION,
            status = BLOCKED
        )
    )

    private val defaultV2AdultSteps = listOf(
        Step(
            templateType = ALICE_INFO,
            status = PENDING
        ),
        Step(
            templateType = SCORE_MAGENTA,
            status = PENDING
        ),
        Step(
            templateType = COVER,
            status = PENDING
        ),
        Step(
            templateType = CONCLUSION,
            status = BLOCKED
        )
    )

    private val defaultV3ChildSteps = listOf(
        Step(
            templateType = COVER_MFC,
            status = PENDING
        ),
        Step(
            templateType = COVER,
            status = PENDING
        ),
        Step(
            templateType = CONCLUSION,
            status = BLOCKED
        )
    )

    private val defaultV3AdultSteps = listOf(
        Step(
            templateType = COVER_MFC,
            status = PENDING
        ),
        Step(
            templateType = VIDEO,
            status = PENDING
        ),
        Step(
            templateType = COVER,
            status = PENDING
        ),
        Step(
            templateType = SCORE_MAGENTA,
            status = PENDING
        ),
        Step(
            templateType = ALICE_INFO,
            status = PENDING
        ),
        Step(
            templateType = CONCLUSION,
            status = BLOCKED
        )
    )

    private val memberOnboarding = TestModelFactory.buildMemberOnboarding(
        steps = defaultChildSteps,
        flowType = CHILD
    )

    private val healthDeclarationTemplate = OnboardingTemplate(
        type = COVER,
        title = "Preencha sua declaração de saúde",
        path = "/health_declaration_cover",
        timeToComplete = "8 min",
        bottomSheet = BottomSheetContent(
            title = "Você já preencheu essa etapa",
            description = "As etapas de Declaração de Saúde e Score Magenta só podem ser preenchidas uma vez."
        )
    )

    private val scoreMagentaTemplate = OnboardingTemplate(
        type = SCORE_MAGENTA,
        title = "Descubra como está sua saúde com o Score Magenta",
        timeToComplete = "8 min",
        bottomSheet = BottomSheetContent(
            title = "Você já preencheu essa etapa",
            description = "As etapas de Declaração de Saúde e Score Magenta só podem ser preenchidas uma vez."
        ),
        questionnaireType = "QUEST_IMMERSION_PROFILE"
    )

    private val memberActivationDate = LocalDateTime.now()

    @Test
    fun `#getStepTypes should get v1 adult onboarding step types`() {
        val expected = listOf(VIDEO, COVER, SCORE_MAGENTA, ALICE_INFO, CONCLUSION)

        val template = OnboardingFactory.getStepTypes(ADULT, V1)

        assertThat(template).isEqualTo(expected)
    }

    @Test
    fun `#getStepTypes should get v1 child onboarding step types`() {
        val expected = listOf(COVER, CONCLUSION)

        val template = OnboardingFactory.getStepTypes(CHILD, V1)

        assertThat(template).isEqualTo(expected)
    }

    @Test
    fun `#getStepTypes should get v2 adult onboarding step types`() {
        val expected = listOf(ALICE_INFO, SCORE_MAGENTA, COVER, CONCLUSION)

        val template = OnboardingFactory.getStepTypes(ADULT, V2)

        assertThat(template).isEqualTo(expected)
    }

    @Test
    fun `#getStepTypes should get v2 child onboarding step types`() {
        val expected = listOf(COVER, CONCLUSION)

        val template = OnboardingFactory.getStepTypes(CHILD, V2)

        assertThat(template).isEqualTo(expected)
    }

    @Test
    fun `#get should return adult v1 health declaration template by type and version`() {
        val expected = healthDeclarationTemplate

        val template = OnboardingFactory[COVER, ADULT, V1, memberActivationDate]

        assertThat(template).isEqualTo(expected)
    }

    @Test
    fun `#get should return adult v2 health declaration template by type and version`() {
        val expected = healthDeclarationTemplate.copy(timeToComplete = "4 min")

        val template = OnboardingFactory[COVER, ADULT, V2, memberActivationDate]

        assertThat(template).isEqualTo(expected)
    }

    @Test
    fun `#get should return child v1 health declaration template by type and version`() {
        val expected = healthDeclarationTemplate.copy(title = "Preencha a Declaração de Saúde")

        val template = OnboardingFactory[COVER, CHILD, V1, memberActivationDate]

        assertThat(template).isEqualTo(expected)
    }

    @Test
    fun `#get should return child v2 health declaration template by type and version`() {
        val expected = healthDeclarationTemplate.copy(title = "Preencha a Declaração de Saúde", timeToComplete = "4 min")

        val template = OnboardingFactory[COVER, CHILD, V2, memberActivationDate]

        assertThat(template).isEqualTo(expected)
    }

    @Test
    fun `#get should return adult v1 score magenta template by type and version`() {
        val expected = scoreMagentaTemplate

        val template = OnboardingFactory[SCORE_MAGENTA, ADULT, V1, memberActivationDate]

        assertThat(template).isEqualTo(expected)
    }

    @Test
    fun `#get should return adult v2 score magenta template by type and version`() {
        val expected = scoreMagentaTemplate.copy(timeToComplete = "4 min")

        val template = OnboardingFactory[SCORE_MAGENTA, ADULT, V2, memberActivationDate]

        assertThat(template).isEqualTo(expected)
    }

    @Test
    fun `#getStepTypes should get v3 adult onboarding step types`() {
        val expected = listOf(COVER_MFC, VIDEO, COVER, SCORE_MAGENTA, ALICE_INFO, CONCLUSION)

        val template = OnboardingFactory.getStepTypes(ADULT, V3)

        assertThat(template).isEqualTo(expected)
    }

    @Test
    fun `#getStepTypes should get v3 child onboarding step types`() {
        val expected = listOf(COVER_MFC, COVER, CONCLUSION)

        val template = OnboardingFactory.getStepTypes(CHILD, V3)

        assertThat(template).isEqualTo(expected)
    }

    @Test
    fun `#get should return adult v3 health declaration template by type and version`() {
        val expected = healthDeclarationTemplate.copy(
            bottomSheet = BottomSheetContent(
                title = "Você já preencheu essa etapa",
                description = "As etapas de escolha do seu médico(a), Declaração de Saúde e Score Magenta só podem ser preenchidas uma vez."
            )
        )

        val template = OnboardingFactory[COVER, ADULT, V3, memberActivationDate]

        assertThat(template).isEqualTo(expected)
    }

    @Test
    fun `#get should return child v3 health declaration template by type and version`() {
        val expected = healthDeclarationTemplate.copy(
            title = "Preencha a Declaração de Saúde",
            bottomSheet = BottomSheetContent(
                title = "Você já preencheu essa etapa",
                description = "As etapas de escolha do médico(a) do seu dependente e Declaração de Saúde só podem ser preenchidas uma vez."
            )
        )

        val template = OnboardingFactory[COVER, CHILD, V3, memberActivationDate]

        assertThat(template).isEqualTo(expected)
    }

    @Test
    fun `#get should return adult v3 score magenta template by type and version`() {
        val expected = scoreMagentaTemplate.copy(
            bottomSheet = BottomSheetContent(
                title = "Você já preencheu essa etapa",
                description = "As etapas de escolha do seu médico(a), Declaração de Saúde e Score Magenta só podem ser preenchidas uma vez."
            )
        )

        val template = OnboardingFactory[SCORE_MAGENTA, ADULT, V3, memberActivationDate]

        assertThat(template).isEqualTo(expected)
    }

    @Test
    fun `#get should return adult v3 alice info template by type and version`() {
        val expected = OnboardingTemplate(
            type = ALICE_INFO,
            title = "Saiba como a Alice funciona na prática",
            timeToComplete = "1 min",
            data = OnboardingTemplate.Data(2)
        )

        val template = OnboardingFactory[ALICE_INFO, ADULT, V3, memberActivationDate]

        assertThat(template).isEqualTo(expected)
    }

    @Test
    fun `#get should return adult v3 cover mfc template by type and version`() {
        val expected = OnboardingTemplate(
            type = COVER_MFC,
            title = "Escolha o Médico que vai cuidar de você na Alice",
            path = "/mfc_cover",
            timeToComplete = "Complete até ${memberActivationDate.plusDays(60L).toBrazilianDateFormat()}",
            bottomSheet = BottomSheetContent(
                title = "Você já preencheu essa etapa",
                description = "As etapas de escolha do seu médico(a), Declaração de Saúde e Score Magenta só podem ser preenchidas uma vez."
            )
        )

        val template = OnboardingFactory[COVER_MFC, ADULT, V3, memberActivationDate]

        assertThat(template).isEqualTo(expected)
    }

    @Test
    fun `#get should return child v3 cover mfc template by type and version`() {
        val expected = OnboardingTemplate(
            type = COVER_MFC,
            title = "Escolha o Médico que vai cuidar de você na Alice",
            path = "/mfc_cover",
            timeToComplete = "Complete até ${memberActivationDate.plusDays(60L).toBrazilianDateFormat()}",
            bottomSheet = BottomSheetContent(
                title = "Você já preencheu essa etapa",
                description = "As etapas de escolha do médico(a) do seu dependente e Declaração de Saúde só podem ser preenchidas uma vez."
            )
        )

        val template = OnboardingFactory[COVER_MFC, CHILD, V3, memberActivationDate]

        assertThat(template).isEqualTo(expected)
    }

    private fun getTitleAndDescriptionScenarios() = listOf(
        Arguments.of(
            V1,
            CHILD,
            defaultChildSteps,
            0,
            "Complete esta etapa" to "Conclua a etapa abaixo para desbloquear o primeiro Plano de Ação do seu dependente."
        ),
        Arguments.of(
            V1,
            CHILD,
            defaultChildSteps,
            1,
            "Seu Plano de Ação" to "Pronto! Com base nas suas respostas, criamos o primeiro Plano de Ação do seu dependente."
        ),
        Arguments.of(
            V1,
            ADULT,
            defaultAdultSteps,
            0,
            "Complete as etapas" to "Conclua as etapas para desbloquear seu primeiro Plano de Ação. As tarefas serão" +
                    " personalizadas de acordo com suas respostas."
        ),
        Arguments.of(
            V1,
            ADULT,
            defaultAdultSteps,
            3,
            "Falta muito pouco" to "Conclua as etapas para desbloquear seu primeiro Plano de Ação. " +
                    "As tarefas serão personalizadas de acordo com suas respostas."
        ),
        Arguments.of(
            V1,
            ADULT,
            defaultAdultSteps,
            4,
            "Seu Plano de Ação" to "Pronto! Com base nas suas respostas, criamos seu primeiro " +
                    "Plano de Ação. Prepare-se para uma vida mais saudável."
        ),
        Arguments.of(
            V2,
            CHILD,
            defaultChildSteps,
            0,
            "Sua jornada começa aqui" to "Cuidar da saúde é mais fácil quando você sabe por onde começar. Desbloqueie" +
                    " o primeiro Plano de Ação do seu dependente."
        ),
        Arguments.of(
            V2,
            CHILD,
            defaultChildSteps,
            1,
            "Seu Plano de Ação chegou" to "Prepare-se para uma vida mais saudável. Com base nas suas respostas, criamos" +
                    " o primeiro Plano de Ação do seu dependente."
        ),
        Arguments.of(
            V2,
            ADULT,
            defaultV2AdultSteps,
            0,
            "Sua jornada começa aqui" to "Cuidar da saúde é mais fácil quando você sabe por onde começar. " +
                    "Desbloqueie seu primeiro Plano de Ação com tarefas personalizadas."
        ),
        Arguments.of(
            V2,
            ADULT,
            defaultV2AdultSteps,
            1,
            "Raio-X da sua saúde" to "Descubra como está sua saúde através de algumas perguntas que nos ajudam a criar " +
                    "um Plano de Ação para você."
        ),
        Arguments.of(
            V2,
            ADULT,
            defaultV2AdultSteps,
            2,
            "Falta muito pouco" to "Está é a última etapa para desbloquear seu primeiro Plano de Ação. As tarefas serão" +
                    " personalizadas de acordo com suas respostas."
        ),
        Arguments.of(
            V2,
            ADULT,
            defaultV2AdultSteps,
            3,
            "Seu Plano de Ação chegou" to "Prepare-se para uma vida mais saudável. Com base nas suas respostas, criamos" +
                    " seu primeiro Plano de Ação."
        ),
        Arguments.of(
            V3,
            CHILD,
            defaultV3ChildSteps,
            0,
            "Complete as etapas" to "Conclua as etapas para desbloquear o primeiro Plano de Ação do seu dependente."
        ),
        Arguments.of(
            V3,
            CHILD,
            defaultV3ChildSteps,
            1,
            "Falta muito pouco" to "Conclua a etapa abaixo para desbloquear o primeiro Plano de Ação do seu dependente."
        ),
        Arguments.of(
            V3,
            CHILD,
            defaultV3ChildSteps,
            3,
            "Seu Plano de Ação" to "Pronto! Com base nas suas respostas, criamos o primeiro Plano de Ação do seu dependente."
        ),
        Arguments.of(
            V3,
            ADULT,
            defaultV3AdultSteps,
            0,
            "Complete as etapas" to "Conclua as etapas para desbloquear seu primeiro Plano de Ação. As tarefas serão" +
                    " personalizadas de acordo com suas respostas."
        ),
        Arguments.of(
            V3,
            ADULT,
            defaultV3AdultSteps,
            4,
            "Falta muito pouco" to "Conclua as etapas para desbloquear seu primeiro Plano de Ação. " +
                    "As tarefas serão personalizadas de acordo com suas respostas."
        ),
        Arguments.of(
            V3,
            ADULT,
            defaultV3AdultSteps,
            5,
            "Seu Plano de Ação" to "Pronto! Com base nas suas respostas, criamos seu primeiro " +
                    "Plano de Ação. Prepare-se para uma vida mais saudável."
        )
    )

    @ParameterizedTest(name="for {0} and {1} with {3} finished steps, return correct title and description")
    @MethodSource("getTitleAndDescriptionScenarios")
    fun `#getTitleAndDescription should return title and description according to version, flow type and steps finished`(
        version: OnboardingVersion,
        flowType: MemberOnboardingFlowType,
        steps: List<Step>,
        qtdStepsFinished: Int,
        expected: Pair<String, String>
    ) {
        val memberOnboarding = memberOnboarding.copy(
            flowType = flowType,
            steps = buildWithFinishedSteps(steps, qtdStepsFinished)
        )

        val template = OnboardingFactory.getTitleAndDescription(memberOnboarding, version)

        assertThat(template).isEqualTo(expected)
    }

    private fun buildWithFinishedSteps(steps: List<Step>, qtd: Int) =
        steps.mapIndexed { index, step ->
            if (index < qtd) step.copy(status = COMPLETED)
            else step
        }
}
