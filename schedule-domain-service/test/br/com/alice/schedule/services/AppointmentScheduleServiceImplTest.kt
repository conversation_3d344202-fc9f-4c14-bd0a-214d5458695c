package br.com.alice.schedule.services

import br.com.alice.authentication.UserType
import br.com.alice.common.RangeUUID
import br.com.alice.common.core.PersonId
import br.com.alice.common.core.exceptions.DatabaseException
import br.com.alice.common.core.exceptions.InvalidArgumentException
import br.com.alice.common.core.exceptions.NotFoundException
import br.com.alice.common.core.extensions.atEndOfTheDay
import br.com.alice.common.core.extensions.toSaoPauloTimeZone
import br.com.alice.common.core.extensions.toSha256
import br.com.alice.common.data.dsl.matchers.ResultAssert.Companion.assertThat
import br.com.alice.common.data.dsl.matchers.queryEq
import br.com.alice.common.featureflag.withFeatureFlag
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.common.helpers.mockLocalDateTime
import br.com.alice.common.helpers.mockRangeUuidAndDateTime
import br.com.alice.common.kafka.interfaces.KafkaProducerService
import br.com.alice.common.service.data.client.CountByValues
import br.com.alice.common.service.data.dsl.SortOrder
import br.com.alice.common.service.data.dsl.and
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.AppointmentSchedule.ReferenceLinkModel
import br.com.alice.data.layer.models.AppointmentSchedule.ReferencedLink
import br.com.alice.data.layer.models.AppointmentScheduleCancelledByType
import br.com.alice.data.layer.models.AppointmentScheduleModel
import br.com.alice.data.layer.models.AppointmentScheduleStatus
import br.com.alice.data.layer.models.AppointmentScheduleType
import br.com.alice.data.layer.models.EventTypeProviderUnit
import br.com.alice.data.layer.models.FeatureNamespace
import br.com.alice.data.layer.models.HealthPlanTaskStatus
import br.com.alice.data.layer.services.AppointmentScheduleModelDataService
import br.com.alice.healthplan.models.HealthPlanTasksTransport
import br.com.alice.healthplan.models.ReferralTransport
import br.com.alice.person.client.PersonService
import br.com.alice.schedule.client.AppointmentScheduleEventTypeService
import br.com.alice.schedule.client.AppointmentScheduleFilter
import br.com.alice.schedule.client.CanceledOrTimedOutScheduleException
import br.com.alice.schedule.client.EventTypeProviderUnitService
import br.com.alice.schedule.client.StaffAvailabilityService
import br.com.alice.schedule.converters.toModel
import br.com.alice.schedule.converters.toTransport
import br.com.alice.schedule.exceptions.AppointmentScheduleCollisionDetectedException
import br.com.alice.schedule.model.AppointmentScheduleCreationValidation
import br.com.alice.schedule.model.AppointmentScheduleCreationValidationResult
import br.com.alice.schedule.model.AppointmentScheduleEvent
import br.com.alice.schedule.model.AppointmentScheduleWithStaff
import br.com.alice.schedule.model.CalendlyV2WebhookCancellation
import br.com.alice.schedule.model.SlotForSpecificDuration
import br.com.alice.schedule.model.events.AppointmentScheduleCancelledEvent
import br.com.alice.schedule.model.events.AppointmentScheduleCollisionDetectedEvent
import br.com.alice.schedule.model.events.AppointmentScheduleCompletedEvent
import br.com.alice.schedule.model.events.AppointmentScheduleCreatedEvent
import br.com.alice.schedule.model.events.AppointmentScheduleNoShowEvent
import br.com.alice.schedule.model.events.AppointmentScheduleUpdatedEvent
import br.com.alice.schedule.model.events.InternalAppointmentScheduleCreatedEvent
import br.com.alice.schedule.model.events.Professional
import br.com.alice.schedule.services.internal.HealthResourcesStore
import com.github.kittinunf.result.failure
import com.github.kittinunf.result.success
import io.mockk.called
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.confirmVerified
import io.mockk.mockk
import kotlinx.coroutines.runBlocking
import java.time.LocalDate
import java.time.LocalDateTime
import java.util.UUID
import kotlin.test.AfterTest
import kotlin.test.Test

class AppointmentScheduleServiceImplTest {

    private val appointmentScheduleModelDataService: AppointmentScheduleModelDataService = mockk()
    private val kafkaProducerService: KafkaProducerService = mockk()
    private val personService: PersonService = mockk()
    private val healthResourcesStore: HealthResourcesStore = mockk()
    private val schedulePreferenceService: SchedulePreferenceServiceImpl = mockk()
    private val appointmentScheduleEventTypeService: AppointmentScheduleEventTypeService = mockk()
    private val staffAvailabilityService: StaffAvailabilityService = mockk()
    private val eventTypeProviderUnitService: EventTypeProviderUnitService = mockk()

    private val appointmentScheduleService = AppointmentScheduleServiceImpl(
        appointmentScheduleModelDataService,
        kafkaProducerService,
        personService,
        healthResourcesStore,
        schedulePreferenceService,
        appointmentScheduleEventTypeService,
        staffAvailabilityService,
        eventTypeProviderUnitService
    )

    private val person = TestModelFactory.buildPerson(firstName = "Marcos")
    private val healthcareTeam = TestModelFactory.buildHealthcareTeam()
    private val staff = TestModelFactory.buildStaff()
    private val taskUUID = RangeUUID.generate()
    private val healthPlanTasks = HealthPlanTasksTransport(
        referral = listOf(
            ReferralTransport(
                id = taskUUID,
                title = "",
                personId = person.id.id,
                status = HealthPlanTaskStatus.ACTIVE,
                description = "",
                dueDate = "",
                diagnosticHypothesis = null,
                favorite = false,
            )
        )
    )

    private val schedulePreference = TestModelFactory.buildSchedulePreference(
        weeklyHours = 30,
        intervalBetweenEvents = 15,
        staffId = staff.id,
        googleRefreshToken = "refresh_token",
        zoomLink = "https://alice-br.zoom.us/j/123456#success"
    )

    private val providerUnitId = RangeUUID.generate()
    private val address = TestModelFactory.buildStructuredAddress()
    private val providerUnit = TestModelFactory.buildProviderUnit(id = providerUnitId).copy(address = address)
    private val startTime = LocalDateTime.of(2020, 1, 1, 10, 10)

    private val appointmentScheduleToAddId = RangeUUID.generate()
    private val baseDate = LocalDateTime.now()
    private val appointmentScheduled = TestModelFactory.buildAppointmentSchedule(
        personId = person.id,
        status = AppointmentScheduleStatus.SCHEDULED,
        location = schedulePreference.zoomLink,
        createdAt = baseDate,
        updatedAt = baseDate
    )
    private val appointmentScheduleEvent = buildAppointmentScheduleRequestEvent("LAB", taskUUID.toString().toSha256())
    private val appointmentScheduleToAdd = appointmentScheduled.copy(
        id = appointmentScheduleToAddId,
        location = appointmentScheduleEvent.location,
        healthcareTeamId = healthcareTeam.id,
        endTime = appointmentScheduleEvent.endTime,
        type = AppointmentScheduleType.HEALTHCARE_TEAM,
        staffId = staff.id,
        healthPlanTaskId = taskUUID,
        currentUserType = UserType.MEMBER,
        scheduledByInternalScheduler = true
    )

    private val appointmentScheduledToUpdate = appointmentScheduled.copy(
        location = appointmentScheduleEvent.location,
        healthcareTeamId = healthcareTeam.id,
        endTime = appointmentScheduleEvent.endTime,
        type = AppointmentScheduleType.HEALTHCARE_TEAM,
        staffId = staff.id,
        healthPlanTaskId = taskUUID,
        currentUserType = UserType.MEMBER,
        scheduledByInternalScheduler = true,
    )

    private val appointmentScheduleEventType = TestModelFactory.buildAppointmentScheduleEventType(
        category = AppointmentScheduleType.HOME_TEST,
    )

    private val eventTypeProviderUnit = EventTypeProviderUnit(
        providerUnitId = providerUnit.id,
        appointmentScheduleEventTypeId = appointmentScheduleEventType.id
    )

    private val eventTypeProviderUnitDigital = EventTypeProviderUnit(
        providerUnitId = null,
        appointmentScheduleEventTypeId = appointmentScheduleEventType.id
    )

    @AfterTest
    fun confirmMocks() = confirmVerified(
        appointmentScheduleModelDataService,
        kafkaProducerService,
        personService,
        healthResourcesStore,
        schedulePreferenceService,
        appointmentScheduleEventTypeService,
        staffAvailabilityService,
        eventTypeProviderUnitService
    )

    @Test
    fun `#cancel should cancel an appointment correctly`() = runBlocking {
        val appointmentToBeCanceled = appointmentScheduled.cancel()

        coEvery { appointmentScheduleModelDataService.get(appointmentScheduled.id) } returns appointmentScheduled.toModel().success()
        coEvery { appointmentScheduleModelDataService.update(appointmentToBeCanceled.toModel()) } returns appointmentToBeCanceled.toModel().success()
        coEvery { kafkaProducerService.produce(AppointmentScheduleCancelledEvent(appointmentToBeCanceled)) } returns mockk()

        val result = appointmentScheduleService.cancel(appointmentScheduled.id)
        assertThat(result).isSuccessWithData(appointmentToBeCanceled)

        coVerifyOnce { appointmentScheduleModelDataService.get(any()) }
        coVerifyOnce { appointmentScheduleModelDataService.update(any()) }
        coVerifyOnce { kafkaProducerService.produce(any()) }
    }

    @Test
    fun `#cancel should cancel an appointment correctly when appointment schedule have staff_id`() =
        runBlocking {
            val appointmentScheduled = appointmentScheduled.copy(staffId = staff.id)
            val appointmentToBeCanceled = appointmentScheduled.copy(staffId = staff.id).cancel()

            coEvery { appointmentScheduleModelDataService.get(appointmentScheduled.id) } returns appointmentScheduled.toModel().success()
            coEvery { appointmentScheduleModelDataService.update(appointmentToBeCanceled.toModel()) } returns appointmentToBeCanceled.toModel().success()
            coEvery { kafkaProducerService.produce(AppointmentScheduleCancelledEvent(appointmentToBeCanceled)) } returns mockk()

            val result = appointmentScheduleService.cancel(appointmentScheduled.id)
            assertThat(result).isSuccessWithData(appointmentToBeCanceled)

            coVerifyOnce { appointmentScheduleModelDataService.get(any()) }
            coVerifyOnce { appointmentScheduleModelDataService.update(any()) }
            coVerifyOnce { kafkaProducerService.produce(any()) }
        }

    @Test
    fun `#cancel should cancel an appointment correctly when appointment schedule have staff_id, and delete previous canceled event if there is one`() =
        runBlocking {
            val appointmentScheduled = appointmentScheduled.copy(staffId = staff.id)
            val appointmentToBeCanceled = appointmentScheduled.cancel()

            coEvery { appointmentScheduleModelDataService.get(appointmentScheduled.id) } returns appointmentScheduled.toModel().success()
            coEvery { appointmentScheduleModelDataService.update(appointmentToBeCanceled.toModel()) } returns appointmentToBeCanceled.toModel().success()
            coEvery { kafkaProducerService.produce(AppointmentScheduleCancelledEvent(appointmentToBeCanceled)) } returns mockk()

            val result = appointmentScheduleService.cancel(appointmentScheduled.id)
            assertThat(result).isSuccessWithData(appointmentToBeCanceled)

            coVerifyOnce { appointmentScheduleModelDataService.get(any()) }
            coVerifyOnce { appointmentScheduleModelDataService.update(any()) }
            coVerifyOnce { kafkaProducerService.produce(any()) }
        }

    @Test
    fun `#cancel should do nothing if not find appointment schedule`() = runBlocking {
        coEvery { appointmentScheduleModelDataService.get(appointmentScheduled.id) } returns NotFoundException().failure()

        val result = appointmentScheduleService.cancel(appointmentScheduled.id)
        assertThat(result).isFailureOfType(NotFoundException::class)

        coVerifyOnce { appointmentScheduleModelDataService.get(any()) }
    }

    @Test
    fun `#cancel should cancel an appointment with cancellation information`() = runBlocking {
        val appointmentToBeCanceled = appointmentScheduled.copy(
            cancelledByType = AppointmentScheduleCancelledByType.CALENDLY_ADMIN
        ).cancel()

        coEvery { appointmentScheduleModelDataService.get(appointmentScheduled.id) } returns appointmentScheduled.toModel().success()
        coEvery { appointmentScheduleModelDataService.update(appointmentToBeCanceled.toModel()) } returns appointmentToBeCanceled.toModel().success()
        coEvery { kafkaProducerService.produce(AppointmentScheduleCancelledEvent(appointmentToBeCanceled)) } returns mockk()

        val result = appointmentScheduleService.cancel(
            id = appointmentScheduled.id,
            appointmentScheduleCancelledByType = AppointmentScheduleCancelledByType.CALENDLY_ADMIN
        )
        assertThat(result).isSuccessWithData(appointmentToBeCanceled)

        coVerifyOnce { appointmentScheduleModelDataService.get(any()) }
        coVerifyOnce { appointmentScheduleModelDataService.update(any()) }
        coVerifyOnce { kafkaProducerService.produce(any()) }
    }

    @Test
    fun `#cancel should just return the appointment when is it already canceled`() = runBlocking {
        val appointmentAlreadyCanceled = appointmentScheduled.cancel()

        coEvery { appointmentScheduleModelDataService.get(appointmentScheduled.id) } returns appointmentAlreadyCanceled.toModel().success()

        val result = appointmentScheduleService.cancel(appointmentScheduled.id)
        assertThat(result).isSuccessWithData(appointmentAlreadyCanceled)

        coVerifyOnce { appointmentScheduleModelDataService.get(any()) }
    }

    @Test
    fun `#cancelByEvent should not cancel an event when update fails`() = runBlocking {
        val appointmentCanceled = appointmentScheduled.cancel()

        coEvery {
            appointmentScheduleModelDataService.findOne(
                queryEq {
                    where { this.eventId.eq(appointmentCanceled.eventId!!) }
                }
            )
        } returns appointmentScheduled.toModel().success()
        coEvery { appointmentScheduleModelDataService.update(appointmentCanceled.toModel()) } returns Exception().failure()

        val result = appointmentScheduleService.cancelByEvent(appointmentScheduleEvent)
        assertThat(result).isFailureOfType(Exception::class)

        coVerifyOnce { appointmentScheduleModelDataService.findOne(any()) }
        coVerifyOnce { appointmentScheduleModelDataService.update(any()) }
    }

    @Test
    fun `#cancelByEvent should cancel an appointment correctly`() = runBlocking {
        val appointmentScheduleEvent = buildAppointmentScheduleRequestEvent("Rua Rebouças, 3506")
        val appointmentCanceled = TestModelFactory.buildAppointmentSchedule(
            personId = person.id,
            status = AppointmentScheduleStatus.CANCELED
        )

        coEvery {
            appointmentScheduleModelDataService.findOne(
                queryEq {
                    where { this.eventId.eq(appointmentCanceled.eventId!!) }
                }
            )
        } returns appointmentCanceled.toModel().success()
        coEvery { appointmentScheduleModelDataService.update(appointmentCanceled.toModel()) } returns appointmentCanceled.toModel().success()
        coEvery { kafkaProducerService.produce(AppointmentScheduleCancelledEvent(appointmentCanceled)) } returns mockk()

        val result = appointmentScheduleService.cancelByEvent(appointmentScheduleEvent)
        assertThat(result).isSuccessWithData(appointmentCanceled)

        coVerifyOnce { appointmentScheduleModelDataService.findOne(any()) }
        coVerifyOnce { appointmentScheduleModelDataService.update(any()) }
        coVerifyOnce { kafkaProducerService.produce(any()) }
    }

    @Test
    fun `#cancelByEvent should cancel appointment schedule and update previous location`() = runBlocking {
        val appointmentScheduleEvent = buildAppointmentScheduleRequestEvent(
            location = null,
            cancellation = CalendlyV2WebhookCancellation(
                canceledBy = person.fullSocialName,
                reason = "some_reason",
                cancelerType = "some_type"
            )
        )
        val appointmentCanceled =
            TestModelFactory.buildAppointmentSchedule(
                personId = person.id,
                status = AppointmentScheduleStatus.CANCELED,
                cancelledByType = AppointmentScheduleCancelledByType.UNKNOWN
            )

        coEvery {
            appointmentScheduleModelDataService.findOne(
                queryEq {
                    where { this.eventId.eq(appointmentCanceled.eventId!!) }
                }
            )
        } returns appointmentCanceled.toModel().success()
        coEvery { appointmentScheduleModelDataService.update(appointmentCanceled.toModel()) } returns appointmentCanceled.toModel().success()
        coEvery { kafkaProducerService.produce(AppointmentScheduleCancelledEvent(appointmentCanceled)) } returns mockk()

        val result = appointmentScheduleService.cancelByEvent(appointmentScheduleEvent)
        assertThat(result).isSuccessWithData(appointmentCanceled)

        coVerifyOnce { appointmentScheduleModelDataService.findOne(any()) }
        coVerifyOnce { appointmentScheduleModelDataService.update(any()) }
        coVerifyOnce { kafkaProducerService.produce(any()) }
    }

    @Test
    fun `#cancelByEvent should return success but not change status when no show`() = runBlocking {
        val appointmentScheduleEvent = buildAppointmentScheduleRequestEvent("Rua Rebouças, 3506")
        val noShow = TestModelFactory.buildAppointmentSchedule(
            personId = person.id,
            status = AppointmentScheduleStatus.NO_SHOW
        )

        coEvery {
            appointmentScheduleModelDataService.findOne(queryEq { where { this.eventId.eq(noShow.eventId!!) } })
        } returns noShow.toModel().success()

        val result = appointmentScheduleService.cancelByEvent(appointmentScheduleEvent)
        assertThat(result).isSuccessWithData(noShow)

        coVerifyOnce { appointmentScheduleModelDataService.findOne(any()) }
    }

    @Test
    fun `#schedule should schedule an appointment if never added before and location is empty`() = runBlocking {
        val eventName = "Imersão"
        val appointmentScheduleEvent = appointmentScheduleEvent.copy(eventName = eventName)
        val appointmentScheduleToAdd = appointmentScheduleToAdd.copy(
            eventName = eventName,
            type = AppointmentScheduleType.IMMERSION,
            scheduledByInternalScheduler = false
        )
        val fromDate = appointmentScheduled.startTime.minusDays(1)
        val toDate = appointmentScheduled.startTime.plusDays(1)

        coEvery { healthResourcesStore.findStaffByEmail(staff.email) } returns staff.success()
        coEvery { healthResourcesStore.getHealthCareTeamByStaffId(staff.id) } returns healthcareTeam.success()
        coEvery { healthResourcesStore.getHealthPlanTaskByPerson(person.id) } returns healthPlanTasks.success()
        coEvery {
            appointmentScheduleModelDataService.exists(
                queryEq {
                    where {
                        this.id.diff(appointmentScheduleToAdd.id) and
                                this.personId.eq(appointmentScheduleToAdd.personId) and
                                this.startTime.eq(appointmentScheduleToAdd.startTime) and
                                this.status.eq(AppointmentScheduleStatus.SCHEDULED)
                    }
                }
            )
        } returns false.success()
        coEvery { appointmentScheduleModelDataService.add(appointmentScheduleToAdd.toModel()) } returns appointmentScheduleToAdd.toModel().success()

        coEvery {
            appointmentScheduleModelDataService.findOne(
                queryEq {
                    where {
                        this.staffId.eq(staff.id) and
                                this.startTime.eq(appointmentScheduleEvent.startTime) and
                                this.personId.eq(person.id)
                    }
                }
            )
        } returns NotFoundException().failure()
        coEvery {
            appointmentScheduleModelDataService.find(
                queryEq {
                    where {
                        this.staffId.eq(staff.id) and
                                this.status.inList(listOf(AppointmentScheduleStatus.SCHEDULED)) and
                                (this.startTime.greaterEq(fromDate) and this.startTime.lessEq(toDate))
                    }.orderBy { startTime }
                        .sortOrder { desc }
                }
            )
        } returns emptyList<AppointmentScheduleModel>().success()
        coEvery {
            kafkaProducerService.produce(
                AppointmentScheduleCreatedEvent(
                    person = person,
                    appointmentSchedule = appointmentScheduleToAdd,
                    isUsingInternalScheduler = false,
                    professional = Professional.from(staff)
                )
            )
        } returns mockk()

        mockRangeUuidAndDateTime(appointmentScheduleToAddId, baseDate) {
            val result = appointmentScheduleService.schedule(person, appointmentScheduleEvent)
            assertThat(result).isSuccessWithData(listOf(appointmentScheduleToAdd))
        }

        coVerifyOnce { healthResourcesStore.findStaffByEmail(any()) }
        coVerifyOnce { healthResourcesStore.getHealthCareTeamByStaffId(any()) }
        coVerifyOnce { healthResourcesStore.getHealthPlanTaskByPerson(any()) }
        coVerifyOnce { appointmentScheduleModelDataService.exists(any()) }
        coVerifyOnce { appointmentScheduleModelDataService.add(any()) }
        coVerifyOnce { appointmentScheduleModelDataService.findOne(any()) }
        coVerifyOnce { appointmentScheduleModelDataService.find(any()) }
        coVerifyOnce { kafkaProducerService.produce(any()) }
    }

    @Test
    fun `#schedule should fail if we cant get staff`() = runBlocking {
        val appointmentScheduleEvent = buildAppointmentScheduleRequestEvent(null).copy(eventName = "Imersão")

        coEvery { healthResourcesStore.findStaffByEmail(staff.email) } returns Exception("").failure()

        val result = appointmentScheduleService.schedule(person, appointmentScheduleEvent)
        assertThat(result).isFailureOfType(Exception::class)

        coVerifyOnce { healthResourcesStore.findStaffByEmail(any()) }
    }

    @Test
    fun `#schedule should not associate healthcare team when healthcare team does not exists for a physician`() =
        runBlocking {
            val appointmentScheduleEvent = appointmentScheduleEvent.copy(healthPlanTaskId = null)
            val appointmentScheduleToAdd = appointmentScheduleToAdd.copy(
                healthPlanTaskId = null,
                healthcareTeamId = null,
                scheduledByInternalScheduler = false
            )
            val fromDate = appointmentScheduled.startTime.minusDays(1)
            val toDate = appointmentScheduled.startTime.plusDays(1)

            coEvery { healthResourcesStore.findStaffByEmail(staff.email) } returns staff.success()
            coEvery { healthResourcesStore.getHealthCareTeamByStaffId(staff.id) } returns NotFoundException().failure()
            coEvery {
                appointmentScheduleModelDataService.exists(
                    queryEq {
                        where {
                            this.id.diff(appointmentScheduleToAdd.id) and
                                    this.personId.eq(appointmentScheduleToAdd.personId) and
                                    this.startTime.eq(appointmentScheduleToAdd.startTime) and
                                    this.status.eq(AppointmentScheduleStatus.SCHEDULED)
                        }
                    }
                )
            } returns false.success()
            coEvery { appointmentScheduleModelDataService.add(appointmentScheduleToAdd.toModel()) } returns appointmentScheduleToAdd.toModel().success()
            coEvery {
                appointmentScheduleModelDataService.findOne(
                    queryEq {
                        where {
                            this.staffId.eq(staff.id) and
                                    this.startTime.eq(appointmentScheduleEvent.startTime) and
                                    this.personId.eq(person.id)
                        }
                    }
                )
            } returns NotFoundException().failure()
            coEvery {
                appointmentScheduleModelDataService.find(
                    queryEq {
                        where {
                            this.staffId.eq(staff.id) and
                                    this.status.inList(listOf(AppointmentScheduleStatus.SCHEDULED)) and
                                    (this.startTime.greaterEq(fromDate) and this.startTime.lessEq(toDate))
                        }.orderBy { startTime }
                            .sortOrder { desc }
                    }
                )
            } returns emptyList<AppointmentScheduleModel>().success()
            coEvery {
                kafkaProducerService.produce(
                    AppointmentScheduleCreatedEvent(
                        person = person,
                        appointmentSchedule = appointmentScheduleToAdd,
                        isUsingInternalScheduler = false,
                        professional = Professional.from(staff)
                    )
                )
            } returns mockk()

            mockRangeUuidAndDateTime(appointmentScheduleToAddId, baseDate) {
                val result = appointmentScheduleService.schedule(person, appointmentScheduleEvent)
                assertThat(result).isSuccessWithData(listOf(appointmentScheduleToAdd))
            }

            coVerifyOnce { healthResourcesStore.findStaffByEmail(any()) }
            coVerifyOnce { healthResourcesStore.getHealthCareTeamByStaffId(any()) }
            coVerifyOnce { appointmentScheduleModelDataService.exists(any()) }
            coVerifyOnce { appointmentScheduleModelDataService.add(any()) }
            coVerifyOnce { appointmentScheduleModelDataService.findOne(any()) }
            coVerifyOnce { appointmentScheduleModelDataService.find(any()) }
            coVerifyOnce { kafkaProducerService.produce(any()) }
        }

    @Test
    fun `#schedule should not throw error when staff is not found and is present in the allowlist`() = runBlocking {
        withFeatureFlag(FeatureNamespace.EHR, "allowed_emails_without_staff_list", listOf(staff.email)) {
            val appointmentScheduleEvent = appointmentScheduleEvent.copy(healthPlanTaskId = null)
            val appointmentScheduleToAdd = appointmentScheduleToAdd.copy(
                staffId = null,
                healthPlanTaskId = null,
                healthcareTeamId = null,
                scheduledByInternalScheduler = false
            )

            coEvery { healthResourcesStore.findStaffByEmail(staff.email) } returns NotFoundException().failure()
            coEvery {
                appointmentScheduleModelDataService.exists(
                    queryEq {
                        where {
                            this.id.diff(appointmentScheduleToAdd.id) and
                                    this.personId.eq(appointmentScheduleToAdd.personId) and
                                    this.startTime.eq(appointmentScheduleToAdd.startTime) and
                                    this.status.eq(AppointmentScheduleStatus.SCHEDULED)
                        }
                    }
                )
            } returns false.success()
            coEvery { appointmentScheduleModelDataService.add(appointmentScheduleToAdd.toModel()) } returns 
                    appointmentScheduleToAdd.toModel().success()

            coEvery {
                appointmentScheduleModelDataService.findOne(
                    queryEq {
                        where {
                            this.eventId.eq(
                                appointmentScheduleEvent.eventId
                            )
                        }
                    }
                )
            } returns NotFoundException().failure()
            coEvery {
                kafkaProducerService.produce(
                    AppointmentScheduleCreatedEvent(
                        person = person,
                        appointmentSchedule = appointmentScheduleToAdd,
                        isUsingInternalScheduler = false,
                        professional = null
                    )
                )
            } returns mockk()

            mockRangeUuidAndDateTime(appointmentScheduleToAddId, baseDate) {
                val result = appointmentScheduleService.schedule(person, appointmentScheduleEvent)
                result.get()
                assertThat(result).isSuccessWithData(listOf(appointmentScheduleToAdd))
            }

            coVerifyOnce { healthResourcesStore.findStaffByEmail(any()) }
            coVerifyOnce { appointmentScheduleModelDataService.exists(any()) }
            coVerifyOnce { appointmentScheduleModelDataService.add(any()) }
            coVerifyOnce { appointmentScheduleModelDataService.findOne(any()) }
            coVerifyOnce { kafkaProducerService.produce(any()) }
        }
    }

    @Test
    fun `#schedule should throw error when staff is not found and not present in the allowlist`() = runBlocking {
        val appointmentScheduleEvent = buildAppointmentScheduleRequestEvent("LAB")

        coEvery { healthResourcesStore.findStaffByEmail(staff.email) } returns NotFoundException().failure()

        val result = appointmentScheduleService.schedule(person, appointmentScheduleEvent)
        assertThat(result).isFailureOfType(IllegalArgumentException::class)

        coVerifyOnce { healthResourcesStore.findStaffByEmail(any()) }
    }

    @Test
    fun `#schedule should schedule an appointment and associate a healthcare team when a person doesn't have one`() =
        runBlocking {
            val eventName = "Imersão"
            val appointmentScheduleEvent = appointmentScheduleEvent.copy(
                eventName = eventName,
                healthPlanTaskId = null
            )
            val appointmentScheduleToAdd = appointmentScheduleToAdd.copy(
                eventName = eventName,
                type = AppointmentScheduleType.IMMERSION,
                healthPlanTaskId = null,
                scheduledByInternalScheduler = false
            )
            val fromDate = appointmentScheduled.startTime.minusDays(1)
            val toDate = appointmentScheduled.startTime.plusDays(1)

            coEvery { healthResourcesStore.findStaffByEmail(staff.email) } returns staff.success()
            coEvery { healthResourcesStore.getHealthCareTeamByStaffId(staff.id) } returns healthcareTeam.success()
            coEvery {
                appointmentScheduleModelDataService.exists(
                    queryEq {
                        where {
                            this.id.diff(appointmentScheduleToAdd.id) and
                                    this.personId.eq(appointmentScheduleToAdd.personId) and
                                    this.startTime.eq(appointmentScheduleToAdd.startTime) and
                                    this.status.eq(AppointmentScheduleStatus.SCHEDULED)
                        }
                    }
                )
            } returns false.success()
            coEvery { appointmentScheduleModelDataService.add(appointmentScheduleToAdd.toModel()) } returns 
                    appointmentScheduleToAdd.toModel().success()
            coEvery {
                appointmentScheduleModelDataService.findOne(
                    queryEq {
                        where {
                            this.staffId.eq(staff.id) and
                                    this.startTime.eq(appointmentScheduleEvent.startTime) and
                                    this.personId.eq(person.id)
                        }
                    }
                )
            } returns NotFoundException().failure()
            coEvery {
                appointmentScheduleModelDataService.find(
                    queryEq {
                        where {
                            this.staffId.eq(staff.id) and
                                    this.status.inList(listOf(AppointmentScheduleStatus.SCHEDULED)) and
                                    (this.startTime.greaterEq(fromDate) and this.startTime.lessEq(toDate))
                        }.orderBy { startTime }
                            .sortOrder { desc }
                    }
                )
            } returns emptyList<AppointmentScheduleModel>().success()
            coEvery {
                kafkaProducerService.produce(
                    AppointmentScheduleCreatedEvent(
                        person = person,
                        appointmentSchedule = appointmentScheduleToAdd,
                        isUsingInternalScheduler = false,
                        professional = Professional.from(staff)
                    )
                )
            } returns mockk()

            mockRangeUuidAndDateTime(appointmentScheduleToAddId, baseDate) {
                val result = appointmentScheduleService.schedule(person, appointmentScheduleEvent)
                assertThat(result).isSuccessWithData(listOf(appointmentScheduleToAdd))
            }

            coVerifyOnce { healthResourcesStore.findStaffByEmail(any()) }
            coVerifyOnce { healthResourcesStore.getHealthCareTeamByStaffId(any()) }
            coVerifyOnce { appointmentScheduleModelDataService.exists(any()) }
            coVerifyOnce { appointmentScheduleModelDataService.add(any()) }
            coVerifyOnce { appointmentScheduleModelDataService.findOne(any()) }
            coVerifyOnce { appointmentScheduleModelDataService.find(any()) }
            coVerifyOnce { kafkaProducerService.produce(any()) }
        }

    @Test
    fun `#schedule should schedule a new appointment when a person has an health care team`() = runBlocking {
        val eventName = "Retorno Nutri"
        val appointmentScheduleEvent = appointmentScheduleEvent.copy(
            eventName = eventName,
            location = schedulePreference.zoomLink
        )
        val appointmentScheduleToAdd = appointmentScheduleToAdd.copy(
            eventName = eventName,
            type = AppointmentScheduleType.FOLLOW_UP_NUTRITIONIST,
            location = schedulePreference.zoomLink,
            scheduledByInternalScheduler = false
        )
        val fromDate = appointmentScheduled.startTime.minusDays(1)
        val toDate = appointmentScheduled.startTime.plusDays(1)

        coEvery { healthResourcesStore.getHealthPlanTaskByPerson(person.id) } returns healthPlanTasks.success()
        coEvery { healthResourcesStore.findStaffByEmail(staff.email) } returns staff.success()
        coEvery { healthResourcesStore.getHealthCareTeamByStaffId(staff.id) } returns healthcareTeam.success()
        coEvery {
            appointmentScheduleModelDataService.exists(
                queryEq {
                    where {
                        this.id.diff(appointmentScheduleToAdd.id) and
                                this.personId.eq(appointmentScheduleToAdd.personId) and
                                this.startTime.eq(appointmentScheduleToAdd.startTime) and
                                this.status.eq(AppointmentScheduleStatus.SCHEDULED)
                    }
                }
            )
        } returns false.success()
        coEvery { appointmentScheduleModelDataService.add(appointmentScheduleToAdd.toModel()) } returns 
                appointmentScheduleToAdd.toModel().success()
        coEvery {
            appointmentScheduleModelDataService.findOne(
                queryEq {
                    where {
                        this.staffId.eq(staff.id) and
                                this.startTime.eq(appointmentScheduleEvent.startTime) and
                                this.personId.eq(person.id)
                    }
                }
            )
        } returns NotFoundException().failure()
        coEvery {
            appointmentScheduleModelDataService.find(
                queryEq {
                    where {
                        this.staffId.eq(staff.id) and
                                this.status.inList(listOf(AppointmentScheduleStatus.SCHEDULED)) and
                                (this.startTime.greaterEq(fromDate) and this.startTime.lessEq(toDate))
                    }.orderBy { startTime }
                        .sortOrder { desc }
                }
            )
        } returns emptyList<AppointmentScheduleModel>().success()
        coEvery {
            kafkaProducerService.produce(
                AppointmentScheduleCreatedEvent(
                    person = person,
                    appointmentSchedule = appointmentScheduleToAdd,
                    isUsingInternalScheduler = false,
                    professional = Professional.from(staff)
                )
            )
        } returns mockk()

        mockRangeUuidAndDateTime(appointmentScheduleToAddId, baseDate) {
            val result = appointmentScheduleService.schedule(person, appointmentScheduleEvent)
            assertThat(result).isSuccessWithData(listOf(appointmentScheduleToAdd))
        }

        coVerifyOnce { healthResourcesStore.getHealthPlanTaskByPerson(any()) }
        coVerifyOnce { healthResourcesStore.findStaffByEmail(any()) }
        coVerifyOnce { healthResourcesStore.getHealthCareTeamByStaffId(any()) }
        coVerifyOnce { appointmentScheduleModelDataService.findOne(any()) }
        coVerifyOnce { appointmentScheduleModelDataService.find(any()) }
        coVerifyOnce { appointmentScheduleModelDataService.exists(any()) }
        coVerifyOnce { appointmentScheduleModelDataService.add(any()) }
        coVerifyOnce { kafkaProducerService.produce(any()) }
    }

    @Test
    fun `#schedule should schedule a event of type other when appointment is meeting`() = runBlocking {
        val eventName = "Encontro Nutri"
        val appointmentScheduleEvent = appointmentScheduleEvent.copy(
            eventName = eventName,
            location = schedulePreference.zoomLink
        )
        val appointmentScheduleToAdd = appointmentScheduleToAdd.copy(
            eventName = eventName,
            type = AppointmentScheduleType.OTHER,
            location = schedulePreference.zoomLink,
            scheduledByInternalScheduler = false
        )
        val fromDate = appointmentScheduled.startTime.minusDays(1)
        val toDate = appointmentScheduled.startTime.plusDays(1)

        coEvery { healthResourcesStore.getHealthPlanTaskByPerson(person.id) } returns healthPlanTasks.success()
        coEvery { healthResourcesStore.findStaffByEmail(staff.email) } returns staff.success()
        coEvery { healthResourcesStore.getHealthCareTeamByStaffId(staff.id) } returns healthcareTeam.success()
        coEvery {
            appointmentScheduleModelDataService.exists(
                queryEq {
                    where {
                        this.id.diff(appointmentScheduleToAdd.id) and
                                this.personId.eq(appointmentScheduleToAdd.personId) and
                                this.startTime.eq(appointmentScheduleToAdd.startTime) and
                                this.status.eq(AppointmentScheduleStatus.SCHEDULED)
                    }
                }
            )
        } returns false.success()
        coEvery { appointmentScheduleModelDataService.add(appointmentScheduleToAdd.toModel()) } returns 
                appointmentScheduleToAdd.toModel().success()
        coEvery {
            appointmentScheduleModelDataService.findOne(
                queryEq {
                    where {
                        this.staffId.eq(staff.id) and
                                this.startTime.eq(appointmentScheduleEvent.startTime) and
                                this.personId.eq(person.id)
                    }
                }
            )
        } returns NotFoundException().failure()
        coEvery {
            appointmentScheduleModelDataService.find(
                queryEq {
                    where {
                        this.staffId.eq(staff.id) and
                                this.status.inList(listOf(AppointmentScheduleStatus.SCHEDULED)) and
                                (this.startTime.greaterEq(fromDate) and this.startTime.lessEq(toDate))
                    }.orderBy { startTime }
                        .sortOrder { desc }
                }
            )
        } returns emptyList<AppointmentScheduleModel>().success()
        coEvery {
            kafkaProducerService.produce(
                AppointmentScheduleCreatedEvent(
                    person = person,
                    appointmentSchedule = appointmentScheduleToAdd,
                    isUsingInternalScheduler = false,
                    professional = Professional.from(staff)
                )
            )
        } returns mockk()

        mockRangeUuidAndDateTime(appointmentScheduleToAddId, baseDate) {
            val result = appointmentScheduleService.schedule(person, appointmentScheduleEvent)
            assertThat(result).isSuccessWithData(listOf(appointmentScheduleToAdd))
        }

        coVerifyOnce { healthResourcesStore.getHealthPlanTaskByPerson(any()) }
        coVerifyOnce { healthResourcesStore.findStaffByEmail(any()) }
        coVerifyOnce { healthResourcesStore.getHealthCareTeamByStaffId(any()) }
        coVerifyOnce { appointmentScheduleModelDataService.exists(any()) }
        coVerifyOnce { appointmentScheduleModelDataService.add(any()) }
        coVerifyOnce { appointmentScheduleModelDataService.find(any()) }
        coVerifyOnce { appointmentScheduleModelDataService.findOne(any()) }
        coVerifyOnce { kafkaProducerService.produce(any()) }
    }

    @Test
    fun `#scheduleInternal should schedule a new appointment`() = runBlocking {

        val copyAppointmentScheduled = appointmentScheduleToAdd.copy(
            startTime = appointmentScheduleToAdd.startTime.plusDays(7),
            endTime = appointmentScheduleToAdd.endTime!!.plusDays(7)
        )
        val appointmentList = listOf(appointmentScheduleToAdd, copyAppointmentScheduled)
        val appointmentModelList = appointmentList.map { it.toModel() }
        val fromDate = appointmentScheduled.startTime.minusDays(1)
        val toDate = appointmentScheduled.startTime.plusDays(1)

        coEvery { healthResourcesStore.getHealthPlanTaskByPerson(person.id) } returns healthPlanTasks.success()
        coEvery { healthResourcesStore.findStaffByEmail(staff.email) } returns staff.success()
        coEvery { healthResourcesStore.getHealthCareTeamByStaffId(staff.id) } returns healthcareTeam.success()
        coEvery { appointmentScheduleModelDataService.addList(appointmentModelList) } returns appointmentModelList.success()
        coEvery { personService.get(person.id) } returns person.success()
        coEvery {
            appointmentScheduleModelDataService.find(
                queryEq {
                    where {
                        this.staffId.eq(staff.id) and
                                this.status.inList(listOf(AppointmentScheduleStatus.SCHEDULED)) and
                                (this.startTime.greaterEq(fromDate) and this.startTime.lessEq(toDate))
                    }.orderBy { startTime }
                        .sortOrder { desc }
                }
            )
        } returns emptyList<AppointmentScheduleModel>().success()
        coEvery {
            kafkaProducerService.produce(InternalAppointmentScheduleCreatedEvent(appointmentScheduleToAdd))
        } returns mockk()
        coEvery {
            kafkaProducerService.produce(InternalAppointmentScheduleCreatedEvent(copyAppointmentScheduled))
        } returns mockk()
        coEvery {
            kafkaProducerService.produce(
                AppointmentScheduleCreatedEvent(
                    person = person,
                    appointmentSchedule = appointmentScheduleToAdd,
                    isUsingInternalScheduler = true,
                    professional = Professional.from(staff)
                )
            )
        } returns mockk()
        coEvery {
            kafkaProducerService.produce(
                AppointmentScheduleCreatedEvent(
                    person = person,
                    appointmentSchedule = copyAppointmentScheduled,
                    isUsingInternalScheduler = true,
                    professional = Professional.from(staff)
                )
            )
        } returns mockk()

        mockRangeUuidAndDateTime(appointmentScheduleToAddId, baseDate) {
            val result =
                appointmentScheduleService.scheduleInternal(person.id, appointmentScheduleEvent, numberOfSessions = 2)
            assertThat(result).isSuccessWithData(appointmentList)
        }

        coVerifyOnce { healthResourcesStore.getHealthPlanTaskByPerson(any()) }
        coVerifyOnce { healthResourcesStore.findStaffByEmail(any()) }
        coVerifyOnce { healthResourcesStore.getHealthCareTeamByStaffId(any()) }
        coVerifyOnce { personService.get(any()) }
        coVerifyOnce { appointmentScheduleModelDataService.find(any()) }
        coVerifyOnce { appointmentScheduleModelDataService.addList(any()) }
        coVerify(exactly = 4) { kafkaProducerService.produce(any()) }
    }

    @Test
    fun `#scheduleInternal should schedule a not create if there is already one with same id`() = runBlocking {
        val fromDate = appointmentScheduled.startTime.minusDays(1)
        val toDate = appointmentScheduled.startTime.plusDays(1)

        coEvery { healthResourcesStore.getHealthPlanTaskByPerson(person.id) } returns healthPlanTasks.success()
        coEvery { healthResourcesStore.findStaffByEmail(staff.email) } returns staff.success()
        coEvery { healthResourcesStore.getHealthCareTeamByStaffId(staff.id) } returns healthcareTeam.success()
        coEvery {
            appointmentScheduleModelDataService.exists(
                queryEq {
                    where {
                        this.id.diff(appointmentScheduledToUpdate.id) and
                                this.personId.eq(appointmentScheduledToUpdate.personId) and
                                this.startTime.eq(appointmentScheduledToUpdate.startTime) and
                                this.status.eq(AppointmentScheduleStatus.SCHEDULED)
                    }
                }
            )
        } returns false.success()
        coEvery { appointmentScheduleModelDataService.update(appointmentScheduledToUpdate.toModel()) } returns 
                appointmentScheduledToUpdate.toModel().success()
        coEvery { personService.get(person.id) } returns person.success()
        coEvery {
            appointmentScheduleModelDataService.findOne(
                queryEq {
                    where {
                        this.staffId.eq(staff.id) and
                                this.startTime.eq(appointmentScheduled.startTime) and
                                this.personId.eq(person.id)
                    }
                }
            )
        } returns appointmentScheduled.toModel().success()
        coEvery {
            appointmentScheduleModelDataService.find(
                queryEq {
                    where {
                        this.staffId.eq(staff.id) and
                                this.status.inList(listOf(AppointmentScheduleStatus.SCHEDULED)) and
                                (this.startTime.greaterEq(fromDate) and this.startTime.lessEq(toDate))
                    }.orderBy { startTime }
                        .sortOrder { desc }
                }
            )
        } returns emptyList<AppointmentScheduleModel>().success()
        coEvery {
            kafkaProducerService.produce(
                AppointmentScheduleCreatedEvent(
                    person = person,
                    appointmentSchedule = appointmentScheduledToUpdate,
                    isUsingInternalScheduler = true,
                    professional = Professional.from(staff)
                )
            )
        } returns mockk()

        mockRangeUuidAndDateTime(appointmentScheduleToAddId, baseDate) {
            val result =
                appointmentScheduleService.scheduleInternal(person.id, appointmentScheduleEvent, numberOfSessions = 1)
            assertThat(result).isSuccessWithData(listOf(appointmentScheduledToUpdate))
        }

        coVerifyOnce { healthResourcesStore.getHealthPlanTaskByPerson(any()) }
        coVerifyOnce { healthResourcesStore.findStaffByEmail(any()) }
        coVerifyOnce { healthResourcesStore.getHealthCareTeamByStaffId(any()) }
        coVerifyOnce { appointmentScheduleModelDataService.find(any()) }
        coVerifyOnce { appointmentScheduleModelDataService.findOne(any()) }
        coVerifyOnce { appointmentScheduleModelDataService.exists(any()) }
        coVerifyOnce { appointmentScheduleModelDataService.update(any()) }
        coVerifyOnce { personService.get(any()) }
        coVerifyOnce { kafkaProducerService.produce(any()) }
    }

    @Test
    fun `#scheduleInternal should schedule a not create if there is already one with same id but if its status is canceled, should produce events`() =
        runBlocking {
            val fromDate = appointmentScheduled.startTime.minusDays(1)
            val toDate = appointmentScheduled.startTime.plusDays(1)

            coEvery { healthResourcesStore.getHealthPlanTaskByPerson(person.id) } returns healthPlanTasks.success()
            coEvery { healthResourcesStore.findStaffByEmail(staff.email) } returns staff.success()
            coEvery { healthResourcesStore.getHealthCareTeamByStaffId(staff.id) } returns healthcareTeam.success()
            coEvery {
                appointmentScheduleModelDataService.exists(
                    queryEq {
                        where {
                            this.id.diff(appointmentScheduledToUpdate.id) and
                                    this.personId.eq(appointmentScheduledToUpdate.personId) and
                                    this.startTime.eq(appointmentScheduledToUpdate.startTime) and
                                    this.status.eq(AppointmentScheduleStatus.SCHEDULED)
                        }
                    }
                )
            } returns false.success()
            coEvery { appointmentScheduleModelDataService.update(appointmentScheduledToUpdate.toModel()) } returns 
                    appointmentScheduledToUpdate.toModel().success()
            coEvery { personService.get(person.id) } returns person.success()
            coEvery {
                appointmentScheduleModelDataService.findOne(
                    queryEq {
                        where {
                            this.staffId.eq(staff.id) and
                                    this.startTime.eq(appointmentScheduled.startTime) and
                                    this.personId.eq(person.id)
                        }
                    }
                )
            } returns appointmentScheduled.copy(status = AppointmentScheduleStatus.CANCELED).toModel().success()
            coEvery {
                appointmentScheduleModelDataService.find(
                    queryEq {
                        where {
                            this.staffId.eq(staff.id) and
                                    this.status.inList(listOf(AppointmentScheduleStatus.SCHEDULED)) and
                                    (this.startTime.greaterEq(fromDate) and this.startTime.lessEq(toDate))
                        }.orderBy { startTime }
                            .sortOrder { desc }
                    }
                )
            } returns emptyList<AppointmentScheduleModel>().success()
            coEvery {
                kafkaProducerService.produce(InternalAppointmentScheduleCreatedEvent(appointmentScheduledToUpdate))
            } returns mockk()
            coEvery {
                kafkaProducerService.produce(
                    AppointmentScheduleCreatedEvent(
                        person = person,
                        appointmentSchedule = appointmentScheduledToUpdate,
                        isUsingInternalScheduler = true,
                        professional = Professional.from(staff)
                    )
                )
            } returns mockk()

            mockRangeUuidAndDateTime(appointmentScheduleToAddId, baseDate) {
                val result = appointmentScheduleService.scheduleInternal(
                    person.id,
                    appointmentScheduleEvent,
                    numberOfSessions = 1
                )
                assertThat(result).isSuccessWithData(listOf(appointmentScheduledToUpdate))
            }

            coVerifyOnce { healthResourcesStore.getHealthPlanTaskByPerson(any()) }
            coVerifyOnce { healthResourcesStore.findStaffByEmail(any()) }
            coVerifyOnce { healthResourcesStore.getHealthCareTeamByStaffId(any()) }
            coVerifyOnce { appointmentScheduleModelDataService.find(any()) }
            coVerifyOnce { appointmentScheduleModelDataService.findOne(any()) }
            coVerifyOnce { appointmentScheduleModelDataService.exists(any()) }
            coVerifyOnce { appointmentScheduleModelDataService.update(any()) }
            coVerifyOnce { personService.get(any()) }
            coVerify(exactly = 2) { kafkaProducerService.produce(any()) }
        }

    @Test
    fun `#scheduleInternal should schedule a new appointment with location if event has provider unit id`() =
        runBlocking {

            val appointmentScheduleToAdd = appointmentScheduleToAdd.copy(
                location = address.formattedAddress(),
                providerUnitId = providerUnit.id
            )

            val fromDate = appointmentScheduled.startTime.minusDays(1)
            val toDate = appointmentScheduled.startTime.plusDays(1)

            coEvery { healthResourcesStore.getHealthPlanTaskByPerson(person.id) } returns healthPlanTasks.success()
            coEvery { healthResourcesStore.findStaffByEmail(staff.email) } returns staff.success()
            coEvery { healthResourcesStore.getHealthCareTeamByStaffId(staff.id) } returns healthcareTeam.success()
            coEvery {
                appointmentScheduleModelDataService.exists(
                    queryEq {
                        where {
                            this.id.diff(appointmentScheduleToAdd.id) and
                                    this.personId.eq(appointmentScheduleToAdd.personId) and
                                    this.startTime.eq(appointmentScheduleToAdd.startTime) and
                                    this.status.eq(AppointmentScheduleStatus.SCHEDULED)
                        }
                    }
                )
            } returns false.success()
            coEvery { appointmentScheduleModelDataService.add(appointmentScheduleToAdd.toModel()) } returns 
                    appointmentScheduleToAdd.toModel().success()
            coEvery { personService.get(person.id) } returns person.success()
            coEvery {
                appointmentScheduleModelDataService.findOne(
                    queryEq {
                        where {
                            this.staffId.eq(staff.id) and
                                    this.startTime.eq(appointmentScheduleEvent.startTime) and
                                    this.personId.eq(person.id)
                        }
                    }
                )
            } returns NotFoundException().failure()
            coEvery {
                appointmentScheduleModelDataService.find(
                    queryEq {
                        where {
                            this.staffId.eq(staff.id) and
                                    this.status.inList(listOf(AppointmentScheduleStatus.SCHEDULED)) and
                                    (this.startTime.greaterEq(fromDate) and this.startTime.lessEq(toDate))
                        }.orderBy { startTime }
                            .sortOrder { desc }
                    }
                )
            } returns emptyList<AppointmentScheduleModel>().success()

            coEvery { healthResourcesStore.getProviderUnitAddress(providerUnit.id) } returns address.formattedAddress()
                .success()
            coEvery {
                kafkaProducerService.produce(InternalAppointmentScheduleCreatedEvent(appointmentScheduleToAdd))
            } returns mockk()
            coEvery {
                kafkaProducerService.produce(
                    AppointmentScheduleCreatedEvent(
                        person = person,
                        appointmentSchedule = appointmentScheduleToAdd,
                        isUsingInternalScheduler = true,
                        professional = Professional.from(staff)
                    )
                )
            } returns mockk()

            mockRangeUuidAndDateTime(appointmentScheduleToAddId, baseDate) {
                val result = appointmentScheduleService.scheduleInternal(
                    person.id,
                    appointmentScheduleEvent.copy(providerUnitId = providerUnit.id.toString()),
                    numberOfSessions = 1
                )
                assertThat(result).isSuccessWithData(listOf(appointmentScheduleToAdd))
            }

            coVerifyOnce { healthResourcesStore.getHealthPlanTaskByPerson(any()) }
            coVerifyOnce { healthResourcesStore.findStaffByEmail(any()) }
            coVerifyOnce { healthResourcesStore.getHealthCareTeamByStaffId(any()) }
            coVerifyOnce { healthResourcesStore.getProviderUnitAddress(any()) }
            coVerifyOnce { appointmentScheduleModelDataService.find(any()) }
            coVerifyOnce { appointmentScheduleModelDataService.findOne(any()) }
            coVerifyOnce { appointmentScheduleModelDataService.exists(any()) }
            coVerifyOnce { appointmentScheduleModelDataService.add(any()) }
            coVerifyOnce { personService.get(any()) }
            coVerify(exactly = 2) { kafkaProducerService.produce(any()) }
        }

    @Test
    fun `#scheduleInternal should fail if it does not have end time and not event type`() = runBlocking {
        val appointmentScheduleEvent =
            buildAppointmentScheduleRequestEvent("LAB", taskUUID.toString().toSha256(), endTime = null)

        coEvery { healthResourcesStore.findStaffByEmail(staff.email) } returns staff.success()
        coEvery { personService.get(person.id) } returns person.success()

        val result =
            appointmentScheduleService.scheduleInternal(person.id, appointmentScheduleEvent, numberOfSessions = 1)
        assertThat(result).isFailureOfType(IllegalArgumentException::class)

        coVerifyOnce { healthResourcesStore.findStaffByEmail(any()) }
        coVerifyOnce { personService.get(any()) }
    }

    @Test
    fun `#scheduleInternal should fail if it does not have end time and event type, but error getting event type`() =
        runBlocking {
            val appointmentScheduleEvent = buildAppointmentScheduleRequestEvent(
                "LAB",
                taskUUID.toString().toSha256(),
                endTime = null,
                appointmentScheduleEventTypeId = appointmentScheduleEventType.id
            )

            coEvery { healthResourcesStore.findStaffByEmail(staff.email) } returns staff.success()
            coEvery {
                appointmentScheduleEventTypeService.get(appointmentScheduleEventType.id)
            } returns IllegalArgumentException().failure()

            val result =
                appointmentScheduleService.scheduleInternal(person.id, appointmentScheduleEvent, numberOfSessions = 1)
            assertThat(result).isFailureOfType(IllegalArgumentException::class)

            coVerifyOnce { healthResourcesStore.findStaffByEmail(any()) }
            coVerifyOnce { appointmentScheduleEventTypeService.get(any()) }
        }

    @Test
    fun `#scheduleInternal should get event type schedule and use it to build end time if payload does not have end time`() =
        runBlocking {
            val appointmentScheduleEvent = appointmentScheduleEvent.copy(
                endTime = null,
                appointmentScheduleEventTypeId = appointmentScheduleEventType.id
            )
            val appointmentScheduleToAdd = appointmentScheduleToAdd.copy(
                eventName = "Ale Carreiro",
                type = AppointmentScheduleType.HOME_TEST,
                endTime = startTime.plusMinutes(30),
                appointmentScheduleEventTypeId = appointmentScheduleEventType.id
            )

            coEvery {
                appointmentScheduleEventTypeService.get(appointmentScheduleEventType.id)
            } returns appointmentScheduleEventType.success()
            coEvery { healthResourcesStore.getHealthPlanTaskByPerson(person.id) } returns healthPlanTasks.success()
            coEvery { healthResourcesStore.findStaffByEmail(staff.email) } returns staff.success()
            coEvery { healthResourcesStore.getHealthCareTeamByStaffId(staff.id) } returns healthcareTeam.success()
            coEvery {
                appointmentScheduleModelDataService.exists(
                    queryEq {
                        where {
                            this.id.diff(appointmentScheduleToAdd.id) and
                                    this.personId.eq(appointmentScheduleToAdd.personId) and
                                    this.startTime.eq(appointmentScheduleToAdd.startTime) and
                                    this.status.eq(AppointmentScheduleStatus.SCHEDULED)
                        }
                    }
                )
            } returns false.success()
            coEvery { appointmentScheduleModelDataService.add(appointmentScheduleToAdd.toModel()) } returns 
                    appointmentScheduleToAdd.toModel().success()
            coEvery { personService.get(person.id) } returns person.success()
            coEvery {
                appointmentScheduleModelDataService.findOne(
                    queryEq {
                        where {
                            this.staffId.eq(staff.id) and
                                    this.startTime.eq(appointmentScheduleEvent.startTime) and
                                    this.personId.eq(person.id)
                        }
                    }
                )
            } returns NotFoundException().failure()
            coEvery { kafkaProducerService.produce(any()) } returns mockk()
            coEvery {
                eventTypeProviderUnitService.getForEventType(appointmentScheduleEventType.id)
            } returns listOf(eventTypeProviderUnitDigital).success()

            mockRangeUuidAndDateTime(appointmentScheduleToAddId, baseDate) {
                val result = appointmentScheduleService.scheduleInternal(
                    person.id,
                    appointmentScheduleEvent,
                    numberOfSessions = 1
                )
                assertThat(result).isSuccessWithData(listOf(appointmentScheduleToAdd))
            }

            coVerifyOnce { healthResourcesStore.getHealthPlanTaskByPerson(any()) }
            coVerifyOnce { healthResourcesStore.findStaffByEmail(any()) }
            coVerifyOnce { healthResourcesStore.getHealthCareTeamByStaffId(any()) }
            coVerifyOnce { appointmentScheduleEventTypeService.get(any()) }
            coVerifyOnce { eventTypeProviderUnitService.getForEventType(any()) }
            coVerifyOnce { appointmentScheduleModelDataService.exists(any()) }
            coVerifyOnce { appointmentScheduleModelDataService.add(any()) }
            coVerifyOnce { personService.get(any()) }
            coVerifyOnce { appointmentScheduleModelDataService.findOne(any()) }
            coVerify(exactly = 2) { kafkaProducerService.produce(any()) }
        }

    @Test
    fun `#scheduleInternal should use digital provider unit from event type provider unit if it has only one associated to event type`() =
        runBlocking {
            val appointmentScheduleEvent = appointmentScheduleEvent.copy(
                endTime = null,
                appointmentScheduleEventTypeId = appointmentScheduleEventType.id
            )
            val appointmentScheduleToAdd = appointmentScheduleToAdd.copy(
                eventName = "Ale Carreiro",
                type = AppointmentScheduleType.HOME_TEST,
                endTime = startTime.plusMinutes(30),
                appointmentScheduleEventTypeId = appointmentScheduleEventType.id
            )
            coEvery {
                appointmentScheduleEventTypeService.get(appointmentScheduleEventType.id)
            } returns appointmentScheduleEventType.success()

            coEvery { healthResourcesStore.getHealthPlanTaskByPerson(person.id) } returns healthPlanTasks.success()
            coEvery { healthResourcesStore.findStaffByEmail(staff.email) } returns staff.success()
            coEvery { healthResourcesStore.getHealthCareTeamByStaffId(staff.id) } returns healthcareTeam.success()
            coEvery {
                appointmentScheduleModelDataService.exists(
                    queryEq {
                        where {
                            this.id.diff(appointmentScheduleToAdd.id) and
                                    this.personId.eq(appointmentScheduleToAdd.personId) and
                                    this.startTime.eq(appointmentScheduleToAdd.startTime) and
                                    this.status.eq(AppointmentScheduleStatus.SCHEDULED)
                        }
                    }
                )
            } returns false.success()
            coEvery { appointmentScheduleModelDataService.add(appointmentScheduleToAdd.toModel()) } returns 
                    appointmentScheduleToAdd.toModel().success()
            coEvery { personService.get(person.id) } returns person.success()
            coEvery {
                appointmentScheduleModelDataService.findOne(
                    queryEq {
                        where {
                            this.staffId.eq(staff.id) and
                                    this.startTime.eq(appointmentScheduleEvent.startTime) and
                                    this.personId.eq(person.id)
                        }
                    }
                )
            } returns NotFoundException().failure()
            coEvery { kafkaProducerService.produce(any()) } returns mockk()
            coEvery {
                eventTypeProviderUnitService.getForEventType(appointmentScheduleEventType.id)
            } returns listOf(eventTypeProviderUnitDigital).success()
            coEvery { healthResourcesStore.getProviderUnitAddress(providerUnit.id) } returns address.formattedAddress()
                .success()

            mockRangeUuidAndDateTime(appointmentScheduleToAddId, baseDate) {
                val result = appointmentScheduleService.scheduleInternal(
                    person.id,
                    appointmentScheduleEvent,
                    numberOfSessions = 1
                )
                assertThat(result).isSuccessWithData(listOf(appointmentScheduleToAdd))
            }

            coVerifyOnce { healthResourcesStore.getHealthPlanTaskByPerson(any()) }
            coVerifyOnce { healthResourcesStore.findStaffByEmail(any()) }
            coVerifyOnce { healthResourcesStore.getHealthCareTeamByStaffId(any()) }
            coVerifyOnce { appointmentScheduleEventTypeService.get(any()) }
            coVerifyOnce { eventTypeProviderUnitService.getForEventType(any()) }
            coVerifyOnce { appointmentScheduleModelDataService.exists(any()) }
            coVerifyOnce { appointmentScheduleModelDataService.add(any()) }
            coVerifyOnce { personService.get(any()) }
            coVerifyOnce { appointmentScheduleModelDataService.findOne(any()) }
            coVerify(exactly = 2) { kafkaProducerService.produce(any()) }
        }

    @Test
    fun `#scheduleInternal should use in person provider unit from event type provider unit if it has only one associated to event type`() =
        runBlocking {
            val appointmentScheduleEvent = appointmentScheduleEvent.copy(
                endTime = null,
                appointmentScheduleEventTypeId = appointmentScheduleEventType.id,
                providerUnitId = eventTypeProviderUnit.providerUnitId.toString()
            )
            val appointmentScheduleToAdd = appointmentScheduleToAdd.copy(
                eventName = "Ale Carreiro",
                type = AppointmentScheduleType.HOME_TEST,
                appointmentScheduleEventTypeId = appointmentScheduleEventType.id,
                location = address.formattedAddress(),
                endTime = startTime.plusMinutes(30),
                providerUnitId = eventTypeProviderUnit.providerUnitId
            )

            coEvery {
                appointmentScheduleEventTypeService.get(appointmentScheduleEventType.id)
            } returns appointmentScheduleEventType.success()
            coEvery { healthResourcesStore.getHealthPlanTaskByPerson(person.id) } returns healthPlanTasks.success()
            coEvery { healthResourcesStore.findStaffByEmail(staff.email) } returns staff.success()
            coEvery { healthResourcesStore.getHealthCareTeamByStaffId(staff.id) } returns healthcareTeam.success()
            coEvery {
                appointmentScheduleModelDataService.exists(
                    queryEq {
                        where {
                            this.id.diff(appointmentScheduleToAdd.id) and
                                    this.personId.eq(appointmentScheduleToAdd.personId) and
                                    this.startTime.eq(appointmentScheduleToAdd.startTime) and
                                    this.status.eq(AppointmentScheduleStatus.SCHEDULED)
                        }
                    }
                )
            } returns false.success()
            coEvery { appointmentScheduleModelDataService.add(appointmentScheduleToAdd.toModel()) } returns 
                    appointmentScheduleToAdd.toModel().success()
            coEvery { personService.get(person.id) } returns person.success()
            coEvery {
                appointmentScheduleModelDataService.findOne(
                    queryEq {
                        where {
                            this.staffId.eq(staff.id) and
                                    this.startTime.eq(appointmentScheduleEvent.startTime) and
                                    this.personId.eq(person.id)
                        }
                    }
                )
            } returns NotFoundException().failure()
            coEvery { kafkaProducerService.produce(any()) } returns mockk()
            coEvery {
                eventTypeProviderUnitService.getForEventType(appointmentScheduleEventType.id)
            } returns listOf(eventTypeProviderUnit).success()
            coEvery { healthResourcesStore.getProviderUnitAddress(providerUnit.id) } returns address.formattedAddress()
                .success()

            mockRangeUuidAndDateTime(appointmentScheduleToAddId, baseDate) {
                val result = appointmentScheduleService.scheduleInternal(
                    person.id,
                    appointmentScheduleEvent,
                    numberOfSessions = 1
                )
                assertThat(result).isSuccessWithData(listOf(appointmentScheduleToAdd))
            }

            coVerifyOnce { healthResourcesStore.getHealthPlanTaskByPerson(any()) }
            coVerifyOnce { healthResourcesStore.findStaffByEmail(any()) }
            coVerifyOnce { healthResourcesStore.getHealthCareTeamByStaffId(any()) }
            coVerifyOnce { healthResourcesStore.getProviderUnitAddress(any()) }
            coVerifyOnce { appointmentScheduleEventTypeService.get(any()) }
            coVerifyOnce { eventTypeProviderUnitService.getForEventType(any()) }
            coVerifyOnce { appointmentScheduleModelDataService.exists(any()) }
            coVerifyOnce { appointmentScheduleModelDataService.add(any()) }
            coVerifyOnce { personService.get(any()) }
            coVerifyOnce { appointmentScheduleModelDataService.findOne(any()) }
            coVerify(exactly = 2) { kafkaProducerService.produce(any()) }
        }

    @Test
    fun `#schedule should schedule a new appointment with right type when a person has an health care team`() =
        runBlocking {
            val eventName = "Preparação Física"
            val appointmentScheduleEvent = appointmentScheduleEvent.copy(
                eventName = eventName,
                location = schedulePreference.zoomLink
            )
            val appointmentScheduleToAdd = appointmentScheduleToAdd.copy(
                eventName = eventName,
                type = AppointmentScheduleType.PHYSICAL_EDUCATOR,
                location = schedulePreference.zoomLink,
                scheduledByInternalScheduler = false
            )
            val fromDate = appointmentScheduled.startTime.minusDays(1)
            val toDate = appointmentScheduled.startTime.plusDays(1)

            coEvery { healthResourcesStore.getHealthPlanTaskByPerson(person.id) } returns healthPlanTasks.success()
            coEvery { healthResourcesStore.findStaffByEmail(staff.email) } returns staff.success()
            coEvery { healthResourcesStore.getHealthCareTeamByStaffId(staff.id) } returns healthcareTeam.success()
            coEvery {
                appointmentScheduleModelDataService.exists(
                    queryEq {
                        where {
                            this.id.diff(appointmentScheduleToAdd.id) and
                                    this.personId.eq(appointmentScheduleToAdd.personId) and
                                    this.startTime.eq(appointmentScheduleToAdd.startTime) and
                                    this.status.eq(AppointmentScheduleStatus.SCHEDULED)
                        }
                    }
                )
            } returns false.success()
            coEvery { appointmentScheduleModelDataService.add(appointmentScheduleToAdd.toModel()) } returns 
                    appointmentScheduleToAdd.toModel().success()
            coEvery {
                appointmentScheduleModelDataService.findOne(
                    queryEq {
                        where {
                            this.staffId.eq(staff.id) and
                                    this.startTime.eq(appointmentScheduleEvent.startTime) and
                                    this.personId.eq(person.id)
                        }
                    }
                )
            } returns NotFoundException().failure()
            coEvery {
                appointmentScheduleModelDataService.find(
                    queryEq {
                        where {
                            this.staffId.eq(staff.id) and
                                    this.status.inList(listOf(AppointmentScheduleStatus.SCHEDULED)) and
                                    (this.startTime.greaterEq(fromDate) and this.startTime.lessEq(toDate))
                        }.orderBy { startTime }
                            .sortOrder { desc }
                    }
                )
            } returns emptyList<AppointmentScheduleModel>().success()
            coEvery {
                kafkaProducerService.produce(
                    AppointmentScheduleCreatedEvent(
                        person = person,
                        appointmentSchedule = appointmentScheduleToAdd,
                        isUsingInternalScheduler = false,
                        professional = Professional.from(staff)
                    )
                )
            } returns mockk()

            mockRangeUuidAndDateTime(appointmentScheduleToAddId, baseDate) {
                val result = appointmentScheduleService.schedule(person, appointmentScheduleEvent)
                assertThat(result).isSuccessWithData(listOf(appointmentScheduleToAdd))
            }

            coVerifyOnce { healthResourcesStore.getHealthPlanTaskByPerson(any()) }
            coVerifyOnce { healthResourcesStore.findStaffByEmail(any()) }
            coVerifyOnce { healthResourcesStore.getHealthCareTeamByStaffId(any()) }
            coVerifyOnce { appointmentScheduleModelDataService.exists(any()) }
            coVerifyOnce { appointmentScheduleModelDataService.add(any()) }
            coVerifyOnce { appointmentScheduleModelDataService.findOne(any()) }
            coVerifyOnce { appointmentScheduleModelDataService.find(any()) }
            coVerifyOnce { kafkaProducerService.produce(any()) }
        }

    @Test
    fun `#schedule should schedule a new appointment with right type when eventName has Return on it`() =
        runBlocking {
            val eventName = "Retorno - Avaliação Física"
            val appointmentScheduleEvent = appointmentScheduleEvent.copy(
                eventName = eventName,
                location = schedulePreference.zoomLink
            )
            val appointmentScheduleToAdd = appointmentScheduleToAdd.copy(
                eventName = eventName,
                type = AppointmentScheduleType.FOLLOW_UP_PHYSICAL_EDUCATOR,
                location = schedulePreference.zoomLink,
                scheduledByInternalScheduler = false
            )
            val fromDate = appointmentScheduled.startTime.minusDays(1)
            val toDate = appointmentScheduled.startTime.plusDays(1)

            coEvery { healthResourcesStore.getHealthPlanTaskByPerson(person.id) } returns healthPlanTasks.success()
            coEvery { healthResourcesStore.findStaffByEmail(staff.email) } returns staff.success()
            coEvery { healthResourcesStore.getHealthCareTeamByStaffId(staff.id) } returns healthcareTeam.success()
            coEvery {
                appointmentScheduleModelDataService.exists(
                    queryEq {
                        where {
                            this.id.diff(appointmentScheduleToAdd.id) and
                                    this.personId.eq(appointmentScheduleToAdd.personId) and
                                    this.startTime.eq(appointmentScheduleToAdd.startTime) and
                                    this.status.eq(AppointmentScheduleStatus.SCHEDULED)
                        }
                    }
                )
            } returns false.success()
            coEvery { appointmentScheduleModelDataService.add(appointmentScheduleToAdd.toModel()) } returns 
                    appointmentScheduleToAdd.toModel().success()
            coEvery {
                appointmentScheduleModelDataService.findOne(
                    queryEq {
                        where {
                            this.staffId.eq(staff.id) and
                                    this.startTime.eq(appointmentScheduleEvent.startTime) and
                                    this.personId.eq(person.id)
                        }
                    }
                )
            } returns NotFoundException().failure()
            coEvery {
                appointmentScheduleModelDataService.find(
                    queryEq {
                        where {
                            this.staffId.eq(staff.id) and
                                    this.status.inList(listOf(AppointmentScheduleStatus.SCHEDULED)) and
                                    (this.startTime.greaterEq(fromDate) and this.startTime.lessEq(toDate))
                        }.orderBy { startTime }
                            .sortOrder { desc }
                    }
                )
            } returns emptyList<AppointmentScheduleModel>().success()
            coEvery {
                kafkaProducerService.produce(
                    AppointmentScheduleCreatedEvent(
                        person = person,
                        appointmentSchedule = appointmentScheduleToAdd,
                        isUsingInternalScheduler = false,
                        professional = Professional.from(staff)
                    )
                )
            } returns mockk()

            mockRangeUuidAndDateTime(appointmentScheduleToAddId, baseDate) {
                val result = appointmentScheduleService.schedule(person, appointmentScheduleEvent)
                assertThat(result).isSuccessWithData(listOf(appointmentScheduleToAdd))
            }

            coVerifyOnce { healthResourcesStore.getHealthPlanTaskByPerson(any()) }
            coVerifyOnce { healthResourcesStore.findStaffByEmail(any()) }
            coVerifyOnce { healthResourcesStore.getHealthCareTeamByStaffId(any()) }
            coVerifyOnce { appointmentScheduleModelDataService.exists(any()) }
            coVerifyOnce { appointmentScheduleModelDataService.add(any()) }
            coVerifyOnce { appointmentScheduleModelDataService.findOne(any()) }
            coVerifyOnce { appointmentScheduleModelDataService.find(any()) }
            coVerifyOnce { kafkaProducerService.produce(any()) }
        }

    @Test
    fun `#schedule should not send push if not added`() = runBlocking {
        val appointmentScheduleToAdd = appointmentScheduleToAdd.copy(scheduledByInternalScheduler = false)
        val fromDate = appointmentScheduled.startTime.minusDays(1)
        val toDate = appointmentScheduled.startTime.plusDays(1)

        coEvery { healthResourcesStore.findStaffByEmail(staff.email) } returns staff.success()
        coEvery { healthResourcesStore.getHealthCareTeamByStaffId(staff.id) } returns healthcareTeam.success()
        coEvery { healthResourcesStore.getHealthPlanTaskByPerson(person.id) } returns healthPlanTasks.success()
        coEvery {
            appointmentScheduleModelDataService.exists(
                queryEq {
                    where {
                        this.id.diff(appointmentScheduleToAdd.id) and
                                this.personId.eq(appointmentScheduleToAdd.personId) and
                                this.startTime.eq(appointmentScheduleToAdd.startTime) and
                                this.status.eq(AppointmentScheduleStatus.SCHEDULED)
                    }
                }
            )
        } returns false.success()
        coEvery { appointmentScheduleModelDataService.add(appointmentScheduleToAdd.toModel()) } returns Exception().failure()
        coEvery {
            appointmentScheduleModelDataService.findOne(
                queryEq {
                    where {
                        this.staffId.eq(staff.id) and
                                this.startTime.eq(appointmentScheduleEvent.startTime) and
                                this.personId.eq(person.id)
                    }
                }
            )
        } returns NotFoundException().failure()
        coEvery {
            appointmentScheduleModelDataService.find(
                queryEq {
                    where {
                        this.staffId.eq(staff.id) and
                                this.status.inList(listOf(AppointmentScheduleStatus.SCHEDULED)) and
                                (this.startTime.greaterEq(fromDate) and this.startTime.lessEq(toDate))
                    }.orderBy { startTime }
                        .sortOrder { desc }
                }
            )
        } returns emptyList<AppointmentScheduleModel>().success()

        mockRangeUuidAndDateTime(appointmentScheduleToAddId, baseDate) {
            val result = appointmentScheduleService.schedule(person, appointmentScheduleEvent)
            assertThat(result).isFailureOfType(Exception::class)
        }

        coVerifyOnce { healthResourcesStore.getHealthPlanTaskByPerson(any()) }
        coVerifyOnce { healthResourcesStore.findStaffByEmail(any()) }
        coVerifyOnce { healthResourcesStore.getHealthCareTeamByStaffId(any()) }
        coVerifyOnce { appointmentScheduleModelDataService.exists(any()) }
        coVerifyOnce { appointmentScheduleModelDataService.add(any()) }
        coVerifyOnce { appointmentScheduleModelDataService.findOne(any()) }
        coVerifyOnce { appointmentScheduleModelDataService.find(any()) }
    }

    @Test
    fun `#schedule should create appointment with referenced links of context id`() = runBlocking {
        val eventName = "Avaliação Física"
        val scheduleScreeningContextID = RangeUUID.generate()
        val referencedLink = ReferencedLink(
            scheduleScreeningContextID,
            ReferenceLinkModel.SCHEDULE_SCREENING_CONTEXT_ID
        )
        val appointmentScheduleEvent = appointmentScheduleEvent.copy(
            eventName = eventName,
            referencedLinks = listOf(referencedLink),
            location = schedulePreference.zoomLink
        )
        val appointmentScheduleToAdd = appointmentScheduleToAdd.copy(
            eventName = eventName,
            type = AppointmentScheduleType.OTHER,
            referencedLinks = listOf(referencedLink),
            location = schedulePreference.zoomLink,
            scheduledByInternalScheduler = false
        )
        val fromDate = appointmentScheduled.startTime.minusDays(1)
        val toDate = appointmentScheduled.startTime.plusDays(1)

        coEvery { healthResourcesStore.findStaffByEmail(staff.email) } returns staff.success()
        coEvery { healthResourcesStore.getHealthCareTeamByStaffId(staff.id) } returns healthcareTeam.success()
        coEvery { healthResourcesStore.getHealthPlanTaskByPerson(person.id) } returns healthPlanTasks.success()
        coEvery {
            appointmentScheduleModelDataService.exists(
                queryEq {
                    where {
                        this.id.diff(appointmentScheduleToAdd.id) and
                                this.personId.eq(appointmentScheduleToAdd.personId) and
                                this.startTime.eq(appointmentScheduleToAdd.startTime) and
                                this.status.eq(AppointmentScheduleStatus.SCHEDULED)
                    }
                }
            )
        } returns false.success()
        coEvery { appointmentScheduleModelDataService.add(appointmentScheduleToAdd.toModel()) } returns 
                appointmentScheduleToAdd.toModel().success()
        coEvery {
            appointmentScheduleModelDataService.findOne(
                queryEq {
                    where {
                        this.staffId.eq(staff.id) and
                                this.startTime.eq(appointmentScheduleEvent.startTime) and
                                this.personId.eq(person.id)
                    }
                }
            )
        } returns NotFoundException().failure()
        coEvery {
            appointmentScheduleModelDataService.find(
                queryEq {
                    where {
                        this.staffId.eq(staff.id) and
                                this.status.inList(listOf(AppointmentScheduleStatus.SCHEDULED)) and
                                (this.startTime.greaterEq(fromDate) and this.startTime.lessEq(toDate))
                    }.orderBy { startTime }
                        .sortOrder { desc }
                }
            )
        } returns emptyList<AppointmentScheduleModel>().success()
        coEvery {
            kafkaProducerService.produce(
                AppointmentScheduleCreatedEvent(
                    person = person,
                    appointmentSchedule = appointmentScheduleToAdd,
                    isUsingInternalScheduler = false,
                    professional = Professional.from(staff)
                )
            )
        } returns mockk()

        mockRangeUuidAndDateTime(appointmentScheduleToAddId, baseDate) {
            val result = appointmentScheduleService.schedule(
                person,
                appointmentScheduleEvent
            )
            assertThat(result).isSuccessWithData(listOf(appointmentScheduleToAdd))
        }

        coVerifyOnce { healthResourcesStore.getHealthPlanTaskByPerson(any()) }
        coVerifyOnce { healthResourcesStore.findStaffByEmail(any()) }
        coVerifyOnce { healthResourcesStore.getHealthCareTeamByStaffId(any()) }
        coVerifyOnce { appointmentScheduleModelDataService.exists(any()) }
        coVerifyOnce { appointmentScheduleModelDataService.add(any()) }
        coVerifyOnce { appointmentScheduleModelDataService.findOne(any()) }
        coVerifyOnce { appointmentScheduleModelDataService.find(any()) }
        coVerifyOnce { kafkaProducerService.produce(any()) }
    }


    @Test
    fun `#cancelByEvent should do nothing to a appointment schedule not found`() = runBlocking {
        val appointmentScheduleEvent = buildAppointmentScheduleRequestEvent("LAB")
        val appointmentCanceled =
            TestModelFactory.buildAppointmentSchedule(
                personId = person.id,
                status = AppointmentScheduleStatus.CANCELED
            )

        coEvery {
            appointmentScheduleModelDataService.findOne(
                queryEq { where { this.eventId.eq(appointmentCanceled.eventId!!) } })
        } returns NotFoundException().failure()

        val result = appointmentScheduleService.cancelByEvent(appointmentScheduleEvent)
        assertThat(result).isFailureOfType(NotFoundException::class)

        coVerifyOnce { appointmentScheduleModelDataService.findOne(any()) }
    }

    @Test
    fun `#complete should return failure when a schedule does not exist`() = runBlocking {
        val scheduleId = RangeUUID.generate()
        coEvery { appointmentScheduleModelDataService.get(scheduleId) } returns NotFoundException().failure()

        val result = appointmentScheduleService.complete(scheduleId)
        assertThat(result).isFailureOfType(NotFoundException::class)

        coVerifyOnce { appointmentScheduleModelDataService.get(any()) }
    }

    @Test
    fun `#complete should return failure when a schedule is canceled`() = runBlocking {
        val scheduleId = RangeUUID.generate()
        val canceled = TestModelFactory.buildAppointmentSchedule(status = AppointmentScheduleStatus.CANCELED)

        coEvery { appointmentScheduleModelDataService.get(scheduleId) } returns canceled.toModel().success()

        val result = appointmentScheduleService.complete(scheduleId)
        assertThat(result).isFailureOfType(CanceledOrTimedOutScheduleException::class)

        coVerifyOnce { appointmentScheduleModelDataService.get(any()) }
    }

    @Test
    fun `#complete should return success when a appointments schedule is already completed`() = runBlocking {
        val scheduleId = RangeUUID.generate()
        val completed = TestModelFactory.buildAppointmentSchedule(status = AppointmentScheduleStatus.COMPLETED)

        coEvery { appointmentScheduleModelDataService.get(scheduleId) } returns completed.toModel().success()

        val result = appointmentScheduleService.complete(scheduleId)
        assertThat(result).isSuccessWithData(completed)

        coVerifyOnce { appointmentScheduleModelDataService.get(any()) }
    }

    @Test
    fun `#complete should update appointment schedule, publish notification and return success when is not completed`() =
        runBlocking {
            val scheduleId = RangeUUID.generate()
            val appointmentSchedule =
                TestModelFactory.buildAppointmentSchedule(status = AppointmentScheduleStatus.SCHEDULED)

            val completedAppointmentSchedule = appointmentSchedule.complete()

            coEvery { appointmentScheduleModelDataService.get(scheduleId) } returns appointmentSchedule.toModel().success()
            coEvery { appointmentScheduleModelDataService.update(completedAppointmentSchedule.toModel()) } returns
                    completedAppointmentSchedule.toModel().success()
            coEvery { kafkaProducerService.produce(AppointmentScheduleCompletedEvent(completedAppointmentSchedule)) } returns mockk()

            val result = appointmentScheduleService.complete(scheduleId)
            assertThat(result).isSuccessWithData(completedAppointmentSchedule)

            coVerifyOnce { appointmentScheduleModelDataService.get(any()) }
            coVerifyOnce { appointmentScheduleModelDataService.update(any()) }
            coVerifyOnce { kafkaProducerService.produce(any()) }
        }

    @Test
    fun `#markAsNoShow should return exception when a schedule is not found`() = runBlocking {
        val scheduleId = RangeUUID.generate()
        coEvery { appointmentScheduleModelDataService.get(scheduleId) } returns NotFoundException().failure()

        val result = appointmentScheduleService.markAsNoShow(scheduleId)
        assertThat(result).isFailureOfType(NotFoundException::class)

        coVerifyOnce { appointmentScheduleModelDataService.get(any()) }
    }

    @Test
    fun `#markAsNoShow should return success when a schedule was no_show`() = runBlocking {
        val scheduleId = RangeUUID.generate()
        val appointmentSchedule = TestModelFactory.buildAppointmentSchedule(status = AppointmentScheduleStatus.NO_SHOW)

        coEvery { appointmentScheduleModelDataService.get(scheduleId) } returns appointmentSchedule.toModel().success()

        val result = appointmentScheduleService.markAsNoShow(scheduleId)
        assertThat(result).isSuccessWithData(appointmentSchedule)

        coVerifyOnce { appointmentScheduleModelDataService.get(any()) }
    }

    @Test
    fun `#markAsNoShow should update to no show when a appointment is scheduled`() = runBlocking {
        val scheduleId = RangeUUID.generate()
        val scheduled = TestModelFactory.buildAppointmentSchedule()
            .copy(startTime = LocalDateTime.now().minusHours(5))
        val scheduledNoShow = scheduled.copy(status = AppointmentScheduleStatus.NO_SHOW)

        coEvery { appointmentScheduleModelDataService.get(scheduleId) } returns scheduled.toModel().success()
        coEvery { appointmentScheduleModelDataService.update(scheduledNoShow.toModel()) } returns scheduledNoShow.toModel().success()
        coEvery { kafkaProducerService.produce(AppointmentScheduleNoShowEvent(scheduledNoShow)) } returns mockk()

        val result = appointmentScheduleService.markAsNoShow(scheduleId)
        assertThat(result).isSuccessWithData(scheduledNoShow)

        coVerifyOnce { appointmentScheduleModelDataService.get(any()) }
        coVerifyOnce { appointmentScheduleModelDataService.update(any()) }
        coVerifyOnce { kafkaProducerService.produce(any()) }
    }

    @Test
    fun `#markAsNoShow should not update when an schedule was canceled`() = runBlocking {
        val scheduleId = RangeUUID.generate()
        val canceled = TestModelFactory.buildAppointmentSchedule(status = AppointmentScheduleStatus.CANCELED)

        coEvery { appointmentScheduleModelDataService.get(scheduleId) } returns canceled.toModel().success()

        val result = appointmentScheduleService.markAsNoShow(scheduleId)
        assertThat(result).isFailureOfType(CanceledOrTimedOutScheduleException::class)

        coVerifyOnce { appointmentScheduleModelDataService.get(any()) }
    }

    @Test
    fun `#markAsNoShow should return error when fails on update`() = runBlocking {
        val scheduleId = RangeUUID.generate()
        val scheduled = TestModelFactory.buildAppointmentSchedule()
            .copy(startTime = LocalDateTime.now().minusHours(5))
        val scheduledNoShow = scheduled.copy(status = AppointmentScheduleStatus.NO_SHOW)

        coEvery { appointmentScheduleModelDataService.get(scheduleId) } returns scheduled.toModel().success()
        coEvery { appointmentScheduleModelDataService.update(scheduledNoShow.toModel()) } returns DatabaseException().failure()

        val result = appointmentScheduleService.markAsNoShow(scheduleId)
        assertThat(result).isFailureOfType(DatabaseException::class)

        coVerifyOnce { appointmentScheduleModelDataService.get(any()) }
        coVerifyOnce { appointmentScheduleModelDataService.update(any()) }
    }

    @Test
    fun `#getByHealthPlanTask should return schedule`() = runBlocking {
        val scheduled = TestModelFactory.buildAppointmentSchedule()
        val healthPlanTask = TestModelFactory.buildHealthPlanTask(personId = person.id)

        coEvery {
            appointmentScheduleModelDataService.findOne(
                queryEq { where { this.healthPlanTaskId.eq(healthPlanTask.id) } })
        } returns scheduled.toModel().success()

        val result = appointmentScheduleService.getByHealthPlanTask(healthPlanTask.id)
        assertThat(result).isSuccessWithData(scheduled)

        coVerifyOnce { appointmentScheduleModelDataService.findOne(any()) }
    }

    @Test
    fun `#hasWithScheduledStatusByHealthPlanTask should return Boolean`() = runBlocking {
        val healthPlanTask = TestModelFactory.buildHealthPlanTask(personId = person.id)

        coEvery {
            appointmentScheduleModelDataService.exists(
                queryEq {
                    where {
                        this.healthPlanTaskId.eq(healthPlanTask.id)
                            .and(this.status.eq(AppointmentScheduleStatus.SCHEDULED))
                    }
                }
            )
        } returns true.success()

        val result = appointmentScheduleService.hasWithScheduledStatusByHealthPlanTask(healthPlanTask.id)
        assertThat(result).isSuccessWithData(true)

        coVerifyOnce { appointmentScheduleModelDataService.exists(any()) }
    }

    @Test
    fun `#schedule should not create a new appointment schedule if it has already been added and change its status`() =
        runBlocking {
            val eventName = "Imersão"
            val appointmentScheduleEvent = appointmentScheduleEvent.copy(eventName = eventName)
            val appointmentScheduledToUpdate = appointmentScheduledToUpdate.copy(
                eventName = eventName,
                type = AppointmentScheduleType.IMMERSION,
                scheduledByInternalScheduler = false
            )
            val fromDate = appointmentScheduled.startTime.minusDays(1)
            val toDate = appointmentScheduled.startTime.plusDays(1)

            coEvery { healthResourcesStore.findStaffByEmail(staff.email) } returns staff.success()
            coEvery { healthResourcesStore.getHealthCareTeamByStaffId(staff.id) } returns healthcareTeam.success()
            coEvery { healthResourcesStore.getHealthPlanTaskByPerson(person.id) } returns healthPlanTasks.success()
            coEvery {
                appointmentScheduleModelDataService.findOne(
                    queryEq {
                        where {
                            this.staffId.eq(staff.id) and
                                    this.startTime.eq(appointmentScheduled.startTime) and
                                    this.personId.eq(person.id)
                        }
                    }
                )
            } returns appointmentScheduled.toModel().success()
            coEvery {
                appointmentScheduleModelDataService.exists(
                    queryEq {
                        where {
                            this.id.diff(appointmentScheduledToUpdate.id) and
                                    this.personId.eq(appointmentScheduledToUpdate.personId) and
                                    this.startTime.eq(appointmentScheduledToUpdate.startTime) and
                                    this.status.eq(AppointmentScheduleStatus.SCHEDULED)
                        }
                    }
                )
            } returns false.success()
            coEvery { appointmentScheduleModelDataService.update(appointmentScheduledToUpdate.toModel()) } returns
                    appointmentScheduledToUpdate.toModel().success()
            coEvery {
                appointmentScheduleModelDataService.find(
                    queryEq {
                        where {
                            this.staffId.eq(staff.id) and
                                    this.status.inList(listOf(AppointmentScheduleStatus.SCHEDULED)) and
                                    (this.startTime.greaterEq(fromDate) and this.startTime.lessEq(toDate))
                        }.orderBy { startTime }
                            .sortOrder { desc }
                    }
                )
            } returns emptyList<AppointmentScheduleModel>().success()
            coEvery {
                kafkaProducerService.produce(
                    AppointmentScheduleCreatedEvent(
                        person = person,
                        appointmentSchedule = appointmentScheduledToUpdate,
                        isUsingInternalScheduler = false,
                        professional = Professional.from(staff)
                    )
                )
            } returns mockk()

            mockRangeUuidAndDateTime(appointmentScheduleToAddId, baseDate) {
                val result = appointmentScheduleService.schedule(person, appointmentScheduleEvent)
                assertThat(result).isSuccessWithData(listOf(appointmentScheduledToUpdate))
            }

            coVerifyOnce { healthResourcesStore.findStaffByEmail(any()) }
            coVerifyOnce { healthResourcesStore.getHealthCareTeamByStaffId(any()) }
            coVerifyOnce { healthResourcesStore.getHealthPlanTaskByPerson(any()) }
            coVerifyOnce { appointmentScheduleModelDataService.exists(any()) }
            coVerifyOnce { appointmentScheduleModelDataService.update(any()) }
            coVerifyOnce { appointmentScheduleModelDataService.findOne(any()) }
            coVerifyOnce { appointmentScheduleModelDataService.find(any()) }
            coVerifyOnce { kafkaProducerService.produce(any()) }
        }

    @Test
    fun `#getAppointmentScheduleByPersonAndStaffId returns first appointment found`() = runBlocking {
        val date = LocalDate.now()
        val appointmentSchedule = TestModelFactory.buildAppointmentSchedule()

        coEvery {
            appointmentScheduleModelDataService.findOne(
                queryEq {
                    where {
                        this.personId.eq(appointmentSchedule.personId) and
                                this.staffId.eq(staff.id) and
                                this.startTime.greaterEq(date.atStartOfDay()) and
                                this.startTime.lessEq(date.atEndOfTheDay()) and
                                status.eq(AppointmentScheduleStatus.SCHEDULED)
                    }
                }
            )
        } returns appointmentSchedule.toModel().success()

        val result = appointmentScheduleService.getAppointmentScheduleByPersonAndStaffId(
            personId = appointmentSchedule.personId,
            staffId = staff.id,
            date = date
        )
        assertThat(result).isSuccessWithData(appointmentSchedule)

        coVerifyOnce { appointmentScheduleModelDataService.findOne(any()) }
    }

    @Test
    fun `#getAppointmentScheduleByPersonAndStaffId returns notFoundException if has no schedules`() = runBlocking {
        val date = LocalDate.now()
        val appointmentSchedule = TestModelFactory.buildAppointmentSchedule()

        coEvery {
            appointmentScheduleModelDataService.findOne(
                queryEq {
                    where {
                        this.personId.eq(appointmentSchedule.personId) and
                                this.staffId.eq(staff.id) and
                                this.startTime.greaterEq(date.atStartOfDay()) and
                                this.startTime.lessEq(date.atEndOfTheDay()) and
                                status.eq(AppointmentScheduleStatus.SCHEDULED)
                    }
                }
            )
        } returns NotFoundException().failure()

        val result = appointmentScheduleService.getAppointmentScheduleByPersonAndStaffId(
            personId = appointmentSchedule.personId,
            staffId = staff.id,
            date = date
        )
        assertThat(result).isFailureOfType(NotFoundException::class)

        coVerifyOnce { appointmentScheduleModelDataService.findOne(any()) }
    }

    @Test
    fun `#reschedule should cancel event and create new one`(): Unit = runBlocking {
        val appointment = TestModelFactory.buildAppointmentSchedule(
            personId = person.id,
            status = AppointmentScheduleStatus.SCHEDULED,
            staffId = staff.id,
            taskId = taskUUID,
        )
        val appointmentCanceled = appointment.cancel()
            .copy(cancelledByType = AppointmentScheduleCancelledByType.RESCHEDULE)
        val fromDate = appointmentScheduled.startTime.minusDays(1)
        val toDate = appointmentScheduled.startTime.plusDays(1)

        coEvery { appointmentScheduleModelDataService.get(appointment.id) } returns appointment.toModel().success()
        coEvery { appointmentScheduleModelDataService.update(appointmentCanceled.toModel()) } returns appointmentCanceled.toModel().success()
        coEvery { healthResourcesStore.getHealthPlanTaskByPerson(person.id) } returns healthPlanTasks.success()
        coEvery { healthResourcesStore.findStaffByEmail(staff.email) } returns staff.success()
        coEvery { healthResourcesStore.getHealthCareTeamByStaffId(staff.id) } returns healthcareTeam.success()
        coEvery {
            appointmentScheduleModelDataService.exists(
                queryEq {
                    where {
                        this.id.diff(appointmentScheduleToAdd.id) and
                                this.personId.eq(appointmentScheduleToAdd.personId) and
                                this.startTime.eq(appointmentScheduleToAdd.startTime) and
                                this.status.eq(AppointmentScheduleStatus.SCHEDULED)
                    }
                }
            )
        } returns false.success()
        coEvery { appointmentScheduleModelDataService.add(appointmentScheduleToAdd.toModel()) } returns
                appointmentScheduleToAdd.toModel().success()
        coEvery { personService.get(person.id) } returns person.success()
        coEvery {
            appointmentScheduleModelDataService.findOne(
                queryEq {
                    where {
                        this.staffId.eq(staff.id) and
                                this.startTime.eq(appointmentScheduleEvent.startTime) and
                                this.personId.eq(person.id)
                    }
                }
            )
        } returns NotFoundException().failure()
        coEvery {
            appointmentScheduleModelDataService.find(
                queryEq {
                    where {
                        this.staffId.eq(staff.id) and
                                this.status.inList(listOf(AppointmentScheduleStatus.SCHEDULED)) and
                                (this.startTime.greaterEq(fromDate) and this.startTime.lessEq(toDate))
                    }.orderBy { startTime }
                        .sortOrder { desc }
                }
            )
        } returns emptyList<AppointmentScheduleModel>().success()
        coEvery {
            kafkaProducerService.produce(InternalAppointmentScheduleCreatedEvent(appointmentScheduleToAdd))
        } returns mockk()
        coEvery { kafkaProducerService.produce(AppointmentScheduleCancelledEvent(appointmentCanceled)) } returns mockk()
        coEvery {
            kafkaProducerService.produce(
                AppointmentScheduleCreatedEvent(
                    person = person,
                    appointmentSchedule = appointmentScheduleToAdd,
                    isUsingInternalScheduler = true,
                    professional = Professional.from(staff)
                )
            )
        } returns mockk()

        mockRangeUuidAndDateTime(appointmentScheduleToAddId, baseDate) {
            val result = appointmentScheduleService.reschedule(person.id, appointmentScheduleEvent, appointment.id)
            assertThat(result).isSuccessWithData(listOf(appointmentScheduleToAdd))
        }

        coVerifyOnce { healthResourcesStore.findStaffByEmail(any()) }
        coVerifyOnce { healthResourcesStore.getHealthCareTeamByStaffId(any()) }
        coVerifyOnce { healthResourcesStore.getHealthPlanTaskByPerson(any()) }
        coVerifyOnce { appointmentScheduleModelDataService.get(any()) }
        coVerifyOnce { appointmentScheduleModelDataService.find(any()) }
        coVerifyOnce { appointmentScheduleModelDataService.findOne(any()) }
        coVerifyOnce { personService.get(any()) }
        coVerifyOnce { appointmentScheduleModelDataService.exists(any()) }
        coVerifyOnce { appointmentScheduleModelDataService.add(any()) }
        coVerifyOnce { appointmentScheduleModelDataService.update(any()) }
        coVerify(exactly = 3) { kafkaProducerService.produce(any()) }
    }

    @Test
    fun `#reschedule should cancel event and create new one, if there was already a canceled event, should delete previous one`(): Unit =
        runBlocking {
            val appointment = TestModelFactory.buildAppointmentSchedule(
                personId = person.id,
                status = AppointmentScheduleStatus.SCHEDULED,
                staffId = staff.id,
                taskId = taskUUID,
            )
            val appointmentCanceled =
                appointment.cancel().copy(cancelledByType = AppointmentScheduleCancelledByType.RESCHEDULE)

            val fromDate = appointmentScheduled.startTime.minusDays(1)
            val toDate = appointmentScheduled.startTime.plusDays(1)

            coEvery { appointmentScheduleModelDataService.get(appointment.id) } returns appointment.toModel().success()
            coEvery { appointmentScheduleModelDataService.update(appointmentCanceled.toModel()) } returns appointmentCanceled.toModel().success()
            coEvery { healthResourcesStore.getHealthPlanTaskByPerson(person.id) } returns healthPlanTasks.success()
            coEvery { healthResourcesStore.findStaffByEmail(staff.email) } returns staff.success()
            coEvery { healthResourcesStore.getHealthCareTeamByStaffId(staff.id) } returns healthcareTeam.success()
            coEvery {
                appointmentScheduleModelDataService.exists(
                    queryEq {
                        where {
                            this.id.diff(appointmentScheduleToAdd.id) and
                                    this.personId.eq(appointmentScheduleToAdd.personId) and
                                    this.startTime.eq(appointmentScheduleToAdd.startTime) and
                                    this.status.eq(AppointmentScheduleStatus.SCHEDULED)
                        }
                    }
                )
            } returns false.success()
            coEvery { appointmentScheduleModelDataService.add(appointmentScheduleToAdd.toModel()) } returns
                    appointmentScheduleToAdd.toModel().success()
            coEvery { personService.get(person.id) } returns person.success()
            coEvery {
                appointmentScheduleModelDataService.findOne(
                    queryEq {
                        where {
                            this.staffId.eq(staff.id) and
                                    this.startTime.eq(appointmentScheduleEvent.startTime) and
                                    this.personId.eq(person.id)
                        }
                    }
                )
            } returns NotFoundException().failure()
            coEvery {
                appointmentScheduleModelDataService.find(
                    queryEq {
                        where {
                            this.staffId.eq(staff.id) and
                                    this.status.inList(listOf(AppointmentScheduleStatus.SCHEDULED)) and
                                    (this.startTime.greaterEq(fromDate) and this.startTime.lessEq(toDate))
                        }.orderBy { startTime }
                            .sortOrder { desc }
                    }
                )
            } returns emptyList<AppointmentScheduleModel>().success()
            coEvery {
                kafkaProducerService.produce(InternalAppointmentScheduleCreatedEvent(appointmentScheduleToAdd))
            } returns mockk()
            coEvery { kafkaProducerService.produce(AppointmentScheduleCancelledEvent(appointmentCanceled)) } returns mockk()
            coEvery {
                kafkaProducerService.produce(
                    AppointmentScheduleCreatedEvent(
                        person = person,
                        appointmentSchedule = appointmentScheduleToAdd,
                        isUsingInternalScheduler = true,
                        professional = Professional.from(staff)
                    )
                )
            } returns mockk()

            mockRangeUuidAndDateTime(appointmentScheduleToAddId, baseDate) {
                val result = appointmentScheduleService.reschedule(person.id, appointmentScheduleEvent, appointment.id)
                assertThat(result).isSuccessWithData(listOf(appointmentScheduleToAdd))
            }

            coVerifyOnce { healthResourcesStore.findStaffByEmail(any()) }
            coVerifyOnce { healthResourcesStore.getHealthCareTeamByStaffId(any()) }
            coVerifyOnce { healthResourcesStore.getHealthPlanTaskByPerson(any()) }
            coVerifyOnce { appointmentScheduleModelDataService.get(any()) }
            coVerifyOnce { appointmentScheduleModelDataService.find(any()) }
            coVerifyOnce { appointmentScheduleModelDataService.findOne(any()) }
            coVerifyOnce { personService.get(any()) }
            coVerifyOnce { appointmentScheduleModelDataService.exists(any()) }
            coVerifyOnce { appointmentScheduleModelDataService.add(any()) }
            coVerifyOnce { appointmentScheduleModelDataService.update(any()) }
            coVerify(exactly = 3) { kafkaProducerService.produce(any()) }
        }

    @Test
    fun `#reschedule should use healthPlanTaskId of previous schedule`(): Unit = runBlocking {
        val appointment = TestModelFactory.buildAppointmentSchedule(
            personId = person.id,
            status = AppointmentScheduleStatus.SCHEDULED,
            staffId = staff.id,
            taskId = taskUUID,
            appointmentScheduleEventTypeId = appointmentScheduleEventType.id
        )
        val appointmentCanceled =
            appointment.cancel().copy(cancelledByType = AppointmentScheduleCancelledByType.RESCHEDULE)
        val fromDate = appointmentScheduled.startTime.minusDays(1)
        val toDate = appointmentScheduled.startTime.plusDays(1)

        coEvery { appointmentScheduleModelDataService.get(appointment.id) } returns appointmentCanceled.toModel().success()
        coEvery { healthResourcesStore.getHealthPlanTaskByPerson(person.id) } returns healthPlanTasks.success()
        coEvery { healthResourcesStore.findStaffByEmail(staff.email) } returns staff.success()
        coEvery { healthResourcesStore.getHealthCareTeamByStaffId(staff.id) } returns healthcareTeam.success()
        coEvery {
            appointmentScheduleModelDataService.exists(
                queryEq {
                    where {
                        this.id.diff(appointmentScheduleToAdd.id) and
                                this.personId.eq(appointmentScheduleToAdd.personId) and
                                this.startTime.eq(appointmentScheduleToAdd.startTime) and
                                this.status.eq(AppointmentScheduleStatus.SCHEDULED)
                    }
                }
            )
        } returns false.success()
        coEvery { appointmentScheduleModelDataService.add(appointmentScheduleToAdd.toModel()) } returns
                appointmentScheduleToAdd.toModel().success()
        coEvery { personService.get(person.id) } returns person.success()
        coEvery {
            appointmentScheduleModelDataService.findOne(
                queryEq {
                    where {
                        this.staffId.eq(staff.id) and
                                this.startTime.eq(appointmentScheduled.startTime) and
                                this.personId.eq(person.id)
                    }
                }
            )
        } returns NotFoundException().failure()
        coEvery {
            appointmentScheduleModelDataService.find(
                queryEq {
                    where {
                        this.staffId.eq(staff.id) and
                                this.status.inList(listOf(AppointmentScheduleStatus.SCHEDULED)) and
                                (this.startTime.greaterEq(fromDate) and this.startTime.lessEq(toDate))
                    }.orderBy { startTime }
                        .sortOrder { desc }
                }
            )
        } returns emptyList<AppointmentScheduleModel>().success()

        coEvery {
            kafkaProducerService.produce(InternalAppointmentScheduleCreatedEvent(appointmentScheduleToAdd))
        } returns mockk()
        coEvery {
            kafkaProducerService.produce(
                AppointmentScheduleCreatedEvent(
                    person = person,
                    appointmentSchedule = appointmentScheduleToAdd,
                    isUsingInternalScheduler = true,
                    professional = Professional.from(staff)
                )
            )
        } returns mockk()

        mockRangeUuidAndDateTime(appointmentScheduleToAddId, baseDate) {
            val result = appointmentScheduleService.reschedule(person.id, appointmentScheduleEvent, appointment.id)
            assertThat(result).isSuccessWithData(listOf(appointmentScheduleToAdd))
        }

        coVerifyOnce { healthResourcesStore.getHealthPlanTaskByPerson(any()) }
        coVerifyOnce { healthResourcesStore.findStaffByEmail(any()) }
        coVerifyOnce { healthResourcesStore.getHealthCareTeamByStaffId(any()) }
        coVerifyOnce { appointmentScheduleModelDataService.get(any()) }
        coVerifyOnce { appointmentScheduleModelDataService.exists(any()) }
        coVerifyOnce { appointmentScheduleModelDataService.add(any()) }
        coVerifyOnce { personService.get(any()) }
        coVerifyOnce { appointmentScheduleModelDataService.findOne(any()) }
        coVerifyOnce { appointmentScheduleModelDataService.find(any()) }
        coVerify(exactly = 2) { kafkaProducerService.produce(any()) }
    }

    @Test
    fun `#reschedule should use return empty list when appointment to be cancelled is not found`(): Unit = runBlocking {
        val appointment = TestModelFactory.buildAppointmentSchedule(
            personId = person.id,
            status = AppointmentScheduleStatus.SCHEDULED,
            staffId = staff.id,
            taskId = taskUUID,
            appointmentScheduleEventTypeId = appointmentScheduleEventType.id
        )
        val appointmentScheduleEvent = buildAppointmentScheduleRequestEvent(
            "LAB", taskUUID.toString().toSha256(), appointmentScheduleEventTypeId = appointmentScheduleEventType.id
        )

        coEvery { appointmentScheduleModelDataService.get(appointment.id) } returns NotFoundException().failure()

        val result = appointmentScheduleService.reschedule(person.id, appointmentScheduleEvent, appointment.id)
        assertThat(result).isSuccessWithData(emptyList())

        coVerifyOnce { appointmentScheduleModelDataService.get(any()) }
        coVerify { appointmentScheduleEventTypeService wasNot called }
        coVerify { eventTypeProviderUnitService wasNot called }
        coVerify { personService wasNot called }
        coVerify { kafkaProducerService wasNot called }
    }

    @Test
    fun `#scheduleInternal should not schedule a new appointment if there is collision`() = runBlocking {

        val collidingAppointmentSchedule = TestModelFactory.buildAppointmentSchedule(
            startTime = appointmentScheduleEvent.startTime.minusHours(1),
            endTime = appointmentScheduleEvent.startTime.plusMinutes(1)
        )
        val fromDate = appointmentScheduled.startTime.minusDays(1)
        val toDate = appointmentScheduled.startTime.plusDays(1)

        coEvery { healthResourcesStore.findStaffByEmail(staff.email) } returns staff.success()
        coEvery { personService.get(person.id) } returns person.success()
        coEvery {
            kafkaProducerService.produce(AppointmentScheduleCollisionDetectedEvent(appointmentScheduleEvent, person.id))
        } returns mockk()

        coEvery {
            appointmentScheduleModelDataService.find(
                queryEq {
                    where {
                        this.staffId.eq(staff.id) and
                                this.status.inList(listOf(AppointmentScheduleStatus.SCHEDULED)) and
                                (this.startTime.greaterEq(fromDate) and this.startTime.lessEq(toDate))
                    }.orderBy { startTime }
                        .sortOrder { desc }
                }
            )
        } returns listOf(collidingAppointmentSchedule.toModel()).success()


        val result =
            appointmentScheduleService.scheduleInternal(person.id, appointmentScheduleEvent, numberOfSessions = 1)
        assertThat(result).isFailureOfType(AppointmentScheduleCollisionDetectedException::class)

        coVerifyOnce { healthResourcesStore.findStaffByEmail(any()) }
        coVerifyOnce { personService.get(any()) }
        coVerifyOnce { kafkaProducerService.produce(any()) }
        coVerifyOnce { appointmentScheduleModelDataService.find(any()) }

    }

    @Test
    fun `#scheduleInternal should not schedule a new appointment if there is collision for the same person and not produce kafka event`() =
        runBlocking {

            val collidingAppointmentSchedule = TestModelFactory.buildAppointmentSchedule(
                startTime = appointmentScheduleEvent.startTime.minusHours(1),
                endTime = appointmentScheduleEvent.startTime.plusMinutes(1),
                personId = person.id,
            )
            val fromDate = appointmentScheduled.startTime.minusDays(1)
            val toDate = appointmentScheduled.startTime.plusDays(1)

            coEvery { healthResourcesStore.findStaffByEmail(staff.email) } returns staff.success()
            coEvery { personService.get(person.id) } returns person.success()

            coEvery {
                appointmentScheduleModelDataService.find(
                    queryEq {
                        where {
                            this.staffId.eq(staff.id) and
                                    this.status.inList(listOf(AppointmentScheduleStatus.SCHEDULED)) and
                                    (this.startTime.greaterEq(fromDate) and this.startTime.lessEq(toDate))
                        }.orderBy { startTime }
                            .sortOrder { desc }
                    }
                )
            } returns listOf(collidingAppointmentSchedule.toModel()).success()

            val result =
                appointmentScheduleService.scheduleInternal(person.id, appointmentScheduleEvent, numberOfSessions = 1)
            assertThat(result).isFailureOfType(AppointmentScheduleCollisionDetectedException::class)

            coVerifyOnce { healthResourcesStore.findStaffByEmail(any()) }
            coVerifyOnce { personService.get(any()) }
            coVerifyOnce { appointmentScheduleModelDataService.find(any()) }
        }

    @Test
    fun `#scheduleInternal should schedule a new appointment with generated zoom link if staff has zoom refresh token`() =
        runBlocking {
            val fromDate = appointmentScheduled.startTime.minusDays(1)
            val toDate = appointmentScheduled.startTime.plusDays(1)

            coEvery { healthResourcesStore.getHealthPlanTaskByPerson(person.id) } returns healthPlanTasks.success()
            coEvery { healthResourcesStore.findStaffByEmail(staff.email) } returns staff.success()
            coEvery { healthResourcesStore.getHealthCareTeamByStaffId(staff.id) } returns healthcareTeam.success()
            coEvery {
                appointmentScheduleModelDataService.exists(
                    queryEq {
                        where {
                            this.id.diff(appointmentScheduleToAdd.id) and
                                    this.personId.eq(appointmentScheduleToAdd.personId) and
                                    this.startTime.eq(appointmentScheduleToAdd.startTime) and
                                    this.status.eq(AppointmentScheduleStatus.SCHEDULED)
                        }
                    }
                )
            } returns false.success()
            coEvery { appointmentScheduleModelDataService.add(appointmentScheduleToAdd.toModel()) } returns
                    appointmentScheduleToAdd.toModel().success()
            coEvery { personService.get(person.id) } returns person.success()
            coEvery {
                appointmentScheduleModelDataService.findOne(
                    queryEq {
                        where {
                            this.staffId.eq(staff.id) and
                                    this.startTime.eq(appointmentScheduleEvent.startTime) and
                                    this.personId.eq(person.id)
                        }
                    }
                )
            } returns NotFoundException().failure()
            coEvery {
                appointmentScheduleModelDataService.find(
                    queryEq {
                        where {
                            this.staffId.eq(staff.id) and
                                    this.status.inList(listOf(AppointmentScheduleStatus.SCHEDULED)) and
                                    (this.startTime.greaterEq(fromDate) and this.startTime.lessEq(toDate))
                        }.orderBy { startTime }
                            .sortOrder { desc }
                    }
                )
            } returns emptyList<AppointmentScheduleModel>().success()
            coEvery {
                kafkaProducerService.produce(InternalAppointmentScheduleCreatedEvent(appointmentScheduleToAdd))
            } returns mockk()
            coEvery {
                kafkaProducerService.produce(
                    AppointmentScheduleCreatedEvent(
                        person = person,
                        appointmentSchedule = appointmentScheduleToAdd,
                        isUsingInternalScheduler = true,
                        professional = Professional.from(staff)
                    )
                )
            } returns mockk()

            mockRangeUuidAndDateTime(appointmentScheduleToAddId, baseDate) {
                val result = appointmentScheduleService.scheduleInternal(
                    person.id,
                    appointmentScheduleEvent,
                    numberOfSessions = 1
                )
                assertThat(result).isSuccessWithData(listOf(appointmentScheduleToAdd))
            }

            coVerifyOnce { healthResourcesStore.getHealthPlanTaskByPerson(any()) }
            coVerifyOnce { healthResourcesStore.findStaffByEmail(any()) }
            coVerifyOnce { healthResourcesStore.getHealthCareTeamByStaffId(any()) }
            coVerifyOnce { appointmentScheduleModelDataService.exists(any()) }
            coVerifyOnce { appointmentScheduleModelDataService.add(any()) }
            coVerifyOnce { personService.get(any()) }
            coVerifyOnce { appointmentScheduleModelDataService.findOne(any()) }
            coVerifyOnce { appointmentScheduleModelDataService.find(any()) }
            coVerify(exactly = 2) { kafkaProducerService.produce(any()) }
        }

    @Test
    fun `#scheduleInternal should schedule a new appointment with event type coming from appointment schedule event type of payload`() =
        runBlocking {
            val appointmentScheduleEvent = appointmentScheduleEvent.copy(
                endTime = null,
                appointmentScheduleEventTypeId = appointmentScheduleEventType.id
            )
            val appointmentScheduleToAdd = appointmentScheduleToAdd.copy(
                eventName = "Ale Carreiro",
                endTime = startTime.plusMinutes(30),
                type = AppointmentScheduleType.HOME_TEST,
                appointmentScheduleEventTypeId = appointmentScheduleEventType.id
            )

            coEvery { healthResourcesStore.getHealthPlanTaskByPerson(person.id) } returns healthPlanTasks.success()
            coEvery { healthResourcesStore.findStaffByEmail(staff.email) } returns staff.success()
            coEvery { healthResourcesStore.getHealthCareTeamByStaffId(staff.id) } returns healthcareTeam.success()
            coEvery {
                appointmentScheduleModelDataService.exists(
                    queryEq {
                        where {
                            this.id.diff(appointmentScheduleToAdd.id) and
                                    this.personId.eq(appointmentScheduleToAdd.personId) and
                                    this.startTime.eq(appointmentScheduleToAdd.startTime) and
                                    this.status.eq(AppointmentScheduleStatus.SCHEDULED)
                        }
                    }
                )
            } returns false.success()
            coEvery { appointmentScheduleModelDataService.add(appointmentScheduleToAdd.toModel()) } returns
                    appointmentScheduleToAdd.toModel().success()
            coEvery { personService.get(person.id) } returns person.success()
            coEvery {
                appointmentScheduleModelDataService.findOne(
                    queryEq {
                        where {
                            this.staffId.eq(staff.id) and
                                    this.startTime.eq(appointmentScheduleEvent.startTime) and
                                    this.personId.eq(person.id)
                        }
                    }
                )
            } returns NotFoundException().failure()
            coEvery {
                appointmentScheduleEventTypeService.get(appointmentScheduleEventType.id)
            } returns appointmentScheduleEventType.success()
            coEvery {
                eventTypeProviderUnitService.getForEventType(appointmentScheduleEventType.id)
            } returns listOf(eventTypeProviderUnitDigital).success()
            coEvery {
                kafkaProducerService.produce(InternalAppointmentScheduleCreatedEvent(appointmentScheduleToAdd))
            } returns mockk()
            coEvery {
                kafkaProducerService.produce(
                    AppointmentScheduleCreatedEvent(
                        person = person,
                        appointmentSchedule = appointmentScheduleToAdd,
                        isUsingInternalScheduler = true,
                        professional = Professional.from(staff)
                    )
                )
            } returns mockk()

            mockRangeUuidAndDateTime(appointmentScheduleToAddId, baseDate) {
                val result = appointmentScheduleService.scheduleInternal(
                    person.id,
                    appointmentScheduleEvent,
                    numberOfSessions = 1
                )
                assertThat(result).isSuccessWithData(listOf(appointmentScheduleToAdd))
            }

            coVerifyOnce { healthResourcesStore.getHealthPlanTaskByPerson(any()) }
            coVerifyOnce { healthResourcesStore.findStaffByEmail(any()) }
            coVerifyOnce { healthResourcesStore.getHealthCareTeamByStaffId(any()) }
            coVerifyOnce { appointmentScheduleEventTypeService.get(any()) }
            coVerifyOnce { eventTypeProviderUnitService.getForEventType(any()) }
            coVerifyOnce { appointmentScheduleModelDataService.exists(any()) }
            coVerifyOnce { appointmentScheduleModelDataService.add(any()) }
            coVerifyOnce { personService.get(any()) }
            coVerifyOnce { appointmentScheduleModelDataService.findOne(any()) }
            coVerify(exactly = 2) { kafkaProducerService.produce(any()) }
        }

    @Test
    fun `#schedule should create a new appointment schedule with correct type and set it according event name to b2b full risk flow`() =
        runBlocking {
            val eventName = "Papo com Enfermeira Alice {B2B}"
            val appointmentScheduleEvent = appointmentScheduleEvent.copy(eventName = eventName)
            val appointmentScheduleToAdd = appointmentScheduleToAdd.copy(
                eventName = eventName,
                type = AppointmentScheduleType.HEALTH_DECLARATION,
                scheduledByInternalScheduler = false
            )
            val fromDate = appointmentScheduled.startTime.minusDays(1)
            val toDate = appointmentScheduled.startTime.plusDays(1)

            coEvery { healthResourcesStore.getHealthPlanTaskByPerson(person.id) } returns healthPlanTasks.success()
            coEvery { healthResourcesStore.findStaffByEmail(staff.email) } returns staff.success()
            coEvery { healthResourcesStore.getHealthCareTeamByStaffId(staff.id) } returns healthcareTeam.success()
            coEvery {
                appointmentScheduleModelDataService.exists(
                    queryEq {
                        where {
                            this.id.diff(appointmentScheduleToAdd.id) and
                                    this.personId.eq(appointmentScheduleToAdd.personId) and
                                    this.startTime.eq(appointmentScheduleToAdd.startTime) and
                                    this.status.eq(AppointmentScheduleStatus.SCHEDULED)
                        }
                    }
                )
            } returns false.success()
            coEvery { appointmentScheduleModelDataService.add(appointmentScheduleToAdd.toModel()) } returns
                    appointmentScheduleToAdd.toModel().success()
            coEvery {
                appointmentScheduleModelDataService.findOne(
                    queryEq {
                        where {
                            this.staffId.eq(staff.id) and
                                    this.startTime.eq(appointmentScheduleEvent.startTime) and
                                    this.personId.eq(person.id)
                        }
                    }
                )
            } returns NotFoundException().failure()
            coEvery {
                appointmentScheduleModelDataService.find(
                    queryEq {
                        where {
                            this.staffId.eq(staff.id) and
                                    this.status.inList(listOf(AppointmentScheduleStatus.SCHEDULED)) and
                                    (this.startTime.greaterEq(fromDate) and this.startTime.lessEq(toDate))
                        }.orderBy { startTime }
                            .sortOrder { desc }
                    }
                )
            } returns emptyList<AppointmentScheduleModel>().success()
            coEvery {
                kafkaProducerService.produce(
                    AppointmentScheduleCreatedEvent(
                        person = person,
                        appointmentSchedule = appointmentScheduleToAdd,
                        isUsingInternalScheduler = false,
                        professional = Professional.from(staff)
                    )
                )
            } returns mockk()

            mockRangeUuidAndDateTime(appointmentScheduleToAddId, baseDate) {
                val result = appointmentScheduleService.schedule(person, appointmentScheduleEvent)
                assertThat(result).isSuccessWithData(listOf(appointmentScheduleToAdd))
            }

            coVerifyOnce { healthResourcesStore.getHealthPlanTaskByPerson(any()) }
            coVerifyOnce { healthResourcesStore.findStaffByEmail(any()) }
            coVerifyOnce { healthResourcesStore.getHealthCareTeamByStaffId(any()) }
            coVerifyOnce { appointmentScheduleModelDataService.exists(any()) }
            coVerifyOnce { appointmentScheduleModelDataService.add(any()) }
            coVerifyOnce { appointmentScheduleModelDataService.findOne(any()) }
            coVerifyOnce { appointmentScheduleModelDataService.find(any()) }
            coVerifyOnce { kafkaProducerService.produce(any()) }
        }

    @Test
    fun `#schedule should create a new appointment schedule with correct type and set it according event name to b2b partial risk flow`() =
        runBlocking {
            val eventName = "Vamos falar de Coberturas? {B2B}"
            val appointmentScheduleEvent = appointmentScheduleEvent.copy(eventName = eventName)
            val appointmentScheduleToAdd = appointmentScheduleToAdd.copy(
                eventName = eventName,
                type = AppointmentScheduleType.HEALTH_DECLARATION,
                scheduledByInternalScheduler = false
            )
            val fromDate = appointmentScheduled.startTime.minusDays(1)
            val toDate = appointmentScheduled.startTime.plusDays(1)

            coEvery { healthResourcesStore.getHealthPlanTaskByPerson(person.id) } returns healthPlanTasks.success()
            coEvery { healthResourcesStore.findStaffByEmail(staff.email) } returns staff.success()
            coEvery { healthResourcesStore.getHealthCareTeamByStaffId(staff.id) } returns healthcareTeam.success()
            coEvery {
                appointmentScheduleModelDataService.exists(
                    queryEq {
                        where {
                            this.id.diff(appointmentScheduleToAdd.id) and
                                    this.personId.eq(appointmentScheduleToAdd.personId) and
                                    this.startTime.eq(appointmentScheduleToAdd.startTime) and
                                    this.status.eq(AppointmentScheduleStatus.SCHEDULED)
                        }
                    }
                )
            } returns false.success()
            coEvery { appointmentScheduleModelDataService.add(appointmentScheduleToAdd.toModel()) } returns
                    appointmentScheduleToAdd.toModel().success()
            coEvery {
                appointmentScheduleModelDataService.findOne(
                    queryEq {
                        where {
                            this.staffId.eq(staff.id) and
                                    this.startTime.eq(appointmentScheduleEvent.startTime) and
                                    this.personId.eq(person.id)
                        }
                    }
                )
            } returns NotFoundException().failure()
            coEvery {
                appointmentScheduleModelDataService.find(
                    queryEq {
                        where {
                            this.staffId.eq(staff.id) and
                                    this.status.inList(listOf(AppointmentScheduleStatus.SCHEDULED)) and
                                    (this.startTime.greaterEq(fromDate) and this.startTime.lessEq(toDate))
                        }.orderBy { startTime }
                            .sortOrder { desc }
                    }
                )
            } returns emptyList<AppointmentScheduleModel>().success()
            coEvery {
                kafkaProducerService.produce(
                    AppointmentScheduleCreatedEvent(
                        person = person,
                        appointmentSchedule = appointmentScheduleToAdd,
                        isUsingInternalScheduler = false,
                        professional = Professional.from(staff)
                    )
                )
            } returns mockk()

            mockRangeUuidAndDateTime(appointmentScheduleToAddId, baseDate) {
                val result = appointmentScheduleService.schedule(person, appointmentScheduleEvent)
                assertThat(result).isSuccessWithData(listOf(appointmentScheduleToAdd))
            }

            coVerifyOnce { healthResourcesStore.getHealthPlanTaskByPerson(any()) }
            coVerifyOnce { healthResourcesStore.findStaffByEmail(any()) }
            coVerifyOnce { healthResourcesStore.getHealthCareTeamByStaffId(any()) }
            coVerifyOnce { appointmentScheduleModelDataService.exists(any()) }
            coVerifyOnce { appointmentScheduleModelDataService.add(any()) }
            coVerifyOnce { appointmentScheduleModelDataService.findOne(any()) }
            coVerifyOnce { appointmentScheduleModelDataService.find(any()) }
            coVerifyOnce { kafkaProducerService.produce(any()) }
        }

    @Test
    fun `#countScheduledByPersonId should return total count of appointment schedules by given person id`(): Unit =
        runBlocking {
            coEvery {
                appointmentScheduleModelDataService.count(
                    queryEq {
                        where {
                            this.personId.eq(person.id) and
                                    this.status.eq(AppointmentScheduleStatus.SCHEDULED)
                        }
                    }
                )
            } returns 5.success()

            val result = appointmentScheduleService.countScheduledByPersonId(person.id)
            assertThat(result).isSuccessWithData(5)

            coVerifyOnce { appointmentScheduleModelDataService.count(any()) }
        }

    @Test
    fun `#countScheduledByPersonIdStaffIdInPeriod should return total count of appointment schedules by given person id staff id and period`(): Unit =
        runBlocking {
            val startDate = LocalDate.now()
            val endDate = startDate.plusDays(30)
            coEvery {
                appointmentScheduleModelDataService.count(
                    queryEq {
                        where {
                            this.status.eq(AppointmentScheduleStatus.SCHEDULED) and
                                    this.personId.eq(person.id) and
                                    this.staffId.eq(staff.id) and
                                    this.startTime.greaterEq(startDate.atStartOfDay()) and
                                    this.endTime.lessEq(endDate.atEndOfTheDay())
                        }
                    }
                )
            } returns 5.success()

            val result = appointmentScheduleService.countScheduledByPersonIdStaffIdInPeriod(
                person.id,
                staff.id,
                startDate,
                endDate,
            )
            assertThat(result).isSuccessWithData(5)

            coVerifyOnce { appointmentScheduleModelDataService.count(any()) }
        }

    @Test
    fun `#validateAppointmentScheduleCreation should return false if there is scheduled appointment schedule for person at the time`(): Unit =
        runBlocking {
            val endTime = startTime.plusMinutes(appointmentScheduleEventType.duration.toLong())
            val appointmentScheduled = appointmentScheduled.copy(startTime = startTime, endTime = endTime)
            val expected = mutableListOf(
                AppointmentScheduleCreationValidation(
                    startTime.toSaoPauloTimeZone(),
                    endTime.toSaoPauloTimeZone(),
                    AppointmentScheduleCreationValidationResult.PERSON_APPOINTMENT_SCHEDULE_COLLISION
                )
            )
            coEvery {
                appointmentScheduleEventTypeService.get(appointmentScheduleEventType.id)
            } returns appointmentScheduleEventType.success()

            coEvery {
                eventTypeProviderUnitService.getForEventType(appointmentScheduleEventType.id)
            } returns listOf(eventTypeProviderUnitDigital).success()

            coEvery {
                appointmentScheduleModelDataService.find(
                    queryEq {
                        where {
                            this.personId.eq(person.id) and
                                    this.status.inList(listOf(AppointmentScheduleStatus.SCHEDULED)) and
                                    (this.startTime.greaterEq(appointmentScheduled.startTime) and
                                            this.startTime.lessEq(
                                                appointmentScheduled.startTime.plusMinutes(
                                                    appointmentScheduleEventType.duration.toLong()
                                                )
                                            ))
                        }.orderBy { startTime }
                            .sortOrder { desc }
                    }
                )
            } returns listOf(appointmentScheduled.toModel()).success()

            coEvery {
                appointmentScheduleModelDataService.find(
                    queryEq {
                        where {
                            this.staffId.eq(staff.id) and
                                    this.status.inList(listOf(AppointmentScheduleStatus.SCHEDULED)) and
                                    (this.startTime.greaterEq(appointmentScheduled.startTime) and
                                            this.startTime.lessEq(
                                                appointmentScheduled.startTime.plusMinutes(
                                                    appointmentScheduleEventType.duration.toLong()
                                                )
                                            ))
                        }.orderBy { startTime }
                            .sortOrder { desc }
                    }
                )
            } returns emptyList<AppointmentScheduleModel>().success()

            coEvery {
                staffAvailabilityService.getSlotsAtSpecificDateTime(
                    staff.id,
                    startTime,
                    endTime,
                    appointmentScheduleEventType.id,
                    appointmentScheduleEventType,
                    null,
                    1
                )
            } returns listOf(
                SlotForSpecificDuration(
                    startTime,
                    endTime,
                    appointmentScheduleEventType.duration.toLong(),
                    emptyList(),
                    staff.id,
                )
            ).success()

            val result = appointmentScheduleService.validateAppointmentScheduleCreation(
                person.id,
                staff.id,
                startTime,
                appointmentScheduleEventType.id,
                numberOfSessions = 1
            )
            assertThat(result).isSuccessWithData(expected)

            coVerifyOnce { appointmentScheduleEventTypeService.get(any()) }
            coVerifyOnce { eventTypeProviderUnitService.getForEventType(any()) }
            coVerify(exactly = 2) { appointmentScheduleModelDataService.find(any()) }
            coVerifyOnce {
                staffAvailabilityService.getSlotsAtSpecificDateTime(
                    any(),
                    any(),
                    any(),
                    any(),
                    any(),
                    any(),
                    any()
                )
            }
        }

    @Test
    fun `#validateAppointmentScheduleCreation should return false if there is scheduled appointment schedule for person at the time for 2 sessions in time of one session`(): Unit =
        runBlocking {
            val varStartTime = startTime
            val endTime = startTime.plusMinutes(appointmentScheduleEventType.duration.toLong())
            val appointmentScheduled =
                appointmentScheduled.copy(startTime = startTime.plusWeeks(1), endTime = endTime.plusWeeks(1))
            val expected = mutableListOf(
                AppointmentScheduleCreationValidation(
                    startTime.toSaoPauloTimeZone(),
                    endTime.toSaoPauloTimeZone(),
                    AppointmentScheduleCreationValidationResult.CAN_CREATE
                ),
                AppointmentScheduleCreationValidation(
                    startTime.plusWeeks(1).toSaoPauloTimeZone(),
                    endTime.plusWeeks(1).toSaoPauloTimeZone(),
                    AppointmentScheduleCreationValidationResult.PERSON_APPOINTMENT_SCHEDULE_COLLISION
                )
            )
            coEvery {
                appointmentScheduleEventTypeService.get(appointmentScheduleEventType.id)
            } returns appointmentScheduleEventType.success()

            coEvery {
                eventTypeProviderUnitService.getForEventType(appointmentScheduleEventType.id)
            } returns listOf(eventTypeProviderUnitDigital).success()

            coEvery {
                appointmentScheduleModelDataService.find(
                    queryEq {
                        where {
                            this.personId.eq(person.id) and
                                    this.status.inList(listOf(AppointmentScheduleStatus.SCHEDULED)) and
                                    (this.startTime.greaterEq(varStartTime) and
                                            this.startTime.lessEq(
                                                varStartTime.plusWeeks(1)
                                                    .plusMinutes(appointmentScheduleEventType.duration.toLong())
                                            ))
                        }.orderBy { startTime }
                            .sortOrder { desc }
                    }
                )
            } returns listOf(appointmentScheduled.toModel()).success()

            coEvery {
                appointmentScheduleModelDataService.find(
                    queryEq {
                        where {
                            this.staffId.eq(staff.id) and
                                    this.status.inList(listOf(AppointmentScheduleStatus.SCHEDULED)) and
                                    (this.startTime.greaterEq(varStartTime) and
                                            this.startTime.lessEq(
                                                varStartTime.plusWeeks(1)
                                                    .plusMinutes(appointmentScheduleEventType.duration.toLong())
                                            ))
                        }.orderBy { startTime }
                            .sortOrder { desc }
                    }
                )
            } returns emptyList<AppointmentScheduleModel>().success()

            coEvery {
                staffAvailabilityService.getSlotsAtSpecificDateTime(
                    staff.id,
                    startTime,
                    endTime.plusWeeks(1),
                    appointmentScheduleEventType.id,
                    appointmentScheduleEventType,
                    null,
                    2
                )
            } returns listOf(
                SlotForSpecificDuration(
                    startTime.toSaoPauloTimeZone(),
                    endTime.toSaoPauloTimeZone(),
                    appointmentScheduleEventType.duration.toLong(),
                    emptyList(),
                    staff.id,
                )
            ).success()

            val result = appointmentScheduleService.validateAppointmentScheduleCreation(
                person.id,
                staff.id,
                startTime,
                appointmentScheduleEventType.id,
                numberOfSessions = 2
            )
            assertThat(result).isSuccessWithData(expected)

            coVerifyOnce { appointmentScheduleEventTypeService.get(any()) }
            coVerifyOnce { eventTypeProviderUnitService.getForEventType(any()) }
            coVerify(exactly = 2) { appointmentScheduleModelDataService.find(any()) }
            coVerifyOnce {
                staffAvailabilityService.getSlotsAtSpecificDateTime(
                    any(),
                    any(),
                    any(),
                    any(),
                    any(),
                    any(),
                    any()
                )
            }
        }

    @Test
    fun `#validateAppointmentScheduleCreation should return false if there is scheduled appointment schedule for staff at the time`(): Unit =
        runBlocking {
            val endTime = startTime.plusMinutes(appointmentScheduleEventType.duration.toLong())
            val appointmentScheduled = appointmentScheduled.copy(startTime = startTime, endTime = endTime)
            val expected = mutableListOf(
                AppointmentScheduleCreationValidation(
                    startTime.toSaoPauloTimeZone(),
                    endTime.toSaoPauloTimeZone(),
                    AppointmentScheduleCreationValidationResult.STAFF_APPOINTMENT_SCHEDULE_COLLISION
                )
            )
            coEvery {
                appointmentScheduleEventTypeService.get(appointmentScheduleEventType.id)
            } returns appointmentScheduleEventType.success()

            coEvery {
                eventTypeProviderUnitService.getForEventType(appointmentScheduleEventType.id)
            } returns listOf(eventTypeProviderUnitDigital).success()

            coEvery {
                appointmentScheduleModelDataService.find(
                    queryEq {
                        where {
                            this.personId.eq(person.id) and
                                    this.status.inList(listOf(AppointmentScheduleStatus.SCHEDULED)) and
                                    (this.startTime.greaterEq(appointmentScheduled.startTime) and
                                            this.startTime.lessEq(
                                                appointmentScheduled.startTime.plusMinutes(
                                                    appointmentScheduleEventType.duration.toLong()
                                                )
                                            ))
                        }.orderBy { startTime }
                            .sortOrder { desc }
                    }
                )
            } returns emptyList<AppointmentScheduleModel>().success()

            coEvery {
                appointmentScheduleModelDataService.find(
                    queryEq {
                        where {
                            this.staffId.eq(staff.id) and
                                    this.status.inList(listOf(AppointmentScheduleStatus.SCHEDULED)) and
                                    (this.startTime.greaterEq(appointmentScheduled.startTime) and
                                            this.startTime.lessEq(
                                                appointmentScheduled.startTime.plusMinutes(
                                                    appointmentScheduleEventType.duration.toLong()
                                                )
                                            ))
                        }.orderBy { startTime }
                            .sortOrder { desc }
                    }
                )
            } returns listOf(appointmentScheduled.toModel()).success()

            coEvery {
                staffAvailabilityService.getSlotsAtSpecificDateTime(
                    staff.id,
                    startTime,
                    endTime,
                    appointmentScheduleEventType.id,
                    appointmentScheduleEventType,
                    null,
                    1,
                )
            } returns listOf(
                SlotForSpecificDuration(
                    startTime,
                    endTime,
                    appointmentScheduleEventType.duration.toLong(),
                    emptyList(),
                    staff.id,
                )
            ).success()

            val result = appointmentScheduleService.validateAppointmentScheduleCreation(
                person.id,
                staff.id,
                startTime,
                appointmentScheduleEventType.id,
                numberOfSessions = 1
            )
            assertThat(result).isSuccessWithData(expected)

            coVerifyOnce { appointmentScheduleEventTypeService.get(any()) }
            coVerifyOnce { eventTypeProviderUnitService.getForEventType(any()) }
            coVerify(exactly = 2) { appointmentScheduleModelDataService.find(any()) }
            coVerifyOnce {
                staffAvailabilityService.getSlotsAtSpecificDateTime(
                    any(),
                    any(),
                    any(),
                    any(),
                    any(),
                    any(),
                    any()
                )
            }
        }

    @Test
    fun `#validateAppointmentScheduleCreation should return false if there is scheduled appointment schedule for staff at the time for two sessions`(): Unit =
        runBlocking {
            val varStartTime = startTime
            val endTime = startTime.plusMinutes(appointmentScheduleEventType.duration.toLong())
            val appointmentScheduled =
                appointmentScheduled.copy(startTime = startTime.plusWeeks(1), endTime = endTime.plusWeeks(1))
            val expected = mutableListOf(
                AppointmentScheduleCreationValidation(
                    startTime.toSaoPauloTimeZone(),
                    endTime.toSaoPauloTimeZone(),
                    AppointmentScheduleCreationValidationResult.CAN_CREATE
                ),
                AppointmentScheduleCreationValidation(
                    startTime.plusWeeks(1).toSaoPauloTimeZone(),
                    endTime.plusWeeks(1).toSaoPauloTimeZone(),
                    AppointmentScheduleCreationValidationResult.STAFF_APPOINTMENT_SCHEDULE_COLLISION
                ),
            )
            coEvery {
                appointmentScheduleEventTypeService.get(appointmentScheduleEventType.id)
            } returns appointmentScheduleEventType.success()

            coEvery {
                eventTypeProviderUnitService.getForEventType(appointmentScheduleEventType.id)
            } returns listOf(eventTypeProviderUnitDigital).success()

            coEvery {
                appointmentScheduleModelDataService.find(
                    queryEq {
                        where {
                            this.personId.eq(person.id) and
                                    this.status.inList(listOf(AppointmentScheduleStatus.SCHEDULED)) and
                                    (this.startTime.greaterEq(varStartTime) and
                                            this.startTime.lessEq(
                                                varStartTime.plusWeeks(1)
                                                    .plusMinutes(appointmentScheduleEventType.duration.toLong())
                                            ))
                        }.orderBy { startTime }
                            .sortOrder { desc }
                    }
                )
            } returns emptyList<AppointmentScheduleModel>().success()

            coEvery {
                appointmentScheduleModelDataService.find(
                    queryEq {
                        where {
                            this.staffId.eq(staff.id) and
                                    this.status.inList(listOf(AppointmentScheduleStatus.SCHEDULED)) and
                                    (this.startTime.greaterEq(varStartTime) and
                                            this.startTime.lessEq(
                                                varStartTime.plusWeeks(1)
                                                    .plusMinutes(appointmentScheduleEventType.duration.toLong())
                                            ))
                        }.orderBy { startTime }
                            .sortOrder { desc }
                    }
                )
            } returns listOf(appointmentScheduled.toModel()).success()

            coEvery {
                staffAvailabilityService.getSlotsAtSpecificDateTime(
                    staff.id,
                    startTime,
                    endTime.plusWeeks(1),
                    appointmentScheduleEventType.id,
                    appointmentScheduleEventType,
                    null,
                    2,
                )
            } returns listOf(
                SlotForSpecificDuration(
                    startTime.toSaoPauloTimeZone(),
                    endTime.toSaoPauloTimeZone(),
                    appointmentScheduleEventType.duration.toLong(),
                    emptyList(),
                    staff.id,
                )
            ).success()

            val result = appointmentScheduleService.validateAppointmentScheduleCreation(
                person.id,
                staff.id,
                startTime,
                appointmentScheduleEventType.id,
                numberOfSessions = 2
            )
            assertThat(result).isSuccessWithData(expected)

            coVerifyOnce { appointmentScheduleEventTypeService.get(any()) }
            coVerifyOnce { eventTypeProviderUnitService.getForEventType(any()) }
            coVerify(exactly = 2) { appointmentScheduleModelDataService.find(any()) }
            coVerifyOnce {
                staffAvailabilityService.getSlotsAtSpecificDateTime(
                    any(),
                    any(),
                    any(),
                    any(),
                    any(),
                    any(),
                    any()
                )
            }
        }

    @Test
    fun `#validateAppointmentScheduleCreation should return false if there are no free schedule slots for period for staff`(): Unit =
        runBlocking {
            val endTime = startTime.plusMinutes(appointmentScheduleEventType.duration.toLong())
            val expected = mutableListOf(
                AppointmentScheduleCreationValidation(
                    startTime.toSaoPauloTimeZone(),
                    endTime.toSaoPauloTimeZone(),
                    AppointmentScheduleCreationValidationResult.NO_FREE_SCHEDULE_SLOTS
                )
            )
            coEvery {
                appointmentScheduleEventTypeService.get(appointmentScheduleEventType.id)
            } returns appointmentScheduleEventType.success()

            coEvery {
                eventTypeProviderUnitService.getForEventType(appointmentScheduleEventType.id)
            } returns listOf(eventTypeProviderUnitDigital).success()

            coEvery {
                appointmentScheduleModelDataService.find(
                    queryEq {
                        where {
                            this.personId.eq(person.id) and
                                    this.status.inList(listOf(AppointmentScheduleStatus.SCHEDULED)) and
                                    (this.startTime.greaterEq(appointmentScheduled.startTime) and
                                            this.startTime.lessEq(
                                                appointmentScheduled.startTime.plusMinutes(
                                                    appointmentScheduleEventType.duration.toLong()
                                                )
                                            ))
                        }.orderBy { startTime }
                            .sortOrder { desc }
                    }
                )
            } returns emptyList<AppointmentScheduleModel>().success()

            coEvery {
                appointmentScheduleModelDataService.find(
                    queryEq {
                        where {
                            this.staffId.eq(staff.id) and
                                    this.status.inList(listOf(AppointmentScheduleStatus.SCHEDULED)) and
                                    (this.startTime.greaterEq(appointmentScheduled.startTime) and
                                            this.startTime.lessEq(
                                                appointmentScheduled.startTime.plusMinutes(
                                                    appointmentScheduleEventType.duration.toLong()
                                                )
                                            ))
                        }.orderBy { startTime }
                            .sortOrder { desc }
                    }
                )
            } returns emptyList<AppointmentScheduleModel>().success()

            coEvery {
                staffAvailabilityService.getSlotsAtSpecificDateTime(
                    staff.id,
                    startTime,
                    endTime,
                    appointmentScheduleEventType.id,
                    appointmentScheduleEventType,
                    null,
                    1,
                )
            } returns emptyList<SlotForSpecificDuration>().success()

            val result = appointmentScheduleService.validateAppointmentScheduleCreation(
                person.id,
                staff.id,
                startTime,
                appointmentScheduleEventType.id,
                numberOfSessions = 1
            )
            assertThat(result).isSuccessWithData(expected)

            coVerifyOnce { appointmentScheduleEventTypeService.get(any()) }
            coVerifyOnce { eventTypeProviderUnitService.getForEventType(any()) }
            coVerify(exactly = 2) { appointmentScheduleModelDataService.find(any()) }
            coVerifyOnce {
                staffAvailabilityService.getSlotsAtSpecificDateTime(
                    any(),
                    any(),
                    any(),
                    any(),
                    any(),
                    any(),
                    any()
                )
            }
        }

    @Test
    fun `#validateAppointmentScheduleCreation should return false if there are no free schedule slots for period for staff for two sessions`(): Unit =
        runBlocking {
            val varStartTime = startTime
            val endTime = startTime.plusMinutes(appointmentScheduleEventType.duration.toLong())
            val expected = mutableListOf(
                AppointmentScheduleCreationValidation(
                    startTime.toSaoPauloTimeZone(),
                    endTime.toSaoPauloTimeZone(),
                    AppointmentScheduleCreationValidationResult.CAN_CREATE
                ),
                AppointmentScheduleCreationValidation(
                    startTime.plusWeeks(1).toSaoPauloTimeZone(),
                    endTime.plusWeeks(1).toSaoPauloTimeZone(),
                    AppointmentScheduleCreationValidationResult.NO_FREE_SCHEDULE_SLOTS
                ),
            )
            coEvery {
                appointmentScheduleEventTypeService.get(appointmentScheduleEventType.id)
            } returns appointmentScheduleEventType.success()

            coEvery {
                eventTypeProviderUnitService.getForEventType(appointmentScheduleEventType.id)
            } returns listOf(eventTypeProviderUnitDigital).success()

            coEvery {
                appointmentScheduleModelDataService.find(
                    queryEq {
                        where {
                            this.personId.eq(person.id) and
                                    this.status.inList(listOf(AppointmentScheduleStatus.SCHEDULED)) and
                                    (this.startTime.greaterEq(varStartTime) and
                                            this.startTime.lessEq(
                                                varStartTime.plusWeeks(1)
                                                    .plusMinutes(appointmentScheduleEventType.duration.toLong())
                                            ))
                        }.orderBy { startTime }
                            .sortOrder { desc }
                    }
                )
            } returns emptyList<AppointmentScheduleModel>().success()

            coEvery {
                appointmentScheduleModelDataService.find(
                    queryEq {
                        where {
                            this.staffId.eq(staff.id) and
                                    this.status.inList(listOf(AppointmentScheduleStatus.SCHEDULED)) and
                                    (this.startTime.greaterEq(varStartTime) and
                                            this.startTime.lessEq(
                                                varStartTime.plusWeeks(1)
                                                    .plusMinutes(appointmentScheduleEventType.duration.toLong())
                                            ))
                        }.orderBy { startTime }
                            .sortOrder { desc }
                    }
                )
            } returns emptyList<AppointmentScheduleModel>().success()

            coEvery {
                staffAvailabilityService.getSlotsAtSpecificDateTime(
                    staff.id,
                    startTime,
                    endTime.plusWeeks(1),
                    appointmentScheduleEventType.id,
                    appointmentScheduleEventType,
                    null,
                    2,
                )
            } returns listOf(
                SlotForSpecificDuration(
                    startTime.toSaoPauloTimeZone(),
                    endTime.toSaoPauloTimeZone(),
                    appointmentScheduleEventType.duration.toLong(),
                    emptyList(),
                    staff.id,
                )
            ).success()

            val result = appointmentScheduleService.validateAppointmentScheduleCreation(
                person.id,
                staff.id,
                startTime,
                appointmentScheduleEventType.id,
                numberOfSessions = 2
            )
            assertThat(result).isSuccessWithData(expected)

            coVerifyOnce { appointmentScheduleEventTypeService.get(any()) }
            coVerifyOnce { eventTypeProviderUnitService.getForEventType(any()) }
            coVerify(exactly = 2) { appointmentScheduleModelDataService.find(any()) }
            coVerifyOnce {
                staffAvailabilityService.getSlotsAtSpecificDateTime(
                    any(),
                    any(),
                    any(),
                    any(),
                    any(),
                    any(),
                    any()
                )
            }
        }

    @Test
    fun `#validateAppointmentScheduleCreation should return true if all conditions to appointment schedule are true`(): Unit =
        runBlocking {
            val endTime = startTime.plusMinutes(appointmentScheduleEventType.duration.toLong())
            val expected = mutableListOf(
                AppointmentScheduleCreationValidation(
                    startTime.toSaoPauloTimeZone(),
                    endTime.toSaoPauloTimeZone(),
                    AppointmentScheduleCreationValidationResult.CAN_CREATE
                )
            )
            coEvery {
                appointmentScheduleEventTypeService.get(appointmentScheduleEventType.id)
            } returns appointmentScheduleEventType.success()

            coEvery {
                eventTypeProviderUnitService.getForEventType(appointmentScheduleEventType.id)
            } returns listOf(eventTypeProviderUnitDigital).success()

            coEvery {
                appointmentScheduleModelDataService.find(
                    queryEq {
                        where {
                            this.personId.eq(person.id) and
                                    this.status.inList(listOf(AppointmentScheduleStatus.SCHEDULED)) and
                                    (this.startTime.greaterEq(appointmentScheduled.startTime) and
                                            this.startTime.lessEq(
                                                appointmentScheduled.startTime.plusMinutes(
                                                    appointmentScheduleEventType.duration.toLong()
                                                )
                                            ))
                        }.orderBy { startTime }
                            .sortOrder { desc }
                    }
                )
            } returns emptyList<AppointmentScheduleModel>().success()

            coEvery {
                appointmentScheduleModelDataService.find(
                    queryEq {
                        where {
                            this.staffId.eq(staff.id) and
                                    this.status.inList(listOf(AppointmentScheduleStatus.SCHEDULED)) and
                                    (this.startTime.greaterEq(appointmentScheduled.startTime) and
                                            this.startTime.lessEq(
                                                appointmentScheduled.startTime.plusMinutes(
                                                    appointmentScheduleEventType.duration.toLong()
                                                )
                                            ))
                        }.orderBy { startTime }
                            .sortOrder { desc }
                    }
                )
            } returns emptyList<AppointmentScheduleModel>().success()

            coEvery {
                staffAvailabilityService.getSlotsAtSpecificDateTime(
                    staff.id,
                    startTime,
                    startTime.plusMinutes(appointmentScheduleEventType.duration.toLong()),
                    appointmentScheduleEventType.id,
                    appointmentScheduleEventType,
                    null,
                    1,
                )
            } returns listOf(
                SlotForSpecificDuration(
                    startTime.toSaoPauloTimeZone(),
                    endTime.toSaoPauloTimeZone(),
                    appointmentScheduleEventType.duration.toLong(),
                    emptyList(),
                    staff.id,
                )
            ).success()

            val result = appointmentScheduleService.validateAppointmentScheduleCreation(
                person.id,
                staff.id,
                startTime,
                appointmentScheduleEventType.id,
                numberOfSessions = 1
            )
            assertThat(result).isSuccessWithData(expected)

            coVerifyOnce { appointmentScheduleEventTypeService.get(any()) }
            coVerifyOnce { eventTypeProviderUnitService.getForEventType(any()) }
            coVerify(exactly = 2) { appointmentScheduleModelDataService.find(any()) }
            coVerifyOnce {
                staffAvailabilityService.getSlotsAtSpecificDateTime(
                    any(),
                    any(),
                    any(),
                    any(),
                    any(),
                    any(),
                    any()
                )
            }
        }

    @Test
    fun `#validateAppointmentScheduleCreation should return true if all conditions to appointment schedule are true for two sessions`(): Unit =
        runBlocking {
            val varStartTime = startTime
            val endTime = startTime.plusMinutes(appointmentScheduleEventType.duration.toLong())
            val expected = mutableListOf(
                AppointmentScheduleCreationValidation(
                    startTime.toSaoPauloTimeZone(),
                    endTime.toSaoPauloTimeZone(),
                    AppointmentScheduleCreationValidationResult.CAN_CREATE
                ),
                AppointmentScheduleCreationValidation(
                    startTime.plusWeeks(1).toSaoPauloTimeZone(),
                    endTime.plusWeeks(1).toSaoPauloTimeZone(),
                    AppointmentScheduleCreationValidationResult.CAN_CREATE
                ),
            )
            coEvery {
                appointmentScheduleEventTypeService.get(appointmentScheduleEventType.id)
            } returns appointmentScheduleEventType.success()

            coEvery {
                eventTypeProviderUnitService.getForEventType(appointmentScheduleEventType.id)
            } returns listOf(eventTypeProviderUnitDigital).success()

            coEvery {
                appointmentScheduleModelDataService.find(
                    queryEq {
                        where {
                            this.personId.eq(person.id) and
                                    this.status.inList(listOf(AppointmentScheduleStatus.SCHEDULED)) and
                                    (this.startTime.greaterEq(varStartTime) and
                                            this.startTime.lessEq(
                                                varStartTime.plusWeeks(1)
                                                    .plusMinutes(appointmentScheduleEventType.duration.toLong())
                                            ))
                        }.orderBy { startTime }
                            .sortOrder { desc }
                    }
                )
            } returns emptyList<AppointmentScheduleModel>().success()

            coEvery {
                appointmentScheduleModelDataService.find(
                    queryEq {
                        where {
                            this.staffId.eq(staff.id) and
                                    this.status.inList(listOf(AppointmentScheduleStatus.SCHEDULED)) and
                                    (this.startTime.greaterEq(varStartTime) and
                                            this.startTime.lessEq(
                                                varStartTime.plusWeeks(1)
                                                    .plusMinutes(appointmentScheduleEventType.duration.toLong())
                                            ))
                        }.orderBy { startTime }
                            .sortOrder { desc }
                    }
                )
            } returns emptyList<AppointmentScheduleModel>().success()

            coEvery {
                staffAvailabilityService.getSlotsAtSpecificDateTime(
                    staff.id,
                    startTime,
                    startTime.plusWeeks(1).plusMinutes(appointmentScheduleEventType.duration.toLong()),
                    appointmentScheduleEventType.id,
                    appointmentScheduleEventType,
                    null,
                    2,
                )
            } returns listOf(
                SlotForSpecificDuration(
                    startTime.toSaoPauloTimeZone(),
                    endTime.toSaoPauloTimeZone(),
                    appointmentScheduleEventType.duration.toLong(),
                    emptyList(),
                    staff.id,
                ), SlotForSpecificDuration(
                    startTime.plusWeeks(1).toSaoPauloTimeZone(),
                    endTime.plusWeeks(1).toSaoPauloTimeZone(),
                    appointmentScheduleEventType.duration.toLong(),
                    emptyList(),
                    staff.id,
                )
            ).success()

            val result = appointmentScheduleService.validateAppointmentScheduleCreation(
                person.id,
                staff.id,
                startTime,
                appointmentScheduleEventType.id,
                numberOfSessions = 2
            )
            assertThat(result).isSuccessWithData(expected)

            coVerifyOnce { appointmentScheduleEventTypeService.get(any()) }
            coVerifyOnce { eventTypeProviderUnitService.getForEventType(any()) }
            coVerify(exactly = 2) { appointmentScheduleModelDataService.find(any()) }
            coVerifyOnce {
                staffAvailabilityService.getSlotsAtSpecificDateTime(
                    any(),
                    any(),
                    any(),
                    any(),
                    any(),
                    any(),
                    any()
                )
            }
        }

    @Test
    fun `#getCloserScheduleByHealthPlanTask should return closer schedule to the current date`() = runBlocking {
        val appointmentScheduled = TestModelFactory.buildAppointmentSchedule(
            startTime = LocalDateTime.now()
        )

        val healthPlanTask = TestModelFactory.buildHealthPlanTask(personId = person.id)

        coEvery {
            appointmentScheduleModelDataService.findOne(
                queryEq {
                    where {
                        this.healthPlanTaskId.eq(healthPlanTask.id)
                            .and(this.status.eq(AppointmentScheduleStatus.SCHEDULED))
                    }
                        .orderBy { startTime }
                        .sortOrder { asc }
                }
            )
        } returns appointmentScheduled.toModel().success()

        val result = appointmentScheduleService.getCloserScheduleByHealthPlanTask(healthPlanTask.id)
        assertThat(result).isSuccessWithData(appointmentScheduled)

        coVerifyOnce { appointmentScheduleModelDataService.findOne(any()) }
    }

    @Test
    fun `#hasCollidingForPerson returns true when start or end time has colliding time with another schedule by the same person id`(): Unit =
        runBlocking {
            val startDate = LocalDateTime.of(2023, 11, 16, 10, 10, 0)
            val endDate = startDate.plusMinutes(30)
            val expectedAppointmentSchedule = appointmentScheduled.copy(
                startTime = startDate.plusMinutes(10),
                endTime = endDate.plusMinutes(10)
            )
            mockLocalDateTime(startDate) {
                coEvery {
                    appointmentScheduleModelDataService.find(
                        queryEq {
                            where {
                                this.personId.eq(person.id) and
                                        this.status.inList(listOf(AppointmentScheduleStatus.SCHEDULED)) and
                                        (this.startTime.greaterEq(startDate) and this.startTime.lessEq(endDate))
                            }.orderBy { startTime }
                                .sortOrder { desc }
                        }
                    )
                } returns listOf(expectedAppointmentSchedule.toModel()).success()

                val result = appointmentScheduleService.hasCollidingForPerson(
                    person.id,
                    startDate,
                    endDate
                )
                assertThat(result).isSuccessWithData(true)

                coVerifyOnce { appointmentScheduleModelDataService.find(any()) }
            }
        }

    @Test
    fun `#hasCollidingForPerson returns false when start or end time has not collided time with another schedule by the same person id`(): Unit =
        runBlocking {
            val startDate = LocalDateTime.now()
            val endDate = startDate.plusMinutes(30)

            mockLocalDateTime(startDate) {
                coEvery {
                    appointmentScheduleModelDataService.find(
                        queryEq {
                            where {
                                this.personId.eq(person.id) and
                                        this.status.inList(listOf(AppointmentScheduleStatus.SCHEDULED)) and
                                        (this.startTime.greaterEq(startDate) and this.startTime.lessEq(endDate))
                            }.orderBy { startTime }
                                .sortOrder { desc }
                        }
                    )
                } returns emptyList<AppointmentScheduleModel>().success()

                val result = appointmentScheduleService.hasCollidingForPerson(
                    person.id,
                    startDate,
                    endDate
                )
                assertThat(result).isSuccessWithData(false)

                coVerifyOnce { appointmentScheduleModelDataService.find(any()) }
            }
        }

    @Test
    fun `#scheduleInternal should schedule a new appointment with google meet link`() = runBlocking {
        withFeatureFlag(FeatureNamespace.SCHEDULE, "should_use_google_meet_to_generate_digital_links", true) {
            val appointmentScheduleEvent = appointmentScheduleEvent.copy(location = "Google Meet")
            val appointmentScheduleToAdd = appointmentScheduleToAdd.copy(location = "Google Meet")
            val fromDate = appointmentScheduled.startTime.minusDays(1)
            val toDate = appointmentScheduled.startTime.plusDays(1)

            coEvery { healthResourcesStore.getHealthPlanTaskByPerson(person.id) } returns healthPlanTasks.success()
            coEvery { healthResourcesStore.findStaffByEmail(staff.email) } returns staff.success()
            coEvery { healthResourcesStore.getHealthCareTeamByStaffId(staff.id) } returns healthcareTeam.success()
            coEvery {
                appointmentScheduleModelDataService.exists(
                    queryEq {
                        where {
                            this.id.diff(appointmentScheduleToAdd.id) and
                                    this.personId.eq(appointmentScheduleToAdd.personId) and
                                    this.startTime.eq(appointmentScheduleToAdd.startTime) and
                                    this.status.eq(AppointmentScheduleStatus.SCHEDULED)
                        }
                    }
                )
            } returns false.success()
            coEvery { appointmentScheduleModelDataService.add(appointmentScheduleToAdd.toModel()) } returns
                    appointmentScheduleToAdd.toModel().success()
            coEvery { personService.get(person.id) } returns person.success()
            coEvery {
                appointmentScheduleModelDataService.findOne(
                    queryEq {
                        where {
                            this.staffId.eq(staff.id) and
                                    this.startTime.eq(appointmentScheduleEvent.startTime) and
                                    this.personId.eq(person.id)
                        }
                    }
                )
            } returns NotFoundException().failure()
            coEvery {
                appointmentScheduleModelDataService.find(
                    queryEq {
                        where {
                            this.staffId.eq(staff.id) and
                                    this.status.inList(listOf(AppointmentScheduleStatus.SCHEDULED)) and
                                    (this.startTime.greaterEq(fromDate) and this.startTime.lessEq(toDate))
                        }.orderBy { startTime }
                            .sortOrder { desc }
                    }
                )
            } returns emptyList<AppointmentScheduleModel>().success()
            coEvery {
                kafkaProducerService.produce(InternalAppointmentScheduleCreatedEvent(appointmentScheduleToAdd))
            } returns mockk()
            coEvery {
                kafkaProducerService.produce(
                    AppointmentScheduleCreatedEvent(
                        person = person,
                        appointmentSchedule = appointmentScheduleToAdd,
                        isUsingInternalScheduler = true,
                        professional = Professional.from(staff)
                    )
                )
            } returns mockk()

            mockRangeUuidAndDateTime(appointmentScheduleToAddId, baseDate) {
                val result = appointmentScheduleService.scheduleInternal(
                    person.id,
                    appointmentScheduleEvent,
                    numberOfSessions = 1
                )
                assertThat(result).isSuccessWithData(listOf(appointmentScheduleToAdd))
            }

            coVerifyOnce { healthResourcesStore.getHealthPlanTaskByPerson(any()) }
            coVerifyOnce { healthResourcesStore.findStaffByEmail(any()) }
            coVerifyOnce { healthResourcesStore.getHealthCareTeamByStaffId(any()) }
            coVerifyOnce { appointmentScheduleModelDataService.exists(any()) }
            coVerifyOnce { appointmentScheduleModelDataService.add(any()) }
            coVerifyOnce { personService.get(any()) }
            coVerifyOnce { appointmentScheduleModelDataService.findOne(any()) }
            coVerifyOnce { appointmentScheduleModelDataService.find(any()) }
            coVerify(exactly = 2) { kafkaProducerService.produce(any()) }
        }
    }

    @Test
    fun `#findBy returns error when filter is invalid`() = runBlocking {
        val filter = AppointmentScheduleFilter()

        val result = appointmentScheduleService.findBy(filter)
        assertThat(result).isFailureOfType(InvalidArgumentException::class)

        coVerify { appointmentScheduleModelDataService wasNot called }
        confirmMocks()
    }

    @Test
    fun `#findBy returns appointment schedules find by all filters`() = runBlocking {
        val ids = listOf(RangeUUID.generate(), RangeUUID.generate())
        val healthPlanTaskId = RangeUUID.generate()
        val personId = PersonId()
        val createdAt = LocalDateTime.now()
        val staffId = RangeUUID.generate()
        val startTime = LocalDate.now()
        val startTimeRange = LocalDateTime.now() to LocalDateTime.now()
        val types = listOf(AppointmentScheduleType.TEST, AppointmentScheduleType.FOLLOW_UP)
        val status = listOf(AppointmentScheduleStatus.SCHEDULED)
        val startDate = LocalDate.now()
        val endDate = LocalDate.now()
        val endTimeGreater = LocalDateTime.now()
        val appointmentScheduleEventTypeId = RangeUUID.generate()
        val range = IntRange(0, 99)

        val filter = AppointmentScheduleFilter(
            ids = ids,
            healthPlanTaskId = healthPlanTaskId,
            personId = personId,
            createdAt = createdAt,
            staffId = staffId,
            startTime = startTime,
            startTimeRange = startTimeRange,
            types = types,
            status = status,
            startDate = startDate,
            endDate = endDate,
            endTimeGreater = endTimeGreater,
            appointmentScheduleEventTypeId = appointmentScheduleEventTypeId,
            range = range,
            providerUnitIdIsNull = true
        )

        coEvery {
            appointmentScheduleModelDataService.find(
                queryEq {
                    where {
                        this.id.inList(ids) and
                                this.healthPlanTaskId.eq(healthPlanTaskId) and
                                this.personId.eq(personId) and
                                this.staffId.eq(staffId) and
                                this.type.inList(types) and
                                this.status.inList(status) and
                                this.appointmentScheduleEventTypeId.eq(appointmentScheduleEventTypeId) and
                                this.createdAt.greaterEq(createdAt) and
                                (this.startTime.greaterEq(startTime.atStartOfDay()) and this.startTime.lessEq(startTime.atEndOfTheDay())) and
                                (this.startTime.greaterEq(startTimeRange.first) and this.startTime.lessEq(startTimeRange.second)) and
                                this.startTime.greaterEq(startDate.atStartOfDay()) and
                                this.endTime.lessEq(endDate.atEndOfTheDay()) and
                                this.endTime.greaterEq(endTimeGreater) and
                                this.providerUnitId.isNull()
                    }.orderBy { this.startTime }
                        .sortOrder { desc }
                        .offset { range.first }
                        .limit { range.count() }
                }
            )
        } returns listOf(appointmentScheduled.toModel()).success()

        val result = appointmentScheduleService.findBy(filter)
        assertThat(result).isSuccessWithData(listOf(appointmentScheduled))

        coVerifyOnce { appointmentScheduleModelDataService.find(any()) }
        confirmMocks()
    }

    @Test
    fun `#findBy returns appointment schedules find by all filters and providerUnitIdIsNull is false`() = runBlocking {
        val ids = listOf(RangeUUID.generate(), RangeUUID.generate())
        val healthPlanTaskId = RangeUUID.generate()
        val personId = PersonId()
        val createdAt = LocalDateTime.now()
        val staffId = RangeUUID.generate()
        val startTime = LocalDate.now()
        val startTimeRange = LocalDateTime.now() to LocalDateTime.now()
        val types = listOf(AppointmentScheduleType.TEST, AppointmentScheduleType.FOLLOW_UP)
        val status = listOf(AppointmentScheduleStatus.SCHEDULED)
        val startDate = LocalDate.now()
        val endDate = LocalDate.now()
        val endTimeGreater = LocalDateTime.now()
        val appointmentScheduleEventTypeId = RangeUUID.generate()
        val range = IntRange(0, 99)

        val filter = AppointmentScheduleFilter(
            ids = ids,
            healthPlanTaskId = healthPlanTaskId,
            personId = personId,
            createdAt = createdAt,
            staffId = staffId,
            startTime = startTime,
            startTimeRange = startTimeRange,
            types = types,
            status = status,
            startDate = startDate,
            endDate = endDate,
            endTimeGreater = endTimeGreater,
            appointmentScheduleEventTypeId = appointmentScheduleEventTypeId,
            range = range,
            providerUnitIdIsNull = false
        )

        coEvery {
            appointmentScheduleModelDataService.find(
                queryEq {
                    where {
                        this.id.inList(ids) and
                                this.healthPlanTaskId.eq(healthPlanTaskId) and
                                this.personId.eq(personId) and
                                this.staffId.eq(staffId) and
                                this.type.inList(types) and
                                this.status.inList(status) and
                                this.appointmentScheduleEventTypeId.eq(appointmentScheduleEventTypeId) and
                                this.createdAt.greaterEq(createdAt) and
                                (this.startTime.greaterEq(startTime.atStartOfDay()) and this.startTime.lessEq(startTime.atEndOfTheDay())) and
                                (this.startTime.greaterEq(startTimeRange.first) and this.startTime.lessEq(startTimeRange.second)) and
                                this.startTime.greaterEq(startDate.atStartOfDay()) and
                                this.endTime.lessEq(endDate.atEndOfTheDay()) and
                                this.endTime.greaterEq(endTimeGreater) and
                                this.providerUnitId.isNotNull()
                    }.orderBy { this.startTime }
                        .sortOrder { desc }
                        .offset { range.first }
                        .limit { range.count() }
                }
            )
        } returns listOf(appointmentScheduled.toModel()).success()

        val result = appointmentScheduleService.findBy(filter)
        assertThat(result).isSuccessWithData(listOf(appointmentScheduled))

        coVerifyOnce { appointmentScheduleModelDataService.find(any()) }
        confirmMocks()
    }

    @Test
    fun `#findLastForEachPersonIn should return appointment schedules for personId list`() = runBlocking {

        val personId1 = PersonId()
        val appointment = TestModelFactory.buildAppointmentSchedule(
            personId = personId1,
            status = AppointmentScheduleStatus.SCHEDULED,
            location = schedulePreference.zoomLink,
            createdAt = baseDate,
            updatedAt = baseDate,
            type = AppointmentScheduleType.HEALTH_DECLARATION
        )
        val expected = listOf(
            appointment
        )

        coEvery {
            appointmentScheduleModelDataService
                .find(
                    queryEq {
                        where {
                            this.personId.inList(listOf(personId1)) and this.type.eq(AppointmentScheduleType.HEALTH_DECLARATION)
                        }
                            .orderBy { this.createdAt }
                            .sortOrder { desc }
                    }
                )
        } returns listOf(appointment.toModel()).success()

        val result = appointmentScheduleService.findLastForEachPersonIn(listOf(personId1))
        assertThat(result).isSuccessWithData(expected)

        coVerifyOnce { appointmentScheduleModelDataService.find(any()) }
    }

    @Test
    fun `#findLastForEachPersonIn should throw error when id list is empty`() = runBlocking {
        val result = appointmentScheduleService.findLastForEachPersonIn(emptyList())
        assertThat(result).isFailureOfType(InvalidArgumentException::class)

        coVerify { appointmentScheduleModelDataService wasNot called }
    }

    @Test
    fun `#findLastForEachPersonIn should return empty list when event type does not exists`() = runBlocking {

        val personId1 = PersonId()
        val personId2 = PersonId()

        coEvery {
            appointmentScheduleModelDataService
                .find(
                    queryEq {
                        where {
                            this.personId.inList(listOf(personId1)) and this.type.eq(AppointmentScheduleType.FOLLOW_UP_NUTRITIONIST)
                        }
                            .orderBy { this.createdAt }
                            .sortOrder { desc }
                    }
                )
        } returns NotFoundException("List of entities is empty").failure()

        val result = appointmentScheduleService.findLastForEachPersonIn(
            listOf(personId1, personId2),
            AppointmentScheduleType.FOLLOW_UP_NUTRITIONIST
        )
        assertThat(result).isFailure()

        coVerifyOnce { appointmentScheduleModelDataService.find(any()) }
    }

    @Test
    fun `#findBy returns appointment schedules find by only one filters`() = runBlocking {
        val ids = listOf(RangeUUID.generate(), RangeUUID.generate())

        val filter = AppointmentScheduleFilter(ids = ids, sortOrder = SortOrder.Ascending)

        coEvery {
            appointmentScheduleModelDataService.find(
                queryEq {
                    where {
                        this.id.inList(ids)
                    }.orderBy { this.startTime }
                        .sortOrder { asc }
                }
            )
        } returns listOf(appointmentScheduled.toModel()).success()

        val result = appointmentScheduleService.findBy(filter)
        assertThat(result).isSuccessWithData(listOf(appointmentScheduled))

        coVerifyOnce { appointmentScheduleModelDataService.find(any()) }
        confirmMocks()
    }

    @Test
    fun `#findWithStaffBy returns error when filter is invalid`() = runBlocking {
        val filter = AppointmentScheduleFilter()

        val result = appointmentScheduleService.findWithStaffBy(filter)
        assertThat(result).isFailureOfType(InvalidArgumentException::class)

        coVerify { appointmentScheduleModelDataService wasNot called }
        confirmMocks()
    }

    @Test
    fun `#findWithStaffBy returns appointment schedules find by all filters`() = runBlocking {
        val ids = listOf(RangeUUID.generate(), RangeUUID.generate())
        val healthPlanTaskId = RangeUUID.generate()
        val personId = PersonId()
        val createdAt = LocalDateTime.now()
        val staffId = RangeUUID.generate()
        val startTime = LocalDate.now()
        val startTimeRange = LocalDateTime.now() to LocalDateTime.now()
        val types = listOf(AppointmentScheduleType.TEST, AppointmentScheduleType.FOLLOW_UP)
        val status = listOf(AppointmentScheduleStatus.SCHEDULED)
        val startDate = LocalDate.now()
        val endDate = LocalDate.now()
        val endTimeGreater = LocalDateTime.now()
        val appointmentScheduleEventTypeId = RangeUUID.generate()
        val range = IntRange(0, 99)

        val filter = AppointmentScheduleFilter(
            ids = ids,
            healthPlanTaskId = healthPlanTaskId,
            personId = personId,
            createdAt = createdAt,
            staffId = staffId,
            startTime = startTime,
            startTimeRange = startTimeRange,
            types = types,
            status = status,
            startDate = startDate,
            endDate = endDate,
            endTimeGreater = endTimeGreater,
            appointmentScheduleEventTypeId = appointmentScheduleEventTypeId,
            range = range
        )

        val singleStaffId = RangeUUID.generate()
        val communitySpecialistId = RangeUUID.generate()

        val singleStaff = TestModelFactory.buildStaff().copy(id = singleStaffId)
        val communitySpecialist = TestModelFactory.buildHealthCommunitySpecialist().copy(id = communitySpecialistId)

        val appointmentSchedules = listOf(
            TestModelFactory.buildAppointmentSchedule(staffId = singleStaffId).toModel(),
        )

        val expected = listOf(
            AppointmentScheduleWithStaff(appointmentSchedules[0].toTransport(), singleStaff),
        )

        coEvery {
            appointmentScheduleModelDataService.find(
                queryEq {
                    where {
                        this.id.inList(ids) and
                                this.healthPlanTaskId.eq(healthPlanTaskId) and
                                this.personId.eq(personId) and
                                this.staffId.eq(staffId) and
                                this.type.inList(types) and
                                this.status.inList(status) and
                                this.appointmentScheduleEventTypeId.eq(appointmentScheduleEventTypeId) and
                                this.createdAt.greaterEq(createdAt) and
                                (this.startTime.greaterEq(startTime.atStartOfDay()) and this.startTime.lessEq(startTime.atEndOfTheDay())) and
                                (this.startTime.greaterEq(startTimeRange.first) and this.startTime.lessEq(startTimeRange.second)) and
                                this.startTime.greaterEq(startDate.atStartOfDay()) and
                                this.endTime.lessEq(endDate.atEndOfTheDay()) and
                                this.endTime.greaterEq(endTimeGreater)
                    }.orderBy { this.startTime }
                        .sortOrder { desc }
                        .offset { range.first }
                        .limit { range.count() }
                }
            )
        } returns appointmentSchedules.success()

        coEvery { healthResourcesStore.findStaffByList(listOf(singleStaffId)) } returns listOf(singleStaff).success()

        val result = appointmentScheduleService.findWithStaffBy(filter)
        assertThat(result).isSuccessWithData(expected)

        coVerifyOnce { appointmentScheduleModelDataService.find(any()) }
        coVerifyOnce { healthResourcesStore.findStaffByList(any()) }
        confirmMocks()
    }

    @Test
    fun `#findWithStaffBy returns appointment schedules find by only one filters`() = runBlocking {
        val ids = listOf(RangeUUID.generate(), RangeUUID.generate())
        val filter = AppointmentScheduleFilter(ids = ids)
        val singleStaffId = RangeUUID.generate()
        val communitySpecialistId = RangeUUID.generate()
        val singleStaff = TestModelFactory.buildStaff().copy(id = singleStaffId)
        val communitySpecialist = TestModelFactory.buildHealthCommunitySpecialist().copy(id = communitySpecialistId)

        val appointmentSchedules = listOf(
            TestModelFactory.buildAppointmentSchedule(staffId = singleStaffId).toModel(),
        )

        val expected = listOf(
            AppointmentScheduleWithStaff(appointmentSchedules[0].toTransport(), singleStaff),
        )

        coEvery {
            appointmentScheduleModelDataService.find(
                queryEq {
                    where {
                        this.id.inList(ids)
                    }.orderBy { startTime }
                        .sortOrder { desc }
                }
            )
        } returns appointmentSchedules.success()

        coEvery { healthResourcesStore.findStaffByList(listOf(singleStaffId)) } returns listOf(singleStaff).success()

        val result = appointmentScheduleService.findWithStaffBy(filter)
        assertThat(result).isSuccessWithData(expected)

        coVerifyOnce { appointmentScheduleModelDataService.find(any()) }
        coVerifyOnce { healthResourcesStore.findStaffByList(any()) }
        confirmMocks()
    }


    @Test
    fun `#update should update AppointmentSchedule`() = runBlocking {
        val expected = appointmentScheduled.copy(status = AppointmentScheduleStatus.CANCELED)

        coEvery {
            appointmentScheduleModelDataService.get(expected.id)
        } returns appointmentScheduled.toModel().success()

        coEvery {
            appointmentScheduleModelDataService.update(expected.toModel())
        } returns expected.toModel().success()

        coEvery {
            kafkaProducerService.produce(AppointmentScheduleUpdatedEvent(expected))
        } returns mockk()

        val result = appointmentScheduleService.update(expected)
        assertThat(result).isSuccessWithData(expected)

        coVerifyOnce { appointmentScheduleModelDataService.get(any()) }
        coVerifyOnce { appointmentScheduleModelDataService.update(any()) }
        coVerifyOnce { kafkaProducerService.produce(any()) }
    }

    @Test
    fun `#update should update AppointmentSchedule returning the right version`() = runBlocking {
        val current = appointmentScheduled.copy(version = 1, status = AppointmentScheduleStatus.SCHEDULED)
        val expected = appointmentScheduled.copy(version = 0, status = AppointmentScheduleStatus.CANCELED)
        val expectedWithVersionOne = expected.copy(version = 1)

        coEvery {
            appointmentScheduleModelDataService.get(expected.id)
        } returns current.toModel().success()

        coEvery {
            appointmentScheduleModelDataService.update(expectedWithVersionOne.toModel())
        } returns expectedWithVersionOne.toModel().success()

        coEvery {
            kafkaProducerService.produce(AppointmentScheduleUpdatedEvent(expectedWithVersionOne))
        } returns mockk()

        val result = appointmentScheduleService.update(expectedWithVersionOne)
        assertThat(result).isSuccessWithData(expectedWithVersionOne)

        coVerifyOnce { appointmentScheduleModelDataService.get(any()) }
        coVerifyOnce { appointmentScheduleModelDataService.update(any()) }
        coVerifyOnce { kafkaProducerService.produce(any()) }
    }

    @Test
    fun `#countBy returns appointment schedules count by all filters`() = runBlocking {
        val ids = listOf(RangeUUID.generate(), RangeUUID.generate())
        val healthPlanTaskId = RangeUUID.generate()
        val personId = PersonId()
        val createdAt = LocalDateTime.now()
        val staffId = RangeUUID.generate()
        val startTime = LocalDate.now()
        val startTimeRange = LocalDateTime.now() to LocalDateTime.now()
        val types = listOf(AppointmentScheduleType.TEST, AppointmentScheduleType.FOLLOW_UP)
        val status = listOf(AppointmentScheduleStatus.SCHEDULED)
        val startDate = LocalDate.now()
        val endDate = LocalDate.now()
        val endTimeGreater = LocalDateTime.now()
        val appointmentScheduleEventTypeId = RangeUUID.generate()
        val range = IntRange(0, 99)

        val filter = AppointmentScheduleFilter(
            ids = ids,
            healthPlanTaskId = healthPlanTaskId,
            personId = personId,
            createdAt = createdAt,
            staffId = staffId,
            startTime = startTime,
            startTimeRange = startTimeRange,
            types = types,
            status = status,
            startDate = startDate,
            endDate = endDate,
            endTimeGreater = endTimeGreater,
            appointmentScheduleEventTypeId = appointmentScheduleEventTypeId,
            range = range
        )

        coEvery {
            appointmentScheduleModelDataService.count(
                queryEq {
                    where {
                        this.id.inList(ids) and
                                this.healthPlanTaskId.eq(healthPlanTaskId) and
                                this.personId.eq(personId) and
                                this.staffId.eq(staffId) and
                                this.type.inList(types) and
                                this.status.inList(status) and
                                this.appointmentScheduleEventTypeId.eq(appointmentScheduleEventTypeId) and
                                this.createdAt.greaterEq(createdAt) and
                                (this.startTime.greaterEq(startTime.atStartOfDay()) and this.startTime.lessEq(startTime.atEndOfTheDay())) and
                                (this.startTime.greaterEq(startTimeRange.first) and this.startTime.lessEq(startTimeRange.second)) and
                                this.startTime.greaterEq(startDate.atStartOfDay()) and
                                this.endTime.lessEq(endDate.atEndOfTheDay()) and
                                this.endTime.greaterEq(endTimeGreater)
                    }
                        .offset { range.first }
                        .limit { range.count() }
                }
            )
        } returns 1.success()

        val result = appointmentScheduleService.countBy(filter)
        assertThat(result).isSuccessWithData(1)

        coVerifyOnce { appointmentScheduleModelDataService.count(any()) }
        confirmMocks()
    }

    @Test
    fun `#countBy returns appointment schedules count by only one filter`() = runBlocking {
        val ids = listOf(RangeUUID.generate(), RangeUUID.generate())

        val filter = AppointmentScheduleFilter(ids = ids, sortOrder = SortOrder.Ascending)

        coEvery {
            appointmentScheduleModelDataService.count(
                queryEq {
                    where {
                        this.id.inList(ids)
                    }
                }
            )
        } returns 1.success()

        val result = appointmentScheduleService.countBy(filter)
        assertThat(result).isSuccessWithData(1)

        coVerifyOnce { appointmentScheduleModelDataService.count(any()) }
        confirmMocks()
    }

    @Test
    fun `#countBy returns error when filter is invalid`() = runBlocking {
        val filter = AppointmentScheduleFilter()

        val result = appointmentScheduleService.countBy(filter)
        assertThat(result).isFailureOfType(InvalidArgumentException::class)

        coVerify { appointmentScheduleModelDataService wasNot called }
        confirmMocks()
    }

    @Test
    fun `#existsBy returns if exists appointment schedules by filter`() = runBlocking {
        val ids = listOf(RangeUUID.generate(), RangeUUID.generate())

        val filter = AppointmentScheduleFilter(ids = ids, sortOrder = SortOrder.Ascending)

        coEvery {
            appointmentScheduleModelDataService.exists(
                queryEq {
                    where {
                        this.id.inList(ids)
                    }
                }
            )
        } returns true.success()

        val result = appointmentScheduleService.existsBy(filter)
        assertThat(result).isSuccessWithData(true)

        coVerifyOnce { appointmentScheduleModelDataService.exists(any()) }
        confirmMocks()
    }

    @Test
    fun `#existsBy returns error when filter is invalid`() = runBlocking {
        val filter = AppointmentScheduleFilter()

        val result = appointmentScheduleService.existsBy(filter)
        assertThat(result).isFailureOfType(InvalidArgumentException::class)

        coVerify { appointmentScheduleModelDataService wasNot called }
        confirmMocks()
    }

    @Test
    fun `#countByStaffIds returns appointment schedules count by staff ids`() = runBlocking {
        val staffIds = listOf(RangeUUID.generate(), RangeUUID.generate())
        val status = listOf(AppointmentScheduleStatus.COMPLETED)

        val expected = listOf(
            CountByValues(listOf(staffIds[0].toString()), 1),
            CountByValues(listOf(staffIds[1].toString()), 1)
        )

        coEvery {
            appointmentScheduleModelDataService.countGrouped(
                queryEq {
                    where {
                        this.staffId.inList(staffIds) and this.status.inList(status)
                    }.groupBy { listOf(this.staffId) }
                }
            )
        } returns expected.success()

        val result = appointmentScheduleService.countByStaffIds(staffIds, status)
        assertThat(result).isSuccessWithData(expected)

        coVerifyOnce { appointmentScheduleModelDataService.countGrouped(any()) }
    }

    private fun buildAppointmentScheduleRequestEvent(
        location: String?,
        healthPlanTaskId: String? = null,
        personTaskId: String? = null,
        eventName: String = "Primeira consulta com seu time de saúde",
        appointmentScheduleEventTypeId: UUID? = null,
        cancellation: CalendlyV2WebhookCancellation? = null,
        endTime: LocalDateTime? = startTime.plusHours(1),
        providerUnitId: String? = null
    ) =
        AppointmentScheduleEvent(
            personEmail = person.email,
            staffEmail = staff.email,
            eventId = "C4L3NDLY3V3NT",
            eventName = eventName,
            location = location,
            startTime = startTime,
            endTime = endTime,
            healthPlanTaskId = healthPlanTaskId,
            personTaskId = personTaskId,
            currentUserType = person.userType,
            appointmentScheduleEventTypeId = appointmentScheduleEventTypeId,
            cancellation = cancellation,
            providerUnitId = providerUnitId
        )

}
