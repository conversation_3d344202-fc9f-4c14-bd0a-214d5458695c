package br.com.alice.api.healthcareops.controllers

import br.com.alice.api.healthcareops.api.RoutesTestHelper
import br.com.alice.api.healthcareops.models.CancelCompanyDealRequest
import br.com.alice.common.RangeUUID
import br.com.alice.common.core.exceptions.InvalidArgumentException
import br.com.alice.common.data.dsl.matchers.ResponseAssert
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.data.layer.models.*
import br.com.alice.sales_channel.service.OngoingCompanyDealService
import com.github.kittinunf.result.success
import io.mockk.coEvery
import io.mockk.mockk
import java.time.LocalDateTime
import kotlin.test.BeforeTest
import kotlin.test.Test

class CompanyDealControllerTest : RoutesTestHelper()  {
    private val ongoingCompanyDealService: OngoingCompanyDealService = mockk()

    private val dealDetails = OngoingCompanyDealDetails(
        livesCount = 1
    )
    private val ongoingCompanyDeal = OngoingCompanyDeal(
        id = RangeUUID.generate(),
        name = "ACME",
        salesFirmId= RangeUUID.generate(),
        cnpj = "70.558.172/0001-60",
        salesAgentDocument= "194.097.010-58",
        status= DealStage.RISK_FLOW,
        legalName = "ACME LTDA",
        dealDetails = dealDetails,
        sourceCreatedAt =  LocalDateTime.now(),
        sourceUpdatedAt = LocalDateTime.now(),
        sourceId = "XXXXXXXXXXX",
        channel = DealChannel.BROKER,
        searchTokens = "ACME 70.558.172/0001-60 70558172000160 19409701058",
        companyId = RangeUUID.generate(),
        statusHistory = listOf(),
        version = 4,
        createdAt = LocalDateTime.now(),
        updatedAt = LocalDateTime.now()
    )
    private val cancelCompanyDealRequest = CancelCompanyDealRequest(
        reason = CancelReason.NON_TARGET_RISK,
        description = "cliente achou caro"
    )
    private val statusHistory = listOf(
        OngoingDealStatusHistory(
            previousStatus = DealStage.RISK_FLOW,
            status = DealStage.CANCELED,
            createdAt = LocalDateTime.now()
        )
    )
    private val reactivateOngoingCompanyDeal = ongoingCompanyDeal.copy(status = DealStage.CANCELED, statusHistory = statusHistory)

    @BeforeTest
    override  fun setup() {
        super.setup()

        module.single {
            CompanyDealController(
                ongoingCompanyDealService,
            )
        }
    }

    @Test
    fun `#cancelCompanyDeal - should cancel the company deal`() {
        coEvery { ongoingCompanyDealService.get(any()) } returns ongoingCompanyDeal.success()
        coEvery { ongoingCompanyDealService.cancel(any(), any(), any()) } returns ongoingCompanyDeal.success()

        authenticatedAs(token, staffToBeAuthenticated) {
            put("/company/ongoing_company_deal/${ongoingCompanyDeal.id}/cancel", cancelCompanyDealRequest) { response ->
                ResponseAssert.assertThat(response).isSuccessfulJson()
            }
        }

        coVerifyOnce { ongoingCompanyDealService.get(ongoingCompanyDeal.id) }
        coVerifyOnce { ongoingCompanyDealService.cancel(ongoingCompanyDeal, cancelCompanyDealRequest.reason, cancelCompanyDealRequest.description) }
    }

    @Test
    fun `#cancelCompanyDeal - shouldn't cancel the company deal if the deal is already canceled`() {
        coEvery { ongoingCompanyDealService.get(any()) } returns ongoingCompanyDeal.copy(status = DealStage.CANCELED).success()
        val expected = InvalidArgumentException(code = "deal_already_canceled", message = "The deal was already canceled")

        authenticatedAs(token, staffToBeAuthenticated) {
            put("/company/ongoing_company_deal/${ongoingCompanyDeal.id}/cancel", cancelCompanyDealRequest) { response ->
                ResponseAssert.assertThat(response).withErrorCode(expected.code)
                ResponseAssert.assertThat(response).withFailMessage(expected.message)
            }
        }

        coVerifyOnce { ongoingCompanyDealService.get(ongoingCompanyDeal.id) }
    }

    @Test
    fun `#reactivateCompanyDeal - should reactivate the company deal`() {
        coEvery { ongoingCompanyDealService.get(any()) } returns reactivateOngoingCompanyDeal.success()
        coEvery { ongoingCompanyDealService.reactivate(any()) } returns reactivateOngoingCompanyDeal.success()

        authenticatedAs(token, staffToBeAuthenticated) {
            put("/company/ongoing_company_deal/${reactivateOngoingCompanyDeal.id}/reactivate") { response ->
                ResponseAssert.assertThat(response).isSuccessfulJson()
            }
        }

        coVerifyOnce { ongoingCompanyDealService.get(reactivateOngoingCompanyDeal.id) }
        coVerifyOnce { ongoingCompanyDealService.reactivate(reactivateOngoingCompanyDeal) }
    }

    @Test
    fun `#reactivateCompanyDeal - shouldn't reactivate the company deal if the deal is already active`() {
        coEvery { ongoingCompanyDealService.get(any()) } returns ongoingCompanyDeal.success()
        val expected = InvalidArgumentException("already_active_deal", "The deal is already active")

        authenticatedAs(token, staffToBeAuthenticated) {
            put("/company/ongoing_company_deal/${ongoingCompanyDeal.id}/reactivate") { response ->
                ResponseAssert.assertThat(response).withErrorCode(expected.code)
                ResponseAssert.assertThat(response).withFailMessage(expected.message)
            }
        }

        coVerifyOnce { ongoingCompanyDealService.get(ongoingCompanyDeal.id) }
    }

    @Test
    fun `#reactivateCompanyDeal - shouldn't reactivate the company deal if the deal was cancelled more than 30 days ago`() {
        coEvery { ongoingCompanyDealService.get(any()) } returns reactivateOngoingCompanyDeal.copy(updatedAt = LocalDateTime.now().minusDays(31)).success()
        val expected = InvalidArgumentException("deal_expired", "The deal was cancelled more than 30 days ago")

        authenticatedAs(token, staffToBeAuthenticated) {
            put("/company/ongoing_company_deal/${ongoingCompanyDeal.id}/reactivate") { response ->
                ResponseAssert.assertThat(response).withErrorCode(expected.code)
                ResponseAssert.assertThat(response).withFailMessage(expected.message)
            }
        }

        coVerifyOnce { ongoingCompanyDealService.get(ongoingCompanyDeal.id) }
    }

    @Test
    fun `#reactivateCompanyDeal - shouldn't reactivate the company deal if the deal has no status history`() {
        coEvery { ongoingCompanyDealService.get(any()) } returns reactivateOngoingCompanyDeal.copy(statusHistory = listOf()).success()
        val expected = InvalidArgumentException("deal_has_no_previous_status", "Deal has no previous status")

        authenticatedAs(token, staffToBeAuthenticated) {
            put("/company/ongoing_company_deal/${ongoingCompanyDeal.id}/reactivate") { response ->
                ResponseAssert.assertThat(response).withErrorCode(expected.code)
                ResponseAssert.assertThat(response).withFailMessage(expected.message)
            }
        }

        coVerifyOnce { ongoingCompanyDealService.get(ongoingCompanyDeal.id) }
    }
}
