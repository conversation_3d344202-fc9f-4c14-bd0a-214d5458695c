package br.com.alice.membership.consumers

import br.com.alice.authentication.UserType
import br.com.alice.business.client.BeneficiaryService
import br.com.alice.business.client.CompanyService
import br.com.alice.business.events.BeneficiaryOnboardingPhaseChangedEvent
import br.com.alice.common.core.exceptions.InternalServiceErrorException
import br.com.alice.common.data.dsl.matchers.ResultAssert.Companion.assertThat
import br.com.alice.common.helpers.coVerifyNone
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.common.helpers.returns
import br.com.alice.common.kafka.exceptions.AutoRetryableException
import br.com.alice.communication.crm.analytics.AnalyticsEvent
import br.com.alice.communication.crm.analytics.AnalyticsEventName
import br.com.alice.communication.crm.analytics.AnalyticsTrackerException
import br.com.alice.communication.crm.analytics.AnalyticsTrackerResult
import br.com.alice.communication.crm.analytics.CrmAnalyticsTracker
import br.com.alice.communication.crm.analytics.PersonBusinessUnit
import br.com.alice.communication.crm.analytics.UserProfile
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.Beneficiary
import br.com.alice.data.layer.models.BeneficiaryOnboardingPhaseType
import br.com.alice.data.layer.models.Company
import br.com.alice.data.layer.models.Member
import br.com.alice.data.layer.models.Person
import br.com.alice.data.layer.models.Product
import br.com.alice.data.layer.models.ProductType
import br.com.alice.membership.client.PromoCodeService
import br.com.alice.membership.client.onboarding.HealthDeclarationAppointmentScheduler
import br.com.alice.membership.model.events.AuthenticationSource
import br.com.alice.membership.model.events.ContractPhaseStartedEvent
import br.com.alice.membership.model.events.InviteRequestedEvent
import br.com.alice.membership.model.events.InviteSentEvent
import br.com.alice.membership.model.events.PersonAgeDefinedEvent
import br.com.alice.membership.model.events.PersonRegistrationFinishedEvent
import br.com.alice.membership.model.events.ShoppingFinishedEvent
import br.com.alice.membership.model.events.SignedContractEvent
import br.com.alice.membership.model.events.UserAuthenticatedEvent
import br.com.alice.moneyin.event.InvoicePaidEvent
import br.com.alice.moneyin.event.InvoiceSentEvent
import br.com.alice.person.client.MemberService
import br.com.alice.person.client.PersonService
import br.com.alice.person.client.UserTypeService
import br.com.alice.person.model.events.MemberActivatedEvent
import br.com.alice.person.model.events.MemberCreatedEvent
import br.com.alice.person.model.events.PersonUpdatedEvent
import br.com.alice.product.client.ProductService
import com.github.kittinunf.result.failure
import com.github.kittinunf.result.success
import io.mockk.called
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.confirmVerified
import io.mockk.mockk
import kotlinx.coroutines.runBlocking
import java.time.LocalDateTime
import kotlin.test.Test

class AcquisitionAnalyticsConsumerTest : ConsumerTest() {

    private val personService: PersonService = mockk()
    private val crmAnalyticsTracker: CrmAnalyticsTracker = mockk()
    private val productService: ProductService = mockk()
    private val userTypeService: UserTypeService = mockk()
    private val healthDeclarationAppointmentScheduler: HealthDeclarationAppointmentScheduler = mockk()
    private val promoCodeService: PromoCodeService = mockk()
    private val memberService: MemberService = mockk()
    private val beneficiaryService: BeneficiaryService = mockk()
    private val companyService: CompanyService = mockk()

    private val consumer = AcquisitionAnalyticsConsumer(
        personService,
        crmAnalyticsTracker,
        productService,
        userTypeService,
        healthDeclarationAppointmentScheduler,
        promoCodeService,
        memberService,
        beneficiaryService,
        companyService,
    )

    private val person = TestModelFactory.buildPerson(dateOfBirth = LocalDateTime.now())
    private val userType = UserType.MEMBER
    private val company = TestModelFactory.buildCompany()
    private val beneficiary = TestModelFactory.buildBeneficiary(
        personId = person.id,
        companyId = company.id,
        onboarding = TestModelFactory.buildBeneficiaryOnboarding(
            phases = listOf(TestModelFactory.buildBeneficiaryOnboardingPhase())
        ),
    )

    @Test
    fun `#sendMemberActivatedEvent should return expected response`() = runBlocking {
        val product = TestModelFactory.buildProduct(type = ProductType.B2C)
        val member = TestModelFactory.buildMember(personId = person.id, product = product)

        coEvery { productService.getProduct(product.id) } returns product
        coEvery { personService.get(person.id) } returns person
        coEvery { userTypeService.get(person.id, true) } returns userType

        val userProfile = buildUserProfile(
            userType = userType,
            businessUnit = PersonBusinessUnit.B2C,
            product = product,
        )

        val event = MemberActivatedEvent(member = member)

        val expectedResult = AnalyticsTrackerResult(true)

        val analyticsEvent = AnalyticsEvent(
            name = AnalyticsEventName.MEMBER_ACTIVATED,
            timestamp = member.activationDate ?: event.eventDate
        )

        coEvery {
            crmAnalyticsTracker.updateUserProfileAndSendEvent(
                nationalId = person.nationalId,
                userProfile = userProfile,
                event = analyticsEvent,
            )
        } returns expectedResult

        val response = consumer.sendMemberActivatedEvent(event)
        assertThat(response).isSuccessWithData(expectedResult)
    }

    @Test
    fun `#sendMemberCreatedEvent should return expected response`() = runBlocking {
        val product = TestModelFactory.buildProduct(type = ProductType.B2C)
        val member = TestModelFactory.buildMember(personId = person.id, product = product)

        coEvery { productService.getProduct(product.id) } returns product
        coEvery { personService.get(person.id) } returns person
        coEvery { userTypeService.get(person.id, true) } returns userType

        val userProfile = UserProfile(
            userType = userType.toString(),
            businessUnit = PersonBusinessUnit.B2C,
            selectedProduct = product.title,
            copaymentType = product.coPayment.name,
            refundType = product.refund.name
        )

        val event = MemberCreatedEvent(payload = member)

        val expectedResult = AnalyticsTrackerResult(true)

        val analyticsEvent = AnalyticsEvent(
            name = AnalyticsEventName.MEMBER_CREATED,
            timestamp = member.createdAt
        )

        coEvery {
            crmAnalyticsTracker.updateUserProfileAndSendEvent(
                nationalId = person.nationalId,
                userProfile = userProfile,
                event = analyticsEvent,
            )
        } returns expectedResult

        val response = consumer.sendMemberCreatedEvent(event)
        assertThat(response).isSuccessWithData(expectedResult)
    }

    @Test
    fun `#sendUserAuthenticatedEvent should return expected response`() = runBlocking {
        val member = TestModelFactory.buildMember(
            personId = person.id,
            externalBrandAccountNumber = "externalBrandAccountNumber"
        )
        val event = UserAuthenticatedEvent(
            personId = person.id,
            source = AuthenticationSource.EMAIL,
            claims = mapOf("CLAIM1" to "VALUE1")
        )

        coEvery { personService.get(event.payload.personId) } returns person
        coEvery { memberService.getCurrent(event.payload.personId) } returns member
        coEvery { userTypeService.get(event.payload.personId, refreshed = true) } returns userType
        coEvery {
            beneficiaryService.findByPersonId(
                event.payload.personId,
                BeneficiaryService.FindOptions(withOnboarding = true)
            )
        } returns beneficiary
        coEvery { companyService.get(beneficiary.companyId) } returns company

        val userProfile = buildUserProfile(
            person,
            userType,
            member = member,
            company = company,
            beneficiary = beneficiary,
        )

        val analyticsEvent = AnalyticsEvent(
            name = AnalyticsEventName.USER_AUTHENTICATED,
            timestamp = event.eventDate,
            properties = event.payload.claims + mapOf("source" to event.payload.source.toString())
        )

        val expectedResult = AnalyticsTrackerResult(true)

        coEvery {
            crmAnalyticsTracker.updateUserProfileAndSendEvent(
                nationalId = person.nationalId,
                userProfile = userProfile,
                event = analyticsEvent,
            )
        } returns expectedResult

        val response = consumer.sendUserAuthenticatedEvent(event)
        assertThat(response).isSuccessWithData(expectedResult)

        coVerifyOnce { personService.get(any()) }
        coVerifyOnce { memberService.getCurrent(any()) }
        coVerifyOnce { userTypeService.get(any(), any()) }
        coVerifyOnce { beneficiaryService.findByPersonId(any(), any()) }
        coVerifyOnce { companyService.get(any()) }
        coVerifyOnce { crmAnalyticsTracker.updateUserProfileAndSendEvent(any(), any(), any()) }
        coVerify { productService wasNot called }
        coVerify { healthDeclarationAppointmentScheduler wasNot called }
        coVerify { promoCodeService wasNot called }
        confirmVerified(
            personService,
            crmAnalyticsTracker,
            productService,
            userTypeService,
            healthDeclarationAppointmentScheduler,
            promoCodeService,
            memberService,
            beneficiaryService,
            companyService
        )
    }

    @Test
    fun `#sendUserAuthenticatedEvent should return auto retryable when the InternalServiceErrorException is thrown`() =
        runBlocking {
            val event = UserAuthenticatedEvent(
                personId = person.id,
                source = AuthenticationSource.EMAIL,
                claims = mapOf("CLAIM1" to "VALUE1")
            )

            coEvery { personService.get(event.payload.personId) } returns InternalServiceErrorException("")

            val response = consumer.sendUserAuthenticatedEvent(event)

            assertThat(response).isFailureOfType(AutoRetryableException::class)

            coVerifyOnce { personService.get(any()) }
            coVerifyNone {
                memberService wasNot called
                userTypeService wasNot called
                beneficiaryService wasNot called
                companyService wasNot called
                crmAnalyticsTracker wasNot called
                productService wasNot called
                healthDeclarationAppointmentScheduler wasNot called
                promoCodeService wasNot called
            }

            confirmVerified(
                personService,
                crmAnalyticsTracker,
                productService,
                userTypeService,
                healthDeclarationAppointmentScheduler,
                promoCodeService,
                memberService,
                beneficiaryService,
                companyService
            )
        }

    @Test
    fun `#sendInsurancePortabilityApprovedEvent should send INSURANCE_PORTABILITY_APPROVED event`() =
        runBlocking {
            val event = ContractPhaseStartedEvent(person.id)

            val analyticsEvent = AnalyticsEvent(
                name = AnalyticsEventName.CONTRACT_RECEIVED,
                timestamp = event.payload.eventDate
            )

            val expectedResult = AnalyticsTrackerResult(true)

            coEvery { crmAnalyticsTracker.sendEvent(person.nationalId, analyticsEvent) } returns expectedResult
            coEvery { personService.get(person.id) } returns person

            val response = consumer.sendContractReceivedEvent(event)

            assertThat(response).isSuccessWithData(expectedResult)
        }

    @Test
    fun `#sendContractSignedEvent should return expected response`() = runBlocking {

        val contract = TestModelFactory.buildOnboardingContract(personId = person.id).sendSignature()
        val product = TestModelFactory.buildProduct()
        coEvery { productService.getProduct(contract.productId) } returns product
        coEvery { personService.get(contract.personId) } returns person
        coEvery { userTypeService.get(contract.personId) } returns userType

        val userProfile = UserProfile(
            selectedProduct = product.title,
            copaymentType = product.coPayment.name,
            refundType = product.refund.name,
            userType = userType.toString(),
        )

        val analyticsEvent = AnalyticsEvent(
            name = AnalyticsEventName.CONTRACT_SIGNED,
            timestamp = contract.signatureSentAt!!
        )

        val expectedResult = AnalyticsTrackerResult(true)

        coEvery {
            crmAnalyticsTracker.updateUserProfileAndSendEvent(
                nationalId = person.nationalId,
                userProfile = userProfile,
                event = analyticsEvent,
            )
        } returns expectedResult

        val event = SignedContractEvent(contract)

        val response = consumer.sendContractSignedEvent(event)
        assertThat(response).isSuccessWithData(expectedResult)
    }

    @Test
    fun `#sendRegistrationFinishedEvent should return expected response`() = runBlocking {

        coEvery { healthDeclarationAppointmentScheduler.getScheduleUrl(person.id) } returns "www.example.com"
        coEvery { userTypeService.get(person.id) } returns userType

        val event = PersonRegistrationFinishedEvent(person)

        val updatedUserProfile = UserProfile(
            nickName = person.nickName,
            biologicalSex = person.sex,
            gender = person.gender,
            isTest = person.isTest,
            userType = userType.toString()
        )

        val analyticsEvent = AnalyticsEvent(
            name = AnalyticsEventName.REGISTRATION_FINISHED,
            timestamp = event.eventDate,
            properties = mapOf(
                "health_declaration_appointment_schedule_url" to "www.example.com".success()
            )
        )

        val expectedResult = AnalyticsTrackerResult(true)

        coEvery {
            crmAnalyticsTracker.updateUserProfileAndSendEvent(
                nationalId = person.nationalId,
                userProfile = updatedUserProfile,
                event = analyticsEvent,
            )
        } returns expectedResult

        val response = consumer.sendRegistrationFinishedEvent(event)

        assertThat(response).isSuccessWithData(expectedResult)
    }

    @Test
    fun `#sendPersonAgeDefinedEvent should return expected response`(): Unit = runBlocking {
        val event = PersonAgeDefinedEvent(person)

        val updatedUserProfile = UserProfile(
            age = person.age.toString(),
            dateOfBirth = person.dateOfBirth!!.toLocalDate()
        )

        val analyticsEvent = AnalyticsEvent(
            name = AnalyticsEventName.AGE_DEFINED,
            timestamp = event.eventDate
        )

        val expectedResult = AnalyticsTrackerResult(true)

        coEvery {
            crmAnalyticsTracker.updateUserProfileAndSendEvent(
                nationalId = person.nationalId,
                userProfile = updatedUserProfile,
                event = analyticsEvent,
            )
        } returns expectedResult
    }

    @Test
    fun `#sendInviteRequestedEvent should return expected response`() = runBlocking {

        val lead = TestModelFactory.buildLead()

        val event = InviteRequestedEvent(lead)

        val userProfile = UserProfile(
            firstName = lead.firstName,
            lastName = lead.lastName,
            email = lead.email,
            dateOfBirth = lead.dateOfBirth?.toLocalDate(),
            utmSource = lead.trackingInfo?.utmSource,
            utmCampaign = lead.trackingInfo?.utmCampaign,
            utmContent = lead.trackingInfo?.utmContent,
            utmMedium = lead.trackingInfo?.utmMedium,
            utmTerm = lead.trackingInfo?.utmTerm,
            allowContact = lead.authorizeCommunication,
            promoCode = null,
            userType = UserType.NON_MEMBER.toString(),
        )

        val analyticsEvent = AnalyticsEvent(
            name = AnalyticsEventName.INVITE_REQUESTED,
            timestamp = lead.createdAt
        )

        val expectedResult = AnalyticsTrackerResult(true)

        coEvery {
            crmAnalyticsTracker.updateUserProfileAndSendEvent(
                nationalId = lead.nationalId!!,
                userProfile = userProfile,
                event = analyticsEvent,
            )
        } returns expectedResult

        val response = consumer.sendInviteRequestedEvent(event)

        assertThat(response).isSuccessWithData(expectedResult)
    }

    @Test
    fun `#sendInviteSentEvent should return expected response`() = runBlocking {

        val lead = TestModelFactory.buildLead(
            invitedAt = LocalDateTime.now(),
            nationalId = person.nationalId,
        )

        val event = InviteSentEvent(lead, person)

        val analyticsEvent = AnalyticsEvent(
            name = AnalyticsEventName.INVITE_SENT,
            timestamp = lead.invitedAt!!
        )

        val expectedResult = AnalyticsTrackerResult(true)

        coEvery {
            crmAnalyticsTracker.sendEvent(
                nationalId = lead.nationalId!!,
                event = analyticsEvent,
            )
        } returns expectedResult

        val response = consumer.sendInviteSentEvent(event)

        assertThat(response).isSuccessWithData(expectedResult)
    }

    @Test
    fun `#sendInviteSentEvent should return error`() = runBlocking {
        val lead = TestModelFactory.buildLead(
            invitedAt = LocalDateTime.now(),
            nationalId = person.nationalId,
        )

        val event = InviteSentEvent(lead, person)

        val analyticsEvent = AnalyticsEvent(
            name = AnalyticsEventName.INVITE_SENT,
            timestamp = lead.invitedAt!!
        )

        coEvery {
            crmAnalyticsTracker.sendEvent(
                nationalId = lead.nationalId!!,
                event = analyticsEvent,
            )
        } throws Exception()

        val response = consumer.sendInviteSentEvent(event)

        assertThat(response).isFailureOfType(Exception::class)
    }

    @Test
    fun `#updateUserProfile should return expected response for Person with Product B2C`() = runBlocking {
        val userType = UserType.NON_MEMBER
        val product = TestModelFactory.buildProduct(type = ProductType.B2C)
        val member = TestModelFactory.buildMember(product = product)

        coEvery { userTypeService.get(person.id, refreshed = true) } returns userType
        coEvery { productService.getProduct(product.id) } returns product
        coEvery { memberService.getCurrent(person.id) } returns member
        coEvery {
            beneficiaryService.findByPersonId(
                person.id,
                BeneficiaryService.FindOptions(withOnboarding = true)
            )
        } returns beneficiary
        coEvery { companyService.get(beneficiary.companyId) } returns company

        val userProfile = buildUserProfile(
            person,
            userType,
            PersonBusinessUnit.B2C,
            product,
            company = company,
            beneficiary = beneficiary
        )

        val expectedResult = AnalyticsTrackerResult(true)

        coEvery {
            crmAnalyticsTracker.createOrUpdateUserProfile(
                nationalId = person.nationalId,
                userProfile = userProfile
            )
        } returns expectedResult

        val event = PersonUpdatedEvent(person)
        val response = consumer.updateUserProfile(event)

        assertThat(response).isSuccessWithData(expectedResult)
    }

    @Test
    fun `#updateUserProfile should return expected response for Person with Product B2B`() = runBlocking {
        val userType = UserType.NON_MEMBER
        val product = TestModelFactory.buildProduct(type = ProductType.B2B)
        val member = TestModelFactory.buildMember(product = product)

        coEvery { userTypeService.get(person.id, refreshed = true) } returns userType
        coEvery { productService.getProduct(product.id) } returns product
        coEvery { memberService.getCurrent(person.id) } returns member
        coEvery {
            beneficiaryService.findByPersonId(
                person.id,
                BeneficiaryService.FindOptions(withOnboarding = true)
            )
        } returns beneficiary
        coEvery { companyService.get(beneficiary.companyId) } returns company

        val anyTagsPerson = person.copy(tags = listOf("whatever", "tags"))

        val userProfile = buildUserProfile(
            anyTagsPerson,
            userType,
            PersonBusinessUnit.B2B,
            product,
            company = company,
            beneficiary = beneficiary
        )

        val expectedResult = AnalyticsTrackerResult(true)

        coEvery {
            crmAnalyticsTracker.createOrUpdateUserProfile(
                nationalId = person.nationalId,
                userProfile = userProfile
            )
        } returns expectedResult

        val event = PersonUpdatedEvent(anyTagsPerson)
        val response = consumer.updateUserProfile(event)

        assertThat(response).isSuccessWithData(expectedResult)
    }

    @Test
    fun `#updateUserProfile should return expected response when the createOrUpdateUserProfile returns the AnalyticsTrackerException`() =
        runBlocking {
            val userType = UserType.NON_MEMBER
            val product = TestModelFactory.buildProduct(type = ProductType.B2B)
            val member = TestModelFactory.buildMember(product = product)

            coEvery { userTypeService.get(person.id, refreshed = true) } returns userType
            coEvery { productService.getProduct(product.id) } returns product
            coEvery { memberService.getCurrent(person.id) } returns member
            coEvery {
                beneficiaryService.findByPersonId(
                    person.id,
                    BeneficiaryService.FindOptions(withOnboarding = true)
                )
            } returns beneficiary
            coEvery { companyService.get(beneficiary.companyId) } returns company

            val anyTagsPerson = person.copy(tags = listOf("whatever", "tags"))

            val userProfile = buildUserProfile(
                anyTagsPerson,
                userType,
                PersonBusinessUnit.B2B,
                product,
                company = company,
                beneficiary = beneficiary
            )

            coEvery {
                crmAnalyticsTracker.createOrUpdateUserProfile(
                    nationalId = person.nationalId,
                    userProfile = userProfile
                )
            } throws (AnalyticsTrackerException(""))

            val event = PersonUpdatedEvent(anyTagsPerson)
            val response = consumer.updateUserProfile(event)

            assertThat(response).isSuccessWithData(true)
        }

    @Test
    fun `#sendShoppingFinishedEvent should send finished event to CRM`() = runBlocking {
        val product = TestModelFactory.buildProduct()
        val productOrder = TestModelFactory.buildProductOrder(person.id, product = product)
        val expectedResult = AnalyticsTrackerResult(true)

        coEvery { productService.getProduct(productOrder.productId) } returns product
        coEvery { personService.get(person.id) } returns person

        coEvery {
            crmAnalyticsTracker.sendEvent(
                nationalId = person.nationalId,
                event = AnalyticsEvent(
                    name = AnalyticsEventName.SHOPPING_FINISHED,
                    timestamp = productOrder.updatedAt,
                    properties = mapOf(
                        "selected_product" to product.title
                    )
                )
            )
        } returns expectedResult

        val event = ShoppingFinishedEvent(productOrder)
        val response = consumer.sendShoppingFinishedEvent(event)

        assertThat(response).isSuccessWithData(expectedResult)
    }

    @Test
    fun `#sendBeneficiaryOnboardingPhaseChangedEvent should send phase changed event to CRM`() = runBlocking {
        val newPhase = BeneficiaryOnboardingPhaseType.HEALTH_DECLARATION
        val beneficiaryOnboardingPhase = TestModelFactory.buildBeneficiaryOnboardingPhase(phase = newPhase)
        val expectedResult = AnalyticsTrackerResult(true)

        coEvery {
            beneficiaryService.get(
                beneficiary.id,
                BeneficiaryService.FindOptions(withOnboarding = true)
            )
        } returns beneficiary
        coEvery { companyService.get(beneficiary.companyId) } returns company
        coEvery { personService.get(beneficiary.personId) } returns person

        val event = BeneficiaryOnboardingPhaseChangedEvent(
            beneficiaryId = beneficiary.id,
            newPhase = beneficiaryOnboardingPhase,
        )

        val userProfile = UserProfile(
            beneficiaryCompanyName = company.name,
            beneficiaryActivatedAt = beneficiary.activatedAt,
            beneficiaryFlow = beneficiary.onboarding?.flowType,
            beneficiaryPhase = beneficiary.onboarding?.currentPhase?.phase,
        )

        coEvery {
            crmAnalyticsTracker.updateUserProfileAndSendEvent(
                nationalId = person.nationalId,
                userProfile = userProfile,
                event = AnalyticsEvent(
                    name = AnalyticsEventName.BENEFICIARY_ONBOARDING_PHASE_CHANGED,
                    timestamp = event.eventDate,
                ),
            )
        } returns expectedResult

        val response = consumer.sendBeneficiaryOnboardingPhaseChangedEvent(event)
        assertThat(response).isSuccessWithData(expectedResult)
    }

    @Test
    fun `#sendInvoiceSentEvent should return expected response`() = runBlocking {
        val member = TestModelFactory.buildMember(person.id)
        val invoice = TestModelFactory.buildMemberInvoice(
            member,
            dueDate = LocalDateTime.of(2023, 2, 1, 0, 0)
        )

        val event = InvoiceSentEvent(person, invoice)

        val analyticsEvent = AnalyticsEvent(
            name = AnalyticsEventName.INVOICE_SENT,
            properties = mapOf(
                "month" to "Fevereiro",
                "formatted_date" to "01/02",
            )
        )

        val expectedResult = AnalyticsTrackerResult(true)

        coEvery {
            crmAnalyticsTracker.sendEvent(
                nationalId = person.nationalId,
                event = match { it.properties == analyticsEvent.properties && it.name == analyticsEvent.name },
            )
        } returns expectedResult

        coEvery { personService.get(person.id) } returns person

        val response = consumer.sendInvoiceSentEvent(event)
        assertThat(response).isSuccessWithData(expectedResult)
    }

    @Test
    fun `#sendInvoicePaidEvent should return expected response`() = runBlocking {
        val member = TestModelFactory.buildMember(person.id)
        val invoice = TestModelFactory.buildMemberInvoice(member)

        val event = InvoicePaidEvent(invoice)

        val analyticsEvent = AnalyticsEvent(
            name = AnalyticsEventName.INVOICE_PAID,
            timestamp = event.eventDate,
            properties = mapOf(
                "due_date" to invoice.dueDate
            )
        )

        val expectedResult = AnalyticsTrackerResult(true)

        coEvery {
            crmAnalyticsTracker.sendEvent(
                nationalId = person.nationalId,
                event = analyticsEvent,
            )
        } returns expectedResult

        coEvery { personService.get(person.id) } returns person

        val response = consumer.sendInvoicePaidEvent(event)
        assertThat(response).isSuccessWithData(expectedResult)
    }

    @Test
    fun `#sendContractReceivedEvent should send CONTRACT_RECEIVED event`() = runBlocking {
        val event = ContractPhaseStartedEvent(person.id)

        val analyticsEvent = AnalyticsEvent(
            name = AnalyticsEventName.CONTRACT_RECEIVED,
            timestamp = event.payload.eventDate
        )

        val expectedResult = AnalyticsTrackerResult(true)

        coEvery { crmAnalyticsTracker.sendEvent(person.nationalId, analyticsEvent) } returns expectedResult
        coEvery { personService.get(person.id) } returns person

        val response = consumer.sendContractReceivedEvent(event)

        assertThat(response).isSuccessWithData(expectedResult)
    }

    private fun buildUserProfile(
        person: Person? = null,
        userType: UserType? = null,
        businessUnit: PersonBusinessUnit? = null,
        product: Product? = null,
        member: Member? = null,
        company: Company? = null,
        beneficiary: Beneficiary? = null
    ) = UserProfile(
        firstName = person?.firstName,
        socialName = person?.socialName,
        socialFirstName = person?.socialFirstName,
        socialLastName = person?.socialLastName,
        lastName = person?.lastName,
        email = person?.email,
        dateOfBirth = person?.dateOfBirth?.toLocalDate(),
        biologicalSex = person?.sex,
        gender = person?.gender,
        isTest = person?.isTest,
        nickName = person?.nickName,
        phoneNumber = person?.phoneNumber,
        city = person?.addresses?.firstOrNull()?.city,
        tags = person?.tags,
        userType = userType.toString(),
        businessUnit = businessUnit,
        selectedProduct = product?.title,
        copaymentType = product?.coPayment?.name,
        refundType = product?.refund?.name,
        beneficiaryCompanyName = company?.name,
        beneficiaryActivatedAt = beneficiary?.activatedAt,
        beneficiaryFlow = beneficiary?.onboarding?.flowType,
        beneficiaryPhase = beneficiary?.onboarding?.currentPhase?.phase,
        externalBrandAccountNumber = member?.externalBrandAccountNumber
    )
}
