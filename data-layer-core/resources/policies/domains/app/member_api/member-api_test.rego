package app.member_api_test

import rego.v1

import data.app.member_api

test_unauth_view_models_allowed if {
    {1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18} == member_api.allow with input as {
        "cases": [
            { "index": 1, "action": "view", "subject": { "opaType": "Unauthenticated" }, "resource": { "opaType": "InvoicePaymentModel" } },
            { "index": 2, "action": "view", "subject": { "opaType": "Unauthenticated" }, "resource": { "opaType": "BolepixPaymentDetailModel" } },
            { "index": 3, "action": "view", "subject": { "opaType": "Unauthenticated" }, "resource": { "opaType": "BoletoPaymentDetailModel" } },
            { "index": 4, "action": "view", "subject": { "opaType": "Unauthenticated" }, "resource": { "opaType": "PixPaymentDetailModel" } },
            { "index": 5, "action": "view", "subject": { "opaType": "Unauthenticated" }, "resource": { "opaType": "SimpleCreditCardPaymentDetailModel" } },
            { "index": 6, "action": "view", "subject": { "opaType": "Unauthenticated" }, "resource": { "opaType": "PaymentDetailModel" } },
            { "index": 7, "action": "view", "subject": { "opaType": "Unauthenticated" }, "resource": { "opaType": "BillingAccountablePartyModel" } },
            { "index": 8, "action": "view", "subject": { "opaType": "Unauthenticated" }, "resource": { "opaType": "BeneficiaryModel" } },
            { "index": 9, "action": "view", "subject": { "opaType": "Unauthenticated" }, "resource": { "opaType": "BeneficiaryOnboardingModel" } },
            { "index": 10, "action": "view", "subject": { "opaType": "Unauthenticated" }, "resource": { "opaType": "UpdateAppRuleModel" } },
            { "index": 11, "action": "view", "subject": { "opaType": "Unauthenticated" }, "resource": { "opaType": "BeneficiaryOnboardingPhaseModel" } },
            { "index": 12, "action": "view", "subject": { "opaType": "Unauthenticated" }, "resource": { "opaType": "Lead" } },
            { "index": 13, "action": "view", "subject": { "opaType": "Unauthenticated" }, "resource": { "opaType": "Opportunity" } },
            { "index": 14, "action": "view", "subject": { "opaType": "Unauthenticated" }, "resource": { "opaType": "HealthProductSimulation" } },
            { "index": 15, "action": "view", "subject": { "opaType": "Unauthenticated" }, "resource": { "opaType": "MemberProductChangeScheduleModel" } },
            { "index": 16, "action": "view", "subject": { "opaType": "Unauthenticated" }, "resource": { "opaType": "AliceAgoraWorkingHours" } },
            { "index": 17, "action": "view", "subject": { "opaType": "Unauthenticated" }, "resource": { "opaType": "StaffModel" } },
            { "index": 18, "action": "view", "subject": { "opaType": "Unauthenticated" }, "resource": { "opaType": "ZendeskExternalReference" } }
        ]
    }
}


test_unauth_count_models_allowed if {
    1 in member_api.allow with input as {
        "cases": [
            { "index": 1, "action": "count", "subject": { "opaType": "Unauthenticated" }, "resource": { "opaType": "PersonModel" } }
        ]
    }
}

test_unauth_view_person_model_doing_login_allowed if {
    {1, 2} == member_api.allow with input as {
        "cases": [
            { "index": 1, "action": "view", "subject": { "opaType": "Unauthenticated", "key": "123" }, "resource": { "opaType": "PersonModel", "id": "123" } },
            { "index": 2, "action": "view", "subject": { "opaType": "Unauthenticated", "key": "123" }, "resource": { "opaType": "PersonModel", "nationalId": "123" } }
        ]
    }
}

test_unauth_view_her_own_member_model_allowed if {
    1 in member_api.allow with input as {
        "cases": [
            { "index": 1, "action": "view", "subject": { "opaType": "Unauthenticated", "key": "123" }, "resource": { "opaType": "MemberModel", "personId": "123" } }
        ]
    }
}

test_unauth_view_her_own_device_model_allowed if {
    1 in member_api.allow with input as {
        "cases": [
            { "index": 1, "action": "view", "subject": { "opaType": "Unauthenticated", "key": "123" }, "resource": { "opaType": "DeviceModel", "personId": "123" } }
        ]
    }
}

test_unauth_view_her_own_person_internal_reference_model_allowed if {
    1 in member_api.allow with input as {
        "cases": [
            { "index": 1, "action": "view", "subject": { "opaType": "Unauthenticated", "key": "123" }, "resource": { "opaType": "PersonInternalReference", "channelPersonId": "123" } }
        ]
    }
}

test_unauth_view_create_update_her_own_person_login_model_to_login_allowed if {
    {1, 2, 3, 4, 5, 6, 7, 8, 9} == member_api.allow with input as {
        "cases": [
            { "index": 1, "action": "view", "subject": { "opaType": "Unauthenticated", "key": "123" }, "resource": { "opaType": "PersonLoginModel", "nationalId": "123" } },
            { "index": 2, "action": "view", "subject": { "opaType": "Unauthenticated", "key": "<EMAIL>" }, "resource": { "opaType": "PersonLoginModel", "email": "<EMAIL>" } },
            { "index": 3, "action": "view", "subject": { "opaType": "Unauthenticated", "key": "123" }, "resource": { "opaType": "PersonLoginModel", "personId": "123" } },

            { "index": 4, "action": "create", "subject": { "opaType": "Unauthenticated", "key": "123" }, "resource": { "opaType": "PersonLoginModel", "nationalId": "123" } },
            { "index": 5, "action": "create", "subject": { "opaType": "Unauthenticated", "key": "<EMAIL>" }, "resource": { "opaType": "PersonLoginModel", "email": "<EMAIL>" } },
            { "index": 6, "action": "create", "subject": { "opaType": "Unauthenticated", "key": "123" }, "resource": { "opaType": "PersonLoginModel", "personId": "123" } },

            { "index": 7, "action": "update", "subject": { "opaType": "Unauthenticated", "key": "123" }, "resource": { "opaType": "PersonLoginModel", "nationalId": "123" } },
            { "index": 8, "action": "update", "subject": { "opaType": "Unauthenticated", "key": "<EMAIL>" }, "resource": { "opaType": "PersonLoginModel", "email": "<EMAIL>" } },
            { "index": 9, "action": "update", "subject": { "opaType": "Unauthenticated", "key": "123" }, "resource": { "opaType": "PersonLoginModel", "personId": "123" } }
        ]
    }
}

test_unauth_view_create_update_her_own_person_model_to_login_allowed if {
    {1, 2, 3, 4, 5, 6, 7, 8, 9} == member_api.allow with input as {
        "cases": [
            { "index": 1, "action": "view", "subject": { "opaType": "Unauthenticated", "key": "123" }, "resource": { "opaType": "PersonModel", "nationalId": "123" } },
            { "index": 2, "action": "view", "subject": { "opaType": "Unauthenticated", "key": "<EMAIL>" }, "resource": { "opaType": "PersonModel", "email": "<EMAIL>" } },
            { "index": 3, "action": "view", "subject": { "opaType": "Unauthenticated", "key": "123" }, "resource": { "opaType": "PersonModel", "personId": "123" } },

            { "index": 4, "action": "create", "subject": { "opaType": "Unauthenticated", "key": "123" }, "resource": { "opaType": "PersonModel", "nationalId": "123" } },
            { "index": 5, "action": "create", "subject": { "opaType": "Unauthenticated", "key": "<EMAIL>" }, "resource": { "opaType": "PersonModel", "email": "<EMAIL>" } },
            { "index": 6, "action": "create", "subject": { "opaType": "Unauthenticated", "key": "123" }, "resource": { "opaType": "PersonModel", "personId": "123" } },

            { "index": 7, "action": "update", "subject": { "opaType": "Unauthenticated", "key": "123" }, "resource": { "opaType": "PersonModel", "nationalId": "123" } },
            { "index": 8, "action": "update", "subject": { "opaType": "Unauthenticated", "key": "<EMAIL>" }, "resource": { "opaType": "PersonModel", "email": "<EMAIL>" } },
            { "index": 9, "action": "update", "subject": { "opaType": "Unauthenticated", "key": "123" }, "resource": { "opaType": "PersonModel", "personId": "123" } }
        ]
    }
}

test_unauth_view_create_update_models_allowed if {
    {1, 2, 3, 4, 5, 6} == member_api.allow with input as {
        "cases": [
            { "index": 1, "action": "view", "subject": { "opaType": "Unauthenticated" }, "resource": { "opaType": "LegalGuardianInfoTempModel" } },
            { "index": 2, "action": "create", "subject": { "opaType": "Unauthenticated" }, "resource": { "opaType": "LegalGuardianInfoTempModel" } },
            { "index": 3, "action": "update", "subject": { "opaType": "Unauthenticated" }, "resource": { "opaType": "LegalGuardianInfoTempModel" } },

            { "index": 4, "action": "view", "subject": { "opaType": "Unauthenticated" }, "resource": { "opaType": "PersonOnboardingModel" } },
            { "index": 5, "action": "create", "subject": { "opaType": "Unauthenticated" }, "resource": { "opaType": "PersonOnboardingModel" } },
            { "index": 6, "action": "update", "subject": { "opaType": "Unauthenticated" }, "resource": { "opaType": "PersonOnboardingModel" } }
        ]
    }
}

test_unauth_view_create_update_her_own_models_allowed if {
    {1, 2, 3, 4, 5, 6, 7, 8, 9} == member_api.allow with input as {
        "cases": [
            { "index": 1, "action": "view", "subject": { "opaType": "Unauthenticated", "key": "123" }, "resource": { "opaType": "PersonInternalReference", "personId": "123" } },
            { "index": 2, "action": "create", "subject": { "opaType": "Unauthenticated", "key": "123" }, "resource": { "opaType": "PersonInternalReference", "personId": "123" } },
            { "index": 3, "action": "update", "subject": { "opaType": "Unauthenticated", "key": "123" }, "resource": { "opaType": "PersonInternalReference", "personId": "123" } },

            { "index": 4, "action": "view", "subject": { "opaType": "Unauthenticated", "key": "123" }, "resource": { "opaType": "HealthDeclaration", "personId": "123" } },
            { "index": 5, "action": "create", "subject": { "opaType": "Unauthenticated", "key": "123" }, "resource": { "opaType": "HealthDeclaration", "personId": "123" } },
            { "index": 6, "action": "update", "subject": { "opaType": "Unauthenticated", "key": "123" }, "resource": { "opaType": "HealthDeclaration", "personId": "123" } },

            { "index": 7, "action": "view", "subject": { "opaType": "Unauthenticated", "key": "123" }, "resource": { "opaType": "PersonRegistrationModel", "personId": "123" } },
            { "index": 8, "action": "create", "subject": { "opaType": "Unauthenticated", "key": "123" }, "resource": { "opaType": "PersonRegistrationModel", "personId": "123" } },
            { "index": 9, "action": "update", "subject": { "opaType": "Unauthenticated", "key": "123" }, "resource": { "opaType": "PersonRegistrationModel", "personId": "123" } }
        ]
    }
}

test_person_subject_count_models_allowed if {
    {1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11} == member_api.allow with input as {
        "cases": [
            { "index": 1, "action": "count", "subject": { "opaType": "PersonSubject" }, "resource": { "opaType": "AppointmentScheduleOptionModel" } },
            { "index": 2, "action": "count", "subject": { "opaType": "PersonSubject" }, "resource": { "opaType": "AppContentScreenDetail" } },
            { "index": 3, "action": "count", "subject": { "opaType": "PersonSubject" }, "resource": { "opaType": "HealthCommunitySpecialistModel" } },
            { "index": 4, "action": "count", "subject": { "opaType": "PersonSubject" }, "resource": { "opaType": "BeneficiaryModel" } },
            { "index": 5, "action": "count", "subject": { "opaType": "PersonSubject" }, "resource": { "opaType": "HealthFormSection" } },
            { "index": 6, "action": "count", "subject": { "opaType": "PersonSubject" }, "resource": { "opaType": "HealthFormQuestion" } },
            { "index": 7, "action": "count", "subject": { "opaType": "PersonSubject" }, "resource": { "opaType": "HealthFormQuestionAnswer" } },
            { "index": 8, "action": "count", "subject": { "opaType": "PersonSubject" }, "resource": { "opaType": "LegalGuardianInfoTempModel" } },
            { "index": 9, "action": "count", "subject": { "opaType": "PersonSubject" }, "resource": { "opaType": "ClinicalOutcomeRecord" } },
            { "index": 10, "action": "count", "subject": { "opaType": "PersonSubject" }, "resource": { "opaType": "ActionPlanTask" } },
            { "index": 11, "action": "count", "subject": { "opaType": "PersonSubject" }, "resource": { "opaType": "DemandActionPlan" } }
        ]
    }
}

test_person_subject_view_models_allowed_pt1 if {
    {1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31,
    32, 33, 34, 35, 36, 37, 38, 39, 40} == member_api.allow with input as {
        "cases": [
            {"index": 1, "action": "view", "subject": {"opaType": "PersonSubject"}, "resource": {"opaType": "HealthConditionGroup"}},
            {"index": 2, "action": "view", "subject": {"opaType": "PersonSubject"}, "resource": {"opaType": "AppContentScreenDetail"}},
            {"index": 3, "action": "view", "subject": {"opaType": "PersonSubject"}, "resource": {"opaType": "InvoicePaymentModel"}},
            {"index": 4, "action": "view", "subject": {"opaType": "PersonSubject"}, "resource": {"opaType": "BoletoPaymentDetailModel"}},
            {"index": 5, "action": "view", "subject": {"opaType": "PersonSubject"}, "resource": {"opaType": "SimpleCreditCardPaymentDetailModel"}},
            {"index": 6, "action": "view", "subject": {"opaType": "PersonSubject"}, "resource": {"opaType": "PixPaymentDetailModel"}},
            {"index": 7, "action": "view", "subject": {"opaType": "PersonSubject"}, "resource": {"opaType": "BolepixPaymentDetailModel"}},
            {"index": 8, "action": "view", "subject": {"opaType": "PersonSubject"}, "resource": {"opaType": "InvoiceLiquidationModel"}},
            {"index": 9, "action": "view", "subject": {"opaType": "PersonSubject"}, "resource": {"opaType": "ChannelFup"}},
            {"index": 10, "action": "view", "subject": {"opaType": "PersonSubject"}, "resource": {"opaType": "PromoCodeModel"}},

            {"index": 11, "action": "view", "subject": {"opaType": "PersonSubject"}, "resource": {"opaType": "ProviderModel"}},
            {"index": 12, "action": "view", "subject": {"opaType": "PersonSubject"}, "resource": {"opaType": "ProviderUnitModel"}},
            {"index": 13, "action": "view", "subject": {"opaType": "PersonSubject"}, "resource": {"opaType": "ConsolidatedAccreditedNetwork"}},
            {"index": 14, "action": "view", "subject": {"opaType": "PersonSubject"}, "resource": {"opaType": "ConsolidatedRating"}},
            {"index": 15, "action": "view", "subject": {"opaType": "PersonSubject"}, "resource": {"opaType": "StructuredAddress"}},
            {"index": 16, "action": "view", "subject": {"opaType": "PersonSubject"}, "resource": {"opaType": "ContactModel"}},
            {"index": 17, "action": "view", "subject": {"opaType": "PersonSubject"}, "resource": {"opaType": "HealthCommunitySpecialistModel"}},
            {"index": 18, "action": "view", "subject": {"opaType": "PersonSubject"}, "resource": {"opaType": "HealthcareTeamModel"}},
            {"index": 19, "action": "view", "subject": {"opaType": "PersonSubject"}, "resource": {"opaType": "StaffModel"}},
            {"index": 20, "action": "view", "subject": {"opaType": "PersonSubject"}, "resource": {"opaType": "HealthProfessionalModel"}},

            {"index": 21, "action": "view", "subject": {"opaType": "PersonSubject"}, "resource": {"opaType": "CassiSpecialistModel"}},
            {"index": 22, "action": "view", "subject": {"opaType": "PersonSubject"}, "resource": {"opaType": "ProviderUnitTestCodeModel"}},
            {"index": 23, "action": "view", "subject": {"opaType": "PersonSubject"}, "resource": {"opaType": "MedicalSpecialtyModel"}},
            {"index": 24, "action": "view", "subject": {"opaType": "PersonSubject"}, "resource": {"opaType": "ProductModel"}},
            {"index": 25, "action": "view", "subject": {"opaType": "PersonSubject"}, "resource": {"opaType": "ProductBundleModel"}},
            {"index": 26, "action": "view", "subject": {"opaType": "PersonSubject"}, "resource": {"opaType": "ProviderTestCodeModel"}},
            {"index": 27, "action": "view", "subject": {"opaType": "PersonSubject"}, "resource": {"opaType": "AliceAgoraWorkingHours"}},
            {"index": 28, "action": "view", "subject": {"opaType": "PersonSubject"}, "resource": {"opaType": "AppointmentScheduleOptionModel"}},
            {"index": 29, "action": "view", "subject": {"opaType": "PersonSubject"}, "resource": {"opaType": "HealthGoalModel"}},
            {"index": 30, "action": "view", "subject": {"opaType": "PersonSubject"}, "resource": {"opaType": "TestCodeModel"}},

            {"index": 31, "action": "view", "subject": {"opaType": "PersonSubject"}, "resource": {"opaType": "TestPreparationModel"}},
            {"index": 32, "action": "view", "subject": {"opaType": "PersonSubject"}, "resource": {"opaType": "HealthForm"}},
            {"index": 33, "action": "view", "subject": {"opaType": "PersonSubject"}, "resource": {"opaType": "HealthFormSection"}},
            {"index": 34, "action": "view", "subject": {"opaType": "PersonSubject"}, "resource": {"opaType": "HealthFormQuestion"}},
            {"index": 35, "action": "view", "subject": {"opaType": "PersonSubject"}, "resource": {"opaType": "HealthcareTeamRecommendationModel"}},
            {"index": 36, "action": "view", "subject": {"opaType": "PersonSubject"}, "resource": {"opaType": "HealthcareTeamRecommendationRuleModel"}},
            {"index": 37, "action": "view", "subject": {"opaType": "PersonSubject"}, "resource": {"opaType": "HealthcareTeamRecommendationRuleToRecommendationModel"}},
            {"index": 38, "action": "view", "subject": {"opaType": "PersonSubject"}, "resource": {"opaType": "FaqGroupModel"}},
            {"index": 39, "action": "view", "subject": {"opaType": "PersonSubject"}, "resource": {"opaType": "FaqContentModel"}},
            {"index": 40, "action": "view", "subject": {"opaType": "PersonSubject"}, "resource": {"opaType": "HealthCondition"}},
        ]
    }
}

test_person_subject_view_models_allowed_pt2 if {
    {41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60,
    61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78} == member_api.allow with input as {
        "cases": [
            {"index": 41, "action": "view", "subject": {"opaType": "PersonSubject"}, "resource": {"opaType": "PriceListingModel"}},
            {"index": 42, "action": "view", "subject": {"opaType": "PersonSubject"}, "resource": {"opaType": "ProductPriceListingModel"}},
            {"index": 43, "action": "view", "subject": {"opaType": "PersonSubject"}, "resource": {"opaType": "MemberProductPriceModel"}},
            {"index": 44, "action": "view", "subject": {"opaType": "PersonSubject"}, "resource": {"opaType": "BillingAccountablePartyModel"}},
            {"index": 45, "action": "view", "subject": {"opaType": "PersonSubject"}, "resource": {"opaType": "HealthMeasurementTypeModel"}},
            {"index": 46, "action": "view", "subject": {"opaType": "PersonSubject"}, "resource": {"opaType": "FeatureConfigModel"}},
            {"index": 47, "action": "view", "subject": {"opaType": "PersonSubject"}, "resource": {"opaType": "InsurancePortabilityHealthInsuranceModel"}},
            {"index": 48, "action": "view", "subject": {"opaType": "PersonSubject"}, "resource": {"opaType": "CompanyModel"}},
            {"index": 49, "action": "view", "subject": {"opaType": "PersonSubject"}, "resource": {"opaType": "BeneficiaryModel"}},
            {"index": 50, "action": "view", "subject": {"opaType": "PersonSubject"}, "resource": {"opaType": "BeneficiaryOnboardingModel"}},

            {"index": 51, "action": "view", "subject": {"opaType": "PersonSubject"}, "resource": {"opaType": "BeneficiaryOnboardingPhaseModel"}},
            {"index": 52, "action": "view", "subject": {"opaType": "PersonSubject"}, "resource": {"opaType": "StaffScheduleModel"}},
            {"index": 53, "action": "view", "subject": {"opaType": "PersonSubject"}, "resource": {"opaType": "ExternalCalendarEventModel"}},
            {"index": 54, "action": "view", "subject": {"opaType": "PersonSubject"}, "resource": {"opaType": "HealthProductSimulation"}},
            {"index": 55, "action": "view", "subject": {"opaType": "PersonSubject"}, "resource": {"opaType": "AppointmentScheduleEventTypeModel"}},
            {"index": 56, "action": "view", "subject": {"opaType": "PersonSubject"}, "resource": {"opaType": "EventTypeProviderUnitModel"}},
            {"index": 57, "action": "view", "subject": {"opaType": "PersonSubject"}, "resource": {"opaType": "AppointmentScheduleEventTypeDateExceptionModel"}},
            {"index": 58, "action": "view", "subject": {"opaType": "PersonSubject"}, "resource": {"opaType": "LegalGuardianInfoTempModel"}},
            {"index": 59, "action": "view", "subject": {"opaType": "PersonSubject"}, "resource": {"opaType": "MemberOnboardingTemplate"}},
            {"index": 60, "action": "view", "subject": {"opaType": "PersonSubject"}, "resource": {"opaType": "MemberOnboardingAction"}},

            {"index": 61, "action": "view", "subject": {"opaType": "PersonSubject"}, "resource": {"opaType": "MemberOnboardingStep"}},
            {"index": 62, "action": "view", "subject": {"opaType": "PersonSubject"}, "resource": {"opaType": "UpdateAppRuleModel"}},
            {"index": 63, "action": "view", "subject": {"opaType": "PersonSubject"}, "resource": {"opaType": "OutcomeConf"}},
            {"index": 64, "action": "view", "subject": {"opaType": "PersonSubject"}, "resource": {"opaType": "PersonPreferencesModel"}},
            {"index": 65, "action": "view", "subject": {"opaType": "PersonSubject"}, "resource": {"opaType": "PersonBillingAccountablePartyModel"}},
            {"index": 66, "action": "view", "subject": {"opaType": "PersonSubject"}, "resource": {"opaType": "MemberInvoiceModel"}},
            {"index": 67, "action": "view", "subject": {"opaType": "PersonSubject"}, "resource": {"opaType": "EmergencyRecommendation"}},
            {"index": 68, "action": "view", "subject": {"opaType": "PersonSubject"}, "resource": {"opaType": "CoPaymentCostInfoModel"}},
            {"index": 69, "action": "view", "subject": {"opaType": "PersonSubject"}, "resource": {"opaType": "RefundCostInfoModel"}},
            {"index": 70, "action": "view", "subject": {"opaType": "PersonSubject"}, "resource": {"opaType": "CompanyRefundCostInfoModel"}},

            {"index": 71, "action": "view", "subject": {"opaType": "PersonSubject"}, "resource": {"opaType": "GenericFileVault"}},
            {"index": 72, "action": "view", "subject": {"opaType": "PersonSubject"}, "resource": {"opaType": "CsatTemplate"}},
            {"index": 73, "action": "view", "subject": {"opaType": "PersonSubject"}, "resource": {"opaType": "HealthPlanTaskTemplate"}},
            {"index": 74, "action": "view", "subject": {"opaType": "PersonSubject"}, "resource": {"opaType": "AIAssistant"}},
            {"index": 75, "action": "view", "subject": {"opaType": "PersonSubject"}, "resource": {"opaType": "ScreenData"}},
            {"index": 76, "action": "view", "subject": {"opaType": "PersonSubject"}, "resource": {"opaType": "ServiceScriptAction"}},
            {"index": 77, "action": "view", "subject": {"opaType": "PersonSubject"}, "resource": {"opaType": "ServiceScriptExecution"}},
            {"index": 78, "action": "view", "subject": {"opaType": "PersonSubject"}, "resource": {"opaType": "HealthcareResourceModel"}}
        ]
    }
}

test_person_subject_create_models_allowed if {
    {1, 2} == member_api.allow with input as {
        "cases": [
            { "index": 1, "action": "create", "subject": { "opaType": "PersonSubject" }, "resource": { "opaType": "LegalGuardianInfoTempModel" } },
            { "index": 2, "action": "create", "subject": { "opaType": "PersonSubject" }, "resource": { "opaType": "AbTestSpecialistRecommendation" } }
        ]
    }
}

test_person_subject_view_count_create_update_actions_models_allowed if {
    {1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24} == member_api.allow with input as {
        "cases": [
            {"index": 1, "action": "view",   "subject": {"opaType": "PersonSubject"}, "resource": {"opaType": "MemberOnboardingCheckpoint"}},
            {"index": 2, "action": "count",  "subject": {"opaType": "PersonSubject"}, "resource": {"opaType": "MemberOnboardingCheckpoint"}},
            {"index": 3, "action": "create", "subject": {"opaType": "PersonSubject"}, "resource": {"opaType": "MemberOnboardingCheckpoint"}},
            {"index": 4, "action": "update", "subject": {"opaType": "PersonSubject"}, "resource": {"opaType": "MemberOnboardingCheckpoint"}},

            {"index": 5, "action": "view",   "subject": {"opaType": "PersonSubject"}, "resource": {"opaType": "GenerateExternalAttendancePa"}},
            {"index": 6, "action": "count",  "subject": {"opaType": "PersonSubject"}, "resource": {"opaType": "GenerateExternalAttendancePa"}},
            {"index": 7, "action": "create", "subject": {"opaType": "PersonSubject"}, "resource": {"opaType": "GenerateExternalAttendancePa"}},
            {"index": 8, "action": "update", "subject": {"opaType": "PersonSubject"}, "resource": {"opaType": "GenerateExternalAttendancePa"}},

            {"index": 9,  "action": "view",   "subject": {"opaType": "PersonSubject"}, "resource": {"opaType": "MemberOnboarding"}},
            {"index": 10, "action": "count",  "subject": {"opaType": "PersonSubject"}, "resource": {"opaType": "MemberOnboarding"}},
            {"index": 11, "action": "create", "subject": {"opaType": "PersonSubject"}, "resource": {"opaType": "MemberOnboarding"}},
            {"index": 12, "action": "update", "subject": {"opaType": "PersonSubject"}, "resource": {"opaType": "MemberOnboarding"}},

            {"index": 13, "action": "view",   "subject": {"opaType": "PersonSubject"}, "resource": {"opaType": "ScreeningNavigation"}},
            {"index": 14, "action": "count",  "subject": {"opaType": "PersonSubject"}, "resource": {"opaType": "ScreeningNavigation"}},
            {"index": 15, "action": "create", "subject": {"opaType": "PersonSubject"}, "resource": {"opaType": "ScreeningNavigation"}},
            {"index": 16, "action": "update", "subject": {"opaType": "PersonSubject"}, "resource": {"opaType": "ScreeningNavigation"}},

            {"index": 17, "action": "view",   "subject": {"opaType": "PersonSubject"}, "resource": {"opaType": "ItauPaymentModel"}},
            {"index": 18, "action": "count",  "subject": {"opaType": "PersonSubject"}, "resource": {"opaType": "ItauPaymentModel"}},
            {"index": 19, "action": "create", "subject": {"opaType": "PersonSubject"}, "resource": {"opaType": "ItauPaymentModel"}},
            {"index": 20, "action": "update", "subject": {"opaType": "PersonSubject"}, "resource": {"opaType": "ItauPaymentModel"}},

            {"index": 21, "action": "view",   "subject": {"opaType": "PersonSubject"}, "resource": {"opaType": "ResourceSignTokenModel"}},
            {"index": 22, "action": "count",  "subject": {"opaType": "PersonSubject"}, "resource": {"opaType": "ResourceSignTokenModel"}},
            {"index": 23, "action": "create", "subject": {"opaType": "PersonSubject"}, "resource": {"opaType": "ResourceSignTokenModel"}},
            {"index": 24, "action": "update", "subject": {"opaType": "PersonSubject"}, "resource": {"opaType": "ResourceSignTokenModel"}},
        ]
    }
}

test_person_subject_view_count_models_allowed if {
    {1, 2, 3, 4, 5, 6, 7, 8} == member_api.allow with input as {
        "cases": [
            {"index": 1, "action": "view",  "subject": {"opaType": "PersonSubject"}, "resource": {"opaType": "AppointmentScheduleModel"}},
            {"index": 2, "action": "count", "subject": {"opaType": "PersonSubject"}, "resource": {"opaType": "AppointmentScheduleModel"}},

            {"index": 3, "action": "view",  "subject": {"opaType": "PersonSubject"}, "resource": {"opaType": "BudNode"}},
            {"index": 4, "action": "count", "subject": {"opaType": "PersonSubject"}, "resource": {"opaType": "BudNode"}},

            {"index": 5, "action": "view",  "subject": {"opaType": "PersonSubject"}, "resource": {"opaType": "Protocol"}},
            {"index": 6, "action": "count", "subject": {"opaType": "PersonSubject"}, "resource": {"opaType": "Protocol"}},

            {"index": 7, "action": "view",  "subject": {"opaType": "PersonSubject"}, "resource": {"opaType": "ServiceScriptRelationship"}},
            {"index": 8, "action": "count", "subject": {"opaType": "PersonSubject"}, "resource": {"opaType": "ServiceScriptRelationship"}}
        ]
    }
}

test_person_subject_view_create_update_models_allowed if {
    {1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18} == member_api.allow with input as {
        "cases": [
            {"index": 1,  "action": "view",   "subject": {"opaType": "PersonSubject"}, "resource": {"opaType": "ServiceScriptNavigation"}},
            {"index": 2,  "action": "create", "subject": {"opaType": "PersonSubject"}, "resource": {"opaType": "ServiceScriptNavigation"}},
            {"index": 3,  "action": "update", "subject": {"opaType": "PersonSubject"}, "resource": {"opaType": "ServiceScriptNavigation"}},

            {"index": 4,  "action": "view",   "subject": {"opaType": "PersonSubject"}, "resource": {"opaType": "ServiceScriptNavigationGroup"}},
            {"index": 5,  "action": "create", "subject": {"opaType": "PersonSubject"}, "resource": {"opaType": "ServiceScriptNavigationGroup"}},
            {"index": 6,  "action": "update", "subject": {"opaType": "PersonSubject"}, "resource": {"opaType": "ServiceScriptNavigationGroup"}},

            {"index": 7,  "action": "view",   "subject": {"opaType": "PersonSubject"}, "resource": {"opaType": "LegalGuardianInfoTempModel"}},
            {"index": 8,  "action": "create", "subject": {"opaType": "PersonSubject"}, "resource": {"opaType": "LegalGuardianInfoTempModel"}},
            {"index": 9,  "action": "update", "subject": {"opaType": "PersonSubject"}, "resource": {"opaType": "LegalGuardianInfoTempModel"}},

            {"index": 10, "action": "view",   "subject": {"opaType": "PersonSubject"}, "resource": {"opaType": "Opportunity"}},
            {"index": 11, "action": "create", "subject": {"opaType": "PersonSubject"}, "resource": {"opaType": "Opportunity"}},
            {"index": 12, "action": "update", "subject": {"opaType": "PersonSubject"}, "resource": {"opaType": "Opportunity"}},

            {"index": 13, "action": "view",   "subject": {"opaType": "PersonSubject"}, "resource": {"opaType": "BeneficiaryOnboardingModel"}},
            {"index": 14, "action": "create", "subject": {"opaType": "PersonSubject"}, "resource": {"opaType": "BeneficiaryOnboardingModel"}},
            {"index": 15, "action": "update", "subject": {"opaType": "PersonSubject"}, "resource": {"opaType": "BeneficiaryOnboardingModel"}},

            {"index": 16, "action": "view",   "subject": {"opaType": "PersonSubject"}, "resource": {"opaType": "BeneficiaryOnboardingPhaseModel"}},
            {"index": 17, "action": "create", "subject": {"opaType": "PersonSubject"}, "resource": {"opaType": "BeneficiaryOnboardingPhaseModel"}},
            {"index": 18, "action": "update", "subject": {"opaType": "PersonSubject"}, "resource": {"opaType": "BeneficiaryOnboardingPhaseModel"}}
        ]
    }
}

test_person_subject_own_person_view_models_allowed if {
    {1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31,
    32, 33, 34, 35, 36, 37, 38} == member_api.allow with input as {
        "cases": [
            {"index": 1, "action": "view", "subject": {"opaType": "PersonSubject", "id": "123"}, "resource": {"opaType": "PersonModel", "id": "123"}},
            {"index": 2, "action": "view", "subject": {"opaType": "PersonSubject", "id": "123"}, "resource": {"opaType": "PersonModel", "personId": "123"}},

            {"index": 3, "action": "view", "subject": {"opaType": "PersonSubject", "id": "123"}, "resource": {"opaType": "MemberModel", "id": "123"}},
            {"index": 4, "action": "view", "subject": {"opaType": "PersonSubject", "id": "123"}, "resource": {"opaType": "MemberModel", "personId": "123"}},

            {"index": 5, "action": "view", "subject": {"opaType": "PersonSubject", "id": "123"}, "resource": {"opaType": "HealthInformation", "id": "123"}},
            {"index": 6, "action": "view", "subject": {"opaType": "PersonSubject", "id": "123"}, "resource": {"opaType": "HealthInformation", "personId": "123"}},

            {"index": 7, "action": "view", "subject": {"opaType": "PersonSubject", "id": "123"}, "resource": {"opaType": "AppointmentScheduleModel", "id": "123"}},
            {"index": 8, "action": "view", "subject": {"opaType": "PersonSubject", "id": "123"}, "resource": {"opaType": "AppointmentScheduleModel", "personId": "123"}},

            {"index": 9, "action": "view", "subject": {"opaType": "PersonSubject", "id": "123"}, "resource": {"opaType": "AppointmentCoordination", "id": "123"}},
            {"index": 10, "action": "view", "subject": {"opaType": "PersonSubject", "id": "123"}, "resource": {"opaType": "AppointmentCoordination", "personId": "123"}},

            {"index": 11, "action": "view", "subject": {"opaType": "PersonSubject", "id": "123"}, "resource": {"opaType": "MemberInvoiceModel", "id": "123"}},
            {"index": 12, "action": "view", "subject": {"opaType": "PersonSubject", "id": "123"}, "resource": {"opaType": "MemberInvoiceModel", "personId": "123"}},

            {"index": 13, "action": "view", "subject": {"opaType": "PersonSubject", "id": "123"}, "resource": {"opaType": "PersonClinicalAccount", "id": "123"}},
            {"index": 14, "action": "view", "subject": {"opaType": "PersonSubject", "id": "123"}, "resource": {"opaType": "PersonClinicalAccount", "personId": "123"}},

            {"index": 15, "action": "view", "subject": {"opaType": "PersonSubject", "id": "123"}, "resource": {"opaType": "LaboratoryTestResultModel", "id": "123"}},
            {"index": 16, "action": "view", "subject": {"opaType": "PersonSubject", "id": "123"}, "resource": {"opaType": "LaboratoryTestResultModel", "personId": "123"}},

            {"index": 17, "action": "view", "subject": {"opaType": "PersonSubject", "id": "123"}, "resource": {"opaType": "ExternalHealthInformation", "id": "123"}},
            {"index": 18, "action": "view", "subject": {"opaType": "PersonSubject", "id": "123"}, "resource": {"opaType": "ExternalHealthInformation", "personId": "123"}},

            {"index": 19, "action": "view", "subject": {"opaType": "PersonSubject", "id": "123"}, "resource": {"opaType": "PersonHealthEvent", "id": "123"}},
            {"index": 20, "action": "view", "subject": {"opaType": "PersonSubject", "id": "123"}, "resource": {"opaType": "PersonHealthEvent", "personId": "123"}},

            {"index": 21, "action": "view", "subject": {"opaType": "PersonSubject", "id": "123"}, "resource": {"opaType": "TestResultFeedback", "id": "123"}},
            {"index": 22, "action": "view", "subject": {"opaType": "PersonSubject", "id": "123"}, "resource": {"opaType": "TestResultFeedback", "personId": "123"}},

            {"index": 23, "action": "view", "subject": {"opaType": "PersonSubject", "id": "123"}, "resource": {"opaType": "AliceTestResultBundle", "id": "123"}},
            {"index": 24, "action": "view", "subject": {"opaType": "PersonSubject", "id": "123"}, "resource": {"opaType": "AliceTestResultBundle", "personId": "123"}},

            {"index": 25, "action": "view", "subject": {"opaType": "PersonSubject", "id": "123"}, "resource": {"opaType": "Risk", "id": "123"}},
            {"index": 26, "action": "view", "subject": {"opaType": "PersonSubject", "id": "123"}, "resource": {"opaType": "Risk", "personId": "123"}},

            {"index": 27, "action": "view", "subject": {"opaType": "PersonSubject", "id": "123"}, "resource": {"opaType": "MemberOnboardingCheckpoint", "id": "123"}},
            {"index": 28, "action": "view", "subject": {"opaType": "PersonSubject", "id": "123"}, "resource": {"opaType": "MemberOnboardingCheckpoint", "personId": "123"}},

            {"index": 29, "action": "view", "subject": {"opaType": "PersonSubject", "id": "123"}, "resource": {"opaType": "MvAuthorizedProcedureModel", "id": "123"}},
            {"index": 30, "action": "view", "subject": {"opaType": "PersonSubject", "id": "123"}, "resource": {"opaType": "MvAuthorizedProcedureModel", "personId": "123"}},

            {"index": 31, "action": "view", "subject": {"opaType": "PersonSubject", "id": "123"}, "resource": {"opaType": "ExternalAppointmentScheduleModel", "id": "123"}},
            {"index": 32, "action": "view", "subject": {"opaType": "PersonSubject", "id": "123"}, "resource": {"opaType": "ExternalAppointmentScheduleModel", "personId": "123"}},

            {"index": 33, "action": "view", "subject": {"opaType": "PersonSubject", "id": "123"}, "resource": {"opaType": "ServiceScriptNavigationGroup", "id": "123"}},
            {"index": 34, "action": "view", "subject": {"opaType": "PersonSubject", "id": "123"}, "resource": {"opaType": "ServiceScriptNavigationGroup", "personId": "123"}},

            {"index": 35, "action": "view", "subject": {"opaType": "PersonSubject", "id": "123"}, "resource": {"opaType": "PersonGracePeriod", "id": "123"}},
            {"index": 36, "action": "view", "subject": {"opaType": "PersonSubject", "id": "123"}, "resource": {"opaType": "PersonGracePeriod", "personId": "123"}},

            {"index": 37, "action": "view", "subject": {"opaType": "PersonSubject", "id": "123"}, "resource": {"opaType": "TotvsGuiaModel", "id": "123"}},
            {"index": 38, "action": "view", "subject": {"opaType": "PersonSubject", "id": "123"}, "resource": {"opaType": "TotvsGuiaModel", "personId": "123"}}
        ]
    }
}

test_person_subject_own_person_view_update_models_allowed if {
    {1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12} == member_api.allow with input as {
        "cases": [
            {"index": 1, "action": "view", "subject": {"opaType": "PersonSubject", "id": "123"}, "resource": {"opaType": "VideoCall", "id": "123"}},
            {"index": 2, "action": "view", "subject": {"opaType": "PersonSubject", "id": "123"}, "resource": {"opaType": "VideoCall", "personId": "123"}},

            {"index": 3, "action": "view", "subject": {"opaType": "PersonSubject", "id": "123"}, "resource": {"opaType": "AppContentScreenDetail", "id": "123"}},
            {"index": 4, "action": "view", "subject": {"opaType": "PersonSubject", "id": "123"}, "resource": {"opaType": "AppContentScreenDetail", "personId": "123"}},

            {"index": 5, "action": "view", "subject": {"opaType": "PersonSubject", "id": "123"}, "resource": {"opaType": "AppointmentScheduleCheckInModel", "id": "123"}},
            {"index": 6, "action": "view", "subject": {"opaType": "PersonSubject", "id": "123"}, "resource": {"opaType": "AppointmentScheduleCheckInModel", "personId": "123"}},

            {"index": 7, "action": "update", "subject": {"opaType": "PersonSubject", "id": "123"}, "resource": {"opaType": "VideoCall", "id": "123"}},
            {"index": 8, "action": "update", "subject": {"opaType": "PersonSubject", "id": "123"}, "resource": {"opaType": "VideoCall", "personId": "123"}},

            {"index": 9, "action": "update", "subject": {"opaType": "PersonSubject", "id": "123"}, "resource": {"opaType": "AppContentScreenDetail", "id": "123"}},
            {"index": 10, "action": "update", "subject": {"opaType": "PersonSubject", "id": "123"}, "resource": {"opaType": "AppContentScreenDetail", "personId": "123"}},

            {"index": 11, "action": "update", "subject": {"opaType": "PersonSubject", "id": "123"}, "resource": {"opaType": "AppointmentScheduleCheckInModel", "id": "123"}},
            {"index": 12, "action": "update", "subject": {"opaType": "PersonSubject", "id": "123"}, "resource": {"opaType": "AppointmentScheduleCheckInModel", "personId": "123"}}
        ]
    }
}

test_person_subject_own_person_view_create_file_vault_channel_models_allowed if {
    {1, 2, 3, 4, 5, 6, 7, 8} == member_api.allow with input as {
        "cases": [
            {"index": 1, "action": "view", "subject": {"opaType": "PersonSubject", "id": "123"}, "resource": {"opaType": "FileVault", "id": "123"}},
            {"index": 2, "action": "view", "subject": {"opaType": "PersonSubject", "id": "123"}, "resource": {"opaType": "FileVault", "personId": "123"}},

            {"index": 3, "action": "view", "subject": {"opaType": "PersonSubject", "id": "123"}, "resource": {"opaType": "Channel", "id": "123"}},
            {"index": 4, "action": "view", "subject": {"opaType": "PersonSubject", "id": "123"}, "resource": {"opaType": "Channel", "personId": "123"}},

            {"index": 5, "action": "create", "subject": {"opaType": "PersonSubject", "id": "123"}, "resource": {"opaType": "FileVault", "id": "123"}},
            {"index": 6, "action": "create", "subject": {"opaType": "PersonSubject", "id": "123"}, "resource": {"opaType": "FileVault", "personId": "123"}},

            {"index": 7, "action": "create", "subject": {"opaType": "PersonSubject", "id": "123"}, "resource": {"opaType": "Channel", "id": "123"}},
            {"index": 8, "action": "create", "subject": {"opaType": "PersonSubject", "id": "123"}, "resource": {"opaType": "Channel", "personId": "123"}}
        ]
    }
}

test_person_subject_own_person_view_models_id_based_allowed if {
    {1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31,
    32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43} == member_api.allow with input as {
        "cases": [
            {"index": 1, "action": "view", "subject": {"opaType": "PersonSubject", "id": "123"}, "resource": {"opaType": "PersonTaskModel", "id": "123"}},
            {"index": 2, "action": "view", "subject": {"opaType": "PersonSubject", "id": "123"}, "resource": {"opaType": "DeviceModel", "id": "123"}},
            {"index": 3, "action": "view", "subject": {"opaType": "PersonSubject", "id": "123"}, "resource": {"opaType": "MemberModel", "id": "123"}},
            {"index": 4, "action": "view", "subject": {"opaType": "PersonSubject", "id": "123"}, "resource": {"opaType": "HealthDeclaration", "id": "123"}},
            {"index": 5, "action": "view", "subject": {"opaType": "PersonSubject", "id": "123"}, "resource": {"opaType": "HealthPlanTaskGroup", "id": "123"}},
            {"index": 6, "action": "view", "subject": {"opaType": "PersonSubject", "id": "123"}, "resource": {"opaType": "HealthPlanTask", "id": "123"}},
            {"index": 7, "action": "view", "subject": {"opaType": "PersonSubject", "id": "123"}, "resource": {"opaType": "ActionPlanTask", "id": "123"}},
            {"index": 8, "action": "view", "subject": {"opaType": "PersonSubject", "id": "123"}, "resource": {"opaType": "DemandActionPlan", "id": "123"}},
            {"index": 9, "action": "view", "subject": {"opaType": "PersonSubject", "id": "123"}, "resource": {"opaType": "PersonInternalReference", "id": "123"}},
            {"index": 10, "action": "view", "subject": {"opaType": "PersonSubject", "id": "123"}, "resource": {"opaType": "PersonOnboardingModel", "id": "123"}},
            {"index": 11, "action": "view", "subject": {"opaType": "PersonSubject", "id": "123"}, "resource": {"opaType": "PersonRegistrationModel", "id": "123"}},
            {"index": 12, "action": "view", "subject": {"opaType": "PersonSubject", "id": "123"}, "resource": {"opaType": "ProductOrderModel", "id": "123"}},
            {"index": 13, "action": "view", "subject": {"opaType": "PersonSubject", "id": "123"}, "resource": {"opaType": "OnboardingContractModel", "id": "123"}},
            {"index": 14, "action": "view", "subject": {"opaType": "PersonSubject", "id": "123"}, "resource": {"opaType": "PersonHealthGoalModel", "id": "123"}},
            {"index": 15, "action": "view", "subject": {"opaType": "PersonSubject", "id": "123"}, "resource": {"opaType": "PersonHealthcareTeamRecommendationModel", "id": "123"}},
            {"index": 16, "action": "view", "subject": {"opaType": "PersonSubject", "id": "123"}, "resource": {"opaType": "TestResultFileModel", "id": "123"}},
            {"index": 17, "action": "view", "subject": {"opaType": "PersonSubject", "id": "123"}, "resource": {"opaType": "InsurancePortabilityRequestModel", "id": "123"}},
            {"index": 18, "action": "view", "subject": {"opaType": "PersonSubject", "id": "123"}, "resource": {"opaType": "InsurancePortabilityRequestFileModel", "id": "123"}},
            {"index": 19, "action": "view", "subject": {"opaType": "PersonSubject", "id": "123"}, "resource": {"opaType": "LegalGuardianInfoTempModel", "id": "123"}},
            {"index": 20, "action": "view", "subject": {"opaType": "PersonSubject", "id": "123"}, "resource": {"opaType": "HealthFormQuestionAnswer", "id": "123"}},
            {"index": 21, "action": "view", "subject": {"opaType": "PersonSubject", "id": "123"}, "resource": {"opaType": "HealthFormAnswerGroup", "id": "123"}},
            {"index": 22, "action": "view", "subject": {"opaType": "PersonSubject", "id": "123"}, "resource": {"opaType": "HealthPlan", "id": "123"}},
            {"index": 23, "action": "view", "subject": {"opaType": "PersonSubject", "id": "123"}, "resource": {"opaType": "ClinicalBackground", "id": "123"}},
            {"index": 24, "action": "view", "subject": {"opaType": "PersonSubject", "id": "123"}, "resource": {"opaType": "HealthMeasurementModel", "id": "123"}},
            {"index": 25, "action": "view", "subject": {"opaType": "PersonSubject", "id": "123"}, "resource": {"opaType": "PersonAdditionalInfoModel", "id": "123"}},
            {"index": 26, "action": "view", "subject": {"opaType": "PersonSubject", "id": "123"}, "resource": {"opaType": "AppointmentScheduleModel", "id": "123"}},
            {"index": 27, "action": "view", "subject": {"opaType": "PersonSubject", "id": "123"}, "resource": {"opaType": "PersonDocumentsUploadModel", "id": "123"}},
            {"index": 28, "action": "view", "subject": {"opaType": "PersonSubject", "id": "123"}, "resource": {"opaType": "PersonBenefitModel", "id": "123"}},
            {"index": 29, "action": "view", "subject": {"opaType": "PersonSubject", "id": "123"}, "resource": {"opaType": "PersonCalendlyModel", "id": "123"}},
            {"index": 30, "action": "view", "subject": {"opaType": "PersonSubject", "id": "123"}, "resource": {"opaType": "TrackPersonABModel", "id": "123"}},
            {"index": 31, "action": "view", "subject": {"opaType": "PersonSubject", "id": "123"}, "resource": {"opaType": "FollowUpHistory", "id": "123"}},
            {"index": 32, "action": "view", "subject": {"opaType": "PersonSubject", "id": "123"}, "resource": {"opaType": "MemberOnboardingCheckpoint", "id": "123"}},
            {"index": 33, "action": "view", "subject": {"opaType": "PersonSubject", "id": "123"}, "resource": {"opaType": "PersonEligibilityDuquesa", "id": "123"}},
            {"index": 34, "action": "view", "subject": {"opaType": "PersonSubject", "id": "123"}, "resource": {"opaType": "Csat", "id": "123"}},
            {"index": 35, "action": "view", "subject": {"opaType": "PersonSubject", "id": "123"}, "resource": {"opaType": "RefundModel", "id": "123"}},
            {"index": 36, "action": "view", "subject": {"opaType": "PersonSubject", "id": "123"}, "resource": {"opaType": "RefundFileModel", "id": "123"}},
            {"index": 37, "action": "view", "subject": {"opaType": "PersonSubject", "id": "123"}, "resource": {"opaType": "FinancialDataModel", "id": "123"}},
            {"index": 38, "action": "view", "subject": {"opaType": "PersonSubject", "id": "123"}, "resource": {"opaType": "LegalGuardianAssociationModel", "id": "123"}},
            {"index": 39, "action": "view", "subject": {"opaType": "PersonSubject", "id": "123"}, "resource": {"opaType": "PersonModel", "id": "123"}},
            {"index": 40, "action": "view", "subject": {"opaType": "PersonSubject", "id": "123"}, "resource": {"opaType": "StaffModel", "id": "123"}},
            {"index": 41, "action": "view", "subject": {"opaType": "PersonSubject", "id": "123"}, "resource": {"opaType": "HealthCommunitySpecialistModel", "id": "123"}},
            {"index": 42, "action": "view", "subject": {"opaType": "PersonSubject", "id": "123"}, "resource": {"opaType": "PersonIdentityValidationModel", "id": "123"}},
            {"index": 43, "action": "view", "subject": {"opaType": "PersonSubject", "id": "123"}, "resource": {"opaType": "PersonClinicalAccount", "id": "123"}},
        ]
    }
}

test_person_subject_own_person_view_models_person_id_based_allowed if {
    {1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31,
    32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43} == member_api.allow with input as {
        "cases": [
            {"index": 1, "action": "view", "subject": {"opaType": "PersonSubject", "id": "123"}, "resource": {"opaType": "PersonTaskModel", "personId": "123"}},
            {"index": 2, "action": "view", "subject": {"opaType": "PersonSubject", "id": "123"}, "resource": {"opaType": "DeviceModel", "personId": "123"}},
            {"index": 3, "action": "view", "subject": {"opaType": "PersonSubject", "id": "123"}, "resource": {"opaType": "MemberModel", "personId": "123"}},
            {"index": 4, "action": "view", "subject": {"opaType": "PersonSubject", "id": "123"}, "resource": {"opaType": "HealthDeclaration", "personId": "123"}},
            {"index": 5, "action": "view", "subject": {"opaType": "PersonSubject", "id": "123"}, "resource": {"opaType": "HealthPlanTaskGroup", "personId": "123"}},
            {"index": 6, "action": "view", "subject": {"opaType": "PersonSubject", "id": "123"}, "resource": {"opaType": "HealthPlanTask", "personId": "123"}},
            {"index": 7, "action": "view", "subject": {"opaType": "PersonSubject", "id": "123"}, "resource": {"opaType": "ActionPlanTask", "personId": "123"}},
            {"index": 8, "action": "view", "subject": {"opaType": "PersonSubject", "id": "123"}, "resource": {"opaType": "DemandActionPlan", "personId": "123"}},
            {"index": 9, "action": "view", "subject": {"opaType": "PersonSubject", "id": "123"}, "resource": {"opaType": "PersonInternalReference", "personId": "123"}},
            {"index": 10, "action": "view", "subject": {"opaType": "PersonSubject", "id": "123"}, "resource": {"opaType": "PersonOnboardingModel", "personId": "123"}},
            {"index": 11, "action": "view", "subject": {"opaType": "PersonSubject", "id": "123"}, "resource": {"opaType": "PersonRegistrationModel", "personId": "123"}},
            {"index": 12, "action": "view", "subject": {"opaType": "PersonSubject", "id": "123"}, "resource": {"opaType": "ProductOrderModel", "personId": "123"}},
            {"index": 13, "action": "view", "subject": {"opaType": "PersonSubject", "id": "123"}, "resource": {"opaType": "OnboardingContractModel", "personId": "123"}},
            {"index": 14, "action": "view", "subject": {"opaType": "PersonSubject", "id": "123"}, "resource": {"opaType": "PersonHealthGoalModel", "personId": "123"}},
            {"index": 15, "action": "view", "subject": {"opaType": "PersonSubject", "id": "123"}, "resource": {"opaType": "PersonHealthcareTeamRecommendationModel", "personId": "123"}},
            {"index": 16, "action": "view", "subject": {"opaType": "PersonSubject", "id": "123"}, "resource": {"opaType": "TestResultFileModel", "personId": "123"}},
            {"index": 17, "action": "view", "subject": {"opaType": "PersonSubject", "id": "123"}, "resource": {"opaType": "InsurancePortabilityRequestModel", "personId": "123"}},
            {"index": 18, "action": "view", "subject": {"opaType": "PersonSubject", "id": "123"}, "resource": {"opaType": "InsurancePortabilityRequestFileModel", "personId": "123"}},
            {"index": 19, "action": "view", "subject": {"opaType": "PersonSubject", "id": "123"}, "resource": {"opaType": "LegalGuardianInfoTempModel", "personId": "123"}},
            {"index": 20, "action": "view", "subject": {"opaType": "PersonSubject", "id": "123"}, "resource": {"opaType": "HealthFormQuestionAnswer", "personId": "123"}},
            {"index": 21, "action": "view", "subject": {"opaType": "PersonSubject", "id": "123"}, "resource": {"opaType": "HealthFormAnswerGroup", "personId": "123"}},
            {"index": 22, "action": "view", "subject": {"opaType": "PersonSubject", "id": "123"}, "resource": {"opaType": "HealthPlan", "personId": "123"}},
            {"index": 23, "action": "view", "subject": {"opaType": "PersonSubject", "id": "123"}, "resource": {"opaType": "ClinicalBackground", "personId": "123"}},
            {"index": 24, "action": "view", "subject": {"opaType": "PersonSubject", "id": "123"}, "resource": {"opaType": "HealthMeasurementModel", "personId": "123"}},
            {"index": 25, "action": "view", "subject": {"opaType": "PersonSubject", "id": "123"}, "resource": {"opaType": "PersonAdditionalInfoModel", "personId": "123"}},
            {"index": 26, "action": "view", "subject": {"opaType": "PersonSubject", "id": "123"}, "resource": {"opaType": "AppointmentScheduleModel", "personId": "123"}},
            {"index": 27, "action": "view", "subject": {"opaType": "PersonSubject", "id": "123"}, "resource": {"opaType": "PersonDocumentsUploadModel", "personId": "123"}},
            {"index": 28, "action": "view", "subject": {"opaType": "PersonSubject", "id": "123"}, "resource": {"opaType": "PersonBenefitModel", "personId": "123"}},
            {"index": 29, "action": "view", "subject": {"opaType": "PersonSubject", "id": "123"}, "resource": {"opaType": "PersonCalendlyModel", "personId": "123"}},
            {"index": 30, "action": "view", "subject": {"opaType": "PersonSubject", "id": "123"}, "resource": {"opaType": "TrackPersonABModel", "personId": "123"}},
            {"index": 31, "action": "view", "subject": {"opaType": "PersonSubject", "id": "123"}, "resource": {"opaType": "FollowUpHistory", "personId": "123"}},
            {"index": 32, "action": "view", "subject": {"opaType": "PersonSubject", "id": "123"}, "resource": {"opaType": "MemberOnboardingCheckpoint", "personId": "123"}},
            {"index": 33, "action": "view", "subject": {"opaType": "PersonSubject", "id": "123"}, "resource": {"opaType": "PersonEligibilityDuquesa", "personId": "123"}},
            {"index": 34, "action": "view", "subject": {"opaType": "PersonSubject", "id": "123"}, "resource": {"opaType": "Csat", "personId": "123"}},
            {"index": 35, "action": "view", "subject": {"opaType": "PersonSubject", "id": "123"}, "resource": {"opaType": "RefundModel", "personId": "123"}},
            {"index": 36, "action": "view", "subject": {"opaType": "PersonSubject", "id": "123"}, "resource": {"opaType": "RefundFileModel", "personId": "123"}},
            {"index": 37, "action": "view", "subject": {"opaType": "PersonSubject", "id": "123"}, "resource": {"opaType": "FinancialDataModel", "personId": "123"}},
            {"index": 38, "action": "view", "subject": {"opaType": "PersonSubject", "id": "123"}, "resource": {"opaType": "LegalGuardianAssociationModel", "personId": "123"}},
            {"index": 39, "action": "view", "subject": {"opaType": "PersonSubject", "id": "123"}, "resource": {"opaType": "PersonModel", "personId": "123"}},
            {"index": 40, "action": "view", "subject": {"opaType": "PersonSubject", "id": "123"}, "resource": {"opaType": "StaffModel", "personId": "123"}},
            {"index": 41, "action": "view", "subject": {"opaType": "PersonSubject", "id": "123"}, "resource": {"opaType": "HealthCommunitySpecialistModel", "personId": "123"}},
            {"index": 42, "action": "view", "subject": {"opaType": "PersonSubject", "id": "123"}, "resource": {"opaType": "PersonIdentityValidationModel", "personId": "123"}},
            {"index": 43, "action": "view", "subject": {"opaType": "PersonSubject", "id": "123"}, "resource": {"opaType": "PersonClinicalAccount", "personId": "123"}}
        ]
    }
}

test_person_subject_own_person_create_models_id_based_allowed if {
    {1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31,
    32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43} == member_api.allow with input as {
        "cases": [
            {"index": 1, "action": "create", "subject": {"opaType": "PersonSubject", "id": "123"}, "resource": {"opaType": "PersonTaskModel", "id": "123"}},
            {"index": 2, "action": "create", "subject": {"opaType": "PersonSubject", "id": "123"}, "resource": {"opaType": "DeviceModel", "id": "123"}},
            {"index": 3, "action": "create", "subject": {"opaType": "PersonSubject", "id": "123"}, "resource": {"opaType": "MemberModel", "id": "123"}},
            {"index": 4, "action": "create", "subject": {"opaType": "PersonSubject", "id": "123"}, "resource": {"opaType": "HealthDeclaration", "id": "123"}},
            {"index": 5, "action": "create", "subject": {"opaType": "PersonSubject", "id": "123"}, "resource": {"opaType": "HealthPlanTaskGroup", "id": "123"}},
            {"index": 6, "action": "create", "subject": {"opaType": "PersonSubject", "id": "123"}, "resource": {"opaType": "HealthPlanTask", "id": "123"}},
            {"index": 7, "action": "create", "subject": {"opaType": "PersonSubject", "id": "123"}, "resource": {"opaType": "ActionPlanTask", "id": "123"}},
            {"index": 8, "action": "create", "subject": {"opaType": "PersonSubject", "id": "123"}, "resource": {"opaType": "DemandActionPlan", "id": "123"}},
            {"index": 9, "action": "create", "subject": {"opaType": "PersonSubject", "id": "123"}, "resource": {"opaType": "PersonInternalReference", "id": "123"}},
            {"index": 10, "action": "create", "subject": {"opaType": "PersonSubject", "id": "123"}, "resource": {"opaType": "PersonOnboardingModel", "id": "123"}},
            {"index": 11, "action": "create", "subject": {"opaType": "PersonSubject", "id": "123"}, "resource": {"opaType": "PersonRegistrationModel", "id": "123"}},
            {"index": 12, "action": "create", "subject": {"opaType": "PersonSubject", "id": "123"}, "resource": {"opaType": "ProductOrderModel", "id": "123"}},
            {"index": 13, "action": "create", "subject": {"opaType": "PersonSubject", "id": "123"}, "resource": {"opaType": "OnboardingContractModel", "id": "123"}},
            {"index": 14, "action": "create", "subject": {"opaType": "PersonSubject", "id": "123"}, "resource": {"opaType": "PersonHealthGoalModel", "id": "123"}},
            {"index": 15, "action": "create", "subject": {"opaType": "PersonSubject", "id": "123"}, "resource": {"opaType": "PersonHealthcareTeamRecommendationModel", "id": "123"}},
            {"index": 16, "action": "create", "subject": {"opaType": "PersonSubject", "id": "123"}, "resource": {"opaType": "TestResultFileModel", "id": "123"}},
            {"index": 17, "action": "create", "subject": {"opaType": "PersonSubject", "id": "123"}, "resource": {"opaType": "InsurancePortabilityRequestModel", "id": "123"}},
            {"index": 18, "action": "create", "subject": {"opaType": "PersonSubject", "id": "123"}, "resource": {"opaType": "InsurancePortabilityRequestFileModel", "id": "123"}},
            {"index": 19, "action": "create", "subject": {"opaType": "PersonSubject", "id": "123"}, "resource": {"opaType": "LegalGuardianInfoTempModel", "id": "123"}},
            {"index": 20, "action": "create", "subject": {"opaType": "PersonSubject", "id": "123"}, "resource": {"opaType": "HealthFormQuestionAnswer", "id": "123"}},
            {"index": 21, "action": "create", "subject": {"opaType": "PersonSubject", "id": "123"}, "resource": {"opaType": "HealthFormAnswerGroup", "id": "123"}},
            {"index": 22, "action": "create", "subject": {"opaType": "PersonSubject", "id": "123"}, "resource": {"opaType": "HealthPlan", "id": "123"}},
            {"index": 23, "action": "create", "subject": {"opaType": "PersonSubject", "id": "123"}, "resource": {"opaType": "ClinicalBackground", "id": "123"}},
            {"index": 24, "action": "create", "subject": {"opaType": "PersonSubject", "id": "123"}, "resource": {"opaType": "HealthMeasurementModel", "id": "123"}},
            {"index": 25, "action": "create", "subject": {"opaType": "PersonSubject", "id": "123"}, "resource": {"opaType": "PersonAdditionalInfoModel", "id": "123"}},
            {"index": 26, "action": "create", "subject": {"opaType": "PersonSubject", "id": "123"}, "resource": {"opaType": "AppointmentScheduleModel", "id": "123"}},
            {"index": 27, "action": "create", "subject": {"opaType": "PersonSubject", "id": "123"}, "resource": {"opaType": "PersonDocumentsUploadModel", "id": "123"}},
            {"index": 28, "action": "create", "subject": {"opaType": "PersonSubject", "id": "123"}, "resource": {"opaType": "PersonBenefitModel", "id": "123"}},
            {"index": 29, "action": "create", "subject": {"opaType": "PersonSubject", "id": "123"}, "resource": {"opaType": "PersonCalendlyModel", "id": "123"}},
            {"index": 30, "action": "create", "subject": {"opaType": "PersonSubject", "id": "123"}, "resource": {"opaType": "TrackPersonABModel", "id": "123"}},
            {"index": 31, "action": "create", "subject": {"opaType": "PersonSubject", "id": "123"}, "resource": {"opaType": "FollowUpHistory", "id": "123"}},
            {"index": 32, "action": "create", "subject": {"opaType": "PersonSubject", "id": "123"}, "resource": {"opaType": "MemberOnboardingCheckpoint", "id": "123"}},
            {"index": 33, "action": "create", "subject": {"opaType": "PersonSubject", "id": "123"}, "resource": {"opaType": "PersonEligibilityDuquesa", "id": "123"}},
            {"index": 34, "action": "create", "subject": {"opaType": "PersonSubject", "id": "123"}, "resource": {"opaType": "Csat", "id": "123"}},
            {"index": 35, "action": "create", "subject": {"opaType": "PersonSubject", "id": "123"}, "resource": {"opaType": "RefundModel", "id": "123"}},
            {"index": 36, "action": "create", "subject": {"opaType": "PersonSubject", "id": "123"}, "resource": {"opaType": "RefundFileModel", "id": "123"}},
            {"index": 37, "action": "create", "subject": {"opaType": "PersonSubject", "id": "123"}, "resource": {"opaType": "FinancialDataModel", "id": "123"}},
            {"index": 38, "action": "create", "subject": {"opaType": "PersonSubject", "id": "123"}, "resource": {"opaType": "LegalGuardianAssociationModel", "id": "123"}},
            {"index": 39, "action": "create", "subject": {"opaType": "PersonSubject", "id": "123"}, "resource": {"opaType": "PersonModel", "id": "123"}},
            {"index": 40, "action": "create", "subject": {"opaType": "PersonSubject", "id": "123"}, "resource": {"opaType": "StaffModel", "id": "123"}},
            {"index": 41, "action": "create", "subject": {"opaType": "PersonSubject", "id": "123"}, "resource": {"opaType": "HealthCommunitySpecialistModel", "id": "123"}},
            {"index": 42, "action": "create", "subject": {"opaType": "PersonSubject", "id": "123"}, "resource": {"opaType": "PersonIdentityValidationModel", "id": "123"}},
            {"index": 43, "action": "create", "subject": {"opaType": "PersonSubject", "id": "123"}, "resource": {"opaType": "PersonClinicalAccount", "id": "123"}}
        ]
    }
}

test_person_subject_own_person_create_models_person_id_based_allowed if {
    {1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31,
    32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43} == member_api.allow with input as {
        "cases": [
            {"index": 1, "action": "create", "subject": {"opaType": "PersonSubject", "id": "123"}, "resource": {"opaType": "PersonTaskModel", "personId": "123"}},
            {"index": 2, "action": "create", "subject": {"opaType": "PersonSubject", "id": "123"}, "resource": {"opaType": "DeviceModel", "personId": "123"}},
            {"index": 3, "action": "create", "subject": {"opaType": "PersonSubject", "id": "123"}, "resource": {"opaType": "MemberModel", "personId": "123"}},
            {"index": 4, "action": "create", "subject": {"opaType": "PersonSubject", "id": "123"}, "resource": {"opaType": "HealthDeclaration", "personId": "123"}},
            {"index": 5, "action": "create", "subject": {"opaType": "PersonSubject", "id": "123"}, "resource": {"opaType": "HealthPlanTaskGroup", "personId": "123"}},
            {"index": 6, "action": "create", "subject": {"opaType": "PersonSubject", "id": "123"}, "resource": {"opaType": "HealthPlanTask", "personId": "123"}},
            {"index": 7, "action": "create", "subject": {"opaType": "PersonSubject", "id": "123"}, "resource": {"opaType": "ActionPlanTask", "personId": "123"}},
            {"index": 8, "action": "create", "subject": {"opaType": "PersonSubject", "id": "123"}, "resource": {"opaType": "DemandActionPlan", "personId": "123"}},
            {"index": 9, "action": "create", "subject": {"opaType": "PersonSubject", "id": "123"}, "resource": {"opaType": "PersonInternalReference", "personId": "123"}},
            {"index": 10, "action": "create", "subject": {"opaType": "PersonSubject", "id": "123"}, "resource": {"opaType": "PersonOnboardingModel", "personId": "123"}},
            {"index": 11, "action": "create", "subject": {"opaType": "PersonSubject", "id": "123"}, "resource": {"opaType": "PersonRegistrationModel", "personId": "123"}},
            {"index": 12, "action": "create", "subject": {"opaType": "PersonSubject", "id": "123"}, "resource": {"opaType": "ProductOrderModel", "personId": "123"}},
            {"index": 13, "action": "create", "subject": {"opaType": "PersonSubject", "id": "123"}, "resource": {"opaType": "OnboardingContractModel", "personId": "123"}},
            {"index": 14, "action": "create", "subject": {"opaType": "PersonSubject", "id": "123"}, "resource": {"opaType": "PersonHealthGoalModel", "personId": "123"}},
            {"index": 15, "action": "create", "subject": {"opaType": "PersonSubject", "id": "123"}, "resource": {"opaType": "PersonHealthcareTeamRecommendationModel", "personId": "123"}},
            {"index": 16, "action": "create", "subject": {"opaType": "PersonSubject", "id": "123"}, "resource": {"opaType": "TestResultFileModel", "personId": "123"}},
            {"index": 17, "action": "create", "subject": {"opaType": "PersonSubject", "id": "123"}, "resource": {"opaType": "InsurancePortabilityRequestModel", "personId": "123"}},
            {"index": 18, "action": "create", "subject": {"opaType": "PersonSubject", "id": "123"}, "resource": {"opaType": "InsurancePortabilityRequestFileModel", "personId": "123"}},
            {"index": 19, "action": "create", "subject": {"opaType": "PersonSubject", "id": "123"}, "resource": {"opaType": "LegalGuardianInfoTempModel", "personId": "123"}},
            {"index": 20, "action": "create", "subject": {"opaType": "PersonSubject", "id": "123"}, "resource": {"opaType": "HealthFormQuestionAnswer", "personId": "123"}},
            {"index": 21, "action": "create", "subject": {"opaType": "PersonSubject", "id": "123"}, "resource": {"opaType": "HealthFormAnswerGroup", "personId": "123"}},
            {"index": 22, "action": "create", "subject": {"opaType": "PersonSubject", "id": "123"}, "resource": {"opaType": "HealthPlan", "personId": "123"}},
            {"index": 23, "action": "create", "subject": {"opaType": "PersonSubject", "id": "123"}, "resource": {"opaType": "ClinicalBackground", "personId": "123"}},
            {"index": 24, "action": "create", "subject": {"opaType": "PersonSubject", "id": "123"}, "resource": {"opaType": "HealthMeasurementModel", "personId": "123"}},
            {"index": 25, "action": "create", "subject": {"opaType": "PersonSubject", "id": "123"}, "resource": {"opaType": "PersonAdditionalInfoModel", "personId": "123"}},
            {"index": 26, "action": "create", "subject": {"opaType": "PersonSubject", "id": "123"}, "resource": {"opaType": "AppointmentScheduleModel", "personId": "123"}},
            {"index": 27, "action": "create", "subject": {"opaType": "PersonSubject", "id": "123"}, "resource": {"opaType": "PersonDocumentsUploadModel", "personId": "123"}},
            {"index": 28, "action": "create", "subject": {"opaType": "PersonSubject", "id": "123"}, "resource": {"opaType": "PersonBenefitModel", "personId": "123"}},
            {"index": 29, "action": "create", "subject": {"opaType": "PersonSubject", "id": "123"}, "resource": {"opaType": "PersonCalendlyModel", "personId": "123"}},
            {"index": 30, "action": "create", "subject": {"opaType": "PersonSubject", "id": "123"}, "resource": {"opaType": "TrackPersonABModel", "personId": "123"}},
            {"index": 31, "action": "create", "subject": {"opaType": "PersonSubject", "id": "123"}, "resource": {"opaType": "FollowUpHistory", "personId": "123"}},
            {"index": 32, "action": "create", "subject": {"opaType": "PersonSubject", "id": "123"}, "resource": {"opaType": "MemberOnboardingCheckpoint", "personId": "123"}},
            {"index": 33, "action": "create", "subject": {"opaType": "PersonSubject", "id": "123"}, "resource": {"opaType": "PersonEligibilityDuquesa", "personId": "123"}},
            {"index": 34, "action": "create", "subject": {"opaType": "PersonSubject", "id": "123"}, "resource": {"opaType": "Csat", "personId": "123"}},
            {"index": 35, "action": "create", "subject": {"opaType": "PersonSubject", "id": "123"}, "resource": {"opaType": "RefundModel", "personId": "123"}},
            {"index": 36, "action": "create", "subject": {"opaType": "PersonSubject", "id": "123"}, "resource": {"opaType": "RefundFileModel", "personId": "123"}},
            {"index": 37, "action": "create", "subject": {"opaType": "PersonSubject", "id": "123"}, "resource": {"opaType": "FinancialDataModel", "personId": "123"}},
            {"index": 38, "action": "create", "subject": {"opaType": "PersonSubject", "id": "123"}, "resource": {"opaType": "LegalGuardianAssociationModel", "personId": "123"}},
            {"index": 39, "action": "create", "subject": {"opaType": "PersonSubject", "id": "123"}, "resource": {"opaType": "PersonModel", "personId": "123"}},
            {"index": 40, "action": "create", "subject": {"opaType": "PersonSubject", "id": "123"}, "resource": {"opaType": "StaffModel", "personId": "123"}},
            {"index": 41, "action": "create", "subject": {"opaType": "PersonSubject", "id": "123"}, "resource": {"opaType": "HealthCommunitySpecialistModel", "personId": "123"}},
            {"index": 42, "action": "create", "subject": {"opaType": "PersonSubject", "id": "123"}, "resource": {"opaType": "PersonIdentityValidationModel", "personId": "123"}},
            {"index": 43, "action": "create", "subject": {"opaType": "PersonSubject", "id": "123"}, "resource": {"opaType": "PersonClinicalAccount", "personId": "123"}}
        ]
    }
}

test_person_subject_own_person_update_models_id_based_allowed if {
    {1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31,
    32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43} == member_api.allow with input as {
        "cases": [
            {"index": 1, "action": "update", "subject": {"opaType": "PersonSubject", "id": "123"}, "resource": {"opaType": "PersonTaskModel", "id": "123"}},
            {"index": 2, "action": "update", "subject": {"opaType": "PersonSubject", "id": "123"}, "resource": {"opaType": "DeviceModel", "id": "123"}},
            {"index": 3, "action": "update", "subject": {"opaType": "PersonSubject", "id": "123"}, "resource": {"opaType": "MemberModel", "id": "123"}},
            {"index": 4, "action": "update", "subject": {"opaType": "PersonSubject", "id": "123"}, "resource": {"opaType": "HealthDeclaration", "id": "123"}},
            {"index": 5, "action": "update", "subject": {"opaType": "PersonSubject", "id": "123"}, "resource": {"opaType": "HealthPlanTaskGroup", "id": "123"}},
            {"index": 6, "action": "update", "subject": {"opaType": "PersonSubject", "id": "123"}, "resource": {"opaType": "HealthPlanTask", "id": "123"}},
            {"index": 7, "action": "update", "subject": {"opaType": "PersonSubject", "id": "123"}, "resource": {"opaType": "ActionPlanTask", "id": "123"}},
            {"index": 8, "action": "update", "subject": {"opaType": "PersonSubject", "id": "123"}, "resource": {"opaType": "DemandActionPlan", "id": "123"}},
            {"index": 9, "action": "update", "subject": {"opaType": "PersonSubject", "id": "123"}, "resource": {"opaType": "PersonInternalReference", "id": "123"}},
            {"index": 10, "action": "update", "subject": {"opaType": "PersonSubject", "id": "123"}, "resource": {"opaType": "PersonOnboardingModel", "id": "123"}},
            {"index": 11, "action": "update", "subject": {"opaType": "PersonSubject", "id": "123"}, "resource": {"opaType": "PersonRegistrationModel", "id": "123"}},
            {"index": 12, "action": "update", "subject": {"opaType": "PersonSubject", "id": "123"}, "resource": {"opaType": "ProductOrderModel", "id": "123"}},
            {"index": 13, "action": "update", "subject": {"opaType": "PersonSubject", "id": "123"}, "resource": {"opaType": "OnboardingContractModel", "id": "123"}},
            {"index": 14, "action": "update", "subject": {"opaType": "PersonSubject", "id": "123"}, "resource": {"opaType": "PersonHealthGoalModel", "id": "123"}},
            {"index": 15, "action": "update", "subject": {"opaType": "PersonSubject", "id": "123"}, "resource": {"opaType": "PersonHealthcareTeamRecommendationModel", "id": "123"}},
            {"index": 16, "action": "update", "subject": {"opaType": "PersonSubject", "id": "123"}, "resource": {"opaType": "TestResultFileModel", "id": "123"}},
            {"index": 17, "action": "update", "subject": {"opaType": "PersonSubject", "id": "123"}, "resource": {"opaType": "InsurancePortabilityRequestModel", "id": "123"}},
            {"index": 18, "action": "update", "subject": {"opaType": "PersonSubject", "id": "123"}, "resource": {"opaType": "InsurancePortabilityRequestFileModel", "id": "123"}},
            {"index": 19, "action": "update", "subject": {"opaType": "PersonSubject", "id": "123"}, "resource": {"opaType": "LegalGuardianInfoTempModel", "id": "123"}},
            {"index": 20, "action": "update", "subject": {"opaType": "PersonSubject", "id": "123"}, "resource": {"opaType": "HealthFormQuestionAnswer", "id": "123"}},
            {"index": 21, "action": "update", "subject": {"opaType": "PersonSubject", "id": "123"}, "resource": {"opaType": "HealthFormAnswerGroup", "id": "123"}},
            {"index": 22, "action": "update", "subject": {"opaType": "PersonSubject", "id": "123"}, "resource": {"opaType": "HealthPlan", "id": "123"}},
            {"index": 23, "action": "update", "subject": {"opaType": "PersonSubject", "id": "123"}, "resource": {"opaType": "ClinicalBackground", "id": "123"}},
            {"index": 24, "action": "update", "subject": {"opaType": "PersonSubject", "id": "123"}, "resource": {"opaType": "HealthMeasurementModel", "id": "123"}},
            {"index": 25, "action": "update", "subject": {"opaType": "PersonSubject", "id": "123"}, "resource": {"opaType": "PersonAdditionalInfoModel", "id": "123"}},
            {"index": 26, "action": "update", "subject": {"opaType": "PersonSubject", "id": "123"}, "resource": {"opaType": "AppointmentScheduleModel", "id": "123"}},
            {"index": 27, "action": "update", "subject": {"opaType": "PersonSubject", "id": "123"}, "resource": {"opaType": "PersonDocumentsUploadModel", "id": "123"}},
            {"index": 28, "action": "update", "subject": {"opaType": "PersonSubject", "id": "123"}, "resource": {"opaType": "PersonBenefitModel", "id": "123"}},
            {"index": 29, "action": "update", "subject": {"opaType": "PersonSubject", "id": "123"}, "resource": {"opaType": "PersonCalendlyModel", "id": "123"}},
            {"index": 30, "action": "update", "subject": {"opaType": "PersonSubject", "id": "123"}, "resource": {"opaType": "TrackPersonABModel", "id": "123"}},
            {"index": 31, "action": "update", "subject": {"opaType": "PersonSubject", "id": "123"}, "resource": {"opaType": "FollowUpHistory", "id": "123"}},
            {"index": 32, "action": "update", "subject": {"opaType": "PersonSubject", "id": "123"}, "resource": {"opaType": "MemberOnboardingCheckpoint", "id": "123"}},
            {"index": 33, "action": "update", "subject": {"opaType": "PersonSubject", "id": "123"}, "resource": {"opaType": "PersonEligibilityDuquesa", "id": "123"}},
            {"index": 34, "action": "update", "subject": {"opaType": "PersonSubject", "id": "123"}, "resource": {"opaType": "Csat", "id": "123"}},
            {"index": 35, "action": "update", "subject": {"opaType": "PersonSubject", "id": "123"}, "resource": {"opaType": "RefundModel", "id": "123"}},
            {"index": 36, "action": "update", "subject": {"opaType": "PersonSubject", "id": "123"}, "resource": {"opaType": "RefundFileModel", "id": "123"}},
            {"index": 37, "action": "update", "subject": {"opaType": "PersonSubject", "id": "123"}, "resource": {"opaType": "FinancialDataModel", "id": "123"}},
            {"index": 38, "action": "update", "subject": {"opaType": "PersonSubject", "id": "123"}, "resource": {"opaType": "LegalGuardianAssociationModel", "id": "123"}},
            {"index": 39, "action": "update", "subject": {"opaType": "PersonSubject", "id": "123"}, "resource": {"opaType": "PersonModel", "id": "123"}},
            {"index": 40, "action": "update", "subject": {"opaType": "PersonSubject", "id": "123"}, "resource": {"opaType": "StaffModel", "id": "123"}},
            {"index": 41, "action": "update", "subject": {"opaType": "PersonSubject", "id": "123"}, "resource": {"opaType": "HealthCommunitySpecialistModel", "id": "123"}},
            {"index": 42, "action": "update", "subject": {"opaType": "PersonSubject", "id": "123"}, "resource": {"opaType": "PersonIdentityValidationModel", "id": "123"}},
            {"index": 43, "action": "update", "subject": {"opaType": "PersonSubject", "id": "123"}, "resource": {"opaType": "PersonClinicalAccount", "id": "123"}}
        ]
    }
}

test_person_subject_own_person_update_models_person_id_based_allowed if {
    {1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31,
    32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43} == member_api.allow with input as {
        "cases": [
            {"index": 1, "action": "update", "subject": {"opaType": "PersonSubject", "id": "123"}, "resource": {"opaType": "PersonTaskModel", "personId": "123"}},
            {"index": 2, "action": "update", "subject": {"opaType": "PersonSubject", "id": "123"}, "resource": {"opaType": "DeviceModel", "personId": "123"}},
            {"index": 3, "action": "update", "subject": {"opaType": "PersonSubject", "id": "123"}, "resource": {"opaType": "MemberModel", "personId": "123"}},
            {"index": 4, "action": "update", "subject": {"opaType": "PersonSubject", "id": "123"}, "resource": {"opaType": "HealthDeclaration", "personId": "123"}},
            {"index": 5, "action": "update", "subject": {"opaType": "PersonSubject", "id": "123"}, "resource": {"opaType": "HealthPlanTaskGroup", "personId": "123"}},
            {"index": 6, "action": "update", "subject": {"opaType": "PersonSubject", "id": "123"}, "resource": {"opaType": "HealthPlanTask", "personId": "123"}},
            {"index": 7, "action": "update", "subject": {"opaType": "PersonSubject", "id": "123"}, "resource": {"opaType": "ActionPlanTask", "personId": "123"}},
            {"index": 8, "action": "update", "subject": {"opaType": "PersonSubject", "id": "123"}, "resource": {"opaType": "DemandActionPlan", "personId": "123"}},
            {"index": 9, "action": "update", "subject": {"opaType": "PersonSubject", "id": "123"}, "resource": {"opaType": "PersonInternalReference", "personId": "123"}},
            {"index": 10, "action": "update", "subject": {"opaType": "PersonSubject", "id": "123"}, "resource": {"opaType": "PersonOnboardingModel", "personId": "123"}},
            {"index": 11, "action": "update", "subject": {"opaType": "PersonSubject", "id": "123"}, "resource": {"opaType": "PersonRegistrationModel", "personId": "123"}},
            {"index": 12, "action": "update", "subject": {"opaType": "PersonSubject", "id": "123"}, "resource": {"opaType": "ProductOrderModel", "personId": "123"}},
            {"index": 13, "action": "update", "subject": {"opaType": "PersonSubject", "id": "123"}, "resource": {"opaType": "OnboardingContractModel", "personId": "123"}},
            {"index": 14, "action": "update", "subject": {"opaType": "PersonSubject", "id": "123"}, "resource": {"opaType": "PersonHealthGoalModel", "personId": "123"}},
            {"index": 15, "action": "update", "subject": {"opaType": "PersonSubject", "id": "123"}, "resource": {"opaType": "PersonHealthcareTeamRecommendationModel", "personId": "123"}},
            {"index": 16, "action": "update", "subject": {"opaType": "PersonSubject", "id": "123"}, "resource": {"opaType": "TestResultFileModel", "personId": "123"}},
            {"index": 17, "action": "update", "subject": {"opaType": "PersonSubject", "id": "123"}, "resource": {"opaType": "InsurancePortabilityRequestModel", "personId": "123"}},
            {"index": 18, "action": "update", "subject": {"opaType": "PersonSubject", "id": "123"}, "resource": {"opaType": "InsurancePortabilityRequestFileModel", "personId": "123"}},
            {"index": 19, "action": "update", "subject": {"opaType": "PersonSubject", "id": "123"}, "resource": {"opaType": "LegalGuardianInfoTempModel", "personId": "123"}},
            {"index": 20, "action": "update", "subject": {"opaType": "PersonSubject", "id": "123"}, "resource": {"opaType": "HealthFormQuestionAnswer", "personId": "123"}},
            {"index": 21, "action": "update", "subject": {"opaType": "PersonSubject", "id": "123"}, "resource": {"opaType": "HealthFormAnswerGroup", "personId": "123"}},
            {"index": 22, "action": "update", "subject": {"opaType": "PersonSubject", "id": "123"}, "resource": {"opaType": "HealthPlan", "personId": "123"}},
            {"index": 23, "action": "update", "subject": {"opaType": "PersonSubject", "id": "123"}, "resource": {"opaType": "ClinicalBackground", "personId": "123"}},
            {"index": 24, "action": "update", "subject": {"opaType": "PersonSubject", "id": "123"}, "resource": {"opaType": "HealthMeasurementModel", "personId": "123"}},
            {"index": 25, "action": "update", "subject": {"opaType": "PersonSubject", "id": "123"}, "resource": {"opaType": "PersonAdditionalInfoModel", "personId": "123"}},
            {"index": 26, "action": "update", "subject": {"opaType": "PersonSubject", "id": "123"}, "resource": {"opaType": "AppointmentScheduleModel", "personId": "123"}},
            {"index": 27, "action": "update", "subject": {"opaType": "PersonSubject", "id": "123"}, "resource": {"opaType": "PersonDocumentsUploadModel", "personId": "123"}},
            {"index": 28, "action": "update", "subject": {"opaType": "PersonSubject", "id": "123"}, "resource": {"opaType": "PersonBenefitModel", "personId": "123"}},
            {"index": 29, "action": "update", "subject": {"opaType": "PersonSubject", "id": "123"}, "resource": {"opaType": "PersonCalendlyModel", "personId": "123"}},
            {"index": 30, "action": "update", "subject": {"opaType": "PersonSubject", "id": "123"}, "resource": {"opaType": "TrackPersonABModel", "personId": "123"}},
            {"index": 31, "action": "update", "subject": {"opaType": "PersonSubject", "id": "123"}, "resource": {"opaType": "FollowUpHistory", "personId": "123"}},
            {"index": 32, "action": "update", "subject": {"opaType": "PersonSubject", "id": "123"}, "resource": {"opaType": "MemberOnboardingCheckpoint", "personId": "123"}},
            {"index": 33, "action": "update", "subject": {"opaType": "PersonSubject", "id": "123"}, "resource": {"opaType": "PersonEligibilityDuquesa", "personId": "123"}},
            {"index": 34, "action": "update", "subject": {"opaType": "PersonSubject", "id": "123"}, "resource": {"opaType": "Csat", "personId": "123"}},
            {"index": 35, "action": "update", "subject": {"opaType": "PersonSubject", "id": "123"}, "resource": {"opaType": "RefundModel", "personId": "123"}},
            {"index": 36, "action": "update", "subject": {"opaType": "PersonSubject", "id": "123"}, "resource": {"opaType": "RefundFileModel", "personId": "123"}},
            {"index": 37, "action": "update", "subject": {"opaType": "PersonSubject", "id": "123"}, "resource": {"opaType": "FinancialDataModel", "personId": "123"}},
            {"index": 38, "action": "update", "subject": {"opaType": "PersonSubject", "id": "123"}, "resource": {"opaType": "LegalGuardianAssociationModel", "personId": "123"}},
            {"index": 39, "action": "update", "subject": {"opaType": "PersonSubject", "id": "123"}, "resource": {"opaType": "PersonModel", "personId": "123"}},
            {"index": 40, "action": "update", "subject": {"opaType": "PersonSubject", "id": "123"}, "resource": {"opaType": "StaffModel", "personId": "123"}},
            {"index": 41, "action": "update", "subject": {"opaType": "PersonSubject", "id": "123"}, "resource": {"opaType": "HealthCommunitySpecialistModel", "personId": "123"}},
            {"index": 42, "action": "update", "subject": {"opaType": "PersonSubject", "id": "123"}, "resource": {"opaType": "PersonIdentityValidationModel", "personId": "123"}},
            {"index": 43, "action": "update", "subject": {"opaType": "PersonSubject", "id": "123"}, "resource": {"opaType": "PersonClinicalAccount", "personId": "123"}}
        ]
    }
}

test_person_subject_own_person_delete_health_form_question_answer_id_and_person_id_based_allowed if {
    {1, 2} == member_api.allow with input as {
        "cases": [
            { "index": 1, "action": "delete", "subject": { "opaType": "PersonSubject", "id": "123" }, "resource": { "opaType": "HealthFormQuestionAnswer", "id": "123" } },
            { "index": 2, "action": "delete", "subject": { "opaType": "PersonSubject", "id": "123" }, "resource": { "opaType": "HealthFormQuestionAnswer", "personId": "123" } }
        ]
    }
}

test_person_subject_own_person_accredited_network_favorite_id_and_person_id_based_allowed if {
    {1, 2, 3, 4, 5, 6, 7, 8} == member_api.allow with input as {
        "cases": [
            { "index": 1, "action": "view", "subject": { "opaType": "PersonSubject", "id": "123" }, "resource": { "opaType": "AccreditedNetworkFavorite", "id": "123" } },
            { "index": 2, "action": "view", "subject": { "opaType": "PersonSubject", "id": "123" }, "resource": { "opaType": "AccreditedNetworkFavorite", "personId": "123" } },

            { "index": 3, "action": "create", "subject": { "opaType": "PersonSubject", "id": "123" }, "resource": { "opaType": "AccreditedNetworkFavorite", "id": "123" } },
            { "index": 4, "action": "create", "subject": { "opaType": "PersonSubject", "id": "123" }, "resource": { "opaType": "AccreditedNetworkFavorite", "personId": "123" } },

            { "index": 5, "action": "update", "subject": { "opaType": "PersonSubject", "id": "123" }, "resource": { "opaType": "AccreditedNetworkFavorite", "id": "123" } },
            { "index": 6, "action": "update", "subject": { "opaType": "PersonSubject", "id": "123" }, "resource": { "opaType": "AccreditedNetworkFavorite", "personId": "123" } },

            { "index": 7, "action": "delete", "subject": { "opaType": "PersonSubject", "id": "123" }, "resource": { "opaType": "AccreditedNetworkFavorite", "id": "123" } },
            { "index": 8, "action": "delete", "subject": { "opaType": "PersonSubject", "id": "123" }, "resource": { "opaType": "AccreditedNetworkFavorite", "personId": "123" } }
        ]
    }
}

test_person_subject_own_member_view_cassi_member_model_allowed if {
    1 in member_api.allow with input as {
        "cases": [
            { "index": 1, "action": "view", "subject": { "opaType": "PersonSubject", "members": ["123", "456"] }, "resource": { "opaType": "CassiMemberModel", "memberId": "456" } }
        ]
    }
}

test_person_subject_own_member_member_contract_models_allowed if {
    {1, 2, 3, 4} == member_api.allow with input as {
        "cases": [
            { "index": 1, "action": "view", "subject": { "opaType": "PersonSubject", "members": ["123", "456"] }, "resource": { "opaType": "MemberContractModel", "memberId": "456" } },
            { "index": 2, "action": "update", "subject": { "opaType": "PersonSubject", "members": ["123", "456"] }, "resource": { "opaType": "MemberContractModel", "memberId": "456" } },

            { "index": 3, "action": "view", "subject": { "opaType": "PersonSubject", "members": ["123", "456"] }, "resource": { "opaType": "MemberContractTermModel", "memberId": "456" } },
            { "index": 4, "action": "update", "subject": { "opaType": "PersonSubject", "members": ["123", "456"] }, "resource": { "opaType": "MemberContractTermModel", "memberId": "456" } }
        ]
    }
}

test_person_subject_her_dependent_view_person_model_allowed if {
    1 in member_api.allow with input as {
        "cases": [
            { "index": 1, "action": "view", "subject": { "opaType": "PersonSubject", "dependentPersons": ["123", "456"] }, "resource": { "opaType": "PersonModel", "personId": "456" } },
        ]
    }
}

test_person_subject_her_dependent_view_member_model_allowed if {
    1 in member_api.allow with input as {
        "cases": [
            { "index": 1, "action": "view", "subject": { "opaType": "PersonSubject", "dependentMembers": ["123", "456"] }, "resource": { "opaType": "MemberModel", "memberId": "456" } },
        ]
    }
}

test_person_subject_her_dependent_view_create_file_vault_allowed if {
    {1, 2} == member_api.allow with input as {
        "cases": [
            { "index": 1, "action": "view", "subject": { "opaType": "PersonSubject", "dependentPersons": ["123", "456"] }, "resource": { "opaType": "FileVault", "personId": "456" } },
            { "index": 2, "action": "create", "subject": { "opaType": "PersonSubject", "dependentPersons": ["123", "456"] }, "resource": { "opaType": "FileVault", "personId": "456" } }
        ]
    }
}

test_person_subject_her_dependent_view_create_update_health_declaration_allowed if {
    {1, 2, 3} == member_api.allow with input as {
        "cases": [
            { "index": 1, "action": "view", "subject": { "opaType": "PersonSubject", "dependentPersons": ["123", "456"] }, "resource": { "opaType": "HealthDeclaration", "personId": "456" } },
            { "index": 2, "action": "create", "subject": { "opaType": "PersonSubject", "dependentPersons": ["123", "456"] }, "resource": { "opaType": "HealthDeclaration", "personId": "456" } },
            { "index": 3, "action": "update", "subject": { "opaType": "PersonSubject", "dependentPersons": ["123", "456"] }, "resource": { "opaType": "HealthDeclaration", "personId": "456" } }
        ]
    }
}

test_person_subject_her_dependent_view_update_member_contract_allowed if {
    {1, 2, 3, 4} == member_api.allow with input as {
        "cases": [
            { "index": 1, "action": "view", "subject": { "opaType": "PersonSubject", "dependentMembers": ["123", "456"] }, "resource": { "opaType": "MemberContractModel", "memberId": "456" } },
            { "index": 2, "action": "update", "subject": { "opaType": "PersonSubject", "dependentMembers": ["123", "456"] }, "resource": { "opaType": "MemberContractModel", "memberId": "456" } },

            { "index": 3, "action": "view", "subject": { "opaType": "PersonSubject", "dependentMembers": ["123", "456"] }, "resource": { "opaType": "MemberContractTermModel", "memberId": "456" } },
            { "index": 4, "action": "update", "subject": { "opaType": "PersonSubject", "dependentMembers": ["123", "456"] }, "resource": { "opaType": "MemberContractTermModel", "memberId": "456" } }
        ]
    }
}

test_person_subject_her_parent_view_person_model_allowed if {
    1 in member_api.allow with input as {
        "cases": [
            { "index": 1, "action": "view", "subject": { "opaType": "PersonSubject", "parentPerson": {"id": "456"} }, "resource": { "opaType": "PersonModel", "id": "456" } },
        ]
    }
}

test_person_subject_her_parent_view_models_allowed if {
    {1, 2} == member_api.allow with input as {
        "cases": [
            { "index": 1, "action": "view", "subject": { "opaType": "PersonSubject", "parentPerson": {"id": "456"} }, "resource": { "opaType": "MemberModel", "personId": "456" } },
            { "index": 2, "action": "view", "subject": { "opaType": "PersonSubject", "parentPerson": {"id": "456"} }, "resource": { "opaType": "PersonModel", "personId": "456" } }
        ]
    }
}

test_person_subject_her_parent_view_create_update_financial_data_model_allowed if {
    {1, 2, 3} == member_api.allow with input as {
        "cases": [
            { "index": 1, "action": "view", "subject": { "opaType": "PersonSubject", "parentPerson": {"id": "456"} }, "resource": { "opaType": "FinancialDataModel", "personId": "456" } },
            { "index": 2, "action": "create", "subject": { "opaType": "PersonSubject", "parentPerson": {"id": "456"} }, "resource": { "opaType": "FinancialDataModel", "personId": "456" } },
            { "index": 3, "action": "update", "subject": { "opaType": "PersonSubject", "parentPerson": {"id": "456"} }, "resource": { "opaType": "FinancialDataModel", "personId": "456" } }
        ]
    }
}


test_person_subject_her_legal_guardian_view_person_model_allowed if {
    1 in member_api.allow with input as {
        "cases": [
            { "index": 1, "action": "view", "subject": { "opaType": "PersonSubject", "legalGuardianPersons": "456" }, "resource": { "opaType": "PersonModel", "id": "456" } },
        ]
    }
}

test_person_subject_her_legal_guardian_view_create_update_financial_data_model_allowed if {
    {1, 2, 3} == member_api.allow with input as {
        "cases": [
            { "index": 1, "action": "view", "subject": { "opaType": "PersonSubject", "legalGuardianPersons": "456" }, "resource": { "opaType": "FinancialDataModel", "personId": "456" } },
            { "index": 2, "action": "create", "subject": { "opaType": "PersonSubject", "legalGuardianPersons": "456" }, "resource": { "opaType": "FinancialDataModel", "personId": "456" } },
            { "index": 3, "action": "update", "subject": { "opaType": "PersonSubject", "legalGuardianPersons": "456" }, "resource": { "opaType": "FinancialDataModel", "personId": "456" } }
        ]
    }
}

test_person_subject_her_member_invoice_group_view_model_allowed if {
    1 in member_api.allow with input as {
        "cases": [
            { "index": 1, "action": "view", "subject": { "opaType": "PersonSubject", "billingAccountablePartyId": "456" }, "resource": { "opaType": "MemberInvoiceGroupModel", "billingAccountablePartyId": "456" } }
        ]
    }
}

test_person_subject_create_update_her_invoice_payment_model_allowed if {
    {1, 2} == member_api.allow with input as {
        "cases": [
            { "index": 1, "action": "create", "subject": { "opaType": "PersonSubject", "billingAccountablePartyId": "456" }, "resource": { "opaType": "InvoicePaymentModel", "billingAccountablePartyId": "456" } },
            { "index": 2, "action": "update", "subject": { "opaType": "PersonSubject", "billingAccountablePartyId": "456" }, "resource": { "opaType": "InvoicePaymentModel", "billingAccountablePartyId": "456" } }
        ]
    }
}

test_person_subject_view_create_update_her_health_professional_model_allowed if {
    {1, 2, 3} == member_api.allow with input as {
        "cases": [
            { "index": 1, "action": "view", "subject": { "opaType": "PersonSubject", "id": "456" }, "resource": { "opaType": "HealthProfessionalModel", "staffId": "456" } },
            { "index": 2, "action": "create", "subject": { "opaType": "PersonSubject", "id": "456" }, "resource": { "opaType": "HealthProfessionalModel", "staffId": "456" } },
            { "index": 3, "action": "update", "subject": { "opaType": "PersonSubject", "id": "456" }, "resource": { "opaType": "HealthProfessionalModel", "staffId": "456" } }
        ]
    }
}


test_person_subject_view_create_update_her_shopping_cart_model_allowed if {
    {1, 2, 3} == member_api.allow with input as {
        "cases": [
            { "index": 1, "action": "view", "subject": { "opaType": "PersonSubject", "personLeadId": "456" }, "resource": { "opaType": "ShoppingCartModel", "leadId": "456" } },
            { "index": 2, "action": "create", "subject": { "opaType": "PersonSubject", "personLeadId": "456" }, "resource": { "opaType": "ShoppingCartModel", "leadId": "456" } },
            { "index": 3, "action": "update", "subject": { "opaType": "PersonSubject", "personLeadId": "456" }, "resource": { "opaType": "ShoppingCartModel", "leadId": "456" } }
        ]
    }
}

test_person_subject_view_create_update_her_lead_model_id_based_allowed if {
    {1, 2, 3} == member_api.allow with input as {
        "cases": [
            { "index": 1, "action": "view", "subject": { "opaType": "PersonSubject", "personLeadId": "456" }, "resource": { "opaType": "Lead", "id": "456" } },
            { "index": 2, "action": "create", "subject": { "opaType": "PersonSubject", "personLeadId": "456" }, "resource": { "opaType": "Lead", "id": "456" } },
            { "index": 3, "action": "update", "subject": { "opaType": "PersonSubject", "personLeadId": "456" }, "resource": { "opaType": "Lead", "id": "456" } }
        ]
    }
}

test_person_subject_view_create_update_her_lead_model_email_based_allowed if {
    {1, 2, 3} == member_api.allow with input as {
        "cases": [
            { "index": 1, "action": "view", "subject": { "opaType": "PersonSubject", "personEmail": "<EMAIL>" }, "resource": { "opaType": "Lead", "email": "<EMAIL>" } },
            { "index": 2, "action": "create", "subject": { "opaType": "PersonSubject", "personEmail": "<EMAIL>" }, "resource": { "opaType": "Lead", "email": "<EMAIL>" } },
            { "index": 3, "action": "update", "subject": { "opaType": "PersonSubject", "personEmail": "<EMAIL>" }, "resource": { "opaType": "Lead", "email": "<EMAIL>" } }
        ]
    }
}

test_person_subject_view_create_update_faq_feed_back_model_without_person_id_allowed if {
    {1, 2, 3} == member_api.allow with input as {
        "cases": [
            { "index": 1, "action": "view", "subject": { "opaType": "PersonSubject", "id": "456" }, "resource": { "opaType": "FaqFeedbackModel" } },
            { "index": 2, "action": "create", "subject": { "opaType": "PersonSubject", "id": "456" }, "resource": { "opaType": "FaqFeedbackModel" } },
            { "index": 3, "action": "update", "subject": { "opaType": "PersonSubject", "id": "456" }, "resource": { "opaType": "FaqFeedbackModel" } }
        ]
    }
}


test_person_subject_view_create_update_faq_feed_back_model_with_person_id_allowed if {
    {1, 2, 3} == member_api.allow with input as {
        "cases": [
            { "index": 1, "action": "view", "subject": { "opaType": "PersonSubject", "id": "456" }, "resource": { "opaType": "FaqFeedbackModel", "personId": "456" } },
            { "index": 2, "action": "create", "subject": { "opaType": "PersonSubject", "id": "456" }, "resource": { "opaType": "FaqFeedbackModel", "personId": "456" } },
            { "index": 3, "action": "update", "subject": { "opaType": "PersonSubject", "id": "456" }, "resource": { "opaType": "FaqFeedbackModel", "personId": "456" } }
        ]
    }
}

test_person_subject_create_update_her_person_health_event_model_without_staff_id_allowed if {
    {1, 2} == member_api.allow with input as {
        "cases": [
            { "index": 1, "action": "create", "subject": { "opaType": "PersonSubject" }, "resource": { "opaType": "PersonHealthEvent" } },
            { "index": 2, "action": "update", "subject": { "opaType": "PersonSubject" }, "resource": { "opaType": "PersonHealthEvent" } }
        ]
    }
}

test_person_subject_create_update_her_person_health_event_model_with_person_id_allowed if {
    {1, 2} == member_api.allow with input as {
        "cases": [
            { "index": 1, "action": "create", "subject": { "opaType": "PersonSubject", "id": "456" }, "resource": { "opaType": "PersonHealthEvent", "personId": "456" } },
            { "index": 2, "action": "update", "subject": { "opaType": "PersonSubject", "id": "456" }, "resource": { "opaType": "PersonHealthEvent", "personId": "456" } }
        ]
    }
}

test_aggregates if {
    member_api.is_aggregate with input as {
        "cases": [{
            "index": 1,
            "action": "view",
            "resource": {
                "opaType": "ProviderModel"
            }
        }]
    }
    member_api.is_aggregate with input as {
        "cases": [{
            "index": 1,
            "action": "view",
            "resource": {
                "opaType": "StaffScheduleModel"
            }
        }]
    }
    member_api.is_aggregate with input as {
        "cases": [{
            "index": 1,
            "action": "view",
            "resource": {
                "opaType": "ExternalCalendarEventModel"
            }
        }]
    }
    not member_api.is_aggregate with input as {
        "cases": [{
            "index": 1,
            "action": "view",
            "resource": {
                "opaType": "SomethingElse"
            }
        }]
    }
}
