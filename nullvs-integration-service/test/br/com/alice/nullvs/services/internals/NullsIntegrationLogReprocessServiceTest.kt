package br.com.alice.nullvs.services.internals

import br.com.alice.business.client.CompanyContractService
import br.com.alice.business.client.CompanySubContractService
import br.com.alice.common.RangeUUID
import br.com.alice.common.helpers.coVerifyNone
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.common.helpers.returns
import br.com.alice.common.kafka.interfaces.KafkaProducerService
import br.com.alice.common.kafka.interfaces.ProducerResult
import br.com.alice.common.models.Sex
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.BatchType
import br.com.alice.data.layer.models.ExternalModelType
import br.com.alice.data.layer.models.InternalModelType
import br.com.alice.data.layer.models.LogStatus
import br.com.alice.data.layer.models.MemberStatus
import br.com.alice.data.layer.models.ProductType
import br.com.alice.nullvs.client.exceptions.TotvsMemberGetException
import br.com.alice.nullvs.clients.TotvsMemberClient
import br.com.alice.nullvs.common.NullvsActionType
import br.com.alice.nullvs.converters.toModel
import br.com.alice.nullvs.events.NullvsSyncCompanyContractRequestEvent
import br.com.alice.nullvs.events.NullvsSyncCompanySubcontractRequestEvent
import br.com.alice.nullvs.events.NullvsSyncMemberRequestEvent
import br.com.alice.nullvs.models.CancelWaitingLogsRequest
import br.com.alice.nullvs.models.Meta
import br.com.alice.nullvs.models.TotvsMemberGetWebServiceResponse
import br.com.alice.person.client.MemberService
import com.github.kittinunf.result.failure
import io.mockk.clearAllMocks
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.mockk
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.TestInstance
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.MethodSource
import java.time.LocalDateTime
import java.util.UUID
import kotlin.test.AfterTest
import kotlin.test.Test

@TestInstance(TestInstance.Lifecycle.PER_CLASS)
class NullsIntegrationLogReprocessServiceTest {

    private val nullvsIntegrationLogService: NullvsIntegrationLogService = mockk()
    private val memberService: MemberService = mockk()
    private val kafkaProducerService: KafkaProducerService = mockk()
    private val totvsMemberClient: TotvsMemberClient = mockk()
    private val subContractService: CompanySubContractService = mockk()
    private val contractService: CompanyContractService = mockk()

    companion object {
        @JvmStatic
        fun processorInternalModels() = listOf(
            InternalModelType.MEMBER,
            InternalModelType.SUBCONTRACT,
            InternalModelType.CONTRACT
        )
    }

    private val service =
        NullsIntegrationLogReprocessService(
            nullvsIntegrationLogService,
            memberService,
            kafkaProducerService,
            totvsMemberClient,
            subContractService,
            contractService
        )

    @AfterTest
    fun setup() = clearAllMocks()

    private val memberId = UUID.randomUUID()
    private val secondMemberId = UUID.randomUUID()

    private val priceListing = TestModelFactory.buildPriceListing()
    private val product = TestModelFactory.buildProduct(ansNumber = "491.970/22-0", priceListing = priceListing)

    private val person =
        TestModelFactory.buildPerson(
            mothersName = "Maria Pereira",
            phoneNumber = "31999998888",
            sex = Sex.FEMALE,
            dateOfBirth = LocalDateTime.of(1994, 10, 17, 14, 0),
        )

    private val member =
        TestModelFactory.buildMember(
            id = memberId,
            personId = person.id,
            product = product,
            productType = ProductType.B2C
        )

    private val secondMember =
        TestModelFactory.buildMember(
            id = secondMemberId,
            personId = person.id,
            product = product,
            productType = ProductType.B2C
        )

    private val totvsMember = TotvsMemberGetWebServiceResponse(
        internalId = memberId.toString(),
        groupCompany = "0004",
        code = "0001",
        matriculation = "004661",
        registerType = "00",
        digit = "9",
    )

    private val memberWaitingLogs = listOf(
        TestModelFactory.buildNullvsIntegrationLog(
            internalId = memberId,
            status = LogStatus.WAITING,
            batchType = BatchType.UPDATE,
            eventName = "update"
        ),
        TestModelFactory.buildNullvsIntegrationLog(
            internalId = memberId,
            status = LogStatus.WAITING,
            batchType = BatchType.UPDATE,
            eventName = "update"
        ),
        TestModelFactory.buildNullvsIntegrationLog(
            internalId = memberId,
            status = LogStatus.WAITING,
            batchType = BatchType.UPDATE,
            eventName = "update"
        )
    )
    private val memberWaitingLogModels = memberWaitingLogs.map { it.toModel() }

    private val membersWaitingLogs = memberWaitingLogs +
            listOf(
                TestModelFactory.buildNullvsIntegrationLog(
                    internalId = secondMemberId,
                    status = LogStatus.WAITING,
                    batchType = BatchType.UPDATE,
                    eventName = "update"
                ),
                TestModelFactory.buildNullvsIntegrationLog(
                    internalId = secondMemberId,
                    status = LogStatus.WAITING,
                    batchType = BatchType.UPDATE,
                    eventName = "update"
                ),
                TestModelFactory.buildNullvsIntegrationLog(
                    internalId = secondMemberId,
                    status = LogStatus.WAITING,
                    batchType = BatchType.UPDATE,
                    eventName = "update"
                )
            )
    private val membersWaitingLogModels = membersWaitingLogs.map { it.toModel() }

    private val memberCreatedFailureLog = TestModelFactory.buildNullvsIntegrationLog(
        internalId = memberId,
        status = LogStatus.FAILURE,
        batchType = BatchType.CREATE
    )
    private val memberCreatedFailureLogModel = memberCreatedFailureLog.toModel()

    private val memberCreatedFailureNotCalledLog = TestModelFactory.buildNullvsIntegrationLog(
        internalId = memberId,
        status = LogStatus.TOTVS_NOT_CALLED,
        batchType = BatchType.CREATE
    )
    private val memberCreatedFailureNotCalledLogModel = memberCreatedFailureNotCalledLog.toModel()

    private val memberCreatedFinishedLog = TestModelFactory.buildNullvsIntegrationLog(
        internalId = memberId,
        status = LogStatus.FINISHED,
        batchType = BatchType.CREATE
    )
    private val memberCreatedFinishedLogModel = memberCreatedFinishedLog.toModel()

    private val memberCreatedPendingLog = TestModelFactory.buildNullvsIntegrationLog(
        internalId = memberId,
        status = LogStatus.PENDING,
        batchType = BatchType.CREATE
    )
    private val memberCreatedPendingLogModel = memberCreatedPendingLog.toModel()

    private val mixedModelWaitingLogs = listOf(
        TestModelFactory.buildNullvsIntegrationLog(
            status = LogStatus.WAITING,
            batchType = BatchType.UPDATE,
            internalId = memberId,
            eventName = "update"
        ),
        TestModelFactory.buildNullvsIntegrationLog(
            status = LogStatus.WAITING,
            batchType = BatchType.UPDATE,
            internalId = memberId,
            eventName = "update"
        ),
        TestModelFactory.buildNullvsIntegrationLog(
            status = LogStatus.WAITING,
            internalModelName = InternalModelType.CONTRACT,
            externalModelName = ExternalModelType.CONTRACT,
            batchType = BatchType.UPDATE,
            eventName = "update"
        )
    )
    private val mixedModelWaitingLogModels = mixedModelWaitingLogs.map { it.toModel() }

    private val subContractWaitingLogs = memberWaitingLogs.map {
        it.copy(
            internalModelName = InternalModelType.SUBCONTRACT,
            externalModelName = ExternalModelType.SUBCONTRACT
        )
    }
    private val subContractWaitingLogModels = subContractWaitingLogs.map { it.toModel() }

    private val subContractCreatedFailureLog = memberCreatedFailureLog.copy(
        internalModelName = InternalModelType.SUBCONTRACT,
        externalModelName = ExternalModelType.SUBCONTRACT
    )
    private val subContractCreatedFailureLogModel = subContractCreatedFailureLog.toModel()

    private val subContractCreatedFinishedLog = memberCreatedFinishedLog.copy(
        internalModelName = InternalModelType.SUBCONTRACT,
        externalModelName = ExternalModelType.SUBCONTRACT
    )
    private val subContractCreatedFinishedLogModel = subContractCreatedFinishedLog.toModel()

    private val subContract =
        TestModelFactory.buildCompanySubContract(
            id = memberId,
            externalId = null
        )

    private val contractWaitingLogs = memberWaitingLogs.map {
        it.copy(
            internalModelName = InternalModelType.CONTRACT,
            externalModelName = ExternalModelType.CONTRACT
        )
    }
    private val contractWaitingLogModels = contractWaitingLogs.map { it.toModel() }

    private val contractCreatedFailureLog = memberCreatedFailureLog.copy(
        internalModelName = InternalModelType.CONTRACT,
        externalModelName = ExternalModelType.CONTRACT
    )
    private val contractCreatedFailureLogModel = contractCreatedFailureLog.toModel()

    private val contractCreatedFinishedLog = memberCreatedFinishedLog.copy(
        internalModelName = InternalModelType.CONTRACT,
        externalModelName = ExternalModelType.CONTRACT
    )
    private val contractCreatedFinishedLogModel = contractCreatedFinishedLog.toModel()

    private val contract =
        TestModelFactory.buildCompanyContract(
            id = memberId,
            externalId = null
        )


    @Test
    fun `should process member waiting items, update logs and create a new CREATED event on FAILURE previous event successfully`() =
        runBlocking<Unit> {

            val cancelledLogs =
                (memberWaitingLogs + memberCreatedFailureLog).map { log -> log.copy(status = LogStatus.SYNC_CANCELLED) }
            coEvery {
                nullvsIntegrationLogService.findByStatusIgnoringDependentCasesPaginated(
                    LogStatus.WAITING,
                    0,
                    500
                )
            } returns memberWaitingLogs
            coEvery {
                nullvsIntegrationLogService.findByInternalIdAndModel(
                    internalModelName = InternalModelType.MEMBER,
                    internalId = memberId
                )
            } returns memberWaitingLogModels + memberCreatedFailureLogModel

            coEvery { memberService.get(member.id) } returns member
            coEvery {
                kafkaProducerService.produce(NullvsSyncMemberRequestEvent(member, NullvsActionType.CREATE))
            } returns ProducerResult(LocalDateTime.now(), "topic", 100)
            coEvery { nullvsIntegrationLogService.updateList(cancelledLogs) } returns cancelledLogs

            service.processWaitingLogItems()

            coVerifyOnce {
                nullvsIntegrationLogService.findByStatusIgnoringDependentCasesPaginated(
                    LogStatus.WAITING,
                    0,
                    500
                )
            }
            coVerifyOnce { nullvsIntegrationLogService.findByInternalIdAndModel(any(), any()) }
            coVerifyOnce { kafkaProducerService.produce(match<NullvsSyncMemberRequestEvent> { it.payload.action == NullvsActionType.CREATE }) }
            coVerifyOnce { nullvsIntegrationLogService.updateList(cancelledLogs) }
            coVerifyNone {
                kafkaProducerService.produce(match<NullvsSyncMemberRequestEvent> { it.payload.action == NullvsActionType.UPDATE })
            }

        }

    @Test
    fun `should process member waiting items, update logs and create a new CREATED event on TOTVS_NOT_CALLED previous event successfully`() =
        runBlocking<Unit> {

            val cancelledLogs =
                (memberWaitingLogs + memberCreatedFailureNotCalledLog).map { log -> log.copy(status = LogStatus.SYNC_CANCELLED) }
            coEvery {
                nullvsIntegrationLogService.findByStatusIgnoringDependentCasesPaginated(
                    LogStatus.WAITING,
                    0,
                    500
                )
            } returns memberWaitingLogs
            coEvery {
                nullvsIntegrationLogService.findByInternalIdAndModel(
                    internalModelName = InternalModelType.MEMBER,
                    internalId = memberId
                )
            } returns memberWaitingLogModels + memberCreatedFailureNotCalledLogModel

            coEvery { memberService.get(member.id) } returns member
            coEvery {
                kafkaProducerService.produce(NullvsSyncMemberRequestEvent(member, NullvsActionType.CREATE))
            } returns ProducerResult(LocalDateTime.now(), "topic", 100)
            coEvery { nullvsIntegrationLogService.updateList(cancelledLogs) } returns cancelledLogs

            service.processWaitingLogItems()

            coVerifyOnce {
                nullvsIntegrationLogService.findByStatusIgnoringDependentCasesPaginated(
                    LogStatus.WAITING,
                    0,
                    500
                )
            }
            coVerifyOnce { nullvsIntegrationLogService.findByInternalIdAndModel(any(), any()) }
            coVerifyOnce { kafkaProducerService.produce(match<NullvsSyncMemberRequestEvent> { it.payload.action == NullvsActionType.CREATE }) }
            coVerifyOnce { nullvsIntegrationLogService.updateList(cancelledLogs) }
            coVerifyNone {
                kafkaProducerService.produce(match<NullvsSyncMemberRequestEvent> { it.payload.action == NullvsActionType.UPDATE })
            }

        }

    @Test
    fun `should process member waiting items, update logs and create a new UPDATE event on WAITING UPDATE ONLY WITHOUT CREATE previous event successfully`() =
        runBlocking<Unit> {
            val activeMember = member.copy(status = MemberStatus.ACTIVE)

            coEvery {
                nullvsIntegrationLogService.findByStatusIgnoringDependentCasesPaginated(
                    LogStatus.WAITING,
                    0,
                    500
                )
            } returns memberWaitingLogs
            coEvery {
                nullvsIntegrationLogService.findByInternalIdAndModel(
                    internalModelName = InternalModelType.MEMBER,
                    internalId = memberId
                )
            } returns memberWaitingLogModels

            coEvery { memberService.get(activeMember.id) } returns activeMember

            coEvery { totvsMemberClient.getMember(activeMember.id) } returns TotvsMemberGetException(activeMember.id).failure()

            coEvery {
                kafkaProducerService.produce(NullvsSyncMemberRequestEvent(activeMember, NullvsActionType.CREATE))
            } returns ProducerResult(LocalDateTime.now(), "topic", 100)
            coEvery { nullvsIntegrationLogService.updateList(memberWaitingLogs) } returns memberWaitingLogs

            service.processWaitingLogItems()

            coVerifyOnce {
                nullvsIntegrationLogService.findByStatusIgnoringDependentCasesPaginated(
                    LogStatus.WAITING,
                    0,
                    500
                )
            }
            coVerifyOnce { nullvsIntegrationLogService.findByInternalIdAndModel(any(), any()) }
            coVerifyOnce { kafkaProducerService.produce(match<NullvsSyncMemberRequestEvent> { it.payload.action == NullvsActionType.CREATE }) }
            coVerifyOnce { nullvsIntegrationLogService.updateList(any()) }
            coVerifyNone {
                kafkaProducerService.produce(match<NullvsSyncMemberRequestEvent> { it.payload.action == NullvsActionType.UPDATE })
            }

        }

    @Test
    fun `should process member waiting items, update logs and create a new UPDATE event on WAITING UPDATE ONLY WITHOUT CREATE previous event successfully - Member inactive`() =
        runBlocking<Unit> {
            val pendingMember = member.copy(status = MemberStatus.PENDING)

            coEvery {
                nullvsIntegrationLogService.findByStatusIgnoringDependentCasesPaginated(
                    LogStatus.WAITING,
                    0,
                    500
                )
            } returns memberWaitingLogs
            coEvery {
                nullvsIntegrationLogService.findByInternalIdAndModel(
                    internalModelName = InternalModelType.MEMBER,
                    internalId = memberId
                )
            } returns memberWaitingLogModels

            coEvery { memberService.get(pendingMember.id) } returns pendingMember
            coEvery { nullvsIntegrationLogService.updateList(memberWaitingLogs) } returns memberWaitingLogs

            service.processWaitingLogItems()

            coVerifyOnce {
                nullvsIntegrationLogService.findByStatusIgnoringDependentCasesPaginated(
                    LogStatus.WAITING,
                    0,
                    500
                )
            }
            coVerifyOnce { nullvsIntegrationLogService.findByInternalIdAndModel(any(), any()) }
            coVerifyOnce { nullvsIntegrationLogService.updateList(any()) }
            coVerifyNone {
                kafkaProducerService.produce(match<NullvsSyncMemberRequestEvent> { it.payload.action == NullvsActionType.UPDATE })
            }
            coVerifyNone {
                kafkaProducerService.produce(match<NullvsSyncMemberRequestEvent> { it.payload.action == NullvsActionType.CREATE })
            }

        }

    @Test
    fun `should process member waiting items, update logs and create a new UPDATE event on WAITING UPDATE ONLY WITH CREATE previous event successfully`() =
        runBlocking<Unit> {

            val activeMember = member.copy(status = MemberStatus.ACTIVE)

            coEvery {
                nullvsIntegrationLogService.findByStatusIgnoringDependentCasesPaginated(
                    LogStatus.WAITING,
                    0,
                    500
                )
            } returns memberWaitingLogs
            coEvery {
                nullvsIntegrationLogService.findByInternalIdAndModel(
                    internalModelName = InternalModelType.MEMBER,
                    internalId = memberId
                )
            } returns memberWaitingLogModels

            coEvery { memberService.get(activeMember.id) } returns activeMember

            coEvery { totvsMemberClient.getMember(activeMember.id) } returns totvsMember

            coEvery {
                kafkaProducerService.produce(NullvsSyncMemberRequestEvent(activeMember, NullvsActionType.CREATE))
            } returns ProducerResult(LocalDateTime.now(), "topic", 100)
            coEvery { nullvsIntegrationLogService.updateList(memberWaitingLogs) } returns memberWaitingLogs

            service.processWaitingLogItems()

            coVerifyOnce {
                nullvsIntegrationLogService.findByStatusIgnoringDependentCasesPaginated(
                    LogStatus.WAITING,
                    0,
                    500
                )
            }
            coVerifyOnce { nullvsIntegrationLogService.findByInternalIdAndModel(any(), any()) }
            coVerifyOnce { kafkaProducerService.produce(match<NullvsSyncMemberRequestEvent> { it.payload.action == NullvsActionType.UPDATE }) }
            coVerifyOnce { nullvsIntegrationLogService.updateList(any()) }
            coVerifyNone {
                kafkaProducerService.produce(match<NullvsSyncMemberRequestEvent> { it.payload.action == NullvsActionType.CREATE })
            }

        }

    @Test
    fun `should process member waiting items, update logs and create a new UPDATE event on FINISHED previous event successfully`() =
        runBlocking<Unit> {

            val cancelledLogs = memberWaitingLogs.map { log -> log.copy(status = LogStatus.SYNC_CANCELLED) }
            coEvery {
                nullvsIntegrationLogService.findByStatusIgnoringDependentCasesPaginated(
                    LogStatus.WAITING,
                    0,
                    500
                )
            } returns memberWaitingLogs
            coEvery {
                nullvsIntegrationLogService.findByInternalIdAndModel(
                    internalModelName = InternalModelType.MEMBER,
                    internalId = memberId
                )
            } returns memberWaitingLogModels + memberCreatedFinishedLogModel

            coEvery { memberService.get(member.id) } returns member
            coEvery { nullvsIntegrationLogService.updateList(cancelledLogs) } returns cancelledLogs

            service.processWaitingLogItems()

            coVerifyOnce {
                nullvsIntegrationLogService.findByStatusIgnoringDependentCasesPaginated(
                    LogStatus.WAITING,
                    0,
                    500
                )
            }
            coVerifyOnce { nullvsIntegrationLogService.findByInternalIdAndModel(any(), any()) }
            coVerifyOnce { kafkaProducerService.produce(match<NullvsSyncMemberRequestEvent> { it.payload.action == NullvsActionType.UPDATE }) }
            coVerifyOnce { nullvsIntegrationLogService.updateList(cancelledLogs) }
            coVerifyNone {
                kafkaProducerService.produce(match<NullvsSyncMemberRequestEvent> { it.payload.action == NullvsActionType.CREATE })
            }
        }

    @Test
    fun `should process member waiting items, ignore logs and not make any changes when has PENDING logs`() =
        runBlocking<Unit> {

            val cancelledLogs = memberWaitingLogs.map { log -> log.copy(status = LogStatus.SYNC_CANCELLED) }
            coEvery {
                nullvsIntegrationLogService.findByStatusIgnoringDependentCasesPaginated(
                    LogStatus.WAITING,
                    0,
                    500
                )
            } returns memberWaitingLogs
            coEvery {
                nullvsIntegrationLogService.findByInternalIdAndModel(
                    internalModelName = InternalModelType.MEMBER,
                    internalId = memberId
                )
            } returns memberWaitingLogModels + memberCreatedPendingLogModel

            service.processWaitingLogItems()

            coVerifyOnce {
                nullvsIntegrationLogService.findByStatusIgnoringDependentCasesPaginated(
                    LogStatus.WAITING,
                    0,
                    500
                )
            }
            coVerifyOnce { nullvsIntegrationLogService.findByInternalIdAndModel(any(), any()) }
            coVerifyNone { kafkaProducerService.produce(any()) }
            coVerifyNone { nullvsIntegrationLogService.updateList(cancelledLogs) }
        }

    @Test
    fun `should process member waiting items, no items to process`() =
        runBlocking<Unit> {

            coEvery {
                nullvsIntegrationLogService.findByStatusIgnoringDependentCasesPaginated(
                    LogStatus.WAITING,
                    0,
                    500
                )
            } returns emptyList()

            service.processWaitingLogItems()

            coVerifyOnce {
                nullvsIntegrationLogService.findByStatusIgnoringDependentCasesPaginated(
                    LogStatus.WAITING,
                    0,
                    500
                )
            }

            coVerifyNone { nullvsIntegrationLogService.findByInternalIdAndModel(any(), any()) }
            coVerifyNone { kafkaProducerService.produce(any()) }
            coVerifyNone { nullvsIntegrationLogService.updateList(any()) }
        }

    @Test
    fun `should process member waiting items, ignore unmapped model types`() =
        runBlocking<Unit> {

            val unmappedModelTypeLogs =
                memberWaitingLogs.map { log -> log.copy(internalModelName = InternalModelType.MEMBER_INVOICE) }
            val unmappedModelTypeLogModels = unmappedModelTypeLogs.map { it.toModel() }

            coEvery {
                nullvsIntegrationLogService.findByStatusIgnoringDependentCasesPaginated(
                    LogStatus.WAITING,
                    0,
                    500
                )
            } returns unmappedModelTypeLogs
            coEvery {
                nullvsIntegrationLogService.findByInternalIdAndModel(
                    internalModelName = InternalModelType.MEMBER_INVOICE,
                    internalId = any()
                )
            } returns unmappedModelTypeLogModels

            service.processWaitingLogItems()

            coVerifyOnce {
                nullvsIntegrationLogService.findByStatusIgnoringDependentCasesPaginated(
                    LogStatus.WAITING,
                    0,
                    500
                )
            }

            coVerifyNone { kafkaProducerService.produce(any()) }
            coVerifyNone { nullvsIntegrationLogService.updateList(any()) }
        }

    @Test
    fun `should process member waiting items, update logs and create a new CREATED event on FAILURE previous event successfully (mixed models types)`() =
        runBlocking<Unit> {

            val cancelledLogs =
                (mixedModelWaitingLogs.filter { it.internalId == memberId && it.internalModelName == InternalModelType.MEMBER } + memberCreatedFailureLog).map { log ->
                    log.copy(
                        status = LogStatus.SYNC_CANCELLED
                    )
                }
            coEvery {
                nullvsIntegrationLogService.findByStatusIgnoringDependentCasesPaginated(
                    LogStatus.WAITING,
                    0,
                    500
                )
            } returns mixedModelWaitingLogs
            coEvery {
                nullvsIntegrationLogService.findByInternalIdAndModel(
                    internalModelName = InternalModelType.MEMBER,
                    internalId = memberId
                )
            } returns mixedModelWaitingLogModels.filter { it.internalId == memberId && it.internalModelName == InternalModelType.MEMBER } + memberCreatedFailureLogModel

            coEvery {
                nullvsIntegrationLogService.findByInternalIdAndModel(
                    internalModelName = InternalModelType.CONTRACT,
                    internalId = any()
                )
            } returns emptyList()

            coEvery { memberService.get(member.id) } returns member
            coEvery {
                kafkaProducerService.produce(NullvsSyncMemberRequestEvent(member, NullvsActionType.CREATE))
            } returns ProducerResult(LocalDateTime.now(), "topic", 100)
            coEvery { nullvsIntegrationLogService.updateList(cancelledLogs) } returns cancelledLogs

            service.processWaitingLogItems()

            coVerifyOnce {
                nullvsIntegrationLogService.findByStatusIgnoringDependentCasesPaginated(
                    LogStatus.WAITING,
                    0,
                    500
                )
            }
            coVerify(exactly = 2) { nullvsIntegrationLogService.findByInternalIdAndModel(any(), any()) }
            coVerifyOnce { kafkaProducerService.produce(match<NullvsSyncMemberRequestEvent> { it.payload.action == NullvsActionType.CREATE }) }
            coVerifyOnce { nullvsIntegrationLogService.updateList(cancelledLogs) }
            coVerifyNone {
                kafkaProducerService.produce(match<NullvsSyncMemberRequestEvent> { it.payload.action == NullvsActionType.UPDATE })
            }
        }

    @Test
    fun `should process member waiting items, process many members successfully`() =
        runBlocking<Unit> {

            val cancelledLogsMember1 =
                (memberWaitingLogs + memberCreatedFailureLog).map { log -> log.copy(status = LogStatus.SYNC_CANCELLED) }
            val cancelledLogsMember2 =
                (memberWaitingLogs + memberCreatedFailureLog).map { log ->
                    log.copy(
                        internalId = secondMemberId,
                        status = LogStatus.SYNC_CANCELLED
                    )
                }
            coEvery {
                nullvsIntegrationLogService.findByStatusIgnoringDependentCasesPaginated(
                    LogStatus.WAITING,
                    0,
                    500
                )
            } returns membersWaitingLogs
            coEvery {
                nullvsIntegrationLogService.findByInternalIdAndModel(
                    internalModelName = InternalModelType.MEMBER,
                    internalId = memberId
                )
            } returns memberWaitingLogModels + memberCreatedFailureLogModel

            coEvery {
                nullvsIntegrationLogService.findByInternalIdAndModel(
                    internalModelName = InternalModelType.MEMBER,
                    internalId = secondMemberId
                )
            } returns (memberWaitingLogModels + memberCreatedFailureLogModel).map { log ->
                log.copy(
                    internalId = secondMemberId,
                )
            }

            coEvery { memberService.get(member.id) } returns member
            coEvery { memberService.get(secondMember.id) } returns secondMember
            coEvery {
                kafkaProducerService.produce(NullvsSyncMemberRequestEvent(member, NullvsActionType.CREATE))
            } returns ProducerResult(LocalDateTime.now(), "topic", 100)
            coEvery {
                kafkaProducerService.produce(NullvsSyncMemberRequestEvent(secondMember, NullvsActionType.CREATE))
            } returns ProducerResult(LocalDateTime.now(), "topic", 100)
            coEvery { nullvsIntegrationLogService.updateList(cancelledLogsMember1) } returns cancelledLogsMember1
            coEvery { nullvsIntegrationLogService.updateList(cancelledLogsMember2) } returns cancelledLogsMember2

            service.processWaitingLogItems()

            coVerifyOnce {
                nullvsIntegrationLogService.findByStatusIgnoringDependentCasesPaginated(
                    LogStatus.WAITING,
                    0,
                    500
                )
            }
            coVerify(exactly = 2) { nullvsIntegrationLogService.findByInternalIdAndModel(any(), any()) }
            coVerify(exactly = 2) { kafkaProducerService.produce(match<NullvsSyncMemberRequestEvent> { it.payload.action == NullvsActionType.CREATE }) }
            coVerify(exactly = 2) { nullvsIntegrationLogService.updateList(any()) }
            coVerifyNone {
                kafkaProducerService.produce(match<NullvsSyncMemberRequestEvent> { it.payload.action == NullvsActionType.UPDATE })
            }
        }

    @Test
    fun `should process member waiting items, update logs and create a new UPDATE event on OLDER PENDING create event successfully`() =
        runBlocking<Unit> {

            val olderMemberCreatedPendingLog =
                memberCreatedPendingLog.copy(createdAt = LocalDateTime.now().minusDays(30))
            val olderMemberCreatedPendingLogModel = olderMemberCreatedPendingLog.toModel()
            val activeMember = member.copy(status = MemberStatus.ACTIVE)


            val cancelledLogs =
                (memberWaitingLogs + olderMemberCreatedPendingLog).map { log -> log.copy(status = LogStatus.SYNC_CANCELLED) }
            coEvery {
                nullvsIntegrationLogService.findByStatusIgnoringDependentCasesPaginated(
                    LogStatus.WAITING,
                    0,
                    500
                )
            } returns memberWaitingLogs
            coEvery {
                nullvsIntegrationLogService.findByInternalIdAndModel(
                    internalModelName = InternalModelType.MEMBER,
                    internalId = memberId
                )
            } returns memberWaitingLogModels + olderMemberCreatedPendingLogModel

            coEvery { memberService.get(activeMember.id) } returns activeMember

            coEvery { totvsMemberClient.getMember(activeMember.id) } returns totvsMember

            coEvery {
                kafkaProducerService.produce(NullvsSyncMemberRequestEvent(activeMember, NullvsActionType.CREATE))
            } returns ProducerResult(LocalDateTime.now(), "topic", 100)
            coEvery { nullvsIntegrationLogService.updateList(cancelledLogs) } returns cancelledLogs

            service.processWaitingLogItems()

            coVerifyOnce {
                nullvsIntegrationLogService.findByStatusIgnoringDependentCasesPaginated(
                    LogStatus.WAITING,
                    0,
                    500
                )
            }
            coVerifyOnce { nullvsIntegrationLogService.findByInternalIdAndModel(any(), any()) }
            coVerifyOnce { totvsMemberClient.getMember(any()) }
            coVerifyOnce { kafkaProducerService.produce(match<NullvsSyncMemberRequestEvent> { it.payload.action == NullvsActionType.UPDATE }) }
            coVerifyOnce { nullvsIntegrationLogService.updateList(cancelledLogs) }
            coVerifyNone {
                kafkaProducerService.produce(match<NullvsSyncMemberRequestEvent> { it.payload.action == NullvsActionType.CREATE })
            }

        }

    @Test
    fun `should process member waiting items, update logs and create a new CREATE event on OLDER PENDING create event successfully`() =
        runBlocking<Unit> {

            val olderMemberCreatedPendingLog =
                memberCreatedPendingLog.copy(createdAt = LocalDateTime.now().minusDays(30))
            val olderMemberCreatedPendingLogModel = olderMemberCreatedPendingLog.toModel()
            val activeMember = member.copy(status = MemberStatus.ACTIVE)


            val cancelledLogs =
                (memberWaitingLogs + olderMemberCreatedPendingLog).map { log -> log.copy(status = LogStatus.SYNC_CANCELLED) }
            coEvery {
                nullvsIntegrationLogService.findByStatusIgnoringDependentCasesPaginated(
                    LogStatus.WAITING,
                    0,
                    500
                )
            } returns memberWaitingLogs
            coEvery {
                nullvsIntegrationLogService.findByInternalIdAndModel(
                    internalModelName = InternalModelType.MEMBER,
                    internalId = memberId
                )
            } returns memberWaitingLogModels + olderMemberCreatedPendingLogModel

            coEvery { memberService.get(activeMember.id) } returns activeMember

            coEvery { totvsMemberClient.getMember(activeMember.id) } returns TotvsMemberGetException(activeMember.id).failure()

            coEvery {
                kafkaProducerService.produce(NullvsSyncMemberRequestEvent(activeMember, NullvsActionType.CREATE))
            } returns ProducerResult(LocalDateTime.now(), "topic", 100)
            coEvery { nullvsIntegrationLogService.updateList(cancelledLogs) } returns cancelledLogs

            service.processWaitingLogItems()

            coVerifyOnce {
                nullvsIntegrationLogService.findByStatusIgnoringDependentCasesPaginated(
                    LogStatus.WAITING,
                    0,
                    500
                )
            }
            coVerifyOnce { nullvsIntegrationLogService.findByInternalIdAndModel(any(), any()) }
            coVerifyOnce { totvsMemberClient.getMember(any()) }
            coVerifyOnce { kafkaProducerService.produce(match<NullvsSyncMemberRequestEvent> { it.payload.action == NullvsActionType.CREATE }) }
            coVerifyOnce { nullvsIntegrationLogService.updateList(cancelledLogs) }
            coVerifyNone {
                kafkaProducerService.produce(match<NullvsSyncMemberRequestEvent> { it.payload.action == NullvsActionType.UPDATE })
            }

        }

    @Test
    fun `should cancel all waiting logs on OLDER PENDING activation and membership is inactive successfully`() =
        runBlocking<Unit> {

            val olderMemberCreatedPendingLog =
                memberCreatedPendingLog.copy(createdAt = LocalDateTime.now().minusDays(30))
            val olderMemberCreatedPendingLogModel = olderMemberCreatedPendingLog.toModel()
            val inactiveMember = member.copy(status = MemberStatus.CANCELED)


            val cancelledLogs =
                (memberWaitingLogs + olderMemberCreatedPendingLog).map { log -> log.copy(status = LogStatus.SYNC_CANCELLED) }
            coEvery {
                nullvsIntegrationLogService.findByStatusIgnoringDependentCasesPaginated(
                    LogStatus.WAITING,
                    0,
                    500
                )
            } returns memberWaitingLogs
            coEvery {
                nullvsIntegrationLogService.findByInternalIdAndModel(
                    internalModelName = InternalModelType.MEMBER,
                    internalId = memberId
                )
            } returns memberWaitingLogModels + olderMemberCreatedPendingLogModel

            coEvery { memberService.get(inactiveMember.id) } returns inactiveMember
            coEvery { nullvsIntegrationLogService.updateList(cancelledLogs) } returns cancelledLogs

            service.processWaitingLogItems()

            coVerifyOnce {
                nullvsIntegrationLogService.findByStatusIgnoringDependentCasesPaginated(
                    LogStatus.WAITING,
                    0,
                    500
                )
            }
            coVerifyOnce { nullvsIntegrationLogService.findByInternalIdAndModel(any(), any()) }

            coVerifyOnce { nullvsIntegrationLogService.updateList(cancelledLogs) }
            coVerifyNone {
                kafkaProducerService.produce(match<NullvsSyncMemberRequestEvent> { it.payload.action == NullvsActionType.CREATE })
            }
            coVerifyNone {
                kafkaProducerService.produce(match<NullvsSyncMemberRequestEvent> { it.payload.action == NullvsActionType.UPDATE })
            }
            coVerifyNone {
                totvsMemberClient.getMember(inactiveMember.id)
            }

        }

    @ParameterizedTest
    @MethodSource("processorInternalModels")
    fun `cancelWaitingLogs should process member waiting items, process many members successfully`(modelType: InternalModelType) =
        runBlocking<Unit> {

            val params = CancelWaitingLogsRequest(
                ids = listOf(UUID.randomUUID(), UUID.randomUUID()),
                internalModelName = modelType
            )

            coEvery {
                nullvsIntegrationLogService.findByStatusAndInternalIdsAndModel(
                    LogStatus.WAITING,
                    params.ids,
                    params.internalModelName
                )
            } returns membersWaitingLogs

            service.cancelWaitingLogs(params)

            coVerifyOnce { nullvsIntegrationLogService.findByStatusAndInternalIdsAndModel(any(), any(), any()) }
            coVerifyOnce { nullvsIntegrationLogService.updateList(any()) }

        }

    @Test
    fun `should update member successfully in mistery cenario 1`() =
        runBlocking<Unit> {

            val activeMember = member.copy(status = MemberStatus.ACTIVE)

            val olderCreateEvent = TestModelFactory.buildNullvsIntegrationLog(
                internalId = memberId,
                status = LogStatus.PENDING,
                batchType = BatchType.CREATE,
                eventName = "update",
            ).copy(createdAt = LocalDateTime.now().minusMonths(2))
            val olderCreateEventModel = olderCreateEvent.toModel()

            coEvery {
                nullvsIntegrationLogService.findByStatusIgnoringDependentCasesPaginated(
                    LogStatus.WAITING, 0, 500
                )
            } returns memberWaitingLogs

            coEvery {
                nullvsIntegrationLogService.findByInternalIdAndModel(
                    memberId, InternalModelType.MEMBER
                )
            } returns membersWaitingLogModels + listOf(olderCreateEventModel)

            coEvery { memberService.get(activeMember.id) } returns activeMember
            coEvery { totvsMemberClient.getMember(activeMember.id) } returns totvsMember

            service.processWaitingLogItems()

            coVerifyOnce { kafkaProducerService.produce(match<NullvsSyncMemberRequestEvent> { it.payload.action == NullvsActionType.UPDATE }) }

            coVerifyOnce {
                nullvsIntegrationLogService.findByStatusIgnoringDependentCasesPaginated(
                    LogStatus.WAITING,
                    0,
                    500
                )
            }
            coVerifyOnce { nullvsIntegrationLogService.findByInternalIdAndModel(any(), any()) }
            coVerifyOnce { nullvsIntegrationLogService.updateList(any()) }

        }

    @Test
    fun `should process subContract waiting items, update logs and create a new CREATED event on FAILURE previous event successfully`() =
        runBlocking {
            val internalId = memberId

            val cancelledLogs =
                (subContractWaitingLogs + subContractCreatedFailureLog).map { log -> log.copy(status = LogStatus.SYNC_CANCELLED) }
            coEvery {
                nullvsIntegrationLogService.findByStatusIgnoringDependentCasesPaginated(
                    LogStatus.WAITING,
                    0,
                    500
                )
            } returns subContractWaitingLogs
            coEvery {
                nullvsIntegrationLogService.findByInternalIdAndModel(
                    internalModelName = InternalModelType.SUBCONTRACT,
                    internalId = internalId
                )
            } returns subContractWaitingLogModels + subContractCreatedFailureLogModel

            coEvery { subContractService.get(internalId) } returns subContract
            coEvery {
                kafkaProducerService.produce(
                    NullvsSyncCompanySubcontractRequestEvent(
                        subContract,
                        NullvsActionType.CREATE
                    )
                )
            } returns ProducerResult(LocalDateTime.now(), "topic", 100)
            coEvery { nullvsIntegrationLogService.updateList(cancelledLogs) } returns cancelledLogs

            service.processWaitingLogItems()

            coVerifyOnce {
                nullvsIntegrationLogService.findByStatusIgnoringDependentCasesPaginated(
                    LogStatus.WAITING,
                    0,
                    500
                )
            }
            coVerifyOnce { nullvsIntegrationLogService.findByInternalIdAndModel(any(), any()) }
            coVerifyOnce { kafkaProducerService.produce(match<NullvsSyncCompanySubcontractRequestEvent> { it.payload.action == NullvsActionType.CREATE }) }
            coVerifyOnce { nullvsIntegrationLogService.updateList(cancelledLogs) }
            coVerifyNone {
                kafkaProducerService.produce(match<NullvsSyncCompanySubcontractRequestEvent> { it.payload.action == NullvsActionType.UPDATE })
            }

        }

    @Test
    fun `should process subContract waiting items, update logs and create a new UPDATE event on WAITING UPDATE ONLY WITH CREATE previous event successfully`() =
        runBlocking {

            val internalId = memberId

            val subContractWithExternalId = subContract.copy(externalId = "234654652434564342")

            coEvery {
                nullvsIntegrationLogService.findByStatusIgnoringDependentCasesPaginated(
                    LogStatus.WAITING,
                    0,
                    500
                )
            } returns subContractWaitingLogs
            coEvery {
                nullvsIntegrationLogService.findByInternalIdAndModel(
                    internalModelName = InternalModelType.SUBCONTRACT,
                    internalId = memberId
                )
            } returns subContractWaitingLogModels + subContractCreatedFinishedLogModel

            coEvery { subContractService.get(internalId) } returns subContractWithExternalId

            coEvery {
                kafkaProducerService.produce(
                    NullvsSyncCompanySubcontractRequestEvent(
                        subContractWithExternalId,
                        NullvsActionType.UPDATE
                    )
                )
            } returns ProducerResult(LocalDateTime.now(), "topic", 100)
            coEvery { nullvsIntegrationLogService.updateList(subContractWaitingLogs) } returns subContractWaitingLogs + subContractCreatedFinishedLog

            service.processWaitingLogItems()

            coVerifyOnce {
                nullvsIntegrationLogService.findByStatusIgnoringDependentCasesPaginated(
                    LogStatus.WAITING,
                    0,
                    500
                )
            }
            coVerifyOnce { nullvsIntegrationLogService.findByInternalIdAndModel(any(), any()) }
            coVerifyOnce { kafkaProducerService.produce(match<NullvsSyncCompanySubcontractRequestEvent> { it.payload.action == NullvsActionType.UPDATE }) }
            coVerifyOnce { nullvsIntegrationLogService.updateList(any()) }
            coVerifyNone {
                kafkaProducerService.produce(match<NullvsSyncCompanySubcontractRequestEvent> { it.payload.action == NullvsActionType.CREATE })
            }

        }

    @Test
    fun `should process contract waiting items, update logs and create a new CREATED event on FAILURE previous event successfully`() =
        runBlocking {
            val internalId = memberId

            val cancelledLogs =
                (contractWaitingLogs + contractCreatedFailureLog).map { log -> log.copy(status = LogStatus.SYNC_CANCELLED) }
            coEvery {
                nullvsIntegrationLogService.findByStatusIgnoringDependentCasesPaginated(
                    LogStatus.WAITING,
                    0,
                    500
                )
            } returns contractWaitingLogs
            coEvery {
                nullvsIntegrationLogService.findByInternalIdAndModel(
                    internalModelName = InternalModelType.CONTRACT,
                    internalId = internalId
                )
            } returns contractWaitingLogModels + contractCreatedFailureLogModel

            coEvery { contractService.get(internalId) } returns contract
            coEvery {
                kafkaProducerService.produce(NullvsSyncCompanyContractRequestEvent(contract, NullvsActionType.CREATE))
            } returns ProducerResult(LocalDateTime.now(), "topic", 100)
            coEvery { nullvsIntegrationLogService.updateList(cancelledLogs) } returns cancelledLogs

            service.processWaitingLogItems()

            coVerifyOnce {
                nullvsIntegrationLogService.findByStatusIgnoringDependentCasesPaginated(
                    LogStatus.WAITING,
                    0,
                    500
                )
            }
            coVerifyOnce { nullvsIntegrationLogService.findByInternalIdAndModel(any(), any()) }
            coVerifyOnce { kafkaProducerService.produce(match<NullvsSyncCompanyContractRequestEvent> { it.payload.action == NullvsActionType.CREATE }) }
            coVerifyOnce { nullvsIntegrationLogService.updateList(cancelledLogs) }
            coVerifyNone {
                kafkaProducerService.produce(match<NullvsSyncCompanyContractRequestEvent> { it.payload.action == NullvsActionType.UPDATE })
            }

        }

    @Test
    fun `should process contract waiting items, update logs and create a new UPDATE event on WAITING UPDATE ONLY WITH CREATE previous event successfully`() =
        runBlocking {

            val internalId = memberId

            val contractWithExternalId = contract.copy(externalId = "234654652434564342")

            coEvery {
                nullvsIntegrationLogService.findByStatusIgnoringDependentCasesPaginated(
                    LogStatus.WAITING,
                    0,
                    500
                )
            } returns contractWaitingLogs
            coEvery {
                nullvsIntegrationLogService.findByInternalIdAndModel(
                    internalModelName = InternalModelType.CONTRACT,
                    internalId = memberId
                )
            } returns contractWaitingLogModels + contractCreatedFinishedLogModel

            coEvery { contractService.get(internalId) } returns contractWithExternalId

            coEvery {
                kafkaProducerService.produce(
                    NullvsSyncCompanyContractRequestEvent(
                        contractWithExternalId,
                        NullvsActionType.UPDATE
                    )
                )
            } returns ProducerResult(LocalDateTime.now(), "topic", 100)
            coEvery { nullvsIntegrationLogService.updateList(contractWaitingLogs) } returns contractWaitingLogs + contractCreatedFinishedLog

            service.processWaitingLogItems()

            coVerifyOnce {
                nullvsIntegrationLogService.findByStatusIgnoringDependentCasesPaginated(
                    LogStatus.WAITING,
                    0,
                    500
                )
            }
            coVerifyOnce { nullvsIntegrationLogService.findByInternalIdAndModel(any(), any()) }
            coVerifyOnce { kafkaProducerService.produce(match<NullvsSyncCompanyContractRequestEvent> { it.payload.action == NullvsActionType.UPDATE }) }
            coVerifyOnce { nullvsIntegrationLogService.updateList(any()) }
            coVerifyNone {
                kafkaProducerService.produce(match<NullvsSyncCompanyContractRequestEvent> { it.payload.action == NullvsActionType.CREATE })
            }

        }

    @Test
    fun `should process contract waiting items, update logs and create a new UPDATE event on WAITING UPDATE ONLY WITHOUT CREATE previous event successfully`() =
        runBlocking<Unit> {

            val internalId = memberId

            coEvery {
                nullvsIntegrationLogService.findByStatusIgnoringDependentCasesPaginated(
                    LogStatus.WAITING,
                    0,
                    500
                )
            } returns contractWaitingLogs
            coEvery {
                nullvsIntegrationLogService.findByInternalIdAndModel(
                    internalModelName = InternalModelType.CONTRACT,
                    internalId = memberId
                )
            } returns contractWaitingLogModels

            coEvery { contractService.get(internalId) } returns contract

            coEvery {
                kafkaProducerService.produce(NullvsSyncCompanyContractRequestEvent(contract, NullvsActionType.CREATE))
            } returns ProducerResult(LocalDateTime.now(), "topic", 100)
            coEvery { nullvsIntegrationLogService.updateList(contractWaitingLogs) } returns contractWaitingLogs

            service.processWaitingLogItems()

            coVerifyOnce {
                nullvsIntegrationLogService.findByStatusIgnoringDependentCasesPaginated(
                    LogStatus.WAITING,
                    0,
                    500
                )
            }
            coVerifyOnce { nullvsIntegrationLogService.findByInternalIdAndModel(any(), any()) }
            coVerifyOnce { kafkaProducerService.produce(match<NullvsSyncCompanyContractRequestEvent> { it.payload.action == NullvsActionType.CREATE }) }
            coVerifyOnce { nullvsIntegrationLogService.updateList(any()) }
            coVerifyNone {
                kafkaProducerService.produce(match<NullvsSyncMemberRequestEvent> { it.payload.action == NullvsActionType.UPDATE })
            }

        }

    @Test
    fun `#should cancel all waiting logs ignoring dependency cases`() = runBlocking<Unit> {
        val contractWaitingLogsCanceled = contractWaitingLogs.map { it.copy(status = LogStatus.SYNC_CANCELLED) }
        coEvery {
            nullvsIntegrationLogService.findByStatusIgnoringDependentCasesPaginated(
                LogStatus.WAITING,
                0,
                500
            )
        } returns contractWaitingLogs
        coEvery { nullvsIntegrationLogService.updateList(contractWaitingLogsCanceled) } returns contractWaitingLogsCanceled

        service.cancelAllWaitingLogs()

        coVerifyOnce {
            nullvsIntegrationLogService.findByStatusIgnoringDependentCasesPaginated(LogStatus.WAITING, 0, 500)
            nullvsIntegrationLogService.updateList(contractWaitingLogsCanceled)
        }
    }

}
