package br.com.alice.healthlogic.services

import br.com.alice.common.core.exceptions.NotFoundException
import br.com.alice.common.extensions.getOrNullIfNotFound
import br.com.alice.common.service.data.dsl.and
import br.com.alice.data.layer.models.ServiceScriptExecution
import br.com.alice.data.layer.models.ServiceScriptNode
import br.com.alice.data.layer.models.ServiceScriptNodeType
import br.com.alice.data.layer.models.ServiceScriptRelationship
import br.com.alice.data.layer.models.ServiceScriptStatus
import br.com.alice.data.layer.models.ServiceScriptStatus.ACTIVE
import br.com.alice.data.layer.services.ServiceScriptExecutionDataService
import br.com.alice.data.layer.services.ServiceScriptNodeDataService
import br.com.alice.data.layer.services.ServiceScriptRelationshipDataService
import br.com.alice.healthlogic.client.ScriptService
import br.com.alice.healthlogic.models.bud.NodeWithRelationships
import com.github.kittinunf.result.Result
import com.github.kittinunf.result.failure
import com.github.kittinunf.result.flatMap
import com.github.kittinunf.result.getOrElse
import com.github.kittinunf.result.map
import com.github.kittinunf.result.success
import java.util.UUID

class ScriptServiceImpl(
    private val serviceScriptNodeDataService: ServiceScriptNodeDataService,
    private val serviceScriptRelationshipDataService: ServiceScriptRelationshipDataService,
    private val serviceScriptExecutionDataService: ServiceScriptExecutionDataService,
) : ScriptService {

    override suspend fun createExecution(model: ServiceScriptExecution) =
        serviceScriptExecutionDataService.add(model)

    override suspend fun getNode(nodeId: UUID) = serviceScriptNodeDataService.get(nodeId)

    override suspend fun searchNodeAndRelationshipsByNameAndType(name: String, type: List<ServiceScriptNodeType>) =
        serviceScriptNodeDataService.find {
            where {
                this.name.like(name)
                    .and(this.status.eq(ACTIVE))
                    .and(this.type.inList(type))
            }
        }.map { nodes ->
            val nodesIds = nodes.associateBy { it.id }
            val relationshipsByParentId = findRelationshipByParents(nodesIds.keys.toList())
                .getOrElse { emptyList() }
                .groupBy { it.nodeParentId!! }

            val relationshipsByChildrenId = findRelationshipByChildren(nodesIds.keys.toList())
                .getOrElse { emptyList() }
                .groupBy { it.nodeChildId!! }

            nodes.map { node ->
                NodeWithRelationships(
                    serviceScriptRelationship =
                    relationshipsByParentId[node.id].orEmpty() +
                            relationshipsByChildrenId[node.id].orEmpty(),
                    serviceScriptNode = node
                )
            }
        }

    override suspend fun getNodeAndRelationshipsById(id: UUID): Result<NodeWithRelationships<ServiceScriptNode>, Throwable> =
        serviceScriptNodeDataService.get(id).flatMap {  node ->
            findRelationshipByParent(node.id).map { NodeWithRelationships(node, it) }
        }

    override suspend fun getNodeAndRelationshipsByNameAndType(
        name: String,
        type: ServiceScriptNodeType,
        status: List<ServiceScriptStatus>
    ) =
        serviceScriptNodeDataService.findOneOrNull {
            where {
                this.name.eq(name).and(this.status.eq(ACTIVE)).and(this.type.eq(type))
            }
        }?.let { node ->
            findRelationshipByParent(node.id, status).getOrNullIfNotFound()?.let { NodeWithRelationships(node, it) }
        }?.success() ?: NotFoundException("node_not_found").failure()

    override suspend fun findRelationshipByParent(parentId: UUID, status: List<ServiceScriptStatus>) =
        serviceScriptRelationshipDataService.find { where {
            this.parentId.eq(parentId).and(this.status.inList(status))
        } }

    override suspend fun findRelationshipByParents(parentIds: List<UUID>): Result<List<ServiceScriptRelationship>, Throwable> =
        serviceScriptRelationshipDataService.find { where { this.parentId.inList(parentIds) and status.eq(ACTIVE) }}

    override suspend fun findRelationshipByChildren(parentIds: List<UUID>): Result<List<ServiceScriptRelationship>, Throwable> =
        serviceScriptRelationshipDataService.find { where { this.childId.inList(parentIds) and status.eq(ACTIVE) }}

    override suspend fun findRelationshipByChildrenAndHealthCondition(
        childrenIds: List<UUID>, healthConditionId: String
    ): Result<List<ServiceScriptRelationship>, Throwable> =
        serviceScriptRelationshipDataService.find { where {
            this.childId.inList(childrenIds).and(this.healthConditionField.eq(healthConditionId)).and(status.eq(ACTIVE))
        }}

    override suspend fun findNodeByIds(ids: List<UUID>) = serviceScriptNodeDataService.find { where { id.inList(ids) } }

    override suspend fun getNodesByRootNodesAndType(rootNodeIds: List<UUID>, type: ServiceScriptNodeType): Result<List<ServiceScriptNode>, Throwable> =
        serviceScriptNodeDataService.find {
            where { this.rootNodeId.inList(rootNodeIds).and(this.type.eq(type)).and(this.status.eq(ACTIVE)) }
        }

    override suspend fun getNodesByRootNodesAndTypes(
        rootNodeIds: List<UUID>,
        type: List<ServiceScriptNodeType>
    ): Result<List<ServiceScriptNode>, Throwable> =
        serviceScriptNodeDataService.find {
            where { this.rootNodeId.inList(rootNodeIds).and(this.type.inList(type)).and(this.status.eq(ACTIVE)) }
        }
}
