package br.com.alice.communication.crm.hubspot.b2c.client

import br.com.alice.common.client.DefaultHttpClient
import br.com.alice.common.core.extensions.fromJson
import br.com.alice.common.observability.opentelemetry.OpenTelemetryClient
import br.com.alice.common.serialization.gson
import br.com.alice.common.service.serialization.registerLocalDateTimeAtEpochAdapter
import br.com.alice.common.service.serialization.simpleGson
import com.google.gson.FieldNamingPolicy
import io.ktor.client.call.body
import io.ktor.client.engine.HttpClientEngine
import io.ktor.client.plugins.HttpResponseValidator
import io.ktor.client.plugins.ResponseException
import io.ktor.client.plugins.ServerResponseException
import io.ktor.client.plugins.contentnegotiation.ContentNegotiation
import io.ktor.client.request.get
import io.ktor.client.request.header
import io.ktor.client.request.patch
import io.ktor.client.request.post
import io.ktor.client.request.setBody
import io.ktor.http.ContentType
import io.ktor.http.HttpHeaders
import io.ktor.http.HttpStatusCode
import io.ktor.http.contentType

class HubspotClientImpl(
    private val hubspotConfiguration: HubspotConfiguration,
    private val httpEngine: HttpClientEngine
) : HubspotClient {

    private fun client(serializerPolicy: FieldNamingPolicy = FieldNamingPolicy.LOWER_CASE_WITH_UNDERSCORES) =
        DefaultHttpClient(httpEngine, {
            install(ContentNegotiation) {
                simpleGson {
                    setFieldNamingPolicy(serializerPolicy)
                    registerLocalDateTimeAtEpochAdapter()
                }
            }
            install(OpenTelemetryClient)

            expectSuccess = true
            HttpResponseValidator {
                handleResponseException { cause: Throwable ->
                    val responseException = cause as? ResponseException ?: return@handleResponseException
                    val response = responseException.response

                    val body = response.body<String>()
                    val errorResponse: ErrorResponse = gson.fromJson(body)
                    when (response.status) {
                        HttpStatusCode.Unauthorized -> throw CredentialsNotFoundException()
                        HttpStatusCode.Conflict -> {
                            if (errorResponse.message.matches(Regex("Contact already exists.+"))) {
                                val idRegex = "[0-9].+$".toRegex()
                                val contactId = idRegex.find(errorResponse.message)!!.value
                                throw HubspotContactConflictException(errorResponse.message, contactId = contactId)
                            }
                        }
                        HttpStatusCode.BadRequest -> {
                            if (errorResponse.category == "VALIDATION_ERROR" &&
                                errorResponse.message.matches(Regex(".*INVALID_EMAIL.*"))
                            )
                                throw HubspotInvalidEmailException(errorResponse.message)

                        }
                        HttpStatusCode.InternalServerError -> throw ServerResponseException(response, response.body())
                    }
                    throw HubspotException(errorResponse.message)
                }
            }
        }, timeoutInMillis = 15_000)

    override suspend fun createContact(request: HubspotContact): ContactResponse {
        val contactUrl = buildUrl("/crm/v3/objects/contacts")
        val contactData = ContactData(request)

        return client().use { client ->
            client.post(contactUrl) {
                header(HttpHeaders.Authorization, "Bearer ${hubspotConfiguration.accessToken}")
                contentType(ContentType.Application.Json)
                setBody(contactData)
            }.body()
        }
    }

    override suspend fun updateContact(contactId: String, request: HubspotContact): ContactResponse {
        val contactUrl = buildUrl("/crm/v3/objects/contacts/$contactId")
        val contactData = ContactData(request)

        return client().use { client ->
            client.patch(contactUrl) {
                header(HttpHeaders.Authorization, "Bearer ${hubspotConfiguration.accessToken}")
                contentType(ContentType.Application.Json)
                setBody(contactData)
            }.body()
        }
    }

    override suspend fun createDeal(request: HubspotDeal): DealResponse {
        val dealUrl = buildUrl("/crm/v3/objects/deals")
        val dealData = DealData(request)

        return client().use { client ->
            client.post(dealUrl) {
                header(HttpHeaders.Authorization, "Bearer ${hubspotConfiguration.accessToken}")
                contentType(ContentType.Application.Json)
                setBody(dealData)
            }.body()
        }
    }

    override suspend fun updateDeal(dealId: String, request: HubspotDeal): DealResponse {
        val dealUrl = buildUrl("/crm/v3/objects/deals/${dealId}")
        val dealData = DealData(request)

        return client().use { client ->
            client.patch(dealUrl) {
                header(HttpHeaders.Authorization, "Bearer ${hubspotConfiguration.accessToken}")
                contentType(ContentType.Application.Json)
                setBody(dealData)
            }.body()
        }
    }

    override suspend fun associateContactToDeal(contactId: String, dealId: String): AssociationResponse {
        val associationUrl = buildUrl("/crm/v3/associations/contact/deal/batch/create")
        val request = AssociationRequest(
            inputs = listOf(
                AssociationInput(
                    from = AssociationItem(id = contactId),
                    to = AssociationItem(id = dealId),
                    type = "contact_to_deal"
                )
            )
        )

        return client().use { client ->
            client.post(associationUrl) {
                header(HttpHeaders.Authorization, "Bearer ${hubspotConfiguration.accessToken}")
                contentType(ContentType.Application.Json)
                setBody(request)
            }.body()
        }
    }

    override suspend fun searchContact(request: HubspotSearchRequest): HubspotContactSearchResponse {
        val searchContactUrl = buildUrl("/crm/v3/objects/contacts/search")

        return client(FieldNamingPolicy.IDENTITY).use { client ->
            client.post(searchContactUrl) {
                header(HttpHeaders.Authorization, "Bearer ${hubspotConfiguration.accessToken}")
                contentType(ContentType.Application.Json)
                setBody(request)
            }.body()
        }
    }

    override suspend fun identifyVisitor(request: HubspotVisitor): HubspotVisitorToken {
        val visitorAuthentication = buildUrl("/conversations/v3/visitor-identification/tokens/create")

        return client().use { client ->
            client.post(visitorAuthentication) {
                header(HttpHeaders.Authorization, "Bearer ${hubspotConfiguration.accessToken}")
                contentType(ContentType.Application.Json)
                setBody(request)
            }.body()
        }
    }

    override suspend fun associateContactToCompany(contactId: String, companyId: String): AssociationResponse {
        val associationUrl = buildUrl("/crm/v3/associations/Contacts/Companies/batch/create")
        val request = AssociationRequest(
            inputs = listOf(
                AssociationInput(
                    from = AssociationItem(id = contactId),
                    to = AssociationItem(id = companyId),
                    type = "contact_to_company"
                )
            )
        )
        return client().use { client ->
            client.post(associationUrl) {
                header(HttpHeaders.Authorization, "Bearer ${hubspotConfiguration.accessToken}")
                contentType(ContentType.Application.Json)
                setBody(request)
            }
        }.body()
    }

    override suspend fun createCompany(request: HubspotCompany): CompanyResponse {
        val companiesUrl = buildUrl("/crm/v3/objects/companies")
        val companyData = CompanyData(request)

        return client().use { client ->
            client.post(companiesUrl) {
                header(HttpHeaders.Authorization, "Bearer ${hubspotConfiguration.accessToken}")
                contentType(ContentType.Application.Json)
                setBody(companyData)
            }
        }.body()
    }

    override suspend fun updateCompany(companyId: String, company: HubspotCompany): CompanyResponse {
        val companiesUrl = buildUrl("/crm/v3/objects/companies/$companyId")
        val companyData = CompanyData(company)

        return client().use { client ->
            client.patch(companiesUrl) {
                header(HttpHeaders.Authorization, "Bearer ${hubspotConfiguration.accessToken}")
                contentType(ContentType.Application.Json)
                setBody(companyData)
            }.body()
        }
    }

    override suspend fun searchCompany(request: HubspotSearchRequest): HubspotCompanySearchResponse {
        val searchCompanyUrl = buildUrl("/crm/v3/objects/companies/search")

        return client(FieldNamingPolicy.IDENTITY).use { client ->
            client.post(searchCompanyUrl) {
                header(HttpHeaders.Authorization, "Bearer ${hubspotConfiguration.accessToken}")
                contentType(ContentType.Application.Json)
                setBody(request)
            }.body()
        }
    }

    override suspend fun searchDeal(request: HubspotSearchRequest): HubspotDealSearchResponse {
        val searchDealUrl = buildUrl("/crm/v3/objects/deals/search")

        return client(FieldNamingPolicy.IDENTITY).use { client ->
            client.post(searchDealUrl) {
                header(HttpHeaders.Authorization, "Bearer ${hubspotConfiguration.accessToken}")
                contentType(ContentType.Application.Json)
                setBody(request)
            }.body()
        }
    }

    override suspend fun getAssociationFromDealToContact(dealId: String): HubspotGetAssociationFromDealToContactResponse {
        val getAssociationUrl = buildUrl("/crm/v3/objects/deals/${dealId}/associations/contacts")

        return client(FieldNamingPolicy.IDENTITY).use { client ->
            client.get(getAssociationUrl) {
                header(HttpHeaders.Authorization, "Bearer ${hubspotConfiguration.accessToken}")
                contentType(ContentType.Application.Json)
            }.body()
        }
    }

    override suspend fun searchContactWithProperties(
        request: HubspotSearchRequest,
    ): HubspotContactWithPropertiesSearchResponse {
        val searchContactUrl = buildUrl("/crm/v3/objects/contacts/search")

        return client(FieldNamingPolicy.IDENTITY).use { client ->
            client.post(searchContactUrl) {
                header(HttpHeaders.Authorization, "Bearer ${hubspotConfiguration.accessToken}")
                contentType(ContentType.Application.Json)
                setBody(request)
            }.body()
        }
    }

    private fun buildUrl(url: String) =
        "${hubspotConfiguration.apiBaseUrl}${url}"

}
