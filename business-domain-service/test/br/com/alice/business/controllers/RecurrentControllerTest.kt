package br.com.alice.business.controllers

import br.com.alice.business.client.BeneficiaryOnboardingService
import br.com.alice.business.client.BeneficiaryService
import br.com.alice.business.client.MemberTelegramTrackingService
import br.com.alice.business.events.MemberTelegramTrackingRegisteredEvent
import br.com.alice.business.events.PendingActivationBeneficiaryMembershipEvent
import br.com.alice.business.metrics.MembershipMetric
import br.com.alice.business.metrics.MembershipMetric.Status.FAILURE
import br.com.alice.business.metrics.MembershipMetric.Status.SUCCESS
import br.com.alice.common.data.dsl.matchers.ResponseAssert
import br.com.alice.common.featureflag.withFeatureFlag
import br.com.alice.common.helpers.coVerifyNone
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.common.helpers.returns
import br.com.alice.common.kafka.interfaces.KafkaProducerService
import br.com.alice.common.kafka.interfaces.ProducerResult
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.Beneficiary
import br.com.alice.data.layer.models.BeneficiaryOnboardingFlowType
import br.com.alice.data.layer.models.BeneficiaryOnboardingPhaseType.CONTRACT_SIGNATURE
import br.com.alice.data.layer.models.BeneficiaryOnboardingPhaseType.FINISHED
import br.com.alice.data.layer.models.BeneficiaryOnboardingPhaseType.READY_TO_ONBOARD
import br.com.alice.data.layer.models.BeneficiaryOnboardingPhaseType.REGISTRATION
import br.com.alice.data.layer.models.BeneficiaryOnboardingPhaseType.WAITING_FOR_REVIEW
import br.com.alice.data.layer.models.FeatureNamespace
import br.com.alice.data.layer.models.Member
import br.com.alice.data.layer.models.MemberStatus
import br.com.alice.data.layer.models.ParentBeneficiaryRelationType
import br.com.alice.person.client.MemberService
import com.github.kittinunf.result.failure
import com.github.kittinunf.result.success
import io.mockk.called
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.mockk
import io.mockk.mockkObject
import io.mockk.unmockkAll
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.Nested
import java.time.LocalDateTime
import java.time.LocalDateTime.now
import java.util.UUID
import kotlin.test.AfterTest
import kotlin.test.BeforeTest
import kotlin.test.Test

class RecurrentControllerTest : RecurrentControllerTestHelper() {
    private val beneficiaryService: BeneficiaryService = mockk()
    private val memberService: MemberService = mockk()
    private val beneficiaryOnboardingService: BeneficiaryOnboardingService = mockk()
    private val memberTelegramTrackingService: MemberTelegramTrackingService = mockk()
    private val kafkaProducerService: KafkaProducerService = mockk()
    private val controller =
        RecurrentController(
            beneficiaryService,
            memberService,
            beneficiaryOnboardingService,
            memberTelegramTrackingService,
            kafkaProducerService
        )

    @BeforeTest
    override fun setup() {
        super.setup()
        mockkObject(MembershipMetric)
        mockkObject(logger)
        module.single { controller }
    }

    @AfterTest
    fun clear() {
        unmockkAll()
    }

    @Nested
    inner class ActivateMembership {
        @Test
        fun `#happy path`() {
            val company = TestModelFactory.buildCompany()
            val companySubContract = TestModelFactory.buildCompanySubContract(companyId = company.id)

            val company2 = TestModelFactory.buildCompany()
            val companySubContract2 = TestModelFactory.buildCompanySubContract(companyId = company2.id)

            val members = mutableListOf<Member>().apply {
                repeat(300) {
                    add(TestModelFactory.buildMember(status = MemberStatus.PENDING))
                }
            }

            val beneficiariesToUpdate = mutableListOf<Beneficiary>().apply {
                repeat(300) {
                    val companyId = if (it < 150) company.id else company2.id
                    val companySubContractId = if (it < 150) companySubContract.id else companySubContract2.id
                    add(
                        TestModelFactory.buildBeneficiary(
                            memberId = members[it].id,
                            companyId = companyId,
                            companySubContractId = companySubContractId,
                            memberStatus = MemberStatus.PENDING,
                        )
                    )
                }
            }

            beneficiariesToUpdate[203] = TestModelFactory.buildBeneficiary(
                memberId = members[203].id,
                companyId = company2.id,
                companySubContractId = companySubContract2.id,
                memberStatus = MemberStatus.PENDING,
                parentBeneficiary = beneficiariesToUpdate[100].id,
                parentBeneficiaryRelationType = ParentBeneficiaryRelationType.CHILD,
                parentPerson = beneficiariesToUpdate[100].personId,
                parentBeneficiaryRelatedAt = now().minusYears(10)
            )

            coEvery {
                beneficiaryService.findPending(
                    any(),
                    0,
                    any()
                )
            } returns beneficiariesToUpdate.take(200)

            coEvery {
                beneficiaryService.findPending(
                    any(),
                    200,
                    any()
                )
            } returns beneficiariesToUpdate.drop(200)

            coEvery {
                beneficiaryOnboardingService.findByBeneficiaryId(any())
            } returns TestModelFactory.buildBeneficiaryOnboarding(
                phases = listOf(
                    TestModelFactory.buildBeneficiaryOnboardingPhase(
                        phase = WAITING_FOR_REVIEW
                    )
                )
            )

            coEvery {
                beneficiaryOnboardingService.findByBeneficiaryId(beneficiariesToUpdate[150].id)
            } returns TestModelFactory.buildBeneficiaryOnboarding(
                phases = listOf(
                    TestModelFactory.buildBeneficiaryOnboardingPhase(
                        phase = REGISTRATION
                    )
                )
            )

            val beneficiaryIds = beneficiariesToUpdate.map { it.id }

            coEvery { beneficiaryService.activateBeneficiary(match { it in beneficiaryIds }) } answers {
                val beneficiary = beneficiariesToUpdate.first { it.id == arg<UUID>(0) }

                beneficiary.success()
            }

            coEvery { beneficiaryOnboardingService.moveToNextPhase(any()) } returns TestModelFactory.buildBeneficiaryOnboardingPhase(
                phase = FINISHED
            )

            coEvery { memberService.findByIds(members.take(200).map { it.id }) } returns members.take(200)

            coEvery {
                memberService.findByIds(members.drop(200).filterNot { it.id == members[203].id }.map { it.id })
            } returns members.drop(200).filterNot { it.id == members[203].id }

            internalAuthentication {
                postAsPlainText("/recurring_subscribers/activate_beneficiaries") { response ->
                    ResponseAssert.assertThat(response).isOK()
                }
            }

            coVerify(exactly = 300) {
                beneficiaryOnboardingService.findByBeneficiaryId(any())
            }
            coVerify(exactly = 2) {
                beneficiaryService.findPending(
                    any(),
                    any(),
                    any(),
                )
            }
            coVerifyOnce { memberService.findByIds(members.take(200).map { it.id }) }
            coVerifyOnce {
                memberService.findByIds(members.drop(200).filterNot { it.id == members[203].id }.map { it.id })
            }
            coVerify(exactly = 298) { beneficiaryOnboardingService.moveToNextPhase(any()) }
            coVerifyNone { beneficiaryOnboardingService.moveToNextPhase(beneficiariesToUpdate[150].id) }
            coVerifyNone { beneficiaryOnboardingService.moveToNextPhase(beneficiariesToUpdate[203].id) }
            coVerify(exactly = 299) { beneficiaryService.activateBeneficiary(any()) }
            coVerify(exactly = 299) { MembershipMetric.reportActivation(SUCCESS) }
        }

        @Test
        fun `#if fails on activating membership`() {
            val members = listOf(
                TestModelFactory.buildMember(
                    status = MemberStatus.PENDING
                ),
                TestModelFactory.buildMember(
                    status = MemberStatus.PENDING
                ),
            )
            val beneficiariesToUpdate = listOf(
                TestModelFactory.buildBeneficiary(
                    memberId = members[0].id,
                    memberStatus = MemberStatus.PENDING
                ),
                TestModelFactory.buildBeneficiary(
                    memberId = members[1].id,
                    memberStatus = MemberStatus.PENDING
                )
            )

            coEvery {
                beneficiaryService.findPending(
                    any(),
                    any(),
                    any(),
                )
            } returns beneficiariesToUpdate
            coEvery {
                beneficiaryOnboardingService.findByBeneficiaryId(any())
            } returns TestModelFactory.buildBeneficiaryOnboarding(
                phases = listOf(TestModelFactory.buildBeneficiaryOnboardingPhase(phase = WAITING_FOR_REVIEW))
            )
            coEvery { beneficiaryService.activateBeneficiary(beneficiariesToUpdate[0].id) } returns beneficiariesToUpdate[0]
            coEvery { beneficiaryService.activateBeneficiary(beneficiariesToUpdate[1].id) } returns IllegalArgumentException(
                "Some error"
            )
            coEvery { memberService.findByIds(members.map { it.id }) } returns members

            coEvery { beneficiaryOnboardingService.moveToNextPhase(beneficiariesToUpdate[0].id) } returns TestModelFactory.buildBeneficiaryOnboardingPhase(
                phase = FINISHED
            )


            internalAuthentication {
                postAsPlainText("/recurring_subscribers/activate_beneficiaries") { response ->
                    ResponseAssert.assertThat(response).isOK()
                }
            }
            coVerifyOnce {
                beneficiaryService.findPending(
                    any(),
                    any(),
                    any(),
                )
            }
            coVerify(exactly = 2) {
                beneficiaryOnboardingService.findByBeneficiaryId(any())
            }
            coVerify(exactly = 2) { beneficiaryService.activateBeneficiary(any()) }
            coVerifyOnce { beneficiaryOnboardingService.moveToNextPhase(any()) }
            coVerifyOnce { MembershipMetric.reportActivation(SUCCESS) }
            coVerifyOnce { MembershipMetric.reportActivation(FAILURE) }
        }

        @Test
        fun `#ignore beneficiary without WAITING_FOR_REVIEW currentPhase`() {
            val correctPhase = TestModelFactory.buildBeneficiaryOnboardingPhase(phase = WAITING_FOR_REVIEW)
            val wrongPhase = TestModelFactory.buildBeneficiaryOnboardingPhase(phase = CONTRACT_SIGNATURE)
            val members = listOf(
                TestModelFactory.buildMember(
                    status = MemberStatus.PENDING
                ),
            )
            val beneficiariesToUpdate = listOf(
                TestModelFactory.buildBeneficiary(
                    memberId = members[0].id,
                    memberStatus = MemberStatus.PENDING
                ),
                TestModelFactory.buildBeneficiary(
                    memberStatus = MemberStatus.PENDING
                ),
            )

            coEvery {
                beneficiaryService.findPending(
                    any(),
                    any(),
                    any(),
                )
            } returns beneficiariesToUpdate
            coEvery {
                beneficiaryOnboardingService.findByBeneficiaryId(beneficiariesToUpdate[0].id)
            } returns TestModelFactory.buildBeneficiaryOnboarding(
                phases = listOf(correctPhase)
            )
            coEvery {
                beneficiaryOnboardingService.findByBeneficiaryId(beneficiariesToUpdate[1].id)
            } returns TestModelFactory.buildBeneficiaryOnboarding(
                phases = listOf(wrongPhase)
            )
            coEvery { beneficiaryService.activateBeneficiary(beneficiariesToUpdate[0].id) } returns beneficiariesToUpdate[0]
            coEvery { beneficiaryOnboardingService.moveToNextPhase(beneficiariesToUpdate[0].id) } returns TestModelFactory.buildBeneficiaryOnboardingPhase(
                phase = FINISHED
            )
            coEvery { memberService.findByIds(members.map { it.id }) } returns members

            internalAuthentication {
                postAsPlainText("/recurring_subscribers/activate_beneficiaries") { response ->
                    ResponseAssert.assertThat(response).isOK()
                }
            }
            coVerifyOnce {
                beneficiaryService.findPending(
                    any(),
                    any(),
                    any(),
                )
            }
            coVerify(exactly = 2) {
                beneficiaryOnboardingService.findByBeneficiaryId(any())
            }
            coVerifyOnce {
                beneficiaryOnboardingService.moveToNextPhase(beneficiariesToUpdate[0].id)
                beneficiaryService.activateBeneficiary(any())
                MembershipMetric.reportActivation(SUCCESS)
            }
        }

        @Test
        fun `#should activate beneficiary with NO_RISK_FLOW flowType`() {
            val members = listOf(
                TestModelFactory.buildMember(
                    status = MemberStatus.PENDING
                ),
            )
            val beneficiariesToUpdate = listOf(
                TestModelFactory.buildBeneficiary(
                    memberId = members[0].id,
                    memberStatus = MemberStatus.PENDING
                ),
            )

            coEvery {
                beneficiaryService.findPending(
                    any(),
                    any(),
                    any(),
                )
            } returns beneficiariesToUpdate
            coEvery {
                beneficiaryOnboardingService.findByBeneficiaryId(any())
            } returns TestModelFactory.buildBeneficiaryOnboarding(
                flowType = BeneficiaryOnboardingFlowType.NO_RISK_FLOW,
                phases = emptyList()
            )
            coEvery { beneficiaryService.activateBeneficiary(beneficiariesToUpdate[0].id) } returns beneficiariesToUpdate[0]
            coEvery { memberService.findByIds(members.map { it.id }) } returns members

            internalAuthentication {
                postAsPlainText("/recurring_subscribers/activate_beneficiaries") { response ->
                    ResponseAssert.assertThat(response).isOK()
                }
            }
            coVerifyOnce {
                beneficiaryService.findPending(
                    any(),
                    any(),
                    any(),
                )
            }
            coVerifyOnce {
                beneficiaryOnboardingService.findByBeneficiaryId(any())
            }
            coVerifyOnce { beneficiaryService.activateBeneficiary(any()) }
            coVerifyOnce { MembershipMetric.reportActivation(SUCCESS) }
        }

        @Test
        fun `#ignore beneficiary with wrong memberStatus`() {
            val members = listOf(
                TestModelFactory.buildMember(
                    status = MemberStatus.PENDING
                ),
                TestModelFactory.buildMember(
                    status = MemberStatus.PENDING
                ),
                TestModelFactory.buildMember(
                    status = MemberStatus.ACTIVE
                ),
            )
            val beneficiariesToUpdate = listOf(
                TestModelFactory.buildBeneficiary(
                    memberId = members[0].id,
                    memberStatus = MemberStatus.PENDING
                ),
                TestModelFactory.buildBeneficiary(
                    memberId = members[1].id,
                    memberStatus = MemberStatus.PENDING
                ),
                TestModelFactory.buildBeneficiary(
                    memberId = members[2].id,
                    memberStatus = MemberStatus.ACTIVE
                )
            )

            coEvery {
                beneficiaryService.findPending(
                    any(),
                    any(),
                    any(),
                )
            } returns beneficiariesToUpdate
            coEvery {
                beneficiaryOnboardingService.findByBeneficiaryId(any())
            } returns TestModelFactory.buildBeneficiaryOnboarding(
                phases = listOf(TestModelFactory.buildBeneficiaryOnboardingPhase(phase = WAITING_FOR_REVIEW))
            )
            coEvery { beneficiaryService.activateBeneficiary(beneficiariesToUpdate[0].id) } returns beneficiariesToUpdate[0]
            coEvery { beneficiaryService.activateBeneficiary(beneficiariesToUpdate[1].id) } returns beneficiariesToUpdate[1]
            coEvery { memberService.findByIds(members.map { it.id }) } returns members
            coEvery { beneficiaryOnboardingService.moveToNextPhase(beneficiariesToUpdate[0].id) } returns TestModelFactory.buildBeneficiaryOnboardingPhase(
                phase = FINISHED
            )
            coEvery { beneficiaryOnboardingService.moveToNextPhase(beneficiariesToUpdate[1].id) } returns TestModelFactory.buildBeneficiaryOnboardingPhase(
                phase = FINISHED
            )

            internalAuthentication {
                postAsPlainText("/recurring_subscribers/activate_beneficiaries") { response ->
                    ResponseAssert.assertThat(response).isOK()
                }
            }
            coVerifyOnce {
                beneficiaryService.findPending(
                    any(),
                    any(),
                    any(),
                )
            }
            coVerify(exactly = 3) {
                beneficiaryOnboardingService.findByBeneficiaryId(
                    any()
                )
            }
            coVerify(exactly = 2) { beneficiaryService.activateBeneficiary(any()) }
            coVerify(exactly = 2) { beneficiaryOnboardingService.moveToNextPhase(any()) }
        }
    }

    @Test
    fun `#inactivateRegistrationMembership - happy path`() {
        val now = LocalDateTime.now()
        val members = mutableListOf<Member>().apply {
            repeat(300) {
                add(TestModelFactory.buildMember())
            }
        }

        val beneficiaries = mutableListOf<Beneficiary>().apply {
            repeat(300) {
                add(
                    TestModelFactory.buildBeneficiary(
                        memberId = members[it].id,
                        canceledAt = now
                    )
                )
            }
        }

        members[199] = members[199].copy(status = MemberStatus.CANCELED)

        coEvery {
            beneficiaryService.findByCanceledAtBetweenInclusivePaginated(
                any(),
                any(),
                0,
                200
            )
        } returns beneficiaries.take(200).success()

        coEvery {
            beneficiaryService.findByCanceledAtBetweenInclusivePaginated(
                any(),
                any(),
                200,
                200
            )
        } returns beneficiaries.drop(200).success()

        coEvery { memberService.findByIds(beneficiaries.take(200).map { it.memberId }) } returns members.take(200)
            .success()
        coEvery { memberService.findByIds(beneficiaries.drop(200).map { it.memberId }) } returns members.drop(200)
            .success()
        coEvery { memberService.cancel(match { it in members }, canceledAt = now) } answers {
            val member = arg<Member>(0)

            member.success()
        }

        coEvery { memberService.cancel(members[38], canceledAt = now) } returns Exception("").failure()

        internalAuthentication {
            postAsPlainText("/recurring_subscribers/inactivate_beneficiary_membership") { response ->
                ResponseAssert.assertThat(response).isOK()
            }
        }

        coVerify(exactly = 1) { beneficiaryService.findByCanceledAtBetweenInclusivePaginated(any(), any(), 0, 200) }
        coVerify(exactly = 1) { beneficiaryService.findByCanceledAtBetweenInclusivePaginated(any(), any(), 200, 200) }
        coVerify(exactly = 1) { memberService.findByIds(beneficiaries.take(200).map { it.memberId }) }
        coVerify(exactly = 1) { memberService.findByIds(beneficiaries.drop(200).map { it.memberId }) }
        coVerify(exactly = 299) { memberService.cancel(match { it in members }, canceledAt = now) }
        coVerify(exactly = 0) { memberService.cancel(members[199]) }
        coVerify(exactly = 298) { MembershipMetric.reportInactivation(SUCCESS) }
        coVerify(exactly = 1) { MembershipMetric.reportInactivation(FAILURE) }
    }

    @Test
    fun `#inactivateRegistrationMembership - if fails on inactivating membership`() {

        coEvery {
            beneficiaryService.findByCanceledAtBetweenInclusivePaginated(
                any(),
                any(),
                0,
                200,
            )
        } returns Exception("").failure()

        internalAuthentication {
            postAsPlainText("/recurring_subscribers/inactivate_beneficiary_membership") { response ->
                ResponseAssert.assertThat(response).isInternalServerError()
            }
        }

        coVerify(exactly = 1) { beneficiaryService.findByCanceledAtBetweenInclusivePaginated(any(), any(), 0, 200) }
    }

    @Test
    fun `#triggerBeneficiariesCassiAccountNumber - success`() {
        coEvery {
            beneficiaryService.triggerCassiAccountNumberForBeneficiaries(
                0,
                200,
            )
        } returns 200
        coEvery {
            beneficiaryService.triggerCassiAccountNumberForBeneficiaries(
                200,
                200,
            )
        } returns 150

        internalAuthentication {
            postAsPlainText("/recurring_subscribers/trigger_beneficiaries_cassi_account_number") { response ->
                ResponseAssert.assertThat(response).isOK()
            }
        }

        coVerify(exactly = 2) { beneficiaryService.triggerCassiAccountNumberForBeneficiaries(any(), any()) }
    }


    @Test
    fun `#requestBeneficiariesCassiAccountNumberOlderExpirationDate - execute case expiration date successful (Feature flag is enabled)`() =
        runBlocking {
            coEvery { beneficiaryService.triggerCassiAccountNumberForBeneficiariesWithOlderExpirationDate() } returns true.success()

            withFeatureFlag(FeatureNamespace.BUSINESS, "enable_cassi_expiration_date_routine", true) {
                internalAuthentication {
                    postAsPlainText("/recurring_subscribers/update_cassi_older_expiration_date") { response ->
                        ResponseAssert.assertThat(response).isOK()
                        ResponseAssert.assertThat(response).isOKWithData(true)
                    }
                }
            }

            coVerifyOnce { beneficiaryService.triggerCassiAccountNumberForBeneficiariesWithOlderExpirationDate() }
        }

    @Test
    fun `#requestBeneficiariesCassiAccountNumberOlderExpirationDate - execute case expiration date successful (Feature flag is disabled)`() {
        coEvery { beneficiaryService.triggerCassiAccountNumberForBeneficiariesWithOlderExpirationDate() } returns true.success()

        internalAuthentication {
            postAsPlainText("/recurring_subscribers/update_cassi_older_expiration_date") { response ->
                ResponseAssert.assertThat(response).isOK()
                ResponseAssert.assertThat(response).isOKWithData(false)
            }
        }

        coVerify { beneficiaryService.triggerCassiAccountNumberForBeneficiariesWithOlderExpirationDate() wasNot called }

    }

    @Test
    fun `#pendingActivationBeneficiariesMemberships - one member already activated`() {
        val onboarding = TestModelFactory.buildBeneficiaryOnboarding(
            flowType = BeneficiaryOnboardingFlowType.FULL_RISK_FLOW,
            phases = listOf(TestModelFactory.buildBeneficiaryOnboardingPhase(phase = READY_TO_ONBOARD))
        )
        val beneficiaries = mutableListOf<Beneficiary>().apply {
            repeat(300) { add(TestModelFactory.buildBeneficiary(activatedAt = now())) }
        }

        beneficiaries[119] = TestModelFactory.buildBeneficiary(activatedAt = now().minusDays(1))
        beneficiaries[120] = TestModelFactory.buildBeneficiary(activatedAt = now().plusDays(1))

        val events = beneficiaries.associateBy { it.id }

        coEvery {
            beneficiaryService.findPending(
                any(),
                0,
                200
            )
        } returns beneficiaries.take(200)
        coEvery {
            beneficiaryService.findPending(
                any(),
                200,
                200
            )
        } returns beneficiaries.drop(200)
        coEvery {
            beneficiaryOnboardingService.findByBeneficiaryId(any())
        } returns onboarding

        coEvery {
            kafkaProducerService.produce(
                match<PendingActivationBeneficiaryMembershipEvent> {
                    events[it.payload.beneficiary.id]?.let {
                        beneficiaryPayload -> beneficiaryPayload.copy(onboarding = onboarding) == it.payload.beneficiary
                    } ?: false
                }
            )
        } returns ProducerResult(
            now(),
            "1",
            1
        )

        internalAuthentication {
            postAsPlainText("/recurring_subscribers/pending_activation_beneficiaries") { response ->
                ResponseAssert.assertThat(response).isOK()
            }
        }

        coVerify(exactly = 2) {
            beneficiaryService.findPending(
                any(),
                any(),
                any(),
            )
        }
        coVerify(exactly = 298) {
            kafkaProducerService.produce(any())
            beneficiaryOnboardingService.findByBeneficiaryId(any())
        }
    }

    @Test
    fun `#syncNotDeliveredItems - sync telegram items successfully`() {

        val items = listOf(
            TestModelFactory.buildMemberTelegramTracking(),
            TestModelFactory.buildMemberTelegramTracking(),
            TestModelFactory.buildMemberTelegramTracking(),
        )
        val events = items.map {
            MemberTelegramTrackingRegisteredEvent(it.externalId)
        }

        coEvery { memberTelegramTrackingService.findNotDeliveredItems() } returns items

        coEvery { kafkaProducerService.produce(match<MemberTelegramTrackingRegisteredEvent> { it.payload == events[0].payload }) } returns ProducerResult(
            now(),
            "1",
            1
        )
        coEvery { kafkaProducerService.produce(match<MemberTelegramTrackingRegisteredEvent> { it.payload == events[1].payload }) } returns ProducerResult(
            now(),
            "1",
            1
        )
        coEvery { kafkaProducerService.produce(match<MemberTelegramTrackingRegisteredEvent> { it.payload == events[2].payload }) } returns ProducerResult(
            now(),
            "1",
            1
        )

        internalAuthentication {
            postAsPlainText("/recurring_subscribers/sync_not_delivered_telegram_items") { response ->
                ResponseAssert.assertThat(response).isOK()
            }
        }

        coVerifyOnce { memberTelegramTrackingService.findNotDeliveredItems() }
        coVerify(exactly = 3) { kafkaProducerService.produce(any()) }
    }

}
