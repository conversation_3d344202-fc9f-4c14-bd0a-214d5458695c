package br.com.alice.member.api.models.accreditedNetwork

import br.com.alice.app.content.model.Tag
import br.com.alice.member.api.models.NavigationResponse
import br.com.alice.member.api.models.appContent.Navigation
import java.util.UUID

data class MedicalSpecialtyTransport(
    val id: UUID,
    val name: String,
    val title: String,
    val description: String? = null,
    val imageUrl: String? = null,
    val needsConfirmation: Boolean,
    val tag: Tag?,
    val navigation: Navigation,
    val hasReferral: Boolean = false
)

data class MedicalSpecialtyResponse(
    val title: String,
    val shortcuts: List<ShortcutTransport>? = emptyList(),
    val items: List<MedicalSpecialtyTransport>,
    val action: ActionNavigation?,
    val isDuquesa: Boolean = false
) {
    data class ActionNavigation(
        val label: String,
        val navigation: NavigationResponse
    )

    data class ShortcutTransport(
        val name: String,
        val icon: String,
        val navigation: Navigation
    )
}
