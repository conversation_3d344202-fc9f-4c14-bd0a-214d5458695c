package br.com.alice.channel.core.services.internal

import br.com.alice.channel.assistance.services.internal.VirtualClinicMessagesService
import br.com.alice.channel.client.ReRoutingExtraInfo
import br.com.alice.channel.core.FirestoreTransactionalTestHelper
import br.com.alice.channel.core.context.FirestoreContextUsage
import br.com.alice.channel.core.mockTimestamp
import br.com.alice.channel.core.services.internal.firestore.ChannelFirestoreService
import br.com.alice.channel.core.services.internal.firestore.MessageFirestoreService
import br.com.alice.channel.core.util.AI_STAFF_ID
import br.com.alice.channel.extensions.toTimestamp
import br.com.alice.channel.models.ChannelDocument
import br.com.alice.channel.models.ChannelMessageQuestionOptions
import br.com.alice.channel.models.ChannelStaffInfo
import br.com.alice.channel.models.MessageDocument
import br.com.alice.channel.models.MessageType
import br.com.alice.channel.models.QuestionContent
import br.com.alice.channel.models.redirectTag
import br.com.alice.channel.models.redirectedToN2ChannelTag
import br.com.alice.channel.notifier.ChannelArchivedByInactivityEvent
import br.com.alice.common.RangeUUID
import br.com.alice.common.core.PersonId
import br.com.alice.common.core.Role.DIGITAL_CARE_NURSE
import br.com.alice.common.core.Role.MANAGER_PHYSICIAN
import br.com.alice.common.core.Role.SCREENING_NURSE
import br.com.alice.common.core.exceptions.InvalidArgumentException
import br.com.alice.common.core.exceptions.NotFoundException
import br.com.alice.common.core.extensions.toUUID
import br.com.alice.common.data.dsl.matchers.ResultAssert.Companion.assertThat
import br.com.alice.common.extensions.toPersonId
import br.com.alice.common.featureflag.withFeatureFlag
import br.com.alice.common.featureflag.withFeatureFlags
import br.com.alice.common.helpers.coVerifyNone
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.common.helpers.verifyOnce
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.CaseSeverity
import br.com.alice.data.layer.models.ChannelArchivedReason
import br.com.alice.data.layer.models.ChannelAssessment
import br.com.alice.data.layer.models.ChannelCategory
import br.com.alice.data.layer.models.ChannelChangeAction
import br.com.alice.data.layer.models.ChannelHistoryExtraInfo
import br.com.alice.data.layer.models.ChannelKind
import br.com.alice.data.layer.models.ChannelStatus
import br.com.alice.data.layer.models.ChannelSubCategory
import br.com.alice.data.layer.models.ChannelSubCategoryClassifier
import br.com.alice.data.layer.models.ChannelSubCategoryClassifier.HEALTH_TEAM
import br.com.alice.data.layer.models.ChannelType
import br.com.alice.data.layer.models.Demand
import br.com.alice.common.Disease
import br.com.alice.common.core.exceptions.AccessForbiddenException
import br.com.alice.data.layer.models.ExtraInfoType
import br.com.alice.data.layer.models.FeatureNamespace
import br.com.alice.data.layer.models.HealthFormQuestionType
import br.com.alice.data.layer.models.Screening
import br.com.alice.data.layer.models.VirtualClinicStatusControl
import br.com.alice.person.client.PersonService
import br.com.alice.questionnaire.models.HealthFormQuestionOptionResponse
import br.com.alice.questionnaire.models.QuestionnaireQuestionInputResponse
import br.com.alice.staff.converters.StaffGenderDescriptionConverter
import com.github.kittinunf.result.failure
import com.github.kittinunf.result.success
import com.google.cloud.Timestamp
import com.google.cloud.firestore.CollectionReference
import com.google.cloud.firestore.FieldValue
import com.google.cloud.firestore.FieldValue.arrayRemove
import com.google.cloud.firestore.Query
import io.mockk.called
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.confirmVerified
import io.mockk.every
import io.mockk.mockk
import io.mockk.mockkStatic
import io.mockk.slot
import io.mockk.spyk
import io.mockk.verify
import kotlinx.coroutines.runBlocking
import java.time.LocalDate
import java.time.LocalDateTime
import kotlin.reflect.KProperty1
import kotlin.test.AfterTest
import kotlin.test.BeforeTest
import kotlin.test.Test

class ChannelServiceTest : FirestoreTransactionalTestHelper() {

    private val channelNotificationService: ChannelNotificationService = mockk()
    private val messageFirestoreService: MessageFirestoreService = mockk()
    private val channelFirestoreService: ChannelFirestoreService = mockk()
    private val personService: PersonService = mockk()
    private val virtualClinicMessagesService: VirtualClinicMessagesService = mockk()

    private val service = ChannelService(
        channelNotificationService,
        messageFirestoreService,
        channelFirestoreService,
        personService,
        virtualClinicMessagesService
    )
    private val serviceSpy = spyk(service)

    private val timestamp = Timestamp.now()
    private val personId = PersonId()
    private val personReference = TestModelFactory.buildPersonInternalReference(personId)
    private val anotherStaffId = "df86b2e8-7266-4005-b961-ce4b046e7d11"
    private val staff01 = TestModelFactory.buildStaff(id = staffId.toUUID(), role = DIGITAL_CARE_NURSE)
    private val staff02 = TestModelFactory.buildStaff(id = anotherStaffId.toUUID(), firstName = "Maria")
    private val person = TestModelFactory.buildPerson(personId = personId)
    private val channelStaffOwner = ChannelStaffInfo(
        id = staff01.id.toString(),
        name = staff01.firstName,
        firstName = staff01.firstName,
        lastName = staff01.lastName,
        description = StaffGenderDescriptionConverter.convert(staff01),
        profileImageUrl = staff01.profileImageUrl.orEmpty(),
        owner = true,
        roles = listOf(DIGITAL_CARE_NURSE),
        lastSync = timestamp
    )
    private val staffChannel02 = ChannelStaffInfo(
        id = staff02.id.toString(),
        name = staff02.firstName,
        firstName = staff02.firstName,
        lastName = staff02.lastName,
        description = StaffGenderDescriptionConverter.convert(staff02),
        profileImageUrl = staff02.profileImageUrl.orEmpty(),
        owner = staff02.allRoles().contains(MANAGER_PHYSICIAN),
        lastSync = timestamp
    )
    private val channelDocument = ChannelDocument(
        id = channelId,
        name = "channel_name",
        personId = personId.toString(),
        channelPersonId = personReference.channelPersonId.toString(),
        kind = ChannelKind.CHANNEL,
        category = ChannelCategory.ASSISTANCE,
        type = ChannelType.ASSISTANCE_CARE,
        status = ChannelStatus.ACTIVE,
        staff = mutableMapOf(staffId to channelStaffOwner.copy(onBackground = true)),
        appVersion = "2.10.1 (11539) android",
        demands = listOf(
            Demand(
                caseId = RangeUUID.generate(),
                startedAt = LocalDateTime.now(),
                description = Disease(
                    type = Disease.Type.CID_10,
                    value = "A10",
                    description = "CID A10"
                ),
                severity = CaseSeverity.DECOMPENSATED
            )
        )
    )

    private val csatFormUrl =
        "https://alicesaudestaging.page.link/?link=https://www.alice.com.br/questionnaire?dl%3Dtrue%26type%3DTEST_CSAT_AA%26source_type%3DCHANNEL%26source_id%3D$channelId&apn=com.alicesaude.staging&isi=1528785841&ibi=staging.br.com.alice.tech.enduser"

    private val archivedReason = ChannelArchivedReason.STAFF_ACTION

    private val messageDocument = MessageDocument(
        type = MessageType.TEXT,
        userId = staffId,
        aliceId = staffId
    )

    private val zendeskTicketId = "123"

    @OptIn(FirestoreContextUsage::class)
    @BeforeTest
    fun setup() {
        mockkStatic(Timestamp::class)

        every { Timestamp.now() } returns timestamp

        coEvery {
            messageFirestoreService.add(channelId,
                match {
                    it.aliceId == channelStaffOwner.id &&
                            it.content == "Conversa encerrada" &&
                            it.type == MessageType.CHANNEL_ARCHIVED
                }
            )
        } returns channelId.success()
    }

    @AfterTest
    fun confirmMocks() =
        confirmVerified(
            channelNotificationService,
            messageFirestoreService,
            channelFirestoreService,
            personService,
            virtualClinicMessagesService,
            serviceSpy
        )

    @OptIn(FirestoreContextUsage::class)
    @Test
    fun `#setTags should add tags to channel`() = runBlocking {
        val tags = listOf("TAG1", "TAG2")

        val expectedChannel = channelDocument.copy(tags = tags)

        coEvery { channelFirestoreService.getChannel(channelId) } returns channelDocument.success()
        coEvery {
            channelFirestoreService.updateFields(
                channelId,
                mapOf(ChannelDocument::tags to tags)
            )
        } returns channelId.success()
        coEvery {
            channelNotificationService.notify(expectedChannel, ChannelChangeAction.SET_TAGS)
        } returns mockk()

        val result = service.setTags(channelId, tags)
        assertThat(result).isSuccessWithData(channelId)

        coVerifyOnce { channelFirestoreService.getChannel(any()) }
        coVerifyOnce { channelFirestoreService.updateFields(any(), any()) }
        coVerifyOnce { channelNotificationService.notify(any<ChannelDocument>(), any(), any(), any()) }
    }

    @OptIn(FirestoreContextUsage::class)
    @Test
    fun `#setTags should return failure`() = runBlocking {
        val tags = listOf("TAG1", "TAG2")

        coEvery { channelFirestoreService.getChannel(channelId) } returns channelDocument.success()
        coEvery {
            channelFirestoreService.updateFields(
                channelId,
                mapOf(ChannelDocument::tags to tags)
            )
        } returns Exception().failure()

        val result = service.setTags(channelId, tags)
        assertThat(result).isFailureOfType(Exception::class)

        coVerifyOnce { channelFirestoreService.getChannel(any()) }
        coVerifyOnce { channelFirestoreService.updateFields(any(), any()) }
    }

    @OptIn(FirestoreContextUsage::class)
    @Test
    fun `#setChannelAssessment should set assessment`() = runBlocking {
        val parameters = mapOf<KProperty1<ChannelDocument, Any?>, Any?>(
            ChannelDocument::screening to Screening(ChannelAssessment.EMERGENCY)
        )

        coEvery { serviceSpy.updateFields(channelId, parameters) } returns channelId.success()

        val result = serviceSpy.setChannelAssessment(channelId, ChannelAssessment.EMERGENCY)
        assertThat(result).isSuccessWithData(channelId)

        coVerifyOnce { serviceSpy.setChannelAssessment(any(), any()) }
        coVerifyOnce { serviceSpy.span(any(), any(), any()) }
        coVerifyOnce { serviceSpy.updateFields(any(), any()) }
    }

    @OptIn(FirestoreContextUsage::class)
    @Test
    fun `#deleteMessage should return access forbiden if staff os null`() = runBlocking {
        coEvery {
            messageFirestoreService.getMessage(channelId, messageId)
        } returns messageDocument.copy(aliceId = null).success()

        val result = service.deleteMessage(channelId, messageId, channelStaffOwner.id, false)
        assertThat(result).isFailureOfType(AccessForbiddenException::class)

        coVerifyOnce { messageFirestoreService.getMessage(any(), any()) }
        coVerifyNone { messageFirestoreService.delete(any(), any<MessageDocument>()) }
        coVerifyNone { messageFirestoreService.delete(any(), any<String>()) }
        coVerify { channelNotificationService wasNot called }
        coVerify { channelFirestoreService wasNot called }
    }

    @OptIn(FirestoreContextUsage::class)
    @Test
    fun `#deleteMessage should delete`() = runBlocking {
        coEvery { messageFirestoreService.getMessage(channelId, messageId) } returns messageDocument.success()
        coEvery { messageFirestoreService.delete(channelId, messageDocument) } returns messageId.success()

        val result = service.deleteMessage(channelId, messageId, channelStaffOwner.id, false)
        assertThat(result).isSuccessWithData(messageId)

        coVerifyOnce { messageFirestoreService.getMessage(any(), any()) }
        coVerifyOnce { messageFirestoreService.delete(any(), any<MessageDocument>()) }
        coVerify { channelNotificationService wasNot called }
        coVerify { channelFirestoreService wasNot called }
    }

    @OptIn(FirestoreContextUsage::class)
    @Test
    fun `#deleteMessage should delete when requested staff is not owner of message, but has role to do this`() =
        runBlocking {
            val messageDocument = messageDocument.copy(aliceId = anotherStaffId)
            coEvery { messageFirestoreService.getMessage(channelId, messageId) } returns messageDocument.success()
            coEvery { messageFirestoreService.delete(channelId, messageDocument) } returns messageId.success()

            val result = service.deleteMessage(channelId, messageId, channelStaffOwner.id, true)
            assertThat(result).isSuccessWithData(messageId)

            coVerifyOnce { messageFirestoreService.getMessage(any(), any()) }
            coVerifyOnce { messageFirestoreService.delete(any(), any<MessageDocument>()) }
            coVerify { channelNotificationService wasNot called }
            coVerify { channelFirestoreService wasNot called }
        }

    @OptIn(FirestoreContextUsage::class)
    @Test
    fun `#addStaff returns channel and add new staff and remove from history`() = runBlocking {
        val expectedMessage = buildChannelMessage(
            type = MessageType.ADD_PARTICIPANT,
            staffOrPersonId = staffId,
            content = "${staffChannel02.name} ${MessageType.ADD_PARTICIPANT.description}"
        )

        val expectedChannel = channelDocument.copy(
            staff = channelDocument.staff.plus(staffChannel02.id to staffChannel02).toMutableMap(),
            staffHistory = channelDocument.staffHistory.minus(staffChannel02.id).toMutableMap(),
            staffIds = channelDocument.staffIds!!.plus(staffChannel02.id)
        )

        coEvery { channelFirestoreService.getChannel(channelId) } returns channelDocument.success()
        coEvery {
            serviceSpy.updateFields(
                channelId,
                mapOf(
                    ChannelDocument::staff to mapOf(staffChannel02.id to staffChannel02),
                    ChannelDocument::staffHistory to mapOf(staffChannel02.id to FieldValue.delete()),
                    ChannelDocument::staffIds to FieldValue.arrayUnion(staffChannel02.id),
                    ChannelDocument::onCallAction to FieldValue.delete(),
                )
            )
        } returns channelId.success()
        coEvery {
            messageFirestoreService.add(channelId,
                match {
                    it.type == expectedMessage.type
                            && it.aliceId == expectedMessage.aliceId
                            && it.content == expectedMessage.content
                })
        } returns channelId.success()
        coEvery {
            channelNotificationService.notify(expectedChannel, ChannelChangeAction.ADD_STAFF)
        } returns mockk()

        val result = serviceSpy.addStaff(channelId, staff02, staffId, false)
        assertThat(result).isSuccessWithData(channelId)

        coVerifyOnce { serviceSpy.span(any(), any(), any()) }
        coVerifyOnce { serviceSpy.updateFields(any(), any()) }
        coVerifyOnce { serviceSpy.addStaff(any(), any(), any(), any()) }
        coVerifyOnce { serviceSpy.getChannel(any()) }
        coVerifyOnce { channelFirestoreService.getChannel(any(), any()) }
        coVerifyOnce { messageFirestoreService.add(any(), any()) }
        coVerifyOnce { channelNotificationService.notify(any<ChannelDocument>(), any(), any(), any()) }
    }

    @OptIn(FirestoreContextUsage::class)
    @Test
    fun `#addStaff returns channel and not add staff when it already exists`() = runBlocking {
        coEvery { channelFirestoreService.getChannel(channelId) } returns channelDocument.success()

        val result = serviceSpy.addStaff(channelId, staff01, anotherStaffId, false)
        assertThat(result).isSuccessWithData(channelId)

        coVerifyOnce { serviceSpy.span(any(), any(), any()) }
        coVerifyOnce { serviceSpy.addStaff(any(), any(), any(), any()) }
        coVerifyOnce { serviceSpy.getChannel(any()) }
        coVerifyOnce { channelFirestoreService.getChannel(any(), any()) }
    }

    @OptIn(FirestoreContextUsage::class)
    @Test
    fun `#addStaff returns channel and add new staff as owner when channel staff is empty`() = runBlocking {
        val channelDocument = channelDocument.copy(staff = mutableMapOf())

        val expectedMessage = buildChannelMessage(
            type = MessageType.ADD_PARTICIPANT,
            staffOrPersonId = staffId,
            content = "${staffChannel02.name} ${MessageType.ADD_PARTICIPANT.description}"
        )

        val expectedChannel = channelDocument.copy(
            staff = channelDocument.staff.plus(staffChannel02.id to staffChannel02).toMutableMap(),
            staffHistory = channelDocument.staffHistory.minus(staffChannel02.id).toMutableMap(),
            staffIds = channelDocument.staffIds!!.plus(staffChannel02.id)
        )

        coEvery { serviceSpy.getChannel(channelId) } returns channelDocument.success()

        coEvery {
            serviceSpy.updateFields(
                channelId,
                mapOf(
                    ChannelDocument::staff to mapOf(staffChannel02.id to staffChannel02.copy(owner = true)),
                    ChannelDocument::staffHistory to mapOf(staffChannel02.id to FieldValue.delete()),
                    ChannelDocument::staffIds to FieldValue.arrayUnion(staffChannel02.id),
                    ChannelDocument::onCallAction to FieldValue.delete(),
                )
            )
        } returns channelId.success()
        coEvery {
            messageFirestoreService.add(channelId,
                match {
                    it.type == expectedMessage.type
                            && it.aliceId == expectedMessage.aliceId
                            && it.content == expectedMessage.content
                })
        } returns channelId.success()
        coEvery {
            channelNotificationService.notify(expectedChannel, ChannelChangeAction.ADD_STAFF)
        } returns mockk()

        val result = serviceSpy.addStaff(channelId, staff02, staffId, false)
        assertThat(result).isSuccessWithData(channelId)

        coVerifyOnce { serviceSpy.span(any(), any(), any()) }
        coVerifyOnce { serviceSpy.updateFields(any(), any()) }
        coVerifyOnce { serviceSpy.addStaff(any(), any(), any(), any()) }
        coVerifyOnce { messageFirestoreService.add(any(), any()) }
        coVerifyOnce { serviceSpy.getChannel(any()) }
        coVerifyOnce { channelNotificationService.notify(any<ChannelDocument>(), any(), any(), any()) }
    }

    @OptIn(FirestoreContextUsage::class)
    @Test
    fun `#addStaff returns error when chat is no empty`() = runBlocking {
        coEvery { serviceSpy.getChannel(channelId) } returns channelDocument.copy(kind = ChannelKind.CHAT).success()

        val result = serviceSpy.addStaff(channelId, staff02, staff02.id.toString(), false)
        assertThat(result).isFailureOfType(IllegalArgumentException::class)

        coVerifyOnce { serviceSpy.span(any(), any(), any()) }
        coVerifyOnce { serviceSpy.addStaff(any(), any(), any(), any(), any()) }
        coVerifyOnce { serviceSpy.getChannel(any()) }
    }

    @OptIn(FirestoreContextUsage::class)
    @Test
    fun `#addStaff returns channel and add new staff as owner when channel staff is empty and is a VIRTUAL_CLINIC and staff is physician`() =
        runBlocking {
            val channelDocument = channelDocument.copy(staff = mutableMapOf())

            val expectedMessage = buildChannelMessage(
                type = MessageType.ADD_PARTICIPANT,
                staffOrPersonId = staffId,
                content = "${staffChannel02.name} ${MessageType.ADD_PARTICIPANT.description}"
            )

            val expectedChannel = channelDocument.copy(
                staff = channelDocument.staff.plus(staffChannel02.id to staffChannel02).toMutableMap(),
                staffHistory = channelDocument.staffHistory.minus(staffChannel02.id).toMutableMap(),
                staffIds = channelDocument.staffIds!!.plus(staffChannel02.id)
            )

            coEvery { serviceSpy.getChannel(channelId) } returns channelDocument.success()

            coEvery {
                serviceSpy.updateFields(
                    channelId,
                    mapOf(
                        ChannelDocument::staff to mapOf(staffChannel02.id to staffChannel02.copy(owner = true)),
                        ChannelDocument::staffHistory to mapOf(staffChannel02.id to FieldValue.delete()),
                        ChannelDocument::staffIds to FieldValue.arrayUnion(staffChannel02.id),
                        ChannelDocument::onCallAction to FieldValue.delete(),
                    )
                )
            } returns channelId.success()
            coEvery {
                messageFirestoreService.add(channelId,
                    match {
                        it.type == expectedMessage.type
                                && it.aliceId == expectedMessage.aliceId
                                && it.content == expectedMessage.content
                    })
            } returns channelId.success()
            coEvery {
                channelNotificationService.notify(expectedChannel, ChannelChangeAction.ADD_STAFF)
            } returns mockk()

            val result = serviceSpy.addStaff(channelId, staff02, staffId, false)
            assertThat(result).isSuccessWithData(channelId)

            coVerifyOnce { serviceSpy.span(any(), any(), any()) }
            coVerifyOnce { serviceSpy.updateFields(any(), any()) }
            coVerifyOnce { serviceSpy.addStaff(any(), any(), any(), any()) }
            coVerifyOnce { messageFirestoreService.add(any(), any()) }
            coVerifyOnce { serviceSpy.getChannel(any()) }
            coVerifyOnce { channelNotificationService.notify(any<ChannelDocument>(), any(), any(), any()) }
        }

    @OptIn(FirestoreContextUsage::class)
    @Test
    fun `#addStaff returns error when chat is VIRTUAL_CLINIC and staff not is a physician`() = runBlocking {
        val staff02 = staff02.copy(role = SCREENING_NURSE)
        coEvery { serviceSpy.getChannel(channelId) } returns channelDocument.copy(kind = ChannelKind.CHAT).success()

        val result = serviceSpy.addStaff(channelId, staff02, staff02.id.toString(), false)
        assertThat(result).isFailureOfType(IllegalArgumentException::class)

        coVerifyOnce { serviceSpy.span(any(), any(), any()) }
        coVerifyOnce { serviceSpy.addStaff(any(), any(), any(), any(), any()) }
        coVerifyOnce { serviceSpy.getChannel(any()) }
    }

    @OptIn(FirestoreContextUsage::class)
    @Test
    fun `#addStaff returns error when chat is VIRTUAL_CLINIC and staff is a physician but chat has another physician`() =
        runBlocking {
            coEvery { serviceSpy.getChannel(channelId) } returns channelDocument.copy(kind = ChannelKind.CHAT).success()

            val result = serviceSpy.addStaff(channelId, staff02, staff02.id.toString(), false)
            assertThat(result).isFailureOfType(IllegalArgumentException::class)

            coVerifyOnce { serviceSpy.span(any(), any(), any()) }
            coVerifyOnce { serviceSpy.addStaff(any(), any(), any(), any(), any()) }
            coVerifyOnce { serviceSpy.getChannel(any()) }
        }

    @OptIn(FirestoreContextUsage::class)
    @Test
    fun `#addStaff returns channel and add new staff as owner when channel staff has a nurse and is a VIRTUAL_CLINIC and staff is physician`() =
        runBlocking {
            val channelDocument = channelDocument

            val expectedMessage = buildChannelMessage(
                type = MessageType.ADD_PARTICIPANT,
                staffOrPersonId = staffId,
                content = "${staffChannel02.name} ${MessageType.ADD_PARTICIPANT.description}"
            )

            val expectedChannel = channelDocument.copy(
                staff = channelDocument.staff.plus(staffChannel02.id to staffChannel02).toMutableMap(),
                staffHistory = channelDocument.staffHistory.minus(staffChannel02.id).toMutableMap(),
                staffIds = channelDocument.staffIds!!.plus(staffChannel02.id)
            )

            coEvery { serviceSpy.getChannel(channelId) } returns channelDocument.success()

            coEvery {
                serviceSpy.updateFields(
                    channelId,
                    mapOf(
                        ChannelDocument::staff to mapOf(staffChannel02.id to staffChannel02.copy(owner = true)),
                        ChannelDocument::staffHistory to mapOf(staffChannel02.id to FieldValue.delete()),
                        ChannelDocument::staffIds to FieldValue.arrayUnion(staffChannel02.id),
                        ChannelDocument::onCallAction to FieldValue.delete(),
                    )
                )
            } returns channelId.success()
            coEvery {
                messageFirestoreService.add(channelId,
                    match {
                        it.type == expectedMessage.type
                                && it.aliceId == expectedMessage.aliceId
                                && it.content == expectedMessage.content
                    })
            } returns channelId.success()
            coEvery {
                channelNotificationService.notify(expectedChannel, ChannelChangeAction.ADD_STAFF)
            } returns mockk()

            val result = serviceSpy.addStaff(channelId, staff02, staffId, false)
            assertThat(result).isSuccessWithData(channelId)

            coVerifyOnce { serviceSpy.span(any(), any(), any()) }
            coVerifyOnce { serviceSpy.updateFields(any(), any()) }
            coVerifyOnce { serviceSpy.addStaff(any(), any(), any(), any()) }
            coVerifyOnce { messageFirestoreService.add(any(), any()) }
            coVerifyOnce { serviceSpy.getChannel(any()) }
            coVerifyOnce { channelNotificationService.notify(any<ChannelDocument>(), any(), any(), any()) }
        }

    @OptIn(FirestoreContextUsage::class)
    @Test
    fun `#addStaff returns success when chat is no empty but force add`() = runBlocking {
        val channelDocument = channelDocument.copy(kind = ChannelKind.CHAT)
        val expectedChannel = channelDocument.copy(
            staff = channelDocument.staff.plus(staffChannel02.id to staffChannel02).toMutableMap(),
            staffHistory = channelDocument.staffHistory.minus(staffChannel02.id).toMutableMap(),
            staffIds = channelDocument.staffIds!!.plus(staffChannel02.id)
        )

        coEvery { serviceSpy.getChannel(channelId) } returns channelDocument.success()
        coEvery {
            serviceSpy.updateFields(
                channelId,
                mapOf(
                    ChannelDocument::staff to mapOf(staffChannel02.id to staffChannel02.copy(owner = true)),
                    ChannelDocument::staffHistory to mapOf(staffChannel02.id to FieldValue.delete()),
                    ChannelDocument::staffIds to FieldValue.arrayUnion(staffChannel02.id),
                    ChannelDocument::onCallAction to FieldValue.delete(),
                )
            )
        } returns channelId.success()
        coEvery {
            channelNotificationService.notify(expectedChannel, ChannelChangeAction.ADD_STAFF)
        } returns mockk()

        val result = serviceSpy.addStaff(channelId, staff02, staff02.id.toString(), false, force = true)
        assertThat(result).isSuccessWithData(channelId)

        coVerifyOnce { serviceSpy.span(any(), any(), any()) }
        coVerifyOnce { serviceSpy.updateFields(any(), any()) }
        coVerifyOnce { serviceSpy.addStaff(any(), any(), any(), any(), any()) }
        coVerifyOnce { serviceSpy.getChannel(any()) }
        coVerifyOnce { channelNotificationService.notify(any<ChannelDocument>(), any(), any(), any()) }
        coVerify { messageFirestoreService wasNot called }
    }

    @OptIn(FirestoreContextUsage::class)
    @Test
    fun `#addStaff returns channel when channel have no staff`() = runBlocking {
        val channelDocument = channelDocument.copy(
            kind = ChannelKind.CHANNEL,
            category = ChannelCategory.ASSISTANCE,
            subCategory = ChannelSubCategory.MULTI,
            subCategoryClassifier = ChannelSubCategoryClassifier.PSYCHOLOGY,
            staff = mutableMapOf(),
            staffIds = emptyList()
        )

        val expectedChannel = channelDocument.copy(
            staff = mutableMapOf(channelStaffOwner.id to channelStaffOwner.copy(roles = emptyList())),
            staffIds = listOf(channelStaffOwner.id)
        )

        coEvery { serviceSpy.getChannel(channelId) } returns channelDocument.success()

        coEvery {
            serviceSpy.updateFields(
                channelId,
                mapOf(
                    ChannelDocument::staff to mapOf(
                        channelStaffOwner.id to channelStaffOwner.copy(
                            owner = true,
                            roles = emptyList()
                        )
                    ),
                    ChannelDocument::staffHistory to mapOf(channelStaffOwner.id to FieldValue.delete()),
                    ChannelDocument::staffIds to FieldValue.arrayUnion(channelStaffOwner.id),
                    ChannelDocument::onCallAction to FieldValue.delete(),
                )
            )
        } returns channelId.success()
        coEvery {
            channelNotificationService.notify(expectedChannel, ChannelChangeAction.ADD_STAFF)
        } returns mockk()

        val result = serviceSpy.addStaff(channelId, staff01, staffId, false)
        assertThat(result).isSuccessWithData(channelId)

        coVerifyOnce { serviceSpy.span(any(), any(), any()) }
        coVerifyOnce { serviceSpy.updateFields(any(), any()) }
        coVerifyOnce { serviceSpy.addStaff(any(), any(), any(), any()) }
        coVerifyOnce { serviceSpy.getChannel(any()) }
        coVerifyNone { messageFirestoreService.add(any(), any()) }
        coVerifyOnce { channelNotificationService.notify(any<ChannelDocument>(), any(), any(), any()) }
        coVerify { messageFirestoreService wasNot called }
    }

    @OptIn(FirestoreContextUsage::class)
    @Test
    fun `#toggleQuestionnaireControls returns channel and update hideMemberInput to true`() = runBlocking {
        val service = spyk(service)

        val expectedParamsToUpdate = mapOf<KProperty1<ChannelDocument, Any?>, Any?>(
            ChannelDocument::hasQuestionnaireInProgress to true,
            ChannelDocument::hideMemberInput to true
        )

        coEvery { service.updateFields(channelId, expectedParamsToUpdate) } returns channelId.success()

        val actual = service.toggleQuestionnaireControls(channelId, true)
        assertThat(actual).isSuccessWithData(channelId)

        coVerify { channelNotificationService wasNot called }
    }

    private fun buildChannelMessage(
        type: MessageType,
        staffOrPersonId: String,
        content: String = type.description,
        appVersion: String? = null,
        additionalInfo: Map<String, Any> = mapOf(),
        isAuto: Boolean? = false,
        isVisible: Boolean? = true,
        source: String? = null,
    ): MessageDocument =
        MessageDocument(
            aliceId = staffOrPersonId,
            userId = staffOrPersonId,
            content = content,
            type = type,
            appVersion = appVersion,
            isAuto = isAuto,
            isVisible = isVisible,
            additionalInfo = additionalInfo,
            source = source
        )

    @Test
    fun `#getChannel returns channel document`() = runBlocking {
        coEvery { channelFirestoreService.getChannel(channelId) } returns channelDocument.success()

        val result = service.getChannel(channelId)
        assertThat(result).isSuccessWithData(channelDocument)

        coVerifyOnce { channelFirestoreService.getChannel(any()) }
        coVerify { channelNotificationService wasNot called }
        coVerify { messageFirestoreService wasNot called }
    }

    @Test
    fun `#getChannel returns specific exception when not found channel by id`() = runBlocking {
        coEvery { channelFirestoreService.getChannel(channelId) } returns NotFoundException().failure()

        val result = service.getChannel(channelId)
        assertThat(result).isFailureOfType(NotFoundException::class)

        coVerifyOnce { channelFirestoreService.getChannel(any()) }
        coVerify { channelNotificationService wasNot called }
        coVerify { messageFirestoreService wasNot called }
    }

    @Test
    fun `#getChannelsById should return list of channels filtered by its ids`() = runBlocking {
        val expected = listOf(channelDocument)
        val channelIds = listOf(channelDocument.id!!)

        coEvery {
            channelFirestoreService.getChannelsByIds(channelIds)
        } returns expected.success()

        val result = service.getChannelsById(channelIds)

        assertThat(result).isSuccessWithData(expected)

        coVerifyOnce { channelFirestoreService.getChannelsByIds(any()) }
    }

    @Test
    fun `#find returns channels found by callback`() = runBlocking {
        val findFunction: suspend (CollectionReference) -> Query = { collectionReference ->
            collectionReference.whereEqualTo("personId", personId.toString())
                .whereIn("status", listOf("ACTIVE", "INACTIVE"))
        }

        val expected = listOf(channelDocument)

        coEvery { channelFirestoreService.find(findFunction) } returns listOf(channelDocument).success()

        val result = service.find(findFunction)
        assertThat(result).isSuccessWithData(expected)

        coVerifyOnce { channelFirestoreService.find(any()) }
    }

    @Test
    fun `#findChannels returns channels found by personId`() = runBlocking {
        val functionSlot = slot<suspend (CollectionReference) -> Query>()

        every { collectionReference.whereEqualTo("personId", personId.toString()) } returns collectionReference
        coEvery { channelFirestoreService.find(capture(functionSlot)) } returns listOf(channelDocument).success()

        val result = service.findChannels(
            personId = personId.toString()
        )
        assertThat(result).isSuccessWithData(listOf(channelDocument))

        functionSlot.captured.invoke(collectionReference)

        verifyOnce { collectionReference.whereEqualTo(any<String>(), any()) }
        coVerifyOnce { channelFirestoreService.find(any()) }
        coVerify { channelNotificationService wasNot called }
        coVerify { messageFirestoreService wasNot called }
        coVerify { serviceSpy wasNot called }
    }

    @Test
    fun `#findChannels returns channels found by personId and status ACTIVE`() = runBlocking {
        val functionSlot = slot<suspend (CollectionReference) -> Query>()

        every { collectionReference.whereEqualTo("personId", personId.toString()) } returns collectionReference
        every { collectionReference.whereEqualTo("status", "ACTIVE") } returns collectionReference
        coEvery { channelFirestoreService.find(capture(functionSlot)) } returns listOf(channelDocument).success()

        val result = service.findChannels(
            personId = personId.toString(),
            onlyActives = true
        )
        assertThat(result).isSuccessWithData(listOf(channelDocument))

        functionSlot.captured.invoke(collectionReference)

        verify(exactly = 2) { collectionReference.whereEqualTo(any<String>(), any()) }
        coVerifyOnce { channelFirestoreService.find(any()) }
        coVerify { channelNotificationService wasNot called }
        coVerify { messageFirestoreService wasNot called }
        coVerify { serviceSpy wasNot called }
    }

    @Test
    fun `#findChannels returns channels found by personId, category and subcategories`() = runBlocking {
        val functionSlot = slot<suspend (CollectionReference) -> Query>()

        every { collectionReference.whereEqualTo("personId", personId.toString()) } returns collectionReference
        every { collectionReference.whereEqualTo("category", "ASSISTANCE") } returns collectionReference
        every {
            collectionReference.whereIn(
                "subCategory",
                listOf(ChannelSubCategory.ACUTE.name, ChannelSubCategory.SCREENING.name)
            )
        } returns collectionReference
        coEvery { channelFirestoreService.find(capture(functionSlot)) } returns listOf(channelDocument).success()

        val result = service.findChannels(
            personId = personId.toString(),
            category = ChannelCategory.ASSISTANCE,
            subCategories = listOf(ChannelSubCategory.ACUTE, ChannelSubCategory.SCREENING)
        )
        assertThat(result).isSuccessWithData(listOf(channelDocument))

        functionSlot.captured.invoke(collectionReference)

        verify(exactly = 2) { collectionReference.whereEqualTo(any<String>(), any()) }
        verifyOnce { collectionReference.whereIn(any<String>(), any()) }
        coVerifyOnce { channelFirestoreService.find(any()) }
        coVerify { channelNotificationService wasNot called }
        coVerify { messageFirestoreService wasNot called }
        coVerify { serviceSpy wasNot called }
    }

    @Test
    fun `#findChannels returns channels found by personId and createdAfter`() = runBlocking {
        val functionSlot = slot<suspend (CollectionReference) -> Query>()
        val createdAfter = LocalDateTime.now()

        every { collectionReference.whereEqualTo("personId", personId.toString()) } returns collectionReference
        every {
            collectionReference.whereGreaterThan(
                "createdAt",
                createdAfter.toTimestamp()
            )
        } returns collectionReference
        coEvery { channelFirestoreService.find(capture(functionSlot)) } returns listOf(channelDocument).success()


        val result = service.findChannels(
            personId = personId.toString(),
            createdAfter = createdAfter
        )
        assertThat(result).isSuccessWithData(listOf(channelDocument))

        functionSlot.captured.invoke(collectionReference)

        verifyOnce { collectionReference.whereEqualTo(any<String>(), any()) }
        verifyOnce { collectionReference.whereGreaterThan(any<String>(), any()) }
        coVerifyOnce { channelFirestoreService.find(any()) }
        coVerify { channelNotificationService wasNot called }
        coVerify { messageFirestoreService wasNot called }
        coVerify { serviceSpy wasNot called }
    }

    @Test
    fun `#findAdministrativeChannels returns channels found by filters`() = runBlocking {
        val functionSlot = slot<suspend (CollectionReference) -> Query>()
        val lastTimeMessage24h = LocalDateTime.now().minusHours(24L)
        val lastTimeMessage72h = LocalDateTime.now().minusHours(72L)


        every {
            collectionReference.whereEqualTo(
                "category",
                ChannelCategory.ADMINISTRATIVE
            )
        } returns collectionReference
        every { collectionReference.whereEqualTo("status", ChannelStatus.ACTIVE) } returns collectionReference
        every {
            collectionReference.whereGreaterThan(
                "timeLastMessage",
                lastTimeMessage72h.toTimestamp()
            )
        } returns collectionReference
        every {
            collectionReference.whereLessThan(
                "timeLastMessage",
                lastTimeMessage24h.toTimestamp()
            )
        } returns collectionReference
        every { collectionReference.whereEqualTo("kind", ChannelKind.CHANNEL) } returns collectionReference

        coEvery { channelFirestoreService.find(capture(functionSlot)) } returns listOf(channelDocument).success()

        val result = service.findAdministrativeChannels(
            lastTimeMessage24h,
            lastTimeMessage72h,
            true
        )
        assertThat(result).isSuccessWithData(listOf(channelDocument))

        functionSlot.captured.invoke(collectionReference)

        verify(exactly = 3) { collectionReference.whereEqualTo(any<String>(), any()) }
        coVerifyOnce { collectionReference.whereGreaterThan(any<String>(), any()) }
        coVerifyOnce { channelFirestoreService.find(any()) }
        coVerify { channelNotificationService wasNot called }
        coVerify { messageFirestoreService wasNot called }
        coVerify { serviceSpy wasNot called }
    }

    @OptIn(FirestoreContextUsage::class)
    @Test
    fun `#giveOwnership returns channel id and change owner`() = runBlocking {
        val requesterId = anotherStaffId
        val channel = channelDocument.copy(
            staff = mutableMapOf(
                staffId to channelStaffOwner.copy(owner = false),
                requesterId to staffChannel02.copy(owner = true)
            )
        )

        val expectedChannel = channel.copy(
            staff = mutableMapOf(
                staffId to channelStaffOwner.copy(owner = true),
                requesterId to staffChannel02.copy(owner = false)
            )
        )

        coEvery { serviceSpy.getChannel(channelId) } returns channel.success()
        coEvery {
            channelFirestoreService.updateFields(
                channelId,
                mapOf<KProperty1<ChannelDocument, Any?>, Any?>(
                    ChannelDocument::staff to mapOf(
                        staffId to mapOf(ChannelStaffInfo::owner.name to true),
                        requesterId to mapOf(ChannelStaffInfo::owner.name to false)
                    )
                )
            )
        } returns channelId.success()
        coEvery {
            channelNotificationService.notify(
                expectedChannel,
                ChannelChangeAction.GIVE_OWNERSHIP
            )
        } returns mockk()

        val result = serviceSpy.giveOwnership(channelId, staffId, requesterId)
        assertThat(result).isSuccessWithData(channelId)

        coVerifyOnce { serviceSpy.giveOwnership(any(), any(), any()) }
        coVerifyOnce { serviceSpy.getChannel(any()) }
        coVerifyOnce { channelFirestoreService.updateFields(any(), any()) }
        coVerifyOnce { channelNotificationService.notify(any<ChannelDocument>(), any(), any(), any()) }
        coVerify { messageFirestoreService wasNot called }
    }

    @OptIn(FirestoreContextUsage::class)
    @Test
    fun `#giveOwnership returns error when the staff does not participate in the channel`() = runBlocking {
        val requesterId = anotherStaffId
        val channel = channelDocument.copy(
            staff = mutableMapOf(
                requesterId to staffChannel02.copy(owner = true)
            )
        )

        coEvery { serviceSpy.getChannel(channelId) } returns channel.success()

        val result = serviceSpy.giveOwnership(channelId, staffId, requesterId)
        assertThat(result).isFailureOfType(InvalidArgumentException::class)

        coVerifyOnce { serviceSpy.giveOwnership(any(), any(), any()) }
        coVerifyOnce { serviceSpy.getChannel(any()) }
        coVerify { channelFirestoreService wasNot called }
        coVerify { channelNotificationService wasNot called }
        coVerify { messageFirestoreService wasNot called }
    }

    @OptIn(FirestoreContextUsage::class)
    @Test
    fun `#giveOwnership returns error when the requester does not participate in the channel`() = runBlocking {
        val requesterId = anotherStaffId
        val channel = channelDocument.copy(
            staff = mutableMapOf(
                staffId to channelStaffOwner.copy(owner = true),
            )
        )

        coEvery { serviceSpy.getChannel(channelId) } returns channel.success()

        val result = serviceSpy.giveOwnership(channelId, staffId, requesterId)
        assertThat(result).isFailureOfType(InvalidArgumentException::class)

        coVerifyOnce { serviceSpy.giveOwnership(any(), any(), any()) }
        coVerifyOnce { serviceSpy.getChannel(any()) }
        coVerify { channelFirestoreService wasNot called }
        coVerify { channelNotificationService wasNot called }
        coVerify { messageFirestoreService wasNot called }
    }

    @OptIn(FirestoreContextUsage::class)
    @Test
    fun `#giveOwnership returns error when the requester not is the owner`() = runBlocking {
        val requesterId = anotherStaffId
        val channel = channelDocument.copy(
            staff = mutableMapOf(
                staffId to channelStaffOwner.copy(owner = false),
                requesterId to staffChannel02.copy(owner = false)
            )
        )

        coEvery { serviceSpy.getChannel(channelId) } returns channel.success()

        val result = serviceSpy.giveOwnership(channelId, staffId, requesterId)
        assertThat(result).isFailureOfType(AccessForbiddenException::class)

        coVerifyOnce { serviceSpy.giveOwnership(any(), any(), any()) }
        coVerifyOnce { serviceSpy.getChannel(any()) }
        coVerify { channelFirestoreService wasNot called }
        coVerify { channelNotificationService wasNot called }
        coVerify { messageFirestoreService wasNot called }
    }

    @OptIn(FirestoreContextUsage::class)
    @Test
    fun `#removeStaff (by channel) returns channel id and remove staff of channel`() = runBlocking {
        val requesterId = staffId
        val staffToRemove = staffChannel02.copy(owner = false)
        val channel = channelDocument.copy(
            staff = mutableMapOf(
                requesterId to channelStaffOwner,
                anotherStaffId to staffToRemove
            )
        )

        val expectedParamsToUpdate = mapOf<KProperty1<ChannelDocument, Any?>, Any?>(
            ChannelDocument::staff to mapOf(anotherStaffId to FieldValue.delete()),
            ChannelDocument::staffHistory to mapOf(anotherStaffId to staffToRemove),
            ChannelDocument::staffIds to arrayRemove(anotherStaffId),
            ChannelDocument::typing to arrayRemove(anotherStaffId),
            ChannelDocument::privateTyping to arrayRemove(anotherStaffId),
        )

        val timestamp = Timestamp.now()

        val expectedMessage = MessageDocument(
            aliceId = requesterId,
            userId = requesterId,
            content = "${staffToRemove.name} ${MessageType.REMOVE_PARTICIPANT.description}",
            type = MessageType.REMOVE_PARTICIPANT,
            source = null,
            createdAt = timestamp
        )

        val expectedChannel = channel.copy(
            staff = channel.staff.minus(anotherStaffId).toMutableMap(),
            staffHistory = channel.staffHistory.plus(anotherStaffId to staffToRemove).toMutableMap(),
            staffIds = channel.staffIds?.minus(anotherStaffId),
            typing = channel.typing?.minus(anotherStaffId),
            privateTyping = channel.privateTyping?.minus(anotherStaffId)
        )

        mockkStatic(Timestamp::class) {
            every { Timestamp.now() } returns timestamp

            coEvery {
                channelFirestoreService.updateFields(channelId, expectedParamsToUpdate)
            } returns channelId.success()

            coEvery {
                messageFirestoreService.add(channelId, expectedMessage)
            } returns channelId.success()

            coEvery {
                channelNotificationService.notify(
                    expectedChannel,
                    ChannelChangeAction.REMOVE_STAFF
                )
            } returns mockk()

            val result = serviceSpy.removeStaff(channel, anotherStaffId, requesterId)
            assertThat(result).isSuccessWithData(channelId)

            coVerifyOnce { serviceSpy.span(any(), any(), any()) }
            coVerifyOnce { serviceSpy.removeStaff(any<ChannelDocument>(), any(), any(), any(), any()) }
            coVerifyOnce { channelFirestoreService.updateFields(any(), any()) }
            coVerifyOnce { messageFirestoreService.add(any(), any()) }
            coVerifyOnce { channelNotificationService.notify(any<ChannelDocument>(), any(), any(), any()) }
        }
    }

    @OptIn(FirestoreContextUsage::class)
    @Test
    fun `#removeStaff (by channel) returns channel id and remove staff of channel when channel have only one staff and checkSize is false`() =
        runBlocking {
            val requesterId = staffId
            val channel = channelDocument.copy(
                staff = mutableMapOf(
                    requesterId to channelStaffOwner,
                )
            )

            val expectedParamsToUpdate = mapOf<KProperty1<ChannelDocument, Any?>, Any?>(
                ChannelDocument::staff to mapOf(staffId to FieldValue.delete()),
                ChannelDocument::staffHistory to mapOf(staffId to channelStaffOwner),
                ChannelDocument::staffIds to arrayRemove(staffId),
                ChannelDocument::typing to arrayRemove(staffId),
                ChannelDocument::privateTyping to arrayRemove(staffId)
            )

            val timestamp = Timestamp.now()

            val expectedMessage = MessageDocument(
                aliceId = requesterId,
                userId = requesterId,
                content = "${channelStaffOwner.name} ${MessageType.REMOVE_PARTICIPANT.description}",
                type = MessageType.REMOVE_PARTICIPANT,
                source = null,
                createdAt = timestamp
            )

            val expectedChannel = channel.copy(
                staff = mutableMapOf(),
                staffHistory = mutableMapOf(requesterId to channelStaffOwner),
                staffIds = emptyList()
            )

            mockkStatic(Timestamp::class) {
                every { Timestamp.now() } returns timestamp

                coEvery {
                    channelFirestoreService.updateFields(channelId, expectedParamsToUpdate)
                } returns channelId.success()

                coEvery {
                    messageFirestoreService.add(channelId, expectedMessage)
                } returns channelId.success()

                coEvery {
                    channelNotificationService.notify(
                        expectedChannel,
                        ChannelChangeAction.REMOVE_STAFF
                    )
                } returns mockk()

                val result = serviceSpy.removeStaff(
                    channel, requesterId, requesterId, checkOwner = false, checkSize = false
                )
                assertThat(result).isSuccessWithData(channelId)

                coVerifyOnce { serviceSpy.span(any(), any(), any()) }
                coVerifyOnce { serviceSpy.removeStaff(any(), any(), any(), any(), any(), any()) }
                coVerifyOnce { channelFirestoreService.updateFields(any(), any()) }
                coVerifyOnce { messageFirestoreService.add(any(), any()) }
                coVerifyOnce { channelNotificationService.notify(any<ChannelDocument>(), any(), any(), any()) }
            }
        }

    @OptIn(FirestoreContextUsage::class)
    @Test
    fun `#removeStaff returns channel id and remove staff of channel`() = runBlocking {
        val requesterId = staffId
        val staffToRemove = staffChannel02.copy(owner = false)
        val channel = channelDocument.copy(
            staff = mutableMapOf(
                requesterId to channelStaffOwner,
                anotherStaffId to staffToRemove
            )
        )

        val expectedParamsToUpdate = mapOf<KProperty1<ChannelDocument, Any?>, Any?>(
            ChannelDocument::staff to mapOf(anotherStaffId to FieldValue.delete()),
            ChannelDocument::staffHistory to mapOf(anotherStaffId to staffToRemove),
            ChannelDocument::staffIds to arrayRemove(anotherStaffId),
            ChannelDocument::typing to arrayRemove(anotherStaffId),
            ChannelDocument::privateTyping to arrayRemove(anotherStaffId),
        )

        val timestamp = Timestamp.now()

        val expectedMessage = MessageDocument(
            aliceId = requesterId,
            userId = requesterId,
            content = "${staffToRemove.name} ${MessageType.REMOVE_PARTICIPANT.description}",
            type = MessageType.REMOVE_PARTICIPANT,
            source = null,
            createdAt = timestamp
        )

        val expectedChannel = channel.copy(
            staff = channel.staff.minus(anotherStaffId).toMutableMap(),
            staffHistory = channel.staffHistory.plus(anotherStaffId to staffToRemove).toMutableMap(),
            staffIds = channel.staffIds?.minus(anotherStaffId),
            typing = channel.typing?.minus(anotherStaffId),
            privateTyping = channel.privateTyping?.minus(anotherStaffId)
        )

        mockkStatic(Timestamp::class) {
            every { Timestamp.now() } returns timestamp

            coEvery { serviceSpy.getChannel(channelId) } returns channel.success()
            coEvery {
                channelFirestoreService.updateFields(channelId, expectedParamsToUpdate)
            } returns channelId.success()

            coEvery {
                messageFirestoreService.add(channelId, expectedMessage)
            } returns channelId.success()

            coEvery {
                channelNotificationService.notify(
                    expectedChannel,
                    ChannelChangeAction.REMOVE_STAFF
                )
            } returns mockk()

            val result = serviceSpy.removeStaff(channelId, anotherStaffId, requesterId)
            assertThat(result).isSuccessWithData(channelId)

            coVerifyOnce { serviceSpy.span(any(), any(), any()) }
            coVerifyOnce { serviceSpy.removeStaff(any<String>(), any(), any(), any(), any()) }
            coVerifyOnce { serviceSpy.removeStaff(any<ChannelDocument>(), any(), any(), any(), any()) }
            coVerifyOnce { serviceSpy.getChannel(any()) }
            coVerifyOnce { channelFirestoreService.updateFields(any(), any()) }
            coVerifyOnce { messageFirestoreService.add(any(), any()) }
            coVerifyOnce { channelNotificationService.notify(any<ChannelDocument>(), any(), any(), any()) }
        }
    }

    @OptIn(FirestoreContextUsage::class)
    @Test
    fun `#removeStaff returns channel id and remove herself of channel`() = runBlocking {
        val staffToRemove = staffChannel02.copy(owner = false)
        val channel = channelDocument.copy(
            staff = mutableMapOf(
                staffId to channelStaffOwner,
                anotherStaffId to staffToRemove
            )
        )

        val expectedParamsToUpdate = mapOf<KProperty1<ChannelDocument, Any?>, Any?>(
            ChannelDocument::staff to mapOf(anotherStaffId to FieldValue.delete()),
            ChannelDocument::staffHistory to mapOf(anotherStaffId to staffToRemove),
            ChannelDocument::staffIds to arrayRemove(anotherStaffId),
            ChannelDocument::typing to arrayRemove(anotherStaffId),
            ChannelDocument::privateTyping to arrayRemove(anotherStaffId),
        )

        val timestamp = Timestamp.now()

        val expectedMessage = MessageDocument(
            aliceId = anotherStaffId,
            userId = anotherStaffId,
            content = "${staffToRemove.name} ${MessageType.REMOVE_PARTICIPANT.description}",
            type = MessageType.REMOVE_PARTICIPANT,
            source = null,
            createdAt = timestamp
        )

        val expectedChannel = channel.copy(
            staff = channel.staff.minus(anotherStaffId).toMutableMap(),
            staffHistory = channel.staffHistory.plus(anotherStaffId to staffToRemove).toMutableMap(),
            staffIds = channel.staffIds?.minus(anotherStaffId),
            typing = channel.typing?.minus(anotherStaffId),
            privateTyping = channel.privateTyping?.minus(anotherStaffId)
        )

        mockkStatic(Timestamp::class) {
            every { Timestamp.now() } returns timestamp

            coEvery { serviceSpy.getChannel(channelId) } returns channel.success()
            coEvery {
                channelFirestoreService.updateFields(channelId, expectedParamsToUpdate)
            } returns channelId.success()

            coEvery {
                messageFirestoreService.add(channelId, expectedMessage)
            } returns channelId.success()

            coEvery {
                channelNotificationService.notify(
                    expectedChannel,
                    ChannelChangeAction.REMOVE_STAFF
                )
            } returns mockk()

            val result = serviceSpy.removeStaff(channelId, anotherStaffId, anotherStaffId)
            assertThat(result).isSuccessWithData(channelId)

            coVerifyOnce { serviceSpy.span(any(), any(), any()) }
            coVerifyOnce { serviceSpy.removeStaff(any<String>(), any(), any(), any(), any()) }
            coVerifyOnce { serviceSpy.removeStaff(any<ChannelDocument>(), any(), any(), any(), any()) }
            coVerifyOnce { serviceSpy.getChannel(any()) }
            coVerifyOnce { channelFirestoreService.updateFields(any(), any()) }
            coVerifyOnce { messageFirestoreService.add(any(), any()) }
            coVerifyOnce { channelNotificationService.notify(any<ChannelDocument>(), any(), any(), any()) }
        }
    }

    @OptIn(FirestoreContextUsage::class)
    @Test
    fun `#removeStaff returns error when get channel return error`() = runBlocking {
        coEvery { serviceSpy.getChannel(channelId) } returns Exception().failure()

        val result = serviceSpy.removeStaff(channelId, staffId, staffId)
        assertThat(result).isFailureOfType(Exception::class)

        coVerifyOnce { serviceSpy.removeStaff(any<String>(), any(), any(), any(), any()) }
        coVerifyOnce { serviceSpy.getChannel(any()) }
        coVerify { channelFirestoreService wasNot called }
        coVerify { messageFirestoreService wasNot called }
        coVerify { channelNotificationService wasNot called }
    }

    @OptIn(FirestoreContextUsage::class)
    @Test
    fun `#removeStaff returns channel id when the staff does not participate in the channel`() = runBlocking {
        val channel = channelDocument.copy(
            staff = mutableMapOf(
                staffId to channelStaffOwner,
                anotherStaffId to channelStaffOwner
            )
        )

        coEvery { serviceSpy.getChannel(channelId) } returns channel.success()

        val result = serviceSpy.removeStaff(channelId, "xxxxx", staffId)
        assertThat(result).isSuccessWithData(channelId)

        coVerifyOnce { serviceSpy.span(any(), any(), any()) }
        coVerifyOnce { serviceSpy.removeStaff(any<String>(), any(), any(), any(), any()) }
        coVerifyOnce { serviceSpy.removeStaff(any<ChannelDocument>(), any(), any(), any(), any()) }
        coVerifyOnce { serviceSpy.getChannel(any()) }
        coVerify { channelFirestoreService wasNot called }
        coVerify { messageFirestoreService wasNot called }
        coVerify { channelNotificationService wasNot called }
    }

    @OptIn(FirestoreContextUsage::class)
    @Test
    fun `#removeStaff returns error when staff to remove is the owner`() = runBlocking {
        val requesterId = staffId
        val channel = channelDocument.copy(
            staff = mutableMapOf(
                requesterId to channelStaffOwner,
                anotherStaffId to staffChannel02.copy(owner = true)
            )
        )

        coEvery { serviceSpy.getChannel(channelId) } returns channel.success()

        val result = serviceSpy.removeStaff(channelId, anotherStaffId, requesterId)
        assertThat(result).isFailureOfType(AccessForbiddenException::class)

        coVerifyOnce { serviceSpy.span(any(), any(), any()) }
        coVerifyOnce { serviceSpy.removeStaff(any<String>(), any(), any(), any(), any()) }
        coVerifyOnce { serviceSpy.removeStaff(any<ChannelDocument>(), any(), any(), any(), any()) }
        coVerifyOnce { serviceSpy.getChannel(any()) }
        coVerify { channelFirestoreService wasNot called }
        coVerify { messageFirestoreService wasNot called }
        coVerify { channelNotificationService wasNot called }
    }

    @OptIn(FirestoreContextUsage::class)
    @Test
    fun `#removeStaff returns channel id and remove staff of channel but not send message`() = runBlocking {
        val requesterId = staffId
        val staffToRemove = staffChannel02.copy(owner = false)
        val channel = channelDocument.copy(
            staff = mutableMapOf(
                requesterId to channelStaffOwner,
                anotherStaffId to staffToRemove
            )
        )

        val expectedParamsToUpdate = mapOf<KProperty1<ChannelDocument, Any?>, Any?>(
            ChannelDocument::staff to mapOf(anotherStaffId to FieldValue.delete()),
            ChannelDocument::staffHistory to mapOf(anotherStaffId to staffToRemove),
            ChannelDocument::staffIds to arrayRemove(anotherStaffId),
            ChannelDocument::typing to arrayRemove(anotherStaffId),
            ChannelDocument::privateTyping to arrayRemove(anotherStaffId),
        )

        val timestamp = Timestamp.now()

        val expectedChannel = channel.copy(
            staff = channel.staff.minus(anotherStaffId).toMutableMap(),
            staffHistory = channel.staffHistory.plus(anotherStaffId to staffToRemove).toMutableMap(),
            staffIds = channel.staffIds?.minus(anotherStaffId),
            typing = channel.typing?.minus(anotherStaffId),
            privateTyping = channel.privateTyping?.minus(anotherStaffId)
        )

        mockkStatic(Timestamp::class) {
            every { Timestamp.now() } returns timestamp

            coEvery { serviceSpy.getChannel(channelId) } returns channel.success()
            coEvery {
                channelFirestoreService.updateFields(channelId, expectedParamsToUpdate)
            } returns channelId.success()

            coEvery {
                channelNotificationService.notify(
                    expectedChannel,
                    ChannelChangeAction.REMOVE_STAFF
                )
            } returns mockk()

            val result = serviceSpy.removeStaff(channelId, anotherStaffId, requesterId, notify = false)
            assertThat(result).isSuccessWithData(channelId)

            coVerifyOnce { serviceSpy.span(any(), any(), any()) }
            coVerifyOnce { serviceSpy.removeStaff(any<String>(), any(), any(), any(), any()) }
            coVerifyOnce { serviceSpy.removeStaff(any<ChannelDocument>(), any(), any(), any(), any()) }
            coVerifyOnce { serviceSpy.getChannel(any()) }
            coVerifyOnce { channelFirestoreService.updateFields(any(), any()) }
            coVerifyOnce { channelNotificationService.notify(any<ChannelDocument>(), any(), any(), any()) }
            coVerify { messageFirestoreService wasNot called }
        }
    }

    @OptIn(FirestoreContextUsage::class)
    @Test
    fun `#removeStaff returns channel id and remove staff of channel but not check if staff is owner`() = runBlocking {
        val requesterId = staffId
        val staffToRemove = staffChannel02.copy(owner = true)
        val channel = channelDocument.copy(
            staff = mutableMapOf(
                requesterId to channelStaffOwner,
                anotherStaffId to staffToRemove
            )
        )

        val expectedParamsToUpdate = mapOf<KProperty1<ChannelDocument, Any?>, Any?>(
            ChannelDocument::staff to mapOf(anotherStaffId to FieldValue.delete()),
            ChannelDocument::staffHistory to mapOf(anotherStaffId to staffToRemove),
            ChannelDocument::staffIds to arrayRemove(anotherStaffId),
            ChannelDocument::typing to arrayRemove(anotherStaffId),
            ChannelDocument::privateTyping to arrayRemove(anotherStaffId),
        )

        val timestamp = Timestamp.now()

        val expectedMessage = MessageDocument(
            aliceId = requesterId,
            userId = requesterId,
            content = "${staffToRemove.name} ${MessageType.REMOVE_PARTICIPANT.description}",
            type = MessageType.REMOVE_PARTICIPANT,
            source = null,
            createdAt = timestamp
        )

        val expectedChannel = channel.copy(
            staff = channel.staff.minus(anotherStaffId).toMutableMap(),
            staffHistory = channel.staffHistory.plus(anotherStaffId to staffToRemove).toMutableMap(),
            staffIds = channel.staffIds?.minus(anotherStaffId),
            typing = channel.typing?.minus(anotherStaffId),
            privateTyping = channel.privateTyping?.minus(anotherStaffId)
        )

        mockkStatic(Timestamp::class) {
            every { Timestamp.now() } returns timestamp

            coEvery { serviceSpy.getChannel(channelId) } returns channel.success()
            coEvery {
                channelFirestoreService.updateFields(channelId, expectedParamsToUpdate)
            } returns channelId.success()

            coEvery {
                messageFirestoreService.add(channelId, expectedMessage)
            } returns channelId.success()

            coEvery {
                channelNotificationService.notify(
                    expectedChannel,
                    ChannelChangeAction.REMOVE_STAFF
                )
            } returns mockk()

            val result = serviceSpy.removeStaff(channelId, anotherStaffId, requesterId, checkOwner = false)
            assertThat(result).isSuccessWithData(channelId)

            coVerifyOnce { serviceSpy.span(any(), any(), any()) }
            coVerifyOnce { serviceSpy.removeStaff(any<String>(), any(), any(), any(), any()) }
            coVerifyOnce { serviceSpy.removeStaff(any<ChannelDocument>(), any(), any(), any(), any()) }
            coVerifyOnce { serviceSpy.getChannel(any()) }
            coVerifyOnce { channelFirestoreService.updateFields(any(), any()) }
            coVerifyOnce { messageFirestoreService.add(any(), any()) }
            coVerifyOnce { channelNotificationService.notify(any<ChannelDocument>(), any(), any(), any()) }
        }
    }

    @OptIn(FirestoreContextUsage::class)
    @Test
    fun `#removeStaff returns channel id and not remove staff when channel has only one staff`() = runBlocking {
        val channel = channelDocument.copy(
            staff = mutableMapOf(staffId to channelStaffOwner)
        )

        coEvery { serviceSpy.getChannel(channelId) } returns channel.success()

        val result = serviceSpy.removeStaff(channelId, staffId, staffId)
        assertThat(result).isFailureOfType(AccessForbiddenException::class)

        coVerifyOnce { serviceSpy.span(any(), any(), any()) }
        coVerifyOnce { serviceSpy.removeStaff(any<String>(), any(), any(), any(), any()) }
        coVerifyOnce { serviceSpy.removeStaff(any<ChannelDocument>(), any(), any(), any(), any()) }
        coVerifyOnce { serviceSpy.getChannel(any()) }
        coVerify { channelFirestoreService wasNot called }
        coVerify { messageFirestoreService wasNot called }
        coVerify { channelNotificationService wasNot called }
    }

    @OptIn(FirestoreContextUsage::class)
    @Test
    fun `#removeStaff returns error and does not remove staff when channel has only one staff and use checkOwner as false`() =
        runBlocking {
            val channel = channelDocument.copy(
                staff = mutableMapOf(staffId to channelStaffOwner)
            )

            coEvery { serviceSpy.getChannel(channelId) } returns channel.success()

            val result = serviceSpy.removeStaff(channelId, staffId, staffId, checkOwner = false)
            assertThat(result).isFailureOfType(AccessForbiddenException::class)

            coVerifyOnce { serviceSpy.span(any(), any(), any()) }
            coVerifyOnce { serviceSpy.removeStaff(any<String>(), any(), any(), any(), any()) }
            coVerifyOnce { serviceSpy.removeStaff(any<ChannelDocument>(), any(), any(), any(), any()) }
            coVerifyOnce { serviceSpy.getChannel(any()) }
            coVerify { channelFirestoreService wasNot called }
            coVerify { messageFirestoreService wasNot called }
            coVerify { channelNotificationService wasNot called }
        }

    @OptIn(FirestoreContextUsage::class)
    @Test
    fun `#archiveChannel returns channel id and set status as archived if not Chat`() = mockTimestamp { timestamp ->
        val expectedChat = channelDocument.copy(
            status = ChannelStatus.ARCHIVED,
            archivedAt = timestamp,
            archivedReason = archivedReason,
            hideMemberInput = true,
            csatUrl = csatFormUrl,
            digitalCareQueueModalInfo = null,
            videoCall = null,
            virtualClinicStatusControl = null
        )
        val extraInfo = listOf(
            ChannelHistoryExtraInfo(
                key = ExtraInfoType.CSAT_QUESTIONNAIRE_KEY,
                value = "TEST_CSAT_AA"
            )
        )

        coEvery { serviceSpy.getChannel(channelId) } returns channelDocument.success()

        coEvery {
            channelFirestoreService.updateFields(
                channelId,
                mapOf(
                    ChannelDocument::status to ChannelStatus.ARCHIVED,
                    ChannelDocument::archivedAt to timestamp,
                    ChannelDocument::archivedReason to archivedReason,
                    ChannelDocument::csatUrl to csatFormUrl,
                    ChannelDocument::hideMemberInput to true,
                    ChannelDocument::followUp to FieldValue.delete(),
                    ChannelDocument::digitalCareQueueModalInfo to null,
                    ChannelDocument::videoCall to null,
                    ChannelDocument::virtualClinicStatusControl to null
                )
            )
        } returns channelId.success()
        coEvery {
            messageFirestoreService.add(
                channelId,
                MessageDocument(
                    aliceId = channelStaffOwner.id,
                    userId = channelStaffOwner.id,
                    content = "Conversa encerrada",
                    type = MessageType.CHANNEL_ARCHIVED,
                    createdAt = timestamp
                )
            )
        } returns channelId.success()
        coEvery {
            channelNotificationService.notify(expectedChat, ChannelChangeAction.ARCHIVE, extraInfo = extraInfo)
        } returns mockk()

        val result = serviceSpy.archiveChannel(channelId, channelStaffOwner.id, archivedReason)
        assertThat(result).isSuccessWithData(channelId)

        coVerify(exactly = 3) { serviceSpy.span(any(), any(), any()) }
        coVerifyOnce { serviceSpy.archiveChannel(any<String>(), any(), any(), any()) }
        coVerifyOnce { serviceSpy.archiveChannel(any<ChannelDocument>(), any(), any(), any()) }
        coVerifyOnce { serviceSpy.getChannel(any()) }
        coVerifyOnce { channelFirestoreService.updateFields(any(), any()) }
        coVerifyOnce { messageFirestoreService.add(any(), any()) }
        coVerifyOnce { channelNotificationService.notify(any<ChannelDocument>(), any(), any(), any()) }
    }

    @OptIn(FirestoreContextUsage::class)
    @Test
    fun `#archiveChannel returns channel id and set status as archived for Chat when force`() =
        mockTimestamp { timestamp ->
            val channelDocument = channelDocument.copy(kind = ChannelKind.CHAT)

            val expectedChat = channelDocument.copy(
                status = ChannelStatus.ARCHIVED,
                archivedAt = timestamp,
                archivedReason = archivedReason,
                hideMemberInput = true,
                csatUrl = csatFormUrl,
                digitalCareQueueModalInfo = null,
                videoCall = null,
                virtualClinicStatusControl = null
            )
            val extraInfo = listOf(
                ChannelHistoryExtraInfo(
                    key = ExtraInfoType.CSAT_QUESTIONNAIRE_KEY,
                    value = "TEST_CSAT_AA"
                )
            )

            coEvery { serviceSpy.getChannel(channelId) } returns channelDocument.success()

            coEvery {
                channelFirestoreService.updateFields(
                    channelId,
                    mapOf(
                        ChannelDocument::status to ChannelStatus.ARCHIVED,
                        ChannelDocument::archivedAt to timestamp,
                        ChannelDocument::archivedReason to archivedReason,
                        ChannelDocument::csatUrl to csatFormUrl,
                        ChannelDocument::hideMemberInput to true,
                        ChannelDocument::followUp to FieldValue.delete(),
                        ChannelDocument::digitalCareQueueModalInfo to null,
                        ChannelDocument::videoCall to null,
                        ChannelDocument::virtualClinicStatusControl to null
                    )
                )
            } returns channelId.success()
            coEvery {
                messageFirestoreService.add(
                    channelId,
                    MessageDocument(
                        aliceId = channelStaffOwner.id,
                        userId = channelStaffOwner.id,
                        content = "Conversa encerrada",
                        type = MessageType.CHANNEL_ARCHIVED,
                        createdAt = timestamp
                    )
                )
            } returns channelId.success()
            coEvery {
                channelNotificationService.notify(expectedChat, ChannelChangeAction.ARCHIVE, extraInfo = extraInfo)
            } returns mockk()


            val result = serviceSpy.archiveChannel(channelId, channelStaffOwner.id, archivedReason, true)
            assertThat(result).isSuccessWithData(channelId)

            coVerify(exactly = 3) { serviceSpy.span(any(), any(), any()) }
            coVerifyOnce { serviceSpy.archiveChannel(any<String>(), any(), any(), any()) }
            coVerifyOnce { serviceSpy.archiveChannel(any<ChannelDocument>(), any(), any(), any()) }
            coVerifyOnce { serviceSpy.getChannel(any()) }
            coVerifyOnce { channelFirestoreService.updateFields(any(), any()) }
            coVerifyOnce { messageFirestoreService.add(any(), any()) }
            coVerifyOnce { channelNotificationService.notify(any<ChannelDocument>(), any(), any(), any()) }
        }

    @OptIn(FirestoreContextUsage::class)
    @Test
    fun `#archiveChannel returns error when channel is Chat`() = runBlocking {
        coEvery { serviceSpy.getChannel(channelId) } returns channelDocument.copy(kind = ChannelKind.CHAT).success()

        val result = serviceSpy.archiveChannel(channelId, channelStaffOwner.id, archivedReason)
        assertThat(result).isFailureOfType(InvalidArgumentException::class)

        coVerify(exactly = 2) { serviceSpy.span(any(), any(), any()) }
        coVerifyOnce { serviceSpy.archiveChannel(any<String>(), any(), any(), any()) }
        coVerifyOnce { serviceSpy.archiveChannel(any<ChannelDocument>(), any(), any(), any()) }
        coVerifyOnce { serviceSpy.getChannel(any()) }
        coVerify { channelFirestoreService wasNot called }
        coVerify { messageFirestoreService wasNot called }
        coVerify { channelNotificationService wasNot called }
    }

    @OptIn(FirestoreContextUsage::class)
    @Test
    fun `#archiveChannel returns error when an error occurs to update channel`() = runBlocking {
        val timestamp = Timestamp.now()
        mockkStatic(Timestamp::class) {
            every { Timestamp.now() } returns timestamp

            coEvery { serviceSpy.getChannel(channelId) } returns channelDocument.success()

            coEvery {
                channelFirestoreService.updateFields(
                    channelId,
                    mapOf(
                        ChannelDocument::status to ChannelStatus.ARCHIVED,
                        ChannelDocument::archivedAt to timestamp,
                        ChannelDocument::archivedReason to archivedReason,
                        ChannelDocument::csatUrl to csatFormUrl,
                        ChannelDocument::hideMemberInput to true,
                        ChannelDocument::followUp to FieldValue.delete(),
                        ChannelDocument::digitalCareQueueModalInfo to null,
                        ChannelDocument::videoCall to null,
                        ChannelDocument::virtualClinicStatusControl to null
                    )
                )
            } returns Exception().failure()

            val result = serviceSpy.archiveChannel(channelId, channelStaffOwner.id, archivedReason)
            assertThat(result).isFailureOfType(Exception::class)

            coVerify(exactly = 2) { serviceSpy.span(any(), any(), any()) }
            coVerifyOnce { serviceSpy.archiveChannel(any<String>(), any(), any(), any()) }
            coVerifyOnce { serviceSpy.archiveChannel(any<ChannelDocument>(), any(), any(), any()) }
            coVerifyOnce { serviceSpy.getChannel(any()) }
            coVerifyOnce { channelFirestoreService.updateFields(any(), any()) }
            coVerify { messageFirestoreService wasNot called }
            coVerify { channelNotificationService wasNot called }
        }
    }

    @OptIn(FirestoreContextUsage::class)
    @Test
    fun `#archiveChannel returns channel id and archive the channel when it has a sub category classifier`() =
        mockTimestamp { timestamp ->
            val channelDocument = channelDocument.copy(
                kind = ChannelKind.CHANNEL,
                category = ChannelCategory.ASSISTANCE,
                subCategory = ChannelSubCategory.LONGITUDINAL,
                subCategoryClassifier = HEALTH_TEAM,
                type = null
            ).populateTypes()

            val expectedChat = channelDocument.copy(
                status = ChannelStatus.ARCHIVED,
                archivedAt = timestamp,
                archivedReason = archivedReason,
                hideMemberInput = true,
                csatUrl = csatFormUrl,
                digitalCareQueueModalInfo = null,
                videoCall = null,
                virtualClinicStatusControl = null
            )
            val extraInfo = listOf(
                ChannelHistoryExtraInfo(
                    key = ExtraInfoType.CSAT_QUESTIONNAIRE_KEY,
                    value = "TEST_CSAT_AA"
                )
            )

            coEvery { serviceSpy.getChannel(channelId) } returns channelDocument.success()
            coEvery {
                channelFirestoreService.updateFields(
                    channelId,
                    mapOf(
                        ChannelDocument::status to ChannelStatus.ARCHIVED,
                        ChannelDocument::archivedAt to timestamp,
                        ChannelDocument::archivedReason to archivedReason,
                        ChannelDocument::csatUrl to csatFormUrl,
                        ChannelDocument::hideMemberInput to true,
                        ChannelDocument::followUp to FieldValue.delete(),
                        ChannelDocument::digitalCareQueueModalInfo to null,
                        ChannelDocument::videoCall to null,
                        ChannelDocument::virtualClinicStatusControl to null
                    )
                )
            } returns channelId.success()
            coEvery {
                messageFirestoreService.add(
                    channelId,
                    MessageDocument(
                        aliceId = channelStaffOwner.id,
                        userId = channelStaffOwner.id,
                        content = "Conversa encerrada",
                        type = MessageType.CHANNEL_ARCHIVED,
                        createdAt = timestamp
                    )
                )
            } returns channelId.success()
            coEvery {
                channelNotificationService.notify(expectedChat, ChannelChangeAction.ARCHIVE, extraInfo = extraInfo)
            } returns mockk()

            val result = serviceSpy.archiveChannel(channelId, channelStaffOwner.id, archivedReason)
            assertThat(result).isSuccessWithData(channelId)

            coVerify(exactly = 3) { serviceSpy.span(any(), any(), any()) }
            coVerifyOnce { serviceSpy.archiveChannel(any<String>(), any(), any(), any()) }
            coVerifyOnce { serviceSpy.archiveChannel(any<ChannelDocument>(), any(), any(), any()) }
            coVerifyOnce { serviceSpy.getChannel(any()) }
            coVerifyOnce { channelFirestoreService.updateFields(any(), any()) }
            coVerifyOnce { messageFirestoreService.add(any(), any()) }
            coVerifyOnce { channelNotificationService.notify(any<ChannelDocument>(), any(), any(), any()) }
        }

    @OptIn(FirestoreContextUsage::class)
    @Test
    fun `#archiveChannel returns error when not found the channel`() = runBlocking {
        coEvery { serviceSpy.getChannel(channelId) } returns NotFoundException().failure()

        val result = serviceSpy.archiveChannel(channelId, channelStaffOwner.id, archivedReason)
        assertThat(result).isFailureOfType(NotFoundException::class)

        coVerifyOnce { serviceSpy.span(any(), any(), any()) }
        coVerifyOnce { serviceSpy.archiveChannel(any<String>(), any(), any(), any()) }
        coVerifyOnce { serviceSpy.getChannel(any()) }
        coVerify { channelFirestoreService wasNot called }
        coVerify { messageFirestoreService wasNot called }
        coVerify { channelNotificationService wasNot called }
    }

    @OptIn(FirestoreContextUsage::class)
    @Test
    fun `#archiveChannel returns channel id and do nothing when channel already is archived`() = runBlocking {
        coEvery {
            serviceSpy.getChannel(channelId)
        } returns channelDocument.copy(status = ChannelStatus.ARCHIVED).success()

        val result = serviceSpy.archiveChannel(channelId, channelStaffOwner.id, archivedReason)
        assertThat(result).isSuccessWithData(channelId)

        coVerify(exactly = 2) { serviceSpy.span(any(), any(), any()) }
        coVerifyOnce { serviceSpy.archiveChannel(any<String>(), any(), any(), any()) }
        coVerifyOnce { serviceSpy.archiveChannel(any<ChannelDocument>(), any(), any(), any()) }
        coVerifyOnce { serviceSpy.getChannel(any()) }
        coVerify { channelFirestoreService wasNot called }
        coVerify { messageFirestoreService wasNot called }
        coVerify { channelNotificationService wasNot called }
    }

    @OptIn(FirestoreContextUsage::class)
    @Test
    fun `#updateFields returns channel id and update fields`() = runBlocking {
        val params = mapOf<KProperty1<ChannelDocument, Any?>, Any?>(
            ChannelDocument::status to ChannelStatus.ACTIVE,
            ChannelDocument::kind to ChannelKind.CHAT,
        )

        coEvery { channelFirestoreService.updateFields(channelId, params) } returns channelId.success()

        val result = service.updateFields(channelId, params)
        assertThat(result).isSuccessWithData(channelId)

        coVerifyOnce { channelFirestoreService.updateFields(any(), any()) }
        coVerify { channelNotificationService wasNot called }
        coVerify { messageFirestoreService wasNot called }
        coVerify { serviceSpy wasNot called }
    }

    @OptIn(FirestoreContextUsage::class)
    @Test
    fun `#rename returns channel id and call rename by document`() = runBlocking {
        coEvery { serviceSpy.getChannel(channelId) } returns channelDocument.success()
        coEvery {
            serviceSpy.rename(channelDocument, "newChannelName", channelStaffOwner.id)
        } returns channelDocument.success()

        val result = serviceSpy.rename(channelId, "newChannelName", channelStaffOwner.id)
        assertThat(result).isSuccessWithData(channelId)

        coVerifyOnce { serviceSpy.span(any(), any(), any()) }
        coVerifyOnce { serviceSpy.getChannel(any()) }
        coVerifyOnce { serviceSpy.rename(any<String>(), any(), any()) }
        coVerifyOnce { serviceSpy.rename(any<ChannelDocument>(), any(), any()) }
        coVerify { messageFirestoreService wasNot called }
        coVerify { channelNotificationService wasNot called }
        coVerify { channelFirestoreService wasNot called }
    }

    @OptIn(FirestoreContextUsage::class)
    @Test
    fun `#rename returns channel id and change channel name and send event to add HPs from attendance team on background`() =
        mockTimestamp(timestamp) {
            val newName = "newChannelName"
            val channelDocument = channelDocument.copy(
                kind = ChannelKind.CHAT,
                category = ChannelCategory.ASSISTANCE,
                type = ChannelType.CHAT,
                subCategory = ChannelSubCategory.ACUTE,
                subCategoryClassifier = null
            )

            val expectedChannelDocument = channelDocument.copy(
                name = newName,
                type = ChannelType.ASSISTANCE_CARE,
                kind = ChannelKind.CHANNEL,
                becameAsyncAt = timestamp
            ).populateTypes()

            val fieldToUpdate = mapOf<KProperty1<ChannelDocument, Any?>, Any?>(
                ChannelDocument::name to newName,
                ChannelDocument::becameAsyncAt to timestamp,
                ChannelDocument::kind to ChannelKind.CHANNEL,
                ChannelDocument::subCategory to ChannelSubCategory.ACUTE,
                ChannelDocument::type to ChannelType.ASSISTANCE_CARE
            )

            val messageToCreate = MessageDocument(
                aliceId = staffId,
                userId = staffId,
                content = "Conversa renomeada para newChannelName",
                type = MessageType.CHANNEL_RENAMED,
                createdAt = timestamp
            )

            coEvery { serviceSpy.updateFields(channelId, fieldToUpdate) } returns channelId.success()
            coEvery { messageFirestoreService.add(channelId, messageToCreate) } returns messageId.success()
            coEvery {
                channelNotificationService.notify(expectedChannelDocument, ChannelChangeAction.RENAME)
            } returns mockk()

            val result = serviceSpy.rename(channelDocument, newName, channelStaffOwner.id)
            assertThat(result).isSuccessWithData(expectedChannelDocument)

            coVerifyOnce { serviceSpy.span(any(), any(), any()) }
            coVerifyOnce { serviceSpy.rename(any<ChannelDocument>(), any(), any()) }
            coVerifyOnce { serviceSpy.updateFields(any(), any()) }
            coVerifyOnce { messageFirestoreService.add(any(), any()) }
            coVerifyOnce { channelNotificationService.notify(any<ChannelDocument>(), any(), any(), any()) }

            coVerify { channelFirestoreService wasNot called }
        }

    @OptIn(FirestoreContextUsage::class)
    @Test
    fun `#rename returns channel id and change channel name and sub category for SCREENING`() =
        mockTimestamp(timestamp) {
            val newName = "newChannelName"
            val channelDocument = channelDocument.copy(
                kind = ChannelKind.CHAT,
                category = ChannelCategory.ASSISTANCE,
                type = ChannelType.CHAT,
                subCategory = ChannelSubCategory.SCREENING,
                subCategoryClassifier = null
            )

            val expectedChannelDocument = channelDocument.copy(
                name = newName,
                type = ChannelType.ASSISTANCE_CARE,
                kind = ChannelKind.CHANNEL,
                subCategory = ChannelSubCategory.ACUTE,
                becameAsyncAt = timestamp
            ).populateTypes()

            val fieldToUpdate = mapOf<KProperty1<ChannelDocument, Any?>, Any?>(
                ChannelDocument::name to newName,
                ChannelDocument::becameAsyncAt to timestamp,
                ChannelDocument::kind to ChannelKind.CHANNEL,
                ChannelDocument::subCategory to ChannelSubCategory.ACUTE,
                ChannelDocument::type to ChannelType.ASSISTANCE_CARE
            )

            val messageToCreate = MessageDocument(
                aliceId = staffId,
                userId = staffId,
                content = "Conversa renomeada para newChannelName",
                type = MessageType.CHANNEL_RENAMED,
                createdAt = timestamp
            )

            coEvery { serviceSpy.updateFields(channelId, fieldToUpdate) } returns channelId.success()
            coEvery { messageFirestoreService.add(channelId, messageToCreate) } returns messageId.success()
            coEvery {
                channelNotificationService.notify(expectedChannelDocument, ChannelChangeAction.RENAME)
            } returns mockk()

            val result = serviceSpy.rename(channelDocument, newName, channelStaffOwner.id)
            assertThat(result).isSuccessWithData(expectedChannelDocument)

            coVerifyOnce { serviceSpy.span(any(), any(), any()) }
            coVerifyOnce { serviceSpy.rename(any<ChannelDocument>(), any(), any()) }
            coVerifyOnce { serviceSpy.updateFields(any(), any()) }
            coVerifyOnce { messageFirestoreService.add(any(), any()) }
            coVerifyOnce { channelNotificationService.notify(any<ChannelDocument>(), any(), any(), any()) }

            coVerify { channelFirestoreService wasNot called }
        }

    @OptIn(FirestoreContextUsage::class)
    @Test
    fun `#rename returns channel id and change channel name and not send event to add HPs on background for VIRTUAL_CLINIC`() =
        mockTimestamp(timestamp) {
            val newName = "newChannelName"
            val channelDocument = channelDocument.copy(
                kind = ChannelKind.CHAT,
                category = ChannelCategory.ASSISTANCE,
                type = ChannelType.CHAT,
                subCategory = ChannelSubCategory.VIRTUAL_CLINIC,
                subCategoryClassifier = null
            )

            val expectedChannelDocument = channelDocument.copy(
                name = newName,
                type = ChannelType.ASSISTANCE_CARE,
                kind = ChannelKind.CHANNEL,
                becameAsyncAt = timestamp
            ).populateTypes()

            val fieldToUpdate = mapOf<KProperty1<ChannelDocument, Any?>, Any?>(
                ChannelDocument::name to newName,
                ChannelDocument::becameAsyncAt to timestamp,
                ChannelDocument::kind to ChannelKind.CHANNEL,
                ChannelDocument::subCategory to ChannelSubCategory.VIRTUAL_CLINIC,
                ChannelDocument::type to ChannelType.ASSISTANCE_CARE
            )

            val messageToCreate = MessageDocument(
                aliceId = staffId,
                userId = staffId,
                content = "Conversa renomeada para newChannelName",
                type = MessageType.CHANNEL_RENAMED,
                createdAt = timestamp
            )

            coEvery { serviceSpy.updateFields(channelId, fieldToUpdate) } returns channelId.success()
            coEvery { messageFirestoreService.add(channelId, messageToCreate) } returns messageId.success()
            coEvery {
                channelNotificationService.notify(expectedChannelDocument, ChannelChangeAction.RENAME)
            } returns mockk()

            val result = serviceSpy.rename(channelDocument, newName, channelStaffOwner.id)
            assertThat(result).isSuccessWithData(expectedChannelDocument)

            coVerifyOnce { serviceSpy.span(any(), any(), any()) }
            coVerifyOnce { serviceSpy.rename(any<ChannelDocument>(), any(), any()) }
            coVerifyOnce { serviceSpy.updateFields(any(), any()) }
            coVerifyOnce { messageFirestoreService.add(any(), any()) }
            coVerifyOnce { channelNotificationService.notify(any<ChannelDocument>(), any(), any(), any()) }

            coVerify { channelFirestoreService wasNot called }
        }

    @OptIn(FirestoreContextUsage::class)
    @Test
    fun `#rename returns channel id and change channel name and send event to add HPs from reference team on background`() =
        mockTimestamp(timestamp) {
            val newName = "newChannelName"
            val channelDocument = channelDocument.copy(
                kind = ChannelKind.CHAT,
                category = ChannelCategory.ASSISTANCE,
                type = ChannelType.CHAT,
                subCategory = ChannelSubCategory.LONGITUDINAL,
                subCategoryClassifier = null
            )

            val expectedChannelDocument = channelDocument.copy(
                name = newName,
                type = ChannelType.ASSISTANCE_CARE,
                kind = ChannelKind.CHANNEL,
                becameAsyncAt = timestamp
            ).populateTypes()

            val fieldToUpdate = mapOf<KProperty1<ChannelDocument, Any?>, Any?>(
                ChannelDocument::name to newName,
                ChannelDocument::becameAsyncAt to timestamp,
                ChannelDocument::kind to ChannelKind.CHANNEL,
                ChannelDocument::subCategory to ChannelSubCategory.LONGITUDINAL,
                ChannelDocument::type to ChannelType.ASSISTANCE_CARE
            )

            val messageToCreate = MessageDocument(
                aliceId = staffId,
                userId = staffId,
                content = "Conversa renomeada para newChannelName",
                type = MessageType.CHANNEL_RENAMED,
                createdAt = timestamp
            )

            coEvery { serviceSpy.updateFields(channelId, fieldToUpdate) } returns channelId.success()
            coEvery { messageFirestoreService.add(channelId, messageToCreate) } returns messageId.success()
            coEvery {
                channelNotificationService.notify(expectedChannelDocument, ChannelChangeAction.RENAME)
            } returns mockk()

            val result = serviceSpy.rename(channelDocument, "newChannelName", channelStaffOwner.id)
            assertThat(result).isSuccessWithData(expectedChannelDocument)

            coVerifyOnce { serviceSpy.span(any(), any(), any()) }
            coVerifyOnce { serviceSpy.rename(any<ChannelDocument>(), any(), any()) }
            coVerifyOnce { serviceSpy.updateFields(any(), any()) }
            coVerifyOnce { messageFirestoreService.add(any(), any()) }
            coVerifyOnce { channelNotificationService.notify(any<ChannelDocument>(), any(), any(), any()) }

            coVerify { channelFirestoreService wasNot called }
        }

    @OptIn(FirestoreContextUsage::class)
    @Test
    fun `#rename returns channel id and change channel name and not send event to add HPs on background`() =
        mockTimestamp(timestamp) {
            val newName = "newChannelName"
            val channelDocument = channelDocument.copy(
                kind = ChannelKind.CHANNEL,
                becameAsyncAt = timestamp,
                type = ChannelType.ADMINISTRATIVE,
                category = ChannelCategory.ADMINISTRATIVE,
                subCategoryClassifier = null,
                demands = emptyList()
            )

            val expectedChannelDocument = channelDocument.copy(name = newName)

            val fieldToUpdate = mapOf<KProperty1<ChannelDocument, Any?>, Any?>(
                ChannelDocument::name to newName,
                ChannelDocument::becameAsyncAt to timestamp,
                ChannelDocument::kind to ChannelKind.CHANNEL,
                ChannelDocument::subCategory to null,
                ChannelDocument::type to ChannelType.ADMINISTRATIVE
            )

            val messageToCreate = MessageDocument(
                aliceId = staffId,
                userId = staffId,
                content = "Conversa renomeada para newChannelName",
                type = MessageType.CHANNEL_RENAMED,
                createdAt = timestamp
            )

            coEvery { serviceSpy.updateFields(channelId, fieldToUpdate) } returns channelId.success()
            coEvery { messageFirestoreService.add(channelId, messageToCreate) } returns messageId.success()
            coEvery {
                channelNotificationService.notify(expectedChannelDocument, ChannelChangeAction.RENAME)
            } returns mockk()

            val result = serviceSpy.rename(channelDocument, "newChannelName", channelStaffOwner.id)
            assertThat(result).isSuccessWithData(expectedChannelDocument)

            coVerifyOnce { serviceSpy.span(any(), any(), any()) }
            coVerifyOnce { serviceSpy.rename(any<ChannelDocument>(), any(), any()) }
            coVerifyOnce { serviceSpy.updateFields(any(), any()) }
            coVerifyOnce { messageFirestoreService.add(any(), any()) }
            coVerifyOnce { channelNotificationService.notify(any<ChannelDocument>(), any(), any(), any()) }

            coVerify { channelFirestoreService wasNot called }
        }

    @OptIn(FirestoreContextUsage::class)
    @Test
    fun `#rename returns error when channel has no demand`() = runBlocking {
        val channelDocument = channelDocument.copy(demands = emptyList())

        val result = serviceSpy.rename(channelDocument, "newChannelName", channelStaffOwner.id)
        assertThat(result).isFailureOfType(IllegalArgumentException::class)

        coVerifyOnce { serviceSpy.span(any(), any(), any()) }
        coVerifyOnce { serviceSpy.rename(any<ChannelDocument>(), any(), any()) }
        coVerify { channelNotificationService wasNot called }
        coVerify { messageFirestoreService wasNot called }
        coVerify { channelFirestoreService wasNot called }
    }

    @OptIn(FirestoreContextUsage::class)
    @Test
    fun `#channelClassification return channel id and update classifications fields`() = runBlocking {
        val kind = ChannelKind.CHANNEL
        val category = ChannelCategory.ASSISTANCE
        val subCategory = ChannelSubCategory.LONGITUDINAL
        val subCategoryClassifier = HEALTH_TEAM

        val expectedChannel = channelDocument.copy(
            type = ChannelType.HEALTH_PLAN,
            kind = ChannelKind.CHANNEL,
            category = ChannelCategory.ASSISTANCE,
            subCategory = ChannelSubCategory.LONGITUDINAL,
            subCategoryClassifier = HEALTH_TEAM
        )

        coEvery {
            channelFirestoreService.updateFields(
                channelId,
                mapOf(
                    ChannelDocument::type to ChannelType.HEALTH_PLAN,
                    ChannelDocument::kind to kind,
                    ChannelDocument::category to category,
                    ChannelDocument::subCategory to subCategory,
                    ChannelDocument::subCategoryClassifier to subCategoryClassifier
                )
            )
        } returns channelId.success()

        coEvery { channelFirestoreService.getChannel(channelId) } returns channelDocument.success()
        coEvery {
            channelNotificationService.notify(expectedChannel, ChannelChangeAction.CHANGE_TYPE)
        } returns mockk()

        val result = service.channelClassification(channelId, kind, category, subCategory, subCategoryClassifier)
        assertThat(result).isSuccessWithData(channelId)

        coVerifyOnce { channelFirestoreService.updateFields(any(), any()) }
        coVerifyOnce { channelFirestoreService.getChannel(any()) }
        coVerifyOnce { channelNotificationService.notify(any<ChannelDocument>(), any(), any(), any()) }

        coVerify { messageFirestoreService wasNot called }
    }

    @OptIn(FirestoreContextUsage::class)
    @Test
    fun `#channelClassification return channel id and update classifications fields and set channel name for category ADM`() =
        runBlocking {
            val kind = ChannelKind.CHAT
            val category = ChannelCategory.ADMINISTRATIVE

            val channelDocument = channelDocument.copy(name = null)

            val expectedChannel = channelDocument.copy(
                type = ChannelType.CHAT,
                kind = ChannelKind.CHAT,
                category = ChannelCategory.ADMINISTRATIVE
            ).populateTypes()

            coEvery {
                channelFirestoreService.updateFields(
                    channelId,
                    mapOf(
                        ChannelDocument::type to ChannelType.CHAT,
                        ChannelDocument::kind to kind,
                        ChannelDocument::category to category,
                        ChannelDocument::subCategory to null,
                        ChannelDocument::subCategoryClassifier to null
                    )
                )
            } returns channelId.success()

            coEvery { channelFirestoreService.getChannel(channelId) } returns channelDocument.success()
            coEvery {
                channelNotificationService.notify(expectedChannel, ChannelChangeAction.CHANGE_TYPE)
            } returns mockk()

            val result = service.channelClassification(channelId, kind, category, null, null)
            assertThat(result).isSuccessWithData(channelId)

            coVerifyOnce { channelFirestoreService.updateFields(any(), any()) }
            coVerifyOnce { channelFirestoreService.getChannel(any()) }
            coVerifyOnce { channelNotificationService.notify(any<ChannelDocument>(), any(), any(), any()) }

        }

    @OptIn(FirestoreContextUsage::class)
    @Test
    fun `#channelClassification return error when kind is chat and have a subCategoryClassifier`() = runBlocking {
        val kind = ChannelKind.CHAT
        val subCategoryClassifier = HEALTH_TEAM

        coEvery {
            channelFirestoreService.getChannel(channelId)
        } returns channelDocument.success()

        val result = service.channelClassification(channelId, kind, null, null, subCategoryClassifier)
        assertThat(result).isFailureOfType(IllegalArgumentException::class)

        coVerifyOnce { channelFirestoreService.getChannel(any()) }

        coVerify { channelNotificationService wasNot called }
        coVerify { messageFirestoreService wasNot called }
    }

    @OptIn(FirestoreContextUsage::class)
    @Test
    fun `#dischargeChannel should updates categorize, rename and archive channel`() =
        mockTimestamp(timestamp) {
            val channelDocument = channelDocument.copy(
                name = "name",
                kind = ChannelKind.CHAT,
                category = ChannelCategory.ADMINISTRATIVE,
                subCategory = null
            )

            val name = "new_name"
            val kind = ChannelKind.CHANNEL
            val staffId = staffId
            val category = ChannelCategory.ADMINISTRATIVE
            val subCategory = ChannelSubCategory.ACUTE

            val channelDocumentClassified = channelDocument.copy(
                kind = kind,
                category = category,
                subCategory = subCategory
            )

            val channelDocumentRenamed = channelDocumentClassified.copy(name = name)

            val channelDocumentArchived = channelDocumentRenamed.copy(
                kind = ChannelKind.CHANNEL,
                becameAsyncAt = timestamp
            )

            coEvery { serviceSpy.getChannel(channelId) } returns channelDocument.success()

            coEvery {
                serviceSpy.channelClassification(channelDocument, kind, category, subCategory, null)
            } returns channelDocumentClassified.success()

            coEvery {
                serviceSpy.rename(
                    channelDocumentClassified,
                    name,
                    staffId
                )
            } returns channelDocumentRenamed.success()

            coEvery {
                serviceSpy.archiveChannel(channelDocumentRenamed, staffId, ChannelArchivedReason.DISCHARGE, true)
            } returns channelDocumentArchived.success()

            coEvery { personService.get(personId) } returns person.success()

            coEvery {
                messageFirestoreService.add(
                    channelId = channelId,
                    messageDocument = MessageDocument(
                        content = "",
                        source = "DISCHARGE",
                        type = MessageType.TEXT,
                        userId = "",
                        createdAt = timestamp
                    ),
                    isPrivate = false
                )
            } returns channelId.success()

            coEvery {
                channelNotificationService.notify(channelDocumentArchived, ChannelChangeAction.DISCHARGED)
            } returns mockk()

            val result = serviceSpy.dischargeChannel(
                channelId = channelId,
                name = name,
                kind = kind,
                staffId = staffId,
                category = category,
                subCategory = subCategory
            )
            assertThat(result).isSuccessWithData(channelId)

            coVerifyOnce { serviceSpy.getChannel(any()) }
            coVerifyOnce { serviceSpy.channelClassification(any<ChannelDocument>(), any(), any(), any(), any()) }
            coVerifyOnce { serviceSpy.rename(any<ChannelDocument>(), any(), any()) }
            coVerifyOnce { serviceSpy.archiveChannel(any<ChannelDocument>(), any(), any(), any()) }
            coVerifyOnce { messageFirestoreService.add(any(), any(), any()) }
            coVerifyOnce { serviceSpy.dischargeChannel(any(), any(), any(), any(), any(), any()) }
            coVerifyOnce { serviceSpy.span(any(), any(), any()) }
            coVerifyOnce { channelNotificationService.notify(any<ChannelDocument>(), any(), any(), any()) }
            coVerifyOnce { personService.get(any()) }
        }

    @OptIn(FirestoreContextUsage::class)
    @Test
    fun `#dischargeChannel should returns error on send message`() = mockTimestamp(timestamp) {
        val channelDocument = channelDocument.copy(
            name = "name",
            kind = ChannelKind.CHAT,
            category = ChannelCategory.ADMINISTRATIVE,
            subCategory = null
        )

        val name = "new_name"
        val kind = ChannelKind.CHANNEL
        val staffId = staffId
        val category = ChannelCategory.ADMINISTRATIVE
        val subCategory = ChannelSubCategory.ACUTE

        coEvery { serviceSpy.getChannel(channelId) } returns channelDocument.success()

        coEvery {
            serviceSpy.channelClassification(channelDocument, kind, category, subCategory, null)
        } returns channelDocument.success()

        coEvery { serviceSpy.rename(channelDocument, name, staffId) } returns channelDocument.success()

        coEvery {
            serviceSpy.archiveChannel(channelDocument, staffId, ChannelArchivedReason.DISCHARGE, true)
        } returns channelDocument.success()

        coEvery { personService.get(personId) } returns person.success()

        coEvery {
            messageFirestoreService.add(
                channelId = channelId,
                messageDocument = MessageDocument(
                    content = "",
                    source = "DISCHARGE",
                    type = MessageType.TEXT,
                    userId = "",
                    createdAt = timestamp
                ),
                isPrivate = false
            )
        } returns Exception("").failure()

        val result = serviceSpy.dischargeChannel(
            channelId = channelId,
            name = name,
            kind = kind,
            staffId = staffId,
            category = category,
            subCategory = subCategory
        )
        assertThat(result).isFailureOfType(Exception::class)

        coVerifyOnce { serviceSpy.getChannel(any()) }
        coVerifyOnce { serviceSpy.channelClassification(any<ChannelDocument>(), any(), any(), any(), any()) }
        coVerifyOnce { serviceSpy.rename(any<ChannelDocument>(), any(), any()) }
        coVerifyOnce { serviceSpy.archiveChannel(any<ChannelDocument>(), any(), any(), any()) }
        coVerifyOnce { serviceSpy.dischargeChannel(any(), any(), any(), any(), any(), any()) }
        coVerifyOnce { messageFirestoreService.add(any(), any(), any()) }
        coVerify { personService.get(any()) }
        coVerifyOnce { serviceSpy.span(any(), any(), any()) }
        coVerify { channelNotificationService wasNot called }
    }

    @OptIn(FirestoreContextUsage::class)
    @Test
    fun `#dischargeChannel should returns error on get person`() = mockTimestamp(timestamp) {
        val channelDocument = channelDocument.copy(
            name = "name",
            kind = ChannelKind.CHAT,
            category = ChannelCategory.ADMINISTRATIVE,
            subCategory = null
        )

        val name = "new_name"
        val kind = ChannelKind.CHANNEL
        val staffId = staffId
        val category = ChannelCategory.ADMINISTRATIVE
        val subCategory = ChannelSubCategory.ACUTE

        coEvery { serviceSpy.getChannel(channelId) } returns channelDocument.success()

        coEvery {
            serviceSpy.channelClassification(channelDocument, kind, category, subCategory, null)
        } returns channelDocument.success()

        coEvery { serviceSpy.rename(channelDocument, name, staffId) } returns channelDocument.success()

        coEvery {
            serviceSpy.archiveChannel(channelDocument, staffId, ChannelArchivedReason.DISCHARGE, true)
        } returns channelDocument.success()

        coEvery { personService.get(personId) } returns Exception("").failure()

        val result = serviceSpy.dischargeChannel(
            channelId = channelId,
            name = name,
            kind = kind,
            staffId = staffId,
            category = category,
            subCategory = subCategory
        )
        assertThat(result).isFailureOfType(Exception::class)

        coVerifyOnce { serviceSpy.getChannel(any()) }
        coVerifyOnce { serviceSpy.channelClassification(any<ChannelDocument>(), any(), any(), any(), any()) }
        coVerifyOnce { serviceSpy.rename(any<ChannelDocument>(), any(), any()) }
        coVerifyOnce { serviceSpy.archiveChannel(any<ChannelDocument>(), any(), any(), any()) }
        coVerifyOnce { serviceSpy.dischargeChannel(any(), any(), any(), any(), any(), any()) }
        coVerify { personService.get(any()) }
        coVerifyOnce { serviceSpy.span(any(), any(), any()) }
        coVerifyOnce { messageFirestoreService wasNot called }
        coVerify { channelNotificationService wasNot called }
    }

    @OptIn(FirestoreContextUsage::class)
    @Test
    fun `#dischargeChannel should returns erro on archive`() = runBlocking {
        val channelDocument = channelDocument.copy(
            name = "name",
            kind = ChannelKind.CHAT,
            category = ChannelCategory.ADMINISTRATIVE,
            subCategory = null
        )

        val name = "new_name"
        val kind = ChannelKind.CHANNEL
        val staffId = staffId
        val category = ChannelCategory.ADMINISTRATIVE
        val subCategory = ChannelSubCategory.ACUTE

        coEvery { serviceSpy.getChannel(channelId) } returns channelDocument.success()

        coEvery {
            serviceSpy.channelClassification(channelDocument, kind, category, subCategory, null)
        } returns channelDocument.success()

        coEvery { serviceSpy.rename(channelDocument, name, staffId) } returns channelDocument.success()

        coEvery {
            serviceSpy.archiveChannel(channelDocument, staffId, ChannelArchivedReason.DISCHARGE, true)
        } returns Exception("").failure()

        val result = serviceSpy.dischargeChannel(
            channelId = channelId,
            name = name,
            kind = kind,
            staffId = staffId,
            category = category,
            subCategory = subCategory
        )
        assertThat(result).isFailureOfType(Exception::class)

        coVerifyOnce { serviceSpy.getChannel(any()) }
        coVerifyOnce { serviceSpy.channelClassification(any<ChannelDocument>(), any(), any(), any(), any()) }
        coVerifyOnce { serviceSpy.rename(any<ChannelDocument>(), any(), any()) }
        coVerifyOnce { serviceSpy.archiveChannel(any<ChannelDocument>(), any(), any(), any()) }
        coVerifyOnce { serviceSpy.dischargeChannel(any(), any(), any(), any(), any(), any()) }
        coVerifyOnce { serviceSpy.span(any(), any(), any()) }
        coVerify { channelNotificationService wasNot called }
        coVerify { messageFirestoreService wasNot called }
    }

    @OptIn(FirestoreContextUsage::class)
    @Test
    fun `#dischargeChannel should returns error on rename`() = runBlocking {
        val channelDocument = channelDocument.copy(
            name = "name",
            kind = ChannelKind.CHAT,
            category = ChannelCategory.ADMINISTRATIVE,
            subCategory = null
        )

        val name = "new_name"
        val kind = ChannelKind.CHANNEL
        val staffId = staffId
        val category = ChannelCategory.ADMINISTRATIVE
        val subCategory = ChannelSubCategory.ACUTE

        coEvery { serviceSpy.getChannel(channelId) } returns channelDocument.success()

        coEvery {
            serviceSpy.channelClassification(channelDocument, kind, category, subCategory, null)
        } returns channelDocument.success()

        coEvery { serviceSpy.rename(channelDocument, name, staffId) } returns Exception("").failure()

        val result = serviceSpy.dischargeChannel(
            channelId = channelId,
            name = name,
            kind = kind,
            staffId = staffId,
            category = category,
            subCategory = subCategory
        )
        assertThat(result).isFailureOfType(Exception::class)

        coVerifyOnce { serviceSpy.getChannel(any()) }
        coVerifyOnce { serviceSpy.channelClassification(any<ChannelDocument>(), any(), any(), any(), any()) }
        coVerifyOnce { serviceSpy.rename(any<ChannelDocument>(), any(), any()) }
        coVerifyOnce { serviceSpy.dischargeChannel(any(), any(), any(), any(), any(), any()) }
        coVerifyOnce { serviceSpy.span(any(), any(), any()) }
        coVerify { messageFirestoreService wasNot called }
        coVerify { channelNotificationService wasNot called }
    }

    @OptIn(FirestoreContextUsage::class)
    @Test
    fun `#dischargeChannel should returns error on channelClassification`() = runBlocking {
        val channelDocument = channelDocument.copy(
            name = "name",
            kind = ChannelKind.CHAT,
            category = ChannelCategory.ADMINISTRATIVE,
            subCategory = null
        )

        val name = "new_name"
        val kind = ChannelKind.CHANNEL
        val staffId = staffId
        val category = ChannelCategory.ADMINISTRATIVE
        val subCategory = ChannelSubCategory.ACUTE

        coEvery { serviceSpy.getChannel(channelId) } returns channelDocument.success()

        coEvery {
            serviceSpy.channelClassification(channelDocument, kind, category, subCategory, null)
        } returns Exception("").failure()

        val result = serviceSpy.dischargeChannel(
            channelId = channelId,
            name = name,
            kind = kind,
            staffId = staffId,
            category = category,
            subCategory = subCategory
        )
        assertThat(result).isFailureOfType(Exception::class)

        coVerifyOnce { serviceSpy.getChannel(any()) }
        coVerifyOnce { serviceSpy.channelClassification(any<ChannelDocument>(), any(), any(), any(), any()) }
        coVerifyOnce { serviceSpy.dischargeChannel(any(), any(), any(), any(), any(), any()) }
        coVerifyOnce { serviceSpy.span(any(), any(), any()) }
        coVerify { messageFirestoreService wasNot called }
        coVerify { channelNotificationService wasNot called }
    }

    @OptIn(FirestoreContextUsage::class)
    @Test
    fun `#dischargeChannel should returns error on getChannel`() = runBlocking {
        coEvery { serviceSpy.getChannel(channelId) } returns NotFoundException().failure()

        val result = serviceSpy.dischargeChannel(
            channelId = channelId,
            name = channelDocument.name!!,
            kind = channelDocument.kind!!,
            staffId = staffId,
            category = channelDocument.category,
            subCategory = channelDocument.subCategory
        )
        assertThat(result).isFailureOfType(NotFoundException::class)

        coVerifyOnce { serviceSpy.getChannel(any()) }
        coVerifyOnce { serviceSpy.dischargeChannel(any(), any(), any(), any(), any(), any()) }
        coVerifyOnce { serviceSpy.span(any(), any(), any()) }
        coVerify { messageFirestoreService wasNot called }
        coVerify { channelNotificationService wasNot called }
    }

    @OptIn(FirestoreContextUsage::class)
    @Test
    fun `#dischargeChannel returns channel id and discharge virtual clinic by missing member interaction`() =
        mockTimestamp(timestamp) {
            val channelDocument = channelDocument.copy(
                name = "name",
                kind = ChannelKind.CHAT,
                category = ChannelCategory.ASSISTANCE,
                subCategory = ChannelSubCategory.VIRTUAL_CLINIC
            )

            val name = "name"
            val kind = ChannelKind.CHANNEL
            val staffId = staffId
            val category = ChannelCategory.ASSISTANCE
            val subCategory = ChannelSubCategory.VIRTUAL_CLINIC

            val channelDocumentClassified = channelDocument.copy(
                kind = kind,
                category = category,
                subCategory = subCategory
            )

            val channelDocumentRenamed = channelDocumentClassified.copy(name = name)

            val channelDocumentArchived = channelDocumentRenamed.copy(
                kind = ChannelKind.CHANNEL,
                becameAsyncAt = timestamp
            )

            coEvery { serviceSpy.getChannel(channelId) } returns channelDocument.success()

            coEvery {
                serviceSpy.channelClassification(channelDocument, kind, category, subCategory, null)
            } returns channelDocumentClassified.success()

            coEvery {
                serviceSpy.rename(
                    channelDocumentClassified,
                    name,
                    staffId
                )
            } returns channelDocumentRenamed.success()

            coEvery {
                serviceSpy.archiveChannel(channelDocumentRenamed, staffId, ChannelArchivedReason.DISCHARGE, true)
            } returns channelDocumentArchived.success()

            coEvery {
                virtualClinicMessagesService.sendMessageArchiveByMemberVideoCallRejected(channelId, personId)
            } returns channelId.success()

            coEvery {
                virtualClinicMessagesService.sendMessageArchiveByMissingMemberInteraction(channelId, personId)
            } returns messageId.success()

            coEvery {
                channelNotificationService.notify(channelDocumentArchived, ChannelChangeAction.DISCHARGED)
            } returns mockk()

            val result = serviceSpy.dischargeChannel(
                channelId = channelId,
                name = name,
                kind = kind,
                staffId = staffId,
                category = category,
                subCategory = subCategory
            )
            assertThat(result).isSuccessWithData(channelId)

            coVerifyOnce { serviceSpy.span(any(), any(), any()) }
            coVerifyOnce { serviceSpy.getChannel(any()) }
            coVerifyOnce { serviceSpy.channelClassification(any<ChannelDocument>(), any(), any(), any(), any()) }
            coVerifyOnce { serviceSpy.rename(any<ChannelDocument>(), any(), any()) }
            coVerifyOnce { serviceSpy.archiveChannel(any<ChannelDocument>(), any(), any(), any()) }
            coVerifyOnce { serviceSpy.dischargeChannel(any(), any(), any(), any(), any(), any()) }
            coVerifyOnce { virtualClinicMessagesService.sendMessageArchiveByMemberVideoCallRejected(any(), any()) }
            coVerifyOnce { virtualClinicMessagesService.sendMessageArchiveByMissingMemberInteraction(any(), any()) }
            coVerifyOnce { channelNotificationService.notify(any<ChannelDocument>(), any(), any(), any()) }
            coVerify { personService wasNot called }
            coVerify { messageFirestoreService wasNot called }
        }

    @OptIn(FirestoreContextUsage::class)
    @Test
    fun `#dischargeChannel returns channel id and discharge virtual clinic by video call rejection`() =
        mockTimestamp(timestamp) {
            val channelDocument = channelDocument.copy(
                name = "name",
                kind = ChannelKind.CHAT,
                category = ChannelCategory.ASSISTANCE,
                subCategory = ChannelSubCategory.VIRTUAL_CLINIC
            )

            val name = "name"
            val kind = ChannelKind.CHANNEL
            val staffId = staffId
            val category = ChannelCategory.ASSISTANCE
            val subCategory = ChannelSubCategory.VIRTUAL_CLINIC

            val channelDocumentClassified = channelDocument.copy(
                kind = kind,
                category = category,
                subCategory = subCategory
            )

            val channelDocumentRenamed = channelDocumentClassified.copy(name = name)

            val channelDocumentArchived = channelDocumentRenamed.copy(
                kind = ChannelKind.CHANNEL,
                becameAsyncAt = timestamp
            )

            coEvery { serviceSpy.getChannel(channelId) } returns channelDocument.success()

            coEvery {
                serviceSpy.channelClassification(channelDocument, kind, category, subCategory, null)
            } returns channelDocumentClassified.success()

            coEvery {
                serviceSpy.rename(
                    channelDocumentClassified,
                    name,
                    staffId
                )
            } returns channelDocumentRenamed.success()

            coEvery {
                serviceSpy.archiveChannel(channelDocumentRenamed, staffId, ChannelArchivedReason.DISCHARGE, true)
            } returns channelDocumentArchived.success()

            coEvery {
                virtualClinicMessagesService.sendMessageArchiveByMemberVideoCallRejected(channelId, personId)
            } returns messageId.success()

            coEvery {
                virtualClinicMessagesService.sendMessageArchiveByMissingMemberInteraction(channelId, personId)
            } returns channelId.success()

            coEvery {
                channelNotificationService.notify(channelDocumentArchived, ChannelChangeAction.DISCHARGED)
            } returns mockk()

            val result = serviceSpy.dischargeChannel(
                channelId = channelId,
                name = name,
                kind = kind,
                staffId = staffId,
                category = category,
                subCategory = subCategory
            )
            assertThat(result).isSuccessWithData(channelId)

            coVerifyOnce { serviceSpy.span(any(), any(), any()) }
            coVerifyOnce { serviceSpy.getChannel(any()) }
            coVerifyOnce { serviceSpy.channelClassification(any<ChannelDocument>(), any(), any(), any(), any()) }
            coVerifyOnce { serviceSpy.rename(any<ChannelDocument>(), any(), any()) }
            coVerifyOnce { serviceSpy.archiveChannel(any<ChannelDocument>(), any(), any(), any()) }
            coVerifyOnce { serviceSpy.dischargeChannel(any(), any(), any(), any(), any(), any()) }
            coVerifyOnce { virtualClinicMessagesService.sendMessageArchiveByMemberVideoCallRejected(any(), any()) }
            coVerifyOnce { virtualClinicMessagesService.sendMessageArchiveByMissingMemberInteraction(any(), any()) }
            coVerifyOnce { channelNotificationService.notify(any<ChannelDocument>(), any(), any(), any()) }
            coVerify { personService wasNot called }
            coVerify { messageFirestoreService wasNot called }
        }

    @OptIn(FirestoreContextUsage::class)
    @Test
    fun `#dischargeChannel returns channel id and discharge virtual clinic by success flow`() =
        mockTimestamp(timestamp) {
            val channelDocument = channelDocument.copy(
                name = "name",
                kind = ChannelKind.CHAT,
                category = ChannelCategory.ASSISTANCE,
                subCategory = ChannelSubCategory.VIRTUAL_CLINIC
            )

            val name = "name"
            val kind = ChannelKind.CHANNEL
            val staffId = staffId
            val category = ChannelCategory.ASSISTANCE
            val subCategory = ChannelSubCategory.VIRTUAL_CLINIC

            val channelDocumentClassified = channelDocument.copy(
                kind = kind,
                category = category,
                subCategory = subCategory
            )

            val channelDocumentRenamed = channelDocumentClassified.copy(name = name)

            val channelDocumentArchived = channelDocumentRenamed.copy(
                kind = ChannelKind.CHANNEL,
                becameAsyncAt = timestamp
            )

            coEvery { serviceSpy.getChannel(channelId) } returns channelDocument.success()

            coEvery {
                serviceSpy.channelClassification(channelDocument, kind, category, subCategory, null)
            } returns channelDocumentClassified.success()

            coEvery {
                serviceSpy.rename(
                    channelDocumentClassified,
                    name,
                    staffId
                )
            } returns channelDocumentRenamed.success()

            coEvery {
                serviceSpy.archiveChannel(channelDocumentRenamed, staffId, ChannelArchivedReason.DISCHARGE, true)
            } returns channelDocumentArchived.success()

            coEvery {
                virtualClinicMessagesService.sendMessageArchiveByMemberVideoCallRejected(channelId, personId)
            } returns channelId.success()

            coEvery {
                virtualClinicMessagesService.sendMessageArchiveByMissingMemberInteraction(channelId, personId)
            } returns channelId.success()

            coEvery { personService.get(personId) } returns person.success()

            coEvery {
                messageFirestoreService.add(
                    channelId = channelId,
                    messageDocument = MessageDocument(
                        content = "",
                        source = "DISCHARGE",
                        type = MessageType.TEXT,
                        userId = "",
                        createdAt = timestamp
                    ),
                    isPrivate = false
                )
            } returns channelId.success()

            coEvery {
                channelNotificationService.notify(channelDocumentArchived, ChannelChangeAction.DISCHARGED)
            } returns mockk()

            val result = serviceSpy.dischargeChannel(
                channelId = channelId,
                name = name,
                kind = kind,
                staffId = staffId,
                category = category,
                subCategory = subCategory
            )
            assertThat(result).isSuccessWithData(channelId)

            coVerifyOnce { serviceSpy.span(any(), any(), any()) }
            coVerifyOnce { serviceSpy.dischargeChannel(any(), any(), any(), any(), any(), any()) }
            coVerifyOnce { serviceSpy.getChannel(any()) }
            coVerifyOnce { serviceSpy.channelClassification(any<ChannelDocument>(), any(), any(), any(), any()) }
            coVerifyOnce { serviceSpy.rename(any<ChannelDocument>(), any(), any()) }
            coVerifyOnce { serviceSpy.archiveChannel(any<ChannelDocument>(), any(), any(), any()) }
            coVerifyOnce { virtualClinicMessagesService.sendMessageArchiveByMemberVideoCallRejected(any(), any()) }
            coVerifyOnce { virtualClinicMessagesService.sendMessageArchiveByMissingMemberInteraction(any(), any()) }
            coVerifyOnce { personService.get(any()) }
            coVerifyOnce { messageFirestoreService.add(any(), any(), any()) }
            coVerifyOnce { channelNotificationService.notify(any<ChannelDocument>(), any(), any(), any()) }
        }

    @OptIn(FirestoreContextUsage::class)
    @Test
    fun `#reRouting returns chat id and change category from ADMINISTRATIVE when is valid to send messages`() =
        runBlocking {
            val memberRedirectMessageContent = "Voce sera redirecionado"
            val enabledDate = "2024-08-27"
            val createdAt = LocalDate.parse(enabledDate).atStartOfDay().plusDays(1)
            withFeatureFlags(
                FeatureNamespace.CHANNELS to mapOf(
                    "redirect_assistance_channel_to_administrative_message" to memberRedirectMessageContent,
                    "redirect_assistance_channel_to_administrative_enabled_date" to enabledDate
                )
            ) {
                mockTimestamp {
                    val chat = channelDocument.copy(
                        name = null,
                        kind = ChannelKind.CHAT,
                        category = ChannelCategory.ASSISTANCE,
                        subCategory = null,
                        subCategoryClassifier = null,
                        routingTo = listOf("allAssistance"),
                        createdAt = createdAt.toTimestamp()
                    )
                    val expectedChat = chat.copy(
                        staff = mutableMapOf(),
                        staffIds = emptyList(),
                        category = ChannelCategory.ADMINISTRATIVE,
                        subCategory = null,
                        subCategoryClassifier = null,
                        tags = listOf(redirectTag)
                    )

                    val redirectInfo = ReRoutingExtraInfo(
                        reason = "motivo",
                        currentStaffId = staffId.toUUID()
                    )

                    val memberRedirectMessage = MessageDocument(
                        userId = "",
                        content = memberRedirectMessageContent,
                        type = MessageType.TEXT,
                        channelCategory = ChannelCategory.ADMINISTRATIVE
                    )
                    val privateMessage = MessageDocument(
                        userId = redirectInfo.currentStaffId!!.toString(),
                        content = redirectInfo.reason!!,
                        type = MessageType.TEXT,
                        channelCategory = ChannelCategory.ADMINISTRATIVE
                    )

                    coEvery { serviceSpy.getChannel(channelId) } returns chat.success()
                    coEvery {
                        serviceSpy.removeStaff(
                            channel = chat,
                            staffId = staffId,
                            requesterStaffId = staffId,
                            notify = true,
                            checkOwner = false,
                            checkSize = false
                        )
                    } returns channelId.success()
                    coEvery {
                        channelFirestoreService.updateFields(
                            channelId,
                            mapOf(
                                ChannelDocument::name to "Atendimento Administrativo",
                                ChannelDocument::category to ChannelCategory.ADMINISTRATIVE,
                                ChannelDocument::subCategory to null,
                                ChannelDocument::subCategoryClassifier to null,
                                ChannelDocument::routingTo to emptyList<String>(),
                                ChannelDocument::tags to listOf(redirectTag),
                                ChannelDocument::hideMemberInput to false,
                                ChannelDocument::canBeArchived to true
                            )
                        )
                    } returns channelId.success()
                    coEvery {
                        channelNotificationService.notify(expectedChat, ChannelChangeAction.RE_ROUTING)
                    } returns mockk()
                    coEvery {
                        messageFirestoreService.add(channelId, memberRedirectMessage)
                    } returns messageId.success()
                    coEvery {
                        messageFirestoreService.add(channelId, privateMessage, isPrivate = true)
                    } returns messageId.success()

                    val result = serviceSpy.reRouting(channelId, ChannelCategory.ADMINISTRATIVE, null, redirectInfo)
                    assertThat(result).isSuccessWithData(channelId)

                    coVerifyOnce { serviceSpy.span(any(), any(), any()) }
                    coVerifyOnce { serviceSpy.reRouting(any(), any(), any(), any()) }
                    coVerifyOnce { serviceSpy.getChannel(any()) }
                    coVerifyOnce { serviceSpy.removeStaff(any(), any(), any(), any(), any(), any()) }
                    coVerifyOnce { channelFirestoreService.updateFields(any(), any()) }
                    coVerifyOnce { channelNotificationService.notify(any<ChannelDocument>(), any(), any(), any()) }
                    coVerify(exactly = 2) { messageFirestoreService.add(any(), any(), any()) }
                }
            }
        }

    @OptIn(FirestoreContextUsage::class)
    @Test
    fun `#reRouting returns chat id and change category from ADMINISTRATIVE when is invalid to send messages`() =
        runBlocking {
            val memberRedirectMessageContent = "Voce sera redirecionado"
            val enabledDate = "2024-08-27"
            val createdAt = LocalDate.parse(enabledDate).atStartOfDay().minusDays(1)
            withFeatureFlags(
                FeatureNamespace.CHANNELS to mapOf(
                    "redirect_assistance_channel_to_administrative_message" to memberRedirectMessageContent,
                    "redirect_assistance_channel_to_administrative_enabled_date" to enabledDate
                )
            ) {
                mockTimestamp {
                    val chat = channelDocument.copy(
                        name = null,
                        kind = ChannelKind.CHAT,
                        category = ChannelCategory.ASSISTANCE,
                        subCategory = null,
                        subCategoryClassifier = null,
                        routingTo = listOf("allAssistance"),
                        createdAt = createdAt.toTimestamp()
                    )
                    val expectedChat = chat.copy(
                        staff = mutableMapOf(),
                        staffIds = emptyList(),
                        category = ChannelCategory.ADMINISTRATIVE,
                        subCategory = null,
                        subCategoryClassifier = null,
                        tags = listOf(redirectTag)
                    )

                    val redirectInfo = ReRoutingExtraInfo(
                        reason = "motivo",
                        currentStaffId = staffId.toUUID()
                    )

                    coEvery { serviceSpy.getChannel(channelId) } returns chat.success()
                    coEvery {
                        serviceSpy.removeStaff(
                            channel = chat,
                            staffId = staffId,
                            requesterStaffId = staffId,
                            notify = true,
                            checkOwner = false,
                            checkSize = false
                        )
                    } returns channelId.success()
                    coEvery {
                        channelFirestoreService.updateFields(
                            channelId,
                            mapOf(
                                ChannelDocument::name to "Atendimento Administrativo",
                                ChannelDocument::category to ChannelCategory.ADMINISTRATIVE,
                                ChannelDocument::subCategory to null,
                                ChannelDocument::subCategoryClassifier to null,
                                ChannelDocument::routingTo to emptyList<String>(),
                                ChannelDocument::tags to listOf(redirectTag),
                                ChannelDocument::hideMemberInput to false,
                                ChannelDocument::canBeArchived to true
                            )
                        )
                    } returns channelId.success()
                    coEvery {
                        channelNotificationService.notify(expectedChat, ChannelChangeAction.RE_ROUTING)
                    } returns mockk()

                    val result = serviceSpy.reRouting(channelId, ChannelCategory.ADMINISTRATIVE, null, redirectInfo)
                    assertThat(result).isSuccessWithData(channelId)

                    coVerifyOnce { serviceSpy.span(any(), any(), any()) }
                    coVerifyOnce { serviceSpy.reRouting(any(), any(), any(), any()) }
                    coVerifyOnce { serviceSpy.getChannel(any()) }
                    coVerifyOnce { serviceSpy.removeStaff(any(), any(), any(), any(), any(), any()) }
                    coVerifyOnce { channelFirestoreService.updateFields(any(), any()) }
                    coVerifyOnce { channelNotificationService.notify(any<ChannelDocument>(), any(), any(), any()) }
                    coVerify { messageFirestoreService wasNot called }
                }
            }
        }

    @OptIn(FirestoreContextUsage::class)
    @Test
    fun `#reRouting returns chat id and change category from ADMINISTRATIVE when is invalid to send messages using default date`() =
        runBlocking {
            val enabledDate = "2024-08-27"
            val createdAt = LocalDate.parse(enabledDate).atStartOfDay()
            mockTimestamp {
                val chat = channelDocument.copy(
                    name = null,
                    kind = ChannelKind.CHAT,
                    category = ChannelCategory.ASSISTANCE,
                    subCategory = null,
                    subCategoryClassifier = null,
                    routingTo = listOf("allAssistance"),
                    createdAt = createdAt.toTimestamp()
                )
                val expectedChat = chat.copy(
                    staff = mutableMapOf(),
                    staffIds = emptyList(),
                    category = ChannelCategory.ADMINISTRATIVE,
                    subCategory = null,
                    subCategoryClassifier = null,
                    tags = listOf(redirectTag)
                )

                val redirectInfo = ReRoutingExtraInfo(
                    reason = "motivo",
                    currentStaffId = staffId.toUUID()
                )

                coEvery { serviceSpy.getChannel(channelId) } returns chat.success()
                coEvery {
                    serviceSpy.removeStaff(
                        channel = chat,
                        staffId = staffId,
                        requesterStaffId = staffId,
                        notify = true,
                        checkOwner = false,
                        checkSize = false
                    )
                } returns channelId.success()
                coEvery {
                    channelFirestoreService.updateFields(
                        channelId,
                        mapOf(
                            ChannelDocument::name to "Atendimento Administrativo",
                            ChannelDocument::category to ChannelCategory.ADMINISTRATIVE,
                            ChannelDocument::subCategory to null,
                            ChannelDocument::subCategoryClassifier to null,
                            ChannelDocument::routingTo to emptyList<String>(),
                            ChannelDocument::tags to listOf(redirectTag),
                            ChannelDocument::hideMemberInput to false,
                            ChannelDocument::canBeArchived to true
                        )
                    )
                } returns channelId.success()
                coEvery {
                    channelNotificationService.notify(expectedChat, ChannelChangeAction.RE_ROUTING)
                } returns mockk()

                val result = serviceSpy.reRouting(channelId, ChannelCategory.ADMINISTRATIVE, null, redirectInfo)
                assertThat(result).isSuccessWithData(channelId)

                coVerifyOnce { serviceSpy.span(any(), any(), any()) }
                coVerifyOnce { serviceSpy.reRouting(any(), any(), any(), any()) }
                coVerifyOnce { serviceSpy.getChannel(any()) }
                coVerifyOnce { serviceSpy.removeStaff(any(), any(), any(), any(), any(), any()) }
                coVerifyOnce { channelFirestoreService.updateFields(any(), any()) }
                coVerifyOnce { channelNotificationService.notify(any<ChannelDocument>(), any(), any(), any()) }
                coVerify { messageFirestoreService wasNot called }
            }
        }

    @OptIn(FirestoreContextUsage::class)
    @Test
    fun `#reRouting returns chat id and change category to ASSISTANCE`() = runBlocking {
        val chat = channelDocument.copy(
            kind = ChannelKind.CHAT,
            category = ChannelCategory.ADMINISTRATIVE,
            subCategory = null,
            subCategoryClassifier = null,
            routingTo = listOf("allAdministrative"),
            staff = mutableMapOf(
                staffId to channelStaffOwner.copy(onBackground = true),
                anotherStaffId to staffChannel02
            )
        )
        val expectedChat = chat.copy(
            staff = mutableMapOf(),
            staffIds = emptyList(),
            category = ChannelCategory.ASSISTANCE,
            subCategory = ChannelSubCategory.ACUTE,
            tags = listOf(redirectTag)
        )

        coEvery { serviceSpy.getChannel(channelId) } returns chat.success()
        coEvery {
            serviceSpy.removeStaff(
                channel = chat,
                staffId = staffId,
                requesterStaffId = staffId,
                notify = true,
                checkOwner = false,
                checkSize = false
            )
        } returns channelId.success()
        coEvery {
            serviceSpy.removeStaff(
                channel = chat,
                staffId = anotherStaffId,
                requesterStaffId = anotherStaffId,
                notify = true,
                checkOwner = false,
                checkSize = false
            )
        } returns channelId.success()
        coEvery {
            channelFirestoreService.updateFields(
                channelId,
                mapOf(
                    ChannelDocument::name to "channel_name",
                    ChannelDocument::category to ChannelCategory.ASSISTANCE,
                    ChannelDocument::subCategory to ChannelSubCategory.ACUTE,
                    ChannelDocument::subCategoryClassifier to null,
                    ChannelDocument::routingTo to emptyList<String>(),
                    ChannelDocument::tags to listOf(redirectTag),
                    ChannelDocument::hideMemberInput to false,
                    ChannelDocument::canBeArchived to true
                )
            )
        } returns channelId.success()
        coEvery {
            channelNotificationService.notify(expectedChat, ChannelChangeAction.RE_ROUTING)
        } returns mockk()

        val result = serviceSpy.reRouting(channelId, ChannelCategory.ASSISTANCE, ChannelSubCategory.ACUTE)
        assertThat(result).isSuccessWithData(channelId)

        coVerifyOnce { serviceSpy.span(any(), any(), any()) }
        coVerifyOnce { serviceSpy.reRouting(any(), any(), any()) }
        coVerifyOnce { serviceSpy.getChannel(any()) }
        coVerify(exactly = 2) { serviceSpy.removeStaff(any(), any(), any(), any(), any(), any()) }
        coVerifyOnce { channelFirestoreService.updateFields(any(), any()) }
        coVerifyOnce { channelNotificationService.notify(any<ChannelDocument>(), any(), any(), any()) }
        coVerify { messageFirestoreService wasNot called }
    }

    @OptIn(FirestoreContextUsage::class)
    @Test
    fun `#reRouting returns chat id and change category to VIRTUAL_CLINIC`() = runBlocking {
        val chat = channelDocument.copy(
            name = null,
            kind = ChannelKind.CHAT,
            category = ChannelCategory.ASSISTANCE,
            subCategory = ChannelSubCategory.SCREENING,
            subCategoryClassifier = null,
            routingTo = emptyList()
        )
        val expectedChat = chat.copy(
            staff = mutableMapOf(),
            staffIds = emptyList(),
            category = ChannelCategory.ASSISTANCE,
            subCategory = ChannelSubCategory.VIRTUAL_CLINIC,
            tags = listOf(redirectTag)
        )

        coEvery { serviceSpy.getChannel(channelId) } returns chat.success()
        coEvery {
            serviceSpy.removeStaff(
                channel = chat,
                staffId = staffId,
                requesterStaffId = staffId,
                notify = true,
                checkOwner = false,
                checkSize = false
            )
        } returns channelId.success()
        coEvery {
            serviceSpy.removeStaff(
                channel = chat,
                staffId = anotherStaffId,
                requesterStaffId = anotherStaffId,
                notify = true,
                checkOwner = false,
                checkSize = false
            )
        } returns channelId.success()
        coEvery {
            channelFirestoreService.updateFields(
                channelId,
                mapOf(
                    ChannelDocument::name to "Atendimento Assistencial",
                    ChannelDocument::category to ChannelCategory.ASSISTANCE,
                    ChannelDocument::subCategory to ChannelSubCategory.VIRTUAL_CLINIC,
                    ChannelDocument::subCategoryClassifier to null,
                    ChannelDocument::routingTo to emptyList<String>(),
                    ChannelDocument::tags to listOf(redirectTag),
                    ChannelDocument::hideMemberInput to true,
                    ChannelDocument::canBeArchived to false
                )
            )
        } returns channelId.success()
        coEvery {
            channelNotificationService.notify(expectedChat, ChannelChangeAction.RE_ROUTING)
        } returns mockk()

        val result = serviceSpy.reRouting(channelId, ChannelCategory.ASSISTANCE, ChannelSubCategory.VIRTUAL_CLINIC)
        assertThat(result).isSuccessWithData(channelId)

        coVerifyOnce { serviceSpy.span(any(), any(), any()) }
        coVerifyOnce { serviceSpy.reRouting(any(), any(), any()) }
        coVerifyOnce { serviceSpy.getChannel(any()) }
        coVerifyOnce { serviceSpy.removeStaff(any(), any(), any(), any(), any(), any()) }
        coVerifyOnce { channelFirestoreService.updateFields(any(), any()) }
        coVerifyOnce { channelNotificationService.notify(any<ChannelDocument>(), any(), any(), any()) }
        coVerify { messageFirestoreService wasNot called }
    }

    @OptIn(FirestoreContextUsage::class)
    @Test
    fun `#archiveChannel returns channel id and set status as archived for virtual clinic chat when force and missing member interaction`() =
        mockTimestamp { timestamp ->
            val channelDocument = channelDocument.copy(
                kind = ChannelKind.CHAT,
                virtualClinicStatusControl = VirtualClinicStatusControl.MISSING_MEMBER_INTERACTION
            )

            val expectedChat = channelDocument.copy(
                status = ChannelStatus.ARCHIVED,
                archivedAt = timestamp,
                archivedReason = archivedReason,
                hideMemberInput = true,
                csatUrl = csatFormUrl,
                digitalCareQueueModalInfo = null,
                videoCall = null,
                virtualClinicStatusControl = null
            )
            val extraInfo = listOf(
                ChannelHistoryExtraInfo(
                    key = ExtraInfoType.CSAT_QUESTIONNAIRE_KEY,
                    value = "TEST_CSAT_AA"
                )
            )

            coEvery { serviceSpy.getChannel(channelId) } returns channelDocument.success()

            coEvery {
                channelFirestoreService.updateFields(
                    channelId,
                    mapOf(
                        ChannelDocument::status to ChannelStatus.ARCHIVED,
                        ChannelDocument::archivedAt to timestamp,
                        ChannelDocument::archivedReason to archivedReason,
                        ChannelDocument::csatUrl to csatFormUrl,
                        ChannelDocument::hideMemberInput to true,
                        ChannelDocument::followUp to FieldValue.delete(),
                        ChannelDocument::digitalCareQueueModalInfo to null,
                        ChannelDocument::videoCall to null,
                        ChannelDocument::virtualClinicStatusControl to null
                    )
                )
            } returns channelId.success()
            coEvery {
                messageFirestoreService.add(
                    channelId,
                    MessageDocument(
                        aliceId = channelStaffOwner.id,
                        userId = channelStaffOwner.id,
                        content = "Conversa encerrada",
                        type = MessageType.CHANNEL_ARCHIVED,
                        createdAt = timestamp
                    )
                )
            } returns channelId.success()

            coEvery {
                channelNotificationService.notify(expectedChat, ChannelChangeAction.ARCHIVE, extraInfo = extraInfo)
            } returns mockk()

            coEvery {
                channelNotificationService.produceGenericEvent(
                    ChannelArchivedByInactivityEvent(
                        channelId = expectedChat.id!!,
                        personId = expectedChat.personId.toPersonId(),
                        channel = expectedChat
                    )
                )
            } returns mockk()

            val result = serviceSpy.archiveChannel(channelId, channelStaffOwner.id, archivedReason, true)
            assertThat(result).isSuccessWithData(channelId)

            coVerify(exactly = 3) { serviceSpy.span(any(), any(), any()) }
            coVerifyOnce { serviceSpy.archiveChannel(any<String>(), any(), any(), any()) }
            coVerifyOnce { serviceSpy.archiveChannel(any<ChannelDocument>(), any(), any(), any()) }
            coVerifyOnce { serviceSpy.getChannel(any()) }
            coVerifyOnce { channelFirestoreService.updateFields(any(), any()) }
            coVerifyOnce { messageFirestoreService.add(any(), any()) }
            coVerifyOnce { channelNotificationService.notify(any<ChannelDocument>(), any(), any(), any()) }
            coVerifyOnce { channelNotificationService.produceGenericEvent(any()) }
        }

    @OptIn(FirestoreContextUsage::class)
    @Test
    fun `#archiveChannel returns channel id and set status as archived for virtual clinic chat when force and reject video call`() =
        mockTimestamp { timestamp ->
            val channelDocument = channelDocument.copy(
                kind = ChannelKind.CHAT,
                virtualClinicStatusControl = VirtualClinicStatusControl.VIDEO_CALL_REJECTED
            )

            val expectedChat = channelDocument.copy(
                status = ChannelStatus.ARCHIVED,
                archivedAt = timestamp,
                archivedReason = archivedReason,
                hideMemberInput = true,
                csatUrl = csatFormUrl,
                digitalCareQueueModalInfo = null,
                videoCall = null,
                virtualClinicStatusControl = null
            )
            val extraInfo = listOf(
                ChannelHistoryExtraInfo(
                    key = ExtraInfoType.CSAT_QUESTIONNAIRE_KEY,
                    value = "TEST_CSAT_AA"
                )
            )

            coEvery { serviceSpy.getChannel(channelId) } returns channelDocument.success()

            coEvery {
                channelFirestoreService.updateFields(
                    channelId,
                    mapOf(
                        ChannelDocument::status to ChannelStatus.ARCHIVED,
                        ChannelDocument::archivedAt to timestamp,
                        ChannelDocument::archivedReason to archivedReason,
                        ChannelDocument::csatUrl to csatFormUrl,
                        ChannelDocument::hideMemberInput to true,
                        ChannelDocument::followUp to FieldValue.delete(),
                        ChannelDocument::digitalCareQueueModalInfo to null,
                        ChannelDocument::videoCall to null,
                        ChannelDocument::virtualClinicStatusControl to null
                    )
                )
            } returns channelId.success()
            coEvery {
                messageFirestoreService.add(
                    channelId,
                    MessageDocument(
                        aliceId = channelStaffOwner.id,
                        userId = channelStaffOwner.id,
                        content = "Conversa encerrada",
                        type = MessageType.CHANNEL_ARCHIVED,
                        createdAt = timestamp
                    )
                )
            } returns channelId.success()

            coEvery {
                channelNotificationService.notify(expectedChat, ChannelChangeAction.ARCHIVE, extraInfo = extraInfo)
            } returns mockk()

            coEvery {
                channelNotificationService.produceGenericEvent(
                    ChannelArchivedByInactivityEvent(
                        channelId = expectedChat.id!!,
                        personId = expectedChat.personId.toPersonId(),
                        channel = expectedChat
                    )
                )
            } returns mockk()

            val result = serviceSpy.archiveChannel(channelId, channelStaffOwner.id, archivedReason, true)
            assertThat(result).isSuccessWithData(channelId)

            coVerify(exactly = 3) { serviceSpy.span(any(), any(), any()) }
            coVerifyOnce { serviceSpy.archiveChannel(any<String>(), any(), any(), any()) }
            coVerifyOnce { serviceSpy.archiveChannel(any<ChannelDocument>(), any(), any(), any()) }
            coVerifyOnce { serviceSpy.getChannel(any()) }
            coVerifyOnce { channelFirestoreService.updateFields(any(), any()) }
            coVerifyOnce { messageFirestoreService.add(any(), any()) }
            coVerifyOnce { channelNotificationService.notify(any<ChannelDocument>(), any(), any(), any()) }
            coVerifyOnce { channelNotificationService.produceGenericEvent(any()) }
        }

    @OptIn(FirestoreContextUsage::class)
    @Test
    fun `#updateWithZendeskTicketData updates channel with zendesk ticket id, send messages, update tags and notify event`() =
        mockTimestamp { timestamp ->
            withFeatureFlag(
                FeatureNamespace.CHANNELS,
                "zendesk_redirection_message",
                "Olá, @firstname! Vou redirecionar seu caso para o setor responsável que te ajudará com o assunto.<br><br>Essa <b>transferência pode levar alguns minutos</b> e assim que for iniciada você receberá uma notificação, mas para garantir que deu tudo certo, seguirei por aqui até que seu atendimento inicie. Combinado?"
            ) {
                val channelDocument = channelDocument.copy(
                    staff = mutableMapOf(staffId to channelStaffOwner, anotherStaffId to staffChannel02),
                    tags = listOf("TAG1")
                )

                val updatedChannel = channelDocument.copy(
                    tags = listOf("TAG1", "aguardando_n2"),
                    zendeskTicketId = zendeskTicketId
                )

                coEvery { serviceSpy.getChannel(channelId) } returns channelDocument.success()

                coEvery {
                    channelFirestoreService.updateFields(
                        channelId,
                        mapOf(ChannelDocument::zendeskTicketId to zendeskTicketId)
                    )
                } returns channelId.success()

                coEvery {
                    messageFirestoreService.add(
                        channelId,
                        MessageDocument(
                            aliceId = staffId,
                            userId = staffId,
                            type = MessageType.TEXT,
                            content = "<a href='https://alice709.zendesk.com/agent/tickets/$zendeskTicketId' target='_blank'>Acesse aqui</a> o caso escalado para N2 no Zendesk",
                            createdAt = timestamp
                        ),
                        true
                    )
                } returns channelId.success()

                coEvery { personService.get(personId) } returns person.success()

                coEvery {
                    messageFirestoreService.add(
                        channelId,
                        MessageDocument(
                            aliceId = staffId,
                            userId = staffId,
                            type = MessageType.TEXT,
                            content = "Olá, José! Vou redirecionar seu caso para o setor responsável que te ajudará com o assunto.<br><br>Essa <b>transferência pode levar alguns minutos</b> e assim que for iniciada você receberá uma notificação, mas para garantir que deu tudo certo, seguirei por aqui até que seu atendimento inicie. Combinado?",
                            createdAt = timestamp
                        ),
                        false
                    )
                } returns channelId.success()

                coEvery { serviceSpy.setTags(channelId, listOf("TAG1", "aguardando_n2")) } returns channelId.success()

                coEvery {
                    channelNotificationService.notify(
                        updatedChannel,
                        ChannelChangeAction.ESCALATED_TO_N2
                    )
                } returns mockk()

                val result = serviceSpy.updateWithZendeskTicketData(channelId, zendeskTicketId)
                assertThat(result).isSuccessWithData(channelId)

                coVerifyOnce { serviceSpy.setZendeskTicketId(any(), any()) }
                coVerifyOnce { serviceSpy.updateWithZendeskTicketData(any(), any()) }
                coVerifyOnce { serviceSpy.getChannel(any()) }
                coVerifyOnce { messageFirestoreService.add(any(), any(), true) }
                coVerifyOnce { personService.get(any()) }
                coVerifyOnce { messageFirestoreService.add(any(), any(), false) }
                coVerifyOnce { serviceSpy.setTags(any(), any()) }
                coVerifyOnce { channelFirestoreService.updateFields(any(), any()) }
                coVerifyOnce { channelNotificationService.notify(any<ChannelDocument>(), any()) }
                coVerify { virtualClinicMessagesService wasNot called }
            }
        }

    @OptIn(FirestoreContextUsage::class)
    @Test
    fun `#updateWithZendeskTicketData updates channel with first staff if not exists owner`() =
        mockTimestamp { timestamp ->
            withFeatureFlag(
                FeatureNamespace.CHANNELS,
                "zendesk_redirection_message",
                "Olá, @firstname! Vou redirecionar seu caso para o setor responsável que te ajudará com o assunto.<br><br>Essa <b>transferência pode levar alguns minutos</b> e assim que for iniciada você receberá uma notificação, mas para garantir que deu tudo certo, seguirei por aqui até que seu atendimento inicie. Combinado?"
            ) {
                val staffNotOwner = channelStaffOwner.copy(owner = false)
                val channelDocument = channelDocument.copy(
                    staff = mutableMapOf(staffId to staffNotOwner),
                    tags = listOf("TAG1")
                )

                val updatedChannel = channelDocument.copy(
                    tags = listOf("TAG1", "aguardando_n2"),
                    zendeskTicketId = zendeskTicketId
                )

                coEvery { serviceSpy.getChannel(channelId) } returns channelDocument.success()

                coEvery {
                    channelFirestoreService.updateFields(
                        channelId,
                        mapOf(ChannelDocument::zendeskTicketId to zendeskTicketId)
                    )
                } returns channelId.success()

                coEvery {
                    messageFirestoreService.add(
                        channelId,
                        MessageDocument(
                            aliceId = staffId,
                            userId = staffId,
                            type = MessageType.TEXT,
                            content = "<a href='https://alice709.zendesk.com/agent/tickets/$zendeskTicketId' target='_blank'>Acesse aqui</a> o caso escalado para N2 no Zendesk",
                            createdAt = timestamp
                        ),
                        true
                    )
                } returns channelId.success()

                coEvery { personService.get(personId) } returns person.success()

                coEvery {
                    messageFirestoreService.add(
                        channelId,
                        MessageDocument(
                            aliceId = staffId,
                            userId = staffId,
                            type = MessageType.TEXT,
                            content = "Olá, José! Vou redirecionar seu caso para o setor responsável que te ajudará com o assunto.<br><br>Essa <b>transferência pode levar alguns minutos</b> e assim que for iniciada você receberá uma notificação, mas para garantir que deu tudo certo, seguirei por aqui até que seu atendimento inicie. Combinado?",
                            createdAt = timestamp
                        ),
                        false
                    )
                } returns channelId.success()

                coEvery { serviceSpy.setTags(channelId, listOf("TAG1", "aguardando_n2")) } returns channelId.success()

                coEvery {
                    channelNotificationService.notify(
                        updatedChannel,
                        ChannelChangeAction.ESCALATED_TO_N2
                    )
                } returns mockk()

                val result = serviceSpy.updateWithZendeskTicketData(channelId, zendeskTicketId)
                assertThat(result).isSuccessWithData(channelId)

                coVerifyOnce { serviceSpy.setZendeskTicketId(any(), any()) }
                coVerifyOnce { serviceSpy.updateWithZendeskTicketData(any(), any()) }
                coVerifyOnce { serviceSpy.getChannel(any()) }
                coVerifyOnce { messageFirestoreService.add(any(), any(), true) }
                coVerifyOnce { personService.get(any()) }
                coVerifyOnce { messageFirestoreService.add(any(), any(), false) }
                coVerifyOnce { serviceSpy.setTags(any(), any()) }
                coVerifyOnce { channelFirestoreService.updateFields(any(), any()) }
                coVerifyOnce { channelNotificationService.notify(any<ChannelDocument>(), any()) }
                coVerify { virtualClinicMessagesService wasNot called }
            }
        }


    @OptIn(FirestoreContextUsage::class)
    @Test
    fun `#updateWithZendeskTicketData should not update channel if it's not active`() = runBlocking {
        val channelDocument = channelDocument.copy(status = ChannelStatus.ARCHIVED)

        coEvery { serviceSpy.getChannel(channelId) } returns channelDocument.success()

        val result = serviceSpy.updateWithZendeskTicketData(channelId, zendeskTicketId)
        assertThat(result).isFailureOfType(IllegalArgumentException::class)

        coVerifyOnce { serviceSpy.updateWithZendeskTicketData(any(), any()) }
        coVerifyOnce { serviceSpy.getChannel(any()) }
        coVerifyNone { serviceSpy.setZendeskTicketId(any(), any()) }
        coVerifyNone { serviceSpy.setTags(any(), any()) }
        coVerify { channelFirestoreService wasNot called }
        coVerify { personService wasNot called }
        coVerify { messageFirestoreService wasNot called }
        coVerify { channelNotificationService wasNot called }
        coVerify { virtualClinicMessagesService wasNot called }
    }

    @OptIn(FirestoreContextUsage::class)
    @Test
    fun `#assignToN2Staff should assign a new staff to the channel, change the tag and notify event`() =
        runBlocking {
            mockTimestamp { timestamp ->
                withFeatureFlag(
                    FeatureNamespace.CHANNELS,
                    "zendesk_assign_n2_message",
                    "@firstname, o @staffname, dará continuidade ao seu atendimento por aqui!<br>Me despeço por agora. Até a próxima!"
                ) {
                    val ownerStaffId = channelDocument.staff.values.first { it.owner }.id
                    val tags = listOf("TAG1")
                    val expectedChannel = channelDocument.copy(tags = tags)

                    val updatedChannel = expectedChannel.copy(
                        staff = mutableMapOf(staff02.id.toString() to ChannelStaffInfo.from(staff02)),
                        staffIds = listOf(staff02.id.toString()),
                        tags = expectedChannel.tags.plus(redirectedToN2ChannelTag)
                    )

                    val expectedMessage = MessageDocument(
                        userId = ownerStaffId,
                        aliceId = ownerStaffId,
                        content = "${person.firstName}, o ${staff02.firstName}, dará continuidade ao seu atendimento por aqui!<br>Me despeço por agora. Até a próxima!",
                        type = MessageType.TEXT,
                        createdAt = timestamp
                    )

                    coEvery {
                        serviceSpy.addStaff(
                            expectedChannel.id(),
                            staff02,
                            ownerStaffId,
                            owner = true,
                            force = true
                        )
                    } returns expectedChannel.id().success()

                    coEvery { personService.get(channelDocument.personId.toPersonId()) } returns person.success()

                    coEvery {
                        messageFirestoreService.add(expectedChannel.id(), expectedMessage)
                    } returns expectedChannel.id().success()

                    coEvery {
                        serviceSpy.removeStaff(
                            expectedChannel,
                            channelStaffOwner.id,
                            channelStaffOwner.id,
                            notify = true,
                            checkOwner = false,
                            checkSize = false
                        )
                    } returns expectedChannel.id().success()

                    coEvery {
                        serviceSpy.setTags(expectedChannel.id(), expectedChannel.tags.plus("redirecionado_n2"))
                    } returns expectedChannel.id().success()

                    coEvery {
                        channelNotificationService.notify(
                            updatedChannel,
                            ChannelChangeAction.N2_CAUGHT
                        )
                    } returns mockk()

                    val result = serviceSpy.assignToN2Staff(expectedChannel, staff02)

                    assertThat(result).isSuccessWithData(expectedChannel.id())

                    coVerifyOnce {
                        serviceSpy.span(any(), any(), any())
                    }
                    coVerifyOnce {
                        serviceSpy.assignToN2Staff(any(), any())
                        serviceSpy.addStaff(any(), any(), any(), any(), any())
                        personService.get(any())
                        messageFirestoreService.add(any(), any())
                        serviceSpy.removeStaff(any<ChannelDocument>(), any(), any(), any(), any(), any())
                        serviceSpy.setTags(any(), any())
                        channelNotificationService.notify(any<ChannelDocument>(), any(), any(), any())
                    }
                }
            }

        }

    @OptIn(FirestoreContextUsage::class)
    @Test
    fun `#sendQuestionMessage - should add message of type question`() = runBlocking {
        mockTimestamp {
            val content = "Olá Mundo"
            val channelOptions = listOf(ChannelMessageQuestionOptions.YES_ARCHIVE_CHANNEL, ChannelMessageQuestionOptions.DO_NOT_ARCHIVE_CHANNEL)
            val expected = MessageDocument(
                userId = AI_STAFF_ID,
                content = content,
                type = MessageType.QUESTION,
                question = QuestionContent(
                    question = "Escolha uma opção:",
                    input = QuestionnaireQuestionInputResponse(
                        action = "",
                        displayAttributes = null,
                        options = channelOptions
                            .map {
                                HealthFormQuestionOptionResponse(
                                    label = it.description,
                                    value = it.name
                                )
                            },
                        type = HealthFormQuestionType.OPTION_BUTTONS
                    ),
                    required = true
                )
            )

            coEvery { messageFirestoreService.add(channelId, expected) } returns messageId.success()

            val result = serviceSpy.sendQuestionMessage(channelId, content, channelOptions)
            assertThat(result).isSuccessWithData(messageId)

            coVerifyOnce {
                serviceSpy.span(any(), any(), any())
            }
            coVerifyOnce {
                serviceSpy.sendQuestionMessage(any(), any(), any())
                messageFirestoreService.add(any(), any()) }

        }
    }

    @OptIn(FirestoreContextUsage::class)
    @Test
    fun `#sendTextMessage - should send text message`() = runBlocking {
        val content = "Hello World"
        val expected = MessageDocument(
            aliceId = AI_STAFF_ID,
            userId = AI_STAFF_ID,
            content = content,
            type = MessageType.TEXT,
            appVersion = null,
            isAuto = false,
            isVisible = true,
            additionalInfo = emptyMap(),
            source = null
        )

        coEvery { messageFirestoreService.add(channelId, expected) } returns messageId.success()

        val result = serviceSpy.sendTextMessage(channelId, content)

        assertThat(result).isSuccessWithData(messageId)

        coVerifyOnce {
            serviceSpy.span(any(), any(), any())
        }
        coVerifyOnce {
            serviceSpy.sendTextMessage(any(), any())
            messageFirestoreService.add(any(), any())
        }

    }
}
