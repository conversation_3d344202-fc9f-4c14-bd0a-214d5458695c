package br.com.alice.api.backoffice.validation

import br.com.alice.api.backoffice.transfers.staff.*
import br.com.alice.common.core.extensions.isEmail
import br.com.alice.common.core.extensions.isValidBrazilianNationalId
import br.com.alice.common.core.Role
import br.com.alice.common.core.StaffType
import br.com.alice.common.models.Gender
import br.com.alice.common.models.State
import br.com.alice.common.models.SpecialistTier
import br.com.alice.data.layer.models.HealthSpecialistScoreEnum
import br.com.alice.data.layer.models.OnCallPaymentMethod
import java.time.LocalDate
import java.util.UUID

object UpdateStaffRequestValidator {

    fun validate(request: UpdateStaffRequest): StaffValidationResponse {
        val errors = mutableListOf<StaffValidationErrorResponse>()

        validateFirstName(request.firstName, errors)
        validateLastName(request.lastName, errors)
        validateEmail(request.email, errors)
        validateNationalId(request.nationalId, errors)
        validateBirthdate(request.birthdate, errors)
        validateGender(request.gender, errors)
        validateRole(request.role, errors)
        validateType(request.type, errors)
        validateProfileImageUrl(request.profileImageUrl, errors)
        validateUrlSlug(request.urlSlug, errors)
        validateQuote(request.quote, errors)
        validateProfileBio(request.profileBio, errors)
        validateCouncil(request.council, errors)
        validateSpecialty(request.specialty, errors)
        validateSubSpecialties(request.subSpecialties, errors)
        validateInternalSpecialty(request.internalSpecialty, errors)
        validateInternalSubSpecialties(request.internalSubSpecialties, errors)
        validateProviderUnits(request.providerUnits, errors)
        validateQualifications(request.qualifications, errors)
        validateTier(request.tier, errors)
        validateTheoristTier(request.theoristTier, errors)
        validateCuriosity(request.curiosity, errors)
        validateEducation(request.education, errors)
        validateHealthSpecialistScore(request.healthSpecialistScore, errors)
        validateDeAccreditationDate(request.deAccreditationDate, errors)
        validatePaymentFrequency(request.paymentFrequency, errors)
        validateAttendsToOnCall(request.attendsToOnCall, errors)
        validateOnCallPaymentMethod(request.onCallPaymentMethod, errors)
        validateContacts(request.contacts, errors)
        validateVacationDates(request.onVacationStart, request.onVacationUntil, errors)

        return StaffValidationResponse(
            isValid = errors.isEmpty(),
            errors = errors
        )
    }

    private fun validateFirstName(firstName: String?, errors: MutableList<StaffValidationErrorResponse>) {
        when {
            firstName.isNullOrBlank() -> errors.add(
                StaffValidationErrorResponse("firstName", "Nome é obrigatório")
            )
            firstName.length < 2 -> errors.add(
                StaffValidationErrorResponse("firstName", "Nome deve ter pelo menos 2 caracteres")
            )
            firstName.length > 50 -> errors.add(
                StaffValidationErrorResponse("firstName", "Nome deve ter no máximo 50 caracteres")
            )
            !firstName.matches(Regex("^[a-zA-ZÀ-ÿ\\s]+$")) -> errors.add(
                StaffValidationErrorResponse("firstName", "Nome deve conter apenas letras e espaços")
            )
        }
    }

    private fun validateLastName(lastName: String?, errors: MutableList<StaffValidationErrorResponse>) {
        when {
            lastName.isNullOrBlank() -> errors.add(
                StaffValidationErrorResponse("lastName", "Sobrenome é obrigatório")
            )
            lastName.length < 2 -> errors.add(
                StaffValidationErrorResponse("lastName", "Sobrenome deve ter pelo menos 2 caracteres")
            )
            lastName.length > 50 -> errors.add(
                StaffValidationErrorResponse("lastName", "Sobrenome deve ter no máximo 50 caracteres")
            )
            !lastName.matches(Regex("^[a-zA-ZÀ-ÿ\\s]+$")) -> errors.add(
                StaffValidationErrorResponse("lastName", "Sobrenome deve conter apenas letras e espaços")
            )
        }
    }

    private fun validateEmail(email: String?, errors: MutableList<StaffValidationErrorResponse>) {
        when {
            email.isNullOrBlank() -> errors.add(
                StaffValidationErrorResponse("email", "E-mail é obrigatório")
            )
            !email.isEmail() -> errors.add(
                StaffValidationErrorResponse("email", "E-mail inválido")
            )
            email.length > 100 -> errors.add(
                StaffValidationErrorResponse("email", "E-mail deve ter no máximo 100 caracteres")
            )
        }
    }

    private fun validateNationalId(nationalId: String?, errors: MutableList<StaffValidationErrorResponse>) {
        if (nationalId != null && nationalId.isNotBlank()) {
            if (!nationalId.isValidBrazilianNationalId()) {
                errors.add(StaffValidationErrorResponse("nationalId", "CPF inválido"))
            }
        }
    }

    private fun validateBirthdate(birthdate: LocalDate?, errors: MutableList<StaffValidationErrorResponse>) {
        if (birthdate != null) {
            val now = LocalDate.now()
            val minimumAge = now.minusYears(16)
            val maximumAge = now.minusYears(100)

            when {
                birthdate.isAfter(minimumAge) -> errors.add(
                    StaffValidationErrorResponse("birthdate", "Data de nascimento inválida. Idade mínima é 16 anos")
                )
                birthdate.isBefore(maximumAge) -> errors.add(
                    StaffValidationErrorResponse("birthdate", "Data de nascimento inválida. Idade máxima é 100 anos")
                )
            }
        }
    }

    private fun validateGender(gender: Gender?, errors: MutableList<StaffValidationErrorResponse>) {
        if (gender == null) {
            errors.add(StaffValidationErrorResponse("gender", "Gênero é obrigatório"))
        }
    }

    private fun validateRole(role: Role?, errors: MutableList<StaffValidationErrorResponse>) {
        if (role == null) {
            errors.add(StaffValidationErrorResponse("role", "Função é obrigatória"))
        }
    }

    private fun validateType(type: StaffType?, errors: MutableList<StaffValidationErrorResponse>) {
        if (type == null) {
            errors.add(StaffValidationErrorResponse("type", "Tipo de staff é obrigatório"))
        }
    }

    private fun validateProfileImageUrl(profileImageUrl: String?, errors: MutableList<StaffValidationErrorResponse>) {
        if (profileImageUrl != null && profileImageUrl.isNotBlank()) {
            if (profileImageUrl.length > 500) {
                errors.add(StaffValidationErrorResponse("profileImageUrl", "URL da imagem deve ter no máximo 500 caracteres"))
            }
            if (!profileImageUrl.matches(Regex("^https?://.*"))) {
                errors.add(StaffValidationErrorResponse("profileImageUrl", "URL da imagem deve começar com http:// ou https://"))
            }
        }
    }

    private fun validateUrlSlug(urlSlug: String?, errors: MutableList<StaffValidationErrorResponse>) {
        if (urlSlug != null && urlSlug.isNotBlank()) {
            when {
                urlSlug.length < 3 -> errors.add(
                    StaffValidationErrorResponse("urlSlug", "URL slug deve ter pelo menos 3 caracteres")
                )
                urlSlug.length > 100 -> errors.add(
                    StaffValidationErrorResponse("urlSlug", "URL slug deve ter no máximo 100 caracteres")
                )
                !urlSlug.matches(Regex("^[a-z0-9-]+$")) -> errors.add(
                    StaffValidationErrorResponse("urlSlug", "URL slug deve conter apenas letras minúsculas, números e hífens")
                )
            }
        }
    }

    private fun validateQuote(quote: String?, errors: MutableList<StaffValidationErrorResponse>) {
        if (quote != null && quote.isNotBlank()) {
            if (quote.length > 500) {
                errors.add(StaffValidationErrorResponse("quote", "Citação deve ter no máximo 500 caracteres"))
            }
        }
    }

    private fun validateProfileBio(profileBio: String?, errors: MutableList<StaffValidationErrorResponse>) {
        if (profileBio != null && profileBio.isNotBlank()) {
            if (profileBio.length > 2000) {
                errors.add(StaffValidationErrorResponse("profileBio", "Biografia deve ter no máximo 2000 caracteres"))
            }
        }
    }

    private fun validateCouncil(council: CouncilDTO?, errors: MutableList<StaffValidationErrorResponse>) {
        if (council != null) {
            if (council.number.isBlank()) {
                errors.add(StaffValidationErrorResponse("council.number", "Número do conselho é obrigatório"))
            } else if (council.number.length > 20) {
                errors.add(StaffValidationErrorResponse("council.number", "Número do conselho deve ter no máximo 20 caracteres"))
            }

            if (council.state == null) {
                errors.add(StaffValidationErrorResponse("council.state", "Estado do conselho é obrigatório"))
            }

            if (council.type != null && council.type <= 0) {
                errors.add(StaffValidationErrorResponse("council.type", "Tipo de conselho inválido"))
            }
        }
    }

    private fun validateSpecialty(specialty: UUID?, errors: MutableList<StaffValidationErrorResponse>) {
        // UUID validation is handled by the type system, no additional validation needed
    }

    private fun validateSubSpecialties(subSpecialties: List<UUID>?, errors: MutableList<StaffValidationErrorResponse>) {
        if (subSpecialties != null && subSpecialties.size > 10) {
            errors.add(StaffValidationErrorResponse("subSpecialties", "Máximo de 10 subespecialidades permitidas"))
        }
    }

    private fun validateInternalSpecialty(internalSpecialty: UUID?, errors: MutableList<StaffValidationErrorResponse>) {
        // UUID validation is handled by the type system, no additional validation needed
    }

    private fun validateInternalSubSpecialties(internalSubSpecialties: List<UUID>?, errors: MutableList<StaffValidationErrorResponse>) {
        if (internalSubSpecialties != null && internalSubSpecialties.size > 10) {
            errors.add(StaffValidationErrorResponse("internalSubSpecialties", "Máximo de 10 subespecialidades internas permitidas"))
        }
    }

    private fun validateProviderUnits(providerUnits: List<UUID>?, errors: MutableList<StaffValidationErrorResponse>) {
        if (providerUnits != null && providerUnits.size > 20) {
            errors.add(StaffValidationErrorResponse("providerUnits", "Máximo de 20 unidades prestadoras permitidas"))
        }
    }

    private fun validateQualifications(qualifications: List<br.com.alice.data.layer.models.Qualification>?, errors: MutableList<StaffValidationErrorResponse>) {
        if (qualifications != null && qualifications.size > 50) {
            errors.add(StaffValidationErrorResponse("qualifications", "Máximo de 50 qualificações permitidas"))
        }
    }

    private fun validateCuriosity(curiosity: String?, errors: MutableList<StaffValidationErrorResponse>) {
        if (curiosity != null && curiosity.isNotBlank()) {
            if (curiosity.length > 1000) {
                errors.add(StaffValidationErrorResponse("curiosity", "Curiosidade deve ter no máximo 1000 caracteres"))
            }
        }
    }

    private fun validateEducation(education: List<String>?, errors: MutableList<StaffValidationErrorResponse>) {
        if (education != null) {
            if (education.size > 20) {
                errors.add(StaffValidationErrorResponse("education", "Máximo de 20 itens de educação permitidos"))
            }

            education.forEachIndexed { index, item ->
                if (item.isBlank()) {
                    errors.add(StaffValidationErrorResponse("education[$index]", "Item de educação não pode estar vazio"))
                } else if (item.length > 200) {
                    errors.add(StaffValidationErrorResponse("education[$index]", "Item de educação deve ter no máximo 200 caracteres"))
                }
            }
        }
    }

    private fun validatePaymentFrequency(paymentFrequency: Int?, errors: MutableList<StaffValidationErrorResponse>) {
        if (paymentFrequency != null && paymentFrequency < 0) {
            errors.add(StaffValidationErrorResponse("paymentFrequency", "Frequência de pagamento deve ser um valor positivo"))
        }
    }

    private fun validateContacts(contacts: List<ContactDTO>?, errors: MutableList<StaffValidationErrorResponse>) {
        if (contacts != null) {
            if (contacts.size > 10) {
                errors.add(StaffValidationErrorResponse("contacts", "Máximo de 10 contatos permitidos"))
            }

            contacts.forEachIndexed { index, contact ->
                validateContact(contact, index, errors)
            }
        }
    }

    private fun validateContact(contact: ContactDTO, index: Int, errors: MutableList<StaffValidationErrorResponse>) {
        if (contact.phones.size > 5) {
            errors.add(StaffValidationErrorResponse("contacts[$index].phones", "Máximo de 5 telefones por contato"))
        }

        if (contact.availableDays.size > 7) {
            errors.add(StaffValidationErrorResponse("contacts[$index].availableDays", "Máximo de 7 dias disponíveis por contato"))
        }

        if (contact.website != null && contact.website.isNotBlank()) {
            if (contact.website.length > 200) {
                errors.add(StaffValidationErrorResponse("contacts[$index].website", "Website deve ter no máximo 200 caracteres"))
            }
            if (!contact.website.matches(Regex("^https?://.*"))) {
                errors.add(StaffValidationErrorResponse("contacts[$index].website", "Website deve começar com http:// ou https://"))
            }
        }

        contact.address?.let { address ->
            validateAddress(address, index, errors)
        }
    }

    private fun validateAddress(address: AddressDTO, contactIndex: Int, errors: MutableList<StaffValidationErrorResponse>) {
        if (address.street != null && address.street.length > 200) {
            errors.add(StaffValidationErrorResponse("contacts[$contactIndex].address.street", "Rua deve ter no máximo 200 caracteres"))
        }

        if (address.number != null && address.number.length > 20) {
            errors.add(StaffValidationErrorResponse("contacts[$contactIndex].address.number", "Número deve ter no máximo 20 caracteres"))
        }

        if (address.complement != null && address.complement.length > 100) {
            errors.add(StaffValidationErrorResponse("contacts[$contactIndex].address.complement", "Complemento deve ter no máximo 100 caracteres"))
        }

        if (address.neighborhood != null && address.neighborhood.length > 100) {
            errors.add(StaffValidationErrorResponse("contacts[$contactIndex].address.neighborhood", "Bairro deve ter no máximo 100 caracteres"))
        }

        if (address.city != null && address.city.length > 100) {
            errors.add(StaffValidationErrorResponse("contacts[$contactIndex].address.city", "Cidade deve ter no máximo 100 caracteres"))
        }

        if (address.state != null && address.state.length > 50) {
            errors.add(StaffValidationErrorResponse("contacts[$contactIndex].address.state", "Estado deve ter no máximo 50 caracteres"))
        }

        if (address.zipcode != null && address.zipcode.isNotBlank()) {
            if (!address.zipcode.matches(Regex("^\\d{5}-?\\d{3}$"))) {
                errors.add(StaffValidationErrorResponse("contacts[$contactIndex].address.zipcode", "CEP deve estar no formato 00000-000"))
            }
        }

        if (address.label != null && address.label.length > 50) {
            errors.add(StaffValidationErrorResponse("contacts[$contactIndex].address.label", "Rótulo deve ter no máximo 50 caracteres"))
        }
    }

    private fun validateVacationDates(onVacationStart: LocalDate?, onVacationUntil: LocalDate?, errors: MutableList<StaffValidationErrorResponse>) {
        if (onVacationStart != null && onVacationUntil != null) {
            if (onVacationStart.isAfter(onVacationUntil)) {
                errors.add(StaffValidationErrorResponse("onVacationStart", "Data de início das férias deve ser anterior à data de fim"))
            }
        } else if (onVacationStart != null && onVacationUntil == null) {
            errors.add(StaffValidationErrorResponse("onVacationUntil", "Data de fim das férias é obrigatória quando data de início é informada"))
        } else if (onVacationStart == null && onVacationUntil != null) {
            errors.add(StaffValidationErrorResponse("onVacationStart", "Data de início das férias é obrigatória quando data de fim é informada"))
        }
    }

    private fun validateTier(tier: SpecialistTier?, errors: MutableList<StaffValidationErrorResponse>) {
        // SpecialistTier é um enum, não precisa de validação específica além de ser nullable
    }

    private fun validateTheoristTier(theoristTier: SpecialistTier?, errors: MutableList<StaffValidationErrorResponse>) {
        // SpecialistTier é um enum, não precisa de validação específica além de ser nullable
    }

    private fun validateHealthSpecialistScore(healthSpecialistScore: HealthSpecialistScoreEnum?, errors: MutableList<StaffValidationErrorResponse>) {
        // HealthSpecialistScoreEnum é um enum, não precisa de validação específica além de ser nullable
    }

    private fun validateDeAccreditationDate(deAccreditationDate: LocalDate?, errors: MutableList<StaffValidationErrorResponse>) {
        deAccreditationDate?.let {
            if (it.isBefore(LocalDate.now().minusYears(50))) {
                errors.add(StaffValidationErrorResponse("deAccreditationDate", "Data de descredenciamento não pode ser muito antiga"))
            }
            if (it.isAfter(LocalDate.now().plusYears(10))) {
                errors.add(StaffValidationErrorResponse("deAccreditationDate", "Data de descredenciamento não pode ser muito futura"))
            }
        }
    }

    private fun validateAttendsToOnCall(attendsToOnCall: Boolean?, errors: MutableList<StaffValidationErrorResponse>) {
        // Boolean não precisa de validação específica além de ser nullable
    }

    private fun validateOnCallPaymentMethod(onCallPaymentMethod: OnCallPaymentMethod?, errors: MutableList<StaffValidationErrorResponse>) {
        // OnCallPaymentMethod é um enum, não precisa de validação específica além de ser nullable
    }
}
