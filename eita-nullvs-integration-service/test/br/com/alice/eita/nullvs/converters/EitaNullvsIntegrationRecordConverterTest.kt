package br.com.alice.eita.nullvs.converters

import br.com.alice.common.RangeUUID
import br.com.alice.data.layer.models.EitaNullvsExternalModelType
import br.com.alice.data.layer.models.EitaNullvsIntegrationRecord
import br.com.alice.data.layer.models.EitaNullvsIntegrationRecordModel
import br.com.alice.data.layer.models.EitaNullvsInternalModelType
import org.assertj.core.api.Assertions.assertThat
import java.time.LocalDateTime
import kotlin.test.Test

class EitaNullvsIntegrationRecordConverterTest {

    private val nullvsIntegrationRecord = EitaNullvsIntegrationRecord(
        internalId = RangeUUID.generate(),
        internalModelName = EitaNullvsInternalModelType.HEALTHCARE_BUNDLE,
        externalId = "externalId",
        externalModelName = EitaNullvsExternalModelType.BUNDLE,
        integratedAt = LocalDateTime.now().minusDays(1),
        canceledAt = LocalDateTime.now()
    )

    private val nullvsIntegrationRecordModel = EitaNullvsIntegrationRecordModel(
        id = nullvsIntegrationRecord.id,
        version = nullvsIntegrationRecord.version,
        createdAt = nullvsIntegrationRecord.createdAt,
        updatedAt = nullvsIntegrationRecord.updatedAt,
        internalId = nullvsIntegrationRecord.internalId,
        internalModelName = EitaNullvsInternalModelType.HEALTHCARE_BUNDLE,
        externalId = "externalId",
        externalModelName = EitaNullvsExternalModelType.BUNDLE,
        integratedAt = nullvsIntegrationRecord.integratedAt,
        canceledAt = nullvsIntegrationRecord.canceledAt
    )

    @Test
    fun testToTransport() {
        assertThat(nullvsIntegrationRecordModel.toTransport()).isEqualTo(nullvsIntegrationRecord)
    }

    @Test
    fun testToModel() {
        assertThat(nullvsIntegrationRecord.toModel()).isEqualTo(nullvsIntegrationRecordModel)
    }

}
