package br.com.alice.member.onboarding.services

import br.com.alice.common.core.PersonId
import br.com.alice.common.core.exceptions.InvalidArgumentException
import br.com.alice.common.core.extensions.toUUID
import br.com.alice.common.extensions.coFoldNotFound
import br.com.alice.common.kafka.interfaces.KafkaProducerService
import br.com.alice.common.logging.logger
import br.com.alice.data.layer.models.Deadline
import br.com.alice.data.layer.models.FeatureNamespace
import br.com.alice.data.layer.models.HealthPlanTask
import br.com.alice.data.layer.models.HealthPlanTaskStatus
import br.com.alice.data.layer.models.HealthPlanTaskType
import br.com.alice.data.layer.models.HealthcareModelType
import br.com.alice.data.layer.models.MemberOnboardingCheckpoint
import br.com.alice.data.layer.models.MemberOnboardingStep
import br.com.alice.data.layer.models.MemberOnboardingStep.MemberOnboardingStepType
import br.com.alice.data.layer.models.MemberOnboardingTemplate.MemberOnboardingTemplateType
import br.com.alice.data.layer.models.PeriodUnit
import br.com.alice.data.layer.models.Person
import br.com.alice.data.layer.models.Risk
import br.com.alice.data.layer.models.RiskDescription
import br.com.alice.featureconfig.core.FeatureService
import br.com.alice.healthplan.client.HealthPlanTaskService
import br.com.alice.marauders.map.client.RiskService
import br.com.alice.member.onboarding.client.MemberOnboardingCheckpointService
import br.com.alice.member.onboarding.client.MemberOnboardingStepService
import br.com.alice.member.onboarding.client.MemberOnboardingStratificationService
import br.com.alice.member.onboarding.client.MemberOnboardingTemplateService
import br.com.alice.person.client.PersonService
import br.com.alice.person.model.events.LowRiskMemberStratificationEvent
import br.com.alice.staff.client.HealthcareTeamService
import com.github.kittinunf.result.map
import com.github.kittinunf.result.success
import kotlinx.coroutines.async
import kotlinx.coroutines.coroutineScope
import java.time.LocalDateTime
import java.util.UUID

const val HEALTH_PLAN_TASK_TITLE = "Consulta de Imersão"

class MemberOnboardingStratificationServiceImpl(
    private val memberOnboardingCheckpointService: MemberOnboardingCheckpointService,
    private val memberOnboardingStepService: MemberOnboardingStepService,
    private val memberOnboardingTemplateService: MemberOnboardingTemplateService,
    private val riskService: RiskService,
    private val kafkaProducer: KafkaProducerService,
    private val healthPlanTaskService: HealthPlanTaskService,
    private val healthcareTeamService: HealthcareTeamService,
    private val personService: PersonService,
) : MemberOnboardingStratificationService {

    override suspend fun getByPersonId(personId: PersonId) =
        memberOnboardingCheckpointService.findByPersonId(personId).map { checkpoint ->
            logger.info(
                "MemberOnboardingStratificationService.getByPersonId: checkpoint found",
                "personId" to personId,
                "checkpoint" to checkpoint
            )
            stratifyMember(checkpoint, personId)
        }

    override suspend fun forceStratificationBasedOnCheckpoint(checkpointId: UUID) =
        memberOnboardingCheckpointService.find(checkpointId).map { checkpoint ->
            val personId = checkpoint.personId
            logger.info(
                "MemberOnboardingStratificationService.forceStratificationBasedOnCheckpoint: forcing stratification",
                "personId" to personId,
                "checkpoint" to checkpoint
            )

            val (updatedCheckpoint, _) = stratifyMember(checkpoint, personId)

            updatedCheckpoint
        }


    private suspend fun stratifyMember(
        checkpoint: MemberOnboardingCheckpoint,
        personId: PersonId
    ): Pair<MemberOnboardingCheckpoint, MemberOnboardingStep> {
        val templateId = checkpoint.memberOnboardingTemplateId
        val person = personService.get(personId).get()
        val lastStep = when (isChildrenTemplate(templateId)) {
            true -> memberOnboardingStepService.findByTemplateIdAndType(
                templateId,
                MemberOnboardingStepType.CHILDREN_SCHEDULE
            )
            else -> defineRiskType(person).map { riskType ->
                memberOnboardingStepService.findByTemplateIdAndType(templateId, riskType).get()
            }
        }.get()

        return executeActionFromStepType(checkpoint, lastStep)
    }

    private suspend fun defineRiskType(person: Person) =
        riskService.getByPerson(person.id).map { risk ->
            logger.info(
                "MemberOnboardingStratificationService.defineRiskType: risk obtained",
                "personId" to person.id,
                "risk" to risk.riskDescription
            )
            when (person.productInfo?.healthcareModelType) {
                HealthcareModelType.V3 -> getRiskType(risk, person)
                else -> getRiskTypeLegacy(risk, person)
            }
        }.coFoldNotFound {
            logger.info(
                "MemberOnboardingStratificationService.defineRiskType: risk is null, assuming low risk",
                "personId" to person.id
            )
            takeLowRisk(person.id).success()
        }

    private suspend fun getRiskTypeLegacy(
        risk: Risk,
        person: Person
    ) = when (risk.riskDescription) {
        RiskDescription.NO_RISK,
        RiskDescription.LOW_RISK -> takeLowRisk(person.id)
        RiskDescription.MEDIUM_RISK,
        RiskDescription.HIGH_RISK -> MemberOnboardingStepType.HIGH_RISK
        else -> throw InvalidArgumentException(message = "The risk does not exist")
    }

    private suspend fun getRiskType(
        risk: Risk,
        person: Person
    ) = when (risk.riskDescription) {
        RiskDescription.TARGET -> MemberOnboardingStepType.HIGH_RISK
        else -> takeLowRisk(person.id)
    }

    private suspend fun takeLowRisk(personId: PersonId): MemberOnboardingStepType {
        kafkaProducer.produce(LowRiskMemberStratificationEvent(personId))
        return MemberOnboardingStepType.LOW_RISK
    }

    private suspend fun isChildrenTemplate(templateId: UUID) =
        memberOnboardingTemplateService.find(templateId).map { template ->
            template.type == MemberOnboardingTemplateType.CHILDREN ||
            template.type == MemberOnboardingTemplateType.NEWBORN
        }.get()


    private suspend fun executeActionFromStepType(
        checkpoint: MemberOnboardingCheckpoint,
        step: MemberOnboardingStep
    ): Pair<MemberOnboardingCheckpoint, MemberOnboardingStep> =
        when (step.type) {
            MemberOnboardingStepType.HIGH_RISK, MemberOnboardingStepType.CHILDREN_SCHEDULE -> {
                val immersionReferralHealthPlanTask = createImmersionReferralHealthPlanTask(checkpoint, step)
                Pair(immersionReferralHealthPlanTask, step)
            }
            else -> {
                memberOnboardingCheckpointService.updateCheckpointBasedOnStep(checkpoint, step)
                    .map { updatedMemberOnboardingCheckpoint -> Pair(updatedMemberOnboardingCheckpoint, step) }
                    .get()
            }
        }

    private suspend fun createImmersionReferralHealthPlanTask(
        checkpoint: MemberOnboardingCheckpoint,
        step: MemberOnboardingStep
    ): MemberOnboardingCheckpoint = coroutineScope {

        val personId = checkpoint.personId
        logger.info(
            "MemberOnboardingController.createImmersionReferralHealthPlanTask: create referral task",
            "personId" to personId
        )

        if (checkpoint.referralTaskId == null) {
            val staffIdDeferred = async {
                healthcareTeamService.getHealthcareTeamByPerson(personId).fold(
                    { healthcareTeam -> healthcareTeam.physicianStaffId },
                    {
                        logger.error(
                            "MemberOnboardingController.createImmersionReferralHealthPlanTask: error while getting healthcare team",
                            "person_id" to personId
                        )
                        getLeagueStaffId()
                    }
                )
            }
            val staffId = staffIdDeferred.await()
            return@coroutineScope healthPlanTaskService.create(
                HealthPlanTask(
                    title = HEALTH_PLAN_TASK_TITLE,
                    personId = checkpoint.personId,
                    status = HealthPlanTaskStatus.ACTIVE,
                    lastRequesterStaffId = staffId,
                    type = HealthPlanTaskType.REFERRAL,
                    favorite = false,
                    releasedByStaffId = staffId,
                    releasedAt = LocalDateTime.now(),
                    deadline = Deadline(
                        quantity = 1,
                        unit = PeriodUnit.DAY,
                        date = LocalDateTime.now().plusDays(60)
                    )
                ),
                staffId
            ).map {
                memberOnboardingCheckpointService
                    .updateCheckpointHighRiskStep(checkpoint, it.id, step)
                    .get()
            }.get()
        } else {
            memberOnboardingCheckpointService
                .updateCheckpointHighRiskStep(checkpoint, checkpoint.referralTaskId, step)
                .get()
        }
    }

    private fun getLeagueStaffId(): UUID =
        FeatureService.get(
            namespace = FeatureNamespace.ONBOARDING,
            key = "member_onboarding_league_staff_id",
            defaultValue = "ba3bbe06-111d-4f33-95c3-9cf3e3672100"
        ).toUUID()

}
