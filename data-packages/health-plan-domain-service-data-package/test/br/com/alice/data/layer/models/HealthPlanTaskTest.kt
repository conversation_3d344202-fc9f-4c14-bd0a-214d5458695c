package br.com.alice.data.layer.models

import br.com.alice.common.Disease
import br.com.alice.common.RangeUUID
import br.com.alice.data.layer.helpers.TestModelFactory
import org.assertj.core.api.Assertions.assertThat
import kotlin.test.Test

class HealthPlanTaskTest {

    @Test
    fun `#therapySentence get therapy sentence`() {
        val healthPlanTask = TestModelFactory.buildHealthPlanTaskReferral(
            sessionsQuantity = 10,
            specialty = ReferralSpecialty(name = "Cardiologia"),
            subSpecialty = ReferralSpecialty(name = "Generalista")
        ).generalize().copy(
            caseRecordDetails = listOf(CaseRecordDetails(
                caseId = RangeUUID.generate(),
                description = DiseaseDetails(
                    type = Disease.Type.CID_10,
                    value = "11",
                    description = "Unha Encravada"
                ),
                severity = CaseSeverity.ONGOING
            ))
        ).specialize<Referral>()

        val therapySentence = healthPlanTask.therapySentence()

        assertThat(therapySentence).isEqualTo("Encaminho para Cardiologia Generalista para 10 sessões, por CID 11 - Unha Encravada.")

    }

}
