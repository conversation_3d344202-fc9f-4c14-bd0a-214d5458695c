package br.com.alice.channel.administrative.services.internal

import br.com.alice.akinator.client.AsyncAssistantService
import br.com.alice.akinator.client.AsyncMessageRequest
import br.com.alice.akinator.client.AsyncRunThreadRequest
import br.com.alice.channel.administrative.util.Tags.EXPLORER_TAG
import br.com.alice.channel.administrative.util.Tags.FRICTION_TAG
import br.com.alice.channel.administrative.util.Tags.HUMAN_CONTACT_TAG
import br.com.alice.channel.administrative.util.Tags.NO_FRICTION_TAG
import br.com.alice.channel.administrative.util.Tags.ONGOING_TAG
import br.com.alice.channel.administrative.util.Tags.ROUTE_APEX_TAG
import br.com.alice.channel.administrative.util.Tags.ROUTE_MLC_TAG
import br.com.alice.channel.administrative.util.Tags.UNDEFINED_TAG
import br.com.alice.channel.core.context.FirestoreContextUsage
import br.com.alice.channel.core.mockTimestamp
import br.com.alice.channel.core.services.internal.ChannelService
import br.com.alice.channel.core.services.internal.firestore.MessageFirestoreService
import br.com.alice.channel.core.util.AI_STAFF_ID
import br.com.alice.channel.models.ChannelDocument
import br.com.alice.channel.models.MessageDocument
import br.com.alice.channel.models.MessageType
import br.com.alice.common.RangeUUID
import br.com.alice.common.core.extensions.plusSafe
import br.com.alice.common.core.extensions.removeDoubleQuotes
import br.com.alice.common.core.extensions.removeOpenAiCitations
import br.com.alice.common.data.dsl.matchers.ResultAssert.Companion.assertThat
import br.com.alice.common.featureflag.withFeatureFlag
import br.com.alice.common.featureflag.withFeatureFlags
import br.com.alice.common.helpers.coVerifyNone
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.common.helpers.mockLocalDateTime
import br.com.alice.data.layer.models.AdministrativeDetailMemberType
import br.com.alice.data.layer.models.AdministrativeDetails
import br.com.alice.data.layer.models.ChannelCategory
import br.com.alice.data.layer.models.ChannelSubCategoryClassifier
import br.com.alice.data.layer.models.FeatureNamespace
import com.github.kittinunf.result.success
import com.google.cloud.Timestamp
import com.google.cloud.firestore.FieldValue.arrayRemove
import com.google.cloud.firestore.FieldValue.arrayUnion
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.confirmVerified
import io.mockk.mockk
import kotlinx.coroutines.runBlocking
import java.time.LocalDateTime
import kotlin.test.AfterTest
import kotlin.test.Test

class AiAdministrativeTriageServiceTest {

    private val asyncAssistantService: AsyncAssistantService = mockk()
    private val channelService: ChannelService = mockk()
    private val messageFirestoreService: MessageFirestoreService = mockk()

    private val aiAdministrativeTriageService = AiAdministrativeTriageService(
        asyncAssistantService,
        channelService,
        messageFirestoreService
    )

    private val channelId = "channelId"
    private val threadId = "threadId"
    private val additionalInstructions = "additionalInstructions"
    private val messageId = "messageId"
    private val userMessageContent = "content"
    private val channelPersonId = RangeUUID.generate().toString()
    private val summaryKeyword = "resumo"
    private val iaMessageContent = "any ia message content"
    private val aiMessageResponseToDrop =
        "Obrigada pelas respostas! Já chamei um especialista para te auxiliar nesse assunto."
    private val aiMessageResponseToDropByTalkToHuman = "Obrigada por ser transparente!"
    private val aiMessageResponseToDropAngry = "Entendo a sua frustração."
    private val nowDateTime = LocalDateTime.now()
    private val nowTimestamp = Timestamp.now()

    private val administrativeAiDropCommonPhrases = listOf("Obrigada pelas respostas")
    private val administrativeAiDropMessage = "Vamos te encaminhar"
    private val administrativeAiDropByTalkToHumanPhrases = listOf("Obrigada por ser transparente!")
    private val administrativeAiDropFrictionPhrases = listOf("Entendo a sua frustração.")
    private val administrativeClassifyAiAssistantId = "asst_classify_XXX"
    private val administrativeExplorerB2CAiAssistantId = "asst_explorerb2c_XXX"
    private val administrativeExplorerB2B5AiAssistantId = "asst_explorerb2b5_XXX"
    private val administrativeExplorerB2B6AiAssistantId = "asst_explorerb2b6_XXX"

    private val channelDocument = ChannelDocument(
        id = channelId,
        channelPersonId = channelPersonId,
        personId = "",
        category = ChannelCategory.ADMINISTRATIVE,
        subCategoryClassifier = ChannelSubCategoryClassifier.AI,
        aiExternalId = threadId,
        tags = emptyList()
    )
    private val messageDocument = MessageDocument(
        id = messageId,
        userId = channelPersonId,
        type = MessageType.TEXT,
        content = userMessageContent
    )

    @AfterTest
    fun confirmMocks() = confirmVerified(
        asyncAssistantService,
        channelService,
        messageFirestoreService
    )

    @OptIn(FirestoreContextUsage::class)
    @Test
    fun `#replyMember returns false when the member is not waiting`() = mocks {
        val channelDocument = channelDocument.copy(isWaiting = false)

        coEvery { channelService.getChannel(channelId) } returns channelDocument.success()

        val result = aiAdministrativeTriageService.replyMember(channelId, iaMessageContent)
        assertThat(result).isSuccessWithData(false)

        coVerifyOnce { channelService.getChannel(any()) }
    }

    @OptIn(FirestoreContextUsage::class)
    @Test
    fun `#replyMember returns success and reply the member`() = mocks {
        val channelDocument = channelDocument.copy(isWaiting = true)

        coEvery {
            channelService.getChannel(channelId)
        } returns channelDocument.success()

        coEvery {
            messageFirestoreService.add(
                channelId = channelId,
                messageDocument = MessageDocument(
                    userId = AI_STAFF_ID,
                    content = iaMessageContent,
                    type = MessageType.TEXT,
                    source = administrativeClassifyAiAssistantId,
                    createdAt = nowTimestamp
                )
            )
        } returns messageId.success()

        val result = aiAdministrativeTriageService.replyMember(channelId, iaMessageContent)
        assertThat(result).isSuccessWithData(messageId)

        coVerifyOnce { channelService.getChannel(any()) }
        coVerifyOnce { messageFirestoreService.add(any(), any()) }
    }

    @OptIn(FirestoreContextUsage::class)
    @Test
    fun `#replyMember returns success and reply the member and drop flow`() = mocks {
        val tags = channelDocument.tags.plusSafe(EXPLORER_TAG)
        val channelDocument = channelDocument.copy(isWaiting = true, tags = tags)
        val tagsToBeAdded = listOf(ROUTE_MLC_TAG, NO_FRICTION_TAG, ONGOING_TAG).toTypedArray()

        coEvery {
            channelService.getChannel(channelId)
        } returns channelDocument.success()

        coEvery {
            messageFirestoreService.add(
                channelId = channelId,
                messageDocument = MessageDocument(
                    userId = AI_STAFF_ID,
                    content = aiMessageResponseToDrop,
                    type = MessageType.TEXT,
                    source = administrativeClassifyAiAssistantId,
                    createdAt = nowTimestamp
                )
            )
        } returns messageId.success()

        coEvery {
            channelService.updateFields(
                channelId,
                mapOf(
                    ChannelDocument::subCategoryClassifier to null,
                    ChannelDocument::routedAt to nowTimestamp,
                    ChannelDocument::hideMemberInput to false,
                    ChannelDocument::typing to arrayRemove(AI_STAFF_ID),
                    ChannelDocument::tags to arrayUnion(*tagsToBeAdded)
                )
            )
        } returns channelId.success()

        coEvery {
            channelService.removeStaff(
                channel = channelDocument,
                staffId = AI_STAFF_ID,
                requesterStaffId = AI_STAFF_ID,
                notify = false,
                checkOwner = false,
                checkSize = false
            )
        } returns channelId.success()

        coEvery {
            asyncAssistantService.sendMessageAndRun(
                request = AsyncMessageRequest(
                    id = channelId,
                    content = summaryKeyword,
                    threadId = threadId,
                    assistantId = administrativeClassifyAiAssistantId,
                    responseTopic = AI_TRIAGE_ADMINISTRATIVE_TOPIC,
                    metadata = mapOf("attempt" to 0)
                )
            )
        } returns Unit.success()

        val result = aiAdministrativeTriageService.replyMember(channelId, aiMessageResponseToDrop)
        assertThat(result).isSuccessWithData(messageId)

        coVerifyOnce { channelService.getChannel(any()) }
        coVerifyOnce { messageFirestoreService.add(any(), any()) }
        coVerifyOnce { channelService.updateFields(any(), any()) }
        coVerifyOnce { channelService.removeStaff(any(), any(), any(), any(), any(), any()) }
        coVerifyOnce { asyncAssistantService.sendMessageAndRun(any()) }
    }

    @OptIn(FirestoreContextUsage::class)
    @Test
    fun `#replyMember returns success and reply the member and drop flow (friction case)`() = mocks {
        val tags = channelDocument.tags.plusSafe(EXPLORER_TAG)
        val channelDocument = channelDocument.copy(isWaiting = true, tags = tags)
        val tagsToBeAdded = listOf(ROUTE_MLC_TAG, FRICTION_TAG, ONGOING_TAG).toTypedArray()

        coEvery {
            channelService.getChannel(channelId)
        } returns channelDocument.success()

        coEvery {
            messageFirestoreService.add(
                channelId = channelId,
                messageDocument = MessageDocument(
                    userId = AI_STAFF_ID,
                    content = aiMessageResponseToDropAngry,
                    type = MessageType.TEXT,
                    source = administrativeClassifyAiAssistantId,
                    createdAt = nowTimestamp
                )
            )
        } returns messageId.success()

        coEvery {
            channelService.updateFields(
                channelId,
                mapOf(
                    ChannelDocument::subCategoryClassifier to null,
                    ChannelDocument::routedAt to nowTimestamp,
                    ChannelDocument::hideMemberInput to false,
                    ChannelDocument::typing to arrayRemove(AI_STAFF_ID),
                    ChannelDocument::tags to arrayUnion(*tagsToBeAdded)
                )
            )
        } returns channelId.success()

        coEvery {
            channelService.removeStaff(
                channel = channelDocument,
                staffId = AI_STAFF_ID,
                requesterStaffId = AI_STAFF_ID,
                notify = false,
                checkOwner = false,
                checkSize = false
            )
        } returns channelId.success()

        coEvery {
            asyncAssistantService.sendMessageAndRun(
                request = AsyncMessageRequest(
                    id = channelId,
                    content = summaryKeyword,
                    threadId = threadId,
                    assistantId = administrativeClassifyAiAssistantId,
                    responseTopic = AI_TRIAGE_ADMINISTRATIVE_TOPIC,
                    metadata = mapOf("attempt" to 0)
                )
            )
        } returns Unit.success()

        val result = aiAdministrativeTriageService.replyMember(channelId, aiMessageResponseToDropAngry)
        assertThat(result).isSuccessWithData(messageId)

        coVerifyOnce { channelService.getChannel(any()) }
        coVerifyOnce { messageFirestoreService.add(any(), any()) }
        coVerifyOnce { channelService.updateFields(any(), any()) }
        coVerifyOnce { channelService.removeStaff(any(), any(), any(), any(), any(), any()) }
        coVerifyOnce { asyncAssistantService.sendMessageAndRun(any()) }
    }

    @OptIn(FirestoreContextUsage::class)
    @Test
    fun `#replyMember returns success and reply the member and drop flow (talk to human case)`() = mocks {
        val tags = channelDocument.tags.plusSafe(EXPLORER_TAG)
        val channelDocument = channelDocument.copy(isWaiting = true, tags = tags)
        val tagsToBeAdded = listOf(ROUTE_MLC_TAG, HUMAN_CONTACT_TAG, ONGOING_TAG).toTypedArray()

        coEvery {
            channelService.getChannel(channelId)
        } returns channelDocument.success()

        coEvery {
            messageFirestoreService.add(
                channelId = channelId,
                messageDocument = MessageDocument(
                    userId = AI_STAFF_ID,
                    content = aiMessageResponseToDropByTalkToHuman,
                    type = MessageType.TEXT,
                    source = administrativeClassifyAiAssistantId,
                    createdAt = nowTimestamp
                )
            )
        } returns messageId.success()

        coEvery {
            channelService.updateFields(
                channelId,
                mapOf(
                    ChannelDocument::subCategoryClassifier to null,
                    ChannelDocument::routedAt to nowTimestamp,
                    ChannelDocument::hideMemberInput to false,
                    ChannelDocument::typing to arrayRemove(AI_STAFF_ID),
                    ChannelDocument::tags to arrayUnion(*tagsToBeAdded)
                )
            )
        } returns channelId.success()

        coEvery {
            channelService.removeStaff(
                channel = channelDocument,
                staffId = AI_STAFF_ID,
                requesterStaffId = AI_STAFF_ID,
                notify = false,
                checkOwner = false,
                checkSize = false
            )
        } returns channelId.success()

        coEvery {
            asyncAssistantService.sendMessageAndRun(
                request = AsyncMessageRequest(
                    id = channelId,
                    content = summaryKeyword,
                    threadId = threadId,
                    assistantId = administrativeClassifyAiAssistantId,
                    responseTopic = AI_TRIAGE_ADMINISTRATIVE_TOPIC,
                    metadata = mapOf("attempt" to 0)
                )
            )
        } returns Unit.success()

        val result = aiAdministrativeTriageService.replyMember(channelId, aiMessageResponseToDropByTalkToHuman)
        assertThat(result).isSuccessWithData(messageId)

        coVerifyOnce { channelService.getChannel(any()) }
        coVerifyOnce { messageFirestoreService.add(any(), any()) }
        coVerifyOnce { channelService.updateFields(any(), any()) }
        coVerifyOnce { channelService.removeStaff(any(), any(), any(), any(), any(), any()) }
        coVerifyOnce { asyncAssistantService.sendMessageAndRun(any()) }
    }

    @OptIn(FirestoreContextUsage::class)
    @Test
    fun `#sendMessageToChannel returns message id for public message`() = mocks {
        coEvery {
            messageFirestoreService.add(
                channelId = channelId,
                messageDocument = MessageDocument(
                    userId = AI_STAFF_ID,
                    content = aiMessageResponseToDrop,
                    type = MessageType.TEXT,
                    source = administrativeClassifyAiAssistantId,
                    createdAt = nowTimestamp
                ),
                isPrivate = false
            )
        } returns messageId.success()

        val result = aiAdministrativeTriageService.sendMessageToChannel(channelDocument, aiMessageResponseToDrop, false)
        assertThat(result).isSuccessWithData(messageId)

        coVerifyOnce { messageFirestoreService.add(any(), any()) }
    }

    @OptIn(FirestoreContextUsage::class)
    @Test
    fun `#sendMessageToChannel returns message id for private message`() = mocks {
        coEvery {
            messageFirestoreService.add(
                channelId = channelId,
                messageDocument = MessageDocument(
                    userId = AI_STAFF_ID,
                    content = aiMessageResponseToDrop,
                    type = MessageType.TEXT,
                    source = administrativeClassifyAiAssistantId,
                    createdAt = nowTimestamp
                ),
                isPrivate = true
            )
        } returns messageId.success()

        val result = aiAdministrativeTriageService.sendMessageToChannel(channelDocument, aiMessageResponseToDrop, true)
        assertThat(result).isSuccessWithData(messageId)

        coVerifyOnce { messageFirestoreService.add(any(), any(), any()) }
    }

    @Test
    fun `#sendMessageToAi returns success and send message to AI`() = mocks {
        coEvery {
            asyncAssistantService.sendMessage(
                request = AsyncMessageRequest(
                    id = channelId,
                    content = userMessageContent,
                    threadId = threadId,
                    assistantId = administrativeClassifyAiAssistantId,
                    responseTopic = AI_TRIAGE_ADMINISTRATIVE_TOPIC,
                    metadata = mapOf(
                        "messageId" to messageId,
                        "attempt" to 2
                    )
                )
            )
        } returns Unit.success()

        val result = aiAdministrativeTriageService.sendMessageToAi(channelDocument, messageDocument, 2)
        assertThat(result).isSuccessWithData(Unit)

        coVerifyOnce { asyncAssistantService.sendMessage(any()) }
    }

    @Test
    fun `#sendMessageToAi returns success and send message to AI when B2C`() = mocks {
        val channelDocument = channelDocument.copy(
            aiInstructions = additionalInstructions,
            administrativeDetails = AdministrativeDetails(
                memberType = AdministrativeDetailMemberType.B2C
            )
        )
        coEvery {
            asyncAssistantService.sendMessage(
                request = AsyncMessageRequest(
                    id = channelId,
                    content = userMessageContent,
                    threadId = threadId,
                    assistantId = administrativeExplorerB2CAiAssistantId,
                    responseTopic = AI_TRIAGE_ADMINISTRATIVE_TOPIC,
                    additionalInstructions = additionalInstructions,
                    metadata = mapOf(
                        "messageId" to messageId,
                        "attempt" to 2
                    )
                )
            )
        } returns Unit.success()

        val result = aiAdministrativeTriageService.sendMessageToAi(channelDocument, messageDocument, 2)
        assertThat(result).isSuccessWithData(Unit)

        coVerifyOnce { asyncAssistantService.sendMessage(any()) }
    }

    @Test
    fun `#sendMessageToAi returns success and send message to AI when B2B`() = mocks {
        val channelDocument = channelDocument.copy(
            aiInstructions = additionalInstructions,
            administrativeDetails = AdministrativeDetails(
                memberType = AdministrativeDetailMemberType.B2B
            )
        )
        coEvery {
            asyncAssistantService.sendMessage(
                request = AsyncMessageRequest(
                    id = channelId,
                    content = userMessageContent,
                    threadId = threadId,
                    assistantId = administrativeExplorerB2B5AiAssistantId,
                    responseTopic = AI_TRIAGE_ADMINISTRATIVE_TOPIC,
                    additionalInstructions = additionalInstructions,
                    metadata = mapOf(
                        "messageId" to messageId,
                        "attempt" to 2
                    )
                )
            )
        } returns Unit.success()

        val result = aiAdministrativeTriageService.sendMessageToAi(channelDocument, messageDocument, 2)
        assertThat(result).isSuccessWithData(Unit)

        coVerifyOnce { asyncAssistantService.sendMessage(any()) }
    }

    @Test
    fun `#sendMessageToAi returns success and send message to AI when B2B with more or equal than 6 lives`() = mocks {
        val channelDocument = channelDocument.copy(
            aiInstructions = additionalInstructions,
            administrativeDetails = AdministrativeDetails(
                memberType = AdministrativeDetailMemberType.B2B,
                companyLifesAmount = 6
            )
        )
        coEvery {
            asyncAssistantService.sendMessage(
                request = AsyncMessageRequest(
                    id = channelId,
                    content = userMessageContent,
                    threadId = threadId,
                    assistantId = administrativeExplorerB2B6AiAssistantId,
                    responseTopic = AI_TRIAGE_ADMINISTRATIVE_TOPIC,
                    additionalInstructions = additionalInstructions,
                    metadata = mapOf(
                        "messageId" to messageId,
                        "attempt" to 2
                    )
                )
            )
        } returns Unit.success()

        val result = aiAdministrativeTriageService.sendMessageToAi(channelDocument, messageDocument, 2)
        assertThat(result).isSuccessWithData(Unit)

        coVerifyOnce { asyncAssistantService.sendMessage(any()) }
    }

    @OptIn(FirestoreContextUsage::class)
    @Test
    fun `#generateChannelSummary returns true send summary message to AI`() = mocks {
        coEvery {
            asyncAssistantService.sendMessageAndRun(
                request = AsyncMessageRequest(
                    id = channelId,
                    content = summaryKeyword,
                    threadId = threadId,
                    assistantId = administrativeClassifyAiAssistantId,
                    responseTopic = AI_TRIAGE_ADMINISTRATIVE_SUMMARY_TOPIC,
                    metadata = mapOf("attempt" to 1)
                )
            )
        } returns Unit.success()

        val result = aiAdministrativeTriageService.generateChannelSummary(channelDocument, 1)
        assertThat(result).isSuccessWithData(true)

        coVerifyOnce { asyncAssistantService.sendMessageAndRun(any()) }
    }

    @OptIn(FirestoreContextUsage::class)
    @Test
    fun `#processAssistantResponse should replyMember with success when assistant is not classify`() = mocks {
        val generalMessageIA = "AI response"
        val tags = channelDocument.tags.plusSafe(EXPLORER_TAG)
        val channelDocument = channelDocument.copy(isWaiting = true, aiInstructions = generalMessageIA, tags = tags)

        coEvery {
            channelService.getChannel(channelId)
        } returns channelDocument.success()

        coEvery {
            channelService.getChannel(channelId)
        } returns channelDocument.success()

        coEvery {
            messageFirestoreService.add(
                channelId = channelId,
                messageDocument = MessageDocument(
                    userId = AI_STAFF_ID,
                    content = generalMessageIA,
                    type = MessageType.TEXT,
                    source = administrativeExplorerB2B5AiAssistantId,
                    createdAt = nowTimestamp
                )
            )
        } returns messageId.success()

        val result = aiAdministrativeTriageService.processAssistantResponse(channelId, generalMessageIA, channelId)
        assertThat(result).isSuccessWithData(messageId)

        coVerify(exactly = 2) { channelService.getChannel(any()) }
        coVerifyNone { channelService.updateFields(any(), any()) }
        coVerifyNone { channelService.removeStaff(any(), any(), any(), any(), any(), any()) }
        coVerifyOnce { messageFirestoreService.add(any(), any()) }
    }

    @OptIn(FirestoreContextUsage::class)
    @Test
    fun `#processAssistantResponse should replyMember with success when assistant is not classify and add explorer tag when its first time`() =
        mocks {
            val generalMessageIA = "AI response"
            val channelDocument = channelDocument.copy(isWaiting = true, aiInstructions = generalMessageIA)

            coEvery {
                channelService.getChannel(channelId)
            } returns channelDocument.success()

            coEvery {
                channelService.updateFields(
                    channelId, mapOf(
                        ChannelDocument::tags to arrayUnion(EXPLORER_TAG)
                    )
                )
            } returns channelId.success()

            coEvery {
                messageFirestoreService.add(
                    channelId = channelId,
                    messageDocument = MessageDocument(
                        userId = AI_STAFF_ID,
                        content = generalMessageIA,
                        type = MessageType.TEXT,
                        source = administrativeExplorerB2B5AiAssistantId,
                        createdAt = nowTimestamp
                    )
                )
            } returns messageId.success()

            val result = aiAdministrativeTriageService.processAssistantResponse(channelId, generalMessageIA, channelId)
            assertThat(result).isSuccessWithData(messageId)

            coVerify(exactly = 2) { channelService.getChannel(any()) }
            coVerifyOnce { channelService.updateFields(any(), any()) }
            coVerifyNone { channelService.removeStaff(any(), any(), any(), any(), any(), any()) }
            coVerifyOnce { messageFirestoreService.add(any(), any()) }
        }

    @OptIn(FirestoreContextUsage::class)
    @Test
    fun `#processAssistantResponse should runThread with success when assistant is classify and could not be redirected`() =
        mocks {
            val generalMessageIA = "AI response"
            val channelDocument = channelDocument.copy(isWaiting = true, aiInstructions = null)

            coEvery {
                channelService.getChannel(channelId)
            } returns channelDocument.success()

            coEvery {
                channelService.updateFields(
                    channelId,
                    mapOf(ChannelDocument::aiInstructions to generalMessageIA)
                )
            } returns channelId.success()

            coEvery {
                asyncAssistantService.runThread(
                    AsyncRunThreadRequest(
                        id = channelId,
                        threadId = threadId,
                        assistantId = administrativeExplorerB2B5AiAssistantId,
                        responseTopic = AI_TRIAGE_ADMINISTRATIVE_TOPIC,
                        metadata = mapOf("attempt" to 0),
                        additionalInstructions = generalMessageIA,
                        toolChoice = null
                    )
                )
            } returns Unit.success()

            val result = aiAdministrativeTriageService.processAssistantResponse(channelId, generalMessageIA, channelId)
            assertThat(result).isSuccess()

            coVerifyOnce { channelService.getChannel(any()) }
            coVerifyOnce { channelService.updateFields(any(), any()) }
            coVerifyNone { channelService.removeStaff(any(), any(), any(), any(), any(), any()) }
            coVerifyOnce { asyncAssistantService.runThread(any()) }
            coVerifyNone { messageFirestoreService.add(any(), any()) }
        }

    @OptIn(FirestoreContextUsage::class)
    @Test
    fun `#processAssistantResponse should runThread without toolChoice`() =
        mocks {
            withFeatureFlag(
                FeatureNamespace.CHANNELS,
                "administrative_explorer_ai_b2c_assistant_id",
                listOf(administrativeExplorerB2CAiAssistantId, "file_search=false")
            ) {
                val generalMessageIA = "AI response"
                val channelDocument = channelDocument.copy(isWaiting = true, aiInstructions = null)

                coEvery {
                    channelService.getChannel(channelId)
                } returns channelDocument.success()

                coEvery {
                    channelService.updateFields(
                        channelId,
                        mapOf(ChannelDocument::aiInstructions to generalMessageIA)
                    )
                } returns channelId.success()

                coEvery {
                    asyncAssistantService.runThread(
                        AsyncRunThreadRequest(
                            id = channelId,
                            threadId = threadId,
                            assistantId = administrativeExplorerB2B5AiAssistantId,
                            responseTopic = AI_TRIAGE_ADMINISTRATIVE_TOPIC,
                            metadata = mapOf("attempt" to 0),
                            additionalInstructions = generalMessageIA,
                            toolChoice = null
                        )
                    )
                } returns Unit.success()

                val result =
                    aiAdministrativeTriageService.processAssistantResponse(channelId, generalMessageIA, channelId)
                assertThat(result).isSuccess()

                coVerifyOnce { channelService.getChannel(any()) }
                coVerifyOnce { channelService.updateFields(any(), any()) }
                coVerifyNone { channelService.removeStaff(any(), any(), any(), any(), any(), any()) }
                coVerifyOnce { asyncAssistantService.runThread(any()) }
                coVerifyNone { messageFirestoreService.add(any(), any()) }
            }
        }


    @OptIn(FirestoreContextUsage::class)
    @Test
    fun `#processAssistantResponse should drop and be redirected to MLC`() =
        mocks {
            val redirectToMLCAiResponse = "indefinido"
            val channelDocument = channelDocument.copy(isWaiting = true, aiInstructions = null)
            val tagsToBeAdded = listOf(ROUTE_MLC_TAG, UNDEFINED_TAG, ONGOING_TAG).toTypedArray()

            coEvery {
                channelService.getChannel(channelId)
            } returns channelDocument.success()

            coEvery {
                channelService.updateFields(
                    channelId,
                    mapOf(ChannelDocument::aiInstructions to redirectToMLCAiResponse)
                )
            } returns channelId.success()

            coEvery {
                channelService.updateFields(
                    channelId,
                    mapOf(
                        ChannelDocument::subCategoryClassifier to null,
                        ChannelDocument::routedAt to nowTimestamp,
                        ChannelDocument::hideMemberInput to false,
                        ChannelDocument::typing to arrayRemove(AI_STAFF_ID),
                        ChannelDocument::tags to arrayUnion(*tagsToBeAdded)
                    )
                )
            } returns channelId.success()

            coEvery {
                channelService.removeStaff(
                    channel = channelDocument,
                    staffId = AI_STAFF_ID,
                    requesterStaffId = AI_STAFF_ID,
                    notify = false,
                    checkOwner = false,
                    checkSize = false
                )
            } returns channelId.success()

            val result =
                aiAdministrativeTriageService.processAssistantResponse(channelId, redirectToMLCAiResponse, channelId)
            assertThat(result).isSuccess()

            coVerifyOnce { channelService.getChannel(any()) }
            coVerify(exactly = 2) { channelService.updateFields(any(), any()) }
            coVerifyOnce { channelService.removeStaff(any(), any(), any(), any(), any(), any()) }
            coVerifyNone { asyncAssistantService.runThread(any()) }
            coVerifyNone { messageFirestoreService.add(any(), any()) }
        }

    @OptIn(FirestoreContextUsage::class)
    @Test
    fun `#processAssistantResponse should drop and be redirected to MLC with dirty string`() =
        mocks {
            val redirectToMLCAiResponse = "\"indefinido\"【0:0†any_file_source】"
            val channelDocument = channelDocument.copy(isWaiting = true, aiInstructions = null)
            val tagsToBeAdded = listOf(ROUTE_MLC_TAG, UNDEFINED_TAG, ONGOING_TAG).toTypedArray()

            coEvery {
                channelService.getChannel(channelId)
            } returns channelDocument.success()

            coEvery {
                channelService.updateFields(
                    channelId,
                    mapOf(
                        ChannelDocument::aiInstructions to redirectToMLCAiResponse.removeDoubleQuotes()
                            .removeOpenAiCitations()
                    )
                )
            } returns channelId.success()

            coEvery {
                channelService.updateFields(
                    channelId,
                    mapOf(
                        ChannelDocument::subCategoryClassifier to null,
                        ChannelDocument::routedAt to nowTimestamp,
                        ChannelDocument::hideMemberInput to false,
                        ChannelDocument::typing to arrayRemove(AI_STAFF_ID),
                        ChannelDocument::tags to arrayUnion(*tagsToBeAdded)
                    )
                )
            } returns channelId.success()

            coEvery {
                channelService.removeStaff(
                    channel = channelDocument,
                    staffId = AI_STAFF_ID,
                    requesterStaffId = AI_STAFF_ID,
                    notify = false,
                    checkOwner = false,
                    checkSize = false
                )
            } returns channelId.success()

            val result =
                aiAdministrativeTriageService.processAssistantResponse(channelId, redirectToMLCAiResponse, channelId)
            assertThat(result).isSuccess()

            coVerifyOnce { channelService.getChannel(any()) }
            coVerify(exactly = 2) { channelService.updateFields(any(), any()) }
            coVerifyOnce { channelService.removeStaff(any(), any(), any(), any(), any(), any()) }
            coVerifyNone { asyncAssistantService.runThread(any()) }
            coVerifyNone { messageFirestoreService.add(any(), any()) }
        }

    @OptIn(FirestoreContextUsage::class)
    @Test
    fun `#processAssistantResponse should drop and be redirected to APEX`() =
        mocks {
            val redirectToAPEXAiResponse = "reembolso"
            val channelDocument = channelDocument.copy(isWaiting = true, aiInstructions = null)
            val tagsToBeAdded = listOf(ROUTE_APEX_TAG, ONGOING_TAG).toTypedArray()

            coEvery {
                channelService.getChannel(channelId)
            } returns channelDocument.success()

            coEvery {
                channelService.updateFields(
                    channelId,
                    mapOf(ChannelDocument::aiInstructions to redirectToAPEXAiResponse)
                )
            } returns channelId.success()

            coEvery {
                channelService.updateFields(
                    channelId,
                    mapOf(
                        ChannelDocument::subCategoryClassifier to null,
                        ChannelDocument::routedAt to nowTimestamp,
                        ChannelDocument::hideMemberInput to false,
                        ChannelDocument::typing to arrayRemove(AI_STAFF_ID),
                        ChannelDocument::tags to arrayUnion(*tagsToBeAdded)
                    )
                )
            } returns channelId.success()

            coEvery {
                channelService.removeStaff(
                    channel = channelDocument,
                    staffId = AI_STAFF_ID,
                    requesterStaffId = AI_STAFF_ID,
                    notify = false,
                    checkOwner = false,
                    checkSize = false
                )
            } returns channelId.success()

            val result =
                aiAdministrativeTriageService.processAssistantResponse(channelId, redirectToAPEXAiResponse, channelId)
            assertThat(result).isSuccess()

            coVerifyOnce { channelService.getChannel(any()) }
            coVerify(exactly = 2) { channelService.updateFields(any(), any()) }
            coVerifyOnce { channelService.removeStaff(any(), any(), any(), any(), any(), any()) }
            coVerifyNone { asyncAssistantService.runThread(any()) }
            coVerifyNone { messageFirestoreService.add(any(), any()) }
        }

    @OptIn(FirestoreContextUsage::class)
    @Test
    fun `#processAssistantResponse should drop and be redirected to APEX with dirty string`() =
        mocks {
            val redirectToAPEXAiResponse = "\"reembolso\" 【0:0†any_file_source】"
            val channelDocument = channelDocument.copy(isWaiting = true, aiInstructions = null)
            val tagsToBeAdded = listOf(ROUTE_APEX_TAG, ONGOING_TAG).toTypedArray()

            coEvery {
                channelService.getChannel(channelId)
            } returns channelDocument.success()

            coEvery {
                channelService.updateFields(
                    channelId,
                    mapOf(
                        ChannelDocument::aiInstructions to redirectToAPEXAiResponse.removeDoubleQuotes()
                            .removeOpenAiCitations()
                    )
                )
            } returns channelId.success()

            coEvery {
                channelService.updateFields(
                    channelId,
                    mapOf(
                        ChannelDocument::subCategoryClassifier to null,
                        ChannelDocument::routedAt to nowTimestamp,
                        ChannelDocument::hideMemberInput to false,
                        ChannelDocument::typing to arrayRemove(AI_STAFF_ID),
                        ChannelDocument::tags to arrayUnion(*tagsToBeAdded)
                    )
                )
            } returns channelId.success()

            coEvery {
                channelService.removeStaff(
                    channel = channelDocument,
                    staffId = AI_STAFF_ID,
                    requesterStaffId = AI_STAFF_ID,
                    notify = false,
                    checkOwner = false,
                    checkSize = false
                )
            } returns channelId.success()

            val result =
                aiAdministrativeTriageService.processAssistantResponse(channelId, redirectToAPEXAiResponse, channelId)
            assertThat(result).isSuccess()

            coVerifyOnce { channelService.getChannel(any()) }
            coVerify(exactly = 2) { channelService.updateFields(any(), any()) }
            coVerifyOnce { channelService.removeStaff(any(), any(), any(), any(), any(), any()) }
            coVerifyNone { asyncAssistantService.runThread(any()) }
            coVerifyNone { messageFirestoreService.add(any(), any()) }
        }

    @OptIn(FirestoreContextUsage::class)
    @Test
    fun `#processAssistantResponse should drop and be redirected to MLC when detect open invoices reason`() =
        mocks {
            val aiResponse = "Solicitação de boletos em aberto"
            val channelDocument = channelDocument.copy(isWaiting = true, aiInstructions = null)
            val tagsToBeAdded = listOf(ROUTE_MLC_TAG, UNDEFINED_TAG, ONGOING_TAG).toTypedArray()

            coEvery {
                channelService.getChannel(channelId)
            } returns channelDocument.success()

            coEvery {
                channelService.updateFields(
                    channelId,
                    mapOf(ChannelDocument::aiInstructions to aiResponse)
                )
            } returns channelId.success()

            coEvery {
                channelService.updateFields(
                    channelId,
                    mapOf(
                        ChannelDocument::subCategoryClassifier to null,
                        ChannelDocument::routedAt to nowTimestamp,
                        ChannelDocument::hideMemberInput to false,
                        ChannelDocument::typing to arrayRemove(AI_STAFF_ID),
                        ChannelDocument::tags to arrayUnion(*tagsToBeAdded)
                    )
                )
            } returns channelId.success()

            coEvery {
                channelService.removeStaff(
                    channel = channelDocument,
                    staffId = AI_STAFF_ID,
                    requesterStaffId = AI_STAFF_ID,
                    notify = false,
                    checkOwner = false,
                    checkSize = false
                )
            } returns channelId.success()

            val result =
                aiAdministrativeTriageService.processAssistantResponse(channelId, aiResponse, channelId)
            assertThat(result).isSuccess()

            coVerifyOnce { channelService.getChannel(any()) }
            coVerify(exactly = 2) { channelService.updateFields(any(), any()) }
            coVerifyOnce { channelService.removeStaff(any(), any(), any(), any(), any(), any()) }
            coVerifyNone { asyncAssistantService.runThread(any()) }
            coVerifyNone { messageFirestoreService.add(any(), any()) }
        }

    @OptIn(FirestoreContextUsage::class)
    @Test
    fun `#processAssistantResponse should drop and be redirected to MLC when detect open invoices reason with dirty string`() =
        mocks {
            val aiResponse = "Solicitação de boletos em \"aberto\" 【0:0†any_file_source】"
            val channelDocument = channelDocument.copy(isWaiting = true, aiInstructions = null)
            val tagsToBeAdded = listOf(ROUTE_MLC_TAG, UNDEFINED_TAG, ONGOING_TAG).toTypedArray()

            coEvery {
                channelService.getChannel(channelId)
            } returns channelDocument.success()

            coEvery {
                channelService.updateFields(
                    channelId,
                    mapOf(ChannelDocument::aiInstructions to aiResponse.removeDoubleQuotes().removeOpenAiCitations())
                )
            } returns channelId.success()

            coEvery {
                channelService.updateFields(
                    channelId,
                    mapOf(
                        ChannelDocument::subCategoryClassifier to null,
                        ChannelDocument::routedAt to nowTimestamp,
                        ChannelDocument::hideMemberInput to false,
                        ChannelDocument::typing to arrayRemove(AI_STAFF_ID),
                        ChannelDocument::tags to arrayUnion(*tagsToBeAdded)
                    )
                )
            } returns channelId.success()

            coEvery {
                channelService.removeStaff(
                    channel = channelDocument,
                    staffId = AI_STAFF_ID,
                    requesterStaffId = AI_STAFF_ID,
                    notify = false,
                    checkOwner = false,
                    checkSize = false
                )
            } returns channelId.success()

            val result =
                aiAdministrativeTriageService.processAssistantResponse(channelId, aiResponse, channelId)
            assertThat(result).isSuccess()

            coVerifyOnce { channelService.getChannel(any()) }
            coVerify(exactly = 2) { channelService.updateFields(any(), any()) }
            coVerifyOnce { channelService.removeStaff(any(), any(), any(), any(), any(), any()) }
            coVerifyNone { asyncAssistantService.runThread(any()) }
            coVerifyNone { messageFirestoreService.add(any(), any()) }
        }

    @OptIn(FirestoreContextUsage::class)
    @Test
    fun `#dropAndRoute should drop and sendMessage`() =
        mocks {
            val channelDocument = channelDocument.copy(isWaiting = true, aiInstructions = null)
            val tagsToBeAdded = listOf(ROUTE_MLC_TAG, ONGOING_TAG).toTypedArray()

            coEvery {
                channelService.updateFields(
                    channelId,
                    mapOf(
                        ChannelDocument::subCategoryClassifier to null,
                        ChannelDocument::routedAt to nowTimestamp,
                        ChannelDocument::hideMemberInput to false,
                        ChannelDocument::typing to arrayRemove(AI_STAFF_ID),
                        ChannelDocument::tags to arrayUnion(*tagsToBeAdded)
                    )
                )
            } returns channelId.success()

            coEvery {
                messageFirestoreService.add(
                    channelId = channelId,
                    messageDocument = MessageDocument(
                        userId = AI_STAFF_ID,
                        content = administrativeAiDropMessage,
                        type = MessageType.TEXT,
                        source = administrativeClassifyAiAssistantId,
                        createdAt = nowTimestamp
                    )
                )
            } returns messageId.success()

            coEvery {
                channelService.removeStaff(
                    channel = channelDocument,
                    staffId = AI_STAFF_ID,
                    requesterStaffId = AI_STAFF_ID,
                    notify = false,
                    checkOwner = false,
                    checkSize = false
                )
            } returns channelId.success()

            val result =
                aiAdministrativeTriageService.dropAndRoute(channelDocument, listOf(ROUTE_MLC_TAG), true)
            assertThat(result).isSuccess()

            coVerifyOnce { channelService.updateFields(any(), any()) }
            coVerifyOnce { channelService.removeStaff(any(), any(), any(), any(), any(), any()) }
            coVerifyNone { asyncAssistantService.runThread(any()) }
            coVerifyOnce { messageFirestoreService.add(any(), any()) }
        }

    private fun mocks(testFunction: suspend () -> Unit) = runBlocking {
        withFeatureFlags(
            FeatureNamespace.CHANNELS,
            mapOf(
                "administrative_ai_drop_common_phrases" to administrativeAiDropCommonPhrases,
                "administrative_ai_drop_talk_to_human_phrases" to administrativeAiDropByTalkToHumanPhrases,
                "administrative_ai_drop_friction_phrases" to administrativeAiDropFrictionPhrases,
                "administrative_message_of_ai_drop" to administrativeAiDropMessage,
                "administrative_classify_ai_assistant_id" to administrativeClassifyAiAssistantId,
                "administrative_explorer_ai_b2c_assistant_id" to listOf(
                    administrativeExplorerB2CAiAssistantId,
                    "file_search=true"
                ),
                "administrative_explorer_ai_b2b_5_assistant_id" to administrativeExplorerB2B5AiAssistantId,
                "administrative_explorer_ai_b2b_6_assistant_id" to administrativeExplorerB2B6AiAssistantId
            )
        ) {
            mockLocalDateTime(nowDateTime) {
                mockTimestamp(nowTimestamp) {
                    testFunction()
                }
            }
        }
    }
}
