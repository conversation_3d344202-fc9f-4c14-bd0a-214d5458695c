package br.com.alice.coverage.controllers

import br.com.alice.common.Response
import br.com.alice.common.controllers.Controller
import br.com.alice.common.core.Status
import br.com.alice.common.extensions.mapEach
import br.com.alice.common.foldResponse
import br.com.alice.common.kafka.interfaces.KafkaProducerService
import br.com.alice.common.withRootServicePolicy
import br.com.alice.common.withUnauthenticatedTokenWithKey
import br.com.alice.coverage.br.com.alice.coverage.event.AccreditedNetworkCassiOrigin
import br.com.alice.coverage.br.com.alice.coverage.event.AccreditedNetworkRegisterCassiEvent
import br.com.alice.coverage.br.com.alice.coverage.event.AccreditedNetworkRegisterHealthProfessionalEvent
import br.com.alice.coverage.br.com.alice.coverage.event.ProviderBackfillChangeEvent
import br.com.alice.data.layer.COVERAGE_DOMAIN_ROOT_SERVICE_NAME
import br.com.alice.data.layer.models.SpecialistStatus
import br.com.alice.provider.client.ProviderFilter
import br.com.alice.provider.client.ProviderService
import br.com.alice.staff.client.CassiSpecialistService
import br.com.alice.staff.client.HealthProfessionalService

class ConsolidatedTableBackfillController(
    private val healthProfessionalService: HealthProfessionalService,
    private val cassiSpecialistService: CassiSpecialistService,
    private val providerService: ProviderService,
    private val kafkaProducerService: KafkaProducerService,
) : Controller() {
    private suspend fun withBackfillEnvironment(func: suspend () -> Response) =
        withRootServicePolicy(COVERAGE_DOMAIN_ROOT_SERVICE_NAME) {
            withUnauthenticatedTokenWithKey(COVERAGE_DOMAIN_ROOT_SERVICE_NAME) {
                func.invoke()
            }
        }

    suspend fun sendHealthProfessionalEvent(request: ConsolidatedBackfill) = withBackfillEnvironment {
        healthProfessionalService.getByRange(IntRange(request.offset, request.offset + request.limit)).mapEach {
            kafkaProducerService.produce(AccreditedNetworkRegisterHealthProfessionalEvent(it))
        }.foldResponse()
    }


    suspend fun sendCassiEvent(request: ConsolidatedBackfill) = withBackfillEnvironment {
        cassiSpecialistService.getByRangeAndFilters(
            range = IntRange(request.offset, request.offset + request.limit),
            namePrefix = null,
            statuses = listOf(SpecialistStatus.ACTIVE, SpecialistStatus.INACTIVE)
        ).mapEach {
            kafkaProducerService.produce(
                AccreditedNetworkRegisterCassiEvent(it.id, AccreditedNetworkCassiOrigin.BACKFILL)
            )
        }.foldResponse()
    }

    suspend fun sendProviderServiceEvent(request: ConsolidatedBackfill) = withBackfillEnvironment {
        providerService.getByFiltersWithRange(
            ProviderFilter(status = listOf(Status.ACTIVE)),
            IntRange(request.offset, request.offset + request.limit)
        ).mapEach {
            kafkaProducerService.produce(ProviderBackfillChangeEvent(it.id))
        }.foldResponse()
    }

    data class ConsolidatedBackfill(
        val limit: Int,
        val offset: Int
    )
}
