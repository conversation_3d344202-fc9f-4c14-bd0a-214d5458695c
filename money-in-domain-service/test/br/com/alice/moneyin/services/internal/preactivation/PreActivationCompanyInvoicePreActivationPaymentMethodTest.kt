package br.com.alice.moneyin.services.internal.preactivation

import br.com.alice.common.PaymentMethod
import br.com.alice.common.RangeUUID
import br.com.alice.common.core.extensions.atBeginningOfTheMonth
import br.com.alice.common.data.dsl.matchers.ResultAssert
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.common.helpers.returns
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.InvoicePaymentOrigin
import br.com.alice.data.layer.models.PreActivationPaymentStatus
import br.com.alice.moneyin.client.InvoicePaymentService
import br.com.alice.moneyin.client.PreActivationPaymentService
import io.mockk.coEvery
import io.mockk.mockk
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.Nested
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.MethodSource
import java.time.LocalDate
import java.util.UUID
import kotlin.test.Test

class PreActivationCompanyInvoicePreActivationPaymentMethodTest {

    private val preActivationPaymentService: PreActivationPaymentService = mockk()
    private val invoicePaymentService: InvoicePaymentService = mockk()

    val method =
        PreActivationCompanyInvoicePreActivationPaymentMethod(preActivationPaymentService, invoicePaymentService)

    companion object {
        @JvmStatic
        fun memberInvoiceIds() = listOf(
            arrayOf(listOf(RangeUUID.generate()), true),
            arrayOf((0..50).toList().map { RangeUUID.generate() }, false),
        )
    }

    @Nested
    inner class CreateInvoicePayment {

        @ParameterizedTest
        @MethodSource("br.com.alice.moneyin.services.internal.preactivation.PreActivationCompanyInvoicePreActivationPaymentMethodTest#memberInvoiceIds")
        fun `#should create an invoice payment`(memberInvoiceIds: List<UUID>, shouldUseSyncProcess: Boolean) =
            runBlocking {
                val preActivationPayment = TestModelFactory.buildPreActivationPayment(
                    memberInvoiceIds = memberInvoiceIds,
                )

                val paymentMethod = PaymentMethod.BOLEPIX
                val invoicePayment = TestModelFactory.buildInvoicePayment(method = paymentMethod)
                val dueDate = LocalDate.now()
                val origin = InvoicePaymentOrigin.ISSUED_BY_STAFF

                coEvery { preActivationPaymentService.get(preActivationPayment.id) } returns preActivationPayment
                coEvery {
                    invoicePaymentService.createFromPreActivationPayment(
                        preActivationPayment,
                        paymentMethod,
                        dueDate = dueDate,
                        origin = origin,
                        copyMemberInvoiceIds = true,
                        syncProcess = shouldUseSyncProcess,
                    )
                } returns invoicePayment

                val result = method.createInvoicePayment(
                    CreateInvoicePaymentRequestDTO(
                        preActivationCompanyId = preActivationPayment.id,
                        paymentMethod = paymentMethod,
                        origin = origin,
                        dueDate = dueDate,
                    )
                )

                ResultAssert.assertThat(result)
                    .isSuccessWithData(
                        CreateInvoicePaymentResponseDTO(
                            id = invoicePayment.id,
                            preActivationCompanyInvoiceId = preActivationPayment.id,
                            PreActivationCompanyInvoiceTypeDTO.PRE_ACTIVATION_PAYMENT,
                        )
                    )

                coVerifyOnce {
                    preActivationPaymentService.get(preActivationPayment.id)
                    invoicePaymentService.createFromPreActivationPayment(
                        preActivationPayment,
                        paymentMethod,
                        dueDate = dueDate,
                        origin = origin,
                        copyMemberInvoiceIds = true,
                        syncProcess = shouldUseSyncProcess,
                    )
                }
            }
    }

    @Test
    fun `should create a pre activation payment for pre activation company invoice`() = runBlocking {
        val preActivationPayment = TestModelFactory.buildPreActivationPayment()
        val member = TestModelFactory.buildMember()
        val members = listOf(member)
        val dueDate = LocalDate.now()
        val referenceDate = LocalDate.now()
        val origin = InvoicePaymentOrigin.ISSUED_BY_STAFF
        val companySubContract =
            TestModelFactory.buildCompanySubContract(
                billingAccountablePartyId = RangeUUID.generate(),
            )


        coEvery {
            preActivationPaymentService.generateForB2B(
                companyId = companySubContract.companyId,
                companySubContractId = companySubContract.id,
                members = members,
                billingAccountablePartyId = companySubContract.billingAccountablePartyId!!,
                referenceDate = referenceDate.atBeginningOfTheMonth(),
                dueDate = dueDate,
                paymentMethod = PaymentMethod.BOLEPIX,
                paymentOrigin = origin,
            )
        } returns preActivationPayment


        val result = method.create(
            CreateInvoiceRequestDTO(
                companyId = companySubContract.companyId,
                companySubContractId = companySubContract.id,
                billingAccountablePartyId = companySubContract.billingAccountablePartyId!!,
                paymentMethod = PaymentMethod.BOLEPIX,
                members = members,
                dueDate = dueDate,
                referenceDate = referenceDate,
                origin = origin,
            )
        )

        ResultAssert.assertThat(result)
            .isSuccessWithData(
                CreateInvoiceResponseDTO(
                    id = preActivationPayment.id,
                    PreActivationCompanyInvoiceTypeDTO.PRE_ACTIVATION_PAYMENT,
                )
            )

        coVerifyOnce {
            preActivationPaymentService.generateForB2B(
                companyId = companySubContract.companyId,
                companySubContractId = companySubContract.id,
                members = members,
                billingAccountablePartyId = companySubContract.billingAccountablePartyId!!,
                referenceDate = referenceDate.atBeginningOfTheMonth(),
                dueDate = dueDate,
                paymentMethod = PaymentMethod.BOLEPIX,
                paymentOrigin = origin,
            )
        }
    }

    @Test
    fun `should cancel the pre activation company invoice`() = runBlocking {
        val preActivationPayment = TestModelFactory.buildPreActivationPayment()

        val preActivationPaymentCanceled = preActivationPayment.copy(status = PreActivationPaymentStatus.CANCELED)

        coEvery { preActivationPaymentService.cancelById(preActivationPayment.id) } returns preActivationPaymentCanceled

        val result = method.cancel(preActivationPayment.id)

        ResultAssert.assertThat(result)
            .isSuccessWithData(
                CancelInvoiceResponseDTO(
                    id = preActivationPayment.id,
                    PreActivationCompanyInvoiceTypeDTO.PRE_ACTIVATION_PAYMENT,
                )
            )

        coVerifyOnce {
            preActivationPaymentService.cancelById(preActivationPayment.id)
        }
    }

}
