package br.com.alice.data.layer.models

import br.com.alice.authentication.UserType
import br.com.alice.common.Brand
import br.com.alice.common.UpdatedBy
import br.com.alice.common.core.LegalGuardianAssociationReference
import br.com.alice.common.core.Model
import br.com.alice.common.core.PersonId
import br.com.alice.common.core.User
import br.com.alice.common.core.extensions.clearWhitespaces
import br.com.alice.common.core.extensions.onlyDigits
import br.com.alice.common.core.extensions.replaceTabs
import br.com.alice.common.models.DependentInformation
import br.com.alice.common.models.Gender
import br.com.alice.common.models.Sex
import br.com.alice.common.models.State
import br.com.alice.common.serialization.JsonSerializable
import br.com.alice.common.storage.AliceFile
import com.google.gson.annotations.Expose
import kotlinx.serialization.Transient
import java.time.LocalDateTime
import java.util.UUID

data class PersonModel(
    val firstName: String,
    val lastName: String,
    @Expose
    val nationalId: String,
    val phoneNumber: String? = null,
    override val email: String,
    val addresses: List<AddressModel> = listOf(),
    val nickName: String? = null,
    val socialName: String? = null,
    val socialFirstName: String? = null,
    val socialLastName: String? = null,
    val dateOfBirth: LocalDateTime? = null,
    val sex: Sex? = null,
    val gender: Gender? = null,
    val genderIdentity: GenderIdentity? = null,
    val sexualOrientation: SexualOrientation? = null,
    val colorAndRace: ColorAndRace? = null,
    val acceptedTermsAt: LocalDateTime? = null,
    val mothersName: String? = null,
    val pisNumber: String? = null,
    val documentPictureUrl: String? = null,
    val profilePicture: AliceFile? = null,
    val searchTokens: String? = null,
    val cnsNumber: String? = null,
    val identityDocument: String? = null,
    val identityDocumentIssuingBody: String? = null,
    @Expose
    val isTest: Boolean = false,
    val nonBinaryIdentity: String? = null,
    val pronoun: Pronoun? = null,
    val customPronoun: String? = null,
    val identityDocumentVaultId: String? = null,
    val tags: List<String>? = null,
    val leadId: UUID? = null,
    val opportunityId: UUID? = null,
    @Transient
    val userType: UserType? = null,
    val changeHistory: List<PersonChangeHistoryModel>? = emptyList(),
    val piiInternalCode: String? = generatePiiInternalCode(),
    override val id: PersonId = PersonId(),
    override val version: Int = 0,
    val createdAt: LocalDateTime = LocalDateTime.now(),
    val updatedAt: LocalDateTime = LocalDateTime.now(),

    // Enriched fields. DO NOT use as model data. This should be refactored to a PersonSubject entity
    @Transient
    val members: List<MemberModel>? = null,
    @Transient
    val dependentPersons: List<PersonModel>? = null,
    val productInfo: ProductInfoModel? = null,
    override var updatedBy: UpdatedBy? = null,
) : Model, User, LegalGuardianAssociationReference, DependentInformation, UpdatedByReference {

    companion object {
        private const val PII_INTERNAL_CODE_PREFIX = "PNC"
        private val alphanumericChars: List<Char> = ('A'..'Z') + ('0'..'9')

        fun generatePiiInternalCode(): String {
            val randomSuffix = (1..5).map { kotlin.random.Random.nextInt(0, alphanumericChars.size) }
                .map(alphanumericChars::get).joinToString("")
            return "$PII_INTERNAL_CODE_PREFIX$randomSuffix"
        }
    }

    override fun getLegalGuardianPersonId() = this.id

    override fun sanitize() = copy(
        email = this.email.lowercase().replace(" *".toRegex(), ""),
        nationalId = this.nationalId.onlyDigits().trim(),
        phoneNumber = this.phoneNumber?.onlyDigits()?.clearWhitespaces(),
        firstName = this.firstName.trim(),
        lastName = this.lastName.trim(),
        nickName = this.nickName?.trim().takeIf { !it.isNullOrBlank() },
        addresses = this.addresses.map { it.sanitize() },
        tags = this.tags?.map { it.lowercase().replace(" ", "_") }
    )

    fun addTag(tag: String): PersonModel {
        val newTags = tags?.plus(tag) ?: listOf(tag)
        return copy(tags = newTags)
    }
}

fun PersonModel.withUserType(userType: UserType): PersonModel =
    copy(userType = userType)

data class AddressModel(
    val state: State,
    val city: String,
    val street: String,
    val number: String,
    val complement: String? = null,
    val neighbourhood: String? = null,
    val postalCode: String? = null,
    val lat: Double? = null,
    val lng: Double? = null
) : JsonSerializable {
    companion object {}

    fun sanitize() = copy(
        city = this.city.trim().replaceTabs(),
        street = this.street.trim().replaceTabs(),
        number = this.number.trim().replaceTabs(),
        complement = this.complement?.trim()?.replaceTabs(),
        neighbourhood = this.neighbourhood?.trim()?.replaceTabs(),
        postalCode = this.postalCode?.trim()?.replaceTabs()
    )

    override fun toString(): String {
        val formattedComplement = complement?.let { ", $it" } ?: ""
        val formattedNeighbourhood = neighbourhood?.let { " - $it" } ?: ""
        val formattedCity = " - $city"
        val formattedState = " - $state"
        val formattedPostalCode = postalCode?.let { " - $it" } ?: ""
        return "$street, $number$formattedComplement$formattedNeighbourhood$formattedCity$formattedState$formattedPostalCode"
    }

}

data class PersonChangeHistoryModel(
    val field: PersonChangeHistory.Field,
    val oldValue: String,
    val changedAt: LocalDateTime = LocalDateTime.now()
)

data class ProductInfoModel(
    val brand: Brand,
    val primaryAttention: PrimaryAttentionType,
    val tier: TierType,
    val coPayment: CoPaymentType,
    val healthcareModelType: HealthcareModelType,
    val refund: RefundType,
    val productType: ProductType? = null,
) : JsonSerializable
