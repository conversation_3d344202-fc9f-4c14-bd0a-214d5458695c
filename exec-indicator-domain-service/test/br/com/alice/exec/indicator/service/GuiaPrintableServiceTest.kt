package br.com.alice.exec.indicator.service

import br.com.alice.common.MvUtil
import br.com.alice.common.core.extensions.toBrazilianDateFormat
import br.com.alice.common.core.extensions.toBrazilianTimeFormat
import br.com.alice.common.core.extensions.toSaoPauloTimeZone
import br.com.alice.common.data.dsl.matchers.ResultAssert
import br.com.alice.common.helpers.mockLocalDateTime
import br.com.alice.common.helpers.returns
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.AnsAdministrationRoute
import br.com.alice.data.layer.models.AnsHospitalizationRegime
import br.com.alice.data.layer.models.AnsUnitOfMeasurement
import br.com.alice.data.layer.models.AttachmentStatus
import br.com.alice.data.layer.models.DrugsIdentification
import br.com.alice.data.layer.models.MvAuthorizedProcedure
import br.com.alice.data.layer.models.MvAuthorizedProcedureStatus
import br.com.alice.data.layer.models.OpmeProcedure
import br.com.alice.data.layer.models.Person
import br.com.alice.data.layer.models.ProviderUnit
import br.com.alice.data.layer.models.RequestedDrugs
import br.com.alice.data.layer.models.RequestedOPME
import br.com.alice.data.layer.models.TotvsGuia
import br.com.alice.exec.indicator.client.HealthcareResourceService
import br.com.alice.exec.indicator.client.HospitalizationInfoService
import br.com.alice.exec.indicator.client.MvAuthorizedProcedureService
import br.com.alice.exec.indicator.client.TotvsGuiaService
import br.com.alice.exec.indicator.models.GuiaChemotherapyPrintable
import br.com.alice.exec.indicator.models.GuiaCommonPrintableData
import br.com.alice.exec.indicator.models.GuiaHospitalizationExtensionPrintable
import br.com.alice.exec.indicator.models.GuiaHospitalizationPrintable
import br.com.alice.exec.indicator.models.GuiaOpmePrintable
import br.com.alice.exec.indicator.models.GuiaPrintable
import br.com.alice.exec.indicator.models.GuiaProceduresPrintable
import br.com.alice.exec.indicator.models.GuiaRadiotherapyPrintable
import br.com.alice.exec.indicator.models.GuiaRequestedOpmePrintable
import br.com.alice.exec.indicator.models.RequestedDrugsPrintable
import br.com.alice.exec.indicator.service.internal.AttachmentChemotherapyService
import br.com.alice.exec.indicator.service.internal.AttachmentOpmeService
import br.com.alice.exec.indicator.service.internal.AttachmentRadiotherapyService
import br.com.alice.person.client.PersonService
import br.com.alice.provider.client.ProviderUnitService
import io.mockk.clearAllMocks
import io.mockk.coEvery
import io.mockk.mockk
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.TestInstance
import java.time.LocalDate
import java.time.LocalDateTime
import kotlin.random.Random
import kotlin.test.AfterTest
import kotlin.test.Test

@TestInstance(TestInstance.Lifecycle.PER_CLASS)
class GuiaPrintableServiceTest {

    private val totvsGuiaService: TotvsGuiaService = mockk()
    private val mvAuthorizedService: MvAuthorizedProcedureService = mockk()
    private val providerUnitService: ProviderUnitService = mockk()
    private val personService: PersonService = mockk()
    private val healthcareResourceService: HealthcareResourceService = mockk()
    private val hospitalizationInfoService: HospitalizationInfoService = mockk()
    private val attachmentOpmeService: AttachmentOpmeService = mockk()
    private val attachmentChemotherapyService: AttachmentChemotherapyService = mockk()
    private val attachmentRadiotherapyService: AttachmentRadiotherapyService = mockk()

    private val guiaPrintableService = GuiaPrintableServiceImpl(
        totvsGuiaService,
        mvAuthorizedService,
        providerUnitService,
        personService,
        healthcareResourceService,
        hospitalizationInfoService,
        attachmentOpmeService,
        attachmentChemotherapyService,
        attachmentRadiotherapyService,
    )

    private val person = TestModelFactory.buildPerson()

    private val totvsGuia = generateSequence {
        TestModelFactory.buildTotvsGuia(personId = person.id).copy(type = MvUtil.TISS.EXAM)
    }.take(2).toList()

    private val providerUnit = TestModelFactory.buildProviderUnit(name = "P1")
    private val providerUnitTwo = TestModelFactory.buildProviderUnit(name = "P2")

    private val mvAuthorizedProceduresOne = generateSequence {
        TestModelFactory.buildMvAuthorizedProcedure(
            procedureId = Random.nextInt(111111, 999999).toString(),
            personId = person.id,
            status = MvAuthorizedProcedureStatus.EXECUTED,
            totvsGuiaId = totvsGuia.first().id,
            executedByProviderUnitId = providerUnit.id,
            guiaExecutionCode = "123456",
        )
    }.take(10).toList()
    private val mvAuthorizedProceduresOneCodes = mvAuthorizedProceduresOne.map { it.procedureId!! }

    private val mvAuthorizedProceduresTwo = generateSequence {
        TestModelFactory.buildMvAuthorizedProcedure(
            procedureId = Random.nextInt(111111, 999999).toString(),
            personId = person.id,
            status = MvAuthorizedProcedureStatus.EXECUTED,
            totvsGuiaId = totvsGuia.last().id,
            executedByProviderUnitId = providerUnitTwo.id,
        )
    }.take(8).toList()
    private val mvAuthorizedProceduresTwoCodes = mvAuthorizedProceduresTwo.map { it.procedureId!! }

    private val allMvAuthorizedProceduresCodes =
        (mvAuthorizedProceduresOneCodes + mvAuthorizedProceduresTwoCodes).distinct()

    private val healthcareResources = allMvAuthorizedProceduresCodes.map {
        TestModelFactory.buildHealthcareResource(code = it)
    }

    @AfterTest
    fun setup() = clearAllMocks()

    @Test
    fun `#getGuiaHospitalizationPrintable should get content to hospitalization type without problems`() = runBlocking {
        val totvsGuia = totvsGuia.first().copy(type = MvUtil.TISS.HOSPITALIZATION)
        val hospitalizationInfo = TestModelFactory.buildHospitalizationInfo(totvsGuiaId = totvsGuia.id)
        val healthcareResources = mvAuthorizedProceduresOneCodes.map {
            TestModelFactory.buildHealthcareResource(code = it)
        }

        coEvery { totvsGuiaService.get(totvsGuia.id) } returns totvsGuia
        coEvery { personService.get(person.id) } returns person
        coEvery { providerUnitService.get(providerUnit.id) } returns providerUnit
        coEvery { hospitalizationInfoService.getByTotvsGuiaId(totvsGuia.id) } returns hospitalizationInfo
        coEvery { mvAuthorizedService.findByTotvsGuiaIdAndStatus(
            totvsGuia.id,
            listOf(MvAuthorizedProcedureStatus.AUTHORIZED, MvAuthorizedProcedureStatus.EXECUTED)
        ) } returns mvAuthorizedProceduresOne
        coEvery {
            healthcareResourceService.findByCodes(mvAuthorizedProceduresOneCodes, false)
        } returns healthcareResources

        mockLocalDateTime { now ->
            val expectedResponse = listOf(
                GuiaHospitalizationPrintable(
                    guiaCommonData = toGuiaCommonPrintableData(totvsGuia, person, providerUnit),
                    suggestedDate = hospitalizationInfo.suggestedDate.toBrazilianDateFormat(),
                    attendanceCharacter = hospitalizationInfo.attendanceCharacter.code.toInt(),
                    type = hospitalizationInfo.type.code.toInt(),
                    attendanceRegime = AnsHospitalizationRegime.HOSPITAL.code.toInt(),
                    numberOfDays = hospitalizationInfo.numberOfDays,
                    opmeIndication = if(hospitalizationInfo.opmeIndication) "Sim" else "Nao",
                    chemotherapyIndication = if(hospitalizationInfo.chemotherapyIndication) "Sim" else "Nao",
                    clinicalIndication = hospitalizationInfo.clinicalIndication,
                    healthConditionCode = hospitalizationInfo.healthCondition.code,
                    accidentIndication = hospitalizationInfo.accidentIndication.code.toInt(),
                    procedures = mvAuthorizedProceduresOne.take(12).map {
                        val currentProcedure = healthcareResources.first { resource -> resource.code == it.procedureId }
                        GuiaProceduresPrintable(
                            code = it.procedureId!!,
                            table = currentProcedure.tableType ?: "",
                            description = currentProcedure.description,
                            quantity = it.extraGuiaInfo.quantity ?: 1,
                            authorizedQuantity = it.extraGuiaInfo.quantity ?: 1,
                            startTime = now.toSaoPauloTimeZone().toBrazilianTimeFormat(),
                            endTime = now.toSaoPauloTimeZone().toBrazilianTimeFormat(),
                        )
                    },
                    admissionProbableDate = hospitalizationInfo.suggestedDate.toBrazilianDateFormat(),
                    authorizedNumberOfDays = hospitalizationInfo.numberOfDays,
                    requestedDate = hospitalizationInfo.createdAt.toSaoPauloTimeZone().toBrazilianDateFormat(),
                    authorizedAt = hospitalizationInfo.createdAt.toSaoPauloTimeZone().toBrazilianDateFormat(),
                )
            )

            val result = guiaPrintableService.getGuiaHospitalizationPrintable(
                totvsGuia.id,
                providerUnit.id,
                person.id
            )

            ResultAssert.assertThat(result).isSuccessWithData(expectedResponse)
        }
    }

    @Test
    fun `#getGuiaHospitalizationPrintable should return empty list when mvAuthorizedProcedures is not authorized`() = runBlocking {
        val totvsGuia = totvsGuia.first().copy(type = MvUtil.TISS.HOSPITALIZATION)

        coEvery { mvAuthorizedService.findByTotvsGuiaIdAndStatus(
            totvsGuia.id,
            listOf(MvAuthorizedProcedureStatus.AUTHORIZED, MvAuthorizedProcedureStatus.EXECUTED)
        ) } returns emptyList<MvAuthorizedProcedure>()

        val expectedResponse = emptyList<GuiaHospitalizationPrintable>()

        val result = guiaPrintableService.getGuiaHospitalizationPrintable(
            totvsGuia.id,
            providerUnit.id,
            person.id
        )

        ResultAssert.assertThat(result).isSuccessWithData(expectedResponse)
    }

    @Test
    fun `#getGuiaHospitalizationExtensionPrintable should get content to hospitalizationExtension type without problems`() = runBlocking {
        val totvsGuia = totvsGuia.first().copy(
            type = MvUtil.TISS.EXTENSION,
        )
        val hospitalizationInfo = TestModelFactory.buildHospitalizationInfo(totvsGuiaId = totvsGuia.id)
        val healthcareResources = mvAuthorizedProceduresOneCodes.map {
            TestModelFactory.buildHealthcareResource(code = it)
        }

        coEvery { totvsGuiaService.get(totvsGuia.id) } returns totvsGuia
        coEvery { personService.get(person.id) } returns person
        coEvery { providerUnitService.get(providerUnit.id) } returns providerUnit
        coEvery { hospitalizationInfoService.getByTotvsGuiaId(totvsGuia.id) } returns hospitalizationInfo
        coEvery { mvAuthorizedService.findByTotvsGuiaIdAndStatus(
            totvsGuia.id,
            listOf(MvAuthorizedProcedureStatus.AUTHORIZED, MvAuthorizedProcedureStatus.EXECUTED)
        ) } returns mvAuthorizedProceduresOne
        coEvery {
            healthcareResourceService.findByCodes(mvAuthorizedProceduresOneCodes, false)
        } returns healthcareResources

        mockLocalDateTime { now ->
            val expectedResponse = listOf(
                GuiaHospitalizationExtensionPrintable(
                    guiaCommonData = toGuiaCommonPrintableData(totvsGuia, person, providerUnit),
                    additionalNumberOfDays = hospitalizationInfo.numberOfDays,
                    clinicalIndication = hospitalizationInfo.clinicalIndication,
                    procedures = mvAuthorizedProceduresOne.take(9).map {
                        val currentProcedure = healthcareResources.first { resource -> resource.code == it.procedureId }
                        GuiaProceduresPrintable(
                            code = it.procedureId!!,
                            startTime = now.toSaoPauloTimeZone().toBrazilianTimeFormat(),
                            endTime = now.toSaoPauloTimeZone().toBrazilianTimeFormat(),
                            table = currentProcedure.tableType ?: "",
                            description = currentProcedure.description,
                            quantity = it.extraGuiaInfo.quantity ?: 1,
                        )
                    },
                    authorizedNumberOfDays = hospitalizationInfo.numberOfDays,
                    requestedDate = hospitalizationInfo.createdAt.toSaoPauloTimeZone().toBrazilianDateFormat(),
                    authorizedAt = hospitalizationInfo.createdAt.toSaoPauloTimeZone().toBrazilianDateFormat(),
                ),
                GuiaHospitalizationExtensionPrintable(
                    guiaCommonData = toGuiaCommonPrintableData(totvsGuia, person, providerUnit),
                    additionalNumberOfDays = hospitalizationInfo.numberOfDays,
                    clinicalIndication = hospitalizationInfo.clinicalIndication,
                    procedures = mvAuthorizedProceduresOne.toMutableList().asReversed().take(1).asReversed().map {
                        val currentProcedure = healthcareResources.first { resource -> resource.code == it.procedureId }
                        GuiaProceduresPrintable(
                            code = it.procedureId!!,
                            startTime = now.toSaoPauloTimeZone().toBrazilianTimeFormat(),
                            endTime = now.toSaoPauloTimeZone().toBrazilianTimeFormat(),
                            table = currentProcedure.tableType ?: "",
                            description = currentProcedure.description,
                            quantity = it.extraGuiaInfo.quantity ?: 1,
                        )
                    },
                    authorizedNumberOfDays = hospitalizationInfo.numberOfDays,
                    requestedDate = hospitalizationInfo.createdAt.toSaoPauloTimeZone().toBrazilianDateFormat(),
                    authorizedAt = hospitalizationInfo.createdAt.toSaoPauloTimeZone().toBrazilianDateFormat(),
                )
            )

            val result = guiaPrintableService.getGuiaHospitalizationExtensionPrintable(
                totvsGuia.id,
                providerUnit.id,
                person.id
            )

            ResultAssert.assertThat(result).isSuccessWithData(expectedResponse)
        }
    }

    @Test
    fun `#getGuiaHospitalizationExtensionPrintable should return empty list when mvAuthorizedProcedures is not authorized`() = runBlocking {
        val totvsGuia = totvsGuia.first().copy(
            type = MvUtil.TISS.EXTENSION,
        )

        coEvery { mvAuthorizedService.findByTotvsGuiaIdAndStatus(
            totvsGuia.id,
            listOf(MvAuthorizedProcedureStatus.AUTHORIZED, MvAuthorizedProcedureStatus.EXECUTED)
        ) } returns emptyList<MvAuthorizedProcedure>()

        val expectedResponse = emptyList<GuiaHospitalizationExtensionPrintable>()

        val result = guiaPrintableService.getGuiaHospitalizationExtensionPrintable(
            totvsGuia.id,
            providerUnit.id,
            person.id
        )

        ResultAssert.assertThat(result).isSuccessWithData(expectedResponse)
    }

    @Test
    fun `#getGuiaOpmePrintable should get content to OPME type without problems`() = runBlocking {
        val mainTotvsGuia = TestModelFactory.buildTotvsGuia()
        val mvAuthorizedProceduresOne = mvAuthorizedProceduresOne.map { it.copy(
            status = MvAuthorizedProcedureStatus.AUTHORIZED,
            totvsGuiaId = mainTotvsGuia.id
        ) }
        val totvsGuia = totvsGuia.first().copy(
            type = MvUtil.TISS.OPME,
            referenceTotvsGuiaId = mainTotvsGuia.id
        )
        val requestedOpmes = generateSequence {
            RequestedOPME(
                opmeIdentification = OpmeProcedure(
                    table = Random.nextInt(111111, 999999).toString(),
                    code = Random.nextInt(111111, 999999).toString(),
                    description = "Descricao",
                ),
                requestedQuantity = 1,
                requestedValue = Random.nextDouble(1.0, 1000.0),
                status = MvAuthorizedProcedureStatus.AUTHORIZED
            )
        }.take(8).toList()
        val attachmentOpme = TestModelFactory.buildAttachmentOpme(
            totvsGuiaId = totvsGuia.id,
            requestedOpmes = requestedOpmes,
            status = AttachmentStatus.AUTHORIZED
        )

        coEvery { totvsGuiaService.get(totvsGuia.id) } returns totvsGuia
        coEvery { personService.get(person.id) } returns person
        coEvery { providerUnitService.get(providerUnit.id) } returns providerUnit
        coEvery { totvsGuiaService.get(totvsGuia.id) } returns totvsGuia
        coEvery { mvAuthorizedService.findByTotvsGuiaIdAndStatus(
            mainTotvsGuia.id,
            listOf(MvAuthorizedProcedureStatus.AUTHORIZED, MvAuthorizedProcedureStatus.EXECUTED)
        ) } returns mvAuthorizedProceduresOne
        coEvery { attachmentOpmeService.getByTotvsGuiaId(totvsGuia.id) } returns attachmentOpme

        val expectedResponse = listOf(
            GuiaOpmePrintable(
                guiaCommonData = toGuiaCommonPrintableData(totvsGuia, person, providerUnit),
                technicalJustification = attachmentOpme.technicalJustification,
                materialSpecification = attachmentOpme.materialSpecification,
                observation = "",
                requestedDate = attachmentOpme.createdAt.toSaoPauloTimeZone().toBrazilianDateFormat(),
                requestedOpmes = requestedOpmes.take(6).map { requestedOpme ->
                    GuiaRequestedOpmePrintable(
                        table = requestedOpme.opmeIdentification.table,
                        code = requestedOpme.opmeIdentification.code,
                        description = requestedOpme.opmeIdentification.description,
                        requestedQuantity = requestedOpme.requestedQuantity,
                        requestedValue = requestedOpme.requestedValue,
                        authorizedRequestedQuantity = requestedOpme.requestedQuantity,
                        authorizedRequestedValue = requestedOpme.requestedValue,
                    )
                },
                authorizedAt = attachmentOpme.createdAt.toSaoPauloTimeZone().toBrazilianDateFormat(),
            ),
            GuiaOpmePrintable(
                guiaCommonData = toGuiaCommonPrintableData(totvsGuia, person, providerUnit),
                technicalJustification = attachmentOpme.technicalJustification,
                materialSpecification = attachmentOpme.materialSpecification,
                observation = "",
                requestedDate = attachmentOpme.createdAt.toSaoPauloTimeZone().toBrazilianDateFormat(),
                requestedOpmes = requestedOpmes.toMutableList().asReversed().take(2).asReversed().map { requestedOpme ->
                    GuiaRequestedOpmePrintable(
                        table = requestedOpme.opmeIdentification.table,
                        code = requestedOpme.opmeIdentification.code,
                        description = requestedOpme.opmeIdentification.description,
                        requestedQuantity = requestedOpme.requestedQuantity,
                        requestedValue = requestedOpme.requestedValue,
                        authorizedRequestedQuantity = requestedOpme.requestedQuantity,
                        authorizedRequestedValue = requestedOpme.requestedValue,
                    )
                },
                authorizedAt = attachmentOpme.createdAt.toSaoPauloTimeZone().toBrazilianDateFormat(),
            )
        )

        val result = guiaPrintableService.getGuiaOpmePrintable(
            totvsGuia.id,
            providerUnit.id,
            person.id
        )

        ResultAssert.assertThat(result).isSuccessWithData(expectedResponse)
    }

    @Test
    fun `#getGuiaOpmePrintable should return empty list when mvAuthorizedProcedures is not authorized`() = runBlocking {
        val mainTotvsGuia = TestModelFactory.buildTotvsGuia()
        val totvsGuia = totvsGuia.first().copy(
            type = MvUtil.TISS.OPME,
            referenceTotvsGuiaId = mainTotvsGuia.id
        )

        coEvery { totvsGuiaService.get(totvsGuia.id) } returns totvsGuia
        coEvery { mvAuthorizedService.findByTotvsGuiaIdAndStatus(
            mainTotvsGuia.id,
            listOf(MvAuthorizedProcedureStatus.AUTHORIZED, MvAuthorizedProcedureStatus.EXECUTED)
        ) } returns emptyList<MvAuthorizedProcedure>()

        val expectedResponse = emptyList<GuiaOpmePrintable>()

        val result = guiaPrintableService.getGuiaOpmePrintable(
            totvsGuia.id,
            providerUnit.id,
            person.id
        )

        ResultAssert.assertThat(result).isSuccessWithData(expectedResponse)
    }

    @Test
    fun `#getGuiaOpmePrintable should return empty list when AttachmentOpme status is not authorized or partially authorized`() = runBlocking {
        val mainTotvsGuia = TestModelFactory.buildTotvsGuia()
        val totvsGuia = totvsGuia.first().copy(
            type = MvUtil.TISS.OPME,
            referenceTotvsGuiaId = mainTotvsGuia.id
        )
        val mvAuthorizedProceduresOne = mvAuthorizedProceduresOne.map { it.copy(
            status = MvAuthorizedProcedureStatus.AUTHORIZED,
            totvsGuiaId = mainTotvsGuia.id
        ) }
        val attachmentOpme = TestModelFactory.buildAttachmentOpme(
            totvsGuiaId = totvsGuia.id,
            status = AttachmentStatus.PENDING
        )

        coEvery { totvsGuiaService.get(totvsGuia.id) } returns totvsGuia
        coEvery { mvAuthorizedService.findByTotvsGuiaIdAndStatus(
            mainTotvsGuia.id,
            listOf(MvAuthorizedProcedureStatus.AUTHORIZED, MvAuthorizedProcedureStatus.EXECUTED)
        ) } returns mvAuthorizedProceduresOne
        coEvery { attachmentOpmeService.getByTotvsGuiaId(totvsGuia.id) } returns attachmentOpme

        val expectedResponse = emptyList<GuiaOpmePrintable>()

        val result = guiaPrintableService.getGuiaOpmePrintable(
            totvsGuia.id,
            providerUnit.id,
            person.id
        )

        ResultAssert.assertThat(result).isSuccessWithData(expectedResponse)
    }

    @Test
    fun `#getGuiaOpmePrintable should return empty list when RequestedOpmes status is not authorized`() = runBlocking {
        val mainTotvsGuia = TestModelFactory.buildTotvsGuia()
        val totvsGuia = totvsGuia.first().copy(
            type = MvUtil.TISS.OPME,
            referenceTotvsGuiaId = mainTotvsGuia.id
        )
        val mvAuthorizedProceduresOne = mvAuthorizedProceduresOne.map { it.copy(
            status = MvAuthorizedProcedureStatus.AUTHORIZED,
            totvsGuiaId = mainTotvsGuia.id
        ) }
        val requestedOpmes = generateSequence {
            RequestedOPME(
                opmeIdentification = OpmeProcedure(
                    table = Random.nextInt(111111, 999999).toString(),
                    code = Random.nextInt(111111, 999999).toString(),
                    description = "Descricao",
                ),
                requestedQuantity = 1,
                requestedValue = Random.nextDouble(1.0, 1000.0),
                status = MvAuthorizedProcedureStatus.UNAUTHORIZED
            )
        }.take(8).toList()
        val attachmentOpme = TestModelFactory.buildAttachmentOpme(
            totvsGuiaId = totvsGuia.id,
            requestedOpmes = requestedOpmes,
            status = AttachmentStatus.PARTIALLY_AUTHORIZED
        )

        coEvery { totvsGuiaService.get(totvsGuia.id) } returns totvsGuia
        coEvery { mvAuthorizedService.findByTotvsGuiaIdAndStatus(
            mainTotvsGuia.id,
            listOf(MvAuthorizedProcedureStatus.AUTHORIZED, MvAuthorizedProcedureStatus.EXECUTED)
        ) } returns mvAuthorizedProceduresOne
        coEvery { attachmentOpmeService.getByTotvsGuiaId(totvsGuia.id) } returns attachmentOpme

        val expectedResponse = emptyList<GuiaOpmePrintable>()

        val result = guiaPrintableService.getGuiaOpmePrintable(
            totvsGuia.id,
            providerUnit.id,
            person.id
        )

        ResultAssert.assertThat(result).isSuccessWithData(expectedResponse)
    }

    @Test
    fun `#getGuiaChemotherapyPrintable should get content to Chemotherapy type without problems`() = runBlocking {
        val totvsGuiaSadt = totvsGuia.first().copy(
            type = MvUtil.TISS.CHEMOTHERAPY,
        )
        val totvsGuiaAttachment = totvsGuia[1].copy(
            type = MvUtil.TISS.CHEMOTHERAPY,
            referenceTotvsGuiaId = totvsGuiaSadt.id
        )
        val mvAuthorizedProceduresOne = mvAuthorizedProceduresOne.map { it.copy(
            status = MvAuthorizedProcedureStatus.AUTHORIZED,
            totvsGuiaId = totvsGuiaSadt.id
        ) }
        val requestedDrugs = generateSequence {
            RequestedDrugs(
                startDate = LocalDate.now(),
                totalCycleDosage = 1.0,
                unitOfMeasurement = AnsUnitOfMeasurement.MG,
                administrationRoute = AnsAdministrationRoute.ORAL,
                frequency = 1,
                status = MvAuthorizedProcedureStatus.AUTHORIZED,
                drugsIdentification = DrugsIdentification(
                    table = Random.nextInt(111111, 999999).toString(),
                    code = Random.nextInt(111111, 999999).toString(),
                    description = "droga01",
                )
            )
        }.take(10).toList()
        val attachmentChemotherapy = TestModelFactory.buildAttachmentChemotherapy(
            totvsGuiaId = totvsGuiaAttachment.id,
            requestedDrugs = requestedDrugs,
            status = AttachmentStatus.AUTHORIZED,
        )

        coEvery { mvAuthorizedService.findByTotvsGuiaIdAndStatus(
            totvsGuiaSadt.id,
            listOf(MvAuthorizedProcedureStatus.AUTHORIZED, MvAuthorizedProcedureStatus.EXECUTED)
        ) } returns mvAuthorizedProceduresOne
        coEvery { totvsGuiaService.findByReferenceTotvsGuiaIdAndType(
            totvsGuiaSadt.id, MvUtil.TISS.CHEMOTHERAPY
        ) } returns totvsGuiaAttachment
        coEvery { attachmentChemotherapyService.getByTotvsGuiaId(totvsGuiaAttachment.id) } returns attachmentChemotherapy
        coEvery { totvsGuiaService.get(totvsGuiaSadt.id) } returns totvsGuiaSadt
        coEvery { totvsGuiaService.get(totvsGuiaAttachment.id) } returns totvsGuiaAttachment
        coEvery { personService.get(person.id) } returns person
        coEvery { providerUnitService.get(providerUnit.id) } returns providerUnit

        val expectedResponse = listOf(
            GuiaChemotherapyPrintable(
                guiaCommonData = toGuiaCommonPrintableData(totvsGuiaAttachment, person, providerUnit, totvsGuiaSadt),
                height = attachmentChemotherapy.height,
                weight = attachmentChemotherapy.weight,
                bodySurface = "1.87".toBigDecimal(),
                diagnosisDate = attachmentChemotherapy.chemotherapyOncologicalDiagnosis.diagnosisDate.toBrazilianDateFormat(),
                stage = attachmentChemotherapy.chemotherapyOncologicalDiagnosis.stage.code.toInt(),
                type = attachmentChemotherapy.chemotherapyOncologicalDiagnosis.type.code.toInt(),
                purpose = attachmentChemotherapy.chemotherapyOncologicalDiagnosis.purpose.code.toInt(),
                tumor = attachmentChemotherapy.chemotherapyOncologicalDiagnosis.tumor.code.toInt(),
                nodule = attachmentChemotherapy.chemotherapyOncologicalDiagnosis.nodule.code.toInt(),
                metastasis = attachmentChemotherapy.chemotherapyOncologicalDiagnosis.metastasis.code.toInt(),
                healthCondition = attachmentChemotherapy.chemotherapyOncologicalDiagnosis.healthCondition.code,
                ecoGt = attachmentChemotherapy.chemotherapyOncologicalDiagnosis.ecoGt.code.toInt(),
                therapeuticPlan = "",
                relevantInformations = "",
                requestedDrugs = requestedDrugs.take(8).map { requestedDrug ->
                    RequestedDrugsPrintable(
                        table = requestedDrug.drugsIdentification.table,
                        code = requestedDrug.drugsIdentification.code,
                        description = requestedDrug.drugsIdentification.description,
                        startDate = requestedDrug.startDate.toBrazilianDateFormat(),
                        totalCycleDosage = requestedDrug.totalCycleDosage.toString(),
                        unitOfMeasurement = requestedDrug.unitOfMeasurement.code,
                        administrationRoute = requestedDrug.administrationRoute.code.toInt(),
                        frequency = requestedDrug.frequency
                    )
                },
                observation = "",
                cyclesQuantity = attachmentChemotherapy.cyclesQuantity,
                currentCycle = attachmentChemotherapy.currentCycle,
                currentCycleDays = attachmentChemotherapy.currentCycleDays,
                cyclesInterval = attachmentChemotherapy.cyclesInterval,
                requestedDate = attachmentChemotherapy.createdAt.toBrazilianDateFormat(),
                authorizedAt = attachmentChemotherapy.createdAt.toBrazilianDateFormat(),
            ),
            GuiaChemotherapyPrintable(
                guiaCommonData = toGuiaCommonPrintableData(totvsGuiaAttachment, person, providerUnit, totvsGuiaSadt),
                height = attachmentChemotherapy.height,
                weight = attachmentChemotherapy.weight,
                bodySurface = "1.87".toBigDecimal(),
                diagnosisDate = attachmentChemotherapy.chemotherapyOncologicalDiagnosis.diagnosisDate.toBrazilianDateFormat(),
                stage = attachmentChemotherapy.chemotherapyOncologicalDiagnosis.stage.code.toInt(),
                type = attachmentChemotherapy.chemotherapyOncologicalDiagnosis.type.code.toInt(),
                purpose = attachmentChemotherapy.chemotherapyOncologicalDiagnosis.purpose.code.toInt(),
                tumor = attachmentChemotherapy.chemotherapyOncologicalDiagnosis.tumor.code.toInt(),
                nodule = attachmentChemotherapy.chemotherapyOncologicalDiagnosis.nodule.code.toInt(),
                metastasis = attachmentChemotherapy.chemotherapyOncologicalDiagnosis.metastasis.code.toInt(),
                healthCondition = attachmentChemotherapy.chemotherapyOncologicalDiagnosis.healthCondition.code,
                ecoGt = attachmentChemotherapy.chemotherapyOncologicalDiagnosis.ecoGt.code.toInt(),
                therapeuticPlan = "",
                relevantInformations = "",
                requestedDrugs = requestedDrugs.toMutableList().asReversed().take(2).asReversed().map { requestedDrug ->
                    RequestedDrugsPrintable(
                        table = requestedDrug.drugsIdentification.table,
                        code = requestedDrug.drugsIdentification.code,
                        description = requestedDrug.drugsIdentification.description,
                        startDate = requestedDrug.startDate.toBrazilianDateFormat(),
                        totalCycleDosage = requestedDrug.totalCycleDosage.toString(),
                        unitOfMeasurement = requestedDrug.unitOfMeasurement.code,
                        administrationRoute = requestedDrug.administrationRoute.code.toInt(),
                        frequency = requestedDrug.frequency
                    )
                },
                observation = "",
                cyclesQuantity = attachmentChemotherapy.cyclesQuantity,
                currentCycle = attachmentChemotherapy.currentCycle,
                currentCycleDays = attachmentChemotherapy.currentCycleDays,
                cyclesInterval = attachmentChemotherapy.cyclesInterval,
                requestedDate = attachmentChemotherapy.createdAt.toBrazilianDateFormat(),
                authorizedAt = attachmentChemotherapy.createdAt.toBrazilianDateFormat(),
            )
        )

        val result = guiaPrintableService.getGuiaChemotherapyPrintable(
            totvsGuiaSadt.id,
            providerUnit.id,
            person.id
        )

        ResultAssert.assertThat(result).isSuccessWithData(expectedResponse)
    }

    @Test
    fun `#getGuiaChemotherapyPrintable should return empty list when mvAuthorizedProcedures is not authorized`() = runBlocking {
        val totvsGuia = totvsGuia.first().copy(
            type = MvUtil.TISS.CHEMOTHERAPY,
        )

        coEvery { mvAuthorizedService.findByTotvsGuiaIdAndStatus(
            totvsGuia.id,
            listOf(MvAuthorizedProcedureStatus.AUTHORIZED, MvAuthorizedProcedureStatus.EXECUTED)
        ) } returns emptyList<MvAuthorizedProcedure>()

        val expectedResponse = emptyList<GuiaChemotherapyPrintable>()

        val result = guiaPrintableService.getGuiaChemotherapyPrintable(
            totvsGuia.id,
            providerUnit.id,
            person.id
        )

        ResultAssert.assertThat(result).isSuccessWithData(expectedResponse)
    }

    @Test
    fun `#getGuiaChemotherapyPrintable should return empty list when AttachmentChemotherapy status is not authorized or partially authorized`() = runBlocking {
        val totvsGuiaSadt = totvsGuia.first().copy(
            type = MvUtil.TISS.CHEMOTHERAPY,
        )
        val totvsGuiaAttachment = totvsGuia[1].copy(
            type = MvUtil.TISS.CHEMOTHERAPY,
            referenceTotvsGuiaId = totvsGuiaSadt.id
        )

        val mvAuthorizedProceduresOne = mvAuthorizedProceduresOne.map { it.copy(
            status = MvAuthorizedProcedureStatus.AUTHORIZED,
            totvsGuiaId = totvsGuiaSadt.id
        ) }
        val attachmentChemotherapy = TestModelFactory.buildAttachmentChemotherapy(
            totvsGuiaId = totvsGuiaAttachment.id,
            status = AttachmentStatus.PENDING,
        )

        coEvery { mvAuthorizedService.findByTotvsGuiaIdAndStatus(
            totvsGuiaSadt.id,
            listOf(MvAuthorizedProcedureStatus.AUTHORIZED, MvAuthorizedProcedureStatus.EXECUTED)
        ) } returns mvAuthorizedProceduresOne
        coEvery { totvsGuiaService.findByReferenceTotvsGuiaIdAndType(
            totvsGuiaSadt.id, MvUtil.TISS.CHEMOTHERAPY
        ) } returns totvsGuiaAttachment
        coEvery { attachmentChemotherapyService.getByTotvsGuiaId(totvsGuiaAttachment.id) } returns attachmentChemotherapy

        val expectedResponse = emptyList<GuiaChemotherapyPrintable>()

        val result = guiaPrintableService.getGuiaChemotherapyPrintable(
            totvsGuiaSadt.id,
            providerUnit.id,
            person.id
        )

        ResultAssert.assertThat(result).isSuccessWithData(expectedResponse)
    }

    @Test
    fun `#getGuiaChemotherapyPrintable should return empty list when RequestedDrugs status is not authorized`() = runBlocking {
        val totvsGuiaSadt = totvsGuia.first().copy(
            type = MvUtil.TISS.CHEMOTHERAPY,
        )
        val totvsGuiaAttachment = totvsGuia[1].copy(
            type = MvUtil.TISS.CHEMOTHERAPY,
            referenceTotvsGuiaId = totvsGuiaSadt.id
        )
        val mvAuthorizedProceduresOne = mvAuthorizedProceduresOne.map { it.copy(
            status = MvAuthorizedProcedureStatus.AUTHORIZED,
            totvsGuiaId = totvsGuiaSadt.id
        ) }
        val requestedDrugs = generateSequence {
            RequestedDrugs(
                startDate = LocalDate.now(),
                totalCycleDosage = 1.0,
                unitOfMeasurement = AnsUnitOfMeasurement.MG,
                administrationRoute = AnsAdministrationRoute.ORAL,
                frequency = 1,
                status = MvAuthorizedProcedureStatus.PENDING,
                drugsIdentification = DrugsIdentification(
                    table = Random.nextInt(111111, 999999).toString(),
                    code = Random.nextInt(111111, 999999).toString(),
                    description = "droga01",
                )
            )
        }.take(10).toList()
        val attachmentChemotherapy = TestModelFactory.buildAttachmentChemotherapy(
            totvsGuiaId = totvsGuiaAttachment.id,
            requestedDrugs = requestedDrugs,
            status = AttachmentStatus.AUTHORIZED,
        )

        coEvery { mvAuthorizedService.findByTotvsGuiaIdAndStatus(
            totvsGuiaSadt.id,
            listOf(MvAuthorizedProcedureStatus.AUTHORIZED, MvAuthorizedProcedureStatus.EXECUTED)
        ) } returns mvAuthorizedProceduresOne
        coEvery { totvsGuiaService.findByReferenceTotvsGuiaIdAndType(
            totvsGuiaSadt.id, MvUtil.TISS.CHEMOTHERAPY
        ) } returns totvsGuiaAttachment
        coEvery { attachmentChemotherapyService.getByTotvsGuiaId(totvsGuiaAttachment.id) } returns attachmentChemotherapy

        val expectedResponse = emptyList<GuiaChemotherapyPrintable>()

        val result = guiaPrintableService.getGuiaChemotherapyPrintable(
            totvsGuiaSadt.id,
            providerUnit.id,
            person.id
        )

        ResultAssert.assertThat(result).isSuccessWithData(expectedResponse)
    }

    @Test
    fun `#getGuiaCommonPrintable should get content without problems`() = runBlocking {
        val totvsGuia = totvsGuia.first()

        coEvery { totvsGuiaService.get(totvsGuia.id) } returns totvsGuia

        coEvery {
            mvAuthorizedService.findByTotvsGuiaIdAndStatus(
                totvsGuia.id,
                listOf(MvAuthorizedProcedureStatus.AUTHORIZED, MvAuthorizedProcedureStatus.EXECUTED)
            )
        } returns (mvAuthorizedProceduresOne + mvAuthorizedProceduresTwo)

        coEvery { providerUnitService.get(providerUnit.id) } returns providerUnit

        coEvery {
            providerUnitService.getByIds(
                listOf(providerUnit.id, providerUnitTwo.id)
            )
        } returns listOf(providerUnit, providerUnitTwo)

        coEvery { personService.get(person.id) } returns person

        coEvery {
            healthcareResourceService.findByCodes(allMvAuthorizedProceduresCodes, false)
        } returns healthcareResources

        mockLocalDateTime { now ->
            val expectedResponse = listOf(
                GuiaPrintable(
                    providerGuiaNumber = mvAuthorizedProceduresOne.first().guiaExecutionCode!!,
                    operatorGuiaNumber = totvsGuia.externalCode ?: totvsGuia.code,
                    mainGuiaNumber = totvsGuia.externalCode ?: totvsGuia.code,
                    ansNumber = 421928,
                    authorizationDate = totvsGuia.createdAt.toSaoPauloTimeZone().toBrazilianDateFormat(),
                    passcode = totvsGuia.passcode ?: totvsGuia.code,
                    passcodeExpirationDate = totvsGuia.createdAt.plusDays(60).toSaoPauloTimeZone()
                        .toBrazilianDateFormat(),
                    memberIdentityNumber = person.nationalId,
                    memberIdentityValidity = null,
                    name = person.fullRegisterName,
                    nationalHealthCard = person.cnsNumber ?: "",
                    newBorn = if (mvAuthorizedProceduresOne.first().extraGuiaInfo.newBorn == true) "Sim" else "Nao",
                    operatorCode = providerUnit.cnpj ?: "",
                    contractedName = providerUnit.name,
                    cnesCode = providerUnit.cnes ?: "",
                    requestingProfessionalName = mvAuthorizedProceduresOne.first().requestedByProfessional.fullName
                        ?: "",
                    council = mvAuthorizedProceduresOne.first().requestedByProfessional.council.name,
                    councilNumber = mvAuthorizedProceduresOne.first().requestedByProfessional.councilNumber,
                    councilUf = mvAuthorizedProceduresOne.first().requestedByProfessional.councilState.name,
                    cboCode = null,
                    careCharacter = 1,
                    requestDate = totvsGuia.createdAt.toSaoPauloTimeZone().toBrazilianDateFormat(),
                    accidentIndication = 9,
                    consultationType = null,
                    procedures = mvAuthorizedProceduresOne.take(5).map {
                        val currentProcedure = healthcareResources.first { resource -> resource.code == it.procedureId }
                        GuiaProceduresPrintable(
                            code = it.procedureId!!,
                            startTime = now.toSaoPauloTimeZone().toBrazilianTimeFormat(),
                            endTime = now.toSaoPauloTimeZone().toBrazilianTimeFormat(),
                            table = currentProcedure.tableType ?: "",
                            description = currentProcedure.description,
                            quantity = it.extraGuiaInfo.quantity ?: 1,
                        )
                    }
                ),
                GuiaPrintable(
                    providerGuiaNumber = mvAuthorizedProceduresOne.first().guiaExecutionCode!!,
                    operatorGuiaNumber = totvsGuia.externalCode ?: totvsGuia.code,
                    mainGuiaNumber = totvsGuia.externalCode ?: totvsGuia.code,
                    ansNumber = 421928,
                    authorizationDate = totvsGuia.createdAt.toSaoPauloTimeZone().toBrazilianDateFormat(),
                    passcode = totvsGuia.passcode ?: totvsGuia.code,
                    passcodeExpirationDate = totvsGuia.createdAt.plusDays(60).toSaoPauloTimeZone()
                        .toBrazilianDateFormat(),
                    memberIdentityNumber = person.nationalId,
                    memberIdentityValidity = null,
                    name = person.fullRegisterName,
                    nationalHealthCard = person.cnsNumber ?: "",
                    newBorn = if (mvAuthorizedProceduresOne.first().extraGuiaInfo.newBorn == true) "Sim" else "Nao",
                    operatorCode = providerUnit.cnpj ?: "",
                    contractedName = providerUnit.name,
                    cnesCode = providerUnit.cnes ?: "",
                    requestingProfessionalName = mvAuthorizedProceduresOne.first().requestedByProfessional.fullName
                        ?: "",
                    council = mvAuthorizedProceduresOne.first().requestedByProfessional.council.name,
                    councilNumber = mvAuthorizedProceduresOne.first().requestedByProfessional.councilNumber,
                    councilUf = mvAuthorizedProceduresOne.first().requestedByProfessional.councilState.name,
                    cboCode = null,
                    careCharacter = 1,
                    requestDate = totvsGuia.createdAt.toSaoPauloTimeZone().toBrazilianDateFormat(),
                    accidentIndication = 9,
                    consultationType = null,
                    procedures = mvAuthorizedProceduresOne.toMutableList().asReversed().take(5).asReversed().map {
                        val currentProcedure = healthcareResources.first { resource -> resource.code == it.procedureId }
                        GuiaProceduresPrintable(
                            code = it.procedureId!!,
                            startTime = now.toSaoPauloTimeZone().toBrazilianTimeFormat(),
                            endTime = now.toSaoPauloTimeZone().toBrazilianTimeFormat(),
                            table = currentProcedure.tableType ?: "",
                            description = currentProcedure.description,
                            quantity = it.extraGuiaInfo.quantity ?: 1,
                        )
                    }
                ),
                GuiaPrintable(
                    providerGuiaNumber = totvsGuia.externalCode ?: totvsGuia.code,
                    operatorGuiaNumber = totvsGuia.externalCode ?: totvsGuia.code,
                    mainGuiaNumber = totvsGuia.externalCode ?: totvsGuia.code,
                    ansNumber = 421928,
                    authorizationDate = totvsGuia.createdAt.toSaoPauloTimeZone().toBrazilianDateFormat(),
                    passcode = totvsGuia.passcode ?: totvsGuia.code,
                    passcodeExpirationDate = totvsGuia.createdAt.plusDays(60).toSaoPauloTimeZone()
                        .toBrazilianDateFormat(),
                    memberIdentityNumber = person.nationalId,
                    memberIdentityValidity = null,
                    name = person.fullRegisterName,
                    nationalHealthCard = person.cnsNumber ?: "",
                    newBorn = if (mvAuthorizedProceduresOne.first().extraGuiaInfo.newBorn == true) "Sim" else "Nao",
                    operatorCode = providerUnit.cnpj ?: "",
                    contractedName = providerUnitTwo.name,
                    cnesCode = providerUnitTwo.cnes ?: "",
                    requestingProfessionalName = mvAuthorizedProceduresTwo.first().requestedByProfessional.fullName
                        ?: "",
                    council = mvAuthorizedProceduresTwo.first().requestedByProfessional.council.name,
                    councilNumber = mvAuthorizedProceduresTwo.first().requestedByProfessional.councilNumber,
                    councilUf = mvAuthorizedProceduresTwo.first().requestedByProfessional.councilState.name,
                    cboCode = null,
                    careCharacter = 1,
                    requestDate = totvsGuia.createdAt.toSaoPauloTimeZone().toBrazilianDateFormat(),
                    accidentIndication = 9,
                    consultationType = null,
                    procedures = mvAuthorizedProceduresTwo.take(5).map {
                        val currentProcedure = healthcareResources.first { resource -> resource.code == it.procedureId }
                        GuiaProceduresPrintable(
                            code = it.procedureId!!,
                            startTime = now.toSaoPauloTimeZone().toBrazilianTimeFormat(),
                            endTime = now.toSaoPauloTimeZone().toBrazilianTimeFormat(),
                            table = currentProcedure.tableType ?: "",
                            description = currentProcedure.description,
                            quantity = it.extraGuiaInfo.quantity ?: 1,
                        )
                    }
                ),
                GuiaPrintable(
                    providerGuiaNumber = totvsGuia.externalCode ?: totvsGuia.code,
                    operatorGuiaNumber = totvsGuia.externalCode ?: totvsGuia.code,
                    mainGuiaNumber = totvsGuia.externalCode ?: totvsGuia.code,
                    ansNumber = 421928,
                    authorizationDate = totvsGuia.createdAt.toSaoPauloTimeZone().toBrazilianDateFormat(),
                    passcode = totvsGuia.passcode ?: totvsGuia.code,
                    passcodeExpirationDate = totvsGuia.createdAt.plusDays(60).toSaoPauloTimeZone()
                        .toBrazilianDateFormat(),
                    memberIdentityNumber = person.nationalId,
                    memberIdentityValidity = null,
                    name = person.fullRegisterName,
                    nationalHealthCard = person.cnsNumber ?: "",
                    newBorn = if (mvAuthorizedProceduresOne.first().extraGuiaInfo.newBorn == true) "Sim" else "Nao",
                    operatorCode = providerUnit.cnpj ?: "",
                    contractedName = providerUnitTwo.name,
                    cnesCode = providerUnitTwo.cnes ?: "",
                    requestingProfessionalName = mvAuthorizedProceduresTwo.first().requestedByProfessional.fullName
                        ?: "",
                    council = mvAuthorizedProceduresTwo.first().requestedByProfessional.council.name,
                    councilNumber = mvAuthorizedProceduresTwo.first().requestedByProfessional.councilNumber,
                    councilUf = mvAuthorizedProceduresTwo.first().requestedByProfessional.councilState.name,
                    cboCode = null,
                    careCharacter = 1,
                    requestDate = totvsGuia.createdAt.toSaoPauloTimeZone().toBrazilianDateFormat(),
                    accidentIndication = 9,
                    consultationType = null,
                    procedures = mvAuthorizedProceduresTwo.toMutableList().asReversed().take(3).asReversed().map {
                        val currentProcedure = healthcareResources.first { resource -> resource.code == it.procedureId }
                        GuiaProceduresPrintable(
                            code = it.procedureId!!,
                            startTime = now.toSaoPauloTimeZone().toBrazilianTimeFormat(),
                            endTime = now.toSaoPauloTimeZone().toBrazilianTimeFormat(),
                            table = currentProcedure.tableType ?: "",
                            description = currentProcedure.description,
                            quantity = it.extraGuiaInfo.quantity ?: 1,
                        )
                    }
                )
            )

            val result = guiaPrintableService.getGuiaCommonPrintable(totvsGuia.id, providerUnit.id, person.id)

            ResultAssert.assertThat(result).isSuccessWithData(expectedResponse)
        }
    }

    @Test
    fun `#getGuiaCommonPrintable should get content without problems for guides of procedureType different from EXAM`() = runBlocking {
        val totvsGuia = totvsGuia.first()

        coEvery { totvsGuiaService.get(totvsGuia.id) } returns totvsGuia.copy(type = MvUtil.TISS.PS)

        coEvery {
            mvAuthorizedService.findByTotvsGuiaIdAndStatus(
                totvsGuia.id,
                listOf(MvAuthorizedProcedureStatus.AUTHORIZED, MvAuthorizedProcedureStatus.EXECUTED)
            )
        } returns mvAuthorizedProceduresOne.map { it.copy(executedByProviderUnitId = null) }

        coEvery { providerUnitService.get(providerUnit.id) } returns providerUnit

        coEvery { personService.get(person.id) } returns person

        coEvery { providerUnitService.getByIds(emptyList()) } returns emptyList()

        coEvery {
            healthcareResourceService.findByCodes(mvAuthorizedProceduresOneCodes, false)
        } returns healthcareResources

        mockLocalDateTime { now ->
            val expectedResponse = listOf(
                GuiaPrintable(
                    providerGuiaNumber = mvAuthorizedProceduresOne.first().guiaExecutionCode!!,
                    operatorGuiaNumber = totvsGuia.externalCode ?: totvsGuia.code,
                    mainGuiaNumber = totvsGuia.externalCode ?: totvsGuia.code,
                    ansNumber = 421928,
                    authorizationDate = totvsGuia.createdAt.toSaoPauloTimeZone().toBrazilianDateFormat(),
                    passcode = totvsGuia.passcode ?: totvsGuia.code,
                    passcodeExpirationDate = totvsGuia.createdAt.plusDays(60).toSaoPauloTimeZone()
                        .toBrazilianDateFormat(),
                    memberIdentityNumber = person.nationalId,
                    memberIdentityValidity = null,
                    name = person.fullRegisterName,
                    nationalHealthCard = person.cnsNumber ?: "",
                    newBorn = if (mvAuthorizedProceduresOne.first().extraGuiaInfo.newBorn == true) "Sim" else "Nao",
                    operatorCode = providerUnit.cnpj ?: "",
                    contractedName = providerUnit.name,
                    cnesCode = providerUnit.cnes ?: "",
                    requestingProfessionalName = mvAuthorizedProceduresOne.first().requestedByProfessional.fullName
                        ?: "",
                    council = mvAuthorizedProceduresOne.first().requestedByProfessional.council.name,
                    councilNumber = mvAuthorizedProceduresOne.first().requestedByProfessional.councilNumber,
                    councilUf = mvAuthorizedProceduresOne.first().requestedByProfessional.councilState.name,
                    cboCode = null,
                    careCharacter = 1,
                    requestDate = totvsGuia.createdAt.toSaoPauloTimeZone().toBrazilianDateFormat(),
                    accidentIndication = 9,
                    consultationType = null,
                    procedures = mvAuthorizedProceduresOne.take(5).map {
                        val currentProcedure = healthcareResources.first { resource -> resource.code == it.procedureId }
                        GuiaProceduresPrintable(
                            code = it.procedureId!!,
                            startTime = now.toSaoPauloTimeZone().toBrazilianTimeFormat(),
                            endTime = now.toSaoPauloTimeZone().toBrazilianTimeFormat(),
                            table = currentProcedure.tableType ?: "",
                            description = currentProcedure.description,
                            quantity = it.extraGuiaInfo.quantity ?: 1,
                        )
                    }
                ),
                GuiaPrintable(
                    providerGuiaNumber = mvAuthorizedProceduresOne.first().guiaExecutionCode!!,
                    operatorGuiaNumber = totvsGuia.externalCode ?: totvsGuia.code,
                    mainGuiaNumber = totvsGuia.externalCode ?: totvsGuia.code,
                    ansNumber = 421928,
                    authorizationDate = totvsGuia.createdAt.toSaoPauloTimeZone().toBrazilianDateFormat(),
                    passcode = totvsGuia.passcode ?: totvsGuia.code,
                    passcodeExpirationDate = totvsGuia.createdAt.plusDays(60).toSaoPauloTimeZone()
                        .toBrazilianDateFormat(),
                    memberIdentityNumber = person.nationalId,
                    memberIdentityValidity = null,
                    name = person.fullRegisterName,
                    nationalHealthCard = person.cnsNumber ?: "",
                    newBorn = if (mvAuthorizedProceduresOne.first().extraGuiaInfo.newBorn == true) "Sim" else "Nao",
                    operatorCode = providerUnit.cnpj ?: "",
                    contractedName = providerUnit.name,
                    cnesCode = providerUnit.cnes ?: "",
                    requestingProfessionalName = mvAuthorizedProceduresOne.first().requestedByProfessional.fullName
                        ?: "",
                    council = mvAuthorizedProceduresOne.first().requestedByProfessional.council.name,
                    councilNumber = mvAuthorizedProceduresOne.first().requestedByProfessional.councilNumber,
                    councilUf = mvAuthorizedProceduresOne.first().requestedByProfessional.councilState.name,
                    cboCode = null,
                    careCharacter = 1,
                    requestDate = totvsGuia.createdAt.toSaoPauloTimeZone().toBrazilianDateFormat(),
                    accidentIndication = 9,
                    consultationType = null,
                    procedures = mvAuthorizedProceduresOne.toMutableList().asReversed().take(5).asReversed().map {
                        val currentProcedure = healthcareResources.first { resource -> resource.code == it.procedureId }
                        GuiaProceduresPrintable(
                            code = it.procedureId!!,
                            startTime = now.toSaoPauloTimeZone().toBrazilianTimeFormat(),
                            endTime = now.toSaoPauloTimeZone().toBrazilianTimeFormat(),
                            table = currentProcedure.tableType ?: "",
                            description = currentProcedure.description,
                            quantity = it.extraGuiaInfo.quantity ?: 1,
                        )
                    }
                )
            )

            val result = guiaPrintableService.getGuiaCommonPrintable(totvsGuia.id, providerUnit.id, person.id)

            ResultAssert.assertThat(result).isSuccessWithData(expectedResponse)
        }
    }

    @Test
    fun `#getGuiaRadiotherapyPrintable should get content to Radiotherapy type without problems`() = runBlocking {
        val totvsGuiaSadt = totvsGuia.first().copy(
            type = MvUtil.TISS.RADIOTHERAPY,
        )
        val totvsGuiaAttachment = totvsGuia[1].copy(
            type = MvUtil.TISS.RADIOTHERAPY,
            referenceTotvsGuiaId = totvsGuiaSadt.id
        )
        val attachmentRadiotherapy = TestModelFactory.buildAttachmentRadiotherapy(
            totvsGuiaId = totvsGuiaAttachment.id,
            status = AttachmentStatus.AUTHORIZED,
        ).copy(createdAt = LocalDateTime.of(2024, 10, 1, 10, 0))

        coEvery { totvsGuiaService.findByReferenceTotvsGuiaIdAndType(
            totvsGuiaSadt.id, MvUtil.TISS.RADIOTHERAPY
        ) } returns totvsGuiaAttachment
        coEvery { attachmentRadiotherapyService.getByTotvsGuiaId(totvsGuiaAttachment.id) } returns attachmentRadiotherapy
        coEvery { totvsGuiaService.get(totvsGuiaSadt.id) } returns totvsGuiaSadt
        coEvery { totvsGuiaService.get(totvsGuiaAttachment.id) } returns totvsGuiaAttachment
        coEvery { personService.get(person.id) } returns person
        coEvery { providerUnitService.get(providerUnit.id) } returns providerUnit

        val expectedResponse = listOf(
            GuiaRadiotherapyPrintable(
                guiaCommonData = toGuiaCommonPrintableData(totvsGuiaAttachment, person, providerUnit, totvsGuiaSadt),
                diagnosisDate = attachmentRadiotherapy.oncologicalDiagnosisRadio.diagnosisDate.toBrazilianDateFormat(),
                healthCondition = attachmentRadiotherapy.oncologicalDiagnosisRadio.healthCondition.code,
                imageDiagnosis = attachmentRadiotherapy.oncologicalDiagnosisRadio.imageDiagnosis.code.toInt(),
                stage = attachmentRadiotherapy.oncologicalDiagnosisRadio.stage.code.toInt(),
                ecoGt = attachmentRadiotherapy.oncologicalDiagnosisRadio.ecoGt.code.toInt(),
                purpose = attachmentRadiotherapy.oncologicalDiagnosisRadio.purpose.code.toInt(),
                histopathological = "",
                relevantInformations = "",
                fieldsQuantity = attachmentRadiotherapy.fieldsQuantity,
                fieldDose = attachmentRadiotherapy.fieldDose,
                totalDose = attachmentRadiotherapy.totalDose,
                daysQuantity = attachmentRadiotherapy.daysQuantity,
                expectedStartDate = attachmentRadiotherapy.expectedStartDate.toBrazilianDateFormat(),
                observation = "",
                requestedDate = attachmentRadiotherapy.createdAt.toBrazilianDateFormat(),
                authorizedAt = attachmentRadiotherapy.createdAt.toSaoPauloTimeZone().toBrazilianDateFormat(),
            )
        )

        val result = guiaPrintableService.getGuiaRadiotherapyPrintable(
            totvsGuiaSadt.id,
            providerUnit.id,
            person.id
        )

        ResultAssert.assertThat(result).isSuccessWithData(expectedResponse)
    }

    @Test
    fun `#getGuiaRadiotherapyPrintable should return empty list when AttachmentRadiotherapy status is not authorized or partially authorized`() = runBlocking {
        val totvsGuiaSadt = totvsGuia.first().copy(
            type = MvUtil.TISS.RADIOTHERAPY,
        )
        val totvsGuiaAttachment = totvsGuia[1].copy(
            type = MvUtil.TISS.RADIOTHERAPY,
            referenceTotvsGuiaId = totvsGuiaSadt.id
        )
        val attachmentRadiotherapy = TestModelFactory.buildAttachmentRadiotherapy(
            totvsGuiaId = totvsGuiaAttachment.id,
            status = AttachmentStatus.UNAUTHORIZED,
        ).copy(createdAt = LocalDateTime.of(2024, 10, 1, 10, 0))

        coEvery { totvsGuiaService.get(totvsGuiaSadt.id) } returns totvsGuiaSadt
        coEvery { totvsGuiaService.findByReferenceTotvsGuiaIdAndType(
            totvsGuiaSadt.id, MvUtil.TISS.RADIOTHERAPY
        ) } returns totvsGuiaAttachment
        coEvery { attachmentRadiotherapyService.getByTotvsGuiaId(totvsGuiaAttachment.id) } returns attachmentRadiotherapy

        val expectedResponse = emptyList<GuiaRadiotherapyPrintable>()

        val result = guiaPrintableService.getGuiaRadiotherapyPrintable(
            totvsGuiaSadt.id,
            providerUnit.id,
            person.id
        )

        ResultAssert.assertThat(result).isSuccessWithData(expectedResponse)
    }

    private fun toGuiaCommonPrintableData(
        totvsGuia: TotvsGuia,
        person: Person,
        providerUnit: ProviderUnit,
        totvsGuiaSadt: TotvsGuia? = null
    ) =
        GuiaCommonPrintableData(
            providerGuiaNumber = totvsGuia.externalCode ?: totvsGuia.code,
            operatorGuiaNumber = totvsGuia.externalCode ?: totvsGuia.code,
            mainGuiaNumber = if (totvsGuiaSadt != null)
                totvsGuiaSadt.externalCode ?: totvsGuiaSadt.code else totvsGuia.externalCode ?: totvsGuia.code,
            ansNumber = 421928,
            passcode = totvsGuia.passcode ?: totvsGuia.code,
            passcodeExpirationDate = totvsGuia.createdAt.plusDays(60).toSaoPauloTimeZone().toBrazilianDateFormat(),
            memberIdentityNumber = person.nationalId,
            name = person.fullRegisterName,
            socialName = person.socialName ?: person.fullRegisterName,
            nationalHealthCard = person.cnsNumber ?: "",
            newBorn = if (totvsGuia.newBorn) "Sim" else "Nao",
            age = person.age,
            operatorCode = providerUnit.cnpj ?: "",
            contractedName = providerUnit.name,
            cnesCode = providerUnit.cnes ?: "",
            providerCnpj = providerUnit.cnpj,
            requestingProfessionalName = totvsGuia.requestedByProfessional.fullName ?: "",
            council = totvsGuia.requestedByProfessional.council.name,
            councilNumber = totvsGuia.requestedByProfessional.councilNumber,
            councilUf = totvsGuia.requestedByProfessional.councilState.name,
            cboCode = null,
            phone = totvsGuia.requestedByProfessional.phone,
            email = totvsGuia.requestedByProfessional.email,
            sex = "",
        )

}
