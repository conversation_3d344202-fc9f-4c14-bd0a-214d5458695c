package br.com.alice.moneyin.services

import br.com.alice.common.PaymentMethod
import br.com.alice.common.PaymentMethod.BOLETO
import br.com.alice.common.PaymentMethod.PIX
import br.com.alice.common.RangeUUID
import br.com.alice.common.core.PersonId
import br.com.alice.common.core.exceptions.NotFoundException
import br.com.alice.common.core.extensions.atBeginningOfTheMonth
import br.com.alice.common.core.extensions.atEndOfTheDay
import br.com.alice.common.core.extensions.atEndOfTheMonth
import br.com.alice.common.core.extensions.atStartOfTheMonth
import br.com.alice.common.core.extensions.toLocalDateTime
import br.com.alice.common.core.extensions.toUUID
import br.com.alice.common.data.dsl.matchers.ResultAssert.Companion.assertThat
import br.com.alice.common.data.dsl.matchers.queryEq
import br.com.alice.common.extensions.money
import br.com.alice.common.helpers.coVerifyNone
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.common.helpers.returns
import br.com.alice.common.kafka.interfaces.ProducerResult
import br.com.alice.common.service.data.dsl.and
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.CancellationReason
import br.com.alice.data.layer.models.InvoiceItem
import br.com.alice.data.layer.models.InvoiceItemOperation
import br.com.alice.data.layer.models.InvoiceItemType
import br.com.alice.data.layer.models.InvoicePayment
import br.com.alice.data.layer.models.InvoicePaymentStatus
import br.com.alice.data.layer.models.InvoiceStatus
import br.com.alice.data.layer.models.MemberInvoice
import br.com.alice.data.layer.models.MemberInvoiceGroupStatus
import br.com.alice.data.layer.models.MemberInvoiceModel
import br.com.alice.data.layer.models.MemberInvoicePriceType
import br.com.alice.data.layer.models.MemberInvoiceType
import br.com.alice.data.layer.models.MemberStatus
import br.com.alice.data.layer.models.PaymentReason
import br.com.alice.data.layer.models.ProductType
import br.com.alice.data.layer.models.withInvoicePayments
import br.com.alice.data.layer.services.MemberInvoiceGroupModelDataService
import br.com.alice.data.layer.services.MemberInvoiceModelDataService
import br.com.alice.membership.client.PersonPreferencesService
import br.com.alice.moneyin.client.BillingAccountablePartyService
import br.com.alice.moneyin.client.InvoiceAlreadyPaidException
import br.com.alice.moneyin.client.InvoiceItemService
import br.com.alice.moneyin.client.InvoicePaymentService
import br.com.alice.moneyin.client.InvoicesService
import br.com.alice.moneyin.client.OverdueInvoiceException
import br.com.alice.moneyin.converters.InvoiceItemListConverter.toInvoiceBreakdown
import br.com.alice.moneyin.converters.toModel
import br.com.alice.moneyin.models.IuguAddress
import br.com.alice.moneyin.models.IuguItem
import br.com.alice.moneyin.models.IuguPayer
import br.com.alice.moneyin.models.IuguPaymentRequest
import br.com.alice.moneyin.notification.InvoiceNotifier
import br.com.alice.person.client.MemberService
import br.com.alice.person.client.PersonService
import com.github.kittinunf.result.Result
import io.mockk.clearAllMocks
import io.mockk.coEvery
import io.mockk.mockk
import io.mockk.slot
import kotlinx.coroutines.runBlocking
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.TestInstance
import java.math.BigDecimal
import java.time.LocalDate
import java.time.LocalDateTime
import java.time.YearMonth
import java.util.UUID
import kotlin.test.AfterTest
import kotlin.test.BeforeTest
import kotlin.test.Test

@TestInstance(TestInstance.Lifecycle.PER_CLASS)
class InvoiceServiceTest {
    private val data: MemberInvoiceModelDataService = mockk()
    private val notifier: InvoiceNotifier = mockk()
    private val invoicePaymentService: InvoicePaymentService = mockk()
    private val invoiceItemService: InvoiceItemService = mockk()
    private val memberService: MemberService = mockk()
    private val personService: PersonService = mockk()
    private val personPreferencesService: PersonPreferencesService = mockk()
    private val billingAccountablePartyService: BillingAccountablePartyService = mockk()
    private val paymentDetailService: PaymentDetailService = mockk()
    private val memberInvoiceGroupDataService: MemberInvoiceGroupModelDataService = mockk()

    private val service: InvoicesService = InvoicesServiceImpl(
        data,
        notifier,
        invoicePaymentService,
        invoiceItemService,
        memberService,
        personService,
        personPreferencesService,
        billingAccountablePartyService,
        paymentDetailService,
        memberInvoiceGroupDataService
    )

    private val member = TestModelFactory.buildMember()
    private val person = TestModelFactory.buildPerson(PersonId("53dec556-d930-45cd-ba7e-ead0d2f83601".toUUID()))

    private val iuguPaymentRequest = IuguPaymentRequest(
        payableWith = listOf("bank_slip", "pix"),
        email = "email",
        dueDate = LocalDateTime.now().plusDays(7).toString(),
        items = listOf(
            IuguItem(
                description = "description",
                quantity = 1,
                priceCents = 1000,
            )
        ),
        orderId = "order-id",
        payer = IuguPayer(
            cpfCnpj = "***********",
            name = "name",
            email = "email",
            address = IuguAddress(
                street = "street",
                city = "city",
                number = "01",
                zipCode = "0123456",
                state = "SP",
                district = "district",
                complement = "complement",
            )
        ),
    )

    private val invoice = MemberInvoice(
        memberId = member.id,
        totalAmount = iuguPaymentRequest.items.first().priceCents.toBigDecimal(),
        dueDate = iuguPaymentRequest.dueDate.toLocalDateTime(),
        personId = person.id,
        referenceDate = YearMonth.now().atStartOfTheMonth(),
        memberInvoiceGroupId = RangeUUID.generate()
    )
    private val invoiceModel = invoice.toModel()

    @BeforeTest
    fun setup() {
        coEvery {
            data.findOneOrNull(queryEq {
                where {
                    this.referenceDate.eq(invoice.referenceDate).and(
                        this.memberId.eq(invoice.memberId),
                    ).and(
                        this.status.eq(InvoiceStatus.OPEN),
                    )
                }
            })
        } returns null
    }

    @AfterTest
    fun tearDown() = clearAllMocks()

    @Test
    fun `#get should return only the invoice`() = runBlocking {
        coEvery {
            data.get(invoice.id)
        } returns Result.success(invoiceModel)

        val result = service.get(invoice.id)

        assertThat(result).isSuccessWithData(invoice)
    }

    @Test
    fun `#should update the memberInvoice`() = runBlocking {
        val updatedInvoice = invoice.copy(status = InvoiceStatus.PAID)
        val updatedInvoiceModel = updatedInvoice.toModel()

        coEvery {
            data.update(updatedInvoiceModel)
        } returns Result.success(updatedInvoiceModel)

        val result = service.update(updatedInvoice)

        assertThat(result).isSuccessWithData(updatedInvoice)
    }

    @Test
    fun `#get should return the invoice with payments`() = runBlocking {

        val invoicePayment = TestModelFactory.buildInvoicePayment(memberInvoiceIds = listOf(invoice.id))

        coEvery {
            data.get(invoice.id)
        } returns Result.success(invoiceModel)
        coEvery { invoicePaymentService.listInvoicePaymentsByInvoicesIds(listOf(invoice.id), true) } returns listOf(
            invoicePayment
        )

        coEvery {
            data.findOneOrNull(queryEq {
                where {
                    this.memberId.eq(member.id).and(
                        this.status.inList(listOf(InvoiceStatus.OPEN, InvoiceStatus.PAID)),
                    )
                }
            })
        } returns null

        val result = service.get(invoice.id, withPayments = true)

        assertThat(result).isSuccessWithData(invoice.withInvoicePayments(listOf(invoicePayment)))

        coVerifyOnce { data.get(invoice.id) }
        coVerifyOnce { invoicePaymentService.listInvoicePaymentsByInvoicesIds(listOf(invoice.id), true) }
    }

    @Test
    fun `#listInvoices should return a list of invoices`() = runBlocking {
        val memberId = RangeUUID.generate()

        coEvery {
            data.find(queryEq {
                where { this.memberId.eq(memberId) }
                    .orderBy { this.referenceDate }
                    .sortOrder { desc }
            })
        } returns Result.success(listOf(invoiceModel))

        val result = service.listInvoices(memberId)

        assertThat(result).isSuccessWithData(listOf(invoice))
    }

    @Test
    fun `#getFromLastFewMonthsByPersonId should return a list of invoices`() = runBlocking {
        val person = TestModelFactory.buildPerson()
        val now = LocalDateTime.now()
        val lastMonth = now.minusMonths(1)
        val twoMonthsAgo = now.minusMonths(2)

        val lowPriorityInvoice = invoice.copy(
            id = UUID.randomUUID(),
            status = InvoiceStatus.OPEN,
            referenceDate = now.toLocalDate(),
            createdAt = now.minusDays(1)
        )
        val highPriorityInvoiceWithNoGroup = invoice.copy(
            id = UUID.randomUUID(),
            status = InvoiceStatus.PAID,
            referenceDate = now.toLocalDate(),
            createdAt = now.minusDays(2),
            memberInvoiceGroupId = null
        )
        val highPriorityInvoice = invoice.copy(
            id = UUID.randomUUID(),
            status = InvoiceStatus.PAID,
            referenceDate = now.toLocalDate(),
            createdAt = now.minusDays(3)
        )
        val highPrioritySecondInvoice = invoice.copy(
            id = UUID.randomUUID(),
            status = InvoiceStatus.OPEN,
            referenceDate = lastMonth.toLocalDate(),
            createdAt = lastMonth.minusDays(1)
        )
        val lowPrioritySecondInvoice = invoice.copy(
            id = UUID.randomUUID(),
            status = InvoiceStatus.OPEN,
            referenceDate = lastMonth.toLocalDate(),
            createdAt = lastMonth.minusDays(2)
        )
        val highPriorityThirdInvoice = invoice.copy(
            id = UUID.randomUUID(),
            status = InvoiceStatus.OPEN,
            referenceDate = twoMonthsAgo.toLocalDate(),
            createdAt = twoMonthsAgo.minusDays(1)
        )
        val lowPriorityThirdInvoice = invoice.copy(
            id = UUID.randomUUID(),
            status = InvoiceStatus.OPEN,
            referenceDate = twoMonthsAgo.toLocalDate(),
            createdAt = twoMonthsAgo.minusDays(2)
        )

        coEvery {
            data.find(queryEq {
                where { this.personId.eq(person.id) }.orderBy { this.referenceDate }.sortOrder { desc }
            })
        } returns listOf(
            lowPriorityInvoice.toModel(),
            highPriorityInvoiceWithNoGroup.toModel(),
            highPriorityInvoice.toModel(),
            highPrioritySecondInvoice.toModel(),
            lowPrioritySecondInvoice.toModel(),
            highPriorityThirdInvoice.toModel(),
            lowPriorityThirdInvoice.toModel()
        )

        val result = service.getFromLastFewMonthsByPersonId(person.id, 2)

        assertThat(result).isSuccessWithData(listOf(highPriorityInvoice, highPrioritySecondInvoice))
    }

    @Test
    fun `#markAsPaid should return success when invoice is already paid`() = runBlocking {
        val paidInvoice = invoice.markAsPaid()
        coEvery { data.get(paidInvoice.id) } returns Result.success(paidInvoice.toModel())

        val result = service.markAsPaid(paidInvoice.id)
        assertThat(result).isSuccessWithData(paidInvoice)

        coVerifyNone { data.update(any()) }
    }

    @Test
    fun `#markAsPaid should return success when invoice was marked as paid`() = runBlocking {
        val expectedInvoice = invoice.markAsPaid()

        coEvery { data.get(invoice.id) } returns Result.success(invoiceModel)
        coEvery {
            data.update(any())
        } returns expectedInvoice.toModel()

        coEvery { notifier.publishPaidInvoice(expectedInvoice) } returns mockk()

        val result = service.markAsPaid(invoice.id)
        assertThat(result).isSuccessWithData(expectedInvoice)

        coVerifyOnce {
            data.update(match {
                it.id == invoice.id && it.alreadyPaid
            })
        }
        coVerifyOnce { notifier.publishPaidInvoice(expectedInvoice) }
    }

    @Test
    fun `#markAsPaid should return success when invoice was marked as paid with given paidAt`() = runBlocking {
        val paidAt = LocalDateTime.now().minusDays(3)
        val expectedInvoice = invoice.markAsPaid(paidAt)

        coEvery { data.get(invoice.id) } returns Result.success(invoiceModel)
        coEvery {
            data.update(any())
        } returns expectedInvoice.toModel()

        coEvery { notifier.publishPaidInvoice(expectedInvoice) } returns mockk()

        val result = service.markAsPaid(invoice.id, paidAt)
        assertThat(result).isSuccessWithData(expectedInvoice)

        coVerifyOnce {
            data.update(match {
                it.id == invoice.id && it.alreadyPaid && it.paidAt == paidAt
            })
        }
        coVerifyOnce { notifier.publishPaidInvoice(expectedInvoice) }
    }

    @Nested
    inner class MarkInvoicesAsPaidByLiquidation {
        @Test
        fun `#should return success when invoice is marked as paid by liquidation`() = runBlocking {
            val expectedInvoice = invoice.markAsPaidByLiquidation()

            coEvery { data.find(queryEq { where { this.id.inList(listOf(invoice.id)) } }) } returns listOf(
                invoiceModel
            )

            coEvery {
                data.update(match {
                    it.id == invoice.id && it.isPaidByLiquidation
                })
            } returns expectedInvoice.toModel()

            val result = service.markInvoicesAsPaidByLiquidation(listOf(invoice.id))

            assertThat(result).isSuccessWithData(listOf(expectedInvoice))

            coVerifyOnce {
                data.find(queryEq { where { this.id.inList(listOf(invoice.id)) } })
                data.update(match {
                    it.id == invoice.id && it.isPaidByLiquidation
                })
            }
        }

        @Test
        fun `#should return success when invoice is already paid by liquidation`() = runBlocking {
            val paidInvoice = invoice.markAsPaidByLiquidation()

            coEvery { data.find(queryEq { where { this.id.inList(listOf(invoice.id)) } }) } returns listOf(
                paidInvoice.toModel()
            )

            val result = service.markInvoicesAsPaidByLiquidation(listOf(invoice.id))

            assertThat(result).isSuccessWithData(listOf(paidInvoice))

            coVerifyOnce {
                data.find(queryEq { where { this.id.inList(listOf(invoice.id)) } })
            }
            coVerifyNone { data.update(any()) }
        }
    }

    @Test
    fun `#cancel should return success when an invoice was canceled`() = runBlocking {
        val canceledInvoice = invoice.cancel(CancellationReason.INVALID)

        coEvery { data.get(invoice.id) } returns Result.success(invoiceModel)
        coEvery {
            data.update(match {
                it.id == invoice.id && it.wasCanceled
            })
        } returns Result.success(canceledInvoice.toModel())

        val result = service.cancel(invoice.id, CancellationReason.INVALID)
        assertThat(result).isSuccessWithData(canceledInvoice)
    }

    @Test
    fun `#cancel should return success when invoice is already canceled`() = runBlocking {
        val canceledInvoice = invoice.cancel(CancellationReason.INVALID)

        coEvery { data.get(invoice.id) } returns Result.success(canceledInvoice.toModel())

        val result = service.cancel(invoice.id, CancellationReason.INVALID)
        assertThat(result).isSuccessWithData(canceledInvoice)
    }

    @Test
    fun `#cancel should return failure when invoice was already paid`() = runBlocking {
        val paidInvoice = invoice.markAsPaid()

        coEvery { data.get(invoice.id) } returns Result.success(paidInvoice.toModel())

        val result = service.cancel(invoice.id, CancellationReason.INVALID)
        assertThat(result).isFailureOfType(InvoiceAlreadyPaidException::class)
    }

    @Test
    fun `#cancel should cancel when invoice was already paid but forceCancellation is true`() = runBlocking {
        val paidInvoice = invoice.markAsPaid()
        val canceledInvoice = paidInvoice.cancel(CancellationReason.INVALID)

        coEvery { data.get(invoice.id) } returns Result.success(paidInvoice.toModel())
        coEvery {
            data.update(match {
                it.id == invoice.id && it.wasCanceled
            })
        } returns Result.success(canceledInvoice.toModel())

        val result = service.cancel(invoice.id, CancellationReason.INVALID, forceCancellation = true)
        assertThat(result).isSuccessWithData(canceledInvoice)
    }

    @Test
    fun `#listAllInvoicesWithPayments should return success with all invoices, payments and details`() =
        runBlocking {
            val member = TestModelFactory.buildMember()
            val invoice1 = TestModelFactory.buildMemberInvoice(member = member)
            val invoice2 = TestModelFactory.buildMemberInvoice(member = member)
            val memberInvoices = listOf(
                invoice1,
                invoice2,
            )
            val invoicePaymentId1 = RangeUUID.generate()
            val invoicePaymentId2 = RangeUUID.generate()
            val invoicePaymentId3 = RangeUUID.generate()
            val invoicePayment1 = TestModelFactory.buildInvoicePayment(
                memberInvoiceIds = listOf(invoice1.id),
                id = invoicePaymentId1,
                paymentDetail = TestModelFactory.buildBoletoPaymentDetail(paymentId = invoicePaymentId1),
            )
            val invoicePayment2 = TestModelFactory.buildInvoicePayment(
                memberInvoiceIds = listOf(invoice2.id),
                id = invoicePaymentId2,
                paymentDetail = TestModelFactory.buildBoletoPaymentDetail(paymentId = invoicePaymentId2),
            )
            val invoicePayment3 = TestModelFactory.buildInvoicePayment(
                memberInvoiceIds = listOf(invoice2.id),
                id = invoicePaymentId3,
                paymentDetail = TestModelFactory.buildBoletoPaymentDetail(paymentId = invoicePaymentId3),
            )

            val invoicePayments1 = listOf(invoicePayment1)
            val invoicePayments2 = listOf(invoicePayment2, invoicePayment3)

            val expected = listOf(
                invoice1.withInvoicePayments(invoicePayments1),
                invoice2.withInvoicePayments(invoicePayments2),
            )

            coEvery {
                data.find(queryEq {
                    where { this.memberId.eq(member.id) }.orderBy { this.referenceDate }.sortOrder { desc }
                })
            } returns memberInvoices.map { it.toModel() }

            coEvery {
                invoicePaymentService.listInvoicePaymentsByInvoicesIds(
                    listOf(invoice1.id, invoice2.id),
                    true
                )
            } returns listOf(invoicePayment1, invoicePayment2, invoicePayment3)

            val result = service.listInvoices(member.id, true)

            assertThat(result).isSuccessWithData(expected)
        }

    @Test
    fun `#createInvoice should not create the invoice if it is overdue`() = runBlocking {
        val invoice = TestModelFactory.buildMemberInvoice(dueDate = LocalDateTime.now().minusDays(10L))

        coEvery { data.findOneOrNull(any()) } returns null

        val result = service.createInvoice(invoice)
        assertThat(result).isFailureOfType(OverdueInvoiceException::class)

        coVerifyNone { data.add(any()) }
        coVerifyNone { notifier.publishNewInvoice(any()) }
    }

    @Test
    fun `#createInvoice should create the invoice even when it is overdue when the validation is skipping`() =
        runBlocking {
            val invoice = TestModelFactory.buildMemberInvoice(dueDate = LocalDateTime.now().minusDays(10L))
            val invoiceModel = invoice.toModel()

            coEvery {
                data.add(invoiceModel)
            } returns invoiceModel

            coEvery { notifier.publishNewInvoice(invoice) } returns mockk()

            val result = service.createInvoice(invoice, skipValidation = true)
            assertThat(result).isSuccessWithData(invoice)

            coVerifyOnce { data.add(invoiceModel) }
            coVerifyOnce { notifier.publishNewInvoice(invoice) }
            coVerifyNone { data.findOneOrNull(any()) }
        }

    @Test
    fun `#createInvoices should create some invoices`() =
        runBlocking {
            val memberInvoiceGroupId = RangeUUID.generate()
            val invoice = TestModelFactory.buildMemberInvoice(memberInvoiceGroupId = memberInvoiceGroupId)
            val invoiceModel = invoice.toModel()

            coEvery {
                data.find(queryEq {
                    where {
                        this.memberId.inList(listOf(invoice.memberId))
                            .and(this.status.inList(listOf(InvoiceStatus.OPEN, InvoiceStatus.PAID)))
                            .and(this.referenceDate.inList(listOf(invoice.referenceDate)))
                            .and(this.memberInvoiceGroupId.notInList(listOf(memberInvoiceGroupId)))
                    }
                })
            } returns emptyList()

            coEvery {
                data.addList(listOf(invoiceModel))
            } returns listOf(invoiceModel)

            coEvery { notifier.publishNewInvoice(invoice) } returns mockk()

            val result = service.createInvoices(listOf(invoice))
            assertThat(result).isSuccessWithData(listOf(invoice))

            coVerifyOnce { data.addList(listOf(invoiceModel)) }
            coVerifyOnce { notifier.publishNewInvoice(invoice) }
            coVerifyNone { data.findOneOrNull(any()) }
        }

    @Test
    fun `#validateInvoices should validate as success`() = runBlocking {
        val memberInvoiceGroupId = RangeUUID.generate()
        val invoice = TestModelFactory.buildMemberInvoice(
            memberInvoiceGroupId = memberInvoiceGroupId,
        )

        coEvery {
            data.find(queryEq {
                where {
                    this.memberId.inList(listOf(invoice.memberId))
                        .and(this.status.inList(listOf(InvoiceStatus.OPEN, InvoiceStatus.PAID)))
                        .and(this.referenceDate.inList(listOf(invoice.referenceDate)))
                        .and(this.memberInvoiceGroupId.notInList(listOf(memberInvoiceGroupId)))
                }
            })
        } returns emptyList<MemberInvoiceModel>()

        val result = service.validateInvoices(listOf(invoice))
        assertThat(result).isSuccessWithData(listOf(invoice))
    }

    @Test
    fun `#validateInvoices should return an error when due date is overdue`() = runBlocking {
        val memberInvoiceGroupId = RangeUUID.generate()
        val invoice = TestModelFactory.buildMemberInvoice(
            dueDate = LocalDateTime.now().minusDays(10L),
            memberInvoiceGroupId = memberInvoiceGroupId,
        )

        val result = service.validateInvoices(listOf(invoice))
        assertThat(result).isFailureOfType(OverdueInvoiceException::class)

        coVerifyNone { data.find(any()) }
    }

    @Test
    fun `#createInvoice should create member invoice`() = runBlocking {
        val invoice = TestModelFactory.buildMemberInvoice()
        val invoiceModel = invoice.toModel()

        coEvery {
            data.findOneOrNull(queryEq {
                where {
                    this.memberId.eq(invoice.memberId)
                        .and(this.status.inList(listOf(InvoiceStatus.OPEN, InvoiceStatus.PAID)))
                        .and(this.referenceDate.eq(invoice.referenceDate))
                }
            })
        } returns null

        coEvery {
            data.add(invoiceModel)
        } returns invoiceModel

        coEvery { notifier.publishNewInvoice(invoice) } returns mockk()

        val result = service.createInvoice(invoice)

        assertThat(result).isSuccessWithData(invoice)
    }

    @Nested
    inner class IssueInvoice {

        private val productPriceInvoiceItem =
            TestModelFactory.buildInvoiceItem(
                absoluteValue = BigDecimal("1000.00"),
                resolvedValue = BigDecimal("1000.00"),
                type = InvoiceItemType.PRODUCT_PRICE
            )

        private val copayInvoiceItem = TestModelFactory.buildInvoiceItem(
            type = InvoiceItemType.COPAY,
            operation = InvoiceItemOperation.CHARGE,
            absoluteValue = BigDecimal("250.00"),
            resolvedValue = BigDecimal("250.00"),
        )

        private val prorationInvoiceItem = TestModelFactory.buildInvoiceItem(
            type = InvoiceItemType.PRORATION,
            resolvedValue = BigDecimal("0.00"),
            absoluteValue = null,
            operation = InvoiceItemOperation.DISCOUNT,
            percentageValue = BigDecimal("0"),
        )

        private val invoiceItems = listOf(productPriceInvoiceItem, prorationInvoiceItem, copayInvoiceItem)

        private val invoiceBreakdown = invoiceItems.toInvoiceBreakdown().get()

        private val expectedInvoice =
            invoice.copy(invoiceItems = invoiceItems, totalAmount = BigDecimal("1250.00"))


        val invoicePayment = InvoicePayment(
            amount = BigDecimal("1250.00"),
            status = InvoicePaymentStatus.PENDING,
            method = PIX,
            memberInvoiceIds = listOf(expectedInvoice.id),
        )

        @Test
        fun `#issueInvoice should create member invoice with invoice items and requesting payment creation`() =
            runBlocking<Unit> {
                val person = TestModelFactory.buildPerson()
                val member = TestModelFactory.buildMember(personId = person.id, status = MemberStatus.PENDING)
                val referenceDate = LocalDate.now()
                val dueDate = LocalDateTime.now().plusDays(3L)
                val slot = slot<MemberInvoiceModel>()
                val billingAccountableParty = TestModelFactory.buildBillingAccountableParty()
                val expectedInvoice = expectedInvoice.copy(type = MemberInvoiceType.B2B_REGULAR_PAYMENT)
                val memberInvoiceGroup = TestModelFactory.buildMemberInvoiceGroup(
                    externalId = null,
                    memberInvoiceIds = listOf(expectedInvoice.id),
                    billingAccountablePartyId = billingAccountableParty.id,
                    referenceDate = expectedInvoice.referenceDate,
                    dueDate = expectedInvoice.dueDate.toLocalDate(),
                    status = MemberInvoiceGroupStatus.PROCESSING,
                    type = expectedInvoice.type!!,
                    totalAmount = expectedInvoice.totalAmount,
                )
                val memberInvoiceGroupModel = memberInvoiceGroup.toModel()

                coEvery {
                    data.findOneOrNull(queryEq {
                        where {
                            this.memberId.eq(member.id)
                                .and(this.status.inList(listOf(InvoiceStatus.OPEN, InvoiceStatus.PAID)))
                                .and(this.referenceDate.eq(referenceDate))
                        }
                    })
                } returns null

                coEvery { memberService.get(member.id) } returns member
                coEvery {
                    data.findOneOrNull(queryEq {
                        where {
                            this.memberId.eq(member.id).and(
                                this.status.inList(listOf(InvoiceStatus.OPEN, InvoiceStatus.PAID)),
                            )
                        }
                    })
                } returns null

                coEvery {
                    invoiceItemService.listActiveInvoiceItems(
                        person.id,
                        referenceDate,
                    )
                } returns listOf(copayInvoiceItem)

                coEvery {
                    invoiceItemService.createFirstProrationInvoiceItem(
                        person.id,
                        productPriceInvoiceItem.value,
                        referenceDate,
                        PIX,
                    )
                } returns prorationInvoiceItem

                coEvery {
                    invoiceItemService.generateProductInvoiceItem(
                        member.id,
                        referenceDate,
                    )
                } returns productPriceInvoiceItem

                coEvery { data.add(capture(slot)) } returns expectedInvoice.toModel()

                coEvery { notifier.publishNewInvoice(expectedInvoice) } returns mockk()
                coEvery { billingAccountablePartyService.getCurrentOrCreateForPerson(member.personId) } returns billingAccountableParty

                coEvery {
                    memberInvoiceGroupDataService.add(match {
                        it.externalId == memberInvoiceGroupModel.externalId &&
                                it.memberInvoiceIds == memberInvoiceGroupModel.memberInvoiceIds &&
                                it.billingAccountablePartyId == memberInvoiceGroupModel.billingAccountablePartyId &&
                                it.referenceDate == memberInvoiceGroupModel.referenceDate &&
                                it.dueDate == memberInvoiceGroupModel.dueDate &&
                                it.status == memberInvoiceGroupModel.status &&
                                it.type == memberInvoiceGroupModel.type &&
                                it.totalAmount == memberInvoiceGroupModel.totalAmount
                    })
                } returns memberInvoiceGroupModel

                coEvery {
                    data.update(match {
                        it.id == expectedInvoice.id &&
                                it.memberInvoiceGroupId == memberInvoiceGroup.id
                    })
                } returns expectedInvoice.toModel()

                coEvery {
                    notifier.publishCreateInvoicePaymentRequestEvent(
                        any(),
                        any(),
                        any(),
                        any(),
                        any()
                    )
                } returns ProducerResult(LocalDateTime.now(), "topic", 100)

                val invoiceToAdd = MemberInvoice(
                    dueDate = dueDate,
                    referenceDate = referenceDate,
                    memberId = member.id,
                    personId = person.id,
                    totalAmount = 1250.money,
                    invoiceItems = invoiceItems,
                    invoiceBreakdown = invoiceBreakdown,
                    type = MemberInvoiceType.B2B_REGULAR_PAYMENT,
                )

                val result =
                    service.issueInvoice(
                        member.id,
                        referenceDate,
                        dueDate,
                        PIX,
                        type = MemberInvoiceType.B2B_REGULAR_PAYMENT,
                    )
                val capturedInvoice = slot.captured

                assertThat(result).isSuccess()
                assertThat(capturedInvoice).usingRecursiveComparison().ignoringFields("id", "createdAt", "updatedAt")
                    .isEqualTo(invoiceToAdd)

                coVerifyOnce {
                    notifier.publishCreateInvoicePaymentRequestEvent(
                        any(),
                        PIX,
                        PaymentReason.B2B_REGULAR_PAYMENT,
                        expectedInvoice.dueDate,
                        billingAccountableParty,
                    )
                }
            }

        @Test
        fun `#issueInvoice should create member invoice with invoice items without requesting payment creation`() =
            runBlocking<Unit> {
                val person = TestModelFactory.buildPerson()
                val member = TestModelFactory.buildMember(personId = person.id, status = MemberStatus.PENDING)
                val referenceDate = LocalDate.now()
                val dueDate = LocalDateTime.now().plusDays(3L)
                val slot = slot<MemberInvoiceModel>()
                val billingAccountableParty = TestModelFactory.buildBillingAccountableParty()
                val expectedInvoice = expectedInvoice.copy(type = MemberInvoiceType.B2B_REGULAR_PAYMENT)
                val memberInvoiceGroup = TestModelFactory.buildMemberInvoiceGroup(
                    externalId = null,
                    memberInvoiceIds = listOf(expectedInvoice.id),
                    billingAccountablePartyId = billingAccountableParty.id,
                    referenceDate = expectedInvoice.referenceDate,
                    dueDate = expectedInvoice.dueDate.toLocalDate(),
                    status = MemberInvoiceGroupStatus.PROCESSING,
                    type = expectedInvoice.type!!,
                    totalAmount = expectedInvoice.totalAmount,
                )
                val memberInvoiceGroupModel = memberInvoiceGroup.toModel()

                coEvery {
                    data.findOneOrNull(queryEq {
                        where {
                            this.memberId.eq(member.id)
                                .and(this.status.inList(listOf(InvoiceStatus.OPEN, InvoiceStatus.PAID)))
                                .and(this.referenceDate.eq(referenceDate))
                        }
                    })
                } returns null

                coEvery { memberService.get(member.id) } returns member
                coEvery {
                    data.findOneOrNull(queryEq {
                        where {
                            this.memberId.eq(member.id).and(
                                this.status.inList(listOf(InvoiceStatus.OPEN, InvoiceStatus.PAID)),
                            )
                        }
                    })
                } returns null

                coEvery {
                    invoiceItemService.listActiveInvoiceItems(
                        person.id,
                        referenceDate,
                    )
                } returns listOf(copayInvoiceItem)

                coEvery {
                    invoiceItemService.createFirstProrationInvoiceItem(
                        person.id,
                        productPriceInvoiceItem.value,
                        referenceDate,
                        PIX,
                    )
                } returns prorationInvoiceItem

                coEvery {
                    invoiceItemService.generateProductInvoiceItem(
                        member.id,
                        referenceDate,
                    )
                } returns productPriceInvoiceItem

                coEvery { data.add(capture(slot)) } returns expectedInvoice.toModel()

                coEvery { notifier.publishNewInvoice(expectedInvoice) } returns mockk()
                coEvery { billingAccountablePartyService.getCurrentOrCreateForPerson(member.personId) } returns billingAccountableParty

                coEvery {
                    memberInvoiceGroupDataService.add(match {
                        it.externalId == memberInvoiceGroupModel.externalId &&
                                it.memberInvoiceIds == memberInvoiceGroupModel.memberInvoiceIds &&
                                it.billingAccountablePartyId == memberInvoiceGroupModel.billingAccountablePartyId &&
                                it.referenceDate == memberInvoiceGroupModel.referenceDate &&
                                it.dueDate == memberInvoiceGroupModel.dueDate &&
                                it.status == memberInvoiceGroupModel.status &&
                                it.type == memberInvoiceGroupModel.type &&
                                it.totalAmount == memberInvoiceGroupModel.totalAmount
                    })
                } returns memberInvoiceGroupModel

                coEvery {
                    data.update(match {
                        it.id == expectedInvoice.id &&
                                it.memberInvoiceGroupId == memberInvoiceGroup.id
                    })
                } returns expectedInvoice.toModel()

                coEvery {
                    notifier.publishCreateInvoicePaymentRequestEvent(
                        any(),
                        any(),
                        any(),
                        any(),
                        any()
                    )
                } returns ProducerResult(LocalDateTime.now(), "topic", 100)

                val invoiceToAdd = MemberInvoice(
                    dueDate = dueDate,
                    referenceDate = referenceDate,
                    memberId = member.id,
                    personId = person.id,
                    totalAmount = 1250.money,
                    invoiceItems = invoiceItems,
                    invoiceBreakdown = invoiceBreakdown,
                    type = MemberInvoiceType.REGULAR_PAYMENT,
                )

                val result =
                    service.issueInvoice(
                        member.id,
                        referenceDate,
                        dueDate,
                        PIX,
                        false,
                    )
                val capturedInvoice = slot.captured

                assertThat(result).isSuccess()
                assertThat(capturedInvoice).usingRecursiveComparison().ignoringFields("id", "createdAt", "updatedAt")
                    .isEqualTo(invoiceToAdd)
            }

        @Test
        fun `#issueInvoice should fail if member invoice group creation fails`() =
            runBlocking<Unit> {
                val person = TestModelFactory.buildPerson()
                val member = TestModelFactory.buildMember(personId = person.id, status = MemberStatus.PENDING)
                val referenceDate = LocalDate.now()
                val dueDate = LocalDateTime.now().plusDays(3L)
                val slot = slot<MemberInvoiceModel>()
                val billingAccountableParty = TestModelFactory.buildBillingAccountableParty()
                val expectedInvoice = expectedInvoice.copy(type = MemberInvoiceType.B2B_REGULAR_PAYMENT)
                val memberInvoiceGroup = TestModelFactory.buildMemberInvoiceGroup(
                    externalId = null,
                    memberInvoiceIds = listOf(expectedInvoice.id),
                    billingAccountablePartyId = billingAccountableParty.id,
                    referenceDate = expectedInvoice.referenceDate,
                    dueDate = expectedInvoice.dueDate.toLocalDate(),
                    status = MemberInvoiceGroupStatus.PROCESSING,
                    type = expectedInvoice.type!!,
                    totalAmount = expectedInvoice.totalAmount,
                )
                val memberInvoiceGroupModel = memberInvoiceGroup.toModel()

                coEvery {
                    data.findOneOrNull(queryEq {
                        where {
                            this.memberId.eq(member.id)
                                .and(this.status.inList(listOf(InvoiceStatus.OPEN, InvoiceStatus.PAID)))
                                .and(this.referenceDate.eq(referenceDate))
                        }
                    })
                } returns null

                coEvery { memberService.get(member.id) } returns member
                coEvery {
                    data.findOneOrNull(queryEq {
                        where {
                            this.memberId.eq(member.id).and(
                                this.status.inList(listOf(InvoiceStatus.OPEN, InvoiceStatus.PAID)),
                            )
                        }
                    })
                } returns null

                coEvery {
                    invoiceItemService.listActiveInvoiceItems(
                        person.id,
                        referenceDate,
                    )
                } returns listOf(copayInvoiceItem)

                coEvery {
                    invoiceItemService.createFirstProrationInvoiceItem(
                        person.id,
                        productPriceInvoiceItem.value,
                        referenceDate,
                        PIX,
                    )
                } returns prorationInvoiceItem

                coEvery {
                    invoiceItemService.generateProductInvoiceItem(
                        member.id,
                        referenceDate,
                    )
                } returns productPriceInvoiceItem

                coEvery { data.add(capture(slot)) } returns expectedInvoice.toModel()

                coEvery { notifier.publishNewInvoice(expectedInvoice) } returns mockk()
                coEvery { billingAccountablePartyService.getCurrentOrCreateForPerson(member.personId) } returns billingAccountableParty

                coEvery {
                    memberInvoiceGroupDataService.add(match {
                        it.externalId == memberInvoiceGroupModel.externalId &&
                                it.memberInvoiceIds == memberInvoiceGroupModel.memberInvoiceIds &&
                                it.billingAccountablePartyId == memberInvoiceGroupModel.billingAccountablePartyId &&
                                it.referenceDate == memberInvoiceGroupModel.referenceDate &&
                                it.dueDate == memberInvoiceGroupModel.dueDate &&
                                it.status == memberInvoiceGroupModel.status &&
                                it.type == memberInvoiceGroupModel.type &&
                                it.totalAmount == memberInvoiceGroupModel.totalAmount
                    })
                } returns NotFoundException("AAAAAAAAAAAAHHHHHHHHH")

                val result =
                    service.issueInvoice(
                        member.id,
                        referenceDate,
                        dueDate,
                        PIX,
                        type = MemberInvoiceType.B2B_REGULAR_PAYMENT,
                    )

                assertThat(result).isFailureOfType(NotFoundException::class)
                coVerifyOnce { memberInvoiceGroupDataService.add(any()) }
                coVerifyNone { data.update(any()) }
            }
    }

    @Nested
    inner class IssueFirstInvoice {

        private val productPriceInvoiceItem =
            TestModelFactory.buildInvoiceItem(
                absoluteValue = BigDecimal("1000.00"),
                resolvedValue = BigDecimal("1000.00"),
                type = InvoiceItemType.PRODUCT_PRICE
            )

        private val prorationInvoiceItem = TestModelFactory.buildInvoiceItem(
            type = InvoiceItemType.PRORATION,
            resolvedValue = BigDecimal("-100.00"),
            absoluteValue = null,
            operation = InvoiceItemOperation.DISCOUNT,
            percentageValue = BigDecimal("10"),
        )

        private val invoiceItems = listOf(productPriceInvoiceItem, prorationInvoiceItem)

        private val expectedInvoice =
            invoice.copy(invoiceItems = invoiceItems, totalAmount = BigDecimal("900.00"))

        val invoicePayment = InvoicePayment(
            amount = BigDecimal("900.00"),
            status = InvoicePaymentStatus.PENDING,
            method = BOLETO,
            memberInvoiceIds = listOf(expectedInvoice.id),
        )

        private val invoiceBreakdown = invoiceItems.toInvoiceBreakdown().get()

        @Test
        fun `#should create PRORATION invoice item when member doesn't have first invoice`() = runBlocking<Unit> {
            val person = TestModelFactory.buildPerson()
            val product = TestModelFactory.buildProduct()
            val member =
                TestModelFactory.buildMember(personId = person.id, status = MemberStatus.PENDING, product = product)
            val personPreferences = TestModelFactory.buildPersonPreferences(firstPaymentMethod = BOLETO)
            val slot = slot<MemberInvoiceModel>()

            val referenceDate =
                LocalDate
                    .now()
                    .atBeginningOfTheMonth()

            val billingAccountableParty = TestModelFactory.buildBillingAccountableParty()

            coEvery {
                data.findOneOrNull(queryEq {
                    where {
                        this.memberId.eq(member.id)
                            .and(this.status.inList(listOf(InvoiceStatus.OPEN, InvoiceStatus.PAID)))
                            .and(this.referenceDate.eq(referenceDate))
                    }
                })
            } returns null

            coEvery { personPreferencesService.findByPersonId(person.id) } returns personPreferences
            coEvery { personService.get(person.id) } returns person
            coEvery { memberService.get(member.id) } returns member
            coEvery {
                data.findOneOrNull(queryEq {
                    where {
                        this.memberId.eq(member.id).and(
                            this.status.inList(listOf(InvoiceStatus.OPEN, InvoiceStatus.PAID)),
                        )
                    }
                })
            } returns null
            coEvery {
                invoiceItemService.listActiveInvoiceItems(
                    person.id,
                    referenceDate
                )
            } returns emptyList<InvoiceItem>()

            coEvery {
                invoiceItemService.createFirstProrationInvoiceItem(
                    person.id,
                    productPriceInvoiceItem.value,
                    referenceDate,
                    BOLETO,
                )
            } returns prorationInvoiceItem

            coEvery {
                invoiceItemService.generateProductInvoiceItem(member.id, referenceDate)
            } returns productPriceInvoiceItem

            coEvery {
                data.add(capture(slot))
            } returns expectedInvoice.toModel()

            coEvery { notifier.publishNewInvoice(expectedInvoice) } returns mockk()

            coEvery { billingAccountablePartyService.getCurrentOrCreateForPerson(member.personId) } returns billingAccountableParty

            coEvery {
                notifier.publishCreateInvoicePaymentRequestEvent(
                    any(),
                    any(),
                    any(),
                    any(),
                    any()
                )
            } returns ProducerResult(LocalDateTime.now(), "topic", 100)

            val invoiceToAdd = MemberInvoice(
                dueDate = LocalDate.now().atEndOfTheMonth().atEndOfTheDay(),
                referenceDate = referenceDate,
                memberId = member.id,
                personId = person.id,
                totalAmount = 900.00.money,
                invoiceItems = listOf(productPriceInvoiceItem, prorationInvoiceItem),
                invoiceBreakdown = invoiceBreakdown,
                type = MemberInvoiceType.FIRST_PAYMENT,
            )

            val result = service.issueFirstInvoice(member)
            val capturedInvoice = slot.captured

            assertThat(result).isSuccess()
            assertThat(capturedInvoice).usingRecursiveComparison().ignoringFields("id", "createdAt", "updatedAt")
                .isEqualTo(invoiceToAdd)
        }

        @Test
        fun `#should create member's first invoice for PIX payment method`() = runBlocking<Unit> {
            val person = TestModelFactory.buildPerson()
            val product = TestModelFactory.buildProduct()
            val member =
                TestModelFactory.buildMember(personId = person.id, status = MemberStatus.PENDING, product = product)
            val personPreferences = TestModelFactory.buildPersonPreferences(firstPaymentMethod = PIX)
            val slot = slot<MemberInvoiceModel>()

            val referenceDate =
                LocalDate
                    .now()
                    .atBeginningOfTheMonth()

            val billingAccountableParty = TestModelFactory.buildBillingAccountableParty()

            coEvery {
                data.findOneOrNull(queryEq {
                    where {
                        this.memberId.eq(member.id)
                            .and(this.status.inList(listOf(InvoiceStatus.OPEN, InvoiceStatus.PAID)))
                            .and(this.referenceDate.eq(referenceDate))
                    }
                })
            } returns null

            coEvery { personPreferencesService.findByPersonId(person.id) } returns personPreferences
            coEvery { personService.get(person.id) } returns person
            coEvery { memberService.get(member.id) } returns member
            coEvery {
                data.findOneOrNull(queryEq {
                    where {
                        this.memberId.eq(member.id).and(
                            this.status.inList(listOf(InvoiceStatus.OPEN, InvoiceStatus.PAID)),
                        )
                    }
                })
            } returns null
            coEvery {
                invoiceItemService.listActiveInvoiceItems(
                    person.id,
                    referenceDate
                )
            } returns emptyList<InvoiceItem>()

            coEvery {
                invoiceItemService.createFirstProrationInvoiceItem(
                    person.id,
                    productPriceInvoiceItem.value,
                    referenceDate,
                    PIX,
                )
            } returns prorationInvoiceItem

            coEvery {
                invoiceItemService.generateProductInvoiceItem(member.id, referenceDate)
            } returns productPriceInvoiceItem

            coEvery {
                data.add(capture(slot))
            } returns expectedInvoice.toModel()

            coEvery { notifier.publishNewInvoice(expectedInvoice) } returns mockk()

            coEvery { billingAccountablePartyService.getCurrentOrCreateForPerson(member.personId) } returns billingAccountableParty

            coEvery {
                notifier.publishCreateInvoicePaymentRequestEvent(
                    any(),
                    any(),
                    any(),
                    any(),
                    any()
                )
            } returns ProducerResult(LocalDateTime.now(), "topic", 100)

            val invoiceToAdd = MemberInvoice(
                dueDate = LocalDate.now().atEndOfTheDay(),
                referenceDate = referenceDate,
                memberId = member.id,
                personId = person.id,
                totalAmount = 900.00.money,
                invoiceItems = listOf(productPriceInvoiceItem, prorationInvoiceItem),
                invoiceBreakdown = invoiceBreakdown,
                type = MemberInvoiceType.FIRST_PAYMENT,
            )

            val result = service.issueFirstInvoice(member)
            val capturedInvoice = slot.captured

            assertThat(result).isSuccess()
            assertThat(capturedInvoice).usingRecursiveComparison().ignoringFields("id", "createdAt", "updatedAt")
                .isEqualTo(invoiceToAdd)
        }

        @Test
        fun `#should not create invoice when member has manual_payments tag`() = runBlocking {
            val person = TestModelFactory.buildPerson(tags = listOf("manual_payments"))
            val product = TestModelFactory.buildProduct()
            val member =
                TestModelFactory.buildMember(personId = person.id, status = MemberStatus.PENDING, product = product)
            val personPreferences = TestModelFactory.buildPersonPreferences(firstPaymentMethod = BOLETO)

            coEvery { personService.get(person.id) } returns person
            coEvery { personPreferencesService.findByPersonId(person.id) } returns personPreferences

            val result = service.issueFirstInvoice(member)

            assertThat(result).isSuccess()

            coVerifyNone { memberService.get(member.id) }
        }

        @Test
        fun `#should not create invoice when member is INTERNAL`() = runBlocking {
            val person = TestModelFactory.buildPerson(tags = listOf("internal"))
            val product = TestModelFactory.buildProduct()
            val member =
                TestModelFactory.buildMember(personId = person.id, status = MemberStatus.PENDING, product = product)
            val personPreferences = TestModelFactory.buildPersonPreferences(firstPaymentMethod = BOLETO)

            coEvery { personService.get(person.id) } returns person
            coEvery { personPreferencesService.findByPersonId(person.id) } returns personPreferences

            val result = service.issueFirstInvoice(member)

            assertThat(result).isSuccess()

            coVerifyNone { memberService.get(member.id) }
        }

        @Test
        fun `#should not create invoice when Product is B2B`() = runBlocking {
            val person = TestModelFactory.buildPerson()
            val member =
                TestModelFactory.buildMember(
                    productType = ProductType.B2B,
                    personId = person.id,
                    status = MemberStatus.PENDING,
                )
            val personPreferences = TestModelFactory.buildPersonPreferences(firstPaymentMethod = BOLETO)

            coEvery { personService.get(person.id) } returns person
            coEvery { personPreferencesService.findByPersonId(person.id) } returns personPreferences

            val result = service.issueFirstInvoice(member)
            assertThat(result).isSuccess()

            coVerifyNone { memberService.get(member.id) }
        }

        @Test
        fun `#should not create invoice when Product is ADESAO`() = runBlocking {
            val person = TestModelFactory.buildPerson()
            val member =
                TestModelFactory.buildMember(
                    productType = ProductType.ADESAO,
                    personId = person.id,
                    status = MemberStatus.PENDING
                )
            val personPreferences = TestModelFactory.buildPersonPreferences(firstPaymentMethod = BOLETO)

            coEvery { personService.get(person.id) } returns person
            coEvery { personPreferencesService.findByPersonId(person.id) } returns personPreferences

            val result = service.issueFirstInvoice(member)
            assertThat(result).isSuccess()

            coVerifyNone { memberService.get(member.id) }
        }

        @Test
        fun `#should not create invoice when member has SIMPLE_CREDIT_CARD as preferred payment method`() =
            runBlocking {
                val person = TestModelFactory.buildPerson()
                val product = TestModelFactory.buildProduct()
                val member =
                    TestModelFactory.buildMember(personId = person.id, status = MemberStatus.PENDING, product = product)
                val paymentPreferences =
                    TestModelFactory.buildPersonPreferences(firstPaymentMethod = PaymentMethod.SIMPLE_CREDIT_CARD)

                coEvery { personService.get(person.id) } returns person
                coEvery { personPreferencesService.findByPersonId(person.id) } returns paymentPreferences

                val result = service.issueFirstInvoice(member)

                assertThat(result).isSuccess()

                coVerifyNone { memberService.get(member.id) }

            }
    }

    @Test
    fun `#listByPersonAndStatuses should return a list of invoices`() = runBlocking {
        val person = TestModelFactory.buildPerson()
        val statuses = listOf(InvoiceStatus.OPEN, InvoiceStatus.PAID, InvoiceStatus.FAILED)
        val statusesModels = listOf(InvoiceStatus.OPEN, InvoiceStatus.PAID, InvoiceStatus.FAILED)

        coEvery {
            data.find(queryEq {
                where {
                    this.personId.eq(person.id)
                        .and(this.status.inList(statusesModels))
                }.orderBy { this.referenceDate }.sortOrder { desc }
            })
        } returns Result.success(listOf(invoiceModel))

        val result = service.listByPersonAndStatuses(person.id, statuses)

        assertThat(result).isSuccessWithData(listOf(invoice))
    }

    @Test
    fun `#listByMemberInvoiceGroupIds should return a list of invoices`() = runBlocking {
        val memberInvoiceGroup = TestModelFactory.buildMemberInvoiceGroup()
        val invoice = invoice.copy(memberInvoiceGroupId = memberInvoiceGroup.id)

        coEvery {
            data.find(queryEq {
                where {
                    this.memberInvoiceGroupId.inList(listOf(memberInvoiceGroup.id))
                }
            })
        } returns Result.success(listOf(invoice.toModel()))

        val result = service.listByMemberInvoiceGroupIds(listOf(memberInvoiceGroup.id))

        assertThat(result).isSuccessWithData(listOf(invoice))
    }

    @Test
    fun `#listByMemberInvoiceGroupIds should return a list of invoices with payments`() = runBlocking {
        val memberInvoiceGroup = TestModelFactory.buildMemberInvoiceGroup()

        val invoicePaymentId = RangeUUID.generate()
        val paymentDetail = TestModelFactory.buildPixPaymentDetail(paymentId = invoicePaymentId)
        val invoicePayment =
            TestModelFactory.buildInvoicePayment(memberInvoiceIds = listOf(invoice.id)).withPaymentDetail(paymentDetail)
        val invoice = invoice
            .copy(
                memberInvoiceGroupId = memberInvoiceGroup.id,
                invoicePayments = listOf(invoicePayment.withPaymentDetail(paymentDetail))
            )

        coEvery {
            data.find(queryEq {
                where {
                    this.memberInvoiceGroupId.inList(listOf(memberInvoiceGroup.id))
                }
            })
        } returns listOf(invoice.toModel())

        coEvery { invoicePaymentService.listInvoicePaymentsByInvoicesIds(listOf(invoice.id), true) } returns listOf(
            invoicePayment
        )

        val result = service.listByMemberInvoiceGroupIds(listOf(memberInvoiceGroup.id), true)

        assertThat(result).isSuccessWithData(listOf(invoice))
    }

    @Test
    fun `#listByMemberInvoiceGroupIdsPaginated should return a list of invoices`() = runBlocking {
        val memberInvoiceGroup = TestModelFactory.buildMemberInvoiceGroup()
        val invoice = invoice.copy(memberInvoiceGroupId = memberInvoiceGroup.id)

        coEvery {
            data.find(queryEq {
                where {
                    this.memberInvoiceGroupId.inList(listOf(memberInvoiceGroup.id))
                }.offset { 2 }.limit { 15 }
            })
        } returns Result.success(listOf(invoice.toModel()))

        val result =
            service.listByMemberInvoiceGroupIdsPaginated(listOf(memberInvoiceGroup.id), false, offset = 2, limit = 15)

        assertThat(result).isSuccessWithData(listOf(invoice))
    }

    @Test
    fun `#listByMemberInvoiceGroupIdsPaginated should return a list of invoices with payments`() = runBlocking {
        val memberInvoiceGroup = TestModelFactory.buildMemberInvoiceGroup()

        val invoicePaymentId = RangeUUID.generate()
        val paymentDetail = TestModelFactory.buildPixPaymentDetail(paymentId = invoicePaymentId)
        val invoicePayment =
            TestModelFactory.buildInvoicePayment(memberInvoiceIds = listOf(invoice.id)).withPaymentDetail(paymentDetail)
        val invoice = invoice
            .copy(
                memberInvoiceGroupId = memberInvoiceGroup.id,
                invoicePayments = listOf(invoicePayment.withPaymentDetail(paymentDetail))
            )

        coEvery {
            data.find(queryEq {
                where {
                    this.memberInvoiceGroupId.inList(listOf(memberInvoiceGroup.id))
                }.offset { 2 }.limit { 15 }
            })
        } returns listOf(invoice.toModel())

        coEvery { invoicePaymentService.listInvoicePaymentsByInvoicesIds(listOf(invoice.id), true) } returns listOf(
            invoicePayment
        )

        val result =
            service.listByMemberInvoiceGroupIdsPaginated(listOf(memberInvoiceGroup.id), true, offset = 2, limit = 15)

        assertThat(result).isSuccessWithData(listOf(invoice))
    }

    @Test
    fun `#listByMemberInvoiceGroupId should return a list of invoices`() = runBlocking {
        val memberInvoiceGroup = TestModelFactory.buildMemberInvoiceGroup()
        val invoice = invoice.copy(memberInvoiceGroupId = memberInvoiceGroup.id)

        coEvery {
            data.find(queryEq {
                where {
                    this.memberInvoiceGroupId.eq(memberInvoiceGroup.id)
                }
            })
        } returns Result.success(listOf(invoice.toModel()))

        val result = service.listByMemberInvoiceGroupId(memberInvoiceGroup.id)

        assertThat(result).isSuccessWithData(listOf(invoice))
    }

    @Test
    fun `#listByMemberInvoiceGroupId should return a list of invoices with payments`() = runBlocking {
        val memberInvoiceGroup = TestModelFactory.buildMemberInvoiceGroup()

        val invoicePaymentId = RangeUUID.generate()
        val paymentDetail = TestModelFactory.buildPixPaymentDetail(paymentId = invoicePaymentId)
        val invoicePayment =
            TestModelFactory.buildInvoicePayment(memberInvoiceIds = listOf(invoice.id)).withPaymentDetail(paymentDetail)
        val invoice = invoice
            .copy(
                memberInvoiceGroupId = memberInvoiceGroup.id,
                invoicePayments = listOf(invoicePayment.withPaymentDetail(paymentDetail))
            )

        coEvery {
            data.find(queryEq {
                where {
                    this.memberInvoiceGroupId.eq(memberInvoiceGroup.id)
                }
            })
        } returns listOf(invoice.toModel())

        coEvery { invoicePaymentService.listInvoicePaymentsByInvoicesIds(listOf(invoice.id), true) } returns listOf(
            invoicePayment
        )

        val result = service.listByMemberInvoiceGroupId(memberInvoiceGroup.id, true)

        assertThat(result).isSuccessWithData(listOf(invoice))
    }

    @Test
    fun `#listByPreActivationPaymentIds should return a list of invoices`() = runBlocking {
        val preActivationPayment = TestModelFactory.buildPreActivationPayment()
        val invoice = invoice.copy(preActivationPaymentId = preActivationPayment.id)

        coEvery {
            data.find(queryEq {
                where {
                    this.preActivationPaymentId.inList(listOf(preActivationPayment.id))
                }
            })
        } returns Result.success(listOf(invoice.toModel()))

        val result = service.listByPreActivationPaymentIds(listOf(preActivationPayment.id))

        assertThat(result).isSuccessWithData(listOf(invoice))
    }

    @Test
    fun `#listByPreActivationPaymentIds should return a list of invoices with payments`() = runBlocking {
        val preActivationPayment = TestModelFactory.buildPreActivationPayment()

        val invoicePaymentId = RangeUUID.generate()
        val paymentDetail = TestModelFactory.buildPixPaymentDetail(paymentId = invoicePaymentId)
        val invoicePayment =
            TestModelFactory.buildInvoicePayment(memberInvoiceIds = listOf(invoice.id)).withPaymentDetail(paymentDetail)
        val invoice = invoice
            .copy(
                preActivationPaymentId = preActivationPayment.id,
                invoicePayments = listOf(invoicePayment.withPaymentDetail(paymentDetail))
            )

        coEvery {
            data.find(queryEq {
                where {
                    this.preActivationPaymentId.inList(listOf(preActivationPayment.id))
                }
            })
        } returns listOf(invoice.toModel())

        coEvery { invoicePaymentService.listInvoicePaymentsByInvoicesIds(listOf(invoice.id), true) } returns listOf(
            invoicePayment
        )

        val result = service.listByPreActivationPaymentIds(listOf(preActivationPayment.id), true)

        assertThat(result).isSuccessWithData(listOf(invoice))
    }

    @Test
    fun `#listByPreActivationPaymentIdsPaginated should return a list of invoices`() = runBlocking {
        val preActivationPayment = TestModelFactory.buildPreActivationPayment()
        val invoice = invoice.copy(preActivationPaymentId = preActivationPayment.id)

        coEvery {
            data.find(queryEq {
                where {
                    this.preActivationPaymentId.inList(listOf(preActivationPayment.id))
                }.offset { 15 }.limit { 23 }
            })
        } returns Result.success(listOf(invoice.toModel()))

        val result =
            service.listByPreActivationPaymentIdsPaginated(
                listOf(preActivationPayment.id),
                false,
                offset = 15,
                limit = 23
            )

        assertThat(result).isSuccessWithData(listOf(invoice))
    }

    @Test
    fun `#listByPreActivationPaymentIdsPaginated should return a list of invoices with payments`() = runBlocking {
        val preActivationPayment = TestModelFactory.buildPreActivationPayment()

        val invoicePaymentId = RangeUUID.generate()
        val paymentDetail = TestModelFactory.buildPixPaymentDetail(paymentId = invoicePaymentId)
        val invoicePayment =
            TestModelFactory.buildInvoicePayment(memberInvoiceIds = listOf(invoice.id)).withPaymentDetail(paymentDetail)
        val invoice = invoice
            .copy(
                preActivationPaymentId = preActivationPayment.id,
                invoicePayments = listOf(invoicePayment.withPaymentDetail(paymentDetail))
            )

        coEvery {
            data.find(queryEq {
                where {
                    this.preActivationPaymentId.inList(listOf(preActivationPayment.id))
                }.offset { 15 }.limit { 23 }
            })
        } returns listOf(invoice.toModel())

        coEvery { invoicePaymentService.listInvoicePaymentsByInvoicesIds(listOf(invoice.id), true) } returns listOf(
            invoicePayment
        )

        val result = service.listByPreActivationPaymentIdsPaginated(
            listOf(preActivationPayment.id),
            true,
            offset = 15,
            limit = 23
        )

        assertThat(result).isSuccessWithData(listOf(invoice))
    }

    @Test
    fun `#listByPreActivationPaymentId should return a list of invoices`() = runBlocking {
        val preActivationPayment = TestModelFactory.buildPreActivationPayment()
        val invoice = invoice.copy(preActivationPaymentId = preActivationPayment.id)

        coEvery {
            data.find(queryEq {
                where {
                    this.preActivationPaymentId.eq(preActivationPayment.id)
                }
            })
        } returns Result.success(listOf(invoice.toModel()))

        val result = service.listByPreActivationPaymentId(preActivationPayment.id)

        assertThat(result).isSuccessWithData(listOf(invoice))
    }

    @Test
    fun `#listByPreActivationPaymentId should return a list of invoices with payments`() = runBlocking {
        val preActivationPayment = TestModelFactory.buildPreActivationPayment()

        val invoicePaymentId = RangeUUID.generate()
        val paymentDetail = TestModelFactory.buildPixPaymentDetail(paymentId = invoicePaymentId)
        val invoicePayment =
            TestModelFactory.buildInvoicePayment(memberInvoiceIds = listOf(invoice.id)).withPaymentDetail(paymentDetail)
        val invoice = invoice
            .copy(
                preActivationPaymentId = preActivationPayment.id,
                invoicePayments = listOf(invoicePayment.withPaymentDetail(paymentDetail))
            )

        coEvery {
            data.find(queryEq {
                where {
                    this.preActivationPaymentId.eq(preActivationPayment.id)
                }
            })
        } returns listOf(invoice.toModel())

        coEvery { invoicePaymentService.listInvoicePaymentsByInvoicesIds(listOf(invoice.id), true) } returns listOf(
            invoicePayment
        )

        val result = service.listByPreActivationPaymentId(preActivationPayment.id, true)

        assertThat(result).isSuccessWithData(listOf(invoice))
    }

    @Test
    fun `#findInvoiceAndPaymentsByPersonId should return a list of invoices and payments by person id`() = runBlocking {
        val person = TestModelFactory.buildPerson()
        val invoiceWithoutPayment = TestModelFactory
            .buildMemberInvoice(personId = person.id)
        val invoicePayment = TestModelFactory.buildInvoicePayment()
        val paymentDetail = TestModelFactory.buildPixPaymentDetail(paymentId = invoicePayment.id)
        val invoice = invoiceWithoutPayment
            .copy(invoicePayments = listOf(invoicePayment.withPaymentDetail(paymentDetail)))
        val res = (invoice.invoicePayments!! to invoice)
        val billingAccountableParty = TestModelFactory.buildBillingAccountableParty()

        coEvery {
            data.findOneOrNull(queryEq {
                where {
                    this.personId.eq(person.id)
                }
            })
        } returns invoice.toModel()
        coEvery {
            invoicePaymentService.listInvoicePaymentsByBillingAccountablePartyId(billingAccountableParty.id)
        } returns invoice.invoicePayments!!
        coEvery {
            paymentDetailService.getPaymentDetail(invoice.invoicePayments!![0])
        } returns paymentDetail

        val result = service.findInvoiceAndPaymentsByPersonId(person.id, billingAccountableParty.id)

        assertThat(result).isSuccessWithData(res)
    }

    @Test
    fun `#findInvoiceAndPaymentsByPersonId should return only the invoices since there is no invoice payments`() =
        runBlocking {
            val person = TestModelFactory.buildPerson()
            val invoice = TestModelFactory
                .buildMemberInvoice(personId = person.id)
                .copy(invoicePayments = emptyList())
            val res = (emptyList<InvoicePayment>() to invoice)
            val billingAccountableParty = TestModelFactory.buildBillingAccountableParty()

            coEvery {
                data.findOneOrNull(queryEq {
                    where {
                        this.personId.eq(person.id)
                    }
                })
            } returns invoice.toModel()

            coEvery {
                invoicePaymentService.listInvoicePaymentsByBillingAccountablePartyId(billingAccountableParty.id)
            } returns emptyList<InvoicePayment>()

            val result = service.findInvoiceAndPaymentsByPersonId(person.id, billingAccountableParty.id)

            assertThat(result).isSuccessWithData(res)
        }

    @Test
    fun `#listOverdueInvoices should return invoices that will overdue tomorrow`() =
        runBlocking {
            val tomorrow = LocalDateTime.now().plusDays(1)
            val person = TestModelFactory.buildPerson()
            val invoice = TestModelFactory
                .buildMemberInvoice(personId = person.id, status = InvoiceStatus.OPEN, dueDate = tomorrow)
                .copy(invoicePayments = emptyList())
            val res = listOf(invoice)

            val twoDaysAhead = LocalDateTime.now().plusDays(2)
            val now = LocalDateTime.now()

            coEvery {
                data.find(queryEq {
                    where {
                        this.status.eq(InvoiceStatus.OPEN)
                            .and(this.type.eq(MemberInvoiceType.REGULAR_PAYMENT))
                            .and(this.dueDate.greater(now))
                            .and(this.dueDate.lessEq(twoDaysAhead))
                    }
                })
            } returns Result.success(listOf(invoice.toModel()))

            val result = service.listNearOverdueInvoices(twoDaysAhead, now, MemberInvoiceType.REGULAR_PAYMENT)
            assertThat(result).isSuccessWithData(res)
        }

    @Nested
    inner class Generate {

        private val productPriceInvoiceItem =
            TestModelFactory.buildInvoiceItem(
                absoluteValue = BigDecimal("1000.00"),
                resolvedValue = BigDecimal("1000.00"),
                type = InvoiceItemType.PRODUCT_PRICE
            )

        private val copayInvoiceItem = TestModelFactory.buildInvoiceItem(
            type = InvoiceItemType.COPAY,
            operation = InvoiceItemOperation.CHARGE,
            absoluteValue = BigDecimal("250.00"),
            resolvedValue = BigDecimal("250.00"),
        )

        private val prorationInvoiceItem = TestModelFactory.buildInvoiceItem(
            type = InvoiceItemType.PRORATION,
            resolvedValue = BigDecimal("-100.00"),
            absoluteValue = null,
            operation = InvoiceItemOperation.DISCOUNT,
            percentageValue = BigDecimal("10"),
        )

        private val person = TestModelFactory.buildPerson()

        private val member = TestModelFactory.buildMember(personId = person.id, status = MemberStatus.PENDING)

        private val dueDate = LocalDateTime.now().plusDays(3L)


        @Test
        fun `#generate should have a pro rata invoice when price type is pro rata`() = runBlocking<Unit> {
            val priceType = MemberInvoicePriceType.PRO_RATA
            val invoiceItemsProRata = listOf(productPriceInvoiceItem, prorationInvoiceItem, copayInvoiceItem)
            val invoiceBreakdownProRata = invoiceItemsProRata.toInvoiceBreakdown().get()
            val referenceDate = LocalDate.now()

            coEvery {
                data.findOneOrNull(queryEq {
                    where {
                        this.memberId.eq(member.id).and(
                            this.status.inList(listOf(InvoiceStatus.OPEN, InvoiceStatus.PAID)),
                        )
                    }
                })
            } returns null

            coEvery {
                invoiceItemService.listActiveInvoiceItems(
                    person.id,
                    referenceDate,
                )
            } returns listOf(copayInvoiceItem)

            coEvery {
                invoiceItemService.createFirstProrationInvoiceItem(
                    person.id,
                    productPriceInvoiceItem.value,
                    referenceDate,
                    PIX,
                )
            } returns prorationInvoiceItem

            coEvery {
                invoiceItemService.generateProductInvoiceItem(
                    member.id,
                    referenceDate,
                )
            } returns productPriceInvoiceItem

            val invoiceToAdd = MemberInvoice(
                dueDate = dueDate,
                referenceDate = referenceDate,
                memberId = member.id,
                personId = person.id,
                totalAmount = 1150.money,
                invoiceItems = invoiceItemsProRata,
                invoiceBreakdown = invoiceBreakdownProRata,
                type = MemberInvoiceType.B2B_REGULAR_PAYMENT,
            )

            val result =
                service.generate(
                    member,
                    referenceDate,
                    dueDate,
                    PIX,
                    MemberInvoiceType.B2B_REGULAR_PAYMENT,
                    priceType
                )

            assertThat(result).isSuccess()
            assertThat(result).isSuccessWithDataIgnoringGivenFields(invoiceToAdd, "id", "createdAt", "updatedAt")
        }

        @Test
        fun `#generate should not have a pro rata invoice when price type is full`() = runBlocking<Unit> {
            val priceType = MemberInvoicePriceType.FULL
            val invoiceItemsFull = listOf(productPriceInvoiceItem, copayInvoiceItem)
            val invoiceBreakdownFull = invoiceItemsFull.toInvoiceBreakdown().get()
            val referenceDate = LocalDate.now()

            coEvery {
                data.findOneOrNull(queryEq {
                    where {
                        this.memberId.eq(member.id)
                            .and(this.status.inList(listOf(InvoiceStatus.OPEN, InvoiceStatus.PAID)))
                            .and(this.referenceDate.eq(referenceDate))
                    }
                })
            } returns null

            coEvery { memberService.get(member.id) } returns member
            coEvery {
                data.findOneOrNull(queryEq {
                    where {
                        this.memberId.eq(member.id).and(
                            this.status.inList(listOf(InvoiceStatus.OPEN, InvoiceStatus.PAID)),
                        )
                    }
                })
            } returns null

            coEvery {
                invoiceItemService.listActiveInvoiceItems(
                    person.id,
                    referenceDate,
                )
            } returns listOf(copayInvoiceItem)

            coEvery {
                invoiceItemService.generateProductInvoiceItem(
                    member.id,
                    referenceDate,
                )
            } returns productPriceInvoiceItem

            val invoiceToAdd = MemberInvoice(
                dueDate = dueDate,
                referenceDate = referenceDate,
                memberId = member.id,
                personId = person.id,
                totalAmount = 1250.money,
                invoiceItems = invoiceItemsFull,
                invoiceBreakdown = invoiceBreakdownFull,
                type = MemberInvoiceType.B2B_REGULAR_PAYMENT,
            )

            val result =
                service.generate(
                    member,
                    referenceDate,
                    dueDate,
                    PIX,
                    MemberInvoiceType.B2B_REGULAR_PAYMENT,
                    priceType
                )

            assertThat(result).isSuccess()
            assertThat(result).isSuccessWithDataIgnoringGivenFields(invoiceToAdd, "id", "createdAt", "updatedAt")
        }
    }

    @Nested
    inner class GenerateForB2B {
        private val subcontract = TestModelFactory.buildCompanySubContract()

        private val person = TestModelFactory.buildPerson()

        private val productPriceInvoiceItem =
            TestModelFactory.buildInvoiceItem(
                absoluteValue = BigDecimal("1000.00"),
                resolvedValue = BigDecimal("1000.00"),
                type = InvoiceItemType.PRODUCT_PRICE,
                personId = person.id,
                companyId = subcontract.companyId,
                subcontractId = subcontract.id,
            )

        private val copayInvoiceItem = TestModelFactory.buildInvoiceItem(
            type = InvoiceItemType.COPAY,
            operation = InvoiceItemOperation.CHARGE,
            absoluteValue = BigDecimal("250.00"),
            resolvedValue = BigDecimal("250.00"),
            personId = person.id,
        )

        private val prorationInvoiceItem = TestModelFactory.buildInvoiceItem(
            type = InvoiceItemType.PRORATION,
            resolvedValue = BigDecimal("-100.00"),
            absoluteValue = null,
            operation = InvoiceItemOperation.DISCOUNT,
            percentageValue = BigDecimal("10"),
            personId = person.id,
        )

        private val member = TestModelFactory.buildMember(personId = person.id, status = MemberStatus.PENDING)

        private val dueDate = LocalDateTime.now().plusDays(3L)

        @Test
        fun `#generate should have a pro rata invoice when price type is pro rata`() = runBlocking<Unit> {
            val priceType = MemberInvoicePriceType.PRO_RATA
            val invoiceItemsProRata = listOf(productPriceInvoiceItem, prorationInvoiceItem)
            val invoiceBreakdownProRata = invoiceItemsProRata.toInvoiceBreakdown().get()
            val referenceDate = LocalDate.now()

            coEvery {
                data.findOneOrNull(queryEq {
                    where {
                        this.memberId.eq(member.id).and(
                            this.status.inList(listOf(InvoiceStatus.OPEN, InvoiceStatus.PAID)),
                        )
                    }
                })
            } returns null

            coEvery {
                invoiceItemService.createProrationInBatch(
                    items = listOf(member.personId to productPriceInvoiceItem.value),
                    referenceDate = referenceDate,
                    paymentMethod = PIX,
                )
            } returns listOf(prorationInvoiceItem)

            coEvery {
                invoiceItemService.listActiveInvoiceItemsByPersonIds(
                    personIds = listOf(member.personId),
                    referenceDate = referenceDate,
                )
            } returns emptyList()

            coEvery {
                invoiceItemService.generateProductInvoiceItemForB2B(
                    members = listOf(member),
                    companyId = subcontract.companyId,
                    subcontractId = subcontract.id,
                    referenceDate = referenceDate,
                )
            } returns listOf(productPriceInvoiceItem)

            val invoiceToAdd = MemberInvoice(
                dueDate = dueDate,
                referenceDate = referenceDate,
                memberId = member.id,
                personId = person.id,
                totalAmount = 900.money,
                invoiceItems = invoiceItemsProRata,
                invoiceBreakdown = invoiceBreakdownProRata,
                type = MemberInvoiceType.B2B_REGULAR_PAYMENT,
            )

            val result =
                service.generateForB2B(
                    subcontract,
                    listOf(member),
                    referenceDate,
                    dueDate,
                    PIX,
                    MemberInvoiceType.B2B_REGULAR_PAYMENT,
                    priceType
                )

            assertThat(result).isSuccess()
            assertThat(result).isSuccessWithListDataIgnoringGivenFields(
                listOf(invoiceToAdd),
                "id",
                "createdAt",
                "updatedAt"
            )
        }

        @Test
        fun `#generate should not have a pro rata invoice when price type is full`() = runBlocking<Unit> {
            val priceType = MemberInvoicePriceType.FULL
            val invoiceItemsFull = listOf(productPriceInvoiceItem, copayInvoiceItem)
            val invoiceBreakdownFull = invoiceItemsFull.toInvoiceBreakdown().get()
            val referenceDate = LocalDate.now()

            coEvery {
                data.findOneOrNull(queryEq {
                    where {
                        this.memberId.eq(member.id).and(
                            this.status.inList(listOf(InvoiceStatus.OPEN, InvoiceStatus.PAID)),
                        )
                    }
                })
            } returns null

            coEvery {
                invoiceItemService.listActiveInvoiceItemsByPersonIds(
                    personIds = listOf(member.personId),
                    referenceDate = referenceDate,
                )
            } returns listOf(copayInvoiceItem)

            coEvery {
                invoiceItemService.generateProductInvoiceItemForB2B(
                    members = listOf(member),
                    companyId = subcontract.companyId,
                    subcontractId = subcontract.id,
                    referenceDate = referenceDate,
                )
            } returns listOf(productPriceInvoiceItem)

            val invoiceToAdd = MemberInvoice(
                dueDate = dueDate,
                referenceDate = referenceDate,
                memberId = member.id,
                personId = person.id,
                totalAmount = 1250.money,
                invoiceItems = invoiceItemsFull,
                invoiceBreakdown = invoiceBreakdownFull,
                type = MemberInvoiceType.B2B_REGULAR_PAYMENT,
            )

            val result =
                service.generateForB2B(
                    subcontract,
                    listOf(member),
                    referenceDate,
                    dueDate,
                    PIX,
                    MemberInvoiceType.B2B_REGULAR_PAYMENT,
                    priceType
                )

            assertThat(result).isSuccess()
            assertThat(result).isSuccessWithListDataIgnoringGivenFields(
                listOf(invoiceToAdd),
                "id",
                "createdAt",
                "updatedAt"
            )

            coVerifyNone {
                invoiceItemService.createProrationInBatch(
                    items = any(),
                    referenceDate = any(),
                    paymentMethod = any(),
                )
            }
        }
    }
}
