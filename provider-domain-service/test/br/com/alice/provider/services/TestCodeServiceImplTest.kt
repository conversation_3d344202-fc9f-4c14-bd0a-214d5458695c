package br.com.alice.provider.services

import br.com.alice.common.RangeUUID
import br.com.alice.common.core.exceptions.NotFoundException
import br.com.alice.common.data.dsl.matchers.ResultAssert
import br.com.alice.common.data.dsl.matchers.queryEq
import br.com.alice.common.kafka.interfaces.KafkaProducerService
import br.com.alice.common.service.data.dsl.and
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.TestCode
import br.com.alice.data.layer.models.TestCodePackage
import br.com.alice.data.layer.services.TestCodeModelDataService
import br.com.alice.data.layer.services.TestCodePackageModelDataService
import br.com.alice.provider.client.TestCodeTransport
import br.com.alice.provider.client.TestCodeType
import br.com.alice.provider.converters.toModel
import br.com.alice.provider.services.event.UpsertTestCodeEvent
import com.github.kittinunf.result.failure
import com.github.kittinunf.result.success
import io.mockk.clearAllMocks
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.mockk
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.TestInstance
import kotlin.test.AfterTest
import kotlin.test.Test


@TestInstance(TestInstance.Lifecycle.PER_CLASS)
class TestCodeServiceImplTest {

    @AfterTest
    fun setup() = clearAllMocks()

    private val testCodeDataService: TestCodeModelDataService = mockk()
    private val testCodePackageDataService: TestCodePackageModelDataService = mockk()
    private val kafkaProducerService: KafkaProducerService = mockk()
    private val testCodeService =
        TestCodeServiceImpl(testCodeDataService, testCodePackageDataService, kafkaProducerService)

    private val testCode = TestModelFactory.buildTestCode()
    private val testCodeModel = testCode.toModel()
    private val testCodePackage = TestModelFactory.buildTestCodePackage()
    private val testCodePackageModel = testCodePackage.toModel()

    @Test
    fun `#findBySearchTokens return list of TestCode`() = runBlocking {
        val searchString = "cod"
        val active = true

        coEvery {
            testCodeDataService.find(queryEq {
                where {
                    this.searchTokens.search(searchString) and this.active.eq(active)
                }.limit { 100 }
            })
        } returns listOf(testCodeModel).success()

        val result = testCodeService.findBySearchTokens(searchString)
        ResultAssert.assertThat(result).isSuccessWithData(listOf(testCode))
    }

    @Test
    fun `#findBySearchTokens return list of TestCode ignoring active filter`() = runBlocking {
        val searchString = "cod"
        val active = null

        coEvery {
            testCodeDataService.find(queryEq { where { this.searchTokens.search(searchString) }.limit { 100 } })
        } returns listOf(testCodeModel).success()

        val result = testCodeService.findBySearchTokens(searchString, active)
        ResultAssert.assertThat(result).isSuccessWithData(listOf(testCode))
    }

    @Test
    fun `#findByRankedSearchTokens return list of TestCode`() = runBlocking {
        val searchString = "cod"
        val active = true

        coEvery {
            testCodeDataService.find(queryEq {
                where {
                    this.searchTokens.rankedSearch(searchString) and this.active.eq(active)
                }.limit { 100 }
            })
        } returns listOf(testCodeModel).success()

        val result = testCodeService.findByRankedSearchTokens(searchString)
        ResultAssert.assertThat(result).isSuccessWithData(listOf(testCode))
    }

    @Test
    fun `#findByRankedSearchTokens return list of TestCode ignoring active filter`() = runBlocking {
        val searchString = "cod"
        val active = null

        coEvery {
            testCodeDataService.find(queryEq { where { this.searchTokens.rankedSearch(searchString) }.limit { 100 } })
        } returns listOf(testCodeModel).success()

        val result = testCodeService.findByRankedSearchTokens(searchString, active)
        ResultAssert.assertThat(result).isSuccessWithData(listOf(testCode))
    }

    @Test
    fun `#upsert return updated TestCode`() = runBlocking {
        val olderDescription = "old description"
        val olderInternalDescription = "old internal description"
        val olderPriority = false
        val olderPreparationId = RangeUUID.generate()
        val mockedOldTestCode = TestModelFactory.buildTestCode(
            description = olderDescription,
            internalDescription = olderInternalDescription,
            priority = olderPriority,
            preparationId = olderPreparationId
        )
        val mockedOldTestCodeModel = mockedOldTestCode.toModel()
        val mockedNewTestCode =
            TestModelFactory.buildTestCode(
                code = mockedOldTestCode.code,
                description = "new description",
                internalDescription = "new internal description",
                priority = true,
                preparationId = RangeUUID.generate()
            ).copy(
                id = mockedOldTestCode.id,
            )
        val mockedNewTestCodeModel = mockedNewTestCode.toModel()

        coEvery {
            testCodeDataService.get(mockedOldTestCode.id)
        } returns mockedOldTestCodeModel.success()

        coEvery {
            testCodeDataService.update(match {
                it.code == mockedOldTestCode.code
                        && it.description == mockedNewTestCode.description
                        && it.internalDescription == mockedNewTestCode.internalDescription
                        && it.priority == mockedNewTestCode.priority
                        && it.preparationId == mockedNewTestCode.preparationId
            })
        } returns mockedNewTestCodeModel.success()

        coEvery { kafkaProducerService.produce(UpsertTestCodeEvent(mockedNewTestCode)) } returns mockk()

        val result = testCodeService.upsert(mockedNewTestCode)
        ResultAssert.assertThat(result).isSuccessWithData(mockedNewTestCode)

        coVerify(exactly = 1) { testCodeDataService.update(any()) }
        coVerify(exactly = 0) { testCodeDataService.add(any()) }
        coVerify(exactly = 1) { kafkaProducerService.produce(any()) }
    }

    @Test
    fun `#upsert return added TestCode`() = runBlocking {
        coEvery {
            testCodeDataService.get(testCode.id)
        } returns NotFoundException().failure()

        coEvery {
            testCodeDataService.add(match {
                it.code == testCode.code
                        && it.description == testCode.description
                        && it.internalDescription == testCode.internalDescription
                        && it.priority == testCode.priority
                        && it.preparationId == testCode.preparationId
            })
        } returns testCodeModel.success()

        coEvery { kafkaProducerService.produce(UpsertTestCodeEvent(testCode)) } returns mockk()

        val result = testCodeService.upsert(testCode)
        ResultAssert.assertThat(result).isSuccessWithData(testCode)

        coVerify(exactly = 0) { testCodeDataService.update(any()) }
        coVerify(exactly = 1) { testCodeDataService.add(any()) }
        coVerify(exactly = 1) { kafkaProducerService.produce(any()) }
    }

    @Test
    fun `#get should get test code by id`() = runBlocking {
        coEvery { testCodeDataService.get(testCode.id) } returns testCodeModel.success()

        val result = testCodeService.get(testCode.id)
        ResultAssert.assertThat(result).isSuccessWithData(testCode)
    }

    @Test
    fun `#findByProcedures should return a list of test code searched by procedures`() = runBlocking {
        coEvery {
            testCodeDataService.find(queryEq { where { this.code.inList(listOf(testCode.code)) } })
        } returns listOf(testCodeModel).success()

        val result = testCodeService.findByProcedures(listOf(testCode.code))
        ResultAssert.assertThat(result).isSuccessWithData(listOf(testCode))
    }

    @Test
    fun `#findAllByRange should return a list of test code limited by range`() = runBlocking {
        val active = true

        coEvery {
            testCodeDataService.find(queryEq {
                where { this.active.eq(active) }
                    .orderBy { code }
                    .sortOrder { asc }
                    .offset { 0 }
                    .limit { 50 }
            })
        } returns listOf(testCodeModel).success()

        val result = testCodeService.findAllByRange(IntRange(0, 49))
        ResultAssert.assertThat(result).isSuccessWithData(listOf(testCode))
    }

    @Test
    fun `#findAllByRange should return a list of test code limited by range ignoring active filter`() = runBlocking {
        val active = null

        coEvery {
            testCodeDataService.find(queryEq { orderBy { code }.sortOrder { asc }.offset { 0 }.limit { 50 } })
        } returns listOf(testCodeModel).success()

        val result = testCodeService.findAllByRange(IntRange(0, 49), active)
        ResultAssert.assertThat(result).isSuccessWithData(listOf(testCode))
    }

    @Test
    fun `#findByIds should return a list of test code by ids`() = runBlocking {
        coEvery {
            testCodeDataService.find(queryEq { where { id.inList(listOf(testCode.id)) } })
        } returns listOf(testCodeModel).success()

        val result = testCodeService.findByIds(listOf(testCode.id))
        ResultAssert.assertThat(result).isSuccessWithData(listOf(testCode))
    }

    @Test
    fun `#count should return count of tests code`() = runBlocking {
        coEvery {
            testCodeDataService.count(queryEq { all() })
        } returns 10.success()

        val result = testCodeService.count()
        ResultAssert.assertThat(result).isSuccessWithData(10)
    }

    @Test
    fun `#getPackage should get test code package by id`() = runBlocking {
        coEvery { testCodePackageDataService.get(testCodePackage.id) } returns testCodePackageModel.success()

        val result = testCodePackageDataService.get(testCodePackage.id)
        ResultAssert.assertThat(result).isSuccessWithData(testCodePackageModel)
    }

    @Test
    fun `#createPackage should create a new test code package`() = runBlocking {
        coEvery {
            testCodePackageDataService.add(testCodePackageModel)
        } returns testCodePackageModel.success()

        val result = testCodeService.createPackage(testCodePackage)
        ResultAssert.assertThat(result).isSuccessWithData(testCodePackage)

        coVerify(exactly = 0) { testCodePackageDataService.find(any()) }
        coVerify(exactly = 0) { testCodePackageDataService.update(any()) }
        coVerify(exactly = 1) { testCodePackageDataService.add(any()) }
    }

    @Test
    fun `#updatePackage should update the test code package`() = runBlocking {
        coEvery {
            testCodePackageDataService.update(testCodePackageModel)
        } returns testCodePackageModel.success()

        val result = testCodeService.updatePackage(testCodePackage)
        ResultAssert.assertThat(result).isSuccessWithData(testCodePackage)

        coVerify(exactly = 0) { testCodePackageDataService.find(any()) }
        coVerify(exactly = 1) { testCodePackageDataService.update(any()) }
        coVerify(exactly = 0) { testCodePackageDataService.add(any()) }
    }

    @Test
    fun `#findPackageBySearchTokens return list of test code packages`() = runBlocking {
        val searchString = "Exa"

        coEvery {
            testCodePackageDataService.find(queryEq { where { this.searchTokens.search(searchString) }.limit { 100 } })
        } returns listOf(testCodePackageModel).success()

        val result = testCodeService.findPackageBySearchTokens(searchString)
        ResultAssert.assertThat(result).isSuccessWithData(listOf(testCodePackage))
    }

    @Test
    fun `#findAllPackagesByRange should return a list of test code packages limited by range`() = runBlocking {
        coEvery {
            testCodePackageDataService.find(queryEq { orderBy { name }.sortOrder { asc }.offset { 0 }.limit { 50 } })
        } returns listOf(testCodePackageModel).success()

        val result = testCodeService.findAllPackagesByRange(IntRange(0, 49))
        ResultAssert.assertThat(result).isSuccessWithData(listOf(testCodePackage))
    }

    @Test
    fun `#countPackage should return count of test code packages`() = runBlocking {
        coEvery {
            testCodePackageDataService.count(queryEq { all() })
        } returns 10.success()

        val result = testCodeService.countPackage()
        ResultAssert.assertThat(result).isSuccessWithData(10)
    }

    @Test
    fun `#findAllPackages should return all packages`() = runBlocking {
        coEvery {
            testCodePackageDataService.find(queryEq {
                orderBy { name }.sortOrder { asc }
            })
        } returns listOf(testCodePackageModel).success()

        val result = testCodeService.findAllPackages()

        ResultAssert.assertThat(result).isSuccessWithData(listOf(testCodePackage))
    }

    @Test
    fun `#findAllByRankedSearchTokens should search test code and packages by tokens`() = runBlocking {
        val searchString = "Exa"
        val active = true

        val testCodePackage = testCodePackage.copy(testCodeIds = setOf(testCode.id))
        val testCodePackageModel = testCodePackage.toModel()

        coEvery {
            testCodePackageDataService.find(queryEq { where { this.searchTokens.search(searchString) }.limit { 100 } })
        } returns listOf(testCodePackageModel).success()

        coEvery {
            testCodeDataService.find(queryEq {
                where {
                    this.searchTokens.rankedSearch(searchString) and this.active.eq(active)
                }.limit { 100 }
            })
        } returns listOf(testCodeModel).success()

        coEvery {
            testCodeDataService.find(queryEq { where { id.inList(listOf(testCode.id)) } })
        } returns listOf(testCodeModel).success()

        val codeTransport = testCode.toTransport()
        val packageTransport = testCodePackage.toTransport(listOf(codeTransport))

        val result = testCodeService.findAllByRankedSearchTokens(searchString)
        ResultAssert.assertThat(result).isSuccessWithData(listOf(packageTransport, codeTransport))
    }

    @Test
    fun `#findAllPackagesByTestCodeId should return a list of test code packages by test code id`() = runBlocking {
        val testCode = TestModelFactory.buildTestCode()
        val testCodePackage = TestModelFactory.buildTestCodePackage(testCodeIds = setOf(testCode.id))
        val testCodePackageModel = testCodePackage.toModel()
        coEvery {
            testCodePackageDataService.find(queryEq { where { this.testCodeIds.contains(testCode.id) } })
        } returns listOf(testCodePackageModel).success()

        val result = testCodeService.findAllPackagesByTestCodeId(testCode.id)
        ResultAssert.assertThat(result).isSuccessWithData(listOf(testCodePackage))
    }

    @Test
    fun `#deletePackage should delete a test code package`() = runBlocking {
        val testCode = TestModelFactory.buildTestCode()
        val testCodePackage = TestModelFactory.buildTestCodePackage(testCodeIds = setOf(testCode.id))
        val testCodePackageModel = testCodePackage.toModel()
        coEvery {
            testCodePackageDataService.delete(testCodePackageModel)
        } returns true.success()

        val result = testCodeService.deletePackage(testCodePackage)
        ResultAssert.assertThat(result).isSuccessWithData(true)
    }

    private fun TestCodePackage.toTransport(tests: List<TestCodeTransport>) =
        TestCodeTransport(
            code = this.id.toString(),
            description = this.name,
            type = TestCodeType.PACKAGE,
            tests = tests,
            active = true,
        )

    private fun TestCode.toTransport() =
        TestCodeTransport(
            code = this.code,
            description = this.description,
            internalDescription = this.internalDescription,
            priority = this.priority,
            type = TestCodeType.TEST,
            active = this.active,
        )

    @Test
    fun `#findByCode should return a test code by code`() = runBlocking {
        coEvery {
            testCodeDataService.findOne(queryEq { where { this.code.eq(testCode.code) } })
        } returns testCodeModel.success()

        val result = testCodeService.findByCode(testCode.code)
        ResultAssert.assertThat(result).isSuccessWithData(testCode)
    }

    @Test
    fun `#findAllByCodes should return a list of test code by codes`() = runBlocking {
        coEvery {
            testCodeDataService.find(queryEq { where { this.code.inList(listOf(testCode.code)) } })
        } returns listOf(testCodeModel).success()

        val result = testCodeService.findAllByCodes(listOf(testCode.code))
        ResultAssert.assertThat(result).isSuccessWithData(listOf(testCode))
    }
}
