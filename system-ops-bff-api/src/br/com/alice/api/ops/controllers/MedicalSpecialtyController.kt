package br.com.alice.api.ops.controllers

import br.com.alice.common.Response
import br.com.alice.common.convertTo
import br.com.alice.common.core.exceptions.InvalidArgumentException
import br.com.alice.common.core.extensions.toUUID
import br.com.alice.common.extensions.coResultOf
import br.com.alice.common.extensions.getOrNullIfNotFound
import br.com.alice.common.extensions.then
import br.com.alice.common.extensions.thenError
import br.com.alice.common.foldResponse
import br.com.alice.common.logging.logger
import br.com.alice.common.toResponse
import br.com.alice.data.layer.models.AttentionLevel
import br.com.alice.data.layer.models.MedicalSpecialty
import br.com.alice.data.layer.models.MedicalSpecialtyType
import br.com.alice.data.layer.models.MedicalSpecialtyType.SPECIALTY
import br.com.alice.data.layer.models.MedicalSpecialtyType.SUBSPECIALTY
import br.com.alice.provider.client.CboCodeService
import br.com.alice.provider.client.MedicalSpecialtyService
import br.com.alice.staff.client.HealthProfessionalService
import com.github.kittinunf.result.Result
import com.github.kittinunf.result.failure
import com.github.kittinunf.result.fanout
import com.github.kittinunf.result.flatMap
import com.github.kittinunf.result.map
import io.ktor.http.HttpHeaders.ContentRange
import io.ktor.http.HttpStatusCode.Companion.OK
import io.ktor.http.Parameters
import java.util.UUID

private const val MAX_RANGE: Int = 999
class MedicalSpecialtyController(
    private val medicalSpecialtyService: MedicalSpecialtyService,
    private val healthProfessionalService: HealthProfessionalService,
    private val cboCodeService: CboCodeService,
) : BaseController<MedicalSpecialtyRequest, MedicalSpecialty, MedicalSpecialty>() {

    private val generalistName = "Generalista"

    override suspend fun index(queryParams: Parameters): Response {
        val range = parseRange(queryParams)
        val name = parseQuery(queryParams)
        val ids = parseIds(queryParams)
        val active = parseFilter(queryParams)?.get("active")?.let {
            it.toString().toBoolean()
        }
        val type = parseFilter(queryParams)?.get("type")?.toString()

        val results = if (name != null || active != null || type != null) {
            val medicalSpecialties = medicalSpecialtyService.getByQuery(name, active, type)
                .get()
                .filterNot { it.isOrphanSubSpecialty() }
            subListMedicalSpecialties(medicalSpecialties, range)
        } else if (ids != null && ids.isNotEmpty()) {
            getAll(ids).get()
        } else {
            val medicalSpecialties = getByRange(IntRange(0, MAX_RANGE))
                .get()
                .filterNot { it.isOrphanSubSpecialty() }
            subListMedicalSpecialties(medicalSpecialties, range)
        }

        return Response(OK, results, mapOf(ContentRange to results.size.toString()))
    }
    override suspend fun getByQueryAndRange(query: String, range: IntRange): Result<List<MedicalSpecialty>, Throwable> {
        return coResultOf {
            medicalSpecialtyService.getByName(query, SPECIALTY).get() +
                    medicalSpecialtyService.getByName(query, SUBSPECIALTY).get()
        }
    }

    override suspend fun getAll(ids: List<String>) = medicalSpecialtyService.getByIds(ids.map { it.toUUID() })
    override suspend fun getByRange(range: IntRange) = medicalSpecialtyService.getByRange(range, MedicalSpecialtyType.values().toList())
    override suspend fun count() = medicalSpecialtyService.count()
    override suspend fun get(id: String) = medicalSpecialtyService.getById(id.toUUID())
    override suspend fun add(model: MedicalSpecialty) = medicalSpecialtyService.add(model)

    override suspend fun update(
        id: String,
        request: MedicalSpecialtyRequest,
    ): Response =
        medicalSpecialtyService.getById(id.toUUID()).fanout {
            healthProfessionalService.findActivesByInternalAndExternalSpecialties(
                listOf(id.toUUID())
            )
        }.flatMap {
            val model = it.first
            val hasSpecialists = it.second.isNotEmpty()

            val deactivating = model.active && !request.active

            if (deactivating && hasSpecialists) {
                    InvalidArgumentException(
                        code = "invalid_specialty_state",
                        message = "specialty with specialists associated"
                    ).failure()
            } else {
                medicalSpecialtyService.update(
                    formatRequestToUpdate(model, request, updateRequestWithCompleteCboCode(request))
                ).then { updated ->
                    logger.info("Updated ${updated::class.simpleName}", "current_staff_id" to currentUserIdKey(),
                        "request" to request, "model" to updated)
                }
            }
        }.foldResponse()

    override suspend fun formatRequest(request: MedicalSpecialtyRequest) = request.convertTo(MedicalSpecialty::class)
    fun formatRequestToUpdate(
        model: MedicalSpecialty,
        request: MedicalSpecialtyRequest,
        cboCode: MedicalSpecialty.CboCode?
    ) = model.copy(
        type = request.type,
        name = request.name,
        parentSpecialtyId = request.parentSpecialtyId,
        requireSpecialist = request.requireSpecialist,
        generateGeneralistSubSpecialty = request.generateGeneralistSubSpecialty,
        active = request.active,
        internal = request.internal,
        attentionLevel = request.attentionLevel,
        isTherapy = request.isTherapy,
        cboCode = cboCode,
        isAdvancedAccess = request.isAdvancedAccess,
    )

    suspend fun getSpecialty(queryParams: Parameters): Response {
        val filterQuery = parseQuery(queryParams)
        val ids = parseIds(queryParams)

        val filtered = when {
            filterQuery != null -> medicalSpecialtyService.getByName(filterQuery, SPECIALTY).get()
            ids != null -> medicalSpecialtyService.getByIds(ids.map { it.toUUID() }).get()
            else -> null
        }

        return if (filtered != null) Response(OK, filtered, mapOf(ContentRange to filtered.size.toString()))
        else getResponseByType(queryParams, SPECIALTY)
    }

    suspend fun getSubSpecialty(queryParams: Parameters): Response {
        val filterQuery = parseQuery(queryParams)
        val id = parseFilter<String>(queryParams, "parent_specialty_id")
        val ids = parseIds(queryParams)


        val filtered = when {
            filterQuery != null -> medicalSpecialtyService.getByName(filterQuery, SUBSPECIALTY).get()
            id != null -> medicalSpecialtyService.getByParentId(id.toUUID()).get()
            ids != null -> medicalSpecialtyService.getByIds(ids.map { it.toUUID() }).get()
            else -> null
        }

        return if (filtered != null) Response(OK, filtered, mapOf(ContentRange to filtered.size.toString()))
        else getResponseByType(queryParams, SUBSPECIALTY)
    }

    private suspend fun getResponseByType(queryParams: Parameters, type: MedicalSpecialtyType): Response {
        val range = parseRange(queryParams)
        val byRange = medicalSpecialtyService.getByRange(range, listOf(type)).get()
        val totalCount = medicalSpecialtyService.count().get()

        return Response(OK, byRange, mapOf(ContentRange to totalCount.toString()))
    }

    private fun formatRequestToCreate(
        request: MedicalSpecialtyRequest,
        cboCode: MedicalSpecialty.CboCode?
    ) = MedicalSpecialty(
        type = request.type,
        name = request.name,
        parentSpecialtyId = request.parentSpecialtyId,
        requireSpecialist = request.requireSpecialist,
        generateGeneralistSubSpecialty = request.generateGeneralistSubSpecialty,
        active = request.active,
        internal = request.internal,
        attentionLevel = request.attentionLevel,
        isTherapy = request.isTherapy,
        urlSlug = request.urlSlug,
        cboCode = cboCode,
        isAdvancedAccess = request.isAdvancedAccess,
    )

    override suspend fun create(request: MedicalSpecialtyRequest): Response {
        val cboCode = updateRequestWithCompleteCboCode(request)
        val specialty = add(formatRequestToCreate(request, cboCode)).get()
        logger.info("Added MedicalSpecialty", "current_staff_id" to currentUserIdKey(), "model" to specialty)
        if (SPECIALTY == specialty.type && specialty.generateGeneralistSubSpecialty) {
            add(MedicalSpecialty(generalistName, SUBSPECIALTY, parentSpecialtyId = specialty.id, urlSlug = ""))
                .then {
                    logger.info("Added MedicalSpecialty", "current_staff_id" to currentUserIdKey(), "model" to it)
                }.thenError {
                    logger.error("Error an create subSpecialty",  "error" to it)
                }
        }
        return specialty.toResponse()
    }

    private suspend fun updateRequestWithCompleteCboCode(request: MedicalSpecialtyRequest): MedicalSpecialty.CboCode? {
        logger.info(
            "Getting complete cboCode from request",
            "request_name" to request.name,
            "request_cbo_code_id" to (request.cboCode?.id ?: "not_present")
        )
        return request.cboCode?.let { cboCodeToUpdate ->
            cboCodeService.get(cboCodeToUpdate.id)
                .map {
                    MedicalSpecialty.CboCode(
                        id = it.id,
                        description = it.description,
                        code = it.code
                    )
                }.getOrNullIfNotFound()
        }
    }

    private fun subListMedicalSpecialties(specialties: List<MedicalSpecialty>,range: IntRange): List<MedicalSpecialty> {
        val total = if (range.count() > specialties.size) {
            specialties.size
        } else {
            range.count()
        }
        return specialties.subList(range.first, total)
    }
}

data class MedicalSpecialtyRequest(
    val name: String,
    val type: MedicalSpecialtyType,
    val parentSpecialtyId: UUID? = null,
    val requireSpecialist: Boolean = false,
    val generateGeneralistSubSpecialty: Boolean = false,
    val active: Boolean = true,
    val internal: Boolean = false,
    val urlSlug: String,
    val attentionLevel: AttentionLevel? = null,
    val isTherapy: Boolean = false,
    val cboCode: CboCode? = null,
    val isAdvancedAccess: Boolean = false,
) {
    data class CboCode(
        val id: UUID,
    )
}
