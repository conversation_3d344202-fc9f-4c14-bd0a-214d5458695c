package br.com.alice.marauders.map.services

import br.com.alice.common.core.Status
import br.com.alice.common.extensions.flatMapPair
import br.com.alice.common.service.data.client.Adder
import br.com.alice.common.service.data.client.Getter
import br.com.alice.common.service.data.client.Updater
import br.com.alice.common.service.data.dsl.and
import br.com.alice.data.layer.models.RiskCalculationConf
import br.com.alice.data.layer.models.RiskGroup
import br.com.alice.data.layer.models.RiskGroup.Type.DEFAULT
import br.com.alice.data.layer.services.RiskGroupDataService
import br.com.alice.healthcondition.client.HealthConditionService
import br.com.alice.marauders.map.client.RiskCalculationConfService
import br.com.alice.marauders.map.client.RiskGroupService
import br.com.alice.marauders.map.models.Config
import br.com.alice.marauders.map.models.RiskGroupWithConfigs
import com.github.kittinunf.result.Result
import com.github.kittinunf.result.flatMap
import com.github.kittinunf.result.success
import java.util.UUID

class RiskGroupServiceImpl(
    private val riskGroupDataService: RiskGroupDataService,
    private val riskCalculationConfService: RiskCalculationConfService,
    private val healthConditionService: HealthConditionService
): RiskGroupService,
    Adder<RiskGroup> by riskGroupDataService,
    Updater<RiskGroup> by riskGroupDataService,
    Getter<RiskGroup> by riskGroupDataService
{
    override suspend fun getByIds(ids: List<UUID>): Result<List<RiskGroup>, Throwable> =
        riskGroupDataService.find { where {
            this.id.inList(ids)
        } }

    override suspend fun getByName(name: String): Result<RiskGroup, Throwable> =
        riskGroupDataService.findOne {
            where { this.name.eq(name) }
        }

    override suspend fun create(name: String, cidsAdd: List<UUID>, type: RiskGroup.Type?): Result<Boolean, Throwable> =
        riskGroupDataService.add(
            RiskGroup(
                name = name,
                key = name.replace(" ", "_").uppercase(),
                type = type ?: DEFAULT
            )
        ).flatMap { riskGroup ->
            if (cidsAdd.isEmpty()) return@flatMap true.success()

            riskCalculationConfService.getByHealthConditions(cidsAdd).flatMap { confList ->

                val hasRiskNotNull = confList.filter { conf -> conf.riskGroupId != null }

                if (hasRiskNotNull.isNotEmpty()) {
                    throw UnsupportedOperationException("Risk Configuration already has risk group")
                }
                confList.forEach { conf -> riskCalculationConfService.update(conf.copy(riskGroupId = riskGroup.id)) }
                true.success()
            }
        }

    override suspend fun getByIdWithConfigs(id: UUID): Result<RiskGroupWithConfigs, Throwable> {
        return riskGroupDataService.get(id).flatMapPair {
            riskCalculationConfService.getByRiskGroup(it.id)
        }.flatMap { pair: Pair<List<RiskCalculationConf>, RiskGroup> ->
            val riskCalculationConfs = pair.first
            val riskGroup = pair.second

            if (riskCalculationConfs.isEmpty()) {
                return@flatMap RiskGroupWithConfigs(
                    riskGroup = riskGroup
                ).success()
            }

            val mapRiskConf = riskCalculationConfs.associateBy { it.healthConditionId }
            val configMutationList = mutableListOf<Config>()

            healthConditionService.getAll(riskCalculationConfs.map { it.healthConditionId }).get()
                .forEach {
                    configMutationList.add(Config(
                        id = mapRiskConf[it.id]!!.id,
                        code = it.code!!,
                        name = it.displayName
                    ))
                }

            RiskGroupWithConfigs(
                riskGroup = riskGroup,
                configs = configMutationList
            ).success()
        }
    }

    override suspend fun updateWithConfigs(riskGroup: RiskGroup, cidsAdd: List<UUID>): Result<Boolean, Throwable> =
        riskGroupDataService.update(riskGroup).flatMapPair {
            riskCalculationConfService.getByRiskGroup(it.id)
        }.flatMap { pair ->
            val listBD = pair.first
            if (cidsAdd.isEmpty() && listBD.isEmpty()) return@flatMap true.success()

            val riskGroupBD = pair.second
            val listAdd = riskCalculationConfService.getByIds(cidsAdd).get()

            listAdd.forEach {
                if (!listBD.contains(it)) {
                    if (it.riskGroupId != null) {
                        throw UnsupportedOperationException("Risk Configuration already has risk group")
                    }
                    riskCalculationConfService.update(it.copy(riskGroupId = riskGroupBD.id))
                }
            }

            listBD.forEach {
                if (!listAdd.contains(it)) {
                    riskCalculationConfService.update(it.copy(riskGroupId = null))
                }
            }
            true.success()
        }

    override suspend fun findByFilter(
        range: IntRange,
        name: String?,
        status: Status
    ): Result<List<RiskGroup>, Throwable> =
        riskGroupDataService.find {
            buildFilterQuery(name, status)
                .orderBy { this.name }
                .sortOrder { asc }
                .offset { range.first }
                .limit { range.count() }
        }

    override suspend fun countByFilter(
        name: String?,
        status: Status
    ): Result<Int, Throwable> =
        riskGroupDataService.count { buildFilterQuery(name, status) }

    private fun buildFilterQuery(name: String?, status: Status) =
        name?.let {
            riskGroupDataService.queryBuilder().where {
                this.name.like(it) and this.status.eq(status)
            }
        } ?: riskGroupDataService.queryBuilder().where {
            this.status.eq(status)
        }
}
