package br.com.alice.data.layer.tables

import br.com.alice.common.RangeUUID
import br.com.alice.data.layer.models.StaffChannelHistoryUpdatedBy
import java.time.LocalDateTime
import java.util.UUID

internal data class StaffChannelHistoryTable(
    override val id: UUID = RangeUUID.generate(),
    val staffId: UUID,
    val status: String,
    val changedDate: LocalDateTime,
    val onCall: Boolean? = null,
    val updatedBy: StaffChannelHistoryUpdatedBy? = null,
    override val version: Int = 0,
    override val createdAt: LocalDateTime = LocalDateTime.now(),
    override val updatedAt: LocalDateTime = LocalDateTime.now(),
) : Table<StaffChannelHistoryTable> {

    override fun copyTable(version: Int, updatedAt: LocalDateTime, createdAt: LocalDateTime) =
        copy(version = version, createdAt = createdAt, updatedAt = updatedAt)
}
