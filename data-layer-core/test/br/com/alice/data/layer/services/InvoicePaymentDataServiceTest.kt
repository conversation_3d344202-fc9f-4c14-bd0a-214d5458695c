package br.com.alice.data.layer.services

import br.com.alice.common.PaymentMethod
import br.com.alice.common.RangeUUID
import br.com.alice.common.data.dsl.matchers.ResultAssert.Companion.assertThat
import br.com.alice.data.layer.helpers.DataServiceTestHelper
import br.com.alice.data.layer.helpers.TestTableFactory
import br.com.alice.data.layer.models.InvoicePaymentModel
import br.com.alice.data.layer.models.InvoicePaymentOrigin
import br.com.alice.data.layer.models.InvoicePaymentStatus
import br.com.alice.data.layer.repositories.JdbiRepositoryFactory
import br.com.alice.data.layer.tables.MemberInvoiceTable
import kotlinx.coroutines.runBlocking
import java.math.BigDecimal
import java.time.LocalDate
import java.time.LocalDateTime
import kotlin.test.BeforeTest
import kotlin.test.Test

class InvoicePaymentDataServiceTest : DataServiceTestHelper() {

    private val dataService = InvoicePaymentModelDataServiceClient(httpInvoker)

    private val repositoryFactory = JdbiRepositoryFactory(mainJdbi)

    private val memberInvoiceRepo = repositoryFactory.get(MemberInvoiceTable::class)

    @BeforeTest
    fun setup() {
        val person1 = TestTableFactory.buildPersonTable()
        val memberInvoice = MemberInvoiceTable(
            memberId = RangeUUID.generate(),
            personId = person1.id,
            totalAmount = BigDecimal("880.00"),
            referenceDate = LocalDate.now(),
            dueDate = LocalDateTime.now(),
            id = RangeUUID.generate(),
        )

        memberInvoiceRepo.add(memberInvoice)
    }

    @Test
    fun `should add and find invoice payment by member invoice id`() = runBlocking {
        val person = TestTableFactory.buildPersonTable()
        val memberInvoice = MemberInvoiceTable(
            memberId = RangeUUID.generate(),
            personId = person.id,
            totalAmount = BigDecimal("880.00"),
            referenceDate = LocalDate.now(),
            dueDate = LocalDateTime.now(),
            id = RangeUUID.generate(),
        )
        val memberInvoiceResult = memberInvoiceRepo.add(memberInvoice).get()

        val invoicePayment = InvoicePaymentModel(
            amount = BigDecimal(699.00),
            approvedAt = null,
            status = InvoicePaymentStatus.PENDING,
            method = PaymentMethod.BOLETO,
            canceledReason = null,
            externalId = null,
            source = null,
            memberInvoiceIds = listOf(memberInvoiceResult.id),
            invoiceGroupId = RangeUUID.generate(),
            billingAccountablePartyId = null,
            origin = InvoicePaymentOrigin.UNDEFINED,
            reason = null,
            invoiceLiquidationId = null,
            sendEmail = null
        )
        val result = dataService.add(invoicePayment)
        assertThat(result).isSuccessWithDataIgnoringGivenFields(invoicePayment, "createdAt", "updatedAt", "updatedBy")

        val resultFind = dataService.find { where { this.memberInvoiceIds.contains(memberInvoiceResult.id) } }
        assertThat(resultFind).isSuccessWithDataIgnoringGivenFields(
            listOf(invoicePayment),
            "createdAt",
            "updatedAt",
            "updatedBy",
        )
    }

    @Test
    fun `should not find invoice payment by member invoice id`() = runBlocking {
        val person = TestTableFactory.buildPersonTable()
        val memberInvoice = MemberInvoiceTable(
            memberId = RangeUUID.generate(),
            personId = person.id,
            totalAmount = BigDecimal("880.00"),
            referenceDate = LocalDate.now(),
            dueDate = LocalDateTime.now(),
            id = RangeUUID.generate(),
        )
        val memberInvoiceResult = memberInvoiceRepo.add(memberInvoice).get()

        val resultFind = dataService.find { where { this.memberInvoiceIds.contains(memberInvoiceResult.id) } }
        assertThat(resultFind).isSuccessWithData(emptyList())
    }

    @Test
    fun `should find invoice payment by invoice liquidation id`() = runBlocking {
        val invoiceLiquidationId = RangeUUID.generate()
        val invoicePayment = InvoicePaymentModel(
            amount = BigDecimal(699.00),
            approvedAt = null,
            status = InvoicePaymentStatus.PENDING,
            method = PaymentMethod.BOLETO,
            canceledReason = null,
            externalId = null,
            source = null,
            memberInvoiceIds = emptyList(),
            invoiceGroupId = RangeUUID.generate(),
            billingAccountablePartyId = null,
            origin = InvoicePaymentOrigin.UNDEFINED,
            reason = null,
            invoiceLiquidationId = invoiceLiquidationId,
            sendEmail = null
        )

        dataService.add(invoicePayment)

        val resultFind = dataService.find { where { this.invoiceLiquidationId.eq(invoiceLiquidationId) } }
        assertThat(resultFind).isSuccessWithDataIgnoringGivenFields(
            listOf(invoicePayment),
            "createdAt",
            "updatedAt",
            "updatedBy",
        )
    }

    @Test
    fun `should find not invoice payment by invoice liquidation id`() = runBlocking {
        val invoiceLiquidationId = RangeUUID.generate()
        val invoicePayment = InvoicePaymentModel(
            amount = BigDecimal(699.00),
            approvedAt = null,
            status = InvoicePaymentStatus.PENDING,
            method = PaymentMethod.BOLETO,
            canceledReason = null,
            externalId = null,
            source = null,
            memberInvoiceIds = emptyList(),
            invoiceGroupId = RangeUUID.generate(),
            billingAccountablePartyId = null,
            origin = InvoicePaymentOrigin.UNDEFINED,
            reason = null,
            invoiceLiquidationId = RangeUUID.generate(),
            sendEmail = null
        )

        dataService.add(invoicePayment)

        val resultFind = dataService.find { where { this.invoiceLiquidationId.eq(invoiceLiquidationId) } }
        assertThat(resultFind).isSuccessWithData(emptyList())
    }
}
