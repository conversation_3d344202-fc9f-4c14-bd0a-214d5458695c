package br.com.alice.exec.indicator.consumers

import br.com.alice.common.data.dsl.matchers.ResultAssert
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.eita.nullvs.events.NullvsAttachmentChemotherapyUpsertedResponseEvent
import br.com.alice.eita.nullvs.models.guia.BeneficiaryComplexGuiaResponse
import br.com.alice.eita.nullvs.models.guia.MedicationResponse
import br.com.alice.eita.nullvs.models.guia.OncologicalDiagnosisChemotherapyResponse
import br.com.alice.eita.nullvs.models.guia.PreviousTreatmentsChemotherapyResponse
import br.com.alice.eita.nullvs.models.guia.RequesterSimpleDataResponse
import br.com.alice.eita.nullvs.models.guia.SolicitationChemotherapyDataResponse
import br.com.alice.eita.nullvs.models.guia.TotvsChemotherapyAttachmentResponse
import br.com.alice.exec.indicator.exceptions.GuiaCreationAlreadyProcessedException
import br.com.alice.exec.indicator.exceptions.ReferenceGuiaNotFoundByExternalCode
import br.com.alice.exec.indicator.service.internal.UpsertAttachmentChemotherapyService
import com.github.kittinunf.result.failure
import com.github.kittinunf.result.success
import io.mockk.coEvery
import io.mockk.mockk
import kotlinx.coroutines.runBlocking
import java.time.LocalDate
import kotlin.test.Test


class NullvsAttachmentChemotherapyUpsertedConsumerTest : ConsumerTest() {

    private val upsertAttachmentChemotherapyService: UpsertAttachmentChemotherapyService = mockk()

    private val consumer = NullvsAttachmentChemotherapyUpsertedConsumer(upsertAttachmentChemotherapyService)

    @Test
    fun `#onAttachmentChemotherapyUpserted should call use case sucessfully`() = runBlocking {
        val payload = buildWebhookPayload()
        val event = NullvsAttachmentChemotherapyUpsertedResponseEvent(payload)
        val chemo = TestModelFactory.buildAttachmentChemotherapy()

        coEvery {
            upsertAttachmentChemotherapyService.upsert(payload)
        } returns chemo.success()

        val result = consumer.onAttachmentChemotherapyUpserted(event)

        ResultAssert.assertThat(result).isSuccessWithData(chemo)

        coVerifyOnce { upsertAttachmentChemotherapyService.upsert(any()) }
    }

    @Test
    fun `#onAttachmentChemotherapyUpserted should call use case sucessfully and process successfuly if upsert throws GuiaCreationAlreadyProcessedException`() = runBlocking {
        val payload = buildWebhookPayload()
        val event = NullvsAttachmentChemotherapyUpsertedResponseEvent(payload)
        val chemo = TestModelFactory.buildAttachmentChemotherapy()

        coEvery {
            upsertAttachmentChemotherapyService.upsert(payload)
        } returns GuiaCreationAlreadyProcessedException("").failure()

        val result = consumer.onAttachmentChemotherapyUpserted(event)

        ResultAssert.assertThat(result).isSuccess()

        coVerifyOnce { upsertAttachmentChemotherapyService.upsert(any()) }
    }

    @Test
    fun `#onAttachmentChemotherapyUpserted should call use case sucessfully and fail if upsert throws ReferenceGuiaNotFoundByExternalCode`() = runBlocking {
        val payload = buildWebhookPayload()
        val event = NullvsAttachmentChemotherapyUpsertedResponseEvent(payload)
        val chemo = TestModelFactory.buildAttachmentChemotherapy()

        coEvery {
            upsertAttachmentChemotherapyService.upsert(payload)
        } returns ReferenceGuiaNotFoundByExternalCode("").failure()

        val result = consumer.onAttachmentChemotherapyUpserted(event)

        ResultAssert.assertThat(result).isFailure()

        coVerifyOnce { upsertAttachmentChemotherapyService.upsert(any()) }
    }

    private fun buildWebhookPayload(): TotvsChemotherapyAttachmentResponse =
        TotvsChemotherapyAttachmentResponse(
            action = "Criar",
            guiaNumber = "123456",
            referenceGuiaNumber = "654321",
            providerGuiaNumber = "789012",
            status = "Aprovado",
            authorizationDate = LocalDate.now(),
            guiaType = "Quimioterapia",
            passcode = "abc123",
            beneficiary = BeneficiaryComplexGuiaResponse(
                name = "João da Silva",
                identificationCardNumber = "123456789",
                nationalId = "98765432100",
                isNewBorn = false,
                weight = "70kg",
                height = "1.75m",
                bodySurface = "1.85m²",
                age = "45",
                sex = "Masculino"
            ),
            requester = RequesterSimpleDataResponse(
                name = "Sei la",
                email = "asjhskajhdjs",
                phone = "182793128379"
            ),
            solicitation = SolicitationChemotherapyDataResponse(
                solicitationDate = LocalDate.now(),
                numberOfCycles = 6,
                currentCycle = 1,
                daysPerCycle = 21,
                interval = 7,
                specification = "Nenhuma observação"
            ),
            oncologicalDiagnosis = OncologicalDiagnosisChemotherapyResponse(
                diagnosisDate = LocalDate.of(2023, 1, 1),
                cid = "C50",
                staging = "IIIA",
                chemotherapyType = "Adjuvante",
                purpose = "Curativo",
                ecog = "1",
                tumor = "T3",
                nodule = "N1",
                metastasis = "M0",
                therapeuticPlan = "Plano A",
                cytologyHistologyDiagnosis = "Adenocarcinoma",
                relevantInfo = "Sem histórico familiar"
            ),
            previousTreatments = PreviousTreatmentsChemotherapyResponse(
                surgery = "Mastectomia",
                surgeryDate = LocalDate.of(2022, 6, 1),
                irradiatedArea = "Mama",
                applicationDate = LocalDate.of(2022, 9, 1)
            ),
            medications = listOf(
                MedicationResponse(
                    table = "Tabela 1",
                    code = "MED001",
                    description = "Medicação A",
                    dosage = 500.0,
                    measureUnit = "mg",
                    administrationRoute = "Intravenosa",
                    expectedAdministrationDate = LocalDate.now(),
                    status = "Aprovado",
                    gloss = emptyList(),
                    frequency = 1
                )
            )
        )
}

