package br.com.alice.ehr.services

import br.com.alice.common.core.PersonId
import br.com.alice.common.extensions.then
import br.com.alice.common.kafka.interfaces.KafkaProducerService
import br.com.alice.common.service.data.dsl.OrPredicateUsage
import br.com.alice.common.service.data.dsl.SortOrder
import br.com.alice.common.service.data.dsl.and
import br.com.alice.common.service.data.dsl.or
import br.com.alice.common.service.data.dsl.scope
import br.com.alice.common.service.extensions.WithFilterPredicateUsage
import br.com.alice.common.service.extensions.basePredicateForFilters
import br.com.alice.common.service.extensions.withFilter
import br.com.alice.data.layer.models.TertiaryIntentionHospitalizationResponsible
import br.com.alice.data.layer.models.TertiaryIntentionSurgeryStatus
import br.com.alice.data.layer.models.TertiaryIntentionTouchPoint
import br.com.alice.data.layer.models.TertiaryIntentionType
import br.com.alice.data.layer.services.TertiaryIntentionTouchPointDataService
import br.com.alice.data.layer.services.TertiaryIntentionTouchPointDataService.OrderingOptions
import br.com.alice.ehr.client.TertiaryIntentionTouchPointService
import br.com.alice.ehr.event.TertiaryIntentionTouchPointCreatedEvent
import br.com.alice.ehr.event.TertiaryIntentionTouchPointUpdatedEvent
import br.com.alice.ehr.model.TertiaryIntentionTouchPointFilter
import br.com.alice.ehr.services.internal.tertiary_intention_touch_point.validate
import com.github.kittinunf.result.Result
import java.time.LocalDateTime
import java.util.UUID

class TertiaryIntentionTouchPointServiceImpl(
    private val dataService: TertiaryIntentionTouchPointDataService,
    private val kafkaProducerService: KafkaProducerService,
) : TertiaryIntentionTouchPointService {

    companion object {
        val CURRENT_ATTENDANCES_STATUS =
            listOf(TertiaryIntentionSurgeryStatus.PERFORMED, TertiaryIntentionSurgeryStatus.POSOPERATIVE)
    }

    override suspend fun get(id: UUID): Result<TertiaryIntentionTouchPoint, Throwable> =
        dataService.get(id)

    @OptIn(OrPredicateUsage::class)
    override suspend fun countAllInCurrent(filters: TertiaryIntentionTouchPointFilter): Result<Int, Throwable> =
        dataService.count {
            where {
                this.startedAt.isNotNull() and
                        filters.stayInDays?.let { this.startedAt.lessEq(LocalDateTime.now().minusDays(it)) } and
                        this.endedAt.isNull() and
                        scope(
                            this.type.eq(TertiaryIntentionType.TIT_HOSPITALIZATION) or
                                    scope(
                                        this.type.eq(TertiaryIntentionType.TIT_SURGERY) and
                                                this.surgeryStatus.inList(CURRENT_ATTENDANCES_STATUS)
                                    )
                        ) and
                        filters.toPredicate(this)
            }
        }


    override suspend fun findByPersonIdAndType(
        personId: PersonId,
        type: TertiaryIntentionType
    ): Result<List<TertiaryIntentionTouchPoint>, Throwable> =
        dataService.find { where { this.personId.eq(personId).and(this.type.eq(type)) } }

    override suspend fun findByPersonIdAndBetweenDates(
        personId: PersonId,
        initialDate: LocalDateTime,
        finalDate: LocalDateTime
    ): Result<List<TertiaryIntentionTouchPoint>, Throwable> =
        dataService.find {
            where {
                this.personId.eq(personId) and
                        this.startedAt.greaterEq(initialDate) and
                        this.startedAt.lessEq(finalDate)
            }.orderBy { this.startedAt }.sortOrder { desc }
        }

    override suspend fun create(model: TertiaryIntentionTouchPoint): Result<TertiaryIntentionTouchPoint, Throwable> {
        model.validate()
        val event = TertiaryIntentionTouchPointCreatedEvent(model)
        kafkaProducerService.produce(event)
        return dataService.add(model)
    }

    override suspend fun update(
        model: TertiaryIntentionTouchPoint,
        publishEvent: Boolean
    ): Result<TertiaryIntentionTouchPoint, Throwable> {
        model.validate()
        return dataService.update(model)
            .then {
                if (publishEvent) {
                    val event = TertiaryIntentionTouchPointUpdatedEvent(it)
                    kafkaProducerService.produce(event)
                }
            }
    }


    @OptIn(OrPredicateUsage::class)
    override suspend fun findAllInCurrentAttendance(
        filters: TertiaryIntentionTouchPointFilter,
        range: IntRange
    ): Result<List<TertiaryIntentionTouchPoint>, Throwable> =
        dataService.find {
            where {
                this.startedAt.isNotNull() and
                        this.endedAt.isNull() and
                        scope(
                            this.type.eq(TertiaryIntentionType.TIT_HOSPITALIZATION) or
                                    scope(
                                        this.type.eq(TertiaryIntentionType.TIT_SURGERY) and
                                                this.surgeryStatus.inList(CURRENT_ATTENDANCES_STATUS)
                                    )
                        ) and
                        filters.toPredicate(this)
            }.orderBy { filters.sort?.toField() ?: startedAt }
                .sortOrder { filters.sort?.toSortOrder() ?: desc }
                .offset { range.first }
                .limit { range.last }
        }

    override suspend fun findByHealthEventId(healthEventId: UUID): Result<TertiaryIntentionTouchPoint, Throwable> =
        dataService.findOne { where { this.healthEventId.eq(healthEventId.toString()) } }

    override suspend fun findByTotvsGuia(totvsGuiaId: UUID): Result<TertiaryIntentionTouchPoint, Throwable> =
        dataService.findOne { where { this.totvsGuiaId.eq(totvsGuiaId) } }

    override suspend fun findTertiaryIntentionByPeriod(
        personId: PersonId,
        providerUnit: UUID,
        type: TertiaryIntentionType,
        dateInit: LocalDateTime,
        dateLimit: LocalDateTime
    ): Result<TertiaryIntentionTouchPoint, Throwable> =
        dataService.findOne {
            where {
                this.personId.eq(personId) and
                        this.providerUnitId.eq(providerUnit) and
                        this.type.eq(type) and
                        this.startedAt.greater(dateInit) and
                        this.startedAt.less(dateLimit)
            }
        }

    override suspend fun findTertiaryIntentionByPeriodAndTypes(
        personId: PersonId,
        types: List<TertiaryIntentionType>,
        dateInit: LocalDateTime,
        dateLimit: LocalDateTime
    ): Result<List<TertiaryIntentionTouchPoint>, Throwable> = dataService.find {
        where {
            this.personId.eq(personId) and
                    this.type.inList(types) and
                    this.startedAt.greater(dateInit) and
                    this.startedAt.less(dateLimit)
        }
    }

    override suspend fun findByObjectiveCodes(
        offset: Int,
        limit: Int
    ): Result<List<TertiaryIntentionTouchPoint>, Throwable> = dataService.find {
        where {
            this.objectiveCodes.isNotEmpty()
        }.orderBy { this.createdAt }.sortOrder { desc }
            .offset { offset }
            .limit { limit }
    }

    @OptIn(WithFilterPredicateUsage::class)
    private fun TertiaryIntentionTouchPointFilter.toPredicate(fieldOptions: TertiaryIntentionTouchPointDataService.FieldOptions) =
        basePredicateForFilters()
            .withFilter(type) { fieldOptions.type.eq(it) }
            .withFilter(personId) { fieldOptions.personId.eq(it) }
            .withFilter(medicalSpecialtyName) { fieldOptions.hospitalizationSpecialty.eq(it) }
            .withFilter(providerUnits) { fieldOptions.providerUnitId.inList(it) }
            .withFilter(hasRear.takeIf { it }) {
                fieldOptions.hospitalizationResponsible.eq(
                    TertiaryIntentionHospitalizationResponsible.ALICE_REAR
                )
            }

    private fun TertiaryIntentionTouchPointFilter.SortingOperation.toSortOrder() =
        when (order) {
            TertiaryIntentionTouchPointFilter.SortDirection.ASC -> SortOrder.Ascending
            else -> SortOrder.Descending
        }

    private fun TertiaryIntentionTouchPointFilter.SortingOperation.toField() =
        when (field) {
            TertiaryIntentionTouchPointFilter.SortField.STAY_IN_DAYS -> OrderingOptions().startedAt
            TertiaryIntentionTouchPointFilter.SortField.HAS_REAR -> OrderingOptions().hospitalizationResponsible
            else -> OrderingOptions().startedAt
        }
}
