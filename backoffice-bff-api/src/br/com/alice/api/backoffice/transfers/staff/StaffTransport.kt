package br.com.alice.api.backoffice.transfers.staff

import br.com.alice.common.core.Role
import br.com.alice.common.core.StaffType
import br.com.alice.common.models.Gender
import br.com.alice.common.models.SpecialistTier
import br.com.alice.common.models.State
import br.com.alice.data.layer.models.AvailableDay
import br.com.alice.data.layer.models.HealthSpecialistScoreEnum
import br.com.alice.data.layer.models.MedicalSpecialty
import br.com.alice.data.layer.models.ModalityType
import br.com.alice.data.layer.models.OnCallPaymentMethod
import br.com.alice.data.layer.models.PhoneNumber
import br.com.alice.data.layer.models.ProviderUnit
import br.com.alice.data.layer.models.Qualification
import java.time.LocalDate
import java.util.UUID

data class StaffShortResponse(
    val id: UUID,
    val firstName: String,
    val lastName: String,
    val email: String,
    val active: Boolean,
    val role: Role
)

data class StaffFullResponse(
    val id: UUID,
    val firstName: String,
    val lastName: String,
    val fullName: String,
    val email: String,
    val nationalId: String? = null,
    val birthdate: LocalDate? = null,
    val gender: Gender,
    val profileImageUrl: String? = null,
    val role: Role,
    val type: StaffType,
    val active: Boolean = true,
    val version: Int,
    val urlSlug: String? = null,
    val quote: String? = null,
    val profileBio: String? = null,
    val council: CouncilDTO? = null,
    val specialty: UUID? = null,
    val subSpecialties: List<UUID>? = null,
    val internalSpecialty: UUID? = null,
    val internalSubSpecialties: List<UUID>? = null,
    val providerUnits: List<UUID>? = null,
    val qualifications: List<Qualification>? = null,
    val tier: SpecialistTier? = null,
    val theoristTier: SpecialistTier? = null,
    val curiosity: String? = null,
    val showOnApp: Boolean? = null,
    val education: List<String>? = null,
    val healthSpecialistScore: HealthSpecialistScoreEnum? = null,
    val deAccreditationDate: LocalDate? = null,
    val contacts: List<ContactDTO>? = null,
    val paymentFrequency: Int? = null,
    val memedStatus: String? = null,
    val attendsToOnCall: Boolean? = null,
    val onCallPaymentMethod: OnCallPaymentMethod? = null,
    val onVacationStart: LocalDate? = null,
    val onVacationUntil: LocalDate? = null
)

data class StaffRolesResponse(
    val id: String,
    val name: String,
    val value: String
)

data class StaffTiersResponse(
    val id: String,
    val name: String,
    val value: String
)

data class StaffScoreResponse(
    val id: String,
    val name: String,
    val value: String
)

data class StaffFormResponse(
    val specialties: List<MedicalSpecialty>? = null,
    val providerUnits: List<ProviderUnit>? = null,
    val staffRoles: List<StaffRolesResponse>? = null,
    val staffTiers: List<StaffTiersResponse>? = null,
    val staffScore: List<StaffScoreResponse>? = null,
    val councilTypes: List<CouncilTypeResponse>? = null
)

data class CouncilTypeResponse(
    val id: Int,
    val name: String
)

data class CreateStaffRequest(
    val firstName: String,
    val lastName: String,
    val email: String,
    val nationalId: String? = null,
    val birthdate: LocalDate? = null,
    val gender: Gender,
    val profileImageUrl: String? = null,
    val role: Role,
    val type: StaffType,
    val active: Boolean = true,
    val urlSlug: String? = null,
    val quote: String? = null,
    val profileBio: String? = null,
    val council: CouncilDTO? = null,
    val specialty: UUID? = null,
    val subSpecialties: List<UUID>? = null,
    val internalSpecialty: UUID? = null,
    val internalSubSpecialties: List<UUID>? = null,
    val providerUnits: List<UUID>? = null,
    val qualifications: List<Qualification>? = null,
    val tier: SpecialistTier? = null,
    val theoristTier: SpecialistTier? = null,
    val curiosity: String? = null,
    val showOnApp: Boolean? = null,
    val education: List<String>? = null,
    val healthSpecialistScore: HealthSpecialistScoreEnum? = null,
    val deAccreditationDate: LocalDate? = null,
    val contacts: List<ContactDTO>? = null,
    val paymentFrequency: Int? = null,
    val attendsToOnCall: Boolean? = null,
    val onCallPaymentMethod: OnCallPaymentMethod? = null,
    val onVacationStart: LocalDate? = null,
    val onVacationUntil: LocalDate? = null
)

data class UpdateStaffRequest(
    val firstName: String,
    val lastName: String,
    val email: String,
    val nationalId: String? = null,
    val birthdate: LocalDate? = null,
    val gender: Gender,
    val profileImageUrl: String? = null,
    val role: Role,
    val type: StaffType,
    val active: Boolean = true,
    val urlSlug: String? = null,
    val quote: String? = null,
    val profileBio: String? = null,
    val council: CouncilDTO? = null,
    val specialty: UUID? = null,
    val subSpecialties: List<UUID>? = null,
    val internalSpecialty: UUID? = null,
    val internalSubSpecialties: List<UUID>? = null,
    val providerUnits: List<UUID>? = null,
    val qualifications: List<Qualification>? = null,
    val tier: SpecialistTier? = null,
    val theoristTier: SpecialistTier? = null,
    val curiosity: String? = null,
    val showOnApp: Boolean? = null,
    val education: List<String>? = null,
    val healthSpecialistScore: HealthSpecialistScoreEnum? = null,
    val deAccreditationDate: LocalDate? = null,
    val contacts: List<ContactDTO>? = null,
    val paymentFrequency: Int? = null,
    val attendsToOnCall: Boolean? = null,
    val onCallPaymentMethod: OnCallPaymentMethod? = null,
    val onVacationStart: LocalDate? = null,
    val onVacationUntil: LocalDate? = null,
    val version: Long? = null
)

data class CouncilDTO(
    val number: String,
    val state: State,
    val type: Int? = null
)

data class ContactDTO(
    val id: UUID?,
    val address: AddressDTO? = null,
    val phones: List<PhoneNumber> = emptyList(),
    val scheduleAvailabilityDays: Int? = null,
    val modality: ModalityType,
    val availableDays: List<AvailableDay> = emptyList(),
    val website: String? = null
)

data class AddressDTO(
    val id: UUID? = null,
    val street: String? = null,
    val number: String? = null,
    val complement: String? = null,
    val neighborhood: String? = null,
    val state: String? = null,
    val city: String? = null,
    val zipcode: String? = null,
    val label: String? = null,
    val active: Boolean = true,
    val latitude: String? = null,
    val longitude: String? = null
)
