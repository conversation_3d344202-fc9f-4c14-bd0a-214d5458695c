package br.com.alice.member.api.converters

import M
import br.com.alice.common.Converter
import br.com.alice.common.core.extensions.capitalize
import br.com.alice.common.map
import br.com.alice.data.layer.models.HealthCommunitySpecialist
import br.com.alice.data.layer.models.MedicalSpecialty
import br.com.alice.data.layer.models.PhoneNumber
import br.com.alice.data.layer.models.PhoneType
import br.com.alice.data.layer.models.Provider
import br.com.alice.data.layer.models.ProviderUnit
import br.com.alice.data.layer.models.ProviderUnit.Type.*
import br.com.alice.data.layer.models.StructuredAddress
import br.com.alice.member.api.MobileApp
import br.com.alice.member.api.models.AddressResponse
import br.com.alice.member.api.models.ContactCallOutResponse
import br.com.alice.member.api.models.Link
import br.com.alice.member.api.models.MainActionResponse
import br.com.alice.member.api.models.MobileRouting
import br.com.alice.member.api.models.NavigationResponse
import br.com.alice.member.api.models.PhoneNumberResponse
import br.com.alice.member.api.models.ProviderResponse
import br.com.alice.member.api.models.ProviderUnitResponse
import br.com.alice.member.api.models.QualificationResponse
import br.com.alice.member.api.models.WorkingHoursResponse
import java.time.DayOfWeek
import java.time.format.DateTimeFormatter
import java.time.format.TextStyle
import java.util.Locale
import java.util.UUID

private const val ADDRESS_LABEL = "Principal"

object ProviderResponseConverter
    : Converter<ProviderUnit, ProviderResponse>(ProviderUnit::class, ProviderResponse::class) {
    suspend fun convert(
        source: ProviderUnit,
        provider: Provider,
        specialties: Map<UUID, MedicalSpecialty>,
        actionNeeded: Boolean,
        i18n: suspend (String) -> String
    ) =
        convert(
            source,
            map(ProviderResponse::addresses) from toResponseAddress(source.address),
            map(ProviderResponse::simplifiedAddresses) from toResponseAddress(source.address),
            map(ProviderResponse::phones) from source.phones.map { PhoneNumberResponseConverter.convert(it) },
            map(ProviderResponse::imageUrl) from getProviderImage(source, provider),
            map(ProviderResponse::type) from type(source.type),
            map(ProviderResponse::workingHours) from workingHours(source.workingPeriods, i18n),
            map(ProviderResponse::qualifications) from source.qualifications.map {
                QualificationResponse(
                    it.imageUrl,
                    it.name,
                    it.description
                )
            },
            map(ProviderResponse::crm) from null,
            map(ProviderResponse::curiosity) from null,
            map(ProviderResponse::specialties) from (source.medicalSpecialtyIds?.map {
                specialties[it]?.name ?: ""
            } ?: emptyList()),
            map(ProviderResponse::subSpecialties) from emptyList(),
            map(ProviderResponse::education) from emptyList(),
            map(ProviderResponse::contactCallOut) from getContactCallOut(actionNeeded),
            map(ProviderResponse::mainAction) from getMainAction(actionNeeded)
        )

    fun getContactCallOut(actionNeeded: Boolean) = if (actionNeeded) contactCallOutResponse else null

    fun getMainAction(actionNeeded: Boolean) =
        if (actionNeeded) mainActionResponse else null

    private fun getProviderImage(providerUnit: ProviderUnit, provider: Provider) =
        when {
            providerUnit.imageUrl != null && providerUnit.imageUrl!!.isNotBlank() -> providerUnit.imageUrl
            provider.imageUrl != null && provider.imageUrl!!.isNotBlank() -> provider.imageUrl
            else -> placeholderImage(providerUnit.type)
        }

    private suspend fun workingHours(periods: List<ProviderUnit.WorkingPeriod>, i18n: suspend (String) -> String) =
        WorkingHoursResponse(
            description = if (periods == ProviderUnit.WorkingPeriod.ALWAYS_OPEN) i18n(M.ALWAYS_OPEN) else null,
            alwaysOpen = periods == ProviderUnit.WorkingPeriod.ALWAYS_OPEN,
            weekdayText = workingHoursWeekdayText(periods, i18n)
        )

    private suspend fun workingHoursWeekdayText(
        periods: List<ProviderUnit.WorkingPeriod>,
        i18n: suspend (String) -> String
    ): List<String> {
        val locale = Locale("pt", "BR")
        val formatter = DateTimeFormatter.ofPattern("HH:mm").withLocale(locale)
        if (periods == ProviderUnit.WorkingPeriod.ALWAYS_OPEN) {
            return DayOfWeek.values().map {
                val dayOfWeekText = it.getDisplayName(TextStyle.FULL, locale).capitalize()
                val value = i18n(M.ALWAYS_OPEN_STATUS)
                "$dayOfWeekText: $value"
            }
        }
        val texts = periods.map {
            val openText = it.open.localTime.format(formatter)
            val closeText = it.close?.localTime?.format(formatter)
            it.open.dayOfWeek to "$openText - $closeText"
        }.toMap()
        return DayOfWeek.values().map {
            val dayOfWeekText = it.getDisplayName(TextStyle.FULL, locale)
            val value = texts[it] ?: i18n(M.CLOSED)
            "$dayOfWeekText: $value"
        }
    }

    private fun type(type: ProviderUnit.Type) =
        when (type) {
            EMERGENCY_UNITY -> "emergency" // TODO: Remove this option after app rollout
            else -> type.name.lowercase()
        }

    private fun placeholderImage(type: ProviderUnit.Type): String = when (type) {
        HOSPITAL, HOSPITAL_CHILDREN, MATERNITY -> MobileApp.Assets.Placeholders.HOSPITAL
        LABORATORY -> MobileApp.Assets.Placeholders.LABORATORY
        ALICE_HOUSE -> MobileApp.Assets.Placeholders.ALICE_HOUSE
        EMERGENCY_UNITY, EMERGENCY_UNITY_CHILDREN -> MobileApp.Assets.Placeholders.EMERGENCY_UNITY
        CLINICAL, CLINICAL_COMMUNITY, VACCINE, MEDICAL_COMPANY -> MobileApp.Assets.Placeholders.DOCTOR
    }
}

object PhoneNumberResponseConverter
    : Converter<PhoneNumber, PhoneNumberResponse>(
    PhoneNumber::class,
    PhoneNumberResponse::class
) {
    fun convert(source: PhoneNumber): PhoneNumberResponse {
        val preformattedPhone = source.phone.replace(Regex("\\s|\\(|\\)|-"), "")
        return convert(
            source,
            map(PhoneNumberResponse::title) from if (source.title != null) source.title else "Principal",
            map(PhoneNumberResponse::phoneUrl) from when (source.type) {
                PhoneType.WHATSAPP -> {
                    val formattedPhone = when {
                        preformattedPhone.startsWith("+") -> preformattedPhone.replace("+", "")
                        preformattedPhone.length > 11 -> preformattedPhone
                        else -> "55$preformattedPhone"
                    }
                    "${MobileApp.Links.WHATSAPP_API_URL}$formattedPhone"
                }

                else -> "tel:${preformattedPhone}"
            }
        )
    }
}

object HealthCommunitySpecialistConverter
    :
    Converter<HealthCommunitySpecialist, ProviderResponse>(HealthCommunitySpecialist::class, ProviderResponse::class) {

    fun convert(
        source: HealthCommunitySpecialist,
        specialty: String,
        subSpecialities: List<String>,
        appointmentScheduleOptionUrl: String? = null,
        actionNeeded: Boolean = false,
    ): ProviderResponse {
        return convert(
            source,
            map(ProviderResponse::specialties) from listOf(specialty),
            map(ProviderResponse::subSpecialties) from subSpecialities,
            map(ProviderResponse::addresses) from emptyList(),
            map(ProviderResponse::phones) from source.phones.map { PhoneNumberResponseConverter.convert(it) },
            map(ProviderResponse::imageUrl) from (if (source.imageUrl.isNullOrEmpty()) MobileApp.Assets.Placeholders.DOCTOR else source.imageUrl),
            map(ProviderResponse::type) from "doctor",
            map(ProviderResponse::crm) from source.council?.number,
            map(ProviderResponse::cnpj) from "",
            map(ProviderResponse::qualifications) from source.qualifications.map {
                QualificationResponse(
                    it.imageUrl,
                    it.name,
                    it.description
                )
            },
            map(ProviderResponse::schedulingUrl) from appointmentScheduleOptionUrl,
            map(ProviderResponse::contactCallOut) from ProviderResponseConverter.getContactCallOut(actionNeeded),
            map(ProviderResponse::mainAction) from ProviderResponseConverter.getMainAction(actionNeeded)
        )
    }
}

object ProviderUnitClinicConverter :
    Converter<ProviderUnit, ProviderResponse>(ProviderUnit::class, ProviderResponse::class) {
    fun convert(
        source: ProviderUnit,
        specialty: String,
        subSpecialties: List<String>,
    ): ProviderResponse {
        return convert(
            source,
            map(ProviderResponse::specialties) from listOf(specialty),
            map(ProviderResponse::subSpecialties) from subSpecialties,
            map(ProviderResponse::addresses) from toResponseAddress(source.address),
            map(ProviderResponse::simplifiedAddresses) from toResponseAddress(source.address),
            map(ProviderResponse::phones) from source.phones.map { PhoneNumberResponseConverter.convert(it) },
            map(ProviderResponse::imageUrl) from (if (source.imageUrl.isNullOrEmpty()) MobileApp.Assets.Placeholders.DOCTOR else source.imageUrl),
            map(ProviderResponse::type) from "doctor",
            map(ProviderResponse::qualifications) from source.qualifications.map {
                QualificationResponse(
                    it.imageUrl,
                    it.name,
                    it.description
                )
            },
            map(ProviderResponse::crm) from null,
            map(ProviderResponse::curiosity) from null,
            map(ProviderResponse::subSpecialties) from emptyList(),
            map(ProviderResponse::education) from emptyList()
        )
    }
}

private fun toResponseAddress(address: StructuredAddress?): List<AddressResponse> =
    address?.formattedAddress()?.let {
        listOf(AddressResponse(ADDRESS_LABEL, it))
    } ?: emptyList()

object ProviderUnitResponseConverter : Converter<ProviderUnit, ProviderUnitResponse>(
    ProviderUnit::class,
    ProviderUnitResponse::class
) {
    fun convert(source: ProviderUnit): ProviderUnitResponse {
        return convert(
            source,
            map(ProviderUnitResponse::address) from source.address?.formattedAddress().orEmpty(),
        )
    }
}

val mainActionResponse = MainActionResponse(
    label = "Falar com Alice Agora",
    navigation = NavigationResponse(
        mobileRoute = MobileRouting.EXTERNAL_APP,
        link = Link(
            // temporary deep link fix for Alice Agora redirection
            href = "https://alicesaude.page.link/?link=https://www.alice.com.br/alice_agora&apn=com.alicesaude&isi=**********&ibi=br.com.alice.tech.enduser",
            rel = "alice_agora"
        )
    )
)
val contactCallOutResponse = ContactCallOutResponse(
    title = "É necessário o encaminhamento",
    body = "Para agendar com essa especialidade fale com Alice Agora.",
    variant = ContactCallOutResponse.Variant.WARNING
)
