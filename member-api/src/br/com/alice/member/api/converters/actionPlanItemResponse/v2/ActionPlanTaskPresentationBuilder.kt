package br.com.alice.member.api.converters.actionPlanItemResponse.v2

import br.com.alice.app.content.model.RemoteActionMethod
import br.com.alice.app.content.model.ScreenType
import br.com.alice.common.core.StaffType
import br.com.alice.common.core.extensions.isNotNullOrEmpty
import br.com.alice.common.core.extensions.toBrazilianDateFormat
import br.com.alice.common.logging.logger
import br.com.alice.common.service.data.dsl.SortOrder
import br.com.alice.coverage.client.City
import br.com.alice.data.layer.models.ActionPlanTask
import br.com.alice.data.layer.models.ActionPlanTaskStatus
import br.com.alice.data.layer.models.ActionPlanTaskType
import br.com.alice.data.layer.models.AppointmentSchedule
import br.com.alice.data.layer.models.AppointmentScheduleStatus
import br.com.alice.data.layer.models.EmergencyNew
import br.com.alice.data.layer.models.FeatureNamespace
import br.com.alice.data.layer.models.FollowUpRequestNew
import br.com.alice.data.layer.models.GenericTaskNew
import br.com.alice.data.layer.models.PrescriptionNew
import br.com.alice.data.layer.models.QuestionnaireNew
import br.com.alice.data.layer.models.ReferralNew
import br.com.alice.data.layer.models.SurgeryPrescriptionNew
import br.com.alice.data.layer.models.TestRequestNew
import br.com.alice.featureconfig.core.FeatureService
import br.com.alice.member.api.ServiceConfig
import br.com.alice.member.api.converters.ActionPlanTaskScheduleTimelineUtils
import br.com.alice.member.api.converters.actionPlanItemResponse.ActionPlanPresentationConstants
import br.com.alice.member.api.converters.actionPlanItemResponse.ActionPlanPresentationConstants.DELETE_BUTTON_INFO
import br.com.alice.member.api.converters.actionPlanItemResponse.ActionPlanPresentationConstants.FINISH_BUTTON_GENERIC_INFO
import br.com.alice.member.api.converters.actionPlanItemResponse.ActionPlanPresentationConstants.FINISH_BUTTON_REFERRAL_INFO
import br.com.alice.member.api.converters.actionPlanItemResponse.ActionPlanPresentationConstants.FINISH_BUTTON_TEST_REQUEST_INFO
import br.com.alice.member.api.converters.actionPlanItemResponse.ActionPlanPresentationConstants.NOT_SHOW_BUTTON
import br.com.alice.member.api.converters.actionPlanItemResponse.ActionPlanPresentationConstants.PRESCRIPTION_NAVIGATION_ICON
import br.com.alice.member.api.converters.actionPlanItemResponse.ActionPlanPresentationConstants.PRESCRIPTION_NAVIGATION_IMAGE
import br.com.alice.member.api.converters.actionPlanItemResponse.ActionPlanPresentationConstants.PRESCRIPTION_NAVIGATION_NAME
import br.com.alice.member.api.converters.actionPlanItemResponse.ActionPlanPresentationConstants.WARN_SCHEDULE_NAVIGATION_DESCRIPTION
import br.com.alice.member.api.converters.actionPlanItemResponse.ActionPlanPresentationConstants.WARN_SCHEDULE_NAVIGATION_ICON
import br.com.alice.member.api.converters.actionPlanItemResponse.ActionPlanPresentationConstants.WARN_SCHEDULE_NAVIGATION_IMAGE
import br.com.alice.member.api.converters.actionPlanItemResponse.ActionPlanPresentationConstants.WARN_SCHEDULE_NAVIGATION_NAME
import br.com.alice.member.api.extensions.toHealthPlanDrawer
import br.com.alice.member.api.extensions.toItemAttachment
import br.com.alice.member.api.models.HealthPlanActionButton
import br.com.alice.member.api.models.HealthPlanItemDrawer.Companion.aliceAgoraDrawer
import br.com.alice.member.api.models.HealthPlanItemDrawer.Companion.preparationsDrawer
import br.com.alice.member.api.models.HealthPlanItemDrawer.Companion.prescriptionDrawer
import br.com.alice.member.api.models.HealthPlanItemDrawer.Companion.promotionDrawer
import br.com.alice.member.api.models.HealthPlanItemDrawer.Companion.renewDrawer
import br.com.alice.member.api.models.HealthPlanItemNavigation
import br.com.alice.member.api.models.HealthPlanItemStatusTag
import br.com.alice.member.api.models.HealthPlanPresentationFields
import br.com.alice.member.api.models.Link
import br.com.alice.member.api.models.MobileRouting
import br.com.alice.member.api.models.NavigationResponse
import br.com.alice.member.api.models.ScheduleInfo
import br.com.alice.member.api.models.TestPreparationTransport
import br.com.alice.member.api.models.procedureAuthorization.ActionPlanTaskAuthorization
import br.com.alice.member.api.models.v2.ActionNavigation.Companion.emergencyAction
import br.com.alice.member.api.models.v2.ActionNavigation.Companion.emergencyIsDoneAction
import br.com.alice.member.api.models.v2.ActionNavigation.Companion.followUpAction
import br.com.alice.member.api.models.v2.ActionNavigation.Companion.genericIsDoneAction
import br.com.alice.member.api.models.v2.ActionNavigation.Companion.prescriptionIsDoneAction
import br.com.alice.member.api.models.v2.ActionNavigation.Companion.questionnaireAction
import br.com.alice.member.api.models.v2.ActionNavigation.Companion.referralAlreadyScheduledAction
import br.com.alice.member.api.models.v2.ActionNavigation.Companion.referralScheduleAction
import br.com.alice.member.api.models.v2.ActionNavigation.Companion.referralScheduleIsDoneAction
import br.com.alice.member.api.models.v2.ActionNavigation.Companion.testRequestAction
import br.com.alice.member.api.models.v2.ActionNavigation.Companion.testRequestIsDoneAction
import br.com.alice.member.api.models.v2.ActionNavigation.Companion.testRequestIsDoneMultipleAction
import br.com.alice.member.api.models.v2.ActionNavigation.Companion.testRequestMultipleAction
import br.com.alice.member.api.models.v2.ActionNavigationConfirmationModal
import br.com.alice.member.api.models.v2.HealthPlanItemAlert
import br.com.alice.member.api.models.v2.HealthPlanItemLink
import br.com.alice.provider.client.MedicalSpecialtyService
import br.com.alice.schedule.client.AppointmentScheduleFilter
import br.com.alice.schedule.client.AppointmentScheduleService
import br.com.alice.staff.client.StaffService
import br.com.alice.wanda.client.AppointmentCoordinationService
import com.github.kittinunf.result.map
import java.time.LocalDateTime
import java.time.format.DateTimeFormatter
import kotlinx.coroutines.async
import kotlinx.coroutines.coroutineScope
import java.util.UUID

val copayScreenNavigation = NavigationResponse(
    mobileRoute = MobileRouting.CHESHIRE_SCREEN,
    properties = mapOf(
        "action" to mapOf(
            "method" to RemoteActionMethod.GET.toString(),
            "endpoint" to ServiceConfig.url("/app_content/screen/${ScreenType.COPAY_MENU}")
        )
    )
)

val taskOverdueAlert = HealthPlanItemAlert(
    id = "overdue_copay_alert",
    title = "Agende antes do prazo expirar",
    description = "Assim, você não paga coparticipação e não precisa de um novo encaminhamento.",
    illustration = "https://alice-member-app-assets.s3.amazonaws.com/health_plan_tasks/v2/app_touch.svg",
    navigation = copayScreenNavigation
)

val taskExpiredAlert = HealthPlanItemAlert(
    id = "expired_copay_alert",
    title = "Agende uma reavaliação",
    description = "Ao agendar sem um novo encaminhamento, você paga uma taxa de coparticipação.",
    illustration = "https://alice-member-app-assets.s3.amazonaws.com/health_plan_tasks/v2/alert.svg",
    navigation = copayScreenNavigation
)

val copayLink = HealthPlanItemLink(
    icon = "money",
    label = "Evite pagar Coparticipação",
    navigation = copayScreenNavigation
)

class ActionPlanTaskPresentationBuilder(
    private val appointmentScheduleService: AppointmentScheduleService,
    private val medicalSpecialtyService: MedicalSpecialtyService,
    private val appointmentCoordinationService: AppointmentCoordinationService,
    private val staffService: StaffService
) {
    companion object {
        val allowScheduleStatus = listOf(
            ActionPlanTaskStatus.SCHEDULED,
            ActionPlanTaskStatus.ACTIVE,
            ActionPlanTaskStatus.ACTIVE_ON_GOING,
            ActionPlanTaskStatus.OVERDUE,
        )
    }

    suspend fun getPresentationFields(
        task: ReferralNew,
        isUncoordinatedCoPayment: Boolean,
        isRedesignHealthPlanDetailsEnabled: Boolean,
    ): HealthPlanPresentationFields = coroutineScope {
        val taskScheduleCompleteDeferred = async {
            appointmentScheduleService.findBy(
                AppointmentScheduleFilter(
                    healthPlanTaskId = task.id,
                    status = listOf(
                        AppointmentScheduleStatus.SCHEDULED
                    ),
                    sortOrder = SortOrder.Ascending
                )
            ).get()
        }
        val taskCompletedCoordinatedAppointmentsDeferred = async {
            appointmentCoordinationService.getByTaskId(task.personId, task.id).get()
        }
        val isSecondaryAttentionLevel = isSecondaryAttentionLevel(task, true)
        val isTherapy = task.specialty?.id?.let { isTherapy(it) } ?: false

        val taskScheduleComplete = taskScheduleCompleteDeferred.await()
        val taskCompletedCoordinatedAppointments = taskCompletedCoordinatedAppointmentsDeferred.await()
        val sessionCompleteCount = taskScheduleComplete.size + taskCompletedCoordinatedAppointments.size
        val hideScheduleOption = getHideScheduleOptions(task, sessionCompleteCount)
        val isAdvancedAccess = task.isAdvancedAccess ?: false

        val showAppointmentScheduleDetails =
            isRedesignHealthPlanDetailsEnabled && !isSecondaryAttentionLevel && !isTherapy && hasOnlyOneSessionAvailable(
                task
            ) && task.isScheduled()

        logger.info(
            "ActionPlanTaskPresentationBuilder getting presentation fields for referral",
            "task_id" to task.id,
            "session_complete_count" to sessionCompleteCount,
            "is_secondary_attention_level" to isSecondaryAttentionLevel,
            "is_therapy" to isTherapy,
            "is_uncoordinated_co_payment" to isUncoordinatedCoPayment,
            "task_schedule_complete_count" to taskScheduleComplete.size,
            "task_completed_coordinated_appointment_count" to taskCompletedCoordinatedAppointments.size,
            "hide_schedule_option" to hideScheduleOption,
            "show_appointment_schedule_details" to showAppointmentScheduleDetails,
            "is_advanced_access" to isAdvancedAccess,
        )

        HealthPlanPresentationFields(
            finishTask = getFinishTaskButton(task, isRedesignHealthPlanDetailsEnabled),
            deleteTask = getDeleteTaskButton(task, isRedesignHealthPlanDetailsEnabled),
            navigations = takeIf { isSecondaryAttentionLevel }?.let {
                getTestRequestAndReferralNavigation(
                    task,
                    isRedesignHealthPlanDetailsEnabled
                )
            },
            scheduleInfo = takeIf { !showAppointmentScheduleDetails }?.let { getScheduleInfo(task) },
            hideScheduleOptions = hideScheduleOption,
            recurrentScheduleInfo = getRecurrentScheduleInfo(task, taskScheduleComplete),
            alert = takeIf { isSecondaryAttentionLevel && isUncoordinatedCoPayment }?.let {
                getTaskDetailsAlert(task, isRedesignHealthPlanDetailsEnabled)
            },
            copayInfo = takeIf { isSecondaryAttentionLevel && isUncoordinatedCoPayment }?.let {
                getCopayLink(task.task, isRedesignHealthPlanDetailsEnabled)
            },
            expireDetails = takeIf {
                shouldShowReferralExpireDetails(
                    isRedesignHealthPlanDetailsEnabled,
                    isSecondaryAttentionLevel,
                    isTherapy,
                    isAdvancedAccess,
                    task
                )
            }?.let {
                task.expirationDetails("Agendar")
            },
            drawers = getDrawers(task, isRedesignHealthPlanDetailsEnabled),
            canArchive = shouldLetArchive(isRedesignHealthPlanDetailsEnabled, task),
            archiveConfirmationModal = ActionNavigationConfirmationModal.archiveConfirmation(),
            mainActionNavigation = takeIf { !hideScheduleOption }?.let { referralScheduleAction(task, isTherapy) },
            secondaryActionNavigation = referralSecondaryAction(
                isTherapy,
                isRedesignHealthPlanDetailsEnabled,
                task,
                showAppointmentScheduleDetails,
            ),
            tertiaryActionNavigation = referralTertiaryAction(
                isTherapy,
                hideScheduleOption,
                isRedesignHealthPlanDetailsEnabled,
                isSecondaryAttentionLevel,
                task,
                showAppointmentScheduleDetails,
            ),
            canAskHealthTeam = canAskHealthTeam(isRedesignHealthPlanDetailsEnabled),
            attachments = getAttachments(task, isRedesignHealthPlanDetailsEnabled),
            statusTag = getStatusTag(task, isRedesignHealthPlanDetailsEnabled, isTherapy),
            appointmentScheduleId = takeIf { showAppointmentScheduleDetails }?.let { task.appointmentScheduleId?.toString() },
            canEditAppointmentSchedule = getCanEditAppointmentSchedule(task.status)
        )
    }

    private fun shouldShowReferralExpireDetails(
        isRedesignHealthPlanDetailsEnabled: Boolean,
        isSecondaryAttentionLevel: Boolean,
        isTherapy: Boolean,
        isAdvancedAccess: Boolean,
        task: ReferralNew
    ) =
        isRedesignHealthPlanDetailsEnabled && (isSecondaryAttentionLevel || isTherapy || isAdvancedAccess) && !task.isScheduled()

    suspend fun getPresentationFields(
        task: FollowUpRequestNew,
        isRedesignHealthPlanDetailsEnabled: Boolean
    ): HealthPlanPresentationFields {

        val hideScheduleOption = !task.canChangeStatusToSchedule()

        val showAppointmentScheduleDetails = isRedesignHealthPlanDetailsEnabled && task.isScheduled()

        return HealthPlanPresentationFields(
            finishTask = getFinishTaskButton(task, isRedesignHealthPlanDetailsEnabled),
            scheduleInfo = takeIf { !showAppointmentScheduleDetails }?.let {
                getUniqueSessionScheduleInfo(task)
            },
            hideScheduleOptions = hideScheduleOption,
            expireDetails = takeIf { isRedesignHealthPlanDetailsEnabled && !task.isScheduled() }?.let {
                task.expirationDetails("Agendar")
            },
            drawers = getDrawers(task, isRedesignHealthPlanDetailsEnabled),
            canArchive = shouldLetArchive(isRedesignHealthPlanDetailsEnabled, task),
            archiveConfirmationModal = ActionNavigationConfirmationModal.archiveConfirmation(),
            mainActionNavigation = takeIf { !hideScheduleOption }?.let {
                followUpAction(task)
            },
            canAskHealthTeam = canAskHealthTeam(isRedesignHealthPlanDetailsEnabled),
            attachments = getAttachments(task, isRedesignHealthPlanDetailsEnabled),
            statusTag = getStatusTag(task, isRedesignHealthPlanDetailsEnabled),
            appointmentScheduleId = takeIf { showAppointmentScheduleDetails }?.let { task.appointmentScheduleId?.toString() },
            canEditAppointmentSchedule = getCanEditAppointmentSchedule(task.status),
        )
    }

    suspend fun getPresentationFields(
        task: TestRequestNew,
        city: City?,
        isUncoordinatedCoPayment: Boolean,
        isRedesignHealthPlanDetailsEnabled: Boolean,
        preparations: List<TestPreparationTransport>,
        authorization: ActionPlanTaskAuthorization?,
    ): HealthPlanPresentationFields {
        val hideScheduleOptions = getHideScheduleOptions(task, city)

        return HealthPlanPresentationFields(
            finishTask = getFinishTaskButton(task, isRedesignHealthPlanDetailsEnabled),
            deleteTask = getDeleteTaskButton(task, isRedesignHealthPlanDetailsEnabled),
            navigations = getTestRequestAndReferralNavigation(task, isRedesignHealthPlanDetailsEnabled),
            scheduleInfo = getUniqueSessionScheduleInfo(task),
            hideScheduleOptions = hideScheduleOptions,
            alert = takeIf { isUncoordinatedCoPayment }?.let {
                getTaskDetailsAlert(
                    task,
                    isRedesignHealthPlanDetailsEnabled
                )
            },
            copayInfo = takeIf { isUncoordinatedCoPayment }?.let {
                getCopayLink(
                    task,
                    isRedesignHealthPlanDetailsEnabled
                )
            },
            expireDetails = takeIf { isRedesignHealthPlanDetailsEnabled && !task.isScheduled() }?.let {
                task.expirationDetails("Agendar")
            },
            drawers = getDrawers(task, isRedesignHealthPlanDetailsEnabled, preparations),
            canArchive = shouldLetArchive(isRedesignHealthPlanDetailsEnabled, task),
            archiveConfirmationModal = ActionNavigationConfirmationModal.archiveConfirmation(),
            mainActionNavigation = takeIf { !hideScheduleOptions }?.let { testRequestAction(authorization, task) },
            tertiaryActionNavigation = takeIf { isRedesignHealthPlanDetailsEnabled }?.let { testRequestIsDoneAction(task) },
            canAskHealthTeam = canAskHealthTeam(isRedesignHealthPlanDetailsEnabled),
            attachments = getAttachments(task, isRedesignHealthPlanDetailsEnabled),
            statusTag = getStatusTag(task, isRedesignHealthPlanDetailsEnabled),
            warning = getTestRequestWarning(task, isRedesignHealthPlanDetailsEnabled)
        )
    }

    suspend fun getPresentationFields(
        tasks: List<TestRequestNew>,
        city: City?,
        isUncoordinatedCoPayment: Boolean,
        isRedesignHealthPlanDetailsEnabled: Boolean,
        preparations: List<TestPreparationTransport>
    ): HealthPlanPresentationFields {
        val hideScheduleOptions = getGroupedTestRequestHideScheduleOptions(tasks, city)

        return HealthPlanPresentationFields(
            finishTask = getGroupedTestRequestFinishTaskButton(tasks, isRedesignHealthPlanDetailsEnabled),
            deleteTask = getGroupedTestRequestDeleteTaskButton(tasks, isRedesignHealthPlanDetailsEnabled),
            navigations = getGroupedTestRequestNavigation(tasks, isRedesignHealthPlanDetailsEnabled),
            scheduleInfo = getGroupedTestRequestUniqueSessionScheduleInfo(tasks),
            hideScheduleOptions = hideScheduleOptions,
            alert = takeIf { isUncoordinatedCoPayment }?.let {
                getTaskDetailsAlert(
                    tasks,
                    isRedesignHealthPlanDetailsEnabled
                )
            },
            copayInfo = takeIf { isUncoordinatedCoPayment }?.let {
                getCopayLink(
                    tasks,
                    isRedesignHealthPlanDetailsEnabled
                )
            },
            expireDetails = takeIf { isRedesignHealthPlanDetailsEnabled && !tasks.first().isScheduled() }?.let {
                getGroupedTestRequestExpireDetails(tasks)
            },
            drawers = getDrawers(tasks, isRedesignHealthPlanDetailsEnabled, preparations),
            canArchive = shouldLetArchive(isRedesignHealthPlanDetailsEnabled, tasks),
            archiveConfirmationModal = ActionNavigationConfirmationModal.archiveConfirmation(),
            mainActionNavigation = takeIf { !hideScheduleOptions }?.let { testRequestMultipleAction(tasks) },
            tertiaryActionNavigation = takeIf { isRedesignHealthPlanDetailsEnabled }?.let {
                testRequestIsDoneMultipleAction(tasks)
            },
            canAskHealthTeam = canAskHealthTeam(isRedesignHealthPlanDetailsEnabled),
            attachments = getAttachments(tasks, isRedesignHealthPlanDetailsEnabled),
            statusTag = getStatusTag(tasks.first(), isRedesignHealthPlanDetailsEnabled),
            warning = getTestRequestWarning(tasks.first(), isRedesignHealthPlanDetailsEnabled)
        )
    }

    suspend fun getPresentationFields(task: GenericTaskNew, isRedesignHealthPlanDetailsEnabled: Boolean) =
        HealthPlanPresentationFields(
            finishTask = getFinishTaskButton(task, isRedesignHealthPlanDetailsEnabled),
            deleteTask = getDeleteTaskButton(task, isRedesignHealthPlanDetailsEnabled),
            drawers = getDrawers(task, isRedesignHealthPlanDetailsEnabled),
            navigations = null,
            canArchive = shouldLetArchive(isRedesignHealthPlanDetailsEnabled, task),
            archiveConfirmationModal = ActionNavigationConfirmationModal.archiveConfirmation(),
            mainActionNavigation = takeIf { isRedesignHealthPlanDetailsEnabled }?.let { genericIsDoneAction(task) },
            canAskHealthTeam = canAskHealthTeam(isRedesignHealthPlanDetailsEnabled),
            attachments = getAttachments(task, isRedesignHealthPlanDetailsEnabled),
            statusTag = getStatusTag(task, isRedesignHealthPlanDetailsEnabled)
        )

    suspend fun getPresentationFields(
        task: EmergencyNew,
        isRedesignHealthPlanDetailsEnabled: Boolean,
    ) = HealthPlanPresentationFields(
        finishTask = getFinishTaskButton(task, isRedesignHealthPlanDetailsEnabled),
        deleteTask = getDeleteTaskButton(task, isRedesignHealthPlanDetailsEnabled),
        navigations = null,
        expireDetails = takeIf { isRedesignHealthPlanDetailsEnabled }?.let {
            task.expirationDetails("Válida")
        },
        drawers = getDrawers(task, isRedesignHealthPlanDetailsEnabled),
        canArchive = shouldLetArchive(isRedesignHealthPlanDetailsEnabled, task),
        archiveConfirmationModal = ActionNavigationConfirmationModal.archiveConfirmation(),
        mainActionNavigation = emergencyAction(task),
        secondaryActionNavigation = takeIf { isRedesignHealthPlanDetailsEnabled }?.let { emergencyIsDoneAction(task) },
        canAskHealthTeam = canAskHealthTeam(isRedesignHealthPlanDetailsEnabled),
        attachments = getAttachments(task, isRedesignHealthPlanDetailsEnabled),
        statusTag = getStatusTag(task, isRedesignHealthPlanDetailsEnabled)
    )

    suspend fun getPresentationFields(
        task: PrescriptionNew,
        isRedesignHealthPlanDetailsEnabled: Boolean
    ) = HealthPlanPresentationFields(
        finishTask = getFinishTaskButton(task, isRedesignHealthPlanDetailsEnabled),
        deleteTask = getDeleteTaskButton(task, isRedesignHealthPlanDetailsEnabled),
        navigations = getPrescriptionNavigation(task, isRedesignHealthPlanDetailsEnabled),
        expireDetails = null,
        drawers = getDrawers(
            task,
            isRedesignHealthPlanDetailsEnabled,
            showPrescriptionDrawer = true,
            showRenewDrawer = true,
            showPromotionDrawer = true
        ),
        canArchive = shouldLetArchive(isRedesignHealthPlanDetailsEnabled, task),
        archiveConfirmationModal = ActionNavigationConfirmationModal.archiveConfirmation(),
        mainActionNavigation = null,
        secondaryActionNavigation = takeIf { isRedesignHealthPlanDetailsEnabled }?.let { prescriptionIsDoneAction(task) },
        canAskHealthTeam = canAskHealthTeam(isRedesignHealthPlanDetailsEnabled),
        attachments = getAttachments(task, isRedesignHealthPlanDetailsEnabled),
        statusTag = getStatusTag(task, isRedesignHealthPlanDetailsEnabled),
        warning = getPrescriptionWarning(task, isRedesignHealthPlanDetailsEnabled)
    )

    suspend fun getPresentationFields(task: SurgeryPrescriptionNew, isRedesignHealthPlanDetailsEnabled: Boolean) =
        HealthPlanPresentationFields(
            finishTask = getFinishTaskButton(task, isRedesignHealthPlanDetailsEnabled),
            deleteTask = getDeleteTaskButton(task, isRedesignHealthPlanDetailsEnabled),
            hideScheduleOptions = true,
            drawers = getDrawers(task, isRedesignHealthPlanDetailsEnabled),
            canArchive = shouldLetArchive(isRedesignHealthPlanDetailsEnabled, task),
            archiveConfirmationModal = ActionNavigationConfirmationModal.archiveConfirmation(),
            mainActionNavigation = null,
            canAskHealthTeam = canAskHealthTeam(isRedesignHealthPlanDetailsEnabled),
            attachments = getAttachments(task, isRedesignHealthPlanDetailsEnabled),
            statusTag = getStatusTag(task, isRedesignHealthPlanDetailsEnabled),
        )

    fun getPresentationFields(task: QuestionnaireNew, isRedesignHealthPlanDetailsEnabled: Boolean) =
        HealthPlanPresentationFields(
            finishTask = null,
            deleteTask = null,
            hideScheduleOptions = false,
            drawers = getDrawers(task, isRedesignHealthPlanDetailsEnabled),
            canArchive = shouldLetArchive(isRedesignHealthPlanDetailsEnabled, task),
            archiveConfirmationModal = ActionNavigationConfirmationModal.archiveConfirmation(),
            mainActionNavigation = questionnaireAction(task),
            canAskHealthTeam = canAskHealthTeam(isRedesignHealthPlanDetailsEnabled),
            attachments = getAttachments(task, isRedesignHealthPlanDetailsEnabled),
            statusTag = getStatusTag(task, isRedesignHealthPlanDetailsEnabled),
        )

    private fun getCanEditAppointmentSchedule(status: ActionPlanTaskStatus) = when (status) {
        ActionPlanTaskStatus.OVERDUE,
        ActionPlanTaskStatus.SCHEDULED,
        ActionPlanTaskStatus.ACTIVE,
        ActionPlanTaskStatus.ACTIVE_ON_GOING -> true

        else -> false
    }

    private fun getPrescriptionWarning(task: PrescriptionNew, redesignHealthPlanDetailsEnabled: Boolean) =
        takeIf { redesignHealthPlanDetailsEnabled && task.isExpired() }?.let {
            "Essa tarefa expirou porque a validade da receita venceu."
        }

    private fun getTestRequestWarning(task: TestRequestNew, redesignHealthPlanDetailsEnabled: Boolean) =
        takeIf { redesignHealthPlanDetailsEnabled && task.isExpired() }?.let {
            "Essa tarefa expirou porque passou a data de agendamento do exame."
        }

    private fun getStatusTag(
        task: ActionPlanTask,
        redesignHealthPlanDetailsEnabled: Boolean,
        isTherapy: Boolean = false
    ) =
        takeIf { redesignHealthPlanDetailsEnabled }?.let {
            when (task.status) {
                ActionPlanTaskStatus.SCHEDULED -> getScheduledTag(isTherapy)

                ActionPlanTaskStatus.ACTIVE_ON_GOING -> getActiveOnGoingTag(task)

                ActionPlanTaskStatus.OVERDUE,
                ActionPlanTaskStatus.ACTIVE -> getActiveTag(task)

                ActionPlanTaskStatus.DONE -> HealthPlanItemStatusTag.doneTag()

                ActionPlanTaskStatus.DELETED,
                ActionPlanTaskStatus.DELETED_BY_MEMBER,
                ActionPlanTaskStatus.DELETED_BY_STAFF -> HealthPlanItemStatusTag.archivedTag()

                ActionPlanTaskStatus.EXPIRED -> HealthPlanItemStatusTag.expiredTag()
            }
        }

    private fun getActiveOnGoingTag(task: ActionPlanTask) =
        if (task is SurgeryPrescriptionNew) HealthPlanItemStatusTag.ongoingTag() else HealthPlanItemStatusTag.openTag()

    private fun getScheduledTag(isTherapy: Boolean) =
        if (isTherapy) HealthPlanItemStatusTag.onTreatmentTag() else HealthPlanItemStatusTag.scheduledTag()

    private fun getActiveTag(task: ActionPlanTask) =
        if (task is SurgeryPrescriptionNew) HealthPlanItemStatusTag.ongoingTag() else HealthPlanItemStatusTag.openTag()

    private fun canAskHealthTeam(isRedesignHealthPlanDetailsEnabled: Boolean) = !isRedesignHealthPlanDetailsEnabled

    private fun getAttachments(task: ActionPlanTask, isRedesignHealthPlanDetailsEnabled: Boolean) =
        takeIf { !isRedesignHealthPlanDetailsEnabled }?.let { task.attachments.toItemAttachment() }

    private fun getAttachments(tasks: List<ActionPlanTask>, isRedesignHealthPlanDetailsEnabled: Boolean) =
        takeIf { !isRedesignHealthPlanDetailsEnabled }?.let {
            tasks.map { it.attachments }
                .flatten()
                .distinctBy { it.id }
                .toItemAttachment()
        }

    private fun getDrawers(
        task: ActionPlanTask,
        isRedesignHealthPlanDetailsEnabled: Boolean,
        preparations: List<TestPreparationTransport>? = null,
        showPrescriptionDrawer: Boolean = false,
        showRenewDrawer: Boolean = false,
        showPromotionDrawer: Boolean = false,
    ) = getDrawers(
        listOf(task),
        isRedesignHealthPlanDetailsEnabled,
        preparations,
        showPrescriptionDrawer,
        showRenewDrawer,
        showPromotionDrawer
    )

    private fun getDrawers(
        tasks: List<ActionPlanTask>,
        isRedesignHealthPlanDetailsEnabled: Boolean,
        preparations: List<TestPreparationTransport>? = null,
        showPrescriptionDrawer: Boolean = false,
        showRenewDrawer: Boolean = false,
        showPromotionDrawer: Boolean = false,
    ) =
        takeIf { isRedesignHealthPlanDetailsEnabled }?.let {
            val attachmentDrawers = tasks.map { it.attachments }
                .flatten()
                .distinctBy { it.id }
                .toHealthPlanDrawer()

            tasks.firstOrNull()?.let { task ->
                attachmentDrawers +
                        listOfNotNull(
                            takeIf { preparations?.isNotEmpty() == true }?.let { preparationsDrawer(preparations!!) },
                            takeIf {
                                showPrescriptionDrawer && task.type == ActionPlanTaskType.PRESCRIPTION
                            }?.let { prescriptionDrawer(task as PrescriptionNew) },
                            takeIf { showRenewDrawer }?.let { renewDrawer(getPrescriptionRootNodeId()) },
                            takeIf { showPromotionDrawer }?.let { promotionDrawer() },
                            aliceAgoraDrawer(),
                        )
            }
        }

    private fun referralTertiaryAction(
        isTherapy: Boolean,
        hideScheduleOption: Boolean,
        isRedesignHealthPlanDetailsEnabled: Boolean,
        isSecondaryAttentionLevel: Boolean,
        task: ReferralNew,
        showAppointmentScheduleDetails: Boolean,
    ) = if (isTherapy)
        scheduleIsDoneAction(
            isRedesignHealthPlanDetailsEnabled,
            task,
            isTherapy = true,
            showAppointmentScheduleDetails = showAppointmentScheduleDetails
        )
    else
        alreadyScheduleAction(
            isRedesignHealthPlanDetailsEnabled,
            hideScheduleOption,
            isSecondaryAttentionLevel,
            task
        )

    private fun referralSecondaryAction(
        isTherapy: Boolean,
        isRedesignHealthPlanDetailsEnabled: Boolean,
        task: ReferralNew,
        showAppointmentScheduleDetails: Boolean,
    ) =
        takeIf { !isTherapy && task.isScheduled() }?.let {
            scheduleIsDoneAction(
                isRedesignHealthPlanDetailsEnabled,
                task,
                isTherapy = false,
                showAppointmentScheduleDetails = showAppointmentScheduleDetails
            )
        }

    private fun alreadyScheduleAction(
        isRedesignHealthPlanDetailsEnabled: Boolean,
        hideScheduleOption: Boolean,
        isSecondaryAttentionLevel: Boolean,
        task: ReferralNew
    ) =
        takeIf { isRedesignHealthPlanDetailsEnabled && !hideScheduleOption && isSecondaryAttentionLevel }?.let {
            referralAlreadyScheduledAction(task)
        }

    private fun scheduleIsDoneAction(
        isRedesignHealthPlanDetailsEnabled: Boolean,
        task: ReferralNew,
        isTherapy: Boolean,
        showAppointmentScheduleDetails: Boolean,
    ) =
        takeIf { isRedesignHealthPlanDetailsEnabled && !showAppointmentScheduleDetails }?.let {
            referralScheduleIsDoneAction(task, isTherapy)
        }

    private suspend fun getTestRequestAndReferralNavigation(
        task: ActionPlanTask,
        isRedesignHealthPlanDetailsEnabled: Boolean
    ): List<HealthPlanItemNavigation>? =
        takeIf { !isRedesignHealthPlanDetailsEnabled }?.let {
            when (task.status) {
                ActionPlanTaskStatus.ACTIVE,
                ActionPlanTaskStatus.ACTIVE_ON_GOING -> checkIfShouldWarnExternalSchedule(task)

                ActionPlanTaskStatus.SCHEDULED -> checkIfThereIsAppointment(task)
                else -> null
            }
        }

    private fun getPrescriptionNavigation(
        prescription: PrescriptionNew,
        isRedesignHealthPlanDetailsEnabled: Boolean
    ) =
        takeIf { !isRedesignHealthPlanDetailsEnabled && prescription.hasDigitalPrescription() }?.let {
            listOf(
                HealthPlanItemNavigation(
                    name = PRESCRIPTION_NAVIGATION_NAME,
                    description = ActionPlanPresentationConstants.PRESCRIPTION_NAVIGATION_DESCRIPTION,
                    imageUrl = PRESCRIPTION_NAVIGATION_IMAGE,
                    icon = PRESCRIPTION_NAVIGATION_ICON,
                    navigation = NavigationResponse(
                        mobileRoute = MobileRouting.EXTERNAL_APP,
                        link = Link(
                            href = prescription.digitalPrescription!!.link,
                        ),
                    ),
                ),
            )
        }

    private fun shouldLetArchive(
        isRedesignHealthPlanDetailsEnabled: Boolean,
        task: ActionPlanTask
    ) = takeIf { isRedesignHealthPlanDetailsEnabled }?.let { getCanArchive(task) }

    private fun shouldLetArchive(
        isRedesignHealthPlanDetailsEnabled: Boolean,
        tasks: List<ActionPlanTask>,
    ) = tasks.firstOrNull()?.let { shouldLetArchive(isRedesignHealthPlanDetailsEnabled, it) }

    private fun getCanArchive(task: ActionPlanTask): Boolean =
        if (task is QuestionnaireNew) false
        else when (task.status) {
            ActionPlanTaskStatus.DONE,
            ActionPlanTaskStatus.EXPIRED,
            ActionPlanTaskStatus.DELETED,
            ActionPlanTaskStatus.DELETED_BY_STAFF,
            ActionPlanTaskStatus.DELETED_BY_MEMBER -> false

            else -> true
        }

    private suspend fun getFinishTaskButton(task: ActionPlanTask, isRedesignHealthPlanDetailsEnabled: Boolean) =
        takeIf { !isRedesignHealthPlanDetailsEnabled }?.let {
            when (task.type) {
                ActionPlanTaskType.FOLLOW_UP_REQUEST,
                ActionPlanTaskType.REFERRAL -> handleTaskType(task)

                else -> handleTaskStatus(task)
            }
        }

    private fun handleTaskStatus(task: ActionPlanTask) =
        when (task.status) {
            ActionPlanTaskStatus.DONE,
            ActionPlanTaskStatus.EXPIRED,
            ActionPlanTaskStatus.DELETED,
            ActionPlanTaskStatus.DELETED_BY_STAFF,
            ActionPlanTaskStatus.DELETED_BY_MEMBER -> null

            else -> FINISH_BUTTON_GENERIC_INFO
        }

    private suspend fun getDeleteTaskButton(task: ActionPlanTask, isRedesignHealthPlanDetailsEnabled: Boolean) =
        takeIf { !isRedesignHealthPlanDetailsEnabled }?.let {
            when {
                isReferralAndSecondaryAttentionLevel(task) -> NOT_SHOW_BUTTON
                else -> handleDeleteTaskStatus(task)
            }
        }

    private fun handleDeleteTaskStatus(task: ActionPlanTask) =
        when (task.status) {
            ActionPlanTaskStatus.DONE,
            ActionPlanTaskStatus.EXPIRED,
            ActionPlanTaskStatus.DELETED,
            ActionPlanTaskStatus.DELETED_BY_MEMBER,
            ActionPlanTaskStatus.DELETED_BY_STAFF,
            ActionPlanTaskStatus.SCHEDULED -> NOT_SHOW_BUTTON

            else -> DELETE_BUTTON_INFO
        }

    private fun isExternalSchedulingNavigationEnabled() = FeatureService.get(
        namespace = FeatureNamespace.ALICE_APP,
        key = "show_test_request_and_referral_navigation",
        defaultValue = false
    )

    private fun getExternalScheduleNavigations(task: ActionPlanTask) =
        takeIf { isExternalSchedulingNavigationEnabled() }?.let {
            listOf(
                HealthPlanItemNavigation(
                    name = WARN_SCHEDULE_NAVIGATION_NAME,
                    description = WARN_SCHEDULE_NAVIGATION_DESCRIPTION,
                    imageUrl = WARN_SCHEDULE_NAVIGATION_IMAGE,
                    icon = WARN_SCHEDULE_NAVIGATION_ICON,
                    navigation = NavigationResponse(
                        mobileRoute = MobileRouting.HEALTH_PLAN_EXTERNAL_SCHEDULE,
                        properties = getWarnScheduleNavigationProperties(task)
                    ),
                )
            )
        }

    private suspend fun checkIfThereIsAppointment(task: ActionPlanTask) =
        appointmentScheduleService.hasWithScheduledStatusByHealthPlanTask(task.id)
            .map { hasScheduledAppointment ->
                if (hasScheduledAppointment) {
                    null
                } else {
                    getExternalScheduleNavigations(task)
                }
            }.get()

    private fun checkIfShouldWarnExternalSchedule(task: ActionPlanTask) =
        takeIf { task.taskAcknowledgedButNotScheduled() }?.let {
            getExternalScheduleNavigations(task)
        }

    private fun getWarnScheduleNavigationProperties(task: ActionPlanTask) =
        if (task.status == ActionPlanTaskStatus.SCHEDULED) {
            mapOf(
                "health_plan_task_id" to task.id,
                "scheduled_at" to task.scheduledAt!!
            )
        } else {
            mapOf(
                "health_plan_task_id" to task.id
            )
        }

    private suspend fun handleTaskType(task: ActionPlanTask): HealthPlanActionButton? =
        if (isScheduledAtBeforeNow(task)) {
            defineExternalSchedulingFinishButton(task.type)
        } else {
            when {
                isReferralAndSecondaryAttentionLevel(task) -> NOT_SHOW_BUTTON
                task.isSchedulable() -> handleSchedulableTypes(task)
                else -> FINISH_BUTTON_GENERIC_INFO
            }
        }

    private suspend fun isReferralAndSecondaryAttentionLevel(task: ActionPlanTask) =
        task.takeIf { it.isReferral() }?.let { isSecondaryAttentionLevel(task.specialize(), false) } ?: false

    private suspend fun handleSchedulableTypes(task: ActionPlanTask) =
        when {
            taskDoesNotHaveScheduledAppointment(task) -> defineExternalSchedulingFinishButton(task.type)
            else -> null
        }

    private suspend fun taskDoesNotHaveScheduledAppointment(task: ActionPlanTask) =
        task.isScheduled() && !appointmentScheduleService.hasWithScheduledStatusByHealthPlanTask(task.id).get()

    private fun defineExternalSchedulingFinishButton(taskType: ActionPlanTaskType) =
        when (taskType) {
            ActionPlanTaskType.FOLLOW_UP_REQUEST,
            ActionPlanTaskType.REFERRAL -> FINISH_BUTTON_REFERRAL_INFO

            ActionPlanTaskType.TEST_REQUEST -> FINISH_BUTTON_TEST_REQUEST_INFO
            else -> null
        }

    private fun getScheduleInfo(task: ReferralNew): ScheduleInfo? {
        return if (hasOnlyOneSessionAvailable(task)) {
            getUniqueSessionScheduleInfo(task)
        } else {
            null
        }
    }

    private suspend fun getRecurrentScheduleInfo(
        task: ReferralNew,
        appointmentsLinkedToTask: List<AppointmentSchedule>?
    ): ScheduleInfo? {
        return if (hasManySessionsAvailable(task) && appointmentsLinkedToTask.isNotNullOrEmpty()) {
            getCloserSessionScheduled(task, appointmentsLinkedToTask!!)
        } else if (hasManySessionsAvailable(task) && appointmentsLinkedToTask.isNullOrEmpty()) {
            val deadline = task.deadline?.date
            val datePattern = DateTimeFormatter.ofPattern("dd/MM/yy")
            val dateFormatted = deadline?.format(datePattern) ?: ""
            ScheduleInfo(
                details = "Consulta 1 - Agendar até **${dateFormatted}**"
            )
        } else {
            null
        }
    }

    private fun hasOnlyOneSessionAvailable(task: ReferralNew) =
        task.getSessions() == 1

    private suspend fun getCloserSessionScheduled(
        task: ReferralNew,
        appointmentsLinkedToTask: List<AppointmentSchedule>
    ): ScheduleInfo? {
        val appointmentsCompleted = appointmentsLinkedToTask.filter { appointment -> appointment.isCompleted }

        if (appointmentsCompleted.size >= task.getSessions()) {
            return buildScheduleInfo(details = "Todas as consultas foram realizadas")
        }

        return appointmentScheduleService.getCloserScheduleByHealthPlanTask(task.id).fold(
            {
                val formattedDate = ActionPlanTaskScheduleTimelineUtils.getScheduledFormattedDateTitle(it)

                val currentIndex = appointmentsCompleted.size + 1
                buildScheduleInfo(it.startTime, "Consulta ${currentIndex}\n**$formattedDate**")
            },
            {
                logger.error(
                    "ActionPlanPresentationBuilder getting schedule info for multiple sessions but having generic error, returning null",
                    "task_id" to task.id,
                    it
                )
                null
            }
        )
    }

    private fun getHideScheduleOptions(
        task: ReferralNew,
        sessionCompleteCount: Int
    ): Boolean {
        if (allowScheduleStatus.contains(task.status).not()) return true

        return if (task.hasManySessions() && sessionCompleteCount >= task.getSessions()) {
            true
        } else if (task.hasManySessions().not() && task.shouldNotSchedule()) {
            true
        } else {
            false
        }
    }

    private fun hasManySessionsAvailable(task: ReferralNew) =
        task.getSessions() > 1

    private fun getHideScheduleOptions(task: TestRequestNew, city: City?) =
        if (task.shouldNotSchedule()) true
        else if (city == null) false
        else hideTestRequestScheduleOptions(city.id)

    private fun getGroupedTestRequestHideScheduleOptions(
        tasks: List<TestRequestNew>,
        city: City?
    ) =
        if (tasks.any { it.shouldNotSchedule() }) true
        else if (city == null) false
        else hideTestRequestScheduleOptions(city.id)

    private fun hideTestRequestScheduleOptions(cityId: String): Boolean {
        val onCityWithSchedulingOptions = FeatureService.inList(
            FeatureNamespace.ALICE_APP,
            "show_test_request_schedule_cities",
            cityId,
            false,
        )

        return !onCityWithSchedulingOptions
    }

    private suspend fun getGroupedTestRequestNavigation(
        tasks: List<ActionPlanTask>,
        isRedesignHealthPlanDetailsEnabled: Boolean
    ): List<HealthPlanItemNavigation>? =
        takeIf { !isRedesignHealthPlanDetailsEnabled }?.let {
            if (tasks.any { it.isScheduled() }) {
                val task = tasks.first { it.isScheduled() }
                checkIfThereIsAppointment(task)
            } else if (tasks.all { it.isActive() }) {
                checkIfShouldWarnExternalSchedule(tasks.first())
            } else {
                null
            }
        }

    private fun getUniqueSessionScheduleInfo(task: ActionPlanTask) =
        task.takeIf { it.isScheduled() }?.let {
            buildScheduleInfo(it.scheduledAt!!, "Agendado")
        }

    private fun getGroupedTestRequestUniqueSessionScheduleInfo(tasks: List<ActionPlanTask>) =
        tasks.firstOrNull { it.isScheduled() }?.let {
            buildScheduleInfo(it.scheduledAt!!, "Agendado")
        }

    private fun getGroupedTestRequestDeleteTaskButton(
        tasks: List<ActionPlanTask>,
        isRedesignHealthPlanDetailsEnabled: Boolean
    ) = takeIf { !isRedesignHealthPlanDetailsEnabled }?.let {
        tasks.firstOrNull { !it.shouldNotEdit() }?.let { DELETE_BUTTON_INFO } ?: NOT_SHOW_BUTTON
    }

    private fun getGroupedTestRequestFinishTaskButton(
        tasks: List<ActionPlanTask>,
        isRedesignHealthPlanDetailsEnabled: Boolean
    ) = takeIf { !isRedesignHealthPlanDetailsEnabled }?.let {
        tasks.firstOrNull { !it.shouldNotEdit() }?.let { FINISH_BUTTON_TEST_REQUEST_INFO } ?: NOT_SHOW_BUTTON
    }

    private fun buildScheduleInfo(scheduleAt: LocalDateTime? = null, details: String) =
        ScheduleInfo(
            scheduledAt = scheduleAt,
            details = details
        )

    private suspend fun isSecondaryAttentionLevel(task: ReferralNew, checkTherapy: Boolean): Boolean =
        task.takeIf { it.hasInfoToCheckAttentionLevel() }?.let { referral ->
            if (hasSuggestedSpecialistWithCommunityRole(referral))
                true
            else {
                task.specialty?.id?.let {
                    medicalSpecialtyService.getById(it).map { specialty ->
                        if (checkTherapy)
                            specialty.isSecondaryAttentionLevelAndNotTherapy()
                        else
                            specialty.isSecondaryAttentionLevel()
                    }.get()
                } ?: false
            }
        } ?: false

    private suspend fun hasSuggestedSpecialistWithCommunityRole(task: ReferralNew): Boolean =
        task.hasSuggestedSpecialistAndTypeIsStaff() && isCommunity(task)

    private suspend fun isCommunity(task: ReferralNew): Boolean =
        staffService.get(task.suggestedSpecialist!!.id)
            .map { staff -> staff.type == StaffType.COMMUNITY_SPECIALIST }.get()

    private suspend fun isTherapy(specialtyId: UUID): Boolean =
        medicalSpecialtyService.getById(specialtyId)
            .map { specialty -> specialty.isTherapy }
            .get()

    private fun getTaskDetailsAlert(
        task: ActionPlanTask,
        isRedesignHealthPlanDetailsEnabled: Boolean
    ): HealthPlanItemAlert? =
        takeIf { !isRedesignHealthPlanDetailsEnabled }?.let {
            task.takeIf { it.shouldShowCopayAlert() }?.let {
                if (it.isOverdue()) taskOverdueAlert
                else taskExpiredAlert
            }
        }


    private fun getCopayLink(task: ActionPlanTask, isRedesignHealthPlanDetailsEnabled: Boolean): HealthPlanItemLink? =
        takeIf { !isRedesignHealthPlanDetailsEnabled }?.let {
            task.takeIf { it.isActive() }?.let { copayLink }
        }

    private fun getTaskDetailsAlert(
        tasks: List<ActionPlanTask>,
        isRedesignHealthPlanDetailsEnabled: Boolean
    ): HealthPlanItemAlert? =
        takeIf { !isRedesignHealthPlanDetailsEnabled }?.let {
            tasks.firstOrNull { it.shouldShowCopayAlert() }?.let {
                if (it.isOverdue()) taskOverdueAlert
                else taskExpiredAlert
            }
        }

    private fun getCopayLink(
        task: List<ActionPlanTask>,
        isRedesignHealthPlanDetailsEnabled: Boolean
    ): HealthPlanItemLink? =
        takeIf { !isRedesignHealthPlanDetailsEnabled }?.let {
            takeIf { task.all { it.isActive() } }?.let { copayLink }
        }

    private fun getGroupedTestRequestExpireDetails(tasks: List<ActionPlanTask>): String? =
        tasks.firstOrNull()?.expirationDetails("Agendar")

    private fun ActionPlanTask.expirationDetails(prefix: String) =
        this.dueDate?.let { "$prefix até ${it.toBrazilianDateFormat()}" }

    private fun isScheduledAtBeforeNow(task: ActionPlanTask) =
        task.isScheduled() && (task.scheduledAt?.isBefore(LocalDateTime.now()) ?: false)

    private fun getPrescriptionRootNodeId(): String = FeatureService.get(
        namespace = FeatureNamespace.SCREENING,
        key = "triage_prescription_protocol_id",
        defaultValue = "2b531a26-7d1a-4dc3-b4df-905e2a605100"
    )
}
