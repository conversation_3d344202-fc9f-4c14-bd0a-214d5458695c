package br.com.alice.healthplan.consumers

import br.com.alice.clinicalaccount.event.PersonHealthcareTeamAssociationUpdatedEvent
import br.com.alice.common.RangeUUID
import br.com.alice.common.data.dsl.matchers.ResultAssert.Companion.assertThat
import br.com.alice.common.helpers.coVerifyNone
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.HealthPlanTaskType
import br.com.alice.data.layer.models.MemberOnboarding.MemberOnboardingStepStatus.COMPLETED
import br.com.alice.data.layer.models.MemberOnboarding.MemberOnboardingStepStatus.PENDING
import br.com.alice.data.layer.models.MemberOnboarding.MemberOnboardingStepType.ALICE_INFO
import br.com.alice.data.layer.models.MemberOnboarding.MemberOnboardingStepType.COVER
import br.com.alice.data.layer.models.MemberOnboarding.MemberOnboardingStepType.SCORE_MAGENTA
import br.com.alice.data.layer.models.MemberOnboardingReferencedLink
import br.com.alice.data.layer.models.MemberOnboardingReferencedLinkModel.HEALTH_PLAN_TASK
import br.com.alice.data.layer.models.MemberOnboardingReferencedLinkModel.HEALTH_PLAN_TASK_TEMPLATE
import br.com.alice.data.layer.models.Step
import br.com.alice.healthplan.converters.HealthPlanTaskTransportConverter
import br.com.alice.healthplan.services.internal.member_onboarding.MemberOnboardingTaskService
import br.com.alice.member.onboarding.client.MemberOnboardingService
import com.github.kittinunf.result.success
import io.mockk.called
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.mockk
import kotlinx.coroutines.runBlocking
import java.time.LocalDateTime
import kotlin.test.Test

class PersonHealthcareTeamAssociationConsumerTest : ConsumerTest() {

    private val memberOnboardingService: MemberOnboardingService = mockk()
    private val memberOnboardingTaskService: MemberOnboardingTaskService = mockk()

    private val consumer = PersonHealthCareTeamAssociationConsumer(
        memberOnboardingService, memberOnboardingTaskService
    )
    private val staff = TestModelFactory.buildStaff()
    private val healthPlanTask =
        TestModelFactory.buildHealthPlanTask(type = HealthPlanTaskType.OTHERS, staffId = staff.id)
    private val transport = HealthPlanTaskTransportConverter.convertToTransport(
        task = healthPlanTask,
        staffsById = mapOf(staff.id to staff),
        groups = emptyMap()
    )

    private val personClinicalAccount = TestModelFactory.buildPersonClinicalAccount()
    private val memberOnboarding = TestModelFactory.buildMemberOnboarding(personId = personClinicalAccount.personId)

    @Test
    fun `#creteTaskIfOnboardingCompleted - should return false if personClinicalAccount is null`() = runBlocking {
        val event = PersonHealthcareTeamAssociationUpdatedEvent(
            personId = personClinicalAccount.personId,
            newHealthcareTeamId = RangeUUID.generate(),
            updatedAt = LocalDateTime.now()
        )

        val result = consumer.creteTaskIfOnboardingCompleted(event)
        assertThat(result).isSuccessWithData(false)

        coVerify { memberOnboardingService wasNot called }
        coVerify { memberOnboardingTaskService wasNot called }
    }

    @Test
    fun `#creteTaskIfOnboardingCompleted - should return false if step of SCORE_MAGENTA not completed`() = runBlocking {
        val event = PersonHealthcareTeamAssociationUpdatedEvent(
            personId = personClinicalAccount.personId,
            newHealthcareTeamId = RangeUUID.generate(),
            updatedAt = LocalDateTime.now(),
            newPersonClinicalAccount = personClinicalAccount
        )

        val memberOnboarding = memberOnboarding.copy(
            steps = listOf(
                Step(templateType = SCORE_MAGENTA, status = PENDING),
                Step(templateType = COVER, status = COMPLETED),
            )
        )

        coEvery { memberOnboardingService.getByPersonId(personClinicalAccount.personId) } returns memberOnboarding.success()

        val result = consumer.creteTaskIfOnboardingCompleted(event)
        assertThat(result).isSuccessWithData(false)

        coVerifyOnce { memberOnboardingService.getByPersonId(any()) }

        coVerify { memberOnboardingTaskService wasNot called }
        coVerifyNone { memberOnboardingService.addReferencedLink(any(), any()) }
    }

    @Test
    fun `#creteTaskIfOnboardingCompleted - should return false if step of COVER not completed`() = runBlocking {
        val event = PersonHealthcareTeamAssociationUpdatedEvent(
            personId = personClinicalAccount.personId,
            newHealthcareTeamId = RangeUUID.generate(),
            updatedAt = LocalDateTime.now(),
            newPersonClinicalAccount = personClinicalAccount
        )

        val memberOnboarding = memberOnboarding.copy(
            steps = listOf(
                Step(templateType = SCORE_MAGENTA, status = COMPLETED),
                Step(templateType = COVER, status = PENDING),
            )
        )

        coEvery { memberOnboardingService.getByPersonId(personClinicalAccount.personId) } returns memberOnboarding.success()

        val result = consumer.creteTaskIfOnboardingCompleted(event)
        assertThat(result).isSuccessWithData(false)

        coVerifyOnce { memberOnboardingService.getByPersonId(any()) }

        coVerify { memberOnboardingTaskService wasNot called }
        coVerifyNone { memberOnboardingService.addReferencedLink(any(), any()) }
    }

    @Test
    fun `#creteTaskIfOnboardingCompleted - should return false if member onboarding have HEALTH_PLAN_TASK`() =
        runBlocking {
            val event = PersonHealthcareTeamAssociationUpdatedEvent(
                personId = personClinicalAccount.personId,
                newHealthcareTeamId = RangeUUID.generate(),
                updatedAt = LocalDateTime.now(),
                newPersonClinicalAccount = personClinicalAccount
            )

            val memberOnboarding = memberOnboarding.copy(
                steps = listOf(
                    Step(templateType = SCORE_MAGENTA, status = COMPLETED),
                    Step(templateType = COVER, status = COMPLETED),
                ),
                referencedLinks = listOf(
                    MemberOnboardingReferencedLink(
                        id = RangeUUID.generate(),
                        model = HEALTH_PLAN_TASK
                    ),
                    MemberOnboardingReferencedLink(
                        id = RangeUUID.generate(),
                        model = HEALTH_PLAN_TASK_TEMPLATE
                    )
                )
            )

            coEvery { memberOnboardingService.getByPersonId(personClinicalAccount.personId) } returns memberOnboarding.success()

            val result = consumer.creteTaskIfOnboardingCompleted(event)
            assertThat(result).isSuccessWithData(false)

            coVerifyOnce { memberOnboardingService.getByPersonId(any()) }

            coVerify { memberOnboardingTaskService wasNot called }
            coVerifyNone { memberOnboardingService.addReferencedLink(any(), any()) }
        }

    @Test
    fun `#creteTaskIfOnboardingCompleted - should return false if referenced links in member onboarding is empty`() =
        runBlocking {
            val event = PersonHealthcareTeamAssociationUpdatedEvent(
                personId = personClinicalAccount.personId,
                newHealthcareTeamId = RangeUUID.generate(),
                updatedAt = LocalDateTime.now(),
                newPersonClinicalAccount = personClinicalAccount
            )

            val memberOnboarding = memberOnboarding.copy(
                steps = listOf(
                    Step(templateType = SCORE_MAGENTA, status = COMPLETED),
                    Step(templateType = COVER, status = COMPLETED),
                ),
                referencedLinks = emptyList()
            )

            coEvery { memberOnboardingService.getByPersonId(personClinicalAccount.personId) } returns memberOnboarding.success()

            val result = consumer.creteTaskIfOnboardingCompleted(event)
            assertThat(result).isSuccessWithData(false)

            coVerifyOnce { memberOnboardingService.getByPersonId(any()) }

            coVerify { memberOnboardingTaskService wasNot called }
            coVerifyNone { memberOnboardingService.addReferencedLink(any(), any()) }
        }

    @Test
    fun `#creteTaskIfOnboardingCompleted - should create task and add referenced links`() = runBlocking {
        val event = PersonHealthcareTeamAssociationUpdatedEvent(
            personId = personClinicalAccount.personId,
            newHealthcareTeamId = RangeUUID.generate(),
            updatedAt = LocalDateTime.now(),
            newPersonClinicalAccount = personClinicalAccount
        )

        val templateId = RangeUUID.generate()
        val memberOnboarding = memberOnboarding.copy(
            steps = listOf(
                Step(templateType = SCORE_MAGENTA, status = COMPLETED),
                Step(templateType = COVER, status = COMPLETED),
            ),
            referencedLinks = listOf(
                MemberOnboardingReferencedLink(
                    id = templateId,
                    model = HEALTH_PLAN_TASK_TEMPLATE
                )
            )
        )

        coEvery { memberOnboardingService.getByPersonId(personClinicalAccount.personId) } returns memberOnboarding.success()
        coEvery {
            memberOnboardingTaskService.createTask(
                personClinicalAccount.personId,
                listOf(templateId)
            )
        } returns listOf(transport).success()
        coEvery {
            memberOnboardingService.addReferencedLink(
                memberOnboarding.id,
                listOf(MemberOnboardingReferencedLink(transport.id!!, HEALTH_PLAN_TASK))
            )
        } returns memberOnboarding.success()

        val result = consumer.creteTaskIfOnboardingCompleted(event)
        assertThat(result).isSuccessWithData(memberOnboarding)

        coVerifyOnce { memberOnboardingService.getByPersonId(any()) }
        coVerifyOnce { memberOnboardingTaskService.createTask(any(), any()) }
        coVerifyOnce { memberOnboardingService.addReferencedLink(any(), any()) }
    }

    @Test
    fun `#creteTaskIfOnboardingCompleted - should create task and add referenced links from HEALTH DECLARATION and haven't SCORE MAGENTA`() = runBlocking {
        val event = PersonHealthcareTeamAssociationUpdatedEvent(
            personId = personClinicalAccount.personId,
            newHealthcareTeamId = RangeUUID.generate(),
            updatedAt = LocalDateTime.now(),
            newPersonClinicalAccount = personClinicalAccount
        )

        val templateId = RangeUUID.generate()
        val memberOnboarding = memberOnboarding.copy(
            steps = listOf(
                Step(templateType = ALICE_INFO, status = COMPLETED),
                Step(templateType = COVER, status = COMPLETED),
            ),
            referencedLinks = listOf(
                MemberOnboardingReferencedLink(
                    id = templateId,
                    model = HEALTH_PLAN_TASK_TEMPLATE
                )
            )
        )

        coEvery { memberOnboardingService.getByPersonId(personClinicalAccount.personId) } returns memberOnboarding.success()
        coEvery {
            memberOnboardingTaskService.createTask(
                personClinicalAccount.personId,
                listOf(templateId)
            )
        } returns listOf(transport).success()
        coEvery {
            memberOnboardingService.addReferencedLink(
                memberOnboarding.id,
                listOf(MemberOnboardingReferencedLink(transport.id!!, HEALTH_PLAN_TASK))
            )
        } returns memberOnboarding.success()

        val result = consumer.creteTaskIfOnboardingCompleted(event)
        assertThat(result).isSuccessWithData(memberOnboarding)

        coVerifyOnce { memberOnboardingService.getByPersonId(any()) }
        coVerifyOnce { memberOnboardingTaskService.createTask(any(), any()) }
        coVerifyOnce { memberOnboardingService.addReferencedLink(any(), any()) }
    }


}
