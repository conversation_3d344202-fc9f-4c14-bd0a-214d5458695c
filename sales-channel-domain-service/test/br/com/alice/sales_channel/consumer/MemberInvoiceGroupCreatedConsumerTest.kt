package br.com.alice.sales_channel.consumer

import br.com.alice.business.client.CompanyService
import br.com.alice.common.core.exceptions.NotFoundException
import br.com.alice.common.helpers.coVerifyNone
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.MemberInvoiceType
import br.com.alice.moneyin.event.MemberInvoiceGroupCreatedEvent
import br.com.alice.sales_channel.br.com.alice.sales_channel.consumer.ConsumerTest
import br.com.alice.sales_channel.service.OngoingCompanyDealMovementService
import com.github.kittinunf.result.failure
import com.github.kittinunf.result.success
import io.mockk.coEvery
import io.mockk.mockk
import kotlin.test.Test
import kotlinx.coroutines.runBlocking

class MemberInvoiceGroupCreatedConsumerTest : ConsumerTest() {
    private val companyService = mockk<CompanyService>()
    private val ongoingCompanyDealMovementService = mockk<OngoingCompanyDealMovementService>()
    private val consumer = MemberInvoiceGroupCreatedConsumer(companyService, ongoingCompanyDealMovementService)

    private val company = TestModelFactory.buildCompany()
    private val invoiceGroup = TestModelFactory.buildMemberInvoiceGroup(
        companyId = company.id,
        type = MemberInvoiceType.FIRST_PAYMENT
    )
    private val ongoingCompanyDeal = TestModelFactory.buildOngoingCompanyDeal(
        cnpj = company.cnpj,
        name = company.name,
        companyId = company.id
    )

    @Test
    fun `processMemberInvoiceGroup should process invoice group when valid`() = runBlocking {
        coEvery { companyService.get(any()) } returns company.success()
        coEvery {
            ongoingCompanyDealMovementService.move(
                any(),
                any()
            )
        } returns true.success()

        val event = MemberInvoiceGroupCreatedEvent(invoiceGroup)

        consumer.processMemberInvoiceGroup(event)

        coVerifyOnce {
            companyService.get(invoiceGroup.companyId!!)
        }
        coVerifyOnce {
            ongoingCompanyDealMovementService.move(any(), any())
        }
    }

    @Test
    fun `processMemberInvoiceGroup should not process invoice group when company not found`() = runBlocking {
        coEvery { companyService.get(any()) } returns NotFoundException().failure()

        val event = MemberInvoiceGroupCreatedEvent(invoiceGroup)

        consumer.processMemberInvoiceGroup(event)

        coVerifyOnce { companyService.get(invoiceGroup.companyId!!) }
        coVerifyNone { ongoingCompanyDealMovementService.move(any(), any()) }
    }

    @Test
    fun `processMemberInvoiceGroup should not process invoice group when deal not found`() = runBlocking {
        coEvery { companyService.get(any()) } returns TestModelFactory.buildCompany().success()
        coEvery {
            ongoingCompanyDealMovementService.move(
                any(),
                any()
            )
        } returns NotFoundException().failure()

        val event = MemberInvoiceGroupCreatedEvent(invoiceGroup)

        consumer.processMemberInvoiceGroup(event)

        coVerifyOnce {
            companyService.get(invoiceGroup.companyId!!)
        }
        coVerifyOnce {
            ongoingCompanyDealMovementService.move(any(), any())
        }
    }

    // Test case for when invoice group is not first payment
    @Test
    fun `processMemberInvoiceGroup should not process invoice group when invoice group is not first payment`() = runBlocking {
        val invoiceGroup = TestModelFactory.buildMemberInvoiceGroup(
            companyId = company.id,
            type = MemberInvoiceType.REGULAR_PAYMENT
        )

        val event = MemberInvoiceGroupCreatedEvent(invoiceGroup)

        consumer.processMemberInvoiceGroup(event)

        coVerifyNone {
            companyService.get(invoiceGroup.companyId!!)
        }
        coVerifyNone {
            ongoingCompanyDealMovementService.move(any(), any())
        }
    }
}
