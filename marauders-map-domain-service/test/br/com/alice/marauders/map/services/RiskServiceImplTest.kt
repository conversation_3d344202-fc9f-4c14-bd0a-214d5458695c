package br.com.alice.marauders.map.services

import br.com.alice.common.RangeUUID
import br.com.alice.common.core.PersonId
import br.com.alice.common.data.dsl.matchers.ResultAssert.Companion.assertThat
import br.com.alice.common.helpers.coVerifyNone
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.CaseRecord
import br.com.alice.data.layer.models.CaseSeverity
import br.com.alice.common.Disease
import br.com.alice.data.layer.models.Risk
import br.com.alice.data.layer.models.Risk.ReferenceModel
import br.com.alice.data.layer.models.RiskDescription
import br.com.alice.healthcondition.client.CaseRecordService
import br.com.alice.healthcondition.client.HealthConditionService
import br.com.alice.marauders.map.client.RiskCalculationConfService
import br.com.alice.marauders.map.models.CaseRecordCalculationConfTransport
import br.com.alice.marauders.map.models.ScoreType
import br.com.alice.marauders.map.services.internal.RiskService
import com.github.kittinunf.result.success
import io.mockk.clearAllMocks
import io.mockk.coEvery
import io.mockk.mockk
import kotlinx.coroutines.runBlocking
import java.time.LocalDateTime
import kotlin.test.AfterTest
import kotlin.test.Test


internal class RiskServiceImplTest {
    private val riskService: RiskService = mockk()
    private val caseRecordService: CaseRecordService = mockk()
    private val healthConditionService: HealthConditionService = mockk()
    private val riskCalculationConfService: RiskCalculationConfService = mockk()
    private val service =
        RiskServiceImpl(riskService, caseRecordService, healthConditionService, riskCalculationConfService)

    private val personId = PersonId()
    private val person = TestModelFactory.buildPerson(personId, dateOfBirth = LocalDateTime.now().minusYears(12))
    private val risk = TestModelFactory.buildRisk()
    private val staffLoggedId = RangeUUID.generate()
    private val caseRecord = TestModelFactory.buildCaseRecord(personId = personId)
    private val healthCondition = TestModelFactory.buildHealthCondition(code = caseRecord.description.value)
    private val riskCalculationConf = TestModelFactory.buildRiskCalculation(healthConditionId = healthCondition.id)

    @AfterTest
    fun clear() = clearAllMocks()

    @Test
    fun `#updateByEdit - should add new risk by manual edit`() = runBlocking {
        val newRisk = RiskDescription.NO_RISK
        val expected = risk.copy(
            finalValue = newRisk.toValue(),
            addedAt = LocalDateTime.now(),
            riskDescription = newRisk,
            addedBy = Risk.AddedBy(
                staffLoggedId,
                Risk.AddedByType.STAFF
            )
        )

        coEvery { riskService.getCurrent(personId) } returns risk.success()
        coEvery {
            riskService.add(match {
                it.finalValue == expected.finalValue
                        && it.riskDescription == expected.riskDescription
                        && it.addedBy == expected.addedBy
            })
        } returns expected.success()

        val result = service.updateByEdit(personId, newRisk, staffLoggedId)
        assertThat(result).isSuccess()

        coVerifyOnce { riskService.getCurrent(any()) }
        coVerifyOnce { riskService.add(any()) }
    }

    @Test
    fun `#updateByEdit - should add new risk by system edit`() = runBlocking {
        val newRisk = RiskDescription.TARGET
        val expected = risk.copy(
            finalValue = risk.finalValue,
            addedAt = LocalDateTime.now(),
            riskDescription = newRisk,
            addedBy = Risk.AddedBy(
                null,
                Risk.AddedByType.SYSTEM
            )
        )

        coEvery { riskService.getCurrent(personId) } returns risk.success()
        coEvery {
            riskService.add(match {
                it.finalValue == expected.finalValue
                        && it.riskDescription == expected.riskDescription
                        && it.addedBy == expected.addedBy
            })
        } returns expected.success()

        val result = service.updateByEdit(personId, newRisk)
        assertThat(result).isSuccess()

        coVerifyOnce { riskService.getCurrent(any()) }
        coVerifyOnce { riskService.add(any()) }
    }

    @Test
    fun `#updateByEdit - should add new risk with new referencedModels`() = runBlocking {
        val newRisk = RiskDescription.TARGET
        val referencedModels = listOf(Risk.ReferencedModel(id = RangeUUID.generate(), model = ReferenceModel.TARGET_CASE_RECORD))
        val expected = risk.copy(
            finalValue = risk.finalValue,
            addedAt = LocalDateTime.now(),
            riskDescription = newRisk,
            addedBy = Risk.AddedBy(
                null,
                Risk.AddedByType.SYSTEM
            ),
            referencedModels = referencedModels
        )

        coEvery { riskService.getCurrent(personId) } returns risk.success()
        coEvery {
            riskService.add(match {
                it.finalValue == expected.finalValue
                        && it.riskDescription == expected.riskDescription
                        && it.addedBy == expected.addedBy
                        && it.referencedModels == referencedModels
            })
        } returns expected.success()

        val result = service.updateByEdit(personId = personId, newRisk = newRisk, referencedModels = referencedModels)
        assertThat(result).isSuccess()

        coVerifyOnce { riskService.getCurrent(any()) }
        coVerifyOnce { riskService.add(any()) }
    }


    @Test
    fun `#getByPerson - should return risk current by person id`() = runBlocking {
        coEvery { riskService.getCurrent(personId) } returns risk.success()

        val result = service.getByPerson(personId)
        assertThat(result).isSuccessWithData(risk)

        coVerifyOnce { riskService.getCurrent(any()) }
    }

    @Test
    fun `#getDetailCalculationConf - should return list empty when not have referenced models`() = runBlocking {
        val riskWithoutModels = risk.copy(referencedModels = emptyList())
        coEvery { riskService.get(riskWithoutModels.id) } returns riskWithoutModels.success()

        val response = service.getDetailCalculationConf(person, riskWithoutModels.id)
        assertThat(response).isSuccessWithData(emptyList())

        coVerifyOnce { riskService.get(any()) }
        coVerifyNone { caseRecordService.getByIds(any()) }
        coVerifyNone { healthConditionService.findByCodes(any()) }
        coVerifyNone { riskCalculationConfService.getByHealthConditions(any()) }
    }

    @Test
    fun `#getDetailCalculationConf - should return list empty when not find case records`() = runBlocking {
        val riskWithModels = risk.copy(
            referencedModels = listOf(
                Risk.ReferencedModel(
                    id = caseRecord.id, model = ReferenceModel.CASE_RECORD
                )
            )
        )
        coEvery { riskService.get(riskWithModels.id) } returns riskWithModels.success()
        coEvery { caseRecordService.getByIds(listOf(caseRecord.id)) } returns emptyList<CaseRecord>().success()

        val response = service.getDetailCalculationConf(person, riskWithModels.id)
        assertThat(response).isSuccessWithData(emptyList())

        coVerifyOnce { riskService.get(any()) }
        coVerifyOnce { caseRecordService.getByIds(any()) }
        coVerifyNone { healthConditionService.findByCodes(any()) }
        coVerifyNone { riskCalculationConfService.getByHealthConditions(any()) }
    }

    @Test
    fun `#getDetailCalculationConf - should return empty list when all conf is score 0`() = runBlocking {
        val riskWithModels = risk.copy(
            referencedModels = listOf(
                Risk.ReferencedModel(
                    id = caseRecord.id, model = ReferenceModel.CASE_RECORD
                )
            )
        )

        coEvery { riskService.get(riskWithModels.id) } returns riskWithModels.success()
        coEvery { caseRecordService.getByIds(listOf(caseRecord.id)) } returns listOf(caseRecord).success()
        coEvery { healthConditionService.findByCodes(listOf(caseRecord.description.value)) } returns listOf(
            healthCondition
        ).success()
        coEvery { riskCalculationConfService.getByHealthConditions(listOf(healthCondition.id)) } returns listOf(
            riskCalculationConf
        ).success()

        val response = service.getDetailCalculationConf(person, riskWithModels.id)
        assertThat(response).isSuccessWithData(emptyList())

        coVerifyOnce { riskService.get(any()) }
        coVerifyOnce { caseRecordService.getByIds(any()) }
        coVerifyOnce { healthConditionService.findByCodes(any()) }
        coVerifyOnce { riskCalculationConfService.getByHealthConditions(any()) }
    }


    @Test
    fun `#getDetailCalculationConf - should return list with one case record calculation conf`() = runBlocking {
        val riskWithModels = risk.copy(
            referencedModels = listOf(
                Risk.ReferencedModel(
                    id = caseRecord.id, model = ReferenceModel.CASE_RECORD
                )
            )
        )
        val caseRecord2 = caseRecord.copy(severity = CaseSeverity.DECOMPENSATED)
        val riskCalculationConf2 = riskCalculationConf.copy(chronicScore = 1, severeScore = 1, decompensatedScore = 1)

        val expected = listOf(
            CaseRecordCalculationConfTransport(
                caseId = caseRecord2.id,
                score = 3,
                addedAt = caseRecord2.addedAt,
                scoreSum = mapOf(ScoreType.CHRONIC to 1, ScoreType.SEVERE to 1, ScoreType.DECOMPENSATED to 1),
                description = caseRecord.description
            )
        )
        coEvery { riskService.get(riskWithModels.id) } returns riskWithModels.success()
        coEvery { caseRecordService.getByIds(listOf(caseRecord2.id)) } returns listOf(caseRecord2).success()
        coEvery { healthConditionService.findByCodes(listOf(caseRecord2.description.value)) } returns listOf(
            healthCondition
        ).success()
        coEvery { riskCalculationConfService.getByHealthConditions(listOf(healthCondition.id)) } returns listOf(
            riskCalculationConf2
        ).success()

        val response = service.getDetailCalculationConf(person, riskWithModels.id)
        assertThat(response).isSuccessWithData(expected)

        coVerifyOnce { riskService.get(any()) }
        coVerifyOnce { caseRecordService.getByIds(any()) }
        coVerifyOnce { healthConditionService.findByCodes(any()) }
        coVerifyOnce { riskCalculationConfService.getByHealthConditions(any()) }
    }

    @Test
    fun `#getDetailCalculationConf - should return list with one case record calculation conf and filter where score is difference of 0`() =
        runBlocking {
            val caseRecord2 = TestModelFactory.buildCaseRecord(
                personId,
                severity = CaseSeverity.DECOMPENSATED,
                description = Disease(Disease.Type.CID_10, "A98")
            )
            val healthCondition2 = TestModelFactory.buildHealthCondition(code = caseRecord2.description.value)
            val riskCalculationConf2 = TestModelFactory.buildRiskCalculation(
                healthConditionId = healthCondition2.id,
                chronicScore = 1,
                severeScore = 1,
                decompensatedScore = 1
            )

            val riskWithModels = risk.copy(
                referencedModels = listOf(
                    Risk.ReferencedModel(
                        id = caseRecord.id, model = ReferenceModel.CASE_RECORD
                    ),
                    Risk.ReferencedModel(
                        id = caseRecord2.id, model = ReferenceModel.CASE_RECORD
                    )
                )
            )

            val expected = listOf(
                CaseRecordCalculationConfTransport(
                    caseId = caseRecord2.id,
                    score = 3,
                    addedAt = caseRecord2.addedAt,
                    scoreSum = mapOf(ScoreType.CHRONIC to 1, ScoreType.SEVERE to 1, ScoreType.DECOMPENSATED to 1),
                    description = caseRecord2.description
                )
            )

            coEvery { riskService.get(riskWithModels.id) } returns riskWithModels.success()
            coEvery { caseRecordService.getByIds(listOf(caseRecord.id, caseRecord2.id)) } returns listOf(
                caseRecord,
                caseRecord2
            ).success()
            coEvery {
                healthConditionService.findByCodes(
                    listOf(
                        caseRecord.description.value,
                        caseRecord2.description.value
                    )
                )
            } returns listOf(healthCondition, healthCondition2).success()
            coEvery {
                riskCalculationConfService.getByHealthConditions(
                    listOf(
                        healthCondition.id,
                        healthCondition2.id
                    )
                )
            } returns listOf(
                riskCalculationConf,
                riskCalculationConf2
            ).success()

            val response = service.getDetailCalculationConf(person, riskWithModels.id)
            assertThat(response).isSuccessWithData(expected)

            coVerifyOnce { riskService.get(any()) }
            coVerifyOnce { caseRecordService.getByIds(any()) }
            coVerifyOnce { healthConditionService.findByCodes(any()) }
            coVerifyOnce { riskCalculationConfService.getByHealthConditions(any()) }
        }

    @Test
    fun `#getDetailCalculationConf - should return list with two case record calculation conf`() =
        runBlocking {
            val caseRecord2 = TestModelFactory.buildCaseRecord(
                personId,
                severity = CaseSeverity.DECOMPENSATED,
                description = Disease(Disease.Type.CID_10, "A98")
            )
            val healthCondition2 = TestModelFactory.buildHealthCondition(code = caseRecord2.description.value)
            val riskCalculationConf2 = TestModelFactory.buildRiskCalculation(
                healthConditionId = healthCondition2.id,
                riskGroupId = RangeUUID.generate(),
                chronicScore = 1,
                severeScore = 1,
                decompensatedScore = 1
            )
            val riskCalculationConf3 = riskCalculationConf.copy(severeScore = 1)

            val riskWithModels = risk.copy(
                referencedModels = listOf(
                    Risk.ReferencedModel(
                        id = caseRecord.id, model = ReferenceModel.CASE_RECORD
                    ),
                    Risk.ReferencedModel(
                        id = caseRecord2.id, model = ReferenceModel.CASE_RECORD
                    )
                )
            )

            val expected = listOf(
                CaseRecordCalculationConfTransport(
                    caseId = caseRecord2.id,
                    score = 3,
                    addedAt = caseRecord2.addedAt,
                    scoreSum = mapOf(ScoreType.CHRONIC to 1, ScoreType.SEVERE to 1, ScoreType.DECOMPENSATED to 1),
                    description = caseRecord2.description
                ),
                CaseRecordCalculationConfTransport(
                    caseId = caseRecord.id,
                    score = 1,
                    addedAt = caseRecord.addedAt,
                    scoreSum = mapOf(ScoreType.CHRONIC to 0, ScoreType.SEVERE to 1, ScoreType.DECOMPENSATED to 0),
                    description = caseRecord.description
                )
            )

            coEvery { riskService.get(riskWithModels.id) } returns riskWithModels.success()
            coEvery { caseRecordService.getByIds(listOf(caseRecord.id, caseRecord2.id)) } returns listOf(
                caseRecord,
                caseRecord2
            ).success()
            coEvery {
                healthConditionService.findByCodes(
                    listOf(
                        caseRecord.description.value,
                        caseRecord2.description.value
                    )
                )
            } returns listOf(healthCondition, healthCondition2).success()
            coEvery {
                riskCalculationConfService.getByHealthConditions(
                    listOf(
                        healthCondition.id,
                        healthCondition2.id
                    )
                )
            } returns listOf(
                riskCalculationConf2,
                riskCalculationConf3
            ).success()

            val response = service.getDetailCalculationConf(person, riskWithModels.id)
            assertThat(response).isSuccessWithData(expected)

            coVerifyOnce { riskService.get(any()) }
            coVerifyOnce { caseRecordService.getByIds(any()) }
            coVerifyOnce { healthConditionService.findByCodes(any()) }
            coVerifyOnce { riskCalculationConfService.getByHealthConditions(any()) }
        }

    @Test
    fun `#getDetailCalculationConf - should return list with one case record when exist group risk`() =
        runBlocking {
            val riskGroupId = RangeUUID.generate()

            val caseRecord2 = TestModelFactory.buildCaseRecord(
                personId,
                severity = CaseSeverity.DECOMPENSATED,
                description = Disease(Disease.Type.CID_10, "A98")
            )
            val healthCondition2 = TestModelFactory.buildHealthCondition(code = caseRecord2.description.value)
            val riskCalculationConf2 = TestModelFactory.buildRiskCalculation(
                healthConditionId = healthCondition2.id,
                riskGroupId = riskGroupId,
                chronicScore = 1,
                severeScore = 1,
                decompensatedScore = 1
            )
            val riskCalculationConf3 = riskCalculationConf.copy(severeScore = 1, riskGroupId = riskGroupId)

            val riskWithModels = risk.copy(
                referencedModels = listOf(
                    Risk.ReferencedModel(
                        id = caseRecord.id, model = ReferenceModel.CASE_RECORD
                    ),
                    Risk.ReferencedModel(
                        id = caseRecord2.id, model = ReferenceModel.CASE_RECORD
                    )
                )
            )

            val expected = listOf(
                CaseRecordCalculationConfTransport(
                    caseId = caseRecord2.id,
                    score = 3,
                    addedAt = caseRecord2.addedAt,
                    scoreSum = mapOf(ScoreType.CHRONIC to 1, ScoreType.SEVERE to 1, ScoreType.DECOMPENSATED to 1),
                    description = caseRecord2.description
                )
            )

            coEvery { riskService.get(riskWithModels.id) } returns riskWithModels.success()
            coEvery { caseRecordService.getByIds(listOf(caseRecord.id, caseRecord2.id)) } returns listOf(
                caseRecord,
                caseRecord2
            ).success()
            coEvery {
                healthConditionService.findByCodes(
                    listOf(
                        caseRecord.description.value,
                        caseRecord2.description.value
                    )
                )
            } returns listOf(healthCondition, healthCondition2).success()
            coEvery {
                riskCalculationConfService.getByHealthConditions(
                    listOf(
                        healthCondition.id,
                        healthCondition2.id
                    )
                )
            } returns listOf(
                riskCalculationConf3,
                riskCalculationConf2
            ).success()

            val response = service.getDetailCalculationConf(person, riskWithModels.id)
            assertThat(response).isSuccessWithData(expected)

            coVerifyOnce { riskService.get(any()) }
            coVerifyOnce { caseRecordService.getByIds(any()) }
            coVerifyOnce { healthConditionService.findByCodes(any()) }
            coVerifyOnce { riskCalculationConfService.getByHealthConditions(any()) }
        }

    @Test
    fun `#getDetailCalculationConf - should return return most recent when there is a tie`() =
        runBlocking {
            val riskGroupId = RangeUUID.generate()

            val caseRecord2 = TestModelFactory.buildCaseRecord(
                personId,
                severity = CaseSeverity.DECOMPENSATED,
                description = Disease(Disease.Type.CID_10, "A98"),
                addedAt = LocalDateTime.now().minusDays(10)
            )
            val caseRecord3 = TestModelFactory.buildCaseRecord(
                personId,
                severity = CaseSeverity.DECOMPENSATED,
                description = Disease(Disease.Type.CID_10, "A97"),
                addedAt = LocalDateTime.now()
            )
            val healthCondition2 = TestModelFactory.buildHealthCondition(code = caseRecord2.description.value)
            val healthCondition3 = TestModelFactory.buildHealthCondition(code = caseRecord3.description.value)
            val riskCalculationConf2 = TestModelFactory.buildRiskCalculation(
                healthConditionId = healthCondition2.id,
                riskGroupId = riskGroupId,
                chronicScore = 1,
                severeScore = 1,
                decompensatedScore = 1
            )
            val riskCalculationConf3 = TestModelFactory.buildRiskCalculation(
                healthConditionId = healthCondition3.id,
                riskGroupId = riskGroupId,
                chronicScore = 1,
                severeScore = 1,
                decompensatedScore = 1
            )

            val riskWithModels = risk.copy(
                referencedModels = listOf(
                    Risk.ReferencedModel(
                        id = caseRecord2.id, model = ReferenceModel.CASE_RECORD
                    ),
                    Risk.ReferencedModel(
                        id = caseRecord3.id, model = ReferenceModel.CASE_RECORD
                    )
                )
            )

            val expected = listOf(
                CaseRecordCalculationConfTransport(
                    caseId = caseRecord3.id,
                    score = 3,
                    addedAt = caseRecord3.addedAt,
                    scoreSum = mapOf(ScoreType.CHRONIC to 1, ScoreType.SEVERE to 1, ScoreType.DECOMPENSATED to 1),
                    description = caseRecord3.description
                )
            )

            coEvery { riskService.get(riskWithModels.id) } returns riskWithModels.success()
            coEvery { caseRecordService.getByIds(listOf(caseRecord2.id, caseRecord3.id)) } returns listOf(
                caseRecord2,
                caseRecord3
            ).success()
            coEvery {
                healthConditionService.findByCodes(
                    listOf(
                        caseRecord2.description.value,
                        caseRecord3.description.value
                    )
                )
            } returns listOf(healthCondition2, healthCondition3).success()
            coEvery {
                riskCalculationConfService.getByHealthConditions(
                    listOf(
                        healthCondition2.id,
                        healthCondition3.id
                    )
                )
            } returns listOf(
                riskCalculationConf2,
                riskCalculationConf3
            ).success()

            val response = service.getDetailCalculationConf(person, riskWithModels.id)
            assertThat(response).isSuccessWithData(expected)

            coVerifyOnce { riskService.get(any()) }
            coVerifyOnce { caseRecordService.getByIds(any()) }
            coVerifyOnce { healthConditionService.findByCodes(any()) }
            coVerifyOnce { riskCalculationConfService.getByHealthConditions(any()) }
        }

    @Test
    fun `#getDetailCalculationConf - should return return most recent when there is a tie and filter different of zero`() =
        runBlocking {
            val riskGroupId = RangeUUID.generate()

            val caseRecord2 = TestModelFactory.buildCaseRecord(
                personId,
                severity = CaseSeverity.DECOMPENSATED,
                description = Disease(Disease.Type.CID_10, "A98"),
                addedAt = LocalDateTime.now().minusDays(10)
            )
            val caseRecord3 = TestModelFactory.buildCaseRecord(
                personId,
                severity = CaseSeverity.DECOMPENSATED,
                description = Disease(Disease.Type.CID_10, "A97"),
                addedAt = LocalDateTime.now()
            )
            val healthCondition2 = TestModelFactory.buildHealthCondition(code = caseRecord2.description.value)
            val healthCondition3 = TestModelFactory.buildHealthCondition(code = caseRecord3.description.value)
            val riskCalculationConf2 = TestModelFactory.buildRiskCalculation(
                healthConditionId = healthCondition2.id,
                riskGroupId = riskGroupId,
                chronicScore = 1,
                severeScore = 1,
                decompensatedScore = 1
            )
            val riskCalculationConf3 = TestModelFactory.buildRiskCalculation(
                healthConditionId = healthCondition3.id,
                riskGroupId = riskGroupId,
                chronicScore = 1,
                severeScore = 1,
                decompensatedScore = 1
            )

            val riskWithModels = risk.copy(
                referencedModels = listOf(
                    Risk.ReferencedModel(
                        id = caseRecord.id, model = ReferenceModel.CASE_RECORD
                    ),
                    Risk.ReferencedModel(
                        id = caseRecord2.id, model = ReferenceModel.CASE_RECORD
                    ),
                    Risk.ReferencedModel(
                        id = caseRecord3.id, model = ReferenceModel.CASE_RECORD
                    )
                )
            )

            val expected = listOf(
                CaseRecordCalculationConfTransport(
                    caseId = caseRecord3.id,
                    score = 3,
                    addedAt = caseRecord3.addedAt,
                    scoreSum = mapOf(ScoreType.CHRONIC to 1, ScoreType.SEVERE to 1, ScoreType.DECOMPENSATED to 1),
                    description = caseRecord3.description
                )
            )

            coEvery { riskService.get(riskWithModels.id) } returns riskWithModels.success()
            coEvery {
                caseRecordService.getByIds(
                    listOf(
                        caseRecord.id,
                        caseRecord2.id,
                        caseRecord3.id
                    )
                )
            } returns listOf(caseRecord, caseRecord2, caseRecord3).success()
            coEvery {
                healthConditionService.findByCodes(
                    listOf(
                        caseRecord.description.value,
                        caseRecord2.description.value,
                        caseRecord3.description.value
                    )
                )
            } returns listOf(healthCondition, healthCondition2, healthCondition3).success()
            coEvery {
                riskCalculationConfService.getByHealthConditions(
                    listOf(
                        healthCondition.id,
                        healthCondition2.id,
                        healthCondition3.id
                    )
                )
            } returns listOf(
                riskCalculationConf,
                riskCalculationConf2,
                riskCalculationConf3
            ).success()

            val response = service.getDetailCalculationConf(person, riskWithModels.id)
            assertThat(response).isSuccessWithData(expected)

            coVerifyOnce { riskService.get(any()) }
            coVerifyOnce { caseRecordService.getByIds(any()) }
            coVerifyOnce { healthConditionService.findByCodes(any()) }
            coVerifyOnce { riskCalculationConfService.getByHealthConditions(any()) }
        }

    @Test
    fun `#getDetailCalculationConf - should return detail calculation conf of person minor of 15 years`() =
        runBlocking {
            val childPerson = person.copy(dateOfBirth = LocalDateTime.now().minusYears(10))
            val riskWithModels = risk.copy(
                referencedModels = listOf(
                    Risk.ReferencedModel(
                        id = caseRecord.id, model = ReferenceModel.CASE_RECORD
                    )
                )
            )
            val caseRecord2 = caseRecord.copy(severity = CaseSeverity.DECOMPENSATED)
            val riskCalculationConf2 = riskCalculationConf.copy(
                chronicScore = 1,
                severeScore = 1,
                decompensatedScore = 1,
                childChronicScore = 2,
                childSevereScore = 2,
                childDecompensatedScore = 2
            )

            val expected = listOf(
                CaseRecordCalculationConfTransport(
                    caseId = caseRecord2.id,
                    score = 6,
                    addedAt = caseRecord2.addedAt,
                    scoreSum = mapOf(ScoreType.CHRONIC to 2, ScoreType.SEVERE to 2, ScoreType.DECOMPENSATED to 2),
                    description = caseRecord.description
                )
            )
            coEvery { riskService.get(riskWithModels.id) } returns riskWithModels.success()
            coEvery { caseRecordService.getByIds(listOf(caseRecord2.id)) } returns listOf(caseRecord2).success()
            coEvery { healthConditionService.findByCodes(listOf(caseRecord2.description.value)) } returns listOf(
                healthCondition
            ).success()
            coEvery { riskCalculationConfService.getByHealthConditions(listOf(healthCondition.id)) } returns listOf(
                riskCalculationConf2
            ).success()

            val response = service.getDetailCalculationConf(childPerson, riskWithModels.id)
            assertThat(response).isSuccessWithData(expected)

            coVerifyOnce { riskService.get(any()) }
            coVerifyOnce { caseRecordService.getByIds(any()) }
            coVerifyOnce { healthConditionService.findByCodes(any()) }
            coVerifyOnce { riskCalculationConfService.getByHealthConditions(any()) }

        }

    @Test
    fun `#getAllByPerson - should return all risk of the person`() = runBlocking {
        coEvery { riskService.getAllByPerson(personId) } returns listOf(risk).success()

        val result = service.getAllByPerson(personId)
        assertThat(result).isSuccessWithData(listOf(risk))

        coVerifyOnce { riskService.getAllByPerson(any()) }
    }

}
