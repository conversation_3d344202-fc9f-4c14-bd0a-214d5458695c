package br.com.alice.member.api.consumers

import br.com.alice.business.client.BeneficiaryService
import br.com.alice.business.events.BeneficiaryOnboardingPhaseChangedEvent
import br.com.alice.common.core.PersonId
import br.com.alice.common.extensions.coFoldNotFound
import br.com.alice.common.extensions.then
import br.com.alice.common.logging.logger
import br.com.alice.member.api.services.AppState.ACTIVATION_CPTS
import br.com.alice.member.api.services.AppState.ACTIVATION_HEALTH_DECLARATIONS
import br.com.alice.member.api.services.AppState.ACTIVATION_HOME
import br.com.alice.member.api.services.AppState.ACTIVATION_REVIEW_TERMS
import br.com.alice.member.api.services.AppState.BENEFICIARY_ONBOARDING_PHASE_CHANGE
import br.com.alice.member.api.services.AppStateNotifier
import com.github.kittinunf.result.Result
import com.github.kittinunf.result.map
import com.github.kittinunf.result.success

class BeneficiaryOnboardingPhaseChangedConsumer(
    private val beneficiaryService: BeneficiaryService,
) : Consumer() {

    suspend fun updateAppState(event: BeneficiaryOnboardingPhaseChangedEvent): Result<Any, Throwable> =
        withSubscribersEnvironment {
            logger.info(
                "BeneficiaryOnboardingPhaseChangedConsumer.updateAppState: Updating app's `BENEFICIARY_ONBOARDING_PHASE_CHANGE` state",
                "event" to event
            )

            beneficiaryService.get(event.payload.beneficiaryId)
                .then { updateByPersonId(it.personId) }
                .then { it.parentPerson?.let { parentPerson -> updateByPersonId(parentPerson) } }
                .map { true }
                .coFoldNotFound { true.success() }
        }

    private fun updateByPersonId(personId: PersonId) {
        AppStateNotifier.updateAppState(personId,
            BENEFICIARY_ONBOARDING_PHASE_CHANGE,
            ACTIVATION_HOME,
            ACTIVATION_HEALTH_DECLARATIONS,
            ACTIVATION_CPTS,
            ACTIVATION_REVIEW_TERMS
        )
    }
}
