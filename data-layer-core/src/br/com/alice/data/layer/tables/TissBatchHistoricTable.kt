package br.com.alice.data.layer.tables

import br.com.alice.common.RangeUUID
import java.math.BigDecimal
import java.time.LocalDate
import java.time.LocalDateTime
import java.util.UUID

internal data class TissBatchHistoricTable(
    override val id: UUID = RangeUUID.generate(),
    val providerId: UUID,
    val valueExpected: BigDecimal?,
    val valueInformed: BigDecimal?,
    val tissBatchId: UUID,
    val itemCode: String,
    val itemName: String?,
    val itemExecutedDate: LocalDate? = null,
    val description: String? = null,
    override val version: Int,
    override val createdAt: LocalDateTime,
    override val updatedAt: LocalDateTime
) : Table<TissBatchHistoricTable> {
    override fun copyTable(version: Int, updatedAt: LocalDateTime, createdAt: LocalDateTime) =
        copy(version = version, createdAt = createdAt, updatedAt = updatedAt)
}
