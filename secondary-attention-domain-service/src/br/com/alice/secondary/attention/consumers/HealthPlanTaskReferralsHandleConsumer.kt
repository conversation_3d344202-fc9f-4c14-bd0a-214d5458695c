package br.com.alice.secondary.attention.consumers

import br.com.alice.common.extensions.getOrNullIfNotFound
import br.com.alice.common.extensions.then
import br.com.alice.common.logging.logger
import br.com.alice.data.layer.models.CounterReferral
import br.com.alice.data.layer.models.HealthPlanTask
import br.com.alice.data.layer.models.HealthPlanTaskReferrals
import br.com.alice.data.layer.models.Referral
import br.com.alice.secondary.attention.client.HealthPlanTaskReferralsService
import br.com.alice.secondary.attention.events.HealthPlanTaskReferralsHandleEvent
import br.com.alice.secondary.attention.events.TaskReferralsEventHandleType
import com.github.kittinunf.result.flatMap
import com.github.kittinunf.result.map
import com.github.kittinunf.result.success

class HealthPlanTaskReferralsHandleConsumer(
    private val healthPlanTaskReferralsService: HealthPlanTaskReferralsService,
) : Consumer() {

    suspend fun handleEventType(event: HealthPlanTaskReferralsHandleEvent) = withSubscribersEnvironment {
        val healthPlanTask = event.payload.healthPlanTask
        val counterReferral = event.payload.counterReferral
        val type = event.payload.type

        logger.info(
            "HealthPlanTaskReferralsHandleConsumer#handleEventReason begin",
            "health_plan_task_id" to healthPlanTask.id,
            "counter_referral_id" to counterReferral?.id,
            "event_type" to type
        )

        if (!healthPlanTask.isReferral()) {
            logger.warn(
                "HealthPlanTaskReferralsHandleConsumer#upsertHealthPlanTaskReferral task is not referral",
                "health_plan_task_id" to healthPlanTask.id,
                "health_plan_task_type" to healthPlanTask.type,
                "health_plan_task_status" to healthPlanTask.status,
            )
            return@withSubscribersEnvironment true.success()
        }

        return@withSubscribersEnvironment when (type) {
            TaskReferralsEventHandleType.UPSERT -> upsertHealthPlanTaskReferral(
                healthPlanTask
            )

            TaskReferralsEventHandleType.INCREMENT_COUNTER_REFERRAL -> incrementCounterReferral(
                healthPlanTask,
                counterReferral
            )

            TaskReferralsEventHandleType.INCREMENT_ADDITIONAL_COUNTER_REFERRAL -> incrementAdditionalCounterReferral(
                healthPlanTask,
                counterReferral
            )
        }
    }

    private suspend fun upsertHealthPlanTaskReferral(
        healthPlanTask: HealthPlanTask
    ) = withSubscribersEnvironment {
        val healthPlanTaskReferral = healthPlanTaskReferralsService
            .getByHealthPlanTaskId(healthPlanTask.id)
            .getOrNullIfNotFound()

        if (healthPlanTaskReferral == null)
            createReferralHealthPlanTaskReferral(healthPlanTask.specialize())
        else
            updateReferralHealthPlanTaskReferral(healthPlanTask.specialize(), healthPlanTaskReferral)
    }

    private suspend fun createReferralHealthPlanTaskReferral(
        healthPlanTask: Referral
    ) = healthPlanTaskReferralsService.create(
        HealthPlanTaskReferrals(
            personId = healthPlanTask.personId,
            healthPlanTaskId = healthPlanTask.id,
            releasedAt = healthPlanTask.releasedAt,
            deadlineAt = healthPlanTask.deadline?.date,
            status = healthPlanTask.status,
            specialty = healthPlanTask.specialty,
            subSpecialty = healthPlanTask.subSpecialty,
            sessionsQuantity = healthPlanTask.getSessions(),
            suggestedSpecialist = healthPlanTask.suggestedSpecialist,
            caseRecordDetails = healthPlanTask.caseRecordDetails,
        )
    ).then {
        logger.info(
            "HealthPlanTaskReferralsHandleConsumer#createReferralHealthPlanTaskReferral",
            "health_plan_task_id" to healthPlanTask.id,
            "health_plan_task_status" to healthPlanTask.status,
        )
    }

    private suspend fun updateReferralHealthPlanTaskReferral(
        healthPlanTask: Referral,
        healthPlanTaskReferral: HealthPlanTaskReferrals
    ) = healthPlanTaskReferralsService.update(
        healthPlanTaskReferral.copy(
            releasedAt = healthPlanTask.releasedAt,
            deadlineAt = healthPlanTask.deadline?.date,
            status = healthPlanTask.status,
            specialty = healthPlanTask.specialty,
            subSpecialty = healthPlanTask.subSpecialty,
            sessionsQuantity = healthPlanTask.getSessions(),
            suggestedSpecialist = healthPlanTask.suggestedSpecialist,
            caseRecordDetails = healthPlanTask.caseRecordDetails,
        )
    ).then {
        logger.info(
            "HealthPlanTaskReferralsHandleConsumer#updateReferralHealthPlanTaskReferral",
            "health_plan_task_id" to healthPlanTask.id,
            "health_plan_task_status" to healthPlanTask.status,
        )
    }

    private suspend fun incrementCounterReferral(
        healthPlanTask: HealthPlanTask,
        counterReferral: CounterReferral?
    ) = withSubscribersEnvironment {
        if (counterReferral == null) {
            logger.warn(
                "HealthPlanTaskReferralsHandleConsumer#incrementCounterReferral counter referral was not sent",
                "health_plan_task_id" to healthPlanTask.id,
            )
            return@withSubscribersEnvironment true.success()
        }

        if (counterReferral.referralId == null) {
            logger.warn(
                "HealthPlanTaskReferralsHandleConsumer#incrementCounterReferral counter referral referralId was not sent",
                "health_plan_task_id" to healthPlanTask.id,
                "counter_referral_id" to counterReferral.id,
            )
            return@withSubscribersEnvironment true.success()
        }

        val healthPlanTaskReferral = healthPlanTaskReferralsService
            .getByAdditionalCounterReferralOrHealthPlanTaskId(counterReferral.referralId!!)
            .getOrNullIfNotFound()

        if (healthPlanTaskReferral != null)
            healthPlanTaskReferralsService
                .incrementCounterReferral(
                    healthPlanTaskReferralId = healthPlanTaskReferral.id,
                    counterReferralId = counterReferral.id,
                    healthCommunitySpecialistId = counterReferral.staffId
                ).map { referral ->
                    if (referral.additionalCounterReferrals.contains(counterReferral.referralId)) {
                        healthPlanTaskReferralsService
                            .getByHealthPlanTaskId(counterReferral.referralId!!)
                            .flatMap {
                                healthPlanTaskReferralsService
                                    .incrementCounterReferral(
                                        healthPlanTaskReferralId = it.id,
                                        counterReferralId = counterReferral.id,
                                        healthCommunitySpecialistId = counterReferral.staffId
                                    )
                            }
                    } else {
                        referral
                    }
                }
        else
            true.success()
    }

    private suspend fun incrementAdditionalCounterReferral(
        healthPlanTask: HealthPlanTask,
        counterReferral: CounterReferral?,
    ) = withSubscribersEnvironment {
        if (counterReferral == null) {
            logger.warn(
                "HealthPlanTaskReferralsHandleConsumer#incrementCounterReferral counter referral was not sent",
                "health_plan_task_id" to healthPlanTask.id,
            )
            return@withSubscribersEnvironment true.success()
        }

        if (counterReferral.referralId == null) {
            logger.warn(
                "HealthPlanTaskReferralsHandleConsumer#incrementCounterReferral counter referral referralId was not sent",
                "health_plan_task_id" to healthPlanTask.id,
                "counter_referral_id" to counterReferral.id,
            )
            return@withSubscribersEnvironment true.success()
        }

        healthPlanTaskReferralsService
            .getByAdditionalCounterReferralOrHealthPlanTaskId(counterReferral.referralId!!)
            .map {
                healthPlanTaskReferralsService.incrementAdditionalCounterReferral(
                    it.id,
                    healthPlanTask.id
                ).get()
            }
    }

}
