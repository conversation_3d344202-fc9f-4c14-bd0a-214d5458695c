{"rules": [{"conditions": ["${subject.opaType} == Unauthenticated", "${subject.key} == ${resource.email}"], "allow": [{"actions": ["view"], "resources": ["StaffModel"]}]}, {"conditions": ["${subject.opaType} == StaffModel"], "allow": [{"resources": ["AppointmentMacro", "StaffModel", "HealthProfessionalModel", "StructuredAddress", "ContactModel", "HealthcareTeamModel", "HealthcareTeamChannel", "HealthcareAdditionalTeam", "PersonModel", "PersonClinicalAccount", "ProductModel", "ProductBundleModel", "PriceListingModel", "ProductPriceListingModel", "CompanyProductPriceListingModel", "ProductGroupModel", "HealthForm", "HealthFormSection", "HealthFormQuestion", "FeatureConfigModel", "BudNode", "Protocol", "ServiceScriptRelationship", "ServiceScriptExecution", "ServiceScriptNavigation", "HealthCondition", "HealthConditionTemplate", "HealthConditionAxis", "HealthConditionRelated", "ServiceScriptAction", "TestCodeModel", "ProviderTestCodeModel", "TestCodePackageModel", "ProviderModel", "ProductModel", "ProviderUnitModel", "StructuredAddress", "PartnerIntegrationProviderUnitModel", "City", "FederativeUnit", "ProviderUnitGroupModel", "ProviderTestCodeDataIntegrationModel", "CboCodeModel", "CompanyModel", "CompanyContractModel", "CompanySubContractModel", "Generic<PERSON><PERSON><PERSON><PERSON>", "CompanyStaffModel", "Beneficiary<PERSON><PERSON>l", "BeneficiaryOnboardingModel", "BeneficiaryOnboardingPhaseModel", "CassiMemberModel", "PersonBillingAccountablePartyModel", "BillingAccountablePartyModel", "MemberModel", "ChannelFup", "HealthFormOutcomeCalculatorConf", "BeneficiaryHubspotModel", "HealthcareResourceModel"], "actions": ["view", "count"]}, {"resources": ["ProviderUnitTestCodeModel", "TestPreparationModel", "InsurancePortabilityHealthInsuranceModel", "InsurancePortabilityRequestModel", "InsurancePortabilityRequestFileModel"], "actions": ["view", "count", "create", "update", "delete"]}, {"resources": ["AppointmentScheduleOptionModel", "HealthGoalModel", "MemberOnboardingTemplate", "MemberOnboardingStep", "MemberOnboardingAction", "MemberOnboardingCheckpoint", "AppointmentScheduleEventTypeModel", "CassiSpecialistModel", "SiteAccreditedNetworkFlagship"], "actions": ["view", "count", "create", "update"]}, {"resources": ["MedicalSpecialtyModel"], "actions": ["view", "count", "create"]}], "branches": [{"conditions": ["${subject.role} @= PRODUCT_TECH_STAFF_EDITOR"], "allow": [{"resources": ["StaffModel", "HealthProfessionalModel", "StructuredAddress"], "actions": ["view", "count", "update", "create"]}, {"resources": ["ContactModel"], "actions": ["view", "count", "update", "create", "delete"]}]}, {"conditions": ["${resource.type} == HEALTH_LOGIC || ${resource.type} == PROGRAM"], "allow": [{"resources": ["ServiceScriptNode"], "actions": ["view", "count"]}]}, {"conditions": ["${subject.role} @= PRODUCT_TECH"], "allow": [{"resources": ["HealthFormAnswerGroup", "PersonInternalReference", "MemberModel", "AliceAgoraWorkingHours", "ChannelTag", "ChannelMacro", "Risk", "OutcomeConf", "TestCodeAnalyteModel", "CsatTemplate"], "actions": ["view", "count"]}, {"resources": ["FeatureConfigModel", "PrescriptionSentenceModel", "HealthForm", "HealthFormSection", "HealthFormQuestion", "HealthCondition", "HealthConditionTemplate", "HealthConditionAxis", "HealthConditionRelated", "TestPreparationModel", "ProviderTestCodeModel", "ProviderTestCodeDataIntegrationModel", "ContactModel", "InsurancePortabilityHealthInsuranceModel", "InsurancePortabilityRequestModel", "InsurancePortabilityRequestFileModel", "LegalGuardianAssociationModel", "LegalGuardianInfoTempModel", "TestCodeAnalyteModel", "SiteAccreditedNetwork", "SalesFirmAgentPartnership"], "actions": ["view", "count", "create", "update", "delete"]}, {"resources": ["DeadletterQueue", "PersonModel", "ExecIndicatorAuthorizerModel", "AppContentScreenDetail", "HealthcareTeamModel", "HealthcareAdditionalTeam", "HealthcareTeamChannel", "TestCodeModel", "TestCodePackageModel", "ProviderModel", "ProductModel", "ProviderUnitModel", "ProviderUnitGroupModel", "City", "FederativeUnit", "ZipcodeAddress", "MedicalSpecialtyModel", "HealthCommunitySpecialistModel", "HealthProfessionalModel", "StructuredAddress", "BillingAccountablePartyModel", "AppointmentScheduleOptionModel", "HealthMeasurementTypeModel", "HealthMeasurementCategoryModel", "ProductBundleModel", "ProductGroupModel", "FhirProviderAccess", "HealthFormOutcomeCalculatorConf", "ClinicalOutcomesConsolidatedCalculatorConf", "HealthProfessionalOpsProfileModel", "ProductRecommendation", "ProcedureProviderModel", "AppointmentTemplate", "ProfessionalTierProcedureValueModel", "UpdateAppRuleModel", "HealthPlanTask", "HealthPlanTaskGroup", "PromoCodeModel", "TussProcedureSpecialtyModel", "HealthcareResourceModel", "HealthcareResourceGroupModel", "HealthcareBundleModel", "HealthcareResourceGroupAssociationModel", "ActionPlanTask", "DemandActionPlan", "SalesFirm", "SalesFirmStaff", "SalesAgent", "CompanyModel", "CompanyContractModel", "CompanySubContractModel", "Generic<PERSON><PERSON><PERSON><PERSON>", "CompanyStaffModel", "SiteAccreditedNetworkProvider", "SiteAccreditedNetworkAddress", "VicProductOption", "FaqGroupModel", "FaqContentModel", "HealthSpecialistResourceBundleModel", "ResourceBundleSpecialtyModel", "ResourceBundleSpecialtyPricingModel", "ResourceBundleSpecialtyPricingUpdateModel"], "actions": ["view", "count", "create", "update"]}, {"resources": ["ResourceBundleSpecialtyPricingModel"], "actions": ["delete"]}, {"resources": ["MedicalSpecialtyModel", "CboCodeModel", "HippocratesHealthcareProfessionalModel"], "actions": ["view", "count", "create"]}, {"resources": ["SpecialistOpinion"], "actions": ["view", "count", "update"]}], "branches": [{"conditions": ["${subject.key} == gas_b2c_allowed_members", "${resource.namespace} == ALICE_APP"], "allow": [{"resources": ["FeatureConfigModel"], "actions": ["view", "create", "update"]}]}]}, {"conditions": ["${subject.role} @= PRODUCT_TECH_HEALTH"], "allow": [{"resources": ["ServiceScriptNode"], "actions": ["view", "count"]}, {"resources": ["FeatureConfigModel", "AliceAgoraWorkingHours", "ChannelTag", "ChannelMacro"], "actions": ["view", "count", "create", "update", "delete"]}, {"resources": ["ServiceScriptRelationship", "BudNode", "Protocol", "ServiceScriptExecution", "ServiceScriptNavigation", "ServiceScriptNode", "HealthPlanTaskTemplate", "HealthPlanTaskGroupTemplate", "RiskGroup", "RiskCalculationConf", "OutcomeConf", "HealthDemandMonitoring", "AppointmentMacro", "CsatTemplate", "ScreenData", "RoutingRule"], "actions": ["view", "count", "create", "update"]}, {"resources": ["HealthForm", "HealthFormSection", "HealthFormQuestion"], "actions": ["view", "count", "create"]}], "branches": [{"conditions": ["${resource.type} == TRIGGER || ${resource.type} == ACTION"], "allow": [{"resources": ["ServiceScriptNode"], "actions": ["view", "create", "update", "count"]}]}]}, {"conditions": ["${subject.role} @= PHYSICIAN"], "allow": [{"resources": ["PrescriptionSentenceModel"], "actions": ["view", "count", "create", "update", "delete"]}]}, {"conditions": ["${subject.role} @= MANAGER_NUTRITIONIST"], "allow": [{"resources": ["ServiceScriptNode"], "actions": ["count"]}, {"resources": ["OutcomeConf", "BudNode", "Protocol", "ServiceScriptRelationship", "ServiceScriptExecution", "ServiceScriptNavigation", "ServiceScriptAction"], "actions": ["view", "count"]}, {"resources": ["ServiceScriptRelationship", "HealthForm", "HealthFormSection", "HealthFormQuestion"], "actions": ["view", "count", "create", "update"]}], "branches": [{"conditions": ["${resource.type} == TRIGGER || ${resource.type} == ACTION"], "allow": [{"resources": ["ServiceScriptNode"], "actions": ["view", "create", "update", "count"]}]}, {"conditions": ["${resource.type} == HEALTH_LOGIC || ${resource.type} == PROGRAM"], "allow": [{"resources": ["ServiceScriptNode"], "actions": ["view", "count"]}]}]}, {"conditions": ["${subject.role} @= NAVIGATOR || ${subject.role} @= NURSE"], "allow": [{"resources": ["PrescriptionSentenceModel"], "actions": ["view", "count", "create", "update", "delete"]}, {"resources": ["MedicineModel", "HealthForm", "HealthFormSection", "HealthFormQuestion", "SalesAgent", "SalesFirmAgentPartnership"], "actions": ["view", "count", "create", "update"]}]}, {"conditions": ["${subject.role} @= DIGITAL_CARE_NURSE"], "allow": [{"resources": ["ServiceScriptNode"], "actions": ["count"]}, {"resources": ["OutcomeConf", "ServiceScriptAction"], "actions": ["view", "count"]}, {"resources": ["ServiceScriptRelationship"], "actions": ["view", "count", "create", "update"]}], "branches": [{"conditions": ["${resource.type} == TRIGGER || ${resource.type} == ACTION"], "allow": [{"resources": ["ServiceScriptNode"], "actions": ["view", "create", "update", "count"]}]}, {"conditions": ["${resource.type} == PROGRAM || ${resource.type} == HEALTH_LOGIC"], "allow": [{"resources": ["ServiceScriptNode"], "actions": ["view", "count"]}]}]}, {"conditions": ["${subject.role} @= DIGITAL_CARE_PHYSICIAN"], "allow": [{"resources": ["ServiceScriptNode"], "actions": ["count"]}, {"resources": ["OutcomeConf", "ServiceScriptRelationship", "ServiceScriptAction"], "actions": ["view", "count"]}, {"resources": ["ServiceScriptRelationship"], "actions": ["view", "create", "update"]}], "branches": [{"conditions": ["${resource.type} == TRIGGER || ${resource.type} == ACTION"], "allow": [{"resources": ["ServiceScriptNode"], "actions": ["view", "create", "update"]}]}, {"conditions": ["${resource.type} == PROGRAM || ${resource.type} == HEALTH_LOGIC"], "allow": [{"resources": ["ServiceScriptNode"], "actions": ["view", "count"]}]}]}, {"conditions": ["${subject.role} @= CARE_COORD_NURSE"], "allow": [{"resources": ["OutcomeConf", "ServiceScriptNode"], "actions": ["view", "count"]}, {"resources": ["CboCodeModel"], "actions": ["view", "count", "create"]}, {"resources": ["ServiceScriptAction", "ServiceScriptExecution", "ServiceScriptNavigation"], "actions": ["view", "create", "update"]}, {"resources": ["BudNode", "HealthCommunitySpecialistModel", "HealthForm", "HealthFormQuestion", "HealthFormSection", "HealthPlanTaskGroupTemplate", "HealthPlanTaskTemplate", "HealthProfessionalModel", "MedicalSpecialtyModel", "MedicineModel", "Protocol", "ServiceScriptRelationship", "ServiceScriptRelationship", "StructuredAddress", "TestCodeModel", "TestCodePackageModel"], "actions": ["view", "count", "create", "update"]}, {"resources": ["AliceAgoraWorkingHours", "ChannelMacro", "ChannelTag", "ContactModel", "PrescriptionSentenceModel", "TestPreparationModel"], "actions": ["view", "count", "create", "update", "delete"]}], "branches": [{"conditions": ["${resource.type} == TRIGGER || ${resource.type} == ACTION || ${resource.type} == PROGRAM || ${resource.type} == HEALTH_LOGIC"], "allow": [{"resources": ["ServiceScriptNode"], "actions": ["view", "create", "update"]}]}]}, {"conditions": ["${subject.role} @= HEALTH_OPS"], "allow": [{"resources": ["AppointmentMacro", "AppointmentScheduleOptionModel", "BudNode", "ChannelFup", "HealthCommunitySpecialistModel", "HealthDemandMonitoring", "HealthForm", "HealthFormOutcomeCalculatorConf", "HealthFormQuestion", "HealthFormSection", "HealthGoalModel", "HealthMeasurementCategoryModel", "HealthMeasurementTypeModel", "HealthPlanTaskGroupTemplate", "HealthPlanTaskTemplate", "HealthProfessionalModel", "HealthcareAdditionalTeam", "HealthcareTeamChannel", "HealthcareTeamModel", "MedicalSpecialtyModel", "MedicineModel", "OutcomeConf", "PersonClinicalAccount", "ProductBundleModel", "ProductGroupModel", "ProductModel", "Protocol", "ServiceScriptNode", "ServiceScriptRelationship", "StructuredAddress", "TestCodeModel", "TestCodePackageModel"], "actions": ["view", "count", "update", "create"]}, {"resources": ["AliceAgoraWorkingHours", "AppointmentTemplate", "ChannelMacro", "ChannelTag", "ContactModel", "FeatureConfigModel", "HealthCondition", "HealthConditionAxis", "HealthConditionRelated", "HealthConditionTemplate", "PrescriptionSentenceModel", "TestCodeAnalyteModel", "TestPreparationModel"], "actions": ["view", "count", "update", "create", "delete"]}, {"resources": ["CboCodeModel"], "actions": ["view", "count", "create"]}, {"resources": ["PersonInternalReference", "PersonModel", "StaffModel"], "actions": ["view", "count"]}, {"resources": ["ClinicalOutcomesConsolidatedCalculatorConf", "ServiceScriptAction", "ServiceScriptExecution", "ServiceScriptNavigation"], "actions": ["view", "update", "create"]}]}, {"conditions": ["${subject.role} @= CARE_COORD_NURSE"], "allow": [{"resources": ["OutcomeConf", "ServiceScriptNode"], "actions": ["view", "count"]}, {"resources": ["CboCodeModel"], "actions": ["view", "count", "create"]}, {"resources": ["ServiceScriptAction", "ServiceScriptExecution", "ServiceScriptNavigation"], "actions": ["view", "create", "update"]}, {"resources": ["BudNode", "HealthCommunitySpecialistModel", "HealthForm", "HealthFormQuestion", "HealthFormSection", "HealthPlanTaskGroupTemplate", "HealthPlanTaskTemplate", "HealthProfessionalModel", "MedicalSpecialtyModel", "MedicineModel", "Protocol", "ServiceScriptRelationship", "ServiceScriptRelationship", "StructuredAddress", "TestCodeModel", "TestCodePackageModel"], "actions": ["view", "count", "create", "update"]}, {"resources": ["AliceAgoraWorkingHours", "ChannelMacro", "ChannelTag", "ContactModel", "PrescriptionSentenceModel", "TestPreparationModel"], "actions": ["view", "count", "create", "update", "delete"]}], "branches": [{"conditions": ["${resource.type} == TRIGGER || ${resource.type} == ACTION || ${resource.type} == PROGRAM || ${resource.type} == HEALTH_LOGIC"], "allow": [{"resources": ["ServiceScriptNode"], "actions": ["view", "create", "update"]}]}]}, {"conditions": ["${subject.role} @= HEALTH_OPS_MULTI || ${subject.role} @= HEALTH_OPS_LEAD"], "allow": [{"resources": ["AppointmentScheduleOptionModel", "CompanyStaffModel", "HealthcareAdditionalTeam", "HealthcareTeamChannel", "HealthcareTeamModel", "HealthCommunitySpecialistModel", "HealthProfessionalModel", "MedicalSpecialtyModel", "PersonClinicalAccount", "ServiceScriptRelationship", "StructuredAddress"], "actions": ["view", "count", "create", "update"]}, {"resources": ["ContactModel"], "actions": ["view", "count", "create", "update", "delete"]}, {"resources": ["CboCodeModel"], "actions": ["view", "count", "create"]}, {"resources": ["ServiceScriptAction"], "actions": ["view", "update", "create"]}, {"resources": ["PersonInternalReference", "PersonModel", "StaffModel"], "actions": ["view", "count"]}, {"resources": ["ServiceScriptNode"], "actions": ["count"]}], "branches": [{"conditions": ["${resource.type} == PROGRAM || ${resource.type} == HEALTH_LOGIC"], "allow": [{"resources": ["ServiceScriptNode"], "actions": ["view", "update", "create"]}]}]}, {"conditions": ["${subject.role} @= MEMBER_OPS"], "allow": [{"resources": ["AppointmentScheduleModel"], "actions": ["view", "count"]}, {"resources": ["InsurancePortabilityRequestModel", "InsurancePortabilityRequestFileModel"], "actions": ["view", "count", "create", "update", "delete"]}]}, {"conditions": ["${subject.role} @= CHIEF_RISK"], "allow": [{"resources": ["HealthcareTeamModel", "HealthcareAdditionalTeam", "PersonModel", "StaffModel", "PersonInternalReference"], "actions": ["view", "count"]}, {"resources": ["PersonClinicalAccount", "ProductModel", "ProductBundleModel", "ProductGroupModel", "BillingAccountablePartyModel", "BeneficiaryOnboardingPhaseModel", "ProviderModel"], "actions": ["view", "count", "create", "update"]}, {"resources": ["HealthCondition", "HealthConditionTemplate", "HealthConditionAxis", "HealthConditionRelated"], "actions": ["view", "count", "create", "update", "delete"]}]}, {"conditions": ["${subject.role} @= RISK_NURSE"], "allow": [{"resources": ["HealthCondition", "HealthConditionTemplate", "HealthConditionAxis", "HealthConditionRelated"], "actions": ["view", "count", "create", "update", "delete"]}, {"resources": ["BeneficiaryOnboardingPhaseModel"], "actions": ["view", "count", "create", "update"]}]}, {"conditions": ["${subject.role} @= MED_RISK"], "allow": [{"resources": ["HealthcareAdditionalTeam", "HealthcareTeamModel", "PersonInternalReference", "PersonModel", "StaffModel"], "actions": ["view", "count"]}, {"resources": ["BillingAccountablePartyModel", "PersonClinicalAccount", "ProductGroupModel", "ProductModel", "ProductBundleModel", "ProductGroupModel", "ProviderModel"], "actions": ["view", "count", "create", "update"]}, {"resources": ["HealthCondition", "HealthConditionAxis", "HealthConditionRelated", "HealthConditionTemplate"], "actions": ["view", "count", "create", "update", "delete"]}]}, {"conditions": ["${subject.role} @= MED_EX"], "allow": [{"resources": ["OutcomeConf", "ServiceScriptNode"], "actions": ["view", "count"]}, {"resources": ["ServiceScriptAction", "ServiceScriptExecution", "ServiceScriptNavigation"], "actions": ["view", "update", "create"]}, {"resources": ["BudNode", "HealthDemandMonitoring", "HealthForm", "HealthFormQuestion", "HealthFormSection", "HealthPlanTaskGroupTemplate", "HealthPlanTaskTemplate", "Protocol", "ServiceScriptRelationship"], "actions": ["count", "view", "update", "create"]}], "branches": [{"conditions": ["${resource.type} == TRIGGER || ${resource.type} == ACTION"], "allow": [{"resources": ["ServiceScriptNode"], "actions": ["view", "update", "create"]}]}]}, {"conditions": ["${subject.role} @= OPS || ${subject.role} @= B2B_OPS"], "allow": [{"resources": ["HealthcareAdditionalTeam", "HealthcareTeamModel", "PersonInternalReference", "PersonModel", "StaffModel"], "actions": ["view", "count"]}, {"resources": ["BillingAccountablePartyModel", "CompanyModel", "CompanyStaffModel", "PersonClinicalAccount", "ProductBundleModel", "ProductGroupModel", "ProductModel", "ProviderModel", "SalesAgent", "SalesFirm", "SalesFirmAgentPartnership", "SalesFirmStaff"], "actions": ["view", "count", "update", "create"]}, {"resources": ["InsurancePortabilityHealthInsuranceModel", "InsurancePortabilityRequestFileModel", "InsurancePortabilityRequestModel"], "actions": ["view", "count", "update", "create", "delete"]}]}, {"conditions": ["${subject.role} @= HEALTH_COMMUNITY"], "allow": [{"resources": ["ProductModel"], "actions": ["view", "count"]}, {"resources": ["CboCodeModel"], "actions": ["view", "count", "create"]}, {"resources": ["City", "FederativeUnit", "HealthCommunitySpecialistModel", "HealthProfessionalModel", "MedicalSpecialtyModel", "ProviderModel", "ProviderUnitGroupModel", "ProviderUnitModel", "StructuredAddress", "StructuredAddress", "ZipcodeAddress"], "actions": ["view", "count", "update", "create"]}, {"resources": ["ContactModel", "PartnerIntegrationProviderUnitModel"], "actions": ["view", "count", "update", "create", "delete"]}]}, {"conditions": ["${subject.role} @= INSURANCE_OPS_HEALTH_INSTITUTION_OPS || ${subject.role} @= INSURANCE_OPS_COMMUNITY_SUCCESS"], "allow": [{"resources": ["HealthcareAdditionalTeam", "HealthcareResourceModel", "HealthcareTeamModel", "PersonClinicalAccount", "PersonInternalReference", "PersonModel", "StaffModel"], "actions": ["view", "count"]}, {"resources": ["ProcedureProviderModel"], "actions": ["view", "update", "create"]}, {"resources": ["AppointmentScheduleOptionModel", "City", "ExecIndicatorAuthorizerModel", "FederativeUnit", "HealthCommunitySpecialistModel", "HealthProfessionalModel", "HealthSpecialistResourceBundleModel", "PersonClinicalAccount", "ProductBundleModel", "ProductGroupModel", "ProductModel", "ProviderModel", "ProviderUnitGroupModel", "ProviderUnitModel", "ResourceBundleSpecialtyModel", "ResourceBundleSpecialtyPricingModel", "ResourceBundleSpecialtyPricingUpdateModel", "StructuredAddress", "StructuredAddress", "TestCodeModel", "TestCodePackageModel", "TussProcedureSpecialtyModel", "ZipcodeAddress"], "actions": ["view", "count", "update", "create"]}, {"resources": ["ResourceBundleSpecialtyPricingModel"], "actions": ["delete"]}, {"resources": ["ContactModel", "PartnerIntegrationProviderUnitModel", "ProviderTestCodeDataIntegrationModel", "ProviderTestCodeModel", "TestPreparationModel"], "actions": ["view", "count", "update", "create", "delete"]}]}, {"conditions": ["${subject.role} @= HEALTH_PROFESSIONAL || ${subject.role} @= NAVIGATOR"], "allow": [{"resources": ["HealthPlanTaskTemplate", "HealthMeasurementTypeModel", "HealthMeasurementCategoryModel", "ChannelFup", "HealthPlanTaskGroupTemplate"], "actions": ["view", "count", "update", "create"]}, {"resources": ["AppointmentTemplate"], "actions": ["view", "count", "update", "create", "delete"]}], "branches": [{"conditions": ["${subject.id} == ${resource.staffId}"], "allow": [{"resources": ["HealthProfessionalModel"], "actions": ["view", "count", "update"]}]}, {"conditions": ["${subject.id} == ${resource.id}"], "allow": [{"resources": ["StaffModel"], "actions": ["view", "count", "update"]}]}]}, {"conditions": ["${subject.role} @= NAVIGATOR || ${subject.role} @= CHIEF_DIGITAL_CARE_NURSE || ${subject.role} @= QUALITY_NURSE"], "allow": [{"resources": ["PersonInternalReference", "MemberModel", "Timeline", "Appointment", "AppointmentEvolution", "AssistanceCare", "CounterReferral", "ExternalHealthInformation", "TestResultFileModel", "TertiaryIntentionTouchPoint"], "actions": ["view"]}, {"resources": ["PersonModel", "TestCodeModel", "TestCodePackageModel"], "actions": ["view", "count", "update", "create"]}, {"resources": ["TestPreparationModel"], "actions": ["view", "count", "update", "create", "delete"]}], "branches": [{"conditions": ["${resource.domain} == interop", "${resource.namespace} == exam_by_provider"], "allow": [{"resources": ["FileVault"], "actions": ["view"]}]}, {"conditions": ["${resource.domain} == eita", "${resource.namespace} == provider_health_document"], "allow": [{"resources": ["FileVault"], "actions": ["view"]}]}]}, {"conditions": ["${subject.role} @= CHIEF_NAVIGATOR"], "allow": [{"resources": ["ChannelTag", "ChannelMacro"], "actions": ["view", "count", "update", "create", "delete"]}]}, {"conditions": ["${subject.role} @= HEALTH_PROFESSIONAL"], "allow": [{"resources": ["HealthcareTeamModel", "HealthcareTeamChannel", "HealthcareAdditionalTeam", "PersonModel", "StaffModel", "PersonInternalReference", "BudNode", "Protocol", "ServiceScriptRelationship", "ServiceScriptExecution", "ServiceScriptNavigation"], "actions": ["view", "count"]}, {"resources": ["PersonClinicalAccount"], "actions": ["view", "count", "update", "create"]}, {"resources": ["ServiceScriptNode", "ServiceScriptRelationship"], "actions": ["count"]}], "branches": [{"conditions": ["${resource.type} == TRIGGER || ${resource.type} == ACTION"], "allow": [{"resources": ["ServiceScriptNode", "ServiceScriptRelationship"], "actions": ["view"]}]}, {"conditions": ["${resource.type} == PROGRAM || ${resource.type} == HEALTH_LOGIC"], "allow": [{"resources": ["ServiceScriptNode", "ServiceScriptRelationship", "ServiceScriptAction"], "actions": ["view", "count"]}]}]}, {"conditions": ["${subject.role} @= CHIEF_DIGITAL_CARE_NURSE || ${subject.role} @= CHIEF_DIGITAL_CARE_PHYSICIAN"], "allow": [{"resources": ["HealthcareTeamModel", "HealthcareTeamChannel", "HealthcareAdditionalTeam", "TestCodeModel", "TestCodePackageModel"], "actions": ["view", "count", "create", "update"]}, {"resources": ["TestPreparationModel", "HealthCondition", "HealthConditionTemplate", "HealthConditionAxis", "HealthConditionRelated"], "actions": ["view", "count", "create", "update", "delete"]}]}, {"conditions": ["${subject.role} @= CHIEF_NAVIGATOR || ${subject.role} @= CHIEF_DIGITAL_CARE_NURSE"], "allow": [{"resources": ["PersonInternalReference", "MemberModel", "ServiceScriptNode", "StaffModel", "ProviderUnitModel"], "actions": ["view"]}, {"resources": ["ProductModel", "RoutingRule"], "actions": ["view", "count"]}, {"resources": ["HippocratesHealthcareProfessionalModel"], "actions": ["view", "count", "create"]}, {"resources": ["PersonModel", "TestCodeModel", "TestCodePackageModel", "HealthcareTeamModel", "HealthcareAdditionalTeam", "HealthcareTeamChannel", "BudNode", "Protocol", "ServiceScriptRelationship", "ServiceScriptExecution", "ServiceScriptNavigation", "ProviderModel", "ProviderUnitModel", "StructuredAddress", "City", "FederativeUnit", "ZipcodeAddress", "ProviderUnitGroupModel", "HealthCommunitySpecialistModel", "HealthProfessionalModel", "TussProcedureSpecialtyModel", "HealthcareResourceModel", "HealthcareResourceGroupModel", "HealthcareBundleModel", "HealthcareResourceGroupAssociationModel"], "actions": ["view", "count", "create", "update"]}, {"resources": ["TestPreparationModel", "FeatureConfigModel", "PartnerIntegrationProviderUnitModel", "ContactModel"], "actions": ["view", "count", "create", "update", "delete"]}], "branches": [{"conditions": ["${resource.name} == Chat ADM"], "allow": [{"resources": ["RoutingRule"], "actions": ["update"]}]}]}, {"conditions": ["${subject.role} @= INSURANCE_OPS_HEALTH_INSTITUTION_OPS"], "allow": [{"resources": ["MemberModel", "StaffModel", "ProviderUnitModel"], "actions": ["view"]}, {"resources": ["ServiceScriptNode", "ServiceScriptRelationship"], "actions": ["count"]}, {"resources": ["HealthcareTeamModel", "HealthcareTeamChannel"], "actions": ["view", "count"]}, {"resources": ["HippocratesHealthcareProfessionalModel", "CboCodeModel"], "actions": ["view", "count", "create"]}, {"resources": ["TestCodeModel", "TestCodePackageModel", "ProviderModel", "ProviderUnitModel", "StructuredAddress", "City", "FederativeUnit", "ZipcodeAddress", "ProviderUnitGroupModel", "HealthCommunitySpecialistModel", "HealthProfessionalModel", "AppointmentScheduleOptionModel", "ProductModel", "ProductBundleModel", "ProductGroupModel", "MedicalSpecialtyModel", "TussProcedureSpecialtyModel", "HippocratesHealthcareProfessionalModel", "HealthcareResourceModel", "HealthcareResourceGroupModel", "HealthcareBundleModel", "HealthcareResourceGroupAssociationModel"], "actions": ["view", "count", "create", "update"]}, {"resources": ["ProviderTestCodeModel", "ProviderTestCodeDataIntegrationModel", "TestPreparationModel", "PartnerIntegrationProviderUnitModel", "ContactModel"], "actions": ["view", "count", "create", "update", "delete"]}], "branches": [{"conditions": ["${resource.type} == TRIGGER || ${resource.type} == ACTION"], "allow": [{"resources": ["ServiceScriptNode", "ServiceScriptRelationship"], "actions": ["view"]}]}, {"conditions": ["${resource.type} == PROGRAM || ${resource.type} == HEALTH_LOGIC"], "allow": [{"resources": ["ServiceScriptNode", "ServiceScriptRelationship", "ServiceScriptAction"], "actions": ["view", "count"]}]}, {"conditions": ["${resource.type} == COMMUNITY_SPECIALIST || ${resource.type} == HEALTH_ADMINISTRATIVE"], "allow": [{"resources": ["StaffModel"], "actions": ["view", "count", "update", "create"]}]}]}, {"conditions": ["${subject.role} @= INSURANCE_OPS_COMMUNITY_SUCCESS"], "allow": [{"resources": ["HealthForm", "HealthFormSection", "HealthFormQuestion", "HealthPlanTaskTemplate", "HealthPlanTaskGroupTemplate"], "actions": ["view", "count", "create", "update"]}]}, {"conditions": ["${subject.role} @= TECHNIQUE_NURSE"], "allow": [{"resources": ["MemberModel", "PersonInternalReference"], "actions": ["view"]}, {"resources": ["TestCodeModel", "ProviderTestCodeModel", "TestCodePackageModel"], "actions": ["view", "count", "create", "update"]}, {"resources": ["TestPreparationModel"], "actions": ["view", "count", "create", "update", "delete"]}]}, {"conditions": ["${subject.role} @= CX_OPS"], "allow": [{"resources": ["HealthCommunitySpecialistModel", "PersonInternalReference", "TestCodeModel", "MemberModel", "ProviderTestCodeModel"], "actions": ["view", "count"]}]}]}, {"conditions": ["${subject.opaType} == EmailDomain"], "allow": [{"resources": ["HealthcareResourceModel"], "actions": ["view", "count"]}]}]}