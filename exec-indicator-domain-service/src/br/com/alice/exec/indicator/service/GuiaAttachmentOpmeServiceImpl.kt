package br.com.alice.exec.indicator.service

import br.com.alice.common.extensions.coResultOf
import br.com.alice.common.extensions.then
import br.com.alice.common.extensions.thenError
import br.com.alice.common.kafka.interfaces.KafkaProducerService
import br.com.alice.common.logging.logger
import br.com.alice.data.layer.models.TotvsGuia
import br.com.alice.data.layer.models.TotvsGuiaOrigin
import br.com.alice.exec.indicator.client.GuiaAttachmentOpmeService
import br.com.alice.exec.indicator.client.HealthcareResourceService
import br.com.alice.exec.indicator.client.NullvsIntegrationService
import br.com.alice.exec.indicator.client.TotvsGuiaService
import br.com.alice.exec.indicator.converters.GuiaAttachmentsOpmeConverter.toAttachmentOpme
import br.com.alice.exec.indicator.converters.GuiaAttachmentsOpmeConverter.toGuiaCreationRequest
import br.com.alice.exec.indicator.converters.GuiaHospitalizationConverter.toBeneficiary
import br.com.alice.exec.indicator.events.AttachmentOpmeCreatedEvent
import br.com.alice.exec.indicator.events.AttachmentOpmeCreatedPayload
import br.com.alice.exec.indicator.metrics.Metrics.Status.SUCCESS
import br.com.alice.exec.indicator.metrics.Metrics.incrementAttachmentOpmeCreated
import br.com.alice.exec.indicator.models.GuiaAttachmentOpmeCreationRequest
import br.com.alice.exec.indicator.models.GuiaOrigin
import br.com.alice.exec.indicator.service.internal.AttachmentOpmeService
import br.com.alice.exec.indicator.service.internal.InternalGuiaService
import com.github.kittinunf.result.map
import java.util.UUID

class GuiaAttachmentOpmeServiceImpl(
    private val totvsGuiaService: TotvsGuiaService,
    private val attachmentOpmeService: AttachmentOpmeService,
    private val internalGuiaService: InternalGuiaService,
    private val healthcareResourcesService: HealthcareResourceService,
    private val kafkaProducerService: KafkaProducerService,
    private val nullvsIntegrationService: NullvsIntegrationService,
) : GuiaAttachmentOpmeService {

    override suspend fun create(
        referenceTotvsGuiaId: UUID,
        request: GuiaAttachmentOpmeCreationRequest,
    ) = coResultOf<TotvsGuia, Throwable> {
        val referenceTotvsGuia = totvsGuiaService
            .get(referenceTotvsGuiaId).get()
        val healthcareResources = healthcareResourcesService
            .findByCodes(request.procedures.map { it.procedureId }).get()
        val guiaCreationRequest = request.toGuiaCreationRequest(referenceTotvsGuia)

        internalGuiaService.getPersonAndCreateTotvsGuia(guiaCreationRequest, TotvsGuiaOrigin.EITA)
            .map { (totvsGuia, person) ->
                val attachmentOpme = request.toAttachmentOpme(totvsGuia, healthcareResources)

                attachmentOpmeService.add(attachmentOpme)
                    .then { incrementAttachmentOpmeCreated(SUCCESS) }
                    .then {
                        logger.info(
                            "GuiaAttachmentOpmeServiceImpl::create() - success",
                            "id" to it.id,
                        )
                    }
                    .thenError {
                        logger.error(
                            "GuiaAttachmentOpmeServiceImpl::create() - failed",
                            it,
                        )
                    }

                nullvsIntegrationService.getByPersonId(person.id)
                    .map { externalId ->
                        kafkaProducerService.produce(
                            AttachmentOpmeCreatedEvent(
                                AttachmentOpmeCreatedPayload(
                                    attachmentOpme,
                                    totvsGuia,
                                    referenceTotvsGuia,
                                    person.toBeneficiary(
                                        newBorn = referenceTotvsGuia.newBorn,
                                        externalId = externalId
                                    ),
                                    GuiaOrigin.EITA,
                                )
                            )
                        )
                    }

                totvsGuia
            }.get()

    }
}
