package br.com.alice.data.layer.services

import br.com.alice.common.rfc.RemoteService
import br.com.alice.common.rfc.Service
import br.com.alice.common.service.data.client.Adder
import br.com.alice.common.service.data.client.CountByValues
import br.com.alice.common.service.data.client.Counter
import br.com.alice.common.service.data.client.Deleter
import br.com.alice.common.service.data.client.Finder
import br.com.alice.common.service.data.client.Getter
import br.com.alice.common.service.data.client.Updater
import br.com.alice.common.service.data.dsl.Field
import br.com.alice.common.service.data.dsl.Predicate
import br.com.alice.common.service.data.dsl.Predicate.Companion.ContainsPredicateUsage
import br.com.alice.common.service.data.dsl.Query
import br.com.alice.common.service.data.dsl.QueryBuilder
import br.com.alice.data.layer.models.TestCodePackageModel
import com.github.kittinunf.result.Result
import java.util.UUID

@RemoteService
interface TestCodePackageModelDataService : Service,
    Finder<TestCodePackageModelDataService.FieldOptions, TestCodePackageModelDataService.OrderingOptions, TestCodePackageModel>,
    Counter<TestCodePackageModelDataService.FieldOptions, TestCodePackageModelDataService.OrderingOptions, TestCodePackageModel>,
    Adder<TestCodePackageModel>,
    Getter<TestCodePackageModel>,
    Updater<TestCodePackageModel>,
    Deleter<TestCodePackageModel> {

    override val namespace: String
        get() = "ehr"
    override val serviceName: String
        get() = "test_code_package"

    class SearchTokenField : Field.TextField(TestCodePackageModel::searchTokens) {
        fun search(value: String) = Predicate.search(this, value)
    }

    class NameField : Field.TextField(TestCodePackageModel::name)

    class TestCodeIdsField : Field.UUIDField(TestCodePackageModel::testCodeIds) {
        @OptIn(ContainsPredicateUsage::class) // TODO: create index
        fun contains(value: UUID) = Predicate.contains(this, value)
    }

    class FieldOptions {
        val searchTokens = SearchTokenField()
        val testCodeIds = TestCodeIdsField()
    }

    class OrderingOptions {
        val name = NameField()
    }

    override fun queryBuilder() = QueryBuilder(
        FieldOptions(),
        OrderingOptions()
    )

    override suspend fun get(id: UUID): Result<TestCodePackageModel, Throwable>
    override suspend fun countGroupedByQuery(query: Query): Result<List<CountByValues>, Throwable>
    override suspend fun countByQuery(query: Query): Result<Int, Throwable>
    override suspend fun existsByQuery(query: Query): Result<Boolean, Throwable>
    override suspend fun add(model: TestCodePackageModel): Result<TestCodePackageModel, Throwable>
    override suspend fun update(model: TestCodePackageModel): Result<TestCodePackageModel, Throwable>
    override suspend fun findByQuery(query: Query): Result<List<TestCodePackageModel>, Throwable>
    override suspend fun delete(model: TestCodePackageModel): Result<Boolean, Throwable>

}
