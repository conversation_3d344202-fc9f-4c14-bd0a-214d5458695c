{"id": "2d3c7696-8f23-4b55-9dd8-741db9e8d280", "code": {"text": "Resultado de Exame Laboratorial", "coding": [{"code": "11502-2", "system": "http://loinc.org", "display": "Laboratory report"}]}, "meta": {"profile": ["https://interoperabilidade.dasa.com.br/fhir/StructureDefinition/DiagnosticReport"]}, "result": [{"type": "Observation", "reference": "urn:uuid:8c2e8455-9151-4fa8-8caa-4d34ddb63817"}, {"type": "Observation", "reference": "urn:uuid:5ec5e901-9750-4484-b365-69a6f2b393ef"}, {"type": "Observation", "reference": "urn:uuid:34b5478d-9914-48b0-a455-6a262035787d"}, {"type": "Observation", "reference": "urn:uuid:61267910-19be-4f30-a1e1-674bf77bb041"}, {"type": "Observation", "reference": "urn:uuid:a8a95e53-d41d-4c9b-9ff8-9b7dff5fb0e9"}, {"type": "Observation", "reference": "urn:uuid:cbd8acbd-9a8f-4746-b0b6-79391f4ad69b"}], "status": "final", "basedOn": [{"type": "ServiceRequest", "reference": "urn:uuid:e917046a-c54e-4dd7-a55f-ad02eda5e5c0"}, {"type": "ServiceRequest", "reference": "urn:uuid:973a6481-0d24-41eb-9e03-a3b7753e5bf8"}, {"type": "ServiceRequest", "reference": "urn:uuid:9a4b428f-6bb4-49a0-a162-d47b09cace8b"}, {"type": "ServiceRequest", "reference": "urn:uuid:127be12d-ce47-471a-911a-0ac163726e0c"}, {"type": "ServiceRequest", "reference": "urn:uuid:36e2a37b-d172-4f96-a783-3a2329f83cdf"}, {"type": "ServiceRequest", "reference": "urn:uuid:1014c316-d3d0-4a4b-93b5-533408aa125d"}], "subject": {"type": "Patient", "display": "@nickname @lastname", "reference": "urn:uuid:3f3d409a-fb67-404d-9c1f-9c89b52e0a40"}, "category": [{"text": "Laboratório", "coding": [{"code": "LAB", "system": "http://terminology.hl7.org/CodeSystem/v2-0074", "display": "Laboratory"}]}], "contained": [{"id": "3f3d409a-fb67-404d-9c1f-9c89b52e0a40", "meta": {"profile": ["https://interoperabilidade.dasa.com.br/fhir/StructureDefinition/Patient"]}, "name": [{"use": "official", "text": "@nickname @lastname"}, {"use": "usual"}], "gender": "unknown", "telecom": [{"use": "mobile", "value": "11 97666173", "system": "phone"}], "birthDate": "1974-07-24", "identifier": [{"use": "usual", "value": "34229798", "system": "https://interoperabilidade.dasa.com.br/fhir/NamingSystem/dasa-mdm-paciente-id"}, {"use": "official", "value": "@nationalid", "system": "https://interoperabilidade.dasa.com.br/fhir/NamingSystem/govbr-receitafederal-pessoafisica-id"}, {"use": "usual", "value": "**********", "system": "https://interoperabilidade.dasa.com.br/fhir/NamingSystem/dasa-motion-paciente-codigoDestino"}], "resourceType": "Patient"}, {"id": "8c2e8455-9151-4fa8-8caa-4d34ddb63817", "code": {"text": "Uré<PERSON>", "coding": [{"code": "40302580", "system": "https://interoperabilidade.dasa.com.br/fhir/CodeSystem/ans-tuss-procedimento-codigo"}, {"code": "4141", "system": "https://interoperabilidade.dasa.com.br/fhir/CodeSystem/dasa-motion-exames-codigoOrigem", "display": "Uré<PERSON>"}]}, "meta": {"profile": ["https://interoperabilidade.dasa.com.br/fhir/StructureDefinition/Observation"]}, "note": [{"text": "Para mais informações consulte o laudo em pdf."}], "status": "final", "basedOn": [{"type": "ServiceRequest", "reference": "urn:uuid:127be12d-ce47-471a-911a-0ac163726e0c"}], "subject": {"type": "Patient", "display": "@nickname @lastname", "reference": "urn:uuid:3f3d409a-fb67-404d-9c1f-9c89b52e0a40"}, "category": [{"text": "Laboratory", "coding": [{"code": "laboratory", "system": "http://terminology.hl7.org/CodeSystem/observation-category", "display": "Laboratory"}]}], "encounter": {"type": "Encounter", "reference": "urn:uuid:56a3cf06-5006-439c-955e-08b76afebfb8"}, "performer": [{"type": "Practitioner", "display": "<PERSON>. <PERSON><PERSON><PERSON><PERSON>mpo Neto CRM- SP 102037"}, {"type": "Practitioner", "display": "Sob a responsabilidade do Dr. Cristovam Scapulatempo Neto - CRM nº 102047"}], "identifier": [{"use": "usual", "value": "************-4141", "system": "https://interoperabilidade.dasa.com.br/fhir/NamingSystem/dasa-motion-requisicao-codigoOrigem"}], "valueString": "28,0|mg/dL", "resourceType": "Observation", "referenceRange": [{"low": {"unit": "mg/dL", "value": 19.3}, "high": {"unit": "mg/dL", "value": 49.2}, "text": "de 19,3 até 49,2\tmg/dL"}], "effectiveDateTime": "2022-08-27T21:15:53-03:00"}, {"id": "5ec5e901-9750-4484-b365-69a6f2b393ef", "code": {"text": "<PERSON><PERSON><PERSON><PERSON>", "coding": [{"code": "40302318", "system": "https://interoperabilidade.dasa.com.br/fhir/CodeSystem/ans-tuss-procedimento-codigo"}, {"code": "3002", "system": "https://interoperabilidade.dasa.com.br/fhir/CodeSystem/dasa-motion-exames-codigoOrigem", "display": "<PERSON><PERSON><PERSON><PERSON>"}]}, "meta": {"profile": ["https://interoperabilidade.dasa.com.br/fhir/StructureDefinition/Observation"]}, "note": [{"text": "Para mais informações consulte o laudo em pdf."}], "status": "final", "basedOn": [{"type": "ServiceRequest", "reference": "urn:uuid:973a6481-0d24-41eb-9e03-a3b7753e5bf8"}], "subject": {"type": "Patient", "display": "@nickname @lastname", "reference": "urn:uuid:3f3d409a-fb67-404d-9c1f-9c89b52e0a40"}, "category": [{"text": "Laboratory", "coding": [{"code": "laboratory", "system": "http://terminology.hl7.org/CodeSystem/observation-category", "display": "Laboratory"}]}], "encounter": {"type": "Encounter", "reference": "urn:uuid:56a3cf06-5006-439c-955e-08b76afebfb8"}, "performer": [{"type": "Practitioner", "display": "<PERSON>. <PERSON><PERSON><PERSON><PERSON>mpo Neto CRM- SP 102037"}, {"type": "Practitioner", "display": "Sob a responsabilidade do Dr. Cristovam Scapulatempo Neto - CRM nº 102047"}], "identifier": [{"use": "usual", "value": "************-3002", "system": "https://interoperabilidade.dasa.com.br/fhir/NamingSystem/dasa-motion-requisicao-codigoOrigem"}], "valueString": "4,7|mmol/L", "resourceType": "Observation", "referenceRange": [{"low": {"unit": "mmol/L", "value": 3.5}, "high": {"unit": "mmol/L", "value": 5.1}, "text": "3,5 a 5,1\tmmol/L"}], "effectiveDateTime": "2022-08-27T21:15:53-03:00"}, {"id": "34b5478d-9914-48b0-a455-6a262035787d", "code": {"text": "Proteína na Urina", "coding": [{"code": "40302377", "system": "https://interoperabilidade.dasa.com.br/fhir/CodeSystem/ans-tuss-procedimento-codigo"}, {"code": "3053", "system": "https://interoperabilidade.dasa.com.br/fhir/CodeSystem/dasa-motion-exames-codigoOrigem", "display": "Proteína na Urina Amostra Isolada"}]}, "meta": {"profile": ["https://interoperabilidade.dasa.com.br/fhir/StructureDefinition/Observation"]}, "note": [{"text": "Para mais informações consulte o laudo em pdf."}], "status": "final", "basedOn": [{"type": "ServiceRequest", "reference": "urn:uuid:9a4b428f-6bb4-49a0-a162-d47b09cace8b"}], "subject": {"type": "Patient", "display": "@nickname @lastname", "reference": "urn:uuid:3f3d409a-fb67-404d-9c1f-9c89b52e0a40"}, "category": [{"text": "Laboratory", "coding": [{"code": "laboratory", "system": "http://terminology.hl7.org/CodeSystem/observation-category", "display": "Laboratory"}]}], "encounter": {"type": "Encounter", "reference": "urn:uuid:56a3cf06-5006-439c-955e-08b76afebfb8"}, "performer": [{"type": "Practitioner", "display": "Dr<PERSON><PERSON> <PERSON><PERSON>e CRM - SP 149388"}, {"type": "Practitioner", "display": "Sob a responsabilidade do Dr. Cristovam Scapulatempo Neto - CRM nº 102047"}], "identifier": [{"use": "usual", "value": "************-3053", "system": "https://interoperabilidade.dasa.com.br/fhir/NamingSystem/dasa-motion-requisicao-codigoOrigem"}], "valueString": "0,08|g/L", "resourceType": "Observation", "referenceRange": [{"text": "0,01 - 0,12 g/L"}], "effectiveDateTime": "2022-08-27T21:15:53-03:00"}, {"id": "61267910-19be-4f30-a1e1-674bf77bb041", "code": {"text": "C<PERSON><PERSON><PERSON>", "coding": [{"code": "40301630", "system": "https://interoperabilidade.dasa.com.br/fhir/CodeSystem/ans-tuss-procedimento-codigo"}, {"code": "12175", "system": "https://interoperabilidade.dasa.com.br/fhir/CodeSystem/dasa-motion-exames-codigoOrigem", "display": "Estimativa de Filtração Glomerular - CREA"}]}, "meta": {"profile": ["https://interoperabilidade.dasa.com.br/fhir/StructureDefinition/Observation"]}, "note": [{"text": "Para mais informações consulte o laudo em pdf."}], "status": "final", "basedOn": [{"type": "ServiceRequest", "reference": "urn:uuid:e917046a-c54e-4dd7-a55f-ad02eda5e5c0"}], "subject": {"type": "Patient", "display": "@nickname @lastname", "reference": "urn:uuid:3f3d409a-fb67-404d-9c1f-9c89b52e0a40"}, "category": [{"text": "Laboratory", "coding": [{"code": "laboratory", "system": "http://terminology.hl7.org/CodeSystem/observation-category", "display": "Laboratory"}]}], "component": [{"code": {"text": "C<PERSON><PERSON><PERSON>", "coding": [{"code": "1078", "system": "https://interoperabilidade.dasa.com.br/fhir/CodeSystem/dasa-motion-exames-codigoOrigem", "display": "C<PERSON><PERSON><PERSON>"}]}, "valueString": "1,31|mg/dL", "referenceRange": [{"low": {"unit": "mg/dL", "value": 0.7}, "high": {"unit": "mg/dL", "value": 1.3}, "text": "0,70 a 1,30\tmg/dL"}]}, {"code": {"text": "*eGFR - Afro Descendente", "coding": [{"code": "12171", "system": "https://interoperabilidade.dasa.com.br/fhir/CodeSystem/dasa-motion-exames-codigoOrigem", "display": "Estimativa de filtração Afro descendente"}]}, "valueString": "74|mL/min/1,73m²", "referenceRange": [{"low": {"unit": "mL/min/1,73m²", "value": 90}, "high": {"unit": "mL/min/1,73m²"}, "text": "Superior a 90\tmL/min/1,73m²"}]}, {"code": {"text": "*eGFR - Não Afro Descendente", "coding": [{"code": "12172", "system": "https://interoperabilidade.dasa.com.br/fhir/CodeSystem/dasa-motion-exames-codigoOrigem", "display": "Estimativa de filtração -Não Afro descendente"}]}, "valueString": "64|mL/min/1,73m²", "referenceRange": [{"low": {"unit": "mL/min/1,73m²", "value": 90}, "high": {"unit": "mL/min/1,73m²"}, "text": "Superior a 90\tmL/min/1,73m²"}]}], "encounter": {"type": "Encounter", "reference": "urn:uuid:56a3cf06-5006-439c-955e-08b76afebfb8"}, "performer": [{"type": "Practitioner", "display": "<PERSON>. <PERSON><PERSON><PERSON><PERSON>mpo Neto CRM- SP 102037"}, {"type": "Practitioner", "display": "Sob a responsabilidade do Dr. Cristovam Scapulatempo Neto - CRM nº 102047"}], "identifier": [{"use": "usual", "value": "************-12175", "system": "https://interoperabilidade.dasa.com.br/fhir/NamingSystem/dasa-motion-requisicao-codigoOrigem"}], "resourceType": "Observation", "effectiveDateTime": "2022-08-27T21:15:53-03:00"}, {"id": "a8a95e53-d41d-4c9b-9ff8-9b7dff5fb0e9", "code": {"text": "Hemoglobina Glicada e Glicose Média Estimada", "coding": [{"code": "40302733", "system": "https://interoperabilidade.dasa.com.br/fhir/CodeSystem/ans-tuss-procedimento-codigo"}, {"code": "13434", "system": "https://interoperabilidade.dasa.com.br/fhir/CodeSystem/dasa-motion-exames-codigoOrigem", "display": "Hemoglobina Glicada e Glicose Média Estimada"}]}, "meta": {"profile": ["https://interoperabilidade.dasa.com.br/fhir/StructureDefinition/Observation"]}, "note": [{"text": "Para mais informações consulte o laudo em pdf."}], "status": "final", "basedOn": [{"type": "ServiceRequest", "reference": "urn:uuid:1014c316-d3d0-4a4b-93b5-533408aa125d"}], "subject": {"type": "Patient", "display": "@nickname @lastname", "reference": "urn:uuid:3f3d409a-fb67-404d-9c1f-9c89b52e0a40"}, "category": [{"text": "Laboratory", "coding": [{"code": "laboratory", "system": "http://terminology.hl7.org/CodeSystem/observation-category", "display": "Laboratory"}]}], "component": [{"code": {"text": "Hemoglobina Glicada -  HbA1c", "coding": [{"code": "2042", "system": "https://interoperabilidade.dasa.com.br/fhir/CodeSystem/dasa-motion-exames-codigoOrigem", "display": "Hemoglobina <PERSON>"}]}, "valueString": "5,0|%", "referenceRange": [{"text": "(Vide Intervalo de Referência Abaixo)"}, {"text": "<table><title>Tabela de Referência - Hemoglobina Glicada -  HbA1c</title><table-def header=\"false\"><col psize=\"25\" border=\"false\"></col><col psize=\"25\" border=\"false\"></col><col psize=\"25\" border=\"false\"></col><col psize=\"25\" border=\"false\"></col></table-def><tr><td colspan=\"4\" border=\"false\"><r>Normal: inferior a 5,7%\nRisco aumentado para diabetes Mellitus: 5,7 a 6,4%\nDiabetes Mellitus: Igual ou superior a 6,5%</r></td></tr></table>"}]}, {"code": {"text": "Glicose Média Estimada (GME)", "coding": [{"code": "13431", "system": "https://interoperabilidade.dasa.com.br/fhir/CodeSystem/dasa-motion-exames-codigoOrigem", "display": "Glicose Média Estimada (GME)"}]}, "valueString": "96|mg/dL"}], "encounter": {"type": "Encounter", "reference": "urn:uuid:56a3cf06-5006-439c-955e-08b76afebfb8"}, "performer": [{"type": "Practitioner", "display": "<PERSON>. <PERSON><PERSON><PERSON><PERSON>mpo Neto CRM- SP 102037"}, {"type": "Practitioner", "display": "Sob a responsabilidade do Dr. Cristovam Scapulatempo Neto - CRM nº 102047"}], "identifier": [{"use": "usual", "value": "************-13434", "system": "https://interoperabilidade.dasa.com.br/fhir/NamingSystem/dasa-motion-requisicao-codigoOrigem"}], "resourceType": "Observation", "effectiveDateTime": "2022-08-27T21:15:53-03:00"}, {"id": "cbd8acbd-9a8f-4746-b0b6-79391f4ad69b", "code": {"text": "<PERSON><PERSON>", "coding": [{"code": "3439", "system": "https://interoperabilidade.dasa.com.br/fhir/CodeSystem/dasa-motion-exames-codigoOrigem", "display": "<PERSON><PERSON><PERSON> (Caracteres Físicos, Elementos Anormais e Sedimentoscopia)"}]}, "meta": {"profile": ["https://interoperabilidade.dasa.com.br/fhir/StructureDefinition/Observation"]}, "note": [{"text": "Para mais informações consulte o laudo em pdf."}], "status": "final", "subject": {"type": "Patient", "display": "@nickname @lastname", "reference": "urn:uuid:3f3d409a-fb67-404d-9c1f-9c89b52e0a40"}, "category": [{"text": "Laboratory", "coding": [{"code": "laboratory", "system": "http://terminology.hl7.org/CodeSystem/observation-category", "display": "Laboratory"}]}], "component": [{"code": {"text": "Densidade", "coding": [{"code": "1283", "system": "https://interoperabilidade.dasa.com.br/fhir/CodeSystem/dasa-motion-exames-codigoOrigem", "display": "Densidade Urinária"}]}, "valueString": "1.027", "referenceRange": [{"low": {"value": 1005}, "high": {"value": 1035}, "text": "1.005 a 1.035"}]}, {"code": {"text": "pH", "coding": [{"code": "4252", "system": "https://interoperabilidade.dasa.com.br/fhir/CodeSystem/dasa-motion-exames-codigoOrigem", "display": "pH Urinário"}]}, "valueString": "5,0", "referenceRange": [{"low": {"value": 5}, "high": {"value": 8}, "text": "5,0 a 8,0"}]}, {"code": {"text": "Cor", "coding": [{"code": "915", "system": "https://interoperabilidade.dasa.com.br/fhir/CodeSystem/dasa-motion-exames-codigoOrigem", "display": "Coloração da Urina"}]}, "valueString": "<PERSON><PERSON>", "referenceRange": [{"text": "<PERSON><PERSON>"}]}, {"code": {"text": "<PERSON><PERSON><PERSON><PERSON>", "coding": [{"code": "20811", "system": "https://interoperabilidade.dasa.com.br/fhir/CodeSystem/dasa-motion-exames-codigoOrigem", "display": "Proteinúria - TIPO I"}]}, "valueString": "Negativo", "referenceRange": [{"text": "Negativo"}]}, {"code": {"text": "Glicose", "coding": [{"code": "20813", "system": "https://interoperabilidade.dasa.com.br/fhir/CodeSystem/dasa-motion-exames-codigoOrigem", "display": "Glicosúria - TIPO I"}]}, "valueString": "Negativo", "referenceRange": [{"text": "Negativo"}]}, {"code": {"text": "Corpos Cetônicos, Pesquisa", "coding": [{"code": "1031", "system": "https://interoperabilidade.dasa.com.br/fhir/CodeSystem/dasa-motion-exames-codigoOrigem", "display": "Corpos Cetônicos , Pesquisa (Urina)"}]}, "valueString": "Negativo", "referenceRange": [{"text": "Negativo"}]}, {"code": {"text": "Pigmentos Biliares", "coding": [{"code": "2950", "system": "https://interoperabilidade.dasa.com.br/fhir/CodeSystem/dasa-motion-exames-codigoOrigem", "display": "Pigmentos Biliares na Urina"}]}, "valueString": "Negativo", "referenceRange": [{"text": "Negativo"}]}, {"code": {"text": "Urobilinogênio", "coding": [{"code": "4133", "system": "https://interoperabilidade.dasa.com.br/fhir/CodeSystem/dasa-motion-exames-codigoOrigem", "display": "Urobilinogênio na Urina"}]}, "valueString": "1,0 mg/dL", "referenceRange": [{"text": "Até 1,0 mg/dL"}]}, {"code": {"text": "<PERSON><PERSON><PERSON>", "coding": [{"code": "5774", "system": "https://interoperabilidade.dasa.com.br/fhir/CodeSystem/dasa-motion-exames-codigoOrigem", "display": "Nitrito na Urina"}]}, "valueString": "Negativo", "referenceRange": [{"text": "Negativo"}]}, {"code": {"text": "Hemoglobina", "coding": [{"code": "2045", "system": "https://interoperabilidade.dasa.com.br/fhir/CodeSystem/dasa-motion-exames-codigoOrigem", "display": "Hemoglobina na Urina"}]}, "valueString": "Negativo", "referenceRange": [{"text": "Negativo"}]}, {"code": {"text": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "coding": [{"code": "2522", "system": "https://interoperabilidade.dasa.com.br/fhir/CodeSystem/dasa-motion-exames-codigoOrigem", "display": "Leucócitos na Urina"}]}, "valueString": "1.000|/mL", "referenceRange": [{"text": "Inferior a 10.000 /mL"}]}, {"code": {"text": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "coding": [{"code": "2061", "system": "https://interoperabilidade.dasa.com.br/fhir/CodeSystem/dasa-motion-exames-codigoOrigem", "display": "Hemácias na Urina"}]}, "valueString": "1.000|/mL", "referenceRange": [{"text": "Inferior a 10.000 /mL"}]}, {"code": {"text": "Células Epiteliais de Descamação", "coding": [{"code": "1238", "system": "https://interoperabilidade.dasa.com.br/fhir/CodeSystem/dasa-motion-exames-codigoOrigem", "display": "<PERSON><PERSON><PERSON><PERSON> Epiteli<PERSON> na Urina"}]}, "valueString": "Negativo"}], "encounter": {"type": "Encounter", "reference": "urn:uuid:56a3cf06-5006-439c-955e-08b76afebfb8"}, "performer": [{"type": "Practitioner", "display": "Dr<PERSON><PERSON> <PERSON><PERSON>e CRM - SP 149388"}, {"type": "Practitioner", "display": "Sob a responsabilidade do Dr. Cristovam Scapulatempo Neto - CRM nº 102047"}], "identifier": [{"use": "usual", "value": "************-3439", "system": "https://interoperabilidade.dasa.com.br/fhir/NamingSystem/dasa-motion-requisicao-codigoOrigem"}], "resourceType": "Observation", "effectiveDateTime": "2022-08-27T21:15:53-03:00"}, {"id": "e917046a-c54e-4dd7-a55f-ad02eda5e5c0", "code": {"text": "C<PERSON><PERSON><PERSON>", "coding": [{"code": "40301630", "system": "https://interoperabilidade.dasa.com.br/fhir/CodeSystem/ans-tuss-procedimento-codigo"}, {"code": "12175", "system": "https://interoperabilidade.dasa.com.br/fhir/CodeSystem/dasa-motion-exames-codigoOrigem", "display": "Estimativa de Filtração Glomerular - CREA"}, {"code": "364", "system": "https://interoperabilidade.dasa.com.br/fhir/CodeSystem/dasa-dataprovider-produto-codigo", "display": "CREATININA"}]}, "meta": {"profile": ["https://interoperabilidade.dasa.com.br/fhir/StructureDefinition/ServiceRequest"]}, "intent": "order", "status": "completed", "subject": {"type": "Patient", "display": "@nickname @lastname", "reference": "urn:uuid:3f3d409a-fb67-404d-9c1f-9c89b52e0a40"}, "category": [{"text": "Procedimento Laboratorial", "coding": [{"code": "108252007", "system": "http://snomed.info/sct", "display": "Laboratory procedure"}]}], "insurance": [{"type": "Coverage", "display": "Db-alice", "reference": "urn:uuid:138fe449-eab7-4b31-aa9d-e9e5054187e1"}], "requester": {"type": "Practitioner", "display": "FILOMENA MARIKO AMARO TAKIGUTI", "reference": "urn:uuid:cfe9d64b-0acf-4bc3-8ad2-4a701711e14c"}, "identifier": [{"use": "usual", "value": "************-12175", "system": "https://interoperabilidade.dasa.com.br/fhir/NamingSystem/dasa-motion-requisicao-codigoOrigem"}, {"use": "usual", "value": "12175", "system": "https://interoperabilidade.dasa.com.br/fhir/NamingSystem/"}], "resourceType": "ServiceRequest"}, {"id": "973a6481-0d24-41eb-9e03-a3b7753e5bf8", "code": {"text": "<PERSON><PERSON><PERSON><PERSON>", "coding": [{"code": "40302318", "system": "https://interoperabilidade.dasa.com.br/fhir/CodeSystem/ans-tuss-procedimento-codigo"}, {"code": "3002", "system": "https://interoperabilidade.dasa.com.br/fhir/CodeSystem/dasa-motion-exames-codigoOrigem", "display": "<PERSON><PERSON><PERSON><PERSON>"}, {"code": "760", "system": "https://interoperabilidade.dasa.com.br/fhir/CodeSystem/dasa-dataprovider-produto-codigo", "display": "POTÁSSIO"}]}, "meta": {"profile": ["https://interoperabilidade.dasa.com.br/fhir/StructureDefinition/ServiceRequest"]}, "intent": "order", "status": "completed", "subject": {"type": "Patient", "display": "@nickname @lastname", "reference": "urn:uuid:3f3d409a-fb67-404d-9c1f-9c89b52e0a40"}, "category": [{"text": "Procedimento Laboratorial", "coding": [{"code": "108252007", "system": "http://snomed.info/sct", "display": "Laboratory procedure"}]}], "insurance": [{"type": "Coverage", "display": "Db-alice", "reference": "urn:uuid:138fe449-eab7-4b31-aa9d-e9e5054187e1"}], "requester": {"type": "Practitioner", "display": "FILOMENA MARIKO AMARO TAKIGUTI", "reference": "urn:uuid:cfe9d64b-0acf-4bc3-8ad2-4a701711e14c"}, "identifier": [{"use": "usual", "value": "************-3002", "system": "https://interoperabilidade.dasa.com.br/fhir/NamingSystem/dasa-motion-requisicao-codigoOrigem"}, {"use": "usual", "value": "3002", "system": "https://interoperabilidade.dasa.com.br/fhir/NamingSystem/"}], "resourceType": "ServiceRequest"}, {"id": "9a4b428f-6bb4-49a0-a162-d47b09cace8b", "code": {"text": "Proteína na Urina", "coding": [{"code": "40302377", "system": "https://interoperabilidade.dasa.com.br/fhir/CodeSystem/ans-tuss-procedimento-codigo"}, {"code": "3053", "system": "https://interoperabilidade.dasa.com.br/fhir/CodeSystem/dasa-motion-exames-codigoOrigem", "display": "Proteína na Urina Amostra Isolada"}, {"code": "812", "system": "https://interoperabilidade.dasa.com.br/fhir/CodeSystem/dasa-dataprovider-produto-codigo", "display": "PROTEINAS , URINA AMOSTRA ISOLADA"}]}, "meta": {"profile": ["https://interoperabilidade.dasa.com.br/fhir/StructureDefinition/ServiceRequest"]}, "intent": "order", "status": "completed", "subject": {"type": "Patient", "display": "@nickname @lastname", "reference": "urn:uuid:3f3d409a-fb67-404d-9c1f-9c89b52e0a40"}, "category": [{"text": "Procedimento Laboratorial", "coding": [{"code": "108252007", "system": "http://snomed.info/sct", "display": "Laboratory procedure"}]}], "insurance": [{"type": "Coverage", "display": "Db-alice", "reference": "urn:uuid:138fe449-eab7-4b31-aa9d-e9e5054187e1"}], "requester": {"type": "Practitioner", "display": "FILOMENA MARIKO AMARO TAKIGUTI", "reference": "urn:uuid:cfe9d64b-0acf-4bc3-8ad2-4a701711e14c"}, "identifier": [{"use": "usual", "value": "************-3053", "system": "https://interoperabilidade.dasa.com.br/fhir/NamingSystem/dasa-motion-requisicao-codigoOrigem"}, {"use": "usual", "value": "3053", "system": "https://interoperabilidade.dasa.com.br/fhir/NamingSystem/"}], "resourceType": "ServiceRequest"}, {"id": "127be12d-ce47-471a-911a-0ac163726e0c", "code": {"text": "Uré<PERSON>", "coding": [{"code": "40302580", "system": "https://interoperabilidade.dasa.com.br/fhir/CodeSystem/ans-tuss-procedimento-codigo"}, {"code": "4141", "system": "https://interoperabilidade.dasa.com.br/fhir/CodeSystem/dasa-motion-exames-codigoOrigem", "display": "Uré<PERSON>"}, {"code": "969", "system": "https://interoperabilidade.dasa.com.br/fhir/CodeSystem/dasa-dataprovider-produto-codigo", "display": "URÉIA"}]}, "meta": {"profile": ["https://interoperabilidade.dasa.com.br/fhir/StructureDefinition/ServiceRequest"]}, "intent": "order", "status": "completed", "subject": {"type": "Patient", "display": "@nickname @lastname", "reference": "urn:uuid:3f3d409a-fb67-404d-9c1f-9c89b52e0a40"}, "category": [{"text": "Procedimento Laboratorial", "coding": [{"code": "108252007", "system": "http://snomed.info/sct", "display": "Laboratory procedure"}]}], "insurance": [{"type": "Coverage", "display": "Db-alice", "reference": "urn:uuid:138fe449-eab7-4b31-aa9d-e9e5054187e1"}], "requester": {"type": "Practitioner", "display": "FILOMENA MARIKO AMARO TAKIGUTI", "reference": "urn:uuid:cfe9d64b-0acf-4bc3-8ad2-4a701711e14c"}, "identifier": [{"use": "usual", "value": "************-4141", "system": "https://interoperabilidade.dasa.com.br/fhir/NamingSystem/dasa-motion-requisicao-codigoOrigem"}, {"use": "usual", "value": "4141", "system": "https://interoperabilidade.dasa.com.br/fhir/NamingSystem/"}], "resourceType": "ServiceRequest"}, {"id": "36e2a37b-d172-4f96-a783-3a2329f83cdf", "code": {"text": "Albuminúria - perfil em amostra isolada", "coding": [{"code": "40311171", "system": "https://interoperabilidade.dasa.com.br/fhir/CodeSystem/ans-tuss-procedimento-codigo"}, {"code": "10943", "system": "https://interoperabilidade.dasa.com.br/fhir/CodeSystem/dasa-motion-exames-codigoOrigem", "display": "Perfil Albuminúria Amostra Isolada"}, {"code": "4405", "system": "https://interoperabilidade.dasa.com.br/fhir/CodeSystem/dasa-dataprovider-produto-codigo", "display": "MICROALBUMINÚRIA, AMOSTRA ISOLADA"}]}, "meta": {"profile": ["https://interoperabilidade.dasa.com.br/fhir/StructureDefinition/ServiceRequest"]}, "intent": "order", "status": "completed", "subject": {"type": "Patient", "display": "@nickname @lastname", "reference": "urn:uuid:3f3d409a-fb67-404d-9c1f-9c89b52e0a40"}, "category": [{"text": "Procedimento Laboratorial", "coding": [{"code": "108252007", "system": "http://snomed.info/sct", "display": "Laboratory procedure"}]}], "insurance": [{"type": "Coverage", "display": "Db-alice", "reference": "urn:uuid:138fe449-eab7-4b31-aa9d-e9e5054187e1"}], "identifier": [{"use": "usual", "value": "************-10943", "system": "https://interoperabilidade.dasa.com.br/fhir/NamingSystem/dasa-motion-requisicao-codigoOrigem"}, {"use": "usual", "value": "10943", "system": "https://interoperabilidade.dasa.com.br/fhir/NamingSystem/"}], "resourceType": "ServiceRequest"}, {"id": "1014c316-d3d0-4a4b-93b5-533408aa125d", "code": {"text": "Hemoglobina Glicada e Glicose Média Estimada", "coding": [{"code": "40302733", "system": "https://interoperabilidade.dasa.com.br/fhir/CodeSystem/ans-tuss-procedimento-codigo"}, {"code": "13434", "system": "https://interoperabilidade.dasa.com.br/fhir/CodeSystem/dasa-motion-exames-codigoOrigem", "display": "Hemoglobina Glicada e Glicose Média Estimada"}, {"code": "7471", "system": "https://interoperabilidade.dasa.com.br/fhir/CodeSystem/dasa-dataprovider-produto-codigo", "display": "HEMOGLOBINA GLICADA"}]}, "meta": {"profile": ["https://interoperabilidade.dasa.com.br/fhir/StructureDefinition/ServiceRequest"]}, "intent": "order", "status": "completed", "subject": {"type": "Patient", "display": "@nickname @lastname", "reference": "urn:uuid:3f3d409a-fb67-404d-9c1f-9c89b52e0a40"}, "category": [{"text": "Procedimento Laboratorial", "coding": [{"code": "108252007", "system": "http://snomed.info/sct", "display": "Laboratory procedure"}]}], "insurance": [{"type": "Coverage", "display": "Db-alice", "reference": "urn:uuid:138fe449-eab7-4b31-aa9d-e9e5054187e1"}], "requester": {"type": "Practitioner", "display": "FILOMENA MARIKO AMARO TAKIGUTI", "reference": "urn:uuid:cfe9d64b-0acf-4bc3-8ad2-4a701711e14c"}, "identifier": [{"use": "usual", "value": "************-13434", "system": "https://interoperabilidade.dasa.com.br/fhir/NamingSystem/dasa-motion-requisicao-codigoOrigem"}, {"use": "usual", "value": "13434", "system": "https://interoperabilidade.dasa.com.br/fhir/NamingSystem/"}], "resourceType": "ServiceRequest"}, {"id": "138fe449-eab7-4b31-aa9d-e9e5054187e1", "meta": {"profile": ["https://interoperabilidade.dasa.com.br/fhir/StructureDefinition/Coverage"]}, "class": [{"name": "Alice Operadora Ltda", "type": {"coding": [{"code": "plan", "system": "http://terminology.hl7.org/CodeSystem/coverage-class"}]}, "value": "Db-alice"}], "payor": [{"type": "Organization", "display": "Alice Operadora Ltda", "identifier": {"value": "Db-alice"}}], "status": "active", "identifier": [{"use": "usual", "value": "**********-7-Db-alice", "system": "https://interoperabilidade.dasa.com.br/fhir/NamingSystem/dasa-gliese-convenio-mPacCv_convenio"}], "beneficiary": {"type": "Patient", "display": "@nickname @lastname", "reference": "urn:uuid:3f3d409a-fb67-404d-9c1f-9c89b52e0a40"}, "resourceType": "Coverage", "subscriberId": "@nationalid"}, {"id": "21769151-ecd1-4893-9629-69771fcb57c8", "meta": {"profile": ["https://interoperabilidade.dasa.com.br/fhir/StructureDefinition/Practitioner"]}, "name": [{"use": "official", "text": "FILOMENA MARIKO AMARO TAKIGUTI"}], "identifier": [{"use": "official", "value": "161610", "system": "https://interoperabilidade.dasa.com.br/fhir/NamingSystem/CRM-SP-registro-profissional-ninscricao"}], "resourceType": "Practitioner"}, {"id": "56a3cf06-5006-439c-955e-08b76afebfb8", "meta": {"profile": ["https://interoperabilidade.dasa.com.br/fhir/StructureDefinition/Encounter"]}, "class": {"system": "http://terminology.hl7.org/CodeSystem/v3-ActCode"}, "period": {"end": "2022-08-27T11:51:20-03:00", "start": "2022-08-27T11:51:20-03:00"}, "status": "finished", "basedOn": [{"type": "ServiceRequest", "reference": "urn:uuid:e917046a-c54e-4dd7-a55f-ad02eda5e5c0"}, {"type": "ServiceRequest", "reference": "urn:uuid:973a6481-0d24-41eb-9e03-a3b7753e5bf8"}, {"type": "ServiceRequest", "reference": "urn:uuid:9a4b428f-6bb4-49a0-a162-d47b09cace8b"}, {"type": "ServiceRequest", "reference": "urn:uuid:127be12d-ce47-471a-911a-0ac163726e0c"}, {"type": "ServiceRequest", "reference": "urn:uuid:36e2a37b-d172-4f96-a783-3a2329f83cdf"}, {"type": "ServiceRequest", "reference": "urn:uuid:1014c316-d3d0-4a4b-93b5-533408aa125d"}], "subject": {"type": "Patient", "display": "@nickname @lastname", "reference": "urn:uuid:3f3d409a-fb67-404d-9c1f-9c89b52e0a40"}, "location": [{"location": {"type": "Location", "display": "DA - Posto Domiciliar Ipiranga", "identifier": {"value": "2370"}}}], "identifier": [{"use": "usual", "value": "************", "system": "https://interoperabilidade.dasa.com.br/fhir/NamingSystem/dasa-motion-atendimento-codigoOrigem"}], "resourceType": "Encounter"}], "encounter": {"type": "Encounter", "reference": "urn:uuid:56a3cf06-5006-439c-955e-08b76afebfb8"}, "performer": [{"type": "Practitioner", "display": "Ana Lúcia Nascimento Camilo  CRBM-SP 5064"}, {"type": "Practitioner", "display": "<PERSON><PERSON><PERSON> Silva Krenke CRBM-11374"}], "identifier": [{"use": "usual", "value": "************", "system": "https://interoperabilidade.dasa.com.br/fhir/NamingSystem/dasa-motion-laudoexame-codigoOrigem"}], "resourceType": "DiagnosticReport", "presentedForm": [{"url": "https://bkt-sa-east-1-deepwater-prd-pdf.s3.sa-east-1.amazonaws.com/laudo-exame-************-*************-11ed-b039-a34b99d3a94c.pdf?AWSAccessKeyId=AKIAQBHT2D72PUOHBEHO&Expires=1661774431&Signature=NHNJnKGpn15A99WpvXTBj3DeDd0%3D", "language": "pt-BR", "contentType": "application/pdf"}], "effectivePeriod": {"end": "2022-08-28T08:51:13-03:00", "start": "2022-08-27T23:35:14-03:00"}, "resultsInterpreter": [{"type": "Practitioner", "display": "<PERSON>. <PERSON><PERSON><PERSON><PERSON>mpo Neto CRM- SP 102037"}, {"type": "Practitioner", "display": "Dr<PERSON><PERSON> <PERSON><PERSON>e CRM - SP 149388"}]}