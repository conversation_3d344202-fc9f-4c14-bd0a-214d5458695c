package br.com.alice.data.layer.services

import br.com.alice.common.RangeUUID
import br.com.alice.common.core.Model
import br.com.alice.common.data.dsl.matchers.ResultAssert.Companion.assertThat
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.common.helpers.verifyOnce
import br.com.alice.common.service.data.dsl.Query
import br.com.alice.common.service.data.dsl.QueryBodyBuilder
import br.com.alice.data.layer.helpers.DataLayerTestModelFactory
import br.com.alice.data.layer.models.StaffScheduleModel
import br.com.alice.data.layer.pipelines.DatabasePipeline
import br.com.alice.data.layer.pipelines.DatabasePipelineFactory
import br.com.alice.data.layer.services.StaffScheduleModelDataService.FieldOptions
import br.com.alice.data.layer.services.StaffScheduleModelDataService.OrderingOptions
import br.com.alice.data.layer.tables.StaffScheduleTable
import br.com.alice.data.layer.tables.Table
import com.github.kittinunf.result.success
import io.mockk.coEvery
import io.mockk.confirmVerified
import io.mockk.every
import io.mockk.mockk
import kotlinx.coroutines.runBlocking
import org.assertj.core.api.Assertions.assertThat
import kotlin.reflect.KClass
import kotlin.test.AfterTest
import kotlin.test.Test

class StaffScheduleModelDataServiceImplTest {

    private val factory: DatabasePipelineFactory = mockk()

    private val dataBasePipeline: DatabasePipeline<StaffScheduleModel> = mockk()

    private val baseStaffSchedule: StaffScheduleModel = mockk()
    private val staffScheduleUtcWeekDay: StaffScheduleModel = mockk()
    private val staffScheduleBrtWeekDay: StaffScheduleModel = mockk()
    private val staffScheduleExpected: StaffScheduleModel = DataLayerTestModelFactory.buildStaffSchedule()

    @AfterTest
    fun confirmMocks() = confirmVerified(
        factory,
        dataBasePipeline,
        baseStaffSchedule,
        staffScheduleUtcWeekDay,
        staffScheduleBrtWeekDay
    )

    @Test
    fun `#add returns model with converted weekday timezone`() = runBlocking {
        every { factory.get(StaffScheduleModel::class, StaffScheduleTable::class) } returns dataBasePipeline
        every { baseStaffSchedule.withUTCWeekDay() } returns staffScheduleUtcWeekDay
        every { staffScheduleUtcWeekDay.sanitize() } returns staffScheduleUtcWeekDay
        coEvery { dataBasePipeline.add(staffScheduleUtcWeekDay) } returns baseStaffSchedule.success()
        every { baseStaffSchedule.withBRTWeekDay() } returns staffScheduleExpected

        val staffScheduleModelDataService = StaffScheduleModelDataServiceImpl(factory)

        val result = staffScheduleModelDataService.add(baseStaffSchedule)
        assertThat(result).isSuccessWithData(staffScheduleExpected)

        verifyOnce { factory.get(any<KClass<Model>>(), any<KClass<Table<*>>>()) }
        verifyOnce { baseStaffSchedule.withUTCWeekDay() }
        verifyOnce { staffScheduleUtcWeekDay.sanitize() }
        coVerifyOnce { dataBasePipeline.add(any()) }
        verifyOnce { baseStaffSchedule.withBRTWeekDay() }
    }

    @Test
    fun `#get returns model with converted weekday timezone`() = runBlocking {
        val id = RangeUUID.generate()

        every { factory.get(StaffScheduleModel::class, StaffScheduleTable::class) } returns dataBasePipeline
        coEvery { dataBasePipeline.get(id) } returns baseStaffSchedule.success()
        every { baseStaffSchedule.withBRTWeekDay() } returns staffScheduleExpected

        val staffScheduleModelDataService = StaffScheduleModelDataServiceImpl(factory)

        val result = staffScheduleModelDataService.get(id)
        assertThat(result).isSuccessWithData(staffScheduleExpected)

        verifyOnce { factory.get(any<KClass<Model>>(), any<KClass<Table<*>>>()) }
        coVerifyOnce { dataBasePipeline.get(any()) }
        verifyOnce { baseStaffSchedule.withBRTWeekDay() }
    }

    @Test
    fun `#getWithUTCWeekDay returns model without convert weekday timezone`() = runBlocking {
        val id = RangeUUID.generate()

        every { factory.get(StaffScheduleModel::class, StaffScheduleTable::class) } returns dataBasePipeline
        coEvery { dataBasePipeline.get(id) } returns staffScheduleExpected.success()

        val staffScheduleModelDataService = StaffScheduleModelDataServiceImpl(factory)

        val result = staffScheduleModelDataService.getWithUTCWeekDay(id)
        assertThat(result).isSuccessWithData(staffScheduleExpected)

        verifyOnce { factory.get(any<KClass<Model>>(), any<KClass<Table<*>>>()) }
        coVerifyOnce { dataBasePipeline.get(any()) }
    }

    @Test
    fun `#find returns models with converted weekday timezone`() = runBlocking {
        val queryBodyBuilder: QueryBodyBuilder<FieldOptions, OrderingOptions> = mockk()
        val query: Query = mockk()

        every { factory.get(StaffScheduleModel::class, StaffScheduleTable::class) } returns dataBasePipeline
        every { queryBodyBuilder(any()).build() } returns query
        coEvery { dataBasePipeline.findByQuery(query) } returns listOf(baseStaffSchedule).success()
        // findByQuery function conversion
        every { baseStaffSchedule.withBRTWeekDay() } returns staffScheduleBrtWeekDay
        // find function conversion
        every { staffScheduleBrtWeekDay.withBRTWeekDay() } returns staffScheduleExpected

        val staffScheduleModelDataService = StaffScheduleModelDataServiceImpl(factory)

        val result = staffScheduleModelDataService.find(queryBodyBuilder)
        assertThat(result).isSuccessWithData(listOf(staffScheduleExpected))

        verifyOnce { factory.get(any<KClass<Model>>(), any<KClass<Table<*>>>()) }
        coVerifyOnce { dataBasePipeline.findByQuery(any()) }
        verifyOnce { baseStaffSchedule.withBRTWeekDay() }
        verifyOnce { staffScheduleBrtWeekDay.withBRTWeekDay() }
    }

    @Test
    fun `#findByQuery returns models with converted weekday timezone`() = runBlocking {
        val query: Query = mockk()

        every { factory.get(StaffScheduleModel::class, StaffScheduleTable::class) } returns dataBasePipeline
        coEvery { dataBasePipeline.findByQuery(query) } returns listOf(baseStaffSchedule).success()
        every { baseStaffSchedule.withBRTWeekDay() } returns staffScheduleExpected

        val staffScheduleModelDataService = StaffScheduleModelDataServiceImpl(factory)

        val result = staffScheduleModelDataService.findByQuery(query)
        assertThat(result).isSuccessWithData(listOf(staffScheduleExpected))

        verifyOnce { factory.get(any<KClass<Model>>(), any<KClass<Table<*>>>()) }
        coVerifyOnce { dataBasePipeline.findByQuery(any()) }
        verifyOnce { baseStaffSchedule.withBRTWeekDay() }
    }

    @Test
    fun `#findAuthorizedByQuery returns models with converted weekday timezone`() = runBlocking {
        val query: Query = mockk()

        every { factory.get(StaffScheduleModel::class, StaffScheduleTable::class) } returns dataBasePipeline
        coEvery { dataBasePipeline.findAuthorizedByQuery(query) } returns listOf(baseStaffSchedule).success()
        every { baseStaffSchedule.withBRTWeekDay() } returns staffScheduleExpected

        val staffScheduleModelDataService = StaffScheduleModelDataServiceImpl(factory)

        val result = staffScheduleModelDataService.findAuthorizedByQuery(query)
        assertThat(result).isSuccessWithData(listOf(staffScheduleExpected))

        verifyOnce { factory.get(any<KClass<Model>>(), any<KClass<Table<*>>>()) }
        coVerifyOnce { dataBasePipeline.findAuthorizedByQuery(any()) }
        verifyOnce { baseStaffSchedule.withBRTWeekDay() }
    }

    @Test
    fun `#findOne returns models with converted weekday timezone`() = runBlocking {
        val queryBodyBuilder: QueryBodyBuilder<FieldOptions, OrderingOptions> = mockk()
        val query: Query = mockk()

        every { factory.get(StaffScheduleModel::class, StaffScheduleTable::class) } returns dataBasePipeline
        every { queryBodyBuilder(any()).limit(any()).build() } returns query
        coEvery { dataBasePipeline.findByQuery(query) } returns listOf(baseStaffSchedule).success()
        // findByQuery function conversion
        every { baseStaffSchedule.withBRTWeekDay() } returns staffScheduleBrtWeekDay
        // findOne function conversion
        every { staffScheduleBrtWeekDay.withBRTWeekDay() } returns staffScheduleExpected

        val staffScheduleModelDataService = StaffScheduleModelDataServiceImpl(factory)

        val result = staffScheduleModelDataService.findOne(queryBodyBuilder)
        assertThat(result).isSuccessWithData(staffScheduleExpected)

        verifyOnce { factory.get(any<KClass<Model>>(), any<KClass<Table<*>>>()) }
        coVerifyOnce { dataBasePipeline.findByQuery(any()) }
        verifyOnce { baseStaffSchedule.withBRTWeekDay() }
        verifyOnce { staffScheduleBrtWeekDay.withBRTWeekDay() }
    }

    @Test
    fun `#findOneOrNull returns models with converted weekday timezone`() = runBlocking {
        val queryBodyBuilder: QueryBodyBuilder<FieldOptions, OrderingOptions> = mockk()
        val query: Query = mockk()

        every { factory.get(StaffScheduleModel::class, StaffScheduleTable::class) } returns dataBasePipeline
        every { queryBodyBuilder(any()).limit(any()).build() } returns query
        coEvery { dataBasePipeline.findByQuery(query) } returns listOf(baseStaffSchedule).success()
        // findByQuery function conversion
        every { baseStaffSchedule.withBRTWeekDay() } returns staffScheduleBrtWeekDay
        // findOne function conversion
        every { staffScheduleBrtWeekDay.withBRTWeekDay() } returns staffScheduleExpected

        val staffScheduleModelDataService = StaffScheduleModelDataServiceImpl(factory)

        val result = staffScheduleModelDataService.findOneOrNull(queryBodyBuilder)
        assertThat(result).isEqualTo(staffScheduleExpected)

        verifyOnce { factory.get(any<KClass<Model>>(), any<KClass<Table<*>>>()) }
        coVerifyOnce { dataBasePipeline.findByQuery(any()) }
        verifyOnce { baseStaffSchedule.withBRTWeekDay() }
        verifyOnce { staffScheduleBrtWeekDay.withBRTWeekDay() }
    }

    @Test
    fun `#findOneOrNull returns null`() = runBlocking {
        val queryBodyBuilder: QueryBodyBuilder<FieldOptions, OrderingOptions> = mockk()
        val query: Query = mockk()

        every { factory.get(StaffScheduleModel::class, StaffScheduleTable::class) } returns dataBasePipeline
        every { queryBodyBuilder(any()).limit(any()).build() } returns query
        coEvery { dataBasePipeline.findByQuery(query) } returns emptyList<StaffScheduleModel>().success()

        val staffScheduleModelDataService = StaffScheduleModelDataServiceImpl(factory)

        val result = staffScheduleModelDataService.findOneOrNull(queryBodyBuilder)
        assertThat(result).isNull()

        verifyOnce { factory.get(any<KClass<Model>>(), any<KClass<Table<*>>>()) }
        coVerifyOnce { dataBasePipeline.findByQuery(any()) }
    }

}
