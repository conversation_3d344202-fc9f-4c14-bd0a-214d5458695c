package br.com.alice.data.layer.models

enum class DealStage (val friendlyStageName: String, val stageOwner: StageOwner, val order: Int) {
    DEAL_CREATED( "Análise de documentação", StageOwner.ALICE, 0),
    LEAD("Lead", StageOwner.FALLBACK, 1),
    SALES_CONTACT("Em contato", StageOwner.FALLBACK, 2),
    QUALIFICATION_SALES_EXEC("Classificação SQL", StageOwner.FALLBACK, 3),
    PROPOSAL_SENT("Classificação SQL", StageOwner.FALLBACK, 4),
    BACKGROUND_CHECK( "Análise de documentação", StageOwner.ALICE, 10),
    BACKGROUND_CHECK_DECLINED( "Análise de documentação", StageOwner.ALICE, 10),
    CORROBORATING_DOCUMENTS_CHECK( "Análise de documentação", StageOwner.ALICE, 20),
    MISSING_CORROBORATING_DOCUMENTS( "Pendência de documentação", StageOwner.COMPANY, 20),
    RISK_FLOW("Realizar análise médica Alice", StageOwner.COMPANY, 30),
    CONTRACT_PREPARATION("Contrato em confecção", StageOwner.ALICE, 40),
    CONTRACT_SENT("Assinar contrato", StageOwner.COMPANY, 50),
    SEND_PAYMENT("Gerando pagamento", StageOwner.ALICE, 60),
    PAYMENT_REQUESTED("Realizar pagamento", StageOwner.COMPANY, 70),
    PAYMENT_CONFIRMED("Pagamento realizado", StageOwner.COMPANY, 80),
    PROCESS_FINISHED("Processo implantado", StageOwner.CONCLUDED, 90),
    CANCELED("Cancelado", StageOwner.FALLBACK, 200),
    LOST_LEAD("Lost Lead", StageOwner.FALLBACK, 210),
    FALLBACK("Em atualização", StageOwner.FALLBACK, 200);

    fun isAfter(other: DealStage): Boolean = this.order > other.order
}

enum class StageOwner() {
    ALICE, COMPANY, FALLBACK, CONCLUDED
}
fun getDealStage(hubspotStageName: String): DealStage = when (hubspotStageName) {


    // Unified pipeline
    "*********" -> DealStage.BACKGROUND_CHECK
    "*********" -> DealStage.BACKGROUND_CHECK_DECLINED
    "*********" -> DealStage.CORROBORATING_DOCUMENTS_CHECK
    "*********" -> DealStage.MISSING_CORROBORATING_DOCUMENTS
    "*********" -> DealStage.RISK_FLOW
    "*********" -> DealStage.CONTRACT_PREPARATION
    "*********" -> DealStage.CONTRACT_SENT
    //Enviar pagamento ([Corretores, Small Biz] Fluxo de Ativação)
    "*********" -> DealStage.SEND_PAYMENT
    //Realizar Pagamento ([Corretores, Small Biz] Fluxo de Ativação)
    "*********" -> DealStage.PAYMENT_REQUESTED
    "*********" -> DealStage.PAYMENT_CONFIRMED
    "*********" -> DealStage.PROCESS_FINISHED
    "*********" -> DealStage.CANCELED
    "*********" -> DealStage.LOST_LEAD
    "*********" -> DealStage.LEAD
    "*********" -> DealStage.SALES_CONTACT
    "*********" -> DealStage.QUALIFICATION_SALES_EXEC
    "137481531" -> DealStage.PROPOSAL_SENT


    // Staging pipeline
    "140367686" -> DealStage.BACKGROUND_CHECK
    "140367687" -> DealStage.BACKGROUND_CHECK_DECLINED
    "140414246" -> DealStage.CORROBORATING_DOCUMENTS_CHECK
    "140414247" -> DealStage.MISSING_CORROBORATING_DOCUMENTS
    "140414248" -> DealStage.RISK_FLOW
    "140414249" -> DealStage.CONTRACT_PREPARATION
    "140414250" -> DealStage.CONTRACT_SENT
    "140414251" -> DealStage.SEND_PAYMENT
    "140414252" -> DealStage.PAYMENT_REQUESTED
    "140414253" -> DealStage.PAYMENT_CONFIRMED
    "140414254" -> DealStage.PROCESS_FINISHED
    "140414255" -> DealStage.CANCELED
    "140367681" -> DealStage.LOST_LEAD
    "140367682" -> DealStage.LEAD
    "140367683" -> DealStage.SALES_CONTACT
    "140367684" -> DealStage.QUALIFICATION_SALES_EXEC
    "140367684" -> DealStage.PROPOSAL_SENT

    // Fallback
    else -> DealStage.FALLBACK
}

fun DealStage.toHubspotStage(): String {
    return when (this) {
        DealStage.DEAL_CREATED -> "137481531"
        DealStage.BACKGROUND_CHECK -> "*********"
        DealStage.BACKGROUND_CHECK_DECLINED -> "*********"
        DealStage.CORROBORATING_DOCUMENTS_CHECK -> "*********"
        DealStage.MISSING_CORROBORATING_DOCUMENTS -> "*********"
        DealStage.RISK_FLOW -> "*********"
        DealStage.CONTRACT_PREPARATION -> "*********"
        DealStage.CONTRACT_SENT -> "*********"
        DealStage.SEND_PAYMENT -> "*********"
        DealStage.PAYMENT_REQUESTED -> "*********"
        DealStage.PAYMENT_CONFIRMED -> "*********"
        DealStage.PROCESS_FINISHED -> "*********"
        DealStage.CANCELED -> "*********"
        DealStage.LOST_LEAD -> "*********"
        DealStage.LEAD -> "*********"
        DealStage.SALES_CONTACT -> "*********"
        DealStage.QUALIFICATION_SALES_EXEC -> "*********"
        DealStage.PROPOSAL_SENT -> "137481531"
        DealStage.FALLBACK -> "127229094"
    }
}
