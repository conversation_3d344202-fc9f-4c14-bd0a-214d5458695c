package br.com.alice.fhir.converters

import br.com.alice.common.RangeUUID
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.HealthMeasurementInternalType.DIASTOLIC_PRESSURE
import br.com.alice.data.layer.models.HealthMeasurementInternalType.HEART_RATE
import br.com.alice.data.layer.models.HealthMeasurementInternalType.HEIGHT
import br.com.alice.data.layer.models.HealthMeasurementInternalType.SYSTOLIC_PRESSURE
import br.com.alice.data.layer.models.HealthMeasurementInternalType.WEIGHT
import br.com.alice.data.layer.models.HealthPlanTaskStatus
import br.com.alice.data.layer.models.Referral
import br.com.alice.data.layer.models.copy
import br.com.alice.fhir.commons.fhirContentParser
import ca.uhn.fhir.context.FhirVersionEnum
import org.assertj.core.api.Assertions.assertThat
import org.hl7.fhir.r4.model.Annotation
import org.hl7.fhir.r4.model.CodeableConcept
import org.hl7.fhir.r4.model.Coding
import org.hl7.fhir.r4.model.Observation
import org.hl7.fhir.r4.model.Reference
import org.hl7.fhir.r4.model.StringType
import java.math.BigDecimal
import java.time.LocalDateTime
import kotlin.test.Test

class ObservationFactoryTest {
    private val person = TestModelFactory.buildPerson(
        dateOfBirth = LocalDateTime.now().minusDays(12)
    )
    private val patientId = RangeUUID.generate().toString()
    private val hapiParser = fhirContentParser(FhirVersionEnum.R4)

    @Test
    fun `#build a Observation fhir resource with Weight and Height`() {
        val appointment = TestModelFactory.buildAppointment(personId = person.id)
        val staff = TestModelFactory.buildStaff()
        val healthTasks = listOf(
            TestModelFactory.buildHealthPlanTaskReferral(
                personId = person.id,
                description = "Levar esse dado para o médico que irá atender",
                diagnosticHypothesis = "Hipotese diagnostica"
            ).copy(status = HealthPlanTaskStatus.ACTIVE),
            TestModelFactory.buildHealthPlanTaskReferral(
                personId = person.id,
                description = "Levar esse outro dado",
                diagnosticHypothesis = "Outro guess"
            ).copy(status = HealthPlanTaskStatus.ACTIVE)
        )
        val weightMeasure = TestModelFactory.buildHealthMeasurement(
            personId = person.id,
            appointmentId = appointment.id,
            type = WEIGHT,
            value = BigDecimal.valueOf(67.8),
            updatedByStaffId = staff.id
        )
        val heightMeasure = TestModelFactory.buildHealthMeasurement(
            personId = person.id,
            appointmentId = appointment.id,
            type = HEIGHT,
            value = BigDecimal.valueOf(1.70),
            updatedByStaffId = staff.id
        )

        val observations = listOf(
            Observation().apply {
                subject = Reference("Patient/$patientId")
                performer = listOf(
                    Reference("Practitioner/${heightMeasure.updatedByStaffId}")
                )
                value = StringType(heightMeasure.value.toString())
                encounter = Reference("Encounter/${appointment.id}")
                code = CodeableConcept().apply {
                    coding = listOf(
                        Coding().apply {
                            code = heightMeasure.typeId?.toString()
                            display = "ALTURA"
                        }
                    )
                    text = "ALTURA"
                }
            },
            Observation().apply {
                subject = Reference("Patient/$patientId")
                performer = listOf(
                    Reference("Practitioner/${weightMeasure.updatedByStaffId}")
                )
                value = StringType(weightMeasure.value.toString())
                encounter = Reference("Encounter/${appointment.id}")
                code = CodeableConcept().apply {
                    coding = listOf(
                        Coding().apply {
                            code = weightMeasure.typeId?.toString()
                            display = "PESO"
                        }
                    )
                    text = "PESO"
                }
            },
            healthTasks[0].specialize<Referral>().let {
                Observation().apply {
                    subject = Reference("Patient/$patientId")
                    performer = listOf(
                        Reference("$PRACTITIONER_RESOURCE_NAME/${it.lastRequesterStaffId}")
                    )
                    value = StringType(it.description)
                    encounter = Reference("$ENCOUNTER_RESOURCE_NAME/${appointment.id}")
                    note = listOf(
                        Annotation().apply {
                            text = it.diagnosticHypothesis
                        },
                        Annotation().apply {
                            text = it.description
                        }
                    )
                }
            },
            healthTasks[1].specialize<Referral>().let {
                Observation().apply {
                    subject = Reference("Patient/$patientId")
                    performer = listOf(
                        Reference("$PRACTITIONER_RESOURCE_NAME/${it.lastRequesterStaffId}")
                    )
                    value = StringType(it.description)
                    encounter = Reference("$ENCOUNTER_RESOURCE_NAME/${appointment.id}")
                    note = listOf(
                        Annotation().apply {
                            text = it.diagnosticHypothesis
                        },
                        Annotation().apply {
                            text = it.description
                        }
                    )
                }
            }
        )

        val factory = ObservationFactory
            .BuilderNutritional(
                patientId,
                appointment.id,
                listOf(heightMeasure, weightMeasure),
                healthTasks
            )
            .build()

        val expectedResource = observations.map {
            hapiParser.encodeResourceToString(it)
        }
        val factoryResource = factory.map {
            hapiParser.encodeResourceToString(it)
        }

        assertThat(factoryResource).isEqualTo(expectedResource)
    }

    @Test
    fun `#build a Observation fhir resource with VITAL_SIGNS`() {
        val appointment = TestModelFactory.buildAppointment(personId = person.id)
        val staff = TestModelFactory.buildStaff()
        val healthTasks = listOf(
            TestModelFactory.buildHealthPlanTaskReferral(
                personId = person.id,
                description = "Levar esse dado para o médico que irá atender",
                diagnosticHypothesis = "Hipotese diagnostica"
            ).copy(status = HealthPlanTaskStatus.ACTIVE),
            TestModelFactory.buildHealthPlanTaskReferral(
                personId = person.id,
                description = "Levar esse outro dado",
                diagnosticHypothesis = "Outro guess"
            ).copy(status = HealthPlanTaskStatus.ACTIVE)
        )
        val heartRateMeasure = TestModelFactory.buildHealthMeasurement(
            personId = person.id,
            appointmentId = appointment.id,
            type = HEART_RATE,
            value = BigDecimal.valueOf(98),
            updatedByStaffId = staff.id
        )
        val systolicPressureMeasure = TestModelFactory.buildHealthMeasurement(
            personId = person.id,
            appointmentId = appointment.id,
            type = SYSTOLIC_PRESSURE,
            value = BigDecimal.valueOf(117),
            updatedByStaffId = staff.id
        )
        val diastolicPressureMeasure = TestModelFactory.buildHealthMeasurement(
            personId = person.id,
            appointmentId = appointment.id,
            type = DIASTOLIC_PRESSURE,
            value = BigDecimal.valueOf(73),
            updatedByStaffId = staff.id
        )

        val observations = listOf(
            Observation().apply {
                subject = Reference("Patient/$patientId")
                performer = listOf(
                    Reference("Practitioner/${heartRateMeasure.updatedByStaffId}")
                )
                value = StringType(heartRateMeasure.value.toString())
                encounter = Reference("Encounter/${appointment.id}")
                code = CodeableConcept().apply {
                    coding = listOf(
                        Coding().apply {
                            code = heartRateMeasure.typeId?.toString()
                            display = "FREQUENCIA_CARDIACA"
                        }
                    )
                    text = "FREQUENCIA_CARDIACA"
                }
            },
            Observation().apply {
                subject = Reference("Patient/$patientId")
                performer = listOf(
                    Reference("Practitioner/${diastolicPressureMeasure.updatedByStaffId}")
                )
                value = StringType(diastolicPressureMeasure.value.toString())
                encounter = Reference("Encounter/${appointment.id}")
                code = CodeableConcept().apply {
                    coding = listOf(
                        Coding().apply {
                            code = diastolicPressureMeasure.typeId?.toString()
                            display = "PRESSAO_DIAST"
                        }
                    )
                    text = "PRESSAO_DIAST"
                }
            },
            Observation().apply {
                subject = Reference("Patient/$patientId")
                performer = listOf(
                    Reference("Practitioner/${systolicPressureMeasure.updatedByStaffId}")
                )
                value = StringType(systolicPressureMeasure.value.toString())
                encounter = Reference("Encounter/${appointment.id}")
                code = CodeableConcept().apply {
                    coding = listOf(
                        Coding().apply {
                            code = systolicPressureMeasure.typeId?.toString()
                            display = "PRESSAO_SIS"
                        }
                    )
                    text = "PRESSAO_SIS"
                }
            },
            healthTasks[0].specialize<Referral>().let {
                Observation().apply {
                    subject = Reference("Patient/$patientId")
                    performer = listOf(
                        Reference("$PRACTITIONER_RESOURCE_NAME/${it.lastRequesterStaffId}")
                    )
                    value = StringType(it.description)
                    encounter = Reference("$ENCOUNTER_RESOURCE_NAME/${appointment.id}")
                    note = listOf(
                        Annotation().apply {
                            text = it.diagnosticHypothesis
                        },
                        Annotation().apply {
                            text = it.description
                        }
                    )
                }
            },
            healthTasks[1].specialize<Referral>().let {
                Observation().apply {
                    subject = Reference("Patient/$patientId")
                    performer = listOf(
                        Reference("$PRACTITIONER_RESOURCE_NAME/${it.lastRequesterStaffId}")
                    )
                    value = StringType(it.description)
                    encounter = Reference("$ENCOUNTER_RESOURCE_NAME/${appointment.id}")
                    note = listOf(
                        Annotation().apply {
                            text = it.diagnosticHypothesis
                        },
                        Annotation().apply {
                            text = it.description
                        }
                    )
                }
            }
        )

        val factory = ObservationFactory
            .BuilderNutritional(
                patientId,
                appointment.id,
                listOf(heartRateMeasure, diastolicPressureMeasure, systolicPressureMeasure),
                healthTasks
            )
            .build()

        val expectedResource = observations.map {
            hapiParser.encodeResourceToString(it)
        }
        val factoryResource = factory.map {
            hapiParser.encodeResourceToString(it)
        }

        assertThat(factoryResource).isEqualTo(expectedResource)
    }
}
