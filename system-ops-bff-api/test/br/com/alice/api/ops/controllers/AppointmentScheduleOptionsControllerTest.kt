package br.com.alice.api.ops.controllers

import br.com.alice.api.ops.ControllerTestHelper
import br.com.alice.common.RangeUUID
import br.com.alice.common.data.dsl.matchers.ResponseAssert.Companion.assertThat
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.common.helpers.mockRangeUuidAndDateTime
import br.com.alice.common.storage.FileStorage
import br.com.alice.common.storage.FileType
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.AppointmentScheduleType
import br.com.alice.schedule.client.AppointmentScheduleOptionFilters
import br.com.alice.schedule.client.AppointmentScheduleOptionService
import com.github.kittinunf.result.success
import io.ktor.client.statement.bodyAsText
import io.ktor.http.HttpHeaders
import io.ktor.http.HttpMethod
import io.mockk.coEvery
import io.mockk.mockk
import org.assertj.core.api.Assertions.assertThat
import java.time.LocalDateTime
import kotlin.test.BeforeTest
import kotlin.test.Test

class AppointmentScheduleOptionsControllerTest : ControllerTestHelper() {

    private val appointmentScheduleOptionService: AppointmentScheduleOptionService = mockk()
    private val fileStorage: FileStorage = mockk()
    private val appointmentScheduleOptionsController = AppointmentScheduleOptionsController(
        appointmentScheduleOptionService,
        fileStorage
    )

    @BeforeTest
    override fun setup() {
        super.setup()
        module.single { appointmentScheduleOptionsController }
    }

    @Test
    fun `#create return created option`() {
        val id = RangeUUID.generate()
        val baseDate = LocalDateTime.now()
        val createdAppointmentScheduleOption = TestModelFactory.buildAppointmentScheduleOption(
            id = id,
            calendarProviderUnits = emptyList(),
            createdAt = baseDate,
            updatedAt = baseDate
        )

        val request = ScheduleOptionRequest(
            title = createdAppointmentScheduleOption.title,
            description = createdAppointmentScheduleOption.description,
            type = createdAppointmentScheduleOption.type,
            staffId = createdAppointmentScheduleOption.staffId,
            imageUrl = createdAppointmentScheduleOption.imageUrl,
            calendarUrl = createdAppointmentScheduleOption.calendarUrl,
            active = createdAppointmentScheduleOption.active,
            specialtyId = createdAppointmentScheduleOption.specialtyId,
            subSpecialtyIds = createdAppointmentScheduleOption.subSpecialtyIds,
            specialistId = createdAppointmentScheduleOption.specialistId,
            showOnApp = createdAppointmentScheduleOption.showOnApp
        )

        coEvery {
            appointmentScheduleOptionService.add(createdAppointmentScheduleOption)
        } returns createdAppointmentScheduleOption.success()

        mockRangeUuidAndDateTime(id, baseDate) {
            authenticatedAs(idToken, staff) {
                post(to = "/appointmentScheduleOptions", body = request) { response ->
                    assertThat(response).isOKWithData(createdAppointmentScheduleOption)
                }
            }
        }

        coVerifyOnce { appointmentScheduleOptionService.add(any()) }
    }

    @Test
    fun `#index return options filtered by range`() {
        val appointmentScheduleOption1 = TestModelFactory.buildAppointmentScheduleOption()
        val appointmentScheduleOption2 = TestModelFactory.buildAppointmentScheduleOption()

        coEvery {
            appointmentScheduleOptionService.findBy(AppointmentScheduleOptionFilters(range = 0..49))
        } returns listOf(appointmentScheduleOption1, appointmentScheduleOption2).success()

        coEvery {
            appointmentScheduleOptionService.countBy(AppointmentScheduleOptionFilters(range = 0..1))
        } returns 2.success()

        authenticatedAs(idToken, staff) {
            get("/appointmentScheduleOptions?filter={}&range=[0,49]&sort=[\"id\",\"DESC\"]") { response ->
                assertThat(response).isOKWithData(listOf(appointmentScheduleOption1, appointmentScheduleOption2))

                assertThat(response.headers[HttpHeaders.ContentRange]).isEqualTo("2")
            }
        }

        coVerifyOnce { appointmentScheduleOptionService.findBy(any()) }
        coVerifyOnce { appointmentScheduleOptionService.countBy(any()) }
    }

    @Test
    fun `#index returns options found by query and range`() {
        val appointmentScheduleOption = TestModelFactory.buildAppointmentScheduleOption(title = "nutri01")
        val range = IntRange(0, 1)

        coEvery {
            appointmentScheduleOptionService.searchScheduleOption(
                titlePrefix = "nutri",
                range = range
            )
        } returns listOf(appointmentScheduleOption).success()

        coEvery {
            appointmentScheduleOptionService.countBy(
                AppointmentScheduleOptionFilters(
                    titlePrefix = "nutri",
                    active = true
                )
            )
        } returns 2.success()

        authenticatedAs(idToken, staff) {
            get("/appointmentScheduleOptions?filter={q=\"nutri\"}&range=[0,1]&sort=[\"id\",\"DESC\"]") { response ->
                assertThat(response).isOKWithData(listOf(appointmentScheduleOption))

                assertThat(response.headers[HttpHeaders.ContentRange]).isEqualTo("2")
            }
        }

        coVerifyOnce { appointmentScheduleOptionService.countBy(any()) }
    }

    @Test
    fun `#get returns option found by id`() {
        val retrievedAppointmentScheduleOption = TestModelFactory.buildAppointmentScheduleOption()

        coEvery {
            appointmentScheduleOptionService.get(retrievedAppointmentScheduleOption.id)
        } returns retrievedAppointmentScheduleOption.success()

        authenticatedAs(idToken, staff) {
            get("/appointmentScheduleOptions/${retrievedAppointmentScheduleOption.id}") { response ->
                assertThat(response).isOKWithData(retrievedAppointmentScheduleOption)
            }
        }

        coVerifyOnce { appointmentScheduleOptionService.get(any()) }
    }

    @Test
    fun `#update returns updated AppointmentScheduleOption`() {
        val appointmentScheduleOption = TestModelFactory.buildAppointmentScheduleOption(calendarProviderUnits = emptyList(),)
        val updatedAppointmentScheduleOption = appointmentScheduleOption.copy(
            title = "modifiedTitle",
            description = "modifiedDescription",
            type = AppointmentScheduleType.values().first { it != appointmentScheduleOption.type },
            staffId = RangeUUID.generate(),
            imageUrl = "modifiedImageUrl",
            calendarUrl = "modifiedCalendarUrl",
            specialtyId = RangeUUID.generate(),
            subSpecialtyIds = listOf(RangeUUID.generate()),
            specialistId = RangeUUID.generate(),
            active = !appointmentScheduleOption.active,
            showOnApp = !appointmentScheduleOption.showOnApp,
            shouldUseInternalScheduler = true
        )

        val request = ScheduleOptionRequest(
            title = updatedAppointmentScheduleOption.title,
            description = updatedAppointmentScheduleOption.description,
            type = updatedAppointmentScheduleOption.type,
            staffId = updatedAppointmentScheduleOption.staffId,
            imageUrl = updatedAppointmentScheduleOption.imageUrl,
            calendarUrl = updatedAppointmentScheduleOption.calendarUrl,
            specialtyId = updatedAppointmentScheduleOption.specialtyId,
            subSpecialtyIds = updatedAppointmentScheduleOption.subSpecialtyIds,
            specialistId = updatedAppointmentScheduleOption.specialistId,
            active = updatedAppointmentScheduleOption.active,
            showOnApp = updatedAppointmentScheduleOption.showOnApp,
            shouldUseInternalScheduler = true
        )

        coEvery {
            appointmentScheduleOptionService.get(appointmentScheduleOption.id)
        } returns appointmentScheduleOption.success()

        coEvery {
            appointmentScheduleOptionService.update(updatedAppointmentScheduleOption)
        } returns updatedAppointmentScheduleOption.success()

        authenticatedAs(idToken, this.staff) {
            put(to = "/appointmentScheduleOptions/${appointmentScheduleOption.id}", body = request) { response ->
                assertThat(response).isOKWithData(updatedAppointmentScheduleOption)
            }
        }

        coVerifyOnce { appointmentScheduleOptionService.get(any()) }
        coVerifyOnce { appointmentScheduleOptionService.update(any()) }
    }

    @Test
    fun `#image should return 200 OK with image URL`() {
        val uploadedFileUrl = "http://image.url"

        coEvery {
            fileStorage.store(
                match {
                    it.bucketName == "publicAssetsBucketTest"
                            && it.filePath == "appointment-schedule-options/apple.png"
                            && it.fileType == FileType.IMAGE_PNG
                }
            )
        } returns uploadedFileUrl

        authenticatedAs(idToken, staff) {
            multipart(
                HttpMethod.Post,
                "/appointmentScheduleOptions/image",
                fileName = "apple.png",
                parameters = emptyMap()
            ) { response ->
                assertThat(response).isOK()

                val resultUrl: String = response.bodyAsText()
                assertThat(resultUrl).isEqualTo(uploadedFileUrl)
            }
        }

        coVerifyOnce { fileStorage.store(any()) }
    }

    @Test
    fun `#image should throw exception when file is not an image`() {
        authenticatedAs(idToken, staff) {
            multipart(
                HttpMethod.Post,
                "/appointmentScheduleOptions/image",
                fileName = "invalid_image.txt",
                parameters = emptyMap()
            ) { response ->
                assertThat(response).isBadRequest()
            }
        }
    }
}
