package br.com.alice.api.ehr.controllers

import br.com.alice.api.ehr.controllers.model.ClinicalProfileLiteResponse
import br.com.alice.api.ehr.controllers.model.DiseaseBackgroundResponse
import br.com.alice.api.ehr.controllers.model.HealthMeasurementUpdateRequest
import br.com.alice.api.ehr.controllers.model.UnstructuredBackgroundResponse
import br.com.alice.api.ehr.converters.ClinicalBackgroundConverter
import br.com.alice.api.ehr.converters.ComposedClinicalProfileConverter
import br.com.alice.api.ehr.converters.HealthMeasurementRequestConverter
import br.com.alice.api.ehr.converters.HealthMeasurementResponseConverter
import br.com.alice.api.ehr.services.health_measurements.HealthMeasurementHistoryLogic
import br.com.alice.appointment.client.AppointmentService
import br.com.alice.common.Response
import br.com.alice.common.core.PersonId
import br.com.alice.common.core.extensions.toPersonId
import br.com.alice.common.core.extensions.toUUID
import br.com.alice.common.extensions.getOrNullIfNotFound
import br.com.alice.common.foldResponse
import br.com.alice.common.toResponse
import br.com.alice.data.layer.models.ClinicalBackground
import br.com.alice.data.layer.models.ClinicalBackgroundSource.STAFF
import br.com.alice.data.layer.models.ClinicalBackgroundType
import br.com.alice.data.layer.models.DiseaseBackground
import br.com.alice.data.layer.models.HealthMeasurementCategory
import br.com.alice.ehr.client.ClinicalBackgroundService
import br.com.alice.ehr.client.HealthMeasurementService
import br.com.alice.ehr.client.MemberCptsService
import br.com.alice.ehr.model.removeOthers
import br.com.alice.healthcondition.client.HealthConditionService
import br.com.alice.staff.client.StaffService
import com.github.kittinunf.result.map
import io.ktor.http.Parameters
import kotlinx.coroutines.async
import kotlinx.coroutines.coroutineScope
import java.util.UUID

class ClinicalProfileController(
    staffService: StaffService,
    private val memberCptsService: MemberCptsService,
    private val healthMeasurementService: HealthMeasurementService,
    private val clinicalBackgroundService: ClinicalBackgroundService,
    private val appointmentService: AppointmentService,
    private val healthConditionService: HealthConditionService,
) : StaffController(staffService) {

    suspend fun getLiteClinicalProfile(personId: PersonId): Response = coroutineScope {
        val healthMeasurementsDeferred = async {
            healthMeasurementService.getActivesByPersonId(
                personId, currentStaffId()
            ).get()
        }
        val clinicalBackgroundDeferred = async {
            clinicalBackgroundService.getByPersonId(
                personId, currentStaffId()
            ).get()
        }

        val clinicalBackground = clinicalBackgroundDeferred.await()
        val healthConditions = getHealthConditions(clinicalBackground).get()

        val healthMeasurements = healthMeasurementsDeferred.await()

        val composed = ComposedClinicalProfileConverter.buildComposedClinicalProfile(
            healthMeasurements = healthMeasurements,
            clinicalBackgrounds = clinicalBackground,
            personId = personId,
            healthConditions = healthConditions,
        )

        ClinicalProfileLiteResponse(
            background = composed.background.joinToString {
                val disease = (it as DiseaseBackgroundResponse)
                disease.description ?: ""
            },
            surgeryBackground = composed.surgicalBackground.joinToString {
                (it as UnstructuredBackgroundResponse).value ?: ""
            },
            medicines = composed.medicines.joinToString {
                (it as UnstructuredBackgroundResponse).value ?: ""
            },
            allergies = composed.allergies.joinToString {
                (it as UnstructuredBackgroundResponse).value ?: ""
            },
            habits = composed.habits.joinToString {
                (it as UnstructuredBackgroundResponse).value ?: ""
            },
            height = composed.height ?: "-",
            weight = composed.weight ?: "-",
        ).toResponse()
    }

    private suspend fun getHealthConditions(clinicalBackground: List<ClinicalBackground>) =
        clinicalBackground
            .filter { it.type == ClinicalBackgroundType.DISEASE }
            .map { it.specialize<DiseaseBackground>() }
            .mapNotNull { it.value }
            .let { healthConditionService.findByCodes(it) }

    suspend fun getClinicalProfile(
        personId: PersonId
    ): Response = coroutineScope {
        val pId = personId

        val cptsDeferred = async { memberCptsService.buildPersonCptsByPersonId(pId).get() }
        val healthMeasurementsDeferred = async {
            healthMeasurementService.getActivesByPersonId(
                pId, currentStaffId()
            ).get()
        }
        val clinicalBackgroundDeferred = async {
            clinicalBackgroundService.getByPersonId(
                pId, currentStaffId()
            ).get()
        }

        val clinicalBackground = clinicalBackgroundDeferred.await()
        val healthConditions = getHealthConditions(clinicalBackground).get()

        val cpts = cptsDeferred.await().removeOthers()
        val healthMeasurements = healthMeasurementsDeferred.await()

        ComposedClinicalProfileConverter.buildComposedClinicalProfile(
            cpts = cpts.conditions,
            gracePeriods = cpts.gracePeriod,
            healthMeasurements = healthMeasurements,
            clinicalBackgrounds = clinicalBackground,
            personId = pId,
            healthConditions = healthConditions,
        ).toResponse()
    }

    suspend fun updateHealthMeasurement(
        healthMeasurement: HealthMeasurementUpdateRequest
    ): Response {
        val model = HealthMeasurementRequestConverter.convert(healthMeasurement)

        val added = healthMeasurementService.addDraft(
            model, currentStaffId()
        ).get()

        val measurementResponse = HealthMeasurementResponseConverter.convert(added)

        return measurementResponse.toResponse()
    }

    suspend fun getHealthMeasurements(
        personId: PersonId,
        params: Parameters
    ): Response = coroutineScope {
        val categoryNameQuery = params["type"]
        val categoryIdQuery = params["category"]?.toUUID()

        val appointmentsDeferred = async {
            appointmentService.getAppointmentsByPerson(personId, false)
        }
        val measurementsDeferred = async {
            healthMeasurementService.getByPersonId(
                personId, currentStaffId()
            )
        }
        val measurementTypesDeferred = async {
            healthMeasurementService.getActiveTypes()
        }
        val measurementCategoriesDeferred = async {
            healthMeasurementService.getActiveCategories()
        }

        val appointments = appointmentsDeferred.await().map { list ->
            list.map { it.appointment }
        }.get()

        val allCategories = measurementCategoriesDeferred.await().get()
        val categoryToFilter = getCategoryToFilterFor(
            allCategories, categoryIdQuery, categoryNameQuery
        )

        val allMeasurementTypes = measurementTypesDeferred.await().get()
        val measurementTypesSet = if (categoryToFilter != null) {
            allMeasurementTypes.filter { it.healthMeasurementCategoryId == categoryToFilter.id }
        } else {
            allMeasurementTypes
        }.map { it.id }.toSet()

        measurementsDeferred.await().map { measurements ->
            val filteredMeasurements = measurements.filter {
                measurementTypesSet.contains(it.typeId)
            }

            HealthMeasurementHistoryLogic.getMeasurementsHistory(
                filteredMeasurements,
                appointments,
                allMeasurementTypes,
            )
        }.foldResponse()
    }

    private fun getCategoryToFilterFor(
        allCategories: List<HealthMeasurementCategory>,
        categoryId: UUID?,
        categoryKey: String?,
    ): HealthMeasurementCategory? {
        if (categoryId != null) {
            return allCategories.find { it.id == categoryId }
        }

        if (categoryKey != null) {
            return allCategories.find { it.key == categoryKey.uppercase() }
        }

        return null
    }

    suspend fun inactivateClinicalBackground(personId: PersonId, id: UUID): Response =
        clinicalBackgroundService.inactivate(personId, id, currentStaffId())
            .map { ClinicalBackgroundConverter.backgroundToResponse(it) }
            .foldResponse()

    suspend fun addClinicalBackground(requestJson: String): Response {
        val request = ClinicalBackgroundConverter.jsonToRequest(requestJson)
        val background = ClinicalBackground(
            personId = request.personId.toPersonId(),
            type = request.type,
            content = request.denied.takeIf { it.not() }
                ?.let { request.getContent() } ?: emptyMap(),
            denied = request.denied,
            addedByStaffId = currentStaffId(),
            appointmentId = request.appointmentId,
            source = STAFF
        )

        val model = clinicalBackgroundService
            .addDraft(background, currentStaffId())
            .get()

        return ClinicalBackgroundConverter.backgroundToResponse(
            model, getHealthCondition(model)
        ).toResponse()
    }

    private suspend fun getHealthCondition(clinicalBackground: ClinicalBackground) =
        if (clinicalBackground.type == ClinicalBackgroundType.DISEASE) {
            clinicalBackground.specialize<DiseaseBackground>().value?.let {
                healthConditionService.findByCode(it).getOrNullIfNotFound()
            }
        } else null
}
