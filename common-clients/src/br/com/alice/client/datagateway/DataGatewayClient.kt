package br.com.alice.client.datagateway

import br.com.alice.common.client.DefaultHttpClient
import br.com.alice.common.extensions.coResultOf
import br.com.alice.common.extensions.then
import br.com.alice.common.extensions.thenError
import br.com.alice.common.observability.Spannable
import br.com.alice.common.observability.setAttribute
import br.com.alice.common.service.serialization.gsonCompleteSerializer
import br.com.alice.common.service.serialization.gsonSnakeCase
import com.github.kittinunf.result.Result
import com.github.kittinunf.result.map
import com.google.gson.reflect.TypeToken
import io.ktor.client.HttpClient
import io.ktor.client.plugins.ResponseException
import io.ktor.client.plugins.contentnegotiation.ContentNegotiation
import io.ktor.client.request.get
import io.ktor.client.statement.bodyAsText
import io.opentelemetry.api.trace.Span
import io.opentelemetry.api.trace.StatusCode
import kotlin.reflect.KClass

class DataGatewayClient(
    private val httpClient: HttpClient = DefaultHttpClient({
        install(ContentNegotiation) {
            gsonSnakeCase()
        }
    }, timeoutInMillis = DataGatewayClientConfiguration.timeout())
) : Spannable {

    private val apiUrl = DataGatewayClientConfiguration.apiUrl()

    suspend fun <T : Any> search(
        tableName: String,
        params: DataGatewayFilterParams = DataGatewayFilterParams(),
        response: KClass<T>
    ): Result<DataGatewayResponse<T>, Throwable> =
        span("search") { span ->
            span.setAttribute("filters", params.filters)
            span.setAttribute("sort", params.sort)
            span.setAttribute("fields", params.fields)
            span.setAttribute("page_size", params.pageSize)
            span.setAttribute("page_number", params.pageNumber)

            val responseType = TypeToken.getParameterized(DataGatewayResponse::class.java, response.java).type

            coResultOf<String, Throwable> {
                httpClient.get("$apiUrl/${tableName}${DataGatewayParamsBuilder.buildParams(params)}")
                    .bodyAsText()
            }
                .map { gsonCompleteSerializer.fromJson(it, responseType) as DataGatewayResponse<T> }
                .recordResponse(span)
        }

    private suspend fun <T : Any> Result<T, Throwable>.recordResponse(span: Span) =
        this.then { span.setStatus(StatusCode.OK) }
            .thenError { error ->
                span.setStatus(StatusCode.ERROR)

                if (error is ResponseException)
                    span.setAttribute("response_error_body", error.response.bodyAsText())
            }
}
