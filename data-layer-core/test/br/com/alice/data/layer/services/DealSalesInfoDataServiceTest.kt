package br.com.alice.data.layer.services

import br.com.alice.common.data.dsl.matchers.ResultAssert
import br.com.alice.data.layer.helpers.DataServiceTestHelper
import br.com.alice.data.layer.helpers.TestTableFactory
import br.com.alice.data.layer.models.DealSalesInfo
import br.com.alice.data.layer.repositories.JdbiRepositoryFactory
import br.com.alice.data.layer.tables.HealthProductSimulationTable
import kotlinx.coroutines.runBlocking
import org.assertj.core.api.Assertions.assertThat
import kotlin.test.Test

class DealSalesInfoDataServiceTest : DataServiceTestHelper() {

    private val repoFactory = JdbiRepositoryFactory(mainJdbi)
    private val simulationRepo = repoFactory.get(HealthProductSimulationTable::class)
    private val data  = DealSalesInfoDataServiceClient(httpInvoker)

    @Test
    fun `#add should add new sales deal with all required information`() = runBlocking<Unit> {
        val simulation = TestTableFactory.buildHealthProductSimulationTable()
        simulationRepo.add(simulation)

        val newSalesInfo = DealSalesInfo(simulationId = simulation.id)

        val dealSalesInfoAdded = data.add(newSalesInfo)
        ResultAssert.assertThat(dealSalesInfoAdded).isSuccess()

        val salesInfoFound = data.findBySimulationId(simulation.id)
        assertThat(salesInfoFound?.id).isEqualTo(dealSalesInfoAdded.get().id)
        assertThat(salesInfoFound?.simulationId).isEqualTo(simulation.id)
    }

}
