package br.com.alice.bottini.services.questions

import br.com.alice.bottini.models.HealthProductSimulationQuestion
import br.com.alice.data.layer.models.HealthProductSimulation
import br.com.alice.data.layer.models.HealthProductSimulationQuestionType
import kotlinx.coroutines.runBlocking
import org.assertj.core.api.Assertions.assertThat
import kotlin.test.Test

class AgesQuestionAlgorithmTest {

    private val simulation = HealthProductSimulation()

    @Test
    fun `#getQuestion should return expected question`() = runBlocking<Unit> {
        val actual = AgesQuestionAlgorithm.getQuestion()
        val expected = HealthProductSimulationQuestion(type = HealthProductSimulationQuestionType.AGES)

        assertThat(actual).isEqualTo(expected)
    }

    @Test
    fun `#nextQuestion should return POSTAL_CODE question `() = runBlocking<Unit> {
        val actual = AgesQuestionAlgorithm.nextQuestion(simulation)
        val expected = HealthProductSimulationQuestionType.POSTAL_CODE

        assertThat(actual).isEqualTo(expected)
    }

    @Test
    fun `#defineSimulationType should return null`() = runBlocking<Unit> {
        val actual = AgesQuestionAlgorithm.defineSimulationType("")

        assertThat(actual).isEqualTo(null)
    }

}
