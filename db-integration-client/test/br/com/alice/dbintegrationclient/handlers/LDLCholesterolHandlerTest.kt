package br.com.alice.dbintegrationclient.handlers

import br.com.alice.data.layer.models.ReferenceRange
import io.mockk.every
import io.mockk.mockk
import io.mockk.verify
import org.assertj.core.api.Assertions.assertThat
import kotlin.test.BeforeTest
import kotlin.test.Test

class LDLCholesterolHandlerTest {

    private val nextHandler = mockk<BetweenValuesHandler>()
    private val handler = LDLCholesterolHandler(nextHandler)

    @BeforeTest
    fun beforeTest() {
        every { nextHandler.parseResult(any(), any(), any()) } returns mockk()
    }

    @Test
    fun `parseResult - null referenceValue`() {
        handler.parseResult(null, "result", "measurement")

        verify(exactly = 1) { nextHandler.parseResult(any(), any(), any()) }
    }

    @Test
    fun `parseResult - null resultValue`() {
        handler.parseResult("reference", null, "measurement")

        verify(exactly = 1) { nextHandler.parseResult(any(), any(), any()) }
    }

    @Test
    fun `parseResult - regex does not matches exactly`() {
        handler.parseResult("mulher ... Superior a 3,10 ng/mL", "3,10", "ng/mL")

        verify(exactly = 1) { nextHandler.parseResult(any(), any(), any()) }
    }

    @Test
    fun `parseResult - result value in Low Reference Range - returns within normal`() {
        val resultValue = "Adultos acima de 20 anos:\r\n\r\nCategoria de risco     \r\n"
            .plus("Baixo                    Inferior a 130 mg/dL\r\nIntermediário            Inferior a 100 mg/dL\r\n")
            .plus("Alto                     Inferior a  70 mg/dL\r\nMuito alto               Inferior a  50 mg/dL\r\n")
            .plus("\r\nValor de referência desejável")

        val result = handler.parseResult(
            resultValue,
            "111",
            "mg/dL"
        )

        assertThat(result).isEqualTo(ReferenceRange.WITHIN_NORMAL_LIMIT)
        verify(exactly = 0) { nextHandler.parseResult(any(), any(), any()) }
    }

    @Test
    fun `parseResult - result value in Normal Reference Range - returns within normal`() {
        val resultValue = "Adultos acima de 20 anos:\r\n\r\nCategoria de risco     \r\n"
            .plus("Baixo                    Inferior a 130 mg/dL\r\nIntermediário            Inferior a 100 mg/dL\r\n")
            .plus("Alto                     Inferior a  70 mg/dL\r\nMuito alto               Inferior a  50 mg/dL\r\n")
            .plus("\r\nValor de referência desejável")

        val result = handler.parseResult(
            resultValue,
            "99",
            "mg/dL"
        )

        assertThat(result).isEqualTo(ReferenceRange.WITHIN_NORMAL_LIMIT)
        verify(exactly = 0) { nextHandler.parseResult(any(), any(), any()) }
    }

    @Test
    fun `parseResult - result value in High Reference Range - returns upper reference`() {
        val resultValue = "Adultos acima de 20 anos:\r\n\r\nCategoria de risco     \r\n"
            .plus("Baixo                    Inferior a 130 mg/dL\r\nIntermediário            Inferior a 100 mg/dL\r\n")
            .plus("Alto                     Inferior a  70 mg/dL\r\nMuito alto               Inferior a  50 mg/dL\r\n")
            .plus("\r\nValor de referência desejável")

        val result = handler.parseResult(
            resultValue,
            "65",
            "mg/dL"
        )

        assertThat(result).isEqualTo(ReferenceRange.UPPER_REFERENCE_LIMIT)
        verify(exactly = 0) { nextHandler.parseResult(any(), any(), any()) }
    }

    @Test
    fun `parseResult - result value in Very High Reference Range - returns upper reference`() {
        val resultValue = "Adultos acima de 20 anos:\r\n\r\nCategoria de risco     \r\n"
            .plus("Baixo                    Inferior a 130 mg/dL\r\nIntermediário            Inferior a 100 mg/dL\r\n")
            .plus("Alto                     Inferior a  70 mg/dL\r\nMuito alto               Inferior a  50 mg/dL\r\n")
            .plus("\r\nValor de referência desejável")

        val result = handler.parseResult(
            resultValue,
            "40",
            "mg/dL"
        )

        assertThat(result).isEqualTo(ReferenceRange.UPPER_REFERENCE_LIMIT)
        verify(exactly = 0) { nextHandler.parseResult(any(), any(), any()) }
    }

}
