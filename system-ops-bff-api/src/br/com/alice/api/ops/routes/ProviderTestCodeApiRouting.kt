import br.com.alice.api.ops.controllers.ProviderTestCodeController
import br.com.alice.common.coHandler
import io.ktor.server.auth.authenticate
import io.ktor.server.routing.Routing
import io.ktor.server.routing.delete
import io.ktor.server.routing.get
import io.ktor.server.routing.post
import io.ktor.server.routing.put
import io.ktor.server.routing.route
import org.koin.ktor.ext.inject

fun Routing.providerTestCodeRoutes() {

    authenticate {
        val providerTestCodeController by inject<ProviderTestCodeController>()
        route("providerTestCode") {
            get { coHandler(providerTestCodeController::index) }
            get("/{providerTestCodeId}") { coHandler("providerTestCodeId", providerTestCodeController::getById) }
            post { coHandler(providerTestCodeController::create) }
            put("/{providerTestCodeId}") { coHandler("providerTestCodeId", providerTestCodeController::update) }
            delete("/{providerTestCodeId}") { coHandler("providerTestCodeId", providerTestCodeController::delete) }
        }
        route("dataIntegration"){
            get("/") { coHandler(providerTestCodeController::getDataIntegration) }
        }
    }
}
