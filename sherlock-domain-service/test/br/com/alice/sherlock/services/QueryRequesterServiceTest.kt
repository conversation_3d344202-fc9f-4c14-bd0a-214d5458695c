package br.com.alice.sherlock.services

import br.com.alice.common.core.Role
import br.com.alice.common.data.dsl.matchers.ResultAssert.Companion.assertThat
import br.com.alice.common.kafka.interfaces.KafkaProducerService
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.PIIField
import br.com.alice.sherlock.client.CustomQueryValidationException
import br.com.alice.sherlock.client.EmptyDescriptionException
import br.com.alice.sherlock.client.EmptyQueryException
import br.com.alice.sherlock.event.QueryRequestedEvent
import br.com.alice.sherlock.logics.InvalidQuerySyntax
import br.com.alice.sherlock.logics.QueryValidator
import com.github.kittinunf.result.failure
import com.github.kittinunf.result.success
import io.mockk.called
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.every
import io.mockk.mockk
import kotlinx.coroutines.runBlocking
import java.util.UUID
import kotlin.test.Test

class QueryRequesterServiceTest {

    private val kafkaProducerService: KafkaProducerService = mockk()
    private val queryValidator: QueryValidator = mockk()
    private val queryRequesterService = QueryRequesterServiceImpl(kafkaProducerService, queryValidator)

    private val staff = TestModelFactory.buildStaff(role = Role.PRODUCT_TECH)
    private val query = "select personId from person"
    private val description = "Query to get all CPFs"

    @Test
    fun `#requestQuery should return success if requester does not have ProdTech role`() = runBlocking {
        every { queryValidator.validate(query) } returns Unit.success()
        coEvery { kafkaProducerService.produce(any()) } returns mockk()
        val staffWithoutPermission = staff.copy(role = Role.MEMBER_OPS)

        val result = queryRequesterService.requestQuery(
            requester = staffWithoutPermission,
            customQuery = query,
            description = description,
            piiFields = listOf(PIIField.CPF)
        )

        val dto = result.get()

        val expectedEvent = QueryRequestedEvent(
            query = query,
            description = description,
            piiFields = listOf(PIIField.CPF),
            requesterId = staff.id
        ).copy(requestId = UUID.fromString(dto.requestId))

        coVerify { kafkaProducerService.produce(expectedEvent) }
    }

    @Test
    fun `#requestQuery should return failure if query is empty`() = runBlocking {
        val result = queryRequesterService.requestQuery(
            requester = staff,
            customQuery = "",
            description = description,
            piiFields = listOf(PIIField.CPF)
        )

        assertThat(result).isFailureOfType(EmptyQueryException::class)
        coVerify { kafkaProducerService wasNot called }
    }

    @Test
    fun `#requestQuery should return failure if description is empty`() = runBlocking {
        val result = queryRequesterService.requestQuery(
            requester = staff,
            customQuery = query,
            description = "",
            piiFields = listOf(PIIField.CPF)
        )

        assertThat(result).isFailureOfType(EmptyDescriptionException::class)
        coVerify { kafkaProducerService wasNot called }
    }

    @Test
    fun `#requestQuery should return failure if query syntax is not valid`() = runBlocking {
        every { queryValidator.validate(query) } returns InvalidQuerySyntax("Invalid syntax").failure()

        val result = queryRequesterService.requestQuery(
            requester = staff,
            customQuery = query,
            description = description,
            piiFields = listOf(PIIField.CPF)
        )

        assertThat(result).isFailureOfType(CustomQueryValidationException::class)
        coVerify { kafkaProducerService wasNot called }
    }

    @Test
    fun `#requestQuery should return request id when query is correctly requested`() = runBlocking {
        every { queryValidator.validate(query) } returns Unit.success()
        coEvery { kafkaProducerService.produce(any()) } returns mockk()

        val result = queryRequesterService.requestQuery(
            requester = staff,
            customQuery = query,
            description = description,
            piiFields = listOf(PIIField.CPF)
        )

        val dto = result.get()

        val expectedEvent = QueryRequestedEvent(
            query = query,
            description = description,
            piiFields = listOf(PIIField.CPF),
            requesterId = staff.id
        ).copy(requestId = UUID.fromString(dto.requestId))

        coVerify { kafkaProducerService.produce(expectedEvent) }
    }
}
