package br.com.alice.client.datagateway

data class DataGatewayFilter<T>(
    val column: String,
    val operator: DataGatewayFilterOperator,
    val value: T? = null
)

enum class DataGatewayFilterOperator(val filterKey: String) {
    EQUALS(""),
    NOT_EQUALS("[ne]"),
    IN(""),
    NOT_IN("[not_in]"),
    LIKE("[like]"),
    GREATER_THAN("[gt]"),
    LESS_THAN("[lt]"),
    IS_NULL("[is_null]"),
    IS_NOT_NULL("[is_not_null]");

}

data class DataGatewaySort(
    val column: String,
    val order: DataGatewaySortOrder = DataGatewaySortOrder.ASC
)

enum class DataGatewaySortOrder {
    ASC, DESC
}

data class DataGatewayFilterParams(
    val filters: List<DataGatewayFilter<*>>? = null,
    val sort: List<DataGatewaySort>? = null,
    val fields: List<String>? = null,
    val pageNumber: Long? = null,
    val pageSize: Long? = null
)

data class DataGatewayResponseMetadata(
    val total: Long,
    val page: Long,
    val pageSize: Long,
    val firstPage: Long,
    val lastPage: Long,
    val hasPreviousPage: Boolean,
    val hasNextPage: Boolean,
    val previousPage: String?,
    val nextPage: String?

)

data class DataGatewayResponse<T>(
    val data: List<T>,
    val meta: DataGatewayResponseMetadata
)
