plugins {
    kotlin
    application
    id("com.github.johnrengelman.shadow")
    id("com.google.cloud.tools.jib")
    id("org.sonarqube")
}

group = "br.com.alice.backoffice-bff-api"
version = aliceBackofficeBffApiVersion

application {
    mainClass.set("io.ktor.server.netty.EngineMain")
}

sourceSets {
    main {
        kotlin.sourceDirs = files("src", "${layout.buildDirectory}/generated/source/kotlin")
        resources.sourceDirs = files("resources")
    }
    test {
        kotlin.sourceDirs = files("test")
        resources.sourceDirs = files("testResources")
    }
}

sonarqube {
    properties {
        property("sonar.projectKey", "mono:backoffice-bff-api")
        property("sonar.organization", "alice-health")
        property("sonar.host.url", "https://sonarcloud.io")
    }
}

tasks {
    shadowJar {
        isZip64 = true
    }
}

dependencies {
    implementation(project(":common"))
    implementation(project(":common-service"))
    implementation(project(":common-kafka"))
    implementation(project(":data-layer-client"))
    implementation(project(":test-result-domain-client"))
    implementation(project(":data-packages:staff-domain-service-data-package"))
    implementation(project(":data-packages:atlas-domain-service-data-package"))
    implementation(project(":data-packages:business-domain-service-data-package"))
    implementation(project(":data-packages:business-domain-service-data-package"))
    implementation(project(":data-packages:sales-channel-domain-service-data-package"))
    implementation(project(":data-packages:exec-indicator-domain-service-data-package"))
    implementation(project(":data-packages:bottini-domain-service-data-package"))
    implementation(project(":data-packages:product-domain-service-data-package"))
    implementation(project(":data-packages:provider-domain-service-data-package"))
    implementation(project(":data-packages:coverage-domain-service-data-package"))
    implementation(project(":data-packages:ehr-domain-service-data-package"))
    implementation(project(":data-packages:person-domain-service-data-package"))
    implementation(project(":data-packages:clinical-account-domain-service-data-package"))
    implementation(project(":data-packages:hippocrates-domain-service-data-package"))
    implementation(project(":data-packages:amas-domain-service-data-package"))
    implementation(project(":data-packages:file-vault-service-data-package"))

    implementation(project(":feature-config-domain-client"))
    implementation(project(":business-domain-client"))
    implementation(project(":atlas-domain-client"))
    implementation(project(":staff-domain-client"))
    implementation(project(":sales-channel-domain-client"))
    implementation(project(":exec-indicator-domain-client"))
    implementation(project(":bottini-domain-client"))
    implementation(project(":product-domain-client"))
    implementation(project(":provider-domain-client"))
    implementation(project(":coverage-domain-client"))
    implementation(project(":ehr-domain-client"))
    implementation(project(":person-domain-client"))
    implementation(project(":clinical-account-domain-client"))
    implementation(project(":hippocrates-domain-client"))
    implementation(project(":hr-core-domain-client"))
    implementation(project(":amas-domain-client"))
    implementation(project(":file-vault-client"))

    implementation("com.google.firebase:firebase-admin:$firebaseAdminVersion")
    implementation("org.apache.commons:commons-csv:$commonsCsvVersion")

    implementation("io.ktor:ktor-server-cors:$ktor2Version")
    implementation("io.ktor:ktor-server-swagger:$ktor2Version")

    ktor2Dependencies()

    testImplementation(project(":common-tests"))
    testImplementation(project(":data-layer-common-tests"))
    test2Dependencies()
}
