package br.com.alice.api.healthcareops.controllers.business

import br.com.alice.api.healthcareops.api.RoutesTestHelper
import br.com.alice.api.healthcareops.converters.ProductConverter
import br.com.alice.api.healthcareops.models.BillingAccountablePartyRequest
import br.com.alice.api.healthcareops.models.BillingAccountablePartyResponse
import br.com.alice.api.healthcareops.models.CompanyContractRequest
import br.com.alice.api.healthcareops.models.CompanyContractResponse
import br.com.alice.api.healthcareops.models.ContractFileResponse
import br.com.alice.business.client.CompanyContractService
import br.com.alice.business.client.CompanyContractService.InvalidFormatException
import br.com.alice.business.client.CompanyContractService.RequiredMainContractException
import br.com.alice.business.client.CompanyService
import br.com.alice.common.RangeUUID
import br.com.alice.common.convertTo
import br.com.alice.common.core.exceptions.DuplicatedItemException
import br.com.alice.common.core.exceptions.RequiredFieldException
import br.com.alice.common.data.dsl.matchers.ResponseAssert
import br.com.alice.common.helpers.bodyAsJson
import br.com.alice.common.helpers.coVerifyNone
import br.com.alice.common.helpers.returns
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.BillingAccountablePartyType
import br.com.alice.data.layer.models.ContractFile
import br.com.alice.data.layer.models.ContractType
import br.com.alice.filevault.client.FileVaultActionService
import br.com.alice.filevault.models.VaultResponse
import br.com.alice.moneyin.client.BillingAccountablePartyService
import br.com.alice.product.client.ProductService
import io.ktor.http.HttpMethod
import io.mockk.coEvery
import io.mockk.mockk
import org.assertj.core.api.Assertions
import org.junit.jupiter.api.Nested
import java.time.LocalDate
import java.util.UUID
import kotlin.test.BeforeTest
import kotlin.test.Test

class CompanyContractControllerTest : RoutesTestHelper() {
    private val companyContractService: CompanyContractService = mockk()
    private val fileVaultActionService: FileVaultActionService = mockk()
    private val productService: ProductService = mockk()
    private val billingAccountablePartyService: BillingAccountablePartyService = mockk()
    private val companyService: CompanyService = mockk()

    @BeforeTest
    override fun setup() {
        super.setup()
        module.single {
            CompanyContractController(
                companyContractService,
                fileVaultActionService,
                productService,
                billingAccountablePartyService,
                companyService,
            )
        }
    }


    @Nested
    inner class ListCompanyContractByTitle {
        @Test
        fun `#should return contracts`() {
            val query = "00"

            val fileContractId = RangeUUID.generate()
            val contractUrl = "http://localhost:9000/file/${fileContractId}"
            val vaultResponse = buildVaultResponse(contractUrl, fileContractId, "contrato.pdf")
            val companyContract = TestModelFactory.buildCompanyContract(
                contractFileIds = listOf(
                    ContractFile(
                        id = fileContractId,
                        type = ContractType.MAIN,
                    )
                )
            )

            coEvery { companyContractService.findByTitle(query) } returns listOf(companyContract)

            coEvery { fileVaultActionService.securedGenericLinks(any()) } returns listOf(vaultResponse)

            val expected = CompanyContractResponse(
                id = companyContract.id,
                externalId = companyContract.externalId,
                title = companyContract.title,
                startedAt = companyContract.startedAt,
                accountableEmail = companyContract.accountableEmail,
                contractFileIds = companyContract.contractFileIds,
                availableProducts = emptyList(),
                billingAccountableParty = null,
                contractFiles = listOf(
                    ContractFileResponse(
                        id = fileContractId,
                        type = ContractType.MAIN,
                    )
                ),
                nature = companyContract.nature,
            )

            authenticatedAs(token, staffToBeAuthenticated) {
                get("/company/contract?query=$query") { response ->
                    ResponseAssert.assertThat(response).isOKWithData(
                        listOf(expected)
                    )
                }
            }
        }

        @Test
        fun `#should not return contracts when the query param is not passed`() {
            val query = "00"

            val companyContract = TestModelFactory.buildCompanyContract()

            coEvery { companyContractService.findByTitle(query) } returns listOf(companyContract)

            authenticatedAs(token, staffToBeAuthenticated) {
                get("/company/contract") { response ->
                    ResponseAssert.assertThat(response).isBadRequestWithErrorCode(
                        "required_field_missing"
                    )
                }
            }
        }
    }

    @Nested
    inner class GetCompanyContractById {
        @Test
        fun `#should return a contract`() {
            val billingAccountableParty = TestModelFactory.buildBillingAccountableParty()
            val product = TestModelFactory.buildProduct()
            val fileContractId = RangeUUID.generate()
            val contractUrl = "http://localhost:9000/file/${fileContractId}"
            val vaultResponse = buildVaultResponse(contractUrl, fileContractId, "contrato.pdf")
            val companyContract =
                TestModelFactory.buildCompanyContract(
                    billingAccountablePartyId = billingAccountableParty.id,
                    availableProducts = listOf(product.id),
                    defaultProductId = product.id,
                    contractFileIds = listOf(
                        ContractFile(
                            id = fileContractId,
                            type = ContractType.MAIN,
                        )
                    )
                )

            coEvery { companyContractService.get(companyContract.id) } returns companyContract
            coEvery { billingAccountablePartyService.get(companyContract.billingAccountablePartyId!!) } returns billingAccountableParty
            coEvery {
                productService.findByIds(
                    companyContract.availableProducts!!,
                    ProductService.FindOptions(withPriceListing = false)
                )
            } returns listOf(product)

            coEvery { fileVaultActionService.securedGenericLinks(any()) } returns listOf(vaultResponse)

            val expected = CompanyContractResponse(
                id = companyContract.id,
                externalId = companyContract.externalId,
                title = companyContract.title,
                startedAt = companyContract.startedAt,
                accountableEmail = companyContract.accountableEmail,
                contractFileIds = companyContract.contractFileIds,
                billingAccountablePartyId = companyContract.billingAccountablePartyId,
                defaultProductId = ProductConverter.convert(product),
                availableProducts = listOf(
                    ProductConverter.convert(product),
                ),
                billingAccountableParty = billingAccountableParty.convertTo(BillingAccountablePartyResponse::class),
                contractFiles = listOf(
                    ContractFileResponse(
                        id = fileContractId,
                        type = ContractType.MAIN,
                        url = contractUrl,
                        filename = "contrato.pdf",
                    )
                ),
                nature = companyContract.nature,
            )

            authenticatedAs(token, staffToBeAuthenticated) {
                get("/company/contract/${companyContract.id}") { response ->
                    ResponseAssert.assertThat(response).isOKWithData(
                        expected
                    )
                }
            }
        }
    }

    @Nested
    inner class UploadContractFileToContractCompany {
        @Test
        fun `#uploadContractAndAdditions - should return a contract file`() {
            val file = TestModelFactory.buildGenericFileVault()

            coEvery { fileVaultActionService.uploadGenericFile(any()) } returns file

            val expected = ContractFile(
                id = file.id,
                type = ContractType.AMENDMENT,
            )

            authenticatedAs(token, staffToBeAuthenticated) {
                multipart(
                    HttpMethod.Post, "/company/contract/upload",
                    fileName = "apple.docx.png",
                    parameters = mapOf("type" to "AMENDMENT")
                ) { response ->
                    ResponseAssert.assertThat(response).isOKWithData(expected)
                }
            }
        }

        @Test
        fun `#uploadContractAndAdditions - shouldn't return a contract when upload is failed`() {
            coEvery { fileVaultActionService.uploadGenericFile(any()) } returns Exception("Something is wrong")

            authenticatedAs(token, staffToBeAuthenticated) {
                multipart(
                    HttpMethod.Post, "/company/contract/upload",
                    fileName = "apple.png",
                    parameters = mapOf("type" to "AMENDMENT")
                ) { response ->
                    ResponseAssert.assertThat(response).isInternalServerError()
                }
            }
        }
    }


    @Nested
    inner class CreateCompanyContract {
        private val company = TestModelFactory.buildCompany()

        private val billingAccountableParty = TestModelFactory.buildBillingAccountableParty()

        private val fileContractId = RangeUUID.generate()
        private val contractFileId = RangeUUID.generate()
        private val defaultProductId = RangeUUID.generate()
        private val availableProducts = listOf(defaultProductId)
        val request = CompanyContractRequest(
            externalId = "123456",
            title = "contrato abc",
            startedAt = "2023-10-03",
            accountableEmail = "<EMAIL>",
            contractFileIds = listOf(ContractFile(id = contractFileId, type = ContractType.MAIN)),
            defaultProductId = defaultProductId,
            availableProducts = availableProducts,
            nature = "20009",
        )

        private val companyContract = TestModelFactory.buildCompanyContract(
            externalId = request.externalId,
            title = request.title,
            startedAt = LocalDate.parse(request.startedAt),
            accountableEmail = request.accountableEmail,
            contractFileIds = listOf(
                ContractFile(
                    id = fileContractId,
                    type = ContractType.MAIN,
                )
            ),
            defaultProductId = defaultProductId,
            availableProducts = availableProducts,
        )

        @Test
        fun `should create a companyContract and associate to company`() {
            val product = TestModelFactory.buildProduct(id = defaultProductId)
            val contractUrl = "http://localhost:9000/file/${fileContractId}"
            val vaultResponse = buildVaultResponse(contractUrl, fileContractId, "contrato.pdf")

            val expected = CompanyContractResponse(
                id = companyContract.id,
                externalId = companyContract.externalId,
                title = companyContract.title,
                startedAt = companyContract.startedAt,
                accountableEmail = companyContract.accountableEmail,
                contractFileIds = companyContract.contractFileIds,
                defaultProductId = ProductConverter.convert(product),
                availableProducts = listOf(
                    ProductConverter.convert(product),
                ),
                contractFiles = listOf(
                    ContractFileResponse(
                        id = fileContractId,
                        type = ContractType.MAIN,
                        url = contractUrl,
                        filename = "contrato.pdf",
                    )
                ),
                billingAccountablePartyId = null,
                billingAccountableParty = null,
                nature = companyContract.nature,
            )

            coEvery {
                companyContractService.add(match {
                    it.title == request.title && it.accountableEmail == request.accountableEmail
                })
            } returns companyContract
            coEvery {
                productService.findByIds(
                    availableProducts,
                    ProductService.FindOptions(withPriceListing = false),
                )
            } returns listOf(product)
            coEvery { companyService.get(company.id) } returns company
            coEvery {
                companyService.update(
                    match { it.id == company.id && companyContract.id in it.contractIds },
                    true
                )
            } returns company.copy(
                contractIds = listOf(companyContract.id),
            )

            coEvery { fileVaultActionService.securedGenericLinks(any()) } returns listOf(vaultResponse)

            authenticatedAs(token, staffToBeAuthenticated) {
                post("/company/${company.id}/contract", request) { response ->
                    ResponseAssert.assertThat(response).isSuccessfulJson()

                    val content: CompanyContractResponse = response.bodyAsJson()
                    Assertions.assertThat(content).isEqualTo(expected)
                }
            }

            coVerifyNone { billingAccountablePartyService.add(any()) }
            coVerifyNone { billingAccountablePartyService.get(any()) }
            coVerifyNone { billingAccountablePartyService.findNationalIdEq(any()) }
        }

        @Test
        fun `should create a companyContract and associate to company and create the billing accountable party`() {
            val billingAccountableParty = billingAccountableParty.copy(type = BillingAccountablePartyType.LEGAL_PERSON)
            val request = request.copy(
                billingAccountableParty = BillingAccountablePartyRequest(
                    nationalId = billingAccountableParty.nationalId,
                    type = billingAccountableParty.type,
                    address = billingAccountableParty.address!!,
                    email = billingAccountableParty.email,
                    firstName = billingAccountableParty.firstName,
                    lastName = billingAccountableParty.lastName
                )
            )
            val product = TestModelFactory.buildProduct(id = defaultProductId)
            val contractUrl = "http://localhost:9000/file/${fileContractId}"
            val vaultResponse = buildVaultResponse(contractUrl, fileContractId, "contrato.pdf")

            val expected = CompanyContractResponse(
                id = companyContract.id,
                externalId = companyContract.externalId,
                title = companyContract.title,
                startedAt = companyContract.startedAt,
                accountableEmail = companyContract.accountableEmail,
                contractFileIds = companyContract.contractFileIds,
                defaultProductId = ProductConverter.convert(product),
                availableProducts = listOf(
                    ProductConverter.convert(product),
                ),
                contractFiles = listOf(
                    ContractFileResponse(
                        id = fileContractId,
                        type = ContractType.MAIN,
                        url = contractUrl,
                        filename = "contrato.pdf",
                    )
                ),
                billingAccountablePartyId = billingAccountableParty.id,
                billingAccountableParty = billingAccountableParty.convertTo(BillingAccountablePartyResponse::class),
                nature = companyContract.nature,
            )

            coEvery {
                companyContractService.add(match {
                    it.title == request.title && it.accountableEmail == request.accountableEmail
                })
            } returns companyContract.copy(billingAccountablePartyId = billingAccountableParty.id)
            coEvery {
                billingAccountablePartyService.add(match {
                    it.nationalId == request.billingAccountableParty?.nationalId &&
                            it.type == request?.billingAccountableParty?.type
                })
            } returns billingAccountableParty
            coEvery {
                productService.findByIds(
                    availableProducts,
                    ProductService.FindOptions(withPriceListing = false),
                )
            } returns listOf(product)
            coEvery { companyService.get(company.id) } returns company
            coEvery {
                companyService.update(
                    match { it.id == company.id && companyContract.id in it.contractIds },
                    true
                )
            } returns company.copy(
                contractIds = listOf(companyContract.id),
            )

            coEvery { fileVaultActionService.securedGenericLinks(any()) } returns listOf(vaultResponse)

            authenticatedAs(token, staffToBeAuthenticated) {
                post("/company/${company.id}/contract", request) { response ->
                    ResponseAssert.assertThat(response).isSuccessfulJson()

                    val content: CompanyContractResponse = response.bodyAsJson()
                    Assertions.assertThat(content).isEqualTo(expected)
                }
            }

            coVerifyNone { billingAccountablePartyService.get(any()) }
            coVerifyNone { billingAccountablePartyService.findNationalIdEq(any()) }
        }

        @Test
        fun `should create a companyContract and associate to company and try to create but get already the billing accountable party created`() {
            val request = request.copy(
                billingAccountableParty = BillingAccountablePartyRequest(
                    nationalId = billingAccountableParty.nationalId,
                    type = BillingAccountablePartyType.LEGAL_PERSON,
                    address = billingAccountableParty.address!!,
                    email = billingAccountableParty.email,
                    firstName = billingAccountableParty.firstName,
                    lastName = billingAccountableParty.lastName
                )
            )
            val product = TestModelFactory.buildProduct(id = defaultProductId)
            val contractUrl = "http://localhost:9000/file/${fileContractId}"
            val vaultResponse = buildVaultResponse(contractUrl, fileContractId, "contrato.pdf")

            val expected = CompanyContractResponse(
                id = companyContract.id,
                externalId = companyContract.externalId,
                title = companyContract.title,
                startedAt = companyContract.startedAt,
                accountableEmail = companyContract.accountableEmail,
                contractFileIds = companyContract.contractFileIds,
                defaultProductId = ProductConverter.convert(product),
                availableProducts = listOf(
                    ProductConverter.convert(product),
                ),
                contractFiles = listOf(
                    ContractFileResponse(
                        id = fileContractId,
                        type = ContractType.MAIN,
                        url = contractUrl,
                        filename = "contrato.pdf",
                    )
                ),
                billingAccountablePartyId = billingAccountableParty.id,
                billingAccountableParty = billingAccountableParty.convertTo(BillingAccountablePartyResponse::class),
                nature = companyContract.nature,
            )

            coEvery {
                companyContractService.add(match {
                    it.title == request.title && it.accountableEmail == request.accountableEmail
                })
            } returns companyContract.copy(billingAccountablePartyId = billingAccountableParty.id)
            coEvery {
                billingAccountablePartyService.add(match { it.nationalId == request.billingAccountableParty?.nationalId })
            } returns DuplicatedItemException("")
            coEvery {
                billingAccountablePartyService.findNationalIdEq(billingAccountableParty.nationalId)
            } returns billingAccountableParty
            coEvery {
                productService.findByIds(
                    availableProducts,
                    ProductService.FindOptions(withPriceListing = false),
                )
            } returns listOf(product)
            coEvery { companyService.get(company.id) } returns company
            coEvery {
                companyService.update(
                    match { it.id == company.id && companyContract.id in it.contractIds },
                    true
                )
            } returns company.copy(
                contractIds = listOf(companyContract.id),
            )

            coEvery { fileVaultActionService.securedGenericLinks(any()) } returns listOf(vaultResponse)

            authenticatedAs(token, staffToBeAuthenticated) {
                post("/company/${company.id}/contract", request) { response ->
                    ResponseAssert.assertThat(response).isSuccessfulJson()

                    val content: CompanyContractResponse = response.bodyAsJson()
                    Assertions.assertThat(content).isEqualTo(expected)
                }
            }
        }

        @Test
        fun `should create a companyContract and associate to company and get the billing accountable party`() {
            val request = request.copy(billingAccountablePartyId = billingAccountableParty.id)
            val product = TestModelFactory.buildProduct(id = defaultProductId)
            val contractUrl = "http://localhost:9000/file/${fileContractId}"
            val vaultResponse = buildVaultResponse(contractUrl, fileContractId, "contrato.pdf")

            val expected = CompanyContractResponse(
                id = companyContract.id,
                externalId = companyContract.externalId,
                title = companyContract.title,
                startedAt = companyContract.startedAt,
                accountableEmail = companyContract.accountableEmail,
                contractFileIds = companyContract.contractFileIds,
                defaultProductId = ProductConverter.convert(product),
                availableProducts = listOf(
                    ProductConverter.convert(product),
                ),
                contractFiles = listOf(
                    ContractFileResponse(
                        id = fileContractId,
                        type = ContractType.MAIN,
                        url = contractUrl,
                        filename = "contrato.pdf",
                    )
                ),
                billingAccountablePartyId = billingAccountableParty.id,
                billingAccountableParty = billingAccountableParty.convertTo(BillingAccountablePartyResponse::class),
                nature = companyContract.nature,
            )

            coEvery {
                companyContractService.add(match {
                    it.title == request.title && it.accountableEmail == request.accountableEmail
                })
            } returns companyContract.copy(billingAccountablePartyId = billingAccountableParty.id)

            coEvery {
                billingAccountablePartyService.get(billingAccountableParty.id)
            } returns billingAccountableParty
            coEvery {
                productService.findByIds(
                    availableProducts,
                    ProductService.FindOptions(withPriceListing = false),
                )
            } returns listOf(product)
            coEvery { companyService.get(company.id) } returns company
            coEvery {
                companyService.update(
                    match { it.id == company.id && companyContract.id in it.contractIds},
                    true
                )
            } returns company.copy(
                contractIds = listOf(companyContract.id),
            )

            coEvery { fileVaultActionService.securedGenericLinks(any()) } returns listOf(vaultResponse)

            authenticatedAs(token, staffToBeAuthenticated) {
                post("/company/${company.id}/contract", request) { response ->
                    ResponseAssert.assertThat(response).isSuccessfulJson()

                    val content: CompanyContractResponse = response.bodyAsJson()
                    Assertions.assertThat(content).isEqualTo(expected)
                }
            }

            coVerifyNone { billingAccountablePartyService.add(any()) }
            coVerifyNone { billingAccountablePartyService.findNationalIdEq(any()) }
        }

        @Test
        fun `should create a contract even when it does not have the started at field set`() {
            val request = request.copy(startedAt = "")
            val product = TestModelFactory.buildProduct(id = defaultProductId)
            val contractUrl = "http://localhost:9000/file/${fileContractId}"
            val vaultResponse = buildVaultResponse(contractUrl, fileContractId, "contrato.pdf")

            val expected = CompanyContractResponse(
                id = companyContract.id,
                externalId = companyContract.externalId,
                title = companyContract.title,
                startedAt = companyContract.startedAt,
                accountableEmail = companyContract.accountableEmail,
                contractFileIds = companyContract.contractFileIds,
                defaultProductId = ProductConverter.convert(product),
                availableProducts = listOf(
                    ProductConverter.convert(product),
                ),
                contractFiles = listOf(
                    ContractFileResponse(
                        id = fileContractId,
                        type = ContractType.MAIN,
                        url = contractUrl,
                        filename = "contrato.pdf",
                    )
                ),
                billingAccountablePartyId = null,
                billingAccountableParty = null,
                nature = companyContract.nature,
            )

            coEvery {
                companyContractService.add(match {
                    it.title == request.title && it.accountableEmail == request.accountableEmail
                })
            } returns companyContract
            coEvery {
                productService.findByIds(
                    availableProducts,
                    ProductService.FindOptions(withPriceListing = false),
                )
            } returns listOf(product)
            coEvery { companyService.get(company.id) } returns company
            coEvery {
                companyService.update(
                    match { it.id == company.id && companyContract.id in it.contractIds},
                    true
                )
            } returns company.copy(
                contractIds = listOf(companyContract.id),
            )

            coEvery { fileVaultActionService.securedGenericLinks(any()) } returns listOf(vaultResponse)

            authenticatedAs(token, staffToBeAuthenticated) {
                post("/company/${company.id}/contract", request) { response ->
                    ResponseAssert.assertThat(response).isSuccessfulJson()

                    val content: CompanyContractResponse = response.bodyAsJson()
                    Assertions.assertThat(content).isEqualTo(expected)
                }
            }

            coVerifyNone { billingAccountablePartyService.add(any()) }
            coVerifyNone { billingAccountablePartyService.get(any()) }
            coVerifyNone { billingAccountablePartyService.findNationalIdEq(any()) }
        }

        @Test
        fun `should throw exception when title is empty`() {
            val request = request.copy(title = "")
            val expected = RequiredFieldException("title")

            authenticatedAs(token, staffToBeAuthenticated) {
                post("/company/${company.id}/contract", request) { response ->
                    ResponseAssert.assertThat(response).withErrorCode(expected.code)
                    ResponseAssert.assertThat(response).withFailMessage(expected.message)
                }
            }
        }

        @Test
        fun `should throw exception when startedAt is in invalid format`() {
            val request = request.copy(startedAt = "13/04/2023")
            val expected = InvalidFormatException("startedAt")

            authenticatedAs(token, staffToBeAuthenticated) {
                post("/company/${company.id}/contract", request) { response ->
                    ResponseAssert.assertThat(response).withErrorCode(expected.code)
                    ResponseAssert.assertThat(response).withFailMessage(expected.message)
                }
            }
        }

        @Test
        fun `should throw exception when accountableEmail is empty`() {
            val request = request.copy(accountableEmail = "")
            val expected = RequiredFieldException("email")

            authenticatedAs(token, staffToBeAuthenticated) {
                post("/company/${company.id}/contract", request) { response ->
                    ResponseAssert.assertThat(response).withErrorCode(expected.code)
                    ResponseAssert.assertThat(response).withFailMessage(expected.message)
                }
            }
        }

        @Test
        fun `should throw exception when accountableEmail is in invalid format`() {
            val request = request.copy(accountableEmail = "@errado.com")
            val expected = InvalidFormatException("email")

            authenticatedAs(token, staffToBeAuthenticated) {
                post("/company/${company.id}/contract", request) { response ->
                    ResponseAssert.assertThat(response).withErrorCode(expected.code)
                    ResponseAssert.assertThat(response).withFailMessage(expected.message)
                }
            }
        }

        @Test
        fun `should throw exception when contractFileIds does not contain contract of type main`() {
            val request =
                request.copy(contractFileIds = listOf(ContractFile(id = contractFileId, type = ContractType.AMENDMENT)))
            val expected = RequiredMainContractException()

            authenticatedAs(token, staffToBeAuthenticated) {
                post("/company/${company.id}/contract", request) { response ->
                    ResponseAssert.assertThat(response).withErrorCode(expected.code)
                    ResponseAssert.assertThat(response).withFailMessage(expected.message)
                }
            }
        }

        @Test
        fun `should throw exception when defaultProductId does not contain in availableProducts`() {
            val availableProduct = RangeUUID.generate()
            val availableProducts = listOf(availableProduct)
            val request = request.copy(
                defaultProductId = defaultProductId,
                availableProducts = availableProducts,
            )
            val product = TestModelFactory.buildProduct(id = availableProduct)

            coEvery { productService.findByIds(availableProducts) } returns listOf(product)

            val expected = IllegalArgumentException("Invalid default product id")

            authenticatedAs(token, staffToBeAuthenticated) {
                post("/company/${company.id}/contract", request) { response ->
                    ResponseAssert.assertThat(response).withFailMessage(expected.message)
                }
            }
        }
    }

    @Nested
    inner class UpdateCompanyContract {
        private val fileContractId = RangeUUID.generate()
        private val defaultProductId = RangeUUID.generate()
        private val availableProducts = listOf(defaultProductId)
        private val billingAccountableParty = TestModelFactory.buildBillingAccountableParty()
        val request = CompanyContractRequest(
            title = "contrato novo",
            accountableEmail = "<EMAIL>",
            startedAt = "2023-10-03",
            contractFileIds = listOf(ContractFile(id = fileContractId, type = ContractType.MAIN)),
            defaultProductId = defaultProductId,
            availableProducts = availableProducts,
        )
        private val companyContractId = RangeUUID.generate()
        private val companyContract = TestModelFactory.buildCompanyContract(id = companyContractId)
        private val companyContractUpdated = companyContract.copy(
            title = request.title,
            accountableEmail = request.accountableEmail,
            startedAt = LocalDate.parse(request.startedAt),
            contractFileIds = request.contractFileIds,
            defaultProductId = defaultProductId,
            availableProducts = availableProducts,
        )

        @Test
        fun `should update a companyContract`() {
            val product = TestModelFactory.buildProduct(id = defaultProductId)
            val contractUrl = "http://localhost:9000/file/${fileContractId}"
            val vaultResponse = buildVaultResponse(contractUrl, fileContractId, "contrato.pdf")

            coEvery { companyContractService.get(companyContractId) } returns companyContract
            coEvery { productService.findByIds(availableProducts, ProductService.FindOptions(false)) } returns listOf(
                product
            )
            coEvery {
                companyContractService.update(match {
                    it.title == request.title && it.accountableEmail == request.accountableEmail &&
                            it.contractFileIds == request.contractFileIds && it.startedAt == LocalDate.parse(request.startedAt)
                })
            } returns companyContractUpdated

            val expected = CompanyContractResponse(
                id = companyContractId,
                externalId = companyContractUpdated.externalId,
                title = request.title,
                startedAt = LocalDate.parse(request.startedAt),
                accountableEmail = request.accountableEmail,
                contractFileIds = request.contractFileIds,
                defaultProductId = ProductConverter.convert(product),
                availableProducts = listOf(
                    ProductConverter.convert(product),
                ),
                contractFiles = listOf(
                    ContractFileResponse(
                        id = fileContractId,
                        type = ContractType.MAIN,
                        url = contractUrl,
                        filename = "contrato.pdf",
                    )
                ),
                billingAccountablePartyId = null,
                billingAccountableParty = null,
                nature = companyContract.nature,
            )

            coEvery { fileVaultActionService.securedGenericLinks(any()) } returns listOf(vaultResponse)

            authenticatedAs(token, staffToBeAuthenticated) {
                put("/company/contract/${companyContractId}", request) { response ->
                    ResponseAssert.assertThat(response).isSuccessfulJson()

                    val content: CompanyContractResponse = response.bodyAsJson()
                    Assertions.assertThat(content).isEqualTo(expected)
                }
            }

            coVerifyNone { billingAccountablePartyService.get(any()) }
            coVerifyNone { billingAccountablePartyService.add(any()) }
            coVerifyNone { billingAccountablePartyService.findNationalIdEq(any()) }
        }

        @Test
        fun `should update a companyContract and create the billing accountable party`() {
            val product = TestModelFactory.buildProduct(id = defaultProductId)
            val contractUrl = "http://localhost:9000/file/${fileContractId}"
            val vaultResponse = buildVaultResponse(contractUrl, fileContractId, "contrato.pdf")
            val billingAccountableParty = billingAccountableParty.copy(type = BillingAccountablePartyType.LEGAL_PERSON)
            val request = request.copy(
                billingAccountableParty = BillingAccountablePartyRequest(
                    nationalId = billingAccountableParty.nationalId,
                    type = billingAccountableParty.type,
                    address = billingAccountableParty.address!!,
                    email = billingAccountableParty.email,
                    firstName = billingAccountableParty.firstName,
                    lastName = billingAccountableParty.lastName
                )
            )

            coEvery { companyContractService.get(companyContractId) } returns companyContract
            coEvery { productService.findByIds(availableProducts, ProductService.FindOptions(false)) } returns listOf(
                product
            )
            coEvery {
                companyContractService.update(match {
                    it.title == request.title && it.accountableEmail == request.accountableEmail &&
                            it.contractFileIds == request.contractFileIds && it.startedAt == LocalDate.parse(request.startedAt)
                })
            } returns companyContractUpdated.copy(billingAccountablePartyId = billingAccountableParty.id)

            coEvery {
                billingAccountablePartyService.add(match {
                    it.nationalId == request.billingAccountableParty?.nationalId &&
                            it.type == request?.billingAccountableParty?.type
                })
            } returns billingAccountableParty

            val expected = CompanyContractResponse(
                id = companyContractId,
                externalId = companyContractUpdated.externalId,
                title = request.title,
                startedAt = LocalDate.parse(request.startedAt),
                accountableEmail = request.accountableEmail,
                contractFileIds = request.contractFileIds,
                defaultProductId = ProductConverter.convert(product),
                availableProducts = listOf(
                    ProductConverter.convert(product),
                ),
                contractFiles = listOf(
                    ContractFileResponse(
                        id = fileContractId,
                        type = ContractType.MAIN,
                        url = contractUrl,
                        filename = "contrato.pdf",
                    )
                ),
                billingAccountablePartyId = billingAccountableParty.id,
                billingAccountableParty = billingAccountableParty.convertTo(BillingAccountablePartyResponse::class),
                nature = companyContract.nature,
            )

            coEvery { fileVaultActionService.securedGenericLinks(any()) } returns listOf(vaultResponse)

            authenticatedAs(token, staffToBeAuthenticated) {
                put("/company/contract/${companyContractId}", request) { response ->
                    ResponseAssert.assertThat(response).isSuccessfulJson()

                    val content: CompanyContractResponse = response.bodyAsJson()
                    Assertions.assertThat(content).isEqualTo(expected)
                }
            }

            coVerifyNone { billingAccountablePartyService.get(any()) }
            coVerifyNone { billingAccountablePartyService.findNationalIdEq(any()) }
        }

        @Test
        fun `should update a companyContract and try to create but get already the billing accountable party created`() {
            val product = TestModelFactory.buildProduct(id = defaultProductId)
            val contractUrl = "http://localhost:9000/file/${fileContractId}"
            val vaultResponse = buildVaultResponse(contractUrl, fileContractId, "contrato.pdf")
            val request = request.copy(
                billingAccountableParty = BillingAccountablePartyRequest(
                    nationalId = billingAccountableParty.nationalId,
                    type = BillingAccountablePartyType.LEGAL_PERSON,
                    address = billingAccountableParty.address!!,
                    email = billingAccountableParty.email,
                    firstName = billingAccountableParty.firstName,
                    lastName = billingAccountableParty.lastName
                )
            )

            coEvery { companyContractService.get(companyContractId) } returns companyContract
            coEvery { productService.findByIds(availableProducts, ProductService.FindOptions(false)) } returns listOf(
                product
            )
            coEvery {
                companyContractService.update(match {
                    it.title == request.title && it.accountableEmail == request.accountableEmail &&
                            it.contractFileIds == request.contractFileIds && it.startedAt == LocalDate.parse(request.startedAt)
                })
            } returns companyContractUpdated.copy(billingAccountablePartyId = billingAccountableParty.id)

            coEvery {
                billingAccountablePartyService.add(match { it.nationalId == request.billingAccountableParty?.nationalId })
            } returns DuplicatedItemException("")
            coEvery {
                billingAccountablePartyService.findNationalIdEq(billingAccountableParty.nationalId)
            } returns billingAccountableParty

            val expected = CompanyContractResponse(
                id = companyContractId,
                externalId = companyContractUpdated.externalId,
                title = request.title,
                startedAt = LocalDate.parse(request.startedAt),
                accountableEmail = request.accountableEmail,
                contractFileIds = request.contractFileIds,
                defaultProductId = ProductConverter.convert(product),
                availableProducts = listOf(
                    ProductConverter.convert(product),
                ),
                contractFiles = listOf(
                    ContractFileResponse(
                        id = fileContractId,
                        type = ContractType.MAIN,
                        url = contractUrl,
                        filename = "contrato.pdf",
                    )
                ),
                billingAccountablePartyId = billingAccountableParty.id,
                billingAccountableParty = billingAccountableParty.convertTo(BillingAccountablePartyResponse::class),
                nature = companyContract.nature,
            )

            coEvery { fileVaultActionService.securedGenericLinks(any()) } returns listOf(vaultResponse)

            authenticatedAs(token, staffToBeAuthenticated) {
                put("/company/contract/${companyContractId}", request) { response ->
                    ResponseAssert.assertThat(response).isSuccessfulJson()

                    val content: CompanyContractResponse = response.bodyAsJson()
                    Assertions.assertThat(content).isEqualTo(expected)
                }
            }

            coVerifyNone { billingAccountablePartyService.get(any()) }
        }

        @Test
        fun `should update a companyContract and get the billing accountable party`() {
            val product = TestModelFactory.buildProduct(id = defaultProductId)
            val contractUrl = "http://localhost:9000/file/${fileContractId}"
            val vaultResponse = buildVaultResponse(contractUrl, fileContractId, "contrato.pdf")
            val request = request.copy(
                billingAccountablePartyId = billingAccountableParty.id,
            )

            coEvery { companyContractService.get(companyContractId) } returns companyContract
            coEvery { productService.findByIds(availableProducts, ProductService.FindOptions(false)) } returns listOf(
                product
            )
            coEvery {
                companyContractService.update(match {
                    it.title == request.title && it.accountableEmail == request.accountableEmail &&
                            it.contractFileIds == request.contractFileIds && it.startedAt == LocalDate.parse(request.startedAt)
                })
            } returns companyContractUpdated.copy(billingAccountablePartyId = billingAccountableParty.id)

            coEvery {
                billingAccountablePartyService.get(billingAccountableParty.id)
            } returns billingAccountableParty

            val expected = CompanyContractResponse(
                id = companyContractId,
                externalId = companyContractUpdated.externalId,
                title = request.title,
                startedAt = LocalDate.parse(request.startedAt),
                accountableEmail = request.accountableEmail,
                contractFileIds = request.contractFileIds,
                defaultProductId = ProductConverter.convert(product),
                availableProducts = listOf(
                    ProductConverter.convert(product),
                ),
                contractFiles = listOf(
                    ContractFileResponse(
                        id = fileContractId,
                        type = ContractType.MAIN,
                        url = contractUrl,
                        filename = "contrato.pdf",
                    )
                ),
                billingAccountablePartyId = billingAccountableParty.id,
                billingAccountableParty = billingAccountableParty.convertTo(BillingAccountablePartyResponse::class),
                nature = companyContract.nature,
            )

            coEvery { fileVaultActionService.securedGenericLinks(any()) } returns listOf(vaultResponse)

            authenticatedAs(token, staffToBeAuthenticated) {
                put("/company/contract/${companyContractId}", request) { response ->
                    ResponseAssert.assertThat(response).isSuccessfulJson()

                    val content: CompanyContractResponse = response.bodyAsJson()
                    Assertions.assertThat(content).isEqualTo(expected)
                }
            }

            coVerifyNone { billingAccountablePartyService.add(any()) }
            coVerifyNone { billingAccountablePartyService.findNationalIdEq(any()) }
        }

        @Test
        fun `should throw exception when title is empty`() {
            val request = request.copy(title = "")
            val expected = RequiredFieldException("title")

            authenticatedAs(token, staffToBeAuthenticated) {
                put("/company/contract/${companyContractId}", request) { response ->
                    ResponseAssert.assertThat(response).withErrorCode(expected.code)
                    ResponseAssert.assertThat(response).withFailMessage(expected.message)
                }
            }
        }

        @Test
        fun `should update the contract even when it does not have the started at field set`() {
            val request = request.copy(startedAt = "")
            val product = TestModelFactory.buildProduct(id = defaultProductId)
            val contractUrl = "http://localhost:9000/file/${fileContractId}"
            val vaultResponse = buildVaultResponse(contractUrl, fileContractId, "contrato.pdf")
            val companyContractUpdated = companyContractUpdated.copy(startedAt = null)

            coEvery { companyContractService.get(companyContractId) } returns companyContract
            coEvery { productService.findByIds(availableProducts, ProductService.FindOptions(false)) } returns listOf(
                product
            )
            coEvery {
                companyContractService.update(match {
                    it.title == request.title && it.accountableEmail == request.accountableEmail &&
                            it.contractFileIds == request.contractFileIds
                })
            } returns companyContractUpdated

            val expected = CompanyContractResponse(
                id = companyContractId,
                externalId = companyContractUpdated.externalId,
                title = request.title,
                startedAt = null,
                accountableEmail = request.accountableEmail,
                contractFileIds = request.contractFileIds,
                defaultProductId = ProductConverter.convert(product),
                availableProducts = listOf(
                    ProductConverter.convert(product),
                ),
                contractFiles = listOf(
                    ContractFileResponse(
                        id = fileContractId,
                        type = ContractType.MAIN,
                        url = contractUrl,
                        filename = "contrato.pdf",
                    )
                ),
                billingAccountablePartyId = null,
                billingAccountableParty = null,
                nature = companyContract.nature,
            )

            coEvery { fileVaultActionService.securedGenericLinks(any()) } returns listOf(vaultResponse)

            authenticatedAs(token, staffToBeAuthenticated) {
                put("/company/contract/${companyContractId}", request) { response ->
                    ResponseAssert.assertThat(response).isSuccessfulJson()

                    val content: CompanyContractResponse = response.bodyAsJson()
                    Assertions.assertThat(content).isEqualTo(expected)
                }
            }

            coVerifyNone { billingAccountablePartyService.get(any()) }
            coVerifyNone { billingAccountablePartyService.add(any()) }
            coVerifyNone { billingAccountablePartyService.findNationalIdEq(any()) }
        }

        @Test
        fun `should throw exception when startedAt is in invalid format`() {
            val request = request.copy(startedAt = "13/04/2023")
            val expected = InvalidFormatException("startedAt")

            authenticatedAs(token, staffToBeAuthenticated) {
                put("/company/contract/${companyContractId}", request) { response ->
                    ResponseAssert.assertThat(response).withErrorCode(expected.code)
                    ResponseAssert.assertThat(response).withFailMessage(expected.message)
                }
            }
        }

        @Test
        fun `should throw exception when accountableEmail is empty`() {
            val request = request.copy(accountableEmail = "")
            val expected = RequiredFieldException("email")

            authenticatedAs(token, staffToBeAuthenticated) {
                put("/company/contract/${companyContractId}", request) { response ->
                    ResponseAssert.assertThat(response).withErrorCode(expected.code)
                    ResponseAssert.assertThat(response).withFailMessage(expected.message)
                }
            }
        }

        @Test
        fun `should throw exception when accountableEmail is in invalid format`() {
            val request = request.copy(accountableEmail = "@errado.com")
            val expected = InvalidFormatException("email")

            authenticatedAs(token, staffToBeAuthenticated) {
                put("/company/contract/${companyContractId}", request) { response ->
                    ResponseAssert.assertThat(response).withErrorCode(expected.code)
                    ResponseAssert.assertThat(response).withFailMessage(expected.message)
                }
            }
        }

        @Test
        fun `should throw exception when contractFileIds does not contain contract of type main`() {
            val request =
                request.copy(contractFileIds = listOf(ContractFile(id = fileContractId, type = ContractType.AMENDMENT)))
            val expected = RequiredMainContractException()

            authenticatedAs(token, staffToBeAuthenticated) {
                put("/company/contract/${companyContractId}", request) { response ->
                    ResponseAssert.assertThat(response).withErrorCode(expected.code)
                    ResponseAssert.assertThat(response).withFailMessage(expected.message)
                }
            }
        }
    }
}

fun buildVaultResponse(url: String, id: UUID, fileName: String) =
    VaultResponse(url, id, "PDF", fileName = fileName, vaultUrl = url)
