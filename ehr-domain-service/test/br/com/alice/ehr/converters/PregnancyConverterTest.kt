package br.com.alice.ehr.converters

import br.com.alice.common.RangeUUID
import br.com.alice.common.core.PersonId
import br.com.alice.data.layer.models.*
import org.junit.jupiter.api.Assertions.assertEquals
import java.math.BigDecimal
import java.time.LocalDate
import java.time.LocalDateTime
import kotlin.test.Test

class PregnancyConverterTest {

    private val pregnancy = Pregnancy(
        id = RangeUUID.generate(),
        personId = PersonId(),
        lastPeriodDate = LocalDate.now(),
        firstUsgDate = LocalDate.now(),
        usgEstimatedGestationalAgeInDays = 42,
        deliveryDate = LocalDate.now(),
        deliveryGestationalAge = PregnancyDeliveryGestationalAge.FULL_TERM,
        outcomeDate = LocalDate.now(),
        outcomeType = PregnancyOutcomeType.VAGINAL_DELIVERY,
        outcomePeriod = PregnancyOutcomePeriod.THIRD_QUARTER,
        pregnancyWalletLink = "http://example.com/wallet",
        babies = listOf(
            PregnancyBaby(
                name = "Baby Name",
                weight = BigDecimal("3.5")
            )
        ),
        notes = "Test notes",
        active = true,
        createdAt = LocalDateTime.now(),
        updatedAt = LocalDateTime.now(),
        version = 1
    )

    private val pregnancyModel = PregnancyModel(
        id = pregnancy.id,
        personId = pregnancy.personId,
        lastPeriodDate = pregnancy.lastPeriodDate,
        firstUsgDate = pregnancy.firstUsgDate,
        usgEstimatedGestationalAgeInDays = pregnancy.usgEstimatedGestationalAgeInDays,
        deliveryDate = pregnancy.deliveryDate,
        deliveryGestationalAge = pregnancy.deliveryGestationalAge,
        outcomeDate = pregnancy.outcomeDate,
        outcomeType = pregnancy.outcomeType,
        outcomePeriod = pregnancy.outcomePeriod,
        pregnancyWalletLink = pregnancy.pregnancyWalletLink,
        babies = listOf(
            PregnancyBabyModel(
                name = pregnancy.babies[0].name,
                weight = pregnancy.babies[0].weight
            )
        ),
        notes = pregnancy.notes,
        active = pregnancy.active,
        createdAt = pregnancy.createdAt,
        updatedAt = pregnancy.updatedAt,
        version = pregnancy.version
    )

    @Test
    fun testToModel() {
        assertEquals(pregnancyModel, pregnancy.toModel())
    }

    @Test
    fun testToTransport() {
        assertEquals(pregnancy, pregnancyModel.toTransport())
    }
}
