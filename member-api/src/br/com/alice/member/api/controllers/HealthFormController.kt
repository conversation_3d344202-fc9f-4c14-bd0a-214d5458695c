package br.com.alice.member.api.controllers

import br.com.alice.app.content.client.AppContentScreenDetailService
import br.com.alice.common.Response
import br.com.alice.common.controllers.Controller
import br.com.alice.common.core.extensions.toPersonId
import br.com.alice.common.core.extensions.unaccent
import br.com.alice.common.foldResponse
import br.com.alice.data.layer.models.HealthFormAnswerSource
import br.com.alice.data.layer.models.HealthFormAnswerSourceType
import br.com.alice.member.api.models.BasicNavigationResponse
import br.com.alice.member.api.models.Links
import br.com.alice.member.api.models.MobileRouting.HOME
import br.com.alice.member.api.models.NavigationResponse
import br.com.alice.member.api.models.healthForm.AnswerRequest
import br.com.alice.member.api.services.AppState
import br.com.alice.member.api.services.AppStateNotifier
import br.com.alice.questionnaire.client.FormNavigationService
import br.com.alice.questionnaire.exceptions.InvalidQuestionIndexException
import br.com.alice.questionnaire.models.HealthFormActionsTransport
import br.com.alice.questionnaire.models.HealthFormAnswerTransport
import br.com.alice.questionnaire.models.HealthFormPreviousQuestionTransport
import com.github.kittinunf.result.Result
import com.github.kittinunf.result.failure
import com.github.kittinunf.result.flatMap
import com.github.kittinunf.result.flatMapError
import com.github.kittinunf.result.getOrNull
import com.github.kittinunf.result.success
import io.ktor.http.Parameters
import kotlinx.coroutines.coroutineScope
import java.util.UUID

class HealthFormController(
    private val formNavigationService: FormNavigationService,
    private val appContentScreenDetailService: AppContentScreenDetailService,
) : Controller() {

    suspend fun startForm(formKey: String, params: Parameters): Response {
        val source = getHealthFormAnswerSource(params)

        return buildActionsResponse(source, shouldGenerateAnswerGroup = false) {
            formNavigationService.startForm(
                currentUid().toPersonId(),
                formKey.unaccent(),
                source
            )
        }
    }

    suspend fun saveAnswerAndGetNextQuestion(
        healthFormId: UUID,
        questionId: UUID,
        params: Parameters,
        answer: AnswerRequest
    ): Response {
        val source = getHealthFormAnswerSource(params)

        return buildActionsResponse(source, shouldGenerateAnswerGroup = true) {
            formNavigationService.moveForwardOneQuestion(
                HealthFormAnswerTransport(
                    healthFormId = healthFormId,
                    personId = currentUid().toPersonId(),
                    questionId = questionId,
                    next = answer.next,
                    value = answer.value
                ),
                source
            )
        }
    }

    suspend fun deleteAnswerAndGetPreviousQuestion(
        healthFormId: UUID,
        questionId: UUID,
        params: Parameters,
    ): Response {
        val source = getHealthFormAnswerSource(params)
        val personId = currentUid().toPersonId()
        return buildActionsResponse(source, shouldGenerateAnswerGroup = true) {
            val previousQuestionTransport = HealthFormPreviousQuestionTransport(
                healthFormId = healthFormId,
                personId = personId,
                questionId = questionId,
            )
            formNavigationService.moveBackOneQuestion(
                previousQuestionTransport,
                source
            )
        }
    }

    private fun getHomeNavigation() =
        BasicNavigationResponse(navigation = NavigationResponse(mobileRoute = HOME))

    private suspend fun getHealthFormAnswerSource(params: Parameters): HealthFormAnswerSource? =
        span("getHealthFormAnswerSource") { span ->

            span.setAttribute("params", params.toString())

            span.setAttribute("source_id", params["source_id"] ?: "")
            span.setAttribute("source_type", params["source_type"] ?: "")
            span.setAttribute("source_subtype", params["source_subtype"] ?: "")

            val id = params["source_id"].let {
                if (it.isNullOrEmpty()) null else it
            }
            val type = params["source_type"].let {
                if (it.isNullOrEmpty()) null else HealthFormAnswerSourceType.valueOf(it.uppercase())
            }

            val subtype = params["source_subtype"].let {
                if (it.isNullOrEmpty()) null else it
            }

            if ((id != null && type != null)) HealthFormAnswerSource(id, type, subtype)
            else null
        }

    private suspend fun buildActionsResponse(
        source: HealthFormAnswerSource?,
        shouldGenerateAnswerGroup: Boolean = false,
        f: suspend () -> Result<HealthFormActionsTransport, Throwable>
    ) = coroutineScope {
        val personId = currentUid().toPersonId()
        val appContentScreenDetail =
            source?.let { appContentScreenDetailService.findOneByHealthFormAnswerSource(it).getOrNull() }

        return@coroutineScope f().flatMap { actions ->
            formNavigationService.getBaseQuestionResponse(
                personId = personId,
                question = actions.currentQuestion,
                source = source,
                selectedAnswer = actions.selectedAnswer,
                shouldGenerateAnswerGroup = shouldGenerateAnswerGroup,
                action = Links.HealthForm.getQuestionLink(
                    actions.currentQuestion.healthFormId,
                    actions.currentQuestion.id,
                    source?.id,
                    source?.type,
                    source?.subtype
                ),
                backAction = actions.previousQuestion?.let {
                    Links.HealthForm.getPreviousQuestionLink(
                        actions.previousQuestion!!.healthFormId,
                        actions.previousQuestion!!.id,
                        source?.id,
                        source?.type,
                        source?.subtype
                    )
                },
                appContentScreenDetail = appContentScreenDetail,
            )
        }.flatMapError {
            if (it is InvalidQuestionIndexException) {
                AppStateNotifier.updateAppState(personId, AppState.REMINDERS)
                getHomeNavigation().success()
            } else it.failure()
        }.foldResponse()
    }
}
