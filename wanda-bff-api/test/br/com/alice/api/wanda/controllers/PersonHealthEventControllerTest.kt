package br.com.alice.api.wanda.controllers

import br.com.alice.api.wanda.ControllerTestHelper
import br.com.alice.api.wanda.converters.PersonHealthEventResponseConverter
import br.com.alice.api.wanda.model.LightPersonHealthEventResponse
import br.com.alice.api.wanda.model.NextPersonHealthTasksResponse
import br.com.alice.api.wanda.model.PersonHealthEventCategoriesResponse
import br.com.alice.api.wanda.model.PersonHealthEventRequest
import br.com.alice.api.wanda.model.PersonHealthEventResponse
import br.com.alice.api.wanda.model.PersonHealthEventStatusesResponse
import br.com.alice.authentication.UserType
import br.com.alice.clinicalaccount.client.PersonClinicalAccountService
import br.com.alice.common.RangeUUID
import br.com.alice.common.core.exceptions.NotFoundException
import br.com.alice.common.core.extensions.fromSaoPauloToUTCTimeZone
import br.com.alice.common.core.extensions.toSaoPauloTimeZone
import br.com.alice.common.core.extensions.toUUID
import br.com.alice.common.data.dsl.matchers.ResponseAssert.Companion.assertThat
import br.com.alice.common.helpers.bodyAsJson
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.Person
import br.com.alice.data.layer.models.PersonHealthEvent
import br.com.alice.data.layer.models.PersonHealthEventAggregator
import br.com.alice.data.layer.models.PersonHealthEventCategory
import br.com.alice.data.layer.models.PersonHealthEventCategory.Companion.allCategories
import br.com.alice.data.layer.models.PersonHealthEventPriority
import br.com.alice.data.layer.models.PersonHealthEventUpdatedBy
import br.com.alice.data.layer.models.PersonHealthEventUpdatedByType
import br.com.alice.data.layer.models.ReferencedLink
import br.com.alice.data.layer.models.Staff
import br.com.alice.person.client.PersonFilter
import br.com.alice.person.client.PersonService
import br.com.alice.staff.client.StaffService
import br.com.alice.staff.converters.SimpleStaffResponseConverter
import br.com.alice.wanda.client.PersonHealthEventService
import com.github.kittinunf.result.failure
import com.github.kittinunf.result.success
import io.mockk.called
import io.mockk.clearAllMocks
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.mockk
import org.assertj.core.api.Assertions.assertThat
import java.time.LocalDateTime
import kotlin.test.BeforeTest
import kotlin.test.Test

class PersonHealthEventControllerTest : ControllerTestHelper() {

    private val personClinicalAccountService: PersonClinicalAccountService = mockk()
    private val personHealthEventService: PersonHealthEventService = mockk()
    private val personService: PersonService = mockk()
    private val staffService: StaffService = mockk()
    private val personHealthEventController = PersonHealthEventController(
        personHealthEventService,
        personService,
        staffService,
        personClinicalAccountService,
    )
    private val personModel = TestModelFactory.buildPerson()
    private val personClinicalAccountModel = TestModelFactory.buildPersonClinicalAccount(personId = personModel.id)

    private val personHealthEvent = TestModelFactory.buildPersonHealthEvent(
        staffId = testStaff.id,
        createdByStaffId = testStaff.id,
        dueDate = LocalDateTime.now().plusDays(1),
        referencedLinks = listOf(ReferencedLink(id = "*********", model = "CHANNEL")),
        priority = PersonHealthEventPriority.LOW
    )
    private val request = PersonHealthEventRequest(
        personId = personHealthEvent.personId.toString(),
        staffId = personHealthEvent.staffId.toString(),
        healthcareTeamId = null,
        category = personHealthEvent.category,
        title = personHealthEvent.title,
        description = personHealthEvent.description,
        dueDate = personHealthEvent.dueDate.toString(),
        status = personHealthEvent.status,
        attachments = null,
        referencedLinks = listOf(ReferencedLink(id = "*********", model = "CHANNEL")),
        priority = PersonHealthEventPriority.MEDIUM,
        automaticFollowUp = true,
        automaticFollowUpMessage = "Como você está se sentindo em relação a sua pressão alta?"
    )

    private val simpleStaffResponse = SimpleStaffResponseConverter.convert(staff)

    @BeforeTest
    override fun setup() {
        super.setup()

        clearAllMocks()
        module.single { personHealthEventController }

        coEvery { staffService.get(testStaff.id) } returns staff.success()
    }

    @Test
    fun `#index should get list of PersonHealthEvents`() {
        val personHealthEvents = listOf(
            TestModelFactory.buildPersonHealthEvent(
                personId = personModel.id,
                staffId = testStaff.id,
                dueDate = LocalDateTime.parse("2020-05-29T11:40:00")
            )
        )
        val personHealthEventResponse = PersonHealthEventAggregator(
            events = personHealthEvents,
            count = 1
        )
        val range = IntRange(0, 19)
        val expectedPersons = listOf(personModel.copy(socialName = "Nome Social"))

        coEvery {
            personHealthEventService.findByFilters(
                filter = "",
                sortBy = "",
                sortOrder = "",
                range = range,
                staff = staff
            )
        } returns personHealthEventResponse.success()

        coEvery {
            personService.findAuthorizedBy(PersonFilter(ids = personHealthEvents.map { it.personId.toString() }))
        } returns expectedPersons.success()

        coEvery {
            staffService.findByList(personHealthEvents.map { it.staffId!! })
        } returns listOf(staff).success()

        val personHealthEvent = personHealthEvents.first()

        authenticatedAs(idToken, testStaff) {
            get("/person_health_events") { response ->
                assertThat(response).isSuccessfulJson()

                val content: List<PersonHealthEventResponse> = response.bodyAsJson()
                assertThat(content.size).isEqualTo(1)
                val firstElem = content.first()
                assertThat(firstElem.id).isNotNull
                assertThat(firstElem.version).isNotNull
                assertThat(firstElem.personId).isEqualTo(personHealthEvent.personId.toString())
                assertThat(firstElem.personName).isEqualTo(expectedPersons.first().socialName)
                assertThat(firstElem.staffId).isEqualTo(personHealthEvent.staffId.toString())
                assertThat(firstElem.staffName).isEqualTo(staff.fullName)
                assertThat(firstElem.healthcareTeamId).isEqualTo(personHealthEvent.healthcareTeamId)
                assertThat(firstElem.category).isEqualTo(personHealthEvent.category)
                assertThat(firstElem.title).isEqualTo(personHealthEvent.title)
                assertThat(firstElem.description).isEqualTo(personHealthEvent.description)
                assertThat(firstElem.dueDate).isEqualTo("2020-05-29T08:40")
                assertThat(firstElem.status).isEqualTo(personHealthEvent.status)
                assertThat(firstElem.referencedModelId).isEqualTo(personHealthEvent.referencedModelId)
                assertThat(firstElem.referencedLinks).isEmpty()
                assertThat(firstElem.automaticFollowUp).isFalse
                assertThat(firstElem.automaticFollowUpMessage).isNull()
                assertThat(firstElem.userType).isEqualTo("MEMBER")
                assertThat(firstElem.responsibleStaff).isEqualTo(simpleStaffResponse)
            }
        }
    }

    @Test
    fun `#index should get list of PersonHealthEvents by range and filters`() {
        val personHealthEvents = listOf(
            TestModelFactory.buildPersonHealthEvent(
                personId = personModel.id,
                staffId = testStaff.id,
                dueDate = LocalDateTime.parse("2020-05-29T11:40:00")
            ),
            TestModelFactory.buildPersonHealthEvent(
                personId = personModel.id,
                staffId = testStaff.id,
                dueDate = LocalDateTime.parse("2020-05-29T11:40:00")
            ).copy(
                referencedLinks = listOf(
                    ReferencedLink(
                        id = "referenceId",
                        model = "referenceModel",
                    )
                )
            ),
            TestModelFactory.buildPersonHealthEvent(
                personId = personModel.id,
                staffId = testStaff.id,
                dueDate = LocalDateTime.parse("2020-05-29T11:40:00"),
                category = PersonHealthEventCategory.AA_FOLLOW_UP
            )
        )
        val range = IntRange(0, 50)
        val filter = "{\"category\": \"APPOINTMENT_IMMERSION\"}"
        val expectedResponse = PersonHealthEventAggregator(
            events = personHealthEvents.filter { it.category == PersonHealthEventCategory.APPOINTMENT_IMMERSION },
            count = 2
        )
        val personIds = personHealthEvents.distinctBy { it.personId }.map { it.personId.toString() }
        val staffIds = personHealthEvents.distinctBy { it.staffId }.mapNotNull { it.staffId }

        coEvery {
            personHealthEventService.findByFilters(
                filter = filter,
                sortBy = "",
                sortOrder = "",
                range = range,
                staff = staff
            )
        } returns expectedResponse.success()

        coEvery {
            personService.findAuthorizedBy(PersonFilter(ids = personIds))
        } returns listOf(personModel.copy(userType = UserType.NON_MEMBER)).success()

        coEvery {
            staffService.findByList(staffIds)
        } returns listOf(staff).success()

        val personHealthEvent = expectedResponse.events.first()

        authenticatedAs(idToken, testStaff) {
            get("/person_health_events?filter=$filter&range=[0,50]") { response ->
                assertThat(response).isSuccessfulJson()

                val content: List<PersonHealthEventResponse> = response.bodyAsJson()
                assertThat(content.size).isEqualTo(2)
                val firstElem = content.first()
                assertThat(firstElem.id).isNotNull
                assertThat(firstElem.version).isNotNull
                assertThat(firstElem.personId).isEqualTo(personHealthEvent.personId.toString())
                assertThat(firstElem.personName).isEqualTo(personModel.fullRegisterName)
                assertThat(firstElem.staffId).isEqualTo(personHealthEvent.staffId.toString())
                assertThat(firstElem.staffName).isEqualTo(staff.fullName)
                assertThat(firstElem.healthcareTeamId).isEqualTo(personHealthEvent.healthcareTeamId)
                assertThat(firstElem.category).isEqualTo(personHealthEvent.category)
                assertThat(firstElem.title).isEqualTo(personHealthEvent.title)
                assertThat(firstElem.description).isEqualTo(personHealthEvent.description)
                assertThat(firstElem.dueDate).isEqualTo("2020-05-29T08:40")
                assertThat(firstElem.status).isEqualTo(personHealthEvent.status)
                assertThat(firstElem.referencedModelId).isEqualTo(personHealthEvent.referencedModelId)
                assertThat(content[1].referencedLinks).isEqualTo(personHealthEvents[1].referencedLinks)
                assertThat(firstElem.automaticFollowUp).isFalse
                assertThat(firstElem.automaticFollowUpMessage).isNull()
                assertThat(firstElem.userType).isEqualTo("NON_MEMBER")
                assertThat(firstElem.responsibleStaff).isEqualTo(simpleStaffResponse)
            }
        }
    }

    @Test
    fun `#index should get empty list of PersonHealthEvents`() {
        val range = IntRange(0, 19)

        coEvery {
            personHealthEventService.findByFilters(
                filter = "",
                sortBy = "",
                sortOrder = "",
                range = range,
                staff = staff
            )
        } returns PersonHealthEventAggregator(events = emptyList(), count = 0).success()

        coEvery {
            personService.findAuthorizedBy(PersonFilter())
        } returns emptyList<Person>().success()

        coEvery {
            staffService.findByList(any())
        } returns emptyList<Staff>().success()

        authenticatedAs(idToken, testStaff) {
            get("/person_health_events") { response ->
                assertThat(response).isSuccessfulJson()

                val content: List<PersonHealthEventAggregator> = response.bodyAsJson()
                assertThat(content).isEmpty()
            }
        }
    }

    @Test
    fun `#index should return bad request if exception on search`() {
        coEvery {
            personHealthEventService.findByFilters(any(), any(), any(), any(), any())
        } returns NotFoundException("error_on_search").failure()

        authenticatedAs(idToken, testStaff) {
            get("/person_health_events") { response ->
                assertThat(response).isBadRequest()
            }
        }
    }

    @Test
    fun `#index should get list of PersonHealthEvents with created staff difference of responsible staff`() {
        val createdStaffId = RangeUUID.generate()
        val staffCreated = TestModelFactory.buildStaff(id = createdStaffId)
        val simpleStaffCreated = SimpleStaffResponseConverter.convert(staffCreated)
        val personHealthEvents = listOf(
            TestModelFactory.buildPersonHealthEvent(
                personId = personModel.id,
                staffId = testStaff.id,
                createdByStaffId = createdStaffId,
                dueDate = LocalDateTime.parse("2020-05-29T11:40:00")
            )
        )
        val personHealthEventResponse = PersonHealthEventAggregator(
            events = personHealthEvents,
            count = 1
        )
        val range = IntRange(0, 19)
        val expectedPersons = listOf(personModel.copy(socialName = "Nome Social"))

        coEvery {
            personHealthEventService.findByFilters(
                filter = "",
                sortBy = "",
                sortOrder = "",
                range = range,
                staff = staff
            )
        } returns personHealthEventResponse.success()

        coEvery {
            personService.findAuthorizedBy(PersonFilter(ids = personHealthEvents.map { it.personId.toString() }))
        } returns expectedPersons.success()

        coEvery {
            staffService.findByList(personHealthEvents.flatMap { it.getStaffIds() })
        } returns listOf(staff, staffCreated).success()

        val personHealthEvent = personHealthEvents.first()

        authenticatedAs(idToken, testStaff) {
            get("/person_health_events") { response ->
                assertThat(response).isSuccessfulJson()

                val content: List<PersonHealthEventResponse> = response.bodyAsJson()
                assertThat(content.size).isEqualTo(1)
                val firstElem = content.first()
                assertThat(firstElem.id).isNotNull
                assertThat(firstElem.version).isNotNull
                assertThat(firstElem.personId).isEqualTo(personHealthEvent.personId.toString())
                assertThat(firstElem.personName).isEqualTo(expectedPersons.first().socialName)
                assertThat(firstElem.staffId).isEqualTo(personHealthEvent.staffId.toString())
                assertThat(firstElem.createdByStaff).isEqualTo(simpleStaffCreated)
                assertThat(firstElem.staffName).isEqualTo(staff.fullName)
                assertThat(firstElem.healthcareTeamId).isEqualTo(personHealthEvent.healthcareTeamId)
                assertThat(firstElem.category).isEqualTo(personHealthEvent.category)
                assertThat(firstElem.title).isEqualTo(personHealthEvent.title)
                assertThat(firstElem.description).isEqualTo(personHealthEvent.description)
                assertThat(firstElem.dueDate).isEqualTo("2020-05-29T08:40")
                assertThat(firstElem.status).isEqualTo(personHealthEvent.status)
                assertThat(firstElem.referencedModelId).isEqualTo(personHealthEvent.referencedModelId)
                assertThat(firstElem.referencedLinks).isEmpty()
                assertThat(firstElem.automaticFollowUp).isFalse
                assertThat(firstElem.automaticFollowUpMessage).isNull()
                assertThat(firstElem.userType).isEqualTo("MEMBER")
                assertThat(firstElem.responsibleStaff).isEqualTo(simpleStaffResponse)
            }
        }
    }

    @Test
    fun `#getCategories should return event categories`() {
        val expected = allCategories
            .map { category ->
                PersonHealthEventCategoriesResponse(
                    id = category.name,
                    name = category.description
                )
            }.sortedBy { it.name }

        coEvery { personHealthEventService.getAllowedCategories(staff) } returns allCategories.success()

        authenticatedAs(idToken, testStaff) {
            get("/person_health_events/categories") { response ->
                assertThat(response).isSuccessfulJson()

                val content: List<PersonHealthEventCategoriesResponse> = response.bodyAsJson()
                assertThat(content).isEqualTo(expected)
            }
        }
    }

    @Test
    fun `#getStatuses should return event statuses`() {
        val expected = listOf(
            PersonHealthEventStatusesResponse(id = "NOT_STARTED", name = "Não iniciado"),
            PersonHealthEventStatusesResponse(id = "IN_PROGRESS", name = "Em andamento"),
            PersonHealthEventStatusesResponse(id = "PENDING_RELEASE", name = "Liberação pendente"),
            PersonHealthEventStatusesResponse(id = "PENDING_DATA", name = "Dado pendente"),
            PersonHealthEventStatusesResponse(id = "FINISHED", name = "Concluído"),
            PersonHealthEventStatusesResponse(id = "FINISHED_BY_INACTIVITY", name = "Encerrado por inatividade"),
            PersonHealthEventStatusesResponse(id = "CANCELLED", name = "Cancelado"),
            PersonHealthEventStatusesResponse(id = "CANCELLED_BY_CHURN", name = "Cancelado por membro inativo"),
            PersonHealthEventStatusesResponse(id = "NO_SHOW", name = "No show")
        ).sortedBy { it.name }

        authenticatedAs(idToken, testStaff) {
            get("/person_health_events/statuses") { response ->
                assertThat(response).isSuccessfulJson()

                val content: List<PersonHealthEventStatusesResponse> = response.bodyAsJson()
                assertThat(content).isEqualTo(expected)

                coVerify { personHealthEventService wasNot called }
            }
        }
    }

    @Test
    fun `#getById should return full staff info`() {
        val eventId = personHealthEvent.id
        coEvery {
            personHealthEventService.get(eventId)
        } returns personHealthEvent.success()

        coEvery {
            personService.get(personHealthEvent.personId, false)
        } returns personModel.success()

        val staffIds = listOfNotNull(
            personHealthEvent.staffId,
            personHealthEvent.createdByStaffId,
            personHealthEvent.getLastEditor()?.id
        )

        coEvery {
            staffService.findByList(staffIds)
        } returns listOf(staff).success()

        authenticatedAs(idToken, testStaff) {
            get("/person_health_events/$eventId") { response ->
                assertThat(response).isSuccessfulJson()
                val event: PersonHealthEventResponse = response.bodyAsJson()
                assertThat(event.responsibleStaff).isEqualTo(simpleStaffResponse)
                assertThat(event.createdByStaff).isEqualTo(simpleStaffResponse)
            }
        }
    }

    @Test
    fun `#create should create PersonHealthEvent`() {
        val request = PersonHealthEventRequest(
            personId = personHealthEvent.personId.toString(),
            staffId = personHealthEvent.staffId.toString(),
            healthcareTeamId = null,
            category = personHealthEvent.category,
            title = personHealthEvent.title,
            description = personHealthEvent.description,
            dueDate = personHealthEvent.dueDate.toString(),
            status = personHealthEvent.status,
            attachments = null,
            referencedLinks = listOf(ReferencedLink(id = "*********", model = "CHANNEL")),
            priority = PersonHealthEventPriority.MEDIUM
        )

        coEvery { staffService.get(request.staffId!!.toUUID()) } returns staff.success()

        coEvery { personService.get(personHealthEvent.personId, false) } returns personModel.success()

        coEvery {
            personClinicalAccountService.getByPersonId(personModel.id)
        } returns personClinicalAccountModel.success()

        coEvery {
            personHealthEventService.create(match {
                it.personId == personHealthEvent.personId
                        && it.title == personHealthEvent.title
                        && it.description == personHealthEvent.description
                        && it.priority == PersonHealthEventPriority.MEDIUM
            })
        } returns personHealthEvent.success()


        authenticatedAs(idToken, testStaff) {
            post(to = "/person_health_events", body = request) { response ->
                val content: PersonHealthEventResponse = response.bodyAsJson()
                assertThat(content.personId).isEqualTo(personHealthEvent.personId.toString())
                assertThat(content.staffId).isEqualTo(personHealthEvent.staffId.toString())
                assertThat(content.healthcareTeamId).isEqualTo(personHealthEvent.healthcareTeamId)
                assertThat(content.category).isEqualTo(personHealthEvent.category)
                assertThat(content.title).isEqualTo(personHealthEvent.title)
                assertThat(content.description).isEqualTo(personHealthEvent.description)
                assertThat(content.status).isEqualTo(personHealthEvent.status)
                assertThat(content.referencedModelId).isEqualTo(personHealthEvent.referencedModelId)
                assertThat(content.referencedLinks).isEqualTo(personHealthEvent.referencedLinks)
                assertThat(response).isSuccessfulJson()
            }
        }
    }

    @Test
    fun `#create should create PersonHealthEvent with empty healthcare team`() {
        val personHealthEvent = TestModelFactory.buildPersonHealthEvent(priority = PersonHealthEventPriority.MEDIUM)
        val request = PersonHealthEventResponseConverter.convert(personHealthEvent, personModel)


        coEvery { staffService.get(request.staffId!!.toUUID()) } returns staff.success()
        coEvery { personService.get(personHealthEvent.personId, false) } returns personModel.success()
        coEvery {
            personClinicalAccountService.getByPersonId(personModel.id)
        } returns NotFoundException().failure()

        coEvery {
            personHealthEventService.create(match {
                it.personId == personHealthEvent.personId
                        && it.title == personHealthEvent.title
                        && it.description == personHealthEvent.description
                        && it.priority == PersonHealthEventPriority.MEDIUM
            })
        } returns personHealthEvent.success()


        authenticatedAs(idToken, testStaff) {
            post(to = "/person_health_events", body = request) { response ->
                assertThat(response).isSuccessfulJson()

                val content: PersonHealthEventResponse = response.bodyAsJson()
                assertThat(content.healthcareTeamId).isNull()
                assertThat(content.dueDate).isEqualTo("null")
            }
        }
    }

    @Test
    fun `#create should not create PersonHealthEvent when request is invalid and throw Exception`() {
        val request = PersonHealthEventResponseConverter.convert(personHealthEvent, personModel)

        coEvery { staffService.get(request.staffId!!.toUUID()) } returns staff.success()
        coEvery {
            personHealthEventService.create(match {
                it.personId == personHealthEvent.personId
                        && it.title == personHealthEvent.title
                        && it.description == personHealthEvent.description
            })
        } returns Exception().failure()


        authenticatedAs(idToken, testStaff) {
            post(to = "/person_health_events", body = request) { response ->
                assertThat(response).isInternalServerError()
            }
        }
    }

    @Test
    fun `#update should update PersonHealthEvent`() {
        val toUpdate = personHealthEvent.copy(
            staffId = request.staffId?.toUUID(),
            title = request.title,
            description = request.description,
            category = request.category,
            attachments = request.attachments ?: emptyList(),
            dueDate = request.dueDate?.let { dueDate ->
                if (dueDate.isNotBlank() && dueDate != "null")
                    LocalDateTime.parse(dueDate).fromSaoPauloToUTCTimeZone()
                else null
            },
            status = request.status,
            updatedByStaffIds = listOf(
                PersonHealthEventUpdatedBy(
                    id = testStaff.id,
                    type = PersonHealthEventUpdatedByType.STAFF,
                    date = LocalDateTime.now()
                )
            ),
            version = request.version,
            priority = request.priority,
            channelId = request.getChannelId()
        )

        coEvery { staffService.get(request.staffId!!.toUUID()) } returns staff.success()
        coEvery { personService.get(personHealthEvent.personId, false) } returns personModel.success()

        coEvery {
            personClinicalAccountService.getByPersonId(personModel.id)
        } returns personClinicalAccountModel.success()

        val staffIds = listOfNotNull(
            personHealthEvent.staffId,
            personHealthEvent.createdByStaffId,
            personHealthEvent.getLastEditor()?.id
        )

        coEvery {
            staffService.findByList(staffIds)
        } returns listOf(staff).success()

        coEvery {
            personHealthEventService.get(personHealthEvent.id)
        } returns personHealthEvent.success()

        coEvery {
            personHealthEventService.update(
                match {
                    it.personId == personHealthEvent.personId
                            && it.title == toUpdate.title
                            && it.description == toUpdate.description
                            && it.healthcareAdditionalTeamId == personHealthEvent.healthcareAdditionalTeamId
                }
            )
        } returns toUpdate.success()


        authenticatedAs(idToken, testStaff) {
            put(to = "/person_health_events/${personHealthEvent.id}", body = request) { response ->
                assertThat(response).isSuccessfulJson()

                val content: PersonHealthEventResponse = response.bodyAsJson()
                assertThat(content.personId).isEqualTo(personHealthEvent.personId.toString())
                assertThat(content.staffId).isEqualTo(toUpdate.staffId.toString())
                assertThat(content.healthcareTeamId).isEqualTo(toUpdate.healthcareTeamId)
                assertThat(content.healthcareAdditionalTeamId).isEqualTo(personHealthEvent.healthcareAdditionalTeamId)
                assertThat(content.category).isEqualTo(toUpdate.category)
                assertThat(content.title).isEqualTo(toUpdate.title)
                assertThat(content.description).isEqualTo(toUpdate.description)
                assertThat(content.status).isEqualTo(toUpdate.status)
            }
        }
    }

    @Test
    fun `#update should not update PersonHealthEvent when request is invalid and throw Exception`() {
        val request = PersonHealthEventResponseConverter.convert(personHealthEvent, personModel)

        coEvery { staffService.get(request.staffId!!.toUUID()) } returns staff.success()
        coEvery {
            personClinicalAccountService.getByPersonId(personModel.id)
        } returns personClinicalAccountModel.success()

        coEvery {
            personHealthEventService.get(personHealthEvent.id)
        } returns personHealthEvent.success()

        coEvery {
            personHealthEventService.update(any())
        } returns Exception().failure()


        authenticatedAs(idToken, testStaff) {
            put(to = "/person_health_events/${personHealthEvent.id}", body = request) { response ->
                assertThat(response).isInternalServerError()
            }
        }
    }

    @Test
    fun `#getNextThreeTasksByPerson return empty list if none found`() {
        val expected = NextPersonHealthTasksResponse(
            totalTasksCount = 0,
            tasks = emptyList()
        )
        coEvery { staffService.findByList(any()) } returns emptyList<Staff>().success()

        coEvery {
            personHealthEventService.getNextThreeTasksByPersonId(personHealthEvent.personId)
        } returns emptyList<PersonHealthEvent>().success()

        coEvery {
            personHealthEventService.countNextTasksByPersonId(personHealthEvent.personId)
        } returns 0.success()

        authenticatedAs(idToken, testStaff) {
            get("/person_health_events/${personHealthEvent.personId}/next_tasks") { response ->
                assertThat(response).isOKWithData(expected)
            }
        }
    }

    @Test
    fun `#getNextThreeTasksByPerson return not found`() {
        coEvery { staffService.findByList(any()) } returns emptyList<Staff>().success()

        coEvery {
            personHealthEventService.getNextThreeTasksByPersonId(personHealthEvent.personId)
        } returns NotFoundException().failure()

        authenticatedAs(idToken, testStaff) {
            get("/person_health_events/${personHealthEvent.personId}/next_tasks") { response ->
                assertThat(response).isNotFound()
            }
        }
    }

    @Test
    fun `#getNextThreeTasksByPerson return list with tasks`() {
        val staff2 = TestModelFactory.buildStaff()
        val personHealthEvent = personHealthEvent.copy(staffId = testStaff.id)
        val personHealthEvent2 = personHealthEvent.copy(staffId = staff2.id)
        val tasks = listOf(
            LightPersonHealthEventResponse(
                personId = personHealthEvent.personId,
                staffId = staff.id,
                staffName = staff.fullName,
                staffProfileImageUrl = staff.profileImageUrl,
                title = personHealthEvent.title,
                description = personHealthEvent.description,
                dueDate = personHealthEvent.dueDate?.toSaoPauloTimeZone()?.toString(),
                status = personHealthEvent.status,
                id = personHealthEvent.id
            ),
            LightPersonHealthEventResponse(
                personId = personHealthEvent.personId,
                staffId = staff2.id,
                staffName = staff2.fullName,
                staffProfileImageUrl = staff2.profileImageUrl,
                title = personHealthEvent.title,
                description = personHealthEvent.description,
                dueDate = personHealthEvent.dueDate?.toSaoPauloTimeZone()?.toString(),
                status = personHealthEvent.status,
                id = personHealthEvent.id
            )
        )
        val expected = NextPersonHealthTasksResponse(
            totalTasksCount = 2,
            tasks = tasks
        )

        coEvery { staffService.findByList(listOf(staff.id, staff2.id)) } returns
                listOf(staff, staff2).success()

        coEvery {
            personHealthEventService.getNextThreeTasksByPersonId(personHealthEvent.personId)
        } returns listOf(personHealthEvent, personHealthEvent2).success()
        coEvery {
            personHealthEventService.countNextTasksByPersonId(personHealthEvent.personId)
        } returns 2.success()

        authenticatedAs(idToken, testStaff) {
            get("/person_health_events/${personHealthEvent.personId}/next_tasks") { response ->
                assertThat(response).isSuccessfulJson()
                val content: NextPersonHealthTasksResponse = response.bodyAsJson()

                assertThat(content).isEqualTo(expected)
            }
        }
    }

}
