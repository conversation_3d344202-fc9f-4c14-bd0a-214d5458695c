package br.com.alice.data.layer.services

import br.com.alice.common.RangeUUID
import br.com.alice.common.core.PersonId
import br.com.alice.common.data.dsl.matchers.ResultAssert.Companion.assertThat
import br.com.alice.data.layer.helpers.DataServiceTestHelper
import br.com.alice.data.layer.helpers.TestTableFactory
import br.com.alice.data.layer.models.AttachmentOpmeModel
import br.com.alice.data.layer.models.AttachmentStatus
import br.com.alice.data.layer.models.RequestedOPMEModel
import br.com.alice.data.layer.repositories.JdbiRepository
import br.com.alice.data.layer.tables.TotvsGuiaTable
import kotlinx.coroutines.runBlocking
import java.util.UUID
import kotlin.test.Test
import br.com.alice.data.layer.models.OpmeProcedureModel

class AttachmentOpmeDataServiceTest : DataServiceTestHelper() {
    private val totvsGuiaRepo = JdbiRepository(mainJdbi, TotvsGuiaTable::class)
    private val personTokenService = PersonTokenServiceImpl(tokenJdbi) as PersonTokenService

    private val client = AttachmentOpmeModelDataServiceClient(httpInvoker)

    @Test
    fun `should create and get AttachmentOpme`() = runBlocking {
        val personId = PersonId()
        val personToken = personTokenService.createForPersonId(personId).get()
        val totvsGuia = TestTableFactory.buildTotvsGuiaTable(personId = personToken.personHiToken)
        totvsGuiaRepo.add(totvsGuia)

        val opme1 = buildAttachmentOpme(totvsGuiaId = totvsGuia.id)
        val opme2 = buildAttachmentOpme(totvsGuiaId = totvsGuia.id, status = AttachmentStatus.AUTHORIZED)

        client.add(opme1).get()
        client.add(opme2).get()

        val attachmentOpmeById = client.get(opme1.id)
        assertThat(attachmentOpmeById).isSuccessWithDataIgnoringGivenFields(opme1, "createdAt", "updatedAt")
    }

    @Test
    fun `should return empty`() = runBlocking {
        val result = client.find { where { this.id.eq(RangeUUID.generate()) } }
        assertThat(result).isSuccessWithData(emptyList())
    }
}

fun buildAttachmentOpme(
    id: UUID = RangeUUID.generate(),
    totvsGuiaId: UUID = RangeUUID.generate(),
    status: AttachmentStatus? = AttachmentStatus.PENDING,
    technicalJustification: String = "Uma justificativa técnica bem legal",
    materialSpecification: String = "Uma especificação de material bem legal",
    requestedOpmes: List<RequestedOPMEModel> = listOf(
        RequestedOPMEModel (
            opmeIdentification = OpmeProcedureModel(
                table = "table",
                code = "code",
                description = "description"
            ),
            requestedQuantity = 1,
            requestedValue = 1.0
        )
    ),
    observation: String? = null,
) = AttachmentOpmeModel(
    id = id,
    totvsGuiaId = totvsGuiaId,
    status = status,
    technicalJustification = technicalJustification,
    materialSpecification = materialSpecification,
    requestedOpmes = requestedOpmes,
    observation = observation,
)
