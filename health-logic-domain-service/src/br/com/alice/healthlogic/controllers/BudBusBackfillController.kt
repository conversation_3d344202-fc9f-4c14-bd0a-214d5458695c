package br.com.alice.healthlogic.controllers

import br.com.alice.common.Response
import br.com.alice.common.asyncLayer
import br.com.alice.common.foldResponse
import br.com.alice.common.observability.Spannable
import br.com.alice.common.observability.recordResult
import br.com.alice.common.observability.setAttribute
import br.com.alice.common.withRootServicePolicy
import br.com.alice.common.withUnauthenticatedTokenWithKey
import br.com.alice.data.layer.HEALTH_LOGIC_DOMAIN_ROOT_SERVICE_NAME
import br.com.alice.data.layer.models.Condition
import br.com.alice.data.layer.models.MedicalDischargeRecommendationScriptAction
import br.com.alice.data.layer.models.ScriptAction
import br.com.alice.data.layer.models.ScriptActionType
import br.com.alice.data.layer.models.ServiceScriptNode
import br.com.alice.data.layer.models.ServiceScriptNodeType
import br.com.alice.data.layer.models.ServiceScriptRelationship
import br.com.alice.data.layer.models.ServiceScriptStatus
import br.com.alice.data.layer.services.ServiceScriptNodeDataService
import br.com.alice.data.layer.services.ServiceScriptRelationshipDataService
import com.github.kittinunf.result.flatMap
import io.ktor.http.HttpStatusCode
import java.util.UUID

class BudBusBackfillController(
    private val serviceScriptNodeDataService: ServiceScriptNodeDataService,
    private val serviceScriptRelationshipDataService: ServiceScriptRelationshipDataService
) : Spannable {

    /**
     * Request e.g:
     * {
     *   "bus_node": {
     *     "id": "UUID?",
     *     "name": "String",
     *     "content": "String",
     *     "action_type": "String",
     *     "health_condition_ids": [
     *       "UUID"
     *     ]
     *   },
     *   "positive_node": {
     *     "id": "UUID?",
     *     "name": "String",
     *     "content": "String",
     *     "action_type": "String"
     *   },
     *   "negative_node": {
     *     "id": "UUID?",
     *     "name": "String",
     *     "content": "String",
     *     "action_type": "String"
     *   },
     *   "positive_relationship": {
     *     "id": "UUID?",
     *     "name": "String",
     *     "priority": 0,
     *     "conditions": [
     *       {
     *         "key": "age",
     *         "value": "360..480",
     *         "operator": "RANGE"
     *       },
     *       {
     *         "key": "health_demand_id",
     *         "value": "UUID",
     *         "operator": "EQUALITY"
     *       }
     *     ]
     *   },
     *   "negative_relationship": {
     *     "id": "UUID?",
     *     "name": "String",
     *     "priority": 0,
     *     "conditions": [
     *       {
     *         "key": "age",
     *         "value": "360..480",
     *         "operator": "RANGE"
     *       },
     *       {
     *         "key": "health_demand_id",
     *         "value": "UUID",
     *         "operator": "EQUALITY"
     *       }
     *     ]
     *   }
     * }
     */
    suspend fun createBusProtocol(request: BudBusRequest): Response = withEnvironment {
        val result = mutableMapOf<String, Any?>()

        try {
            val busNodeId = request.busNode.id
                ?: createNode(request.busNode.toNode())!!.also { result["busNodeId"] = it }
            val positiveNodeId = request.positiveNode.id
                ?: createNode(request.positiveNode.toNode(busNodeId))!!.also { result["positiveNodeId"] = it }
            val negativeNodeId = request.negativeNode?.id
                ?: createNode(request.negativeNode?.toNode(busNodeId)).also { result["negativeNodeId"] = it }

            request.positiveRelationship.id ?: createRelationship(
                request.positiveRelationship.toRelationship(
                    busNodeId,
                    positiveNodeId
                )
            ).also { result["positiveRelationshipId"] = it }
            negativeNodeId?.let { negativeNodeIdNonNull ->
                request.negativeRelationship?.id ?: createRelationship(
                    request.negativeRelationship?.toRelationship(
                        busNodeId,
                        negativeNodeIdNonNull
                    )
                ).also { result["negativeRelationshipId"] = it }
            }
        } catch (ex: Exception) {
            result["error"] = ex.stackTraceToString()
        }

        Response(
            HttpStatusCode.OK,
            result
        )
    }

    suspend fun updateNode(request: NodeUpdateRequest) = withEnvironment {
        serviceScriptNodeDataService.get(request.id).flatMap { node ->
            var nodeToUpdate = request.name?.let { node.copy(name = it) } ?: node

            nodeToUpdate = request.internalOrientation?.let { nodeToUpdate.copy(internalOrientation = it) } ?: nodeToUpdate
            nodeToUpdate = request.content?.let { nodeToUpdate.copy(content = it) } ?: nodeToUpdate
            nodeToUpdate = request.type?.let { nodeToUpdate.copy(type = it) } ?: nodeToUpdate
            nodeToUpdate = request.status?.let { nodeToUpdate.copy(status = it) } ?: nodeToUpdate
            nodeToUpdate = request.actions?.let { if (it.isNotEmpty()) nodeToUpdate.copy(actions = it) else nodeToUpdate } ?: nodeToUpdate
            nodeToUpdate = request.serviceScriptActionIds?.let { nodeToUpdate.copy(serviceScriptActionIds = it) } ?: nodeToUpdate
            nodeToUpdate = request.rootNodeId?.let { nodeToUpdate.copy(rootNodeId = it) } ?: nodeToUpdate

            serviceScriptNodeDataService.update(nodeToUpdate)
        }.foldResponse()
    }

    suspend fun updateRelationship(request: RelationshipUpdateRequest) = withEnvironment {
        serviceScriptRelationshipDataService.get(request.id).flatMap { relationship ->
            var relationshipToUpdate = request.name?.let { relationship.copy(name = it) } ?: relationship

            relationshipToUpdate = request.nodeParentId?.let { relationshipToUpdate.copy(nodeParentId = it) } ?: relationshipToUpdate
            relationshipToUpdate = request.nodeChildId?.let { relationshipToUpdate.copy(nodeChildId = it) } ?: relationshipToUpdate
            relationshipToUpdate = request.status?.let { relationshipToUpdate.copy(status = it) } ?: relationshipToUpdate
            relationshipToUpdate = request.conditions?.let { if (it.isNotEmpty()) relationshipToUpdate.copy(conditions = it) else relationshipToUpdate } ?: relationshipToUpdate
            relationshipToUpdate = request.priority?.let { relationshipToUpdate.copy(priority = it) } ?: relationshipToUpdate

            serviceScriptRelationshipDataService.update(relationshipToUpdate)
        }.foldResponse()
    }

    private suspend fun createNode(node: ServiceScriptNode?) =
        span("createNode") { span ->
            if (node == null) return@span null

            span.setAttribute("node_id", node.id)
            span.setAttribute("node_type", node.type)
            serviceScriptNodeDataService.add(node)
                .recordResult(span)
                .get()
                .id
        }

    private suspend fun createRelationship(relationship: ServiceScriptRelationship?) =
        span("createNode") { span ->
            if (relationship == null) return@span null

            span.setAttribute("relationship_type", relationship.id)
            serviceScriptRelationshipDataService.add(relationship)
                .recordResult(span)
                .get()
                .id
        }

    private suspend fun withEnvironment(function: suspend () -> Response) =
        withRootServicePolicy(HEALTH_LOGIC_DOMAIN_ROOT_SERVICE_NAME) {
            withUnauthenticatedTokenWithKey(HEALTH_LOGIC_DOMAIN_ROOT_SERVICE_NAME) {
                asyncLayer {
                    function()
                }
            }
        }
}


data class BudBusRequest(
    val busNode: BusNodeRequest,
    val positiveNode: ActionNodeRequest,
    val negativeNode: ActionNodeRequest? = null,
    val positiveRelationship: RelationshipRequest,
    val negativeRelationship: RelationshipRequest? = null
)

data class BusNodeRequest(
    val id: UUID? = null,
    val name: String,
    val content: String,
    val actionType: ScriptActionType,
    val healthConditionIds: List<UUID>
) {
    fun toNode() = ServiceScriptNode(
        name = name,
        content = name,
        type = ServiceScriptNodeType.BUS,
        status = ServiceScriptStatus.ACTIVE,
        actions = buildActions()
    )

    private fun buildActions() =
        listOf(
            MedicalDischargeRecommendationScriptAction(
                healthConditionIds,
                ScriptAction(type = actionType)
            ).generalize()
        )
}

data class ActionNodeRequest(
    val id: UUID? = null,
    val name: String,
    val content: String,
    val actionType: ScriptActionType? = null
) {
    fun toNode(rootNodeId: UUID) = ServiceScriptNode(
        name = name,
        content = name,
        type = ServiceScriptNodeType.ACTION,
        status = ServiceScriptStatus.ACTIVE,
        rootNodeId = rootNodeId,
        actions = actionType?.let { listOf(ScriptAction(type = actionType)) } ?: emptyList()
    )
}

data class RelationshipRequest(
    val id: UUID? = null,
    val name: String,
    val priority: Int,
    val conditions: List<Condition>
) {
    fun toRelationship(parentId: UUID, childId: UUID) =
        ServiceScriptRelationship(
            name = name,
            nodeParentId = parentId,
            nodeChildId = childId,
            status = ServiceScriptStatus.ACTIVE,
            conditions = conditions,
            priority = priority
        )
}

data class NodeUpdateRequest(
    val id: UUID,
    val name: String? = null,
    val internalOrientation: String? = null,
    val content: String? = null,
    val type: ServiceScriptNodeType? = null,
    val status: ServiceScriptStatus? = null,
    val actions: List<ScriptAction>? = null,
    val serviceScriptActionIds: List<UUID>? = null,
    val rootNodeId: UUID? = null
)

data class RelationshipUpdateRequest(
    val id: UUID,
    val name: String? = null,
    val nodeParentId: UUID? = null,
    val nodeChildId: UUID? = null,
    val status: ServiceScriptStatus? = null,
    val conditions: List<Condition>? = null,
    val priority: Int? = null
)

