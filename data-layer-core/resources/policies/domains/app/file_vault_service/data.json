{"rules": [{"conditions": ["${subject.opaType} == Unauthenticated", "${resource.domain} == ehr"], "branches": [{"conditions": ["${subject.key} == ${resource.personId}", "${resource.namespace} == signed_prescription || ${resource.namespace} == signed_excuse_notes || ${resource.namespace} == signed_referral"], "allow": [{"actions": ["view"], "resources": ["FileVault"]}]}, {"conditions": ["${subject.key} == appointment-domain-service", "${resource.namespace} == signed_excuse_notes"], "allow": [{"actions": ["view", "create"], "resources": ["FileVault"]}]}, {"conditions": ["${subject.key} == health-plan-domain-service", "${resource.namespace} == test_request || ${resource.namespace} == signed_test_request || ${resource.namespace} == signed_prescription"], "allow": [{"actions": ["view", "create"], "resources": ["FileVault"]}]}]}, {"conditions": ["${subject.opaType} == EmailDomain", "${resource.namespace} == member-photo"], "allow": [{"actions": ["view"], "resources": ["FileVault"]}]}, {"conditions": ["${subject.opaType} == StaffModel", "${subject.role} @= HEALTH_PROFESSIONAL || ${subject.role} @= NAVIGATOR"], "allow": [{"actions": ["view", "update", "create"], "resources": ["FileVault"]}]}, {"conditions": ["${subject.opaType} == PersonSubject", "${subject.id} == ${resource.personId} || ${resource.personId} in ${subject.dependentPersons}"], "allow": [{"actions": ["view", "update", "create", "delete"], "resources": ["FileVault"]}]}, {"conditions": ["${subject.opaType} == StaffModel", "${subject.role} @= PRODUCT_TECH"], "branches": [{"conditions": ["${resource.namespace} == portability"], "allow": [{"actions": ["view", "update", "create"], "resources": ["FileVault"]}]}, {"conditions": ["${resource.namespace} == contract", "${resource.domain} == member"], "allow": [{"actions": ["view", "update", "create"], "resources": ["FileVault"]}]}, {"conditions": ["${resource.namespace} == term", "${resource.domain} == member"], "allow": [{"actions": ["view", "update", "create"], "resources": ["FileVault"]}]}]}, {"conditions": ["${subject.opaType} == StaffModel", "${subject.role} @= MEMBER_OPS", "${resource.namespace} == portability"], "allow": [{"actions": ["view", "update", "create"], "resources": ["FileVault"]}]}, {"conditions": ["${subject.opaType} == StaffModel", "${subject.role} @= OPS"], "branches": [{"conditions": ["${resource.namespace} == portability"], "allow": [{"actions": ["view", "update", "create"], "resources": ["FileVault"]}]}, {"conditions": ["${resource.namespace} == documents", "${resource.domain} == member"], "allow": [{"actions": ["view"], "resources": ["FileVault"]}]}, {"conditions": ["${resource.namespace} == contract", "${resource.domain} == member"], "allow": [{"actions": ["view", "update", "create"], "resources": ["FileVault"]}]}]}, {"conditions": ["${subject.opaType} == HealthCommunitySpecialistModel"], "allow": [{"actions": ["view", "update", "create"], "resources": ["FileVault"]}]}, {"conditions": ["${subject.opaType} == StaffSubject", "${subject.role} @= COMMUNITY"], "allow": [{"actions": ["view", "update", "create"], "resources": ["FileVault"]}]}, {"conditions": ["${subject.opaType} == StaffModel", "${subject.role} @= MED_EX || ${subject.role} @= HEALTH_OPS_LEAD"], "branches": [{"conditions": ["${resource.domain} == ehr"], "allow": [{"actions": ["view"], "resources": ["FileVault"]}]}, {"conditions": ["${resource.namespace} == member-photo"], "allow": [{"actions": ["view"], "resources": ["FileVault"]}]}]}, {"conditions": ["${subject.opaType} == StaffModel", "${subject.role} @= OPS || ${subject.role} @= B2B_OPS", "${resource.domain} == member", "${resource.namespace} == contract || ${resource.namespace} == term"], "allow": [{"actions": ["view", "update", "create"], "resources": ["FileVault"]}]}, {"conditions": ["${subject.opaType} == StaffModel", "${subject.role} @= CHIEF_NAVIGATOR || ${subject.role} @= CHIEF_DIGITAL_CARE_NURSE", "${resource.domain} == refund"], "allow": [{"actions": ["view"], "resources": ["FileVault"]}]}, {"conditions": ["${subject.opaType} == StaffModel", "${subject.role} @= RISK_NURSE", "${resource.domain} == member", "${resource.namespace} == term"], "allow": [{"actions": ["view", "update", "create"], "resources": ["FileVault"]}]}, {"conditions": ["${subject.opaType} == CompanyStaff", "${subject.role} == MAIN_COMPANY_STAFF || ${subject.role} == LEGAL_REPRESENTATIVE", "${resource.domain} == business", "${resource.namespace} == dependent_files"], "allow": [{"actions": ["view", "update", "create", "delete"], "resources": ["FileVault"]}]}, {"conditions": ["${subject.opaType} == StaffModel", "${subject.role} @= CHIEF_RISK"], "branches": [{"conditions": ["${resource.domain} == ehr"], "allow": [{"actions": ["view", "update", "create"], "resources": ["FileVault"]}]}, {"conditions": ["${resource.namespace} == portability"], "allow": [{"actions": ["view", "update", "create"], "resources": ["FileVault"]}]}, {"conditions": ["${resource.namespace} == documents", "${resource.domain} == member"], "allow": [{"actions": ["view"], "resources": ["FileVault"]}]}, {"conditions": ["${resource.namespace} == contract", "${resource.domain} == member"], "allow": [{"actions": ["view", "update", "create"], "resources": ["FileVault"]}]}, {"conditions": ["${resource.namespace} == member-photo || ${resource.namespace} == personHealthDocument"], "allow": [{"actions": ["view"], "resources": ["FileVault"]}]}]}]}