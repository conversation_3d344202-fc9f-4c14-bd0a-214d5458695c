{"resourceType": "Bundle", "id": "cf79aa15-466e-46d6-885e-c2e63a21dc71", "meta": {"lastUpdated": "2023-07-31T20:29:41.029+00:00"}, "type": "searchset", "total": 4, "link": [{"relation": "self", "url": "https://interopteste.haoc.com.br:8243/ehrrunner/fhir/Encounter?subject%3APatient.identifier=urn%3Aoid%3A2.16.840.1.113883.13.237%7C17621136782&_count=5&_sort=-date"}], "entry": [{"fullUrl": "https://interopteste.haoc.com.br:8243/ehrrunner/fhir/Encounter/1.42.20130403134532.123.1661367091610.2", "resource": {"resourceType": "Encounter", "id": "1.42.20130403134532.123.1661367091610.2", "meta": {"versionId": "1", "lastUpdated": "2022-08-24T18:51:32.665+00:00", "source": "#9nlXw9iO4azzZlb5", "tag": [{"system": "http://ehrrunner.com/fhir/CodingSystem/EventSummaryType", "code": "SPA"}, {"system": "http://ehrrunner.com/fhir/CodeSystem/EHRDocumentType", "code": "Sumário Pronto Atendimento HAOC v 2.1"}, {"system": "urn:oid:*******.4.1.54443.*******", "code": "60596861095"}, {"system": "http://ehrrunner.com/fhir/NamingSystem/MHDDocumentOID", "code": "urn:oid:1.42.20130403134532.123.1661367091610.2"}, {"system": "urn:oid:*******.4.1.54443.*******", "code": "4034788"}]}, "identifier": [{"system": "urn:ietf:rfc:3986", "value": "urn:oid:1.42.20130403134532.123.1661367091610.2"}], "status": "finished", "subject": {"reference": "Patient/2.16.840.1.113883.13.237-17621136782"}, "participant": [{"type": [{"coding": [{"system": "http://www.saude.gov.br/fhir/r4/CodeSystem/BRResponsabilidadeParticipante-1.0", "code": "alta"}]}, {"coding": [{"system": "http://www.saude.gov.br/fhir/r4/CodeSystem/BRCBO", "code": "223151", "display": "Médico pneumologista"}]}], "individual": {"type": "Practitioner", "identifier": {"system": "urn:oid:2.16.840.1.113883.13.243", "value": "123456", "assigner": {"display": "CRM/SP"}}}}, {"type": [{"coding": [{"system": "http://www.saude.gov.br/fhir/r4/CodeSystem/BRResponsabilidadeParticipante-1.0", "code": "atendimento"}]}, {"coding": [{"system": "http://www.saude.gov.br/fhir/r4/CodeSystem/BRCBO", "code": "223151", "display": "Médico pneumologista"}]}], "individual": {"type": "Practitioner", "identifier": {"system": "urn:oid:2.16.840.1.113883.13.243", "value": "123456", "assigner": {"display": "CRM/SP"}}}}], "period": {"start": "2022-07-23T07:00:00-03:00", "end": "2022-07-23T10:00:00-03:00"}, "reasonReference": [{"reference": "Condition/1241948"}], "diagnosis": [{"condition": {"reference": "Procedure/1241949"}}, {"condition": {"reference": "Procedure/1241950"}}], "serviceProvider": {"reference": "Organization/2.16.840.1.113883.13.36-2076950"}}, "search": {"mode": "match"}}, {"fullUrl": "https://interopteste.haoc.com.br:8243/ehrrunner/fhir/Encounter/1.42.20130403134532.123.1661257049321.2", "resource": {"resourceType": "Encounter", "id": "1.42.20130403134532.123.1661257049321.2", "meta": {"versionId": "1", "lastUpdated": "2022-08-23T15:42:36.127+00:00", "source": "#XKfIBe9FpML4EGJO", "tag": [{"system": "urn:oid:*******.4.1.54443.*******", "code": "00010002000027007"}, {"system": "http://ehrrunner.com/fhir/CodingSystem/EventSummaryType", "code": "SA"}, {"system": "http://ehrrunner.com/fhir/CodeSystem/EHRDocumentType", "code": "Sumário de Alta v1.0"}, {"system": "http://ehrrunner.com/fhir/NamingSystem/MHDDocumentOID", "code": "urn:oid:1.42.20130403134532.123.1661257049321.2"}, {"system": "urn:oid:*******.4.1.54443.*******", "code": "4222202"}]}, "identifier": [{"system": "urn:ietf:rfc:3986", "value": "urn:oid:1.42.20130403134532.123.1661257049321.2"}], "status": "finished", "subject": {"reference": "Patient/2.16.840.1.113883.13.237-17621136782"}, "participant": [{"type": [{"coding": [{"system": "http://www.saude.gov.br/fhir/r4/CodeSystem/BRResponsabilidadeParticipante-1.0", "code": "alta"}]}, {"text": "Médico generalista"}], "individual": {"type": "Practitioner", "identifier": {"system": "urn:oid:2.16.840.1.113883.13.243", "value": "123456", "assigner": {"display": "CRM/SP"}}}}], "period": {"start": "2022-06-18T13:46:00-03:00", "end": "2022-06-25T13:16:00-03:00"}, "diagnosis": [{"condition": {"reference": "Condition/1241786"}}, {"condition": {"reference": "Condition/1241787"}}, {"condition": {"reference": "Condition/1241788"}}, {"condition": {"reference": "Procedure/1241789"}}, {"condition": {"reference": "Procedure/1241790"}}, {"condition": {"reference": "Procedure/1241791"}}, {"condition": {"reference": "Procedure/1241792"}}, {"condition": {"reference": "Procedure/1241793"}}, {"condition": {"reference": "Procedure/1241794"}}, {"condition": {"reference": "Procedure/1241795"}}, {"condition": {"reference": "Procedure/1241796"}}], "serviceProvider": {"reference": "Organization/2.16.840.1.113883.13.36-2078597"}}, "search": {"mode": "match"}}, {"fullUrl": "https://interopteste.haoc.com.br:8243/ehrrunner/fhir/Encounter/*******.4.1.54443.*******.2076950.5.173626.2", "resource": {"resourceType": "Encounter", "id": "*******.4.1.54443.*******.2076950.5.173626.2", "meta": {"versionId": "1", "lastUpdated": "2022-10-28T00:00:12.860+00:00", "source": "#nDM2xkrxQpdLxktX", "tag": [{"system": "urn:oid:*******.4.1.54443.*******", "code": "00010002000027007"}, {"system": "http://ehrrunner.com/fhir/CodingSystem/EventSummaryType", "code": "RAC"}, {"system": "http://ehrrunner.com/fhir/CodeSystem/EHRDocumentType", "code": "Registro de Atendimento Clínico v1.0"}, {"system": "urn:oid:*******.4.1.54443.*******", "code": "4222163"}, {"system": "http://ehrrunner.com/fhir/NamingSystem/MHDDocumentOID", "code": "urn:oid:*******.4.1.54443.*******.2076950.5.173626.2"}]}, "identifier": [{"system": "urn:ietf:rfc:3986", "value": "urn:oid:*******.4.1.54443.*******.2076950.5.173626.2"}], "status": "finished", "subject": {"reference": "Patient/2.16.840.1.113883.13.237-17621136782"}, "participant": [{"type": [{"coding": [{"system": "http://www.saude.gov.br/fhir/r4/CodeSystem/BRResponsabilidadeParticipante-1.0", "code": "atendimento"}]}, {"coding": [{"system": "http://www.saude.gov.br/fhir/r4/CodeSystem/BRCBO", "code": "223110", "display": "Cirurgião vascular"}]}], "individual": {"type": "Practitioner", "identifier": {"system": "urn:oid:2.16.840.1.113883.13.243", "value": "123456", "assigner": {"display": "CRM/SP"}}}}], "period": {"start": "2022-02-01T16:00:00-03:00"}, "diagnosis": [{"condition": {"reference": "Condition/1244162"}}, {"condition": {"reference": "Procedure/1244163"}}], "serviceProvider": {"reference": "Organization/2.16.840.1.113883.13.36-2076950"}}, "search": {"mode": "match"}}, {"fullUrl": "https://interopteste.haoc.com.br:8243/ehrrunner/fhir/Encounter/1.42.20130403134532.123.1661179017918.2", "resource": {"resourceType": "Encounter", "id": "1.42.20130403134532.123.1661179017918.2", "meta": {"versionId": "1", "lastUpdated": "2022-08-22T14:37:00.726+00:00", "source": "#sZkyLBliDPiBSOGF", "tag": [{"system": "urn:oid:*******.4.1.54443.*******", "code": "00010002000027007"}, {"system": "http://ehrrunner.com/fhir/CodingSystem/EventSummaryType", "code": "RAC"}, {"system": "http://ehrrunner.com/fhir/CodeSystem/EHRDocumentType", "code": "Registro de Atendimento Clínico v1.0"}, {"system": "http://ehrrunner.com/fhir/NamingSystem/MHDDocumentOID", "code": "urn:oid:1.42.20130403134532.123.1661179017918.2"}, {"system": "urn:oid:*******.4.1.54443.*******", "code": "4222163"}]}, "identifier": [{"system": "urn:ietf:rfc:3986", "value": "urn:oid:1.42.20130403134532.123.1661179017918.2"}], "status": "finished", "subject": {"reference": "Patient/2.16.840.1.113883.13.237-17621136782"}, "participant": [{"type": [{"coding": [{"system": "http://www.saude.gov.br/fhir/r4/CodeSystem/BRResponsabilidadeParticipante-1.0", "code": "atendimento"}]}, {"coding": [{"system": "http://www.saude.gov.br/fhir/r4/CodeSystem/BRCBO", "code": "225130", "display": "Cirurgião vascular"}]}], "individual": {"type": "Practitioner", "identifier": {"system": "urn:oid:2.16.840.1.113883.13.243", "value": "123456", "assigner": {"display": "CRM/SP"}}}}], "period": {"start": "2022-02-01T16:00:00-03:00"}, "reasonReference": [{"reference": "Condition/1241757"}], "diagnosis": [{"condition": {"reference": "Procedure/1241758"}}], "serviceProvider": {"reference": "Organization/2.16.840.1.113883.13.36-2076950"}}, "search": {"mode": "match"}}]}