package br.com.alice.api.ops.controllers

import br.com.alice.api.ops.ControllerTestHelper
import br.com.alice.common.RangeUUID
import br.com.alice.common.core.exceptions.NotFoundException
import br.com.alice.common.core.extensions.toUUID
import br.com.alice.common.data.dsl.matchers.ResponseAssert
import br.com.alice.common.helpers.bodyAsJson
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.FeatureNamespace
import br.com.alice.data.layer.models.FeatureType
import br.com.alice.featureconfig.client.FeatureConfigService
import com.github.kittinunf.result.failure
import com.github.kittinunf.result.success
import io.ktor.http.HttpHeaders
import io.mockk.clearAllMocks
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.mockk
import org.assertj.core.api.Assertions.assertThat
import kotlin.test.BeforeTest
import kotlin.test.Test

class GasB2cAllowedMembersControllerTest : ControllerTestHelper() {

    private val featureConfigService: FeatureConfigService = mockk()
    private val gasB2cAllowedMembersControllerController = GasB2cAllowedMembersController(featureConfigService)
    private val gasB2CfeatureConfig = TestModelFactory.buildFeatureConfig().copy(
        namespace = FeatureNamespace.ALICE_APP,
        key = GasB2cAllowedMembersController.KEY,
        type = FeatureType.LIST,
        value = "",
        description = "allowed B2C members"
    )

    @BeforeTest
    override fun setup() {
        clearAllMocks()
        super.setup()
        module.single { gasB2cAllowedMembersControllerController }
    }

    @Test
    fun `#index should create GAS B2C FeatureConfig if it does not exist`() {
        authenticatedAs(idToken, staff) {
            coEvery {
                featureConfigService.getByNamespaceAndKey(FeatureNamespace.ALICE_APP, GasB2cAllowedMembersController.KEY)
            } returns NotFoundException().failure()

            coEvery {
                featureConfigService.add(any())
            } returns gasB2CfeatureConfig.success()

            get("/gasB2cAllowedMembers") { response ->
                ResponseAssert.assertThat(response).isSuccessfulJson()

                val gasB2cAllowedMembersResponses: List<GasB2cAllowedMembersResponse> = response.bodyAsJson()
                assertThat(gasB2cAllowedMembersResponses).hasSize(1)

                assertThat(response.headers[HttpHeaders.ContentRange]).isEqualTo("1")

                val gasB2cAllowedMembersResponse = gasB2cAllowedMembersResponses.first()
                assertThat(gasB2cAllowedMembersResponse.value).isEqualTo("")
                assertThat(gasB2cAllowedMembersResponse.active).isTrue()
            }

            coVerify {
                featureConfigService.add(match {
                    it.namespace == FeatureNamespace.ALICE_APP
                            && it.key == GasB2cAllowedMembersController.KEY
                            && it.value == ""
                            && it.type == FeatureType.LIST
                            && it.description == "allowed B2C members"
                })
            }
        }
    }

    @Test
    fun `#index should return existent GAS B2C FeatureConfig if it already exists`() {
        authenticatedAs(idToken, staff) {
            coEvery {
                featureConfigService.getByNamespaceAndKey(FeatureNamespace.ALICE_APP, GasB2cAllowedMembersController.KEY)
            } returns gasB2CfeatureConfig.success()

            get("/gasB2cAllowedMembers") { response ->
                ResponseAssert.assertThat(response).isSuccessfulJson()

                val gasB2cAllowedMembersResponses: List<GasB2cAllowedMembersResponse> = response.bodyAsJson()
                assertThat(gasB2cAllowedMembersResponses).hasSize(1)

                assertThat(response.headers[HttpHeaders.ContentRange]).isEqualTo("1")

                val gasB2cAllowedMembersResponse = gasB2cAllowedMembersResponses.first()
                assertThat(gasB2cAllowedMembersResponse.value).isEqualTo("")
                assertThat(gasB2cAllowedMembersResponse.active).isTrue()
            }
        }
    }

    @Test
    fun `#getById should return FeatureConfig`() {
        authenticatedAs(idToken, staff) {
            coEvery {
                featureConfigService.get(gasB2CfeatureConfig.id.toString())
            } returns gasB2CfeatureConfig.success()

            get("/gasB2cAllowedMembers/${gasB2CfeatureConfig.id}") { response ->
                ResponseAssert.assertThat(response).isSuccessfulJson()

                val gasB2cAllowedMembersResponse: GasB2cAllowedMembersResponse = response.bodyAsJson()
                assertThat(gasB2cAllowedMembersResponse.id.toUUID()).isEqualTo(gasB2CfeatureConfig.id)
                assertThat(gasB2cAllowedMembersResponse.value).isEqualTo("")
                assertThat(gasB2cAllowedMembersResponse.active).isTrue()            }
        }
    }

    @Test
    fun `#update should update GAS B2C FeatureConfig`() {
        val request = UpdateGasB2cAllowedMembersRequest(
            value = "${RangeUUID.generate()},${RangeUUID.generate()}",
            active = false,
        )

        coEvery { featureConfigService.get(gasB2CfeatureConfig.id.toString()) } returns gasB2CfeatureConfig.success()
        coEvery { featureConfigService.update(match {
            it.value == request.value &&
                    it.active == request.active
        }) } returns gasB2CfeatureConfig.success()

        authenticatedAs(idToken, staff) {
            put(to = "/gasB2cAllowedMembers/${gasB2CfeatureConfig.id}", body = request) { response ->
                ResponseAssert.assertThat(response).isSuccessfulJson()

                val gasB2cAllowedMembersResponse: GasB2cAllowedMembersResponse = response.bodyAsJson()
                assertThat(gasB2cAllowedMembersResponse.id.toUUID()).isEqualTo(gasB2CfeatureConfig.id)
                assertThat(gasB2cAllowedMembersResponse.value).isEqualTo("")
                assertThat(gasB2cAllowedMembersResponse.active).isTrue()
            }
        }
    }

}
