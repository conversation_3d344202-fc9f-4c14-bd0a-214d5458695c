package br.com.alice.bud.services.internal

import br.com.alice.common.RangeUUID
import br.com.alice.common.data.dsl.matchers.ResultAssert.Companion.assertThat
import br.com.alice.common.data.dsl.matchers.queryEq
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.common.service.data.dsl.and
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.ServiceScriptNavigationSource
import br.com.alice.data.layer.models.ServiceScriptNavigationSourceType.SCREENING_NAVIGATION
import br.com.alice.data.layer.services.ServiceScriptNavigationGroupDataService
import com.github.kittinunf.result.success
import io.mockk.coEvery
import io.mockk.mockk
import kotlinx.coroutines.runBlocking
import kotlin.test.Test

class ServiceScriptNavigationGroupServiceTest {

    private val serviceScriptNavigationGroupDataService: ServiceScriptNavigationGroupDataService = mockk()

    private val serviceScriptNavigationGroupService = ServiceScriptNavigationGroupService(serviceScriptNavigationGroupDataService)

    private val navigationGroup = TestModelFactory.buildServiceScriptNavigationGroup(
        source = ServiceScriptNavigationSource(
            SCREENING_NAVIGATION,
            RangeUUID.generate().toString()
        )
    )

    @Test
    fun `#create - should create a navigation group`() = runBlocking {
        coEvery { serviceScriptNavigationGroupDataService.add(navigationGroup) } returns navigationGroup.success()

        val result = serviceScriptNavigationGroupService.create(navigationGroup)

        assertThat(result).isSuccessWithData(navigationGroup)

        coVerifyOnce { serviceScriptNavigationGroupDataService.add(any()) }

    }

    @Test
    fun `#update - should update a navigation group`() = runBlocking {
        coEvery { serviceScriptNavigationGroupDataService.update(navigationGroup) } returns navigationGroup.success()

        val result = serviceScriptNavigationGroupService.update(navigationGroup)

        assertThat(result).isSuccessWithData(navigationGroup)

        coVerifyOnce { serviceScriptNavigationGroupDataService.update(any()) }

    }

    @Test
    fun `#get - should get a navigation group by id`() = runBlocking {
        coEvery { serviceScriptNavigationGroupDataService.get(navigationGroup.id) } returns navigationGroup.success()

        val result = serviceScriptNavigationGroupService.get(navigationGroup.id)

        assertThat(result).isSuccessWithData(navigationGroup)

        coVerifyOnce { serviceScriptNavigationGroupDataService.get(any()) }

    }

    @Test
    fun `#getBySourceAndPerson - should get a navigation group active by source and person`() = runBlocking {
        coEvery {
            serviceScriptNavigationGroupDataService.findOne(
                queryEq {
                    where { this.personId.eq(navigationGroup.personId) and this.source.eq(navigationGroup.source) and finishedAt.isNull() }
                }
            )
        } returns navigationGroup.success()

        val result = serviceScriptNavigationGroupService.getBySourceAndPerson(navigationGroup.personId, navigationGroup.source!!, true)

        assertThat(result).isSuccessWithData(navigationGroup)

        coVerifyOnce { serviceScriptNavigationGroupDataService.findOne(any()) }

    }

    @Test
    fun `#getBySourceAndPerson - should get a navigation group inactive by source and person`() = runBlocking {
        coEvery {
            serviceScriptNavigationGroupDataService.findOne(
                queryEq {
                    where { this.personId.eq(navigationGroup.personId) and this.source.eq(navigationGroup.source) and finishedAt.isNotNull() }
                }
            )
        } returns navigationGroup.success()

        val result = serviceScriptNavigationGroupService.getBySourceAndPerson(navigationGroup.personId, navigationGroup.source!!, false)

        assertThat(result).isSuccessWithData(navigationGroup)

        coVerifyOnce { serviceScriptNavigationGroupDataService.findOne(any()) }

    }

    @Test
    fun `#getBySourceAndPerson - should get a navigation group by source and person`() = runBlocking {
        coEvery {
            serviceScriptNavigationGroupDataService.findOne(
                queryEq {
                    where { this.personId.eq(navigationGroup.personId) and this.source.eq(navigationGroup.source) }
                }
            )
        } returns navigationGroup.success()

        val result = serviceScriptNavigationGroupService.getBySourceAndPerson(navigationGroup.personId, navigationGroup.source!!, null)

        assertThat(result).isSuccessWithData(navigationGroup)

        coVerifyOnce { serviceScriptNavigationGroupDataService.findOne(any()) }

    }

    @Test
    fun `#getBySource - should get a navigation group by source`() = runBlocking {
        coEvery {
            serviceScriptNavigationGroupDataService.findOne(
                queryEq {
                    where { this.source.eq(navigationGroup.source) }
                        .orderBy { startedAt }
                        .sortOrder { desc }
                }
            )
        } returns navigationGroup.success()

        val result = serviceScriptNavigationGroupService.getBySource(navigationGroup.source!!)

        assertThat(result).isSuccessWithData(navigationGroup)

        coVerifyOnce { serviceScriptNavigationGroupDataService.findOne(any()) }

    }

}
