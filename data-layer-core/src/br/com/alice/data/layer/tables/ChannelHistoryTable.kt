package br.com.alice.data.layer.tables

import br.com.alice.common.RangeUUID
import br.com.alice.data.layer.models.ChannelCategory
import br.com.alice.data.layer.models.ChannelChangeAction
import br.com.alice.data.layer.models.ChannelHistoryExtraInfo
import br.com.alice.data.layer.models.ChannelKind
import br.com.alice.data.layer.models.ChannelStatus
import br.com.alice.data.layer.models.ChannelSubCategory
import br.com.alice.data.layer.models.ChannelSubCategoryClassifier
import br.com.alice.data.layer.models.ChannelType
import br.com.alice.data.layer.models.ScreeningStatus
import br.com.alice.data.layer.services.PersonNonPiiToken
import java.time.LocalDateTime
import java.util.UUID

internal data class ChannelHistoryTable(
    override val personId: PersonNonPiiToken,
    val channelId: String,
    val name: String? = null,
    val type: ChannelType? = null,
    val status: ChannelStatus,
    val staffIds: List<UUID> = emptyList(),
    val tags: List<String> = emptyList(),
    val origin: String? = null,
    val mergedWith: String? = null,
    val action: ChannelChangeAction,
    val lastSync: LocalDateTime? = null,
    val kind: ChannelKind? = null,
    val category: ChannelCategory? = null,
    val subCategory: ChannelSubCategory? = null,
    val subCategoryClassifier: ChannelSubCategoryClassifier? = null,
    val removed: String? = null,
    val screeningStatus: ScreeningStatus? = null,
    val screeningFinishedAt: LocalDateTime? = null,
    val extraInfos: List<ChannelHistoryExtraInfo>? = emptyList(),
    val eventDate: LocalDateTime? = null,
    val appVersion: String? = null,
    override val id: UUID = RangeUUID.generate(),
    override val version: Int = 0,
    override val createdAt: LocalDateTime = LocalDateTime.now(),
    override val updatedAt: LocalDateTime = LocalDateTime.now()
) : Table<ChannelHistoryTable>, PersonNonPiiReference, DeIdentifiedReference {

    override fun copyTable(version: Int, updatedAt: LocalDateTime, createdAt: LocalDateTime) =
        copy(version = version, createdAt = createdAt, updatedAt = updatedAt)
}
