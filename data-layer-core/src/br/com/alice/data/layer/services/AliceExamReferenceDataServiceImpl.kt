package br.com.alice.data.layer.services

import br.com.alice.data.layer.models.AliceExamReference
import br.com.alice.data.layer.pipelines.DatabasePipelineFactory
import br.com.alice.data.layer.tables.AliceExamReferenceTable

class AliceExamReferenceDataServiceImpl internal constructor(factory: DatabasePipelineFactory) :
        BaseDataServiceImpl<AliceExamReference>(
            factory.get<AliceExamReference, AliceExamReferenceTable>()
        ),
        AliceExamReferenceDataService
