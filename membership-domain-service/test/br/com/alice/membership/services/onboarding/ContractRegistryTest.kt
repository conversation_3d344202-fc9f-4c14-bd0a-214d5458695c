package br.com.alice.membership.services.onboarding

import br.com.alice.business.client.MemberContractService
import br.com.alice.common.RangeUUID
import br.com.alice.common.core.exceptions.NotFoundException
import br.com.alice.common.data.dsl.matchers.ResultAssert.Companion.assertThat
import br.com.alice.common.kafka.internals.LocalProducer
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.ContractPart
import br.com.alice.data.layer.models.ContractSignature
import br.com.alice.data.layer.models.MemberProduct
import br.com.alice.data.layer.models.OnboardingContract
import br.com.alice.data.layer.models.OnboardingPhase
import br.com.alice.data.layer.models.ProductContract
import br.com.alice.data.layer.services.OnboardingContractModelDataService
import br.com.alice.membership.client.ContractAlreadySentException
import br.com.alice.membership.client.ContractNotSignedException
import br.com.alice.membership.client.onboarding.OnboardingService
import br.com.alice.membership.converters.toModel
import br.com.alice.membership.model.Signature
import br.com.alice.membership.model.events.ContractPhaseStartedEvent
import br.com.alice.product.client.ProductService
import br.com.alice.product.model.ProductWithProviders
import com.github.kittinunf.result.Result
import com.github.kittinunf.result.success
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.mockk
import kotlinx.coroutines.runBlocking
import org.assertj.core.api.Assertions
import java.time.LocalDateTime
import java.time.format.DateTimeFormatter
import kotlin.test.BeforeTest
import kotlin.test.Test

class ContractRegistryTest {

    private val person = TestModelFactory.buildPerson()

    private val productWithProviders = ProductWithProviders(
        product = TestModelFactory.buildProduct(),
        providers = TestModelFactory.buildProviders(),
    )

    private val productOrder = TestModelFactory.buildProductOrder(person.id, productWithProviders.product)

    private val data: OnboardingContractModelDataService = mockk()
    private val onboardingService: OnboardingService = mockk()
    private val productService: ProductService = mockk()
    private val memberContractService: MemberContractService = mockk()

    private val contractRegistry = ContractRegistryImpl(
        data,
        onboardingService,
        productService,
        memberContractService,
        LocalProducer
    )

    private val healthDeclarationSignature = Signature(
        idToken = "someIdToken",
        ipAddress = "293.232.12.34",
        userAgent = "Android/0.0.24-2677 (j4primelte/28)",
        contractPart = ContractPart.HEALTH_DECLARATION,
        person = person
    )

    private val randomSignature = ContractSignature(
        id = RangeUUID.generate(),
        idToken = healthDeclarationSignature.idToken,
        fullName = person.fullRegisterName,
        nationalId = person.nationalId,
        ipAddress = healthDeclarationSignature.ipAddress,
        productContract = ProductContract(
            name = productWithProviders.product.title
        ),
        signedAt = LocalDateTime.now().format(DateTimeFormatter.ISO_DATE_TIME)
    )

    private val fakeContract = OnboardingContract(
        personId = person.id,
        documentUrl = "https://somedocument.pdf",
        contractSignature = null,
        healthDeclarationSignature = null,
        ansOrientationLetterSignature = null,
        signatureSentAt = null,
        selectedProduct = productOrder.selectedProduct
    )

    @BeforeTest
    fun setup() {
        LocalProducer.clearMessages()

        coEvery { onboardingService.changePhaseTo(person.id, OnboardingPhase.CONTRACT) } returns Result.success(
            TestModelFactory.buildPersonOnboarding(person.id).copy(currentPhase = OnboardingPhase.CONTRACT)
        )
    }

    @Test
    fun `#sign should not sign a contract part when it's already sent to OPS team`() = runBlocking {
        val onboardingContract = OnboardingContract(
            personId = person.id,
            documentUrl = "someUrl",
            ansOrientationLetterSignature = randomSignature,
            healthDeclarationSignature = randomSignature,
            contractSignature = randomSignature,
            signatureSentAt = LocalDateTime.now(),
            selectedProduct = productOrder.selectedProduct
        )

        val result = contractRegistry.sign(onboardingContract, healthDeclarationSignature)
        assertThat(result).isFailureOfType(ContractAlreadySentException::class)
    }

    @Test
    fun `#sign should override a contract part signature when it already exists`() = runBlocking {
        val product = TestModelFactory.buildProduct()

        val onboardingContract = OnboardingContract(
            personId = person.id,
            documentUrl = "someUrl",
            healthDeclarationSignature = randomSignature,
            selectedProduct = MemberProduct(product.id, product.prices, product.type)
        )

        val newSignature = Signature(
            idToken = "anotherIdToken",
            ipAddress = "293.232.12.34",
            userAgent = "Android/0.0.24-2677 (j4primelte/28)",
            contractPart = ContractPart.HEALTH_DECLARATION,
            person = person
        )

        coEvery { productService.getProduct(onboardingContract.productId) } returns product.success()

        val expected = onboardingContract.copy(
            ansOrientationLetterSignature = randomSignature.copy(
                idToken = newSignature.idToken
            )
        )

        coEvery { data.update(match { it.healthDeclarationSignature?.idToken == newSignature.idToken }) } returns
                Result.success(expected.toModel())

        val result = contractRegistry.sign(onboardingContract, newSignature)
        assertThat(result).isSuccessWithData(expected)
    }

    @Test
    fun `#store should store a new document when a person doesn't have one and not publish event`() = runBlocking<Unit> {
        val contractDocumentUrl = "https://s3.aws/contract/${person.id}.pdf"
        val contract = OnboardingContract(
            personId = person.id,
            documentUrl = contractDocumentUrl,
            selectedProduct = productOrder.selectedProduct
        )

        coEvery { data.findByPersonId(person.id) } returns null

        coEvery { data.add(match {
            it.documentUrl == contractDocumentUrl &&
                    it.sentAt != null
        }) } returns Result.success(contract.toModel())

        val contractResult = contractRegistry.store(person, productOrder, contractDocumentUrl)
        assertThat(contractResult).isSuccess()

        coVerify(exactly = 0) { onboardingService.changePhaseTo(any(), any()) }
        coVerify(exactly = 0) { data.update(any()) }
    }

    @Test
    fun `#start should change onboarding phase to CONTRACT and produce message when phase is before CONTRACT`() = runBlocking<Unit> {
        val personOnboarding = TestModelFactory.buildPersonOnboarding(currentPhase = OnboardingPhase.HEALTH_DECLARATION_APPOINTMENT)
        coEvery { onboardingService.findByPerson(person.id) } returns personOnboarding.success()
        coEvery { onboardingService.changePhaseTo(person.id, OnboardingPhase.CONTRACT) } returns personOnboarding.success()

        val contractResult = contractRegistry.start(person.id)
        assertThat(contractResult).isSuccess()

        coVerify(exactly = 1) { onboardingService.changePhaseTo(person.id, OnboardingPhase.CONTRACT) }
        Assertions.assertThat(LocalProducer.hasEvent(ContractPhaseStartedEvent.name)).isTrue()
    }

    @Test
    fun `#start should change onboarding phase to CONTRACT and produce message when phase is already in CONTRACT`() = runBlocking<Unit> {
        val personOnboarding = TestModelFactory.buildPersonOnboarding(currentPhase = OnboardingPhase.CONTRACT)
        coEvery { onboardingService.findByPerson(person.id) } returns personOnboarding.success()
        coEvery { onboardingService.changePhaseTo(person.id, OnboardingPhase.CONTRACT) } returns personOnboarding.success()

        val contractResult = contractRegistry.start(person.id)
        assertThat(contractResult).isSuccess()

        coVerify(exactly = 1) { onboardingService.changePhaseTo(person.id, OnboardingPhase.CONTRACT) }
        Assertions.assertThat(LocalProducer.hasEvent(ContractPhaseStartedEvent.name)).isTrue()
    }

    @Test
    fun `#start should NOT change onboarding phase to CONTRACT nor produce message when phase is greater than CONTRACT`() = runBlocking<Unit> {
        val personOnboarding = TestModelFactory.buildPersonOnboarding(currentPhase = OnboardingPhase.PAYMENT)
        coEvery { onboardingService.findByPerson(person.id) } returns personOnboarding.success()

        val contractResult = contractRegistry.start(person.id)
        assertThat(contractResult).isSuccess()

        coVerify(exactly = 0) { onboardingService.changePhaseTo(person.id, OnboardingPhase.CONTRACT) }
        Assertions.assertThat(LocalProducer.hasEvent(ContractPhaseStartedEvent.name)).isFalse()
    }

    @Test
    fun `#updateDocument should update contract with new document urls`() = runBlocking<Unit> {
        val contract = TestModelFactory.buildOnboardingContract()
        coEvery { data.findByPersonId(person.id) } returns contract.toModel()

        val documentUrl = "https://s3.alice.com.br/new_document.pdf"

        val updatedContract = contract.copy(documentUrl = documentUrl, certificateUrl = documentUrl)
        coEvery { data.update(updatedContract.toModel()) } returns updatedContract.toModel().success()

        val result = contractRegistry.updateDocument(person.id, documentUrl)
        assertThat(result).isSuccessWithData(updatedContract)

        coVerify(exactly = 1) { data.update(updatedContract.toModel()) }
    }

    @Test
    fun `#updateDocument should return NotFoundException and not update, when contract is not found`() = runBlocking<Unit> {
        coEvery { data.findByPersonId(person.id) } returns null

        val documentUrl = "https://s3.alice.com.br/new_document.pdf"

        val result = contractRegistry.updateDocument(person.id, documentUrl)
        assertThat(result).isFailureOfType(NotFoundException::class)

        coVerify(exactly = 0) { data.update(any()) }
    }

    @Test
    fun `#store should update document url when a person already has a document and not publish event`() = runBlocking<Unit> {
        val contractDocumentUrl = "https://s3.aws/contract/${person.id}.pdf"

        val existingDocument = OnboardingContract(
            personId = person.id,
            documentUrl = "https://somedocument.pdf",
            selectedProduct = productOrder.selectedProduct
        )

        coEvery { data.findByPersonId(person.id) } returns existingDocument.toModel()

        val expectedResponse = existingDocument.copy(documentUrl = contractDocumentUrl)
        coEvery { data.update(match { it.documentUrl == contractDocumentUrl }) } returns expectedResponse.toModel().success()

        val contractResult = contractRegistry.store(person, productOrder, contractDocumentUrl)
        assertThat(contractResult).isSuccessWithData(expectedResponse)

        coVerify(exactly = 0) { data.add(any()) }
        coVerify(exactly = 0) { onboardingService.changePhaseTo(any(), any()) }
    }

    @Test
    fun `#finish should return success when a signature was already sent`() = runBlocking<Unit> {
        val contract = OnboardingContract(
            personId = person.id,
            documentUrl = "https://somedocument.pdf",
            signatureSentAt = LocalDateTime.now(),
            selectedProduct = productOrder.selectedProduct
        )

        val signatureResult = contractRegistry.finish(contract)
        assertThat(signatureResult).isSuccessWithData(contract)
    }

    @Test
    fun `#finish should return failure when a contract was not signed`() = runBlocking<Unit> {
        val contract = OnboardingContract(
            personId = person.id,
            documentUrl = "https://somedocument.pdf",
            contractSignature = null,
            healthDeclarationSignature = null,
            ansOrientationLetterSignature = null,
            signatureSentAt = null,
            selectedProduct = productOrder.selectedProduct
        )

        val signatureResult = contractRegistry.finish(contract)
        assertThat(signatureResult).isFailureOfType(ContractNotSignedException::class)
    }

    @Test
    fun `#finish should update signature send date and move onboarding to payment`() = runBlocking<Unit> {
        val contract = OnboardingContract(
            personId = person.id,
            documentUrl = "https://somedocument.pdf",
            contractSignature = randomSignature,
            healthDeclarationSignature = randomSignature,
            ansOrientationLetterSignature = randomSignature,
            signatureSentAt = null,
            selectedProduct = productOrder.selectedProduct
        )

        coEvery { data.update(match { it.alreadySent }) } returns contract.copy(
            signatureSentAt = null
        ).toModel().success()

        val paymentPhaseOnboarding = TestModelFactory.buildPersonOnboarding().copy(
            currentPhase = OnboardingPhase.PAYMENT
        )

        coEvery { onboardingService.changePhaseTo(
            personId = contract.personId,
            newPhase = OnboardingPhase.CONTRACT.nextPhase()
        ) } returns Result.success(paymentPhaseOnboarding)

        val signatureResult = contractRegistry.finish(contract)
        assertThat(signatureResult).isSuccess()

        coVerify(exactly = 1) { data.update(match { it.alreadySent }) }
    }

    @Test
    fun `#findContract should return success when a contract was found`() = runBlocking {
        coEvery { data.findByPersonId(person.id) } returns fakeContract.toModel()
        val result = contractRegistry.findContract(person.id)

        assertThat(result).isSuccessWithData(fakeContract)
    }

    @Test
    fun `#findContract should return not found when a contract was not found`() = runBlocking {
        coEvery { data.findByPersonId(person.id) } returns null
        val result = contractRegistry.findContract(person.id)

        assertThat(result).isFailureOfType(NotFoundException::class)
    }

    @Test
    fun `#findLatestContract should return success when a contract was found`() = runBlocking {
        val onboardingContract = TestModelFactory.buildOnboardingContract()

        coEvery { data.findLatestByPersonId(person.id) } returns onboardingContract.toModel().success()

        val result = contractRegistry.findLatestContract(person.id)

        assertThat(result).isSuccessWithData(onboardingContract)
    }

}
