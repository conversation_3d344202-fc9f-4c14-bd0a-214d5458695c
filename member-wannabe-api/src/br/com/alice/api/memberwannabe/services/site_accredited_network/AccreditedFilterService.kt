package br.com.alice.api.memberwannabe.services.site_accredited_network

import br.com.alice.atlas.model.AccreditedCategory
import br.com.alice.atlas.services.AccreditedService
import br.com.alice.atlas.services.SiteAccreditedNetworkService
import br.com.alice.common.core.exceptions.BadRequestException
import br.com.alice.common.core.extensions.toUUID
import br.com.alice.common.extensions.resultOf
import br.com.alice.data.layer.models.FeatureNamespace
import br.com.alice.featureconfig.core.FeatureService
import com.github.kittinunf.result.Result
import com.github.kittinunf.result.failure
import java.util.UUID

class AccreditedFilterService(
    private val siteAccreditedNetworkService: SiteAccreditedNetworkService,
) {
    suspend fun createFilter(
        siteAccreditedNetworkId: UUID?,
        categories: List<AccreditedCategory>?,
        specialityId: UUID?,
        lat: String?,
        lon: String?,
        radiusInMeters: Int,
        limit: Int? = null,
        offset: Int? = null,
        providerName: String? = null,
    ): Result<AccreditedService.Filter, Throwable> =
        if (lat != null && lon != null) {
            val siteAccreditedNetworkToFilter = siteAccreditedNetworkId ?: siteAccreditedNetworkIdDefaultFilter().toUUID()

            val siteAccreditedNetwork = siteAccreditedNetworkService.get(siteAccreditedNetworkToFilter).fold(
                success = { it },
                failure = { return it.failure() }
            )

            resultOf {
                AccreditedService.Filter(
                    categories = categories,
                    geoLocation = AccreditedService.GeoLocationFilter(
                        radiusInMeters,
                        lat,
                        lon,
                    ),
                    specialtyId = specialityId,
                    siteAccreditedNetwork = siteAccreditedNetwork,
                    providerName = providerName,
                    limit = limit,
                    offset = offset,
                )
            }
        } else {
            throw BadRequestException("Either placeId or lat and lon must be provided")
        }

    private fun siteAccreditedNetworkIdDefaultFilter() =
        FeatureService.get(
            namespace = FeatureNamespace.MEMBER_WANNABE,
            key = "site_accredited_network_id_default_filter",
            defaultValue = "d4b8df07-fc44-4823-a036-e4b8a15ab400",
        )
}
