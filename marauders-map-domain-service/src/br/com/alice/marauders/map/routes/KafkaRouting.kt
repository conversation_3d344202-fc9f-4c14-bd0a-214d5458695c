package br.com.alice.marauders.map.routes

import br.com.alice.clinicalaccount.event.PersonClinicalAccountDeleteEvent
import br.com.alice.clinicalaccount.event.PersonHealthcareTeamAssociationUpdatedEvent
import br.com.alice.common.extensions.inject
import br.com.alice.common.kafka.internals.ConsumerJob
import br.com.alice.healthcondition.event.CaseRecordCreatedEvent
import br.com.alice.marauders.map.consumers.AppointmentCoordinationCreatedEventConsumer
import br.com.alice.marauders.map.consumers.CaseRecordConsumer
import br.com.alice.marauders.map.consumers.PersonClinicalAccountConsumer
import br.com.alice.marauders.map.consumers.PersonHealthEventConsumer
import br.com.alice.marauders.map.consumers.PersonHealthcareTeamConsumer
import br.com.alice.marauders.map.consumers.RiskCreatedEventConsumer
import br.com.alice.marauders.map.event.RiskCreatedEvent
import br.com.alice.sortinghat.event.PersonTeamAssociationEvent
import br.com.alice.wanda.event.AppointmentCoordinationCreatedEvent
import br.com.alice.wanda.event.PersonHealthEventCreatedEvent
import br.com.alice.wanda.event.PersonHealthEventUpdatedEvent

fun ConsumerJob.Configuration.kafkaRoutes() {

    val caseRecordConsumer by inject<CaseRecordConsumer>()
    consume(
        handlerName = "handle-case-record-created",
        topicName = CaseRecordCreatedEvent.name,
        handler = caseRecordConsumer::updateHealthcareMap
    )
    consume(
        handlerName = "handle-case-record-created-update-risk",
        topicName = CaseRecordCreatedEvent.name,
        handler = caseRecordConsumer::updateRisk
    )

    val personHealthcareTeamConsumer by inject<PersonHealthcareTeamConsumer>()
    consume(
        handlerName = "handle-person-healthcare-team-association-update",
        topicName = PersonHealthcareTeamAssociationUpdatedEvent.name,
        handler = personHealthcareTeamConsumer::healthcareTeamAssociation
    )

    consume(
        handlerName = "handle-healthcare-team-association",
        topicName = PersonTeamAssociationEvent.name,
        handler = personHealthcareTeamConsumer::associateWithTeam
    )

    val personHealthEventConsumer by inject<PersonHealthEventConsumer>()
    consume(
        handlerName = "handle-person-health-event-created",
        topicName = PersonHealthEventCreatedEvent.name,
        handler = personHealthEventConsumer::handleCreatePersonHealthEvent
    )
    consume(
        handlerName = "handle-person-health-event-update",
        topicName = PersonHealthEventUpdatedEvent.name,
        handler = personHealthEventConsumer::handleUpdatePersonHealthEvent
    )

    val personClinicalAccountConsumer by inject<PersonClinicalAccountConsumer>()
    consume(
        handlerName = "handle-person-clinical-account-event-delete",
        topicName = PersonClinicalAccountDeleteEvent.name,
        handler = personClinicalAccountConsumer::deletePersonClinicalAccount
    )

    val riskCreatedEventConsumer by inject<RiskCreatedEventConsumer>()
    consume(
        handlerName = "handle-risk-created-update-healthcare-map",
        topicName = RiskCreatedEvent.name,
        handler = riskCreatedEventConsumer::updateHealthcareMap
    )
    consume(
        handlerName = "validate-risk-created",
        topicName = RiskCreatedEvent.name,
        handler = riskCreatedEventConsumer::validateRisk
    )

    val appointmentCoordinationCreatedEventConsumer by inject<AppointmentCoordinationCreatedEventConsumer>()
    consume(
        handlerName = "handle-appointment-coordination-created-event",
        topicName = AppointmentCoordinationCreatedEvent.name,
        handler = appointmentCoordinationCreatedEventConsumer::validateRisk
    )
}
