package br.com.alice.sales_channel.events

import br.com.alice.common.notification.NotificationEvent
import br.com.alice.data.layer.SALES_CHANNEL_API_ROOT_SERVICE_NAME

data class SignerAddedToContractEvent (
    val clicksignPayload: ClicksignPayload
): NotificationEvent<ClicksignPayload>(
    name = name,
    producer = SALES_CHANNEL_API_ROOT_SERVICE_NAME,
    payload = clicksignPayload
) {
    companion object {
        const val name = "SIGNER-ADDED-TO-CONTRACT-EVENT"
    }
}

data class ClicksignPayload(
    val event: ClicksignEvent,
    val document: ClicksignDocument
)

data class ClicksignUser(
    val email: String,
    val name: String
)

data class ClicksignAccount(
    val key: String
)

data class ClicksignSigner(
    val signAs: String,
    val key: String,
    var email: String,
    val name: String,
    val birthday: String?,
    val documentation: String?,
    val hasDocumentation: <PERSON>olean,
    val createdAt: String,
    val auths: List<String>?,
    val selfieEnabled: Boolean,
    val handwrittenEnabled: Boolean,
    val officialDocumentEnabled: Boolean,
    val livenessEnabled: <PERSON><PERSON><PERSON>,
    val facialBiometricsEnabled: Boolean,
    val communicateBy: String?,
    val listKey: String?,
    val url: String?,
    val readReceipts: Boolean?
)

data class ClicksignDocumentDownloads(
    val originalFileUrl: String
)

data class ClicksignDocument(
    val key: String,
    val accountKey: String,
    val path: String,
    val filename: String,
    val uploadedAt: String,
    val updatedAt: String,
    val finishedAt: String?,
    val deadlineAt: String,
    val status: String,
    val autoClose: Boolean,
    val locale: String,
    val metadata: Map<String, Any>,
    val sequenceEnabled: Boolean,
    val signableGroup: Int,
    val remindInterval: String?,
    val blockAfterRefusal: Boolean,
    val preview: Boolean,
    val downloads: ClicksignDocumentDownloads,
    val template: String?,
    val signers: List<ClicksignSigner>?,
    val events: List<ClicksignEvent>,
    val attachments: List<Any>,
    val links: ClicksignLinks
)

data class ClicksignEvent(
    val name: String,
    val data: ClicksignEventData,
    val occurredAt: String
)

data class ClicksignEventData(
    val user: ClicksignUser?,
    val account: ClicksignAccount,
    val signers: List<ClicksignSigner>?
)

data class ClicksignLinks(
    val self: String
)
