package br.com.alice.data.layer.policies.features

import br.com.alice.data.layer.authorization.Count
import br.com.alice.data.layer.authorization.Create
import br.com.alice.data.layer.authorization.Delete
import br.com.alice.data.layer.authorization.Update
import br.com.alice.data.layer.authorization.View
import br.com.alice.data.layer.authorization.policySet
import br.com.alice.data.layer.models.*
import br.com.alice.data.layer.subjects.EmailDomain
import br.com.alice.data.layer.subjects.Unauthenticated

val createElibilityCheck = policySet {
    describe("at create-eligibility-check") {
        match("can view and update and create", { action is View || action is Update || action is Create }) {
            match("any EligibilityCheck") { resource is EligibilityCheckModel }
        }
    }
}

val createGuia = policySet {
    describe("at create-guia") {
        match("can view", { action is View }) {
            match("any PersonModel") { resource is PersonModel }
            match("any MemberModel") { resource is MemberModel }
            match("any Provider") { resource is ProviderModel }
            includes(viewProductComponents)
        }
        match("can view and update and create", { action is View || action is Update || action is Create }) {
            match("any event") { resource is ExecIndicatorEventModel }
        }
    }
}

val testRequestCreateGuia = policySet {
    describe("at test-request-create-guia") {
        match("can view", { action is View }) {
            match("any PersonModel") { resource is PersonModel }
            match("any MemberModel") { resource is MemberModel }
            match("any StaffModel") { resource is StaffModel }
        }
        match("can view and update and create", { action is View || action is Update || action is Create }) {
            match("any event") { resource is ExecIndicatorEventModel }
        }
    }
}

val createTicket = policySet {
    describe("at create-ticket") {
        match("can view", { action is View }) {
            match("any PersonModel") { resource is PersonModel }
            match("any MemberModel") { resource is MemberModel }
            match("any StaffModel") { resource is StaffModel }
        }
        match("can view and update and create", { action is View || action is Update || action is Create }) {
            match("any event") { resource is ExecIndicatorEventModel}
        }
    }
}

val createAuthorizationTicket = policySet {
    describe("at create-authorization-ticket") {
        match("can view", { action is View }) {
            match("any HealthcareTeamModel") { resource is HealthcareTeamModel }
            match("any PersonModel") { resource is PersonModel }
            match("any MemberModel") { resource is MemberModel }
            match("any PersonClinicalAccount") { resource is PersonClinicalAccount }
            match("any StaffModel") { resource is StaffModel }
        }
        match("can view and update and create", { action is View || action is Update || action is Create }) {
            match("any event") { resource is ExecIndicatorEventModel }
            match("specific PersonHealthEvent") {
                resourceIs(PersonHealthEvent::class) && subject is Unauthenticated &&
                        (resource as PersonHealthEvent).referencedModelId.toString() == subject.key
            }
        }
    }
}

val authorization = policySet {
    describe("at authorization") {
        match("can view and update and create", { action is View || action is Update || action is Create }) {
            match("any authorized procedure") { resource is MvAuthorizedProcedureModel }
        }

        match("can view", { action is View }) {
            match("any execAuthorizer") { resource is ExecIndicatorAuthorizerModel }
        }
    }
}

val totvsUpsert = policySet {
    describe("at totvsUpsert") {
        match("can view", { action is View }) {
            match("any execAuthorizer") { resource is ExecIndicatorAuthorizerModel }
            match("any ProviderUnit") { resource is ProviderUnitModel }
            match("any StructuredAddress") { resource is StructuredAddress }
        }
    }
}

val execution = policySet {
    describe("at execution") {
        match("can view", { action is View }) {
            match("any ExecutionGroup") { resource is ExecutionGroupModel }
        }
        match("can view and update and create", { action is View || action is Update || action is Create }) {
            match("any authorized procedure") { resource is MvAuthorizedProcedureModel }
        }
    }
}

val sync = policySet {
    describe("at syncng") {
        match("can view", { action is View }) {
            match("any PersonModel") { resourceIs(PersonModel::class) }
        }
    }
}

val eventinderSync = policySet {
    describe("at syncng") {
        match("can view", { action is View }) {
            match("any HealthProfessionalModel") { resourceIs(HealthProfessionalModel::class) }
            match("any MedicalSpecialty") { resourceIs(MedicalSpecialtyModel::class) }
        }
    }
}

val updateExecutionGroupTag = policySet {
    describe("at update-execution-group-tag") {
        match("can view and update and create", { action is View || action is Update || action is Create }) {
            match("any ExecutionGroup") { resource is ExecutionGroupModel }
        }
    }
}

val viewAndUpdateAndCreateProcedureProvider = policySet {
    describe("at system-api") {
        match("can view and update and create", { action is View || action is Update || action is Create }) {
            match("any ProcedureProvider") { resource is ProcedureProviderModel }
        }
    }
}

val healthDocumentSet = policySet {
    describe("at provider-health-document") {
        match("can view and create and update", { action is View || action is Create || action is Update }) {
            match("any document") { resource is ProviderHealthDocumentModel }
        }
    }
}

val viewAndUpdateAndCreateHealthDocument = policySet {
    match("can view and update and create", { action is View || action is Update || action is Create }) {
        match("any health document") {
            resource is FileVault &&
                    resource.domain == "eita" &&
                    resource.namespace == "provider_health_document" &&
                    subject is EmailDomain
        }
    }
}

val viewAndCreateHealthDocument = policySet {
    match("can view and create", { action is View || action is Create }) {
        match("any health document") {
            resource is FileVault &&
                    resource.domain == "eita" &&
                    resource.namespace == "provider_health_document" &&
                    ((subject is StaffModel && subject.isEitaUser()) || subject is EmailDomain)
        }
    }
}

val viewAndUpdateAndCreateProfessionalTierProcedureValues = policySet {
    describe("at system-api") {
        match("can view and update and create", { action is View || action is Update || action is Create }) {
            match("any ProfessionalTierProcedureValue") { resource is ProfessionalTierProcedureValueModel }
        }
    }
}
val viewAndCountHealthCommunitySpecialist = policySet {
    match("can view", { action is View }) {
        match("any Health Community Specialist") { resource is HealthCommunitySpecialistModel }
        match("any Health Professional") { resource is HealthProfessionalModel }
        match("any StructuredAddress") { resource is StructuredAddress }
        match("any ContactModel") { resource is ContactModel }
        match("any Medical Specialty") { resource is MedicalSpecialtyModel }
    }
    match("can count", { action is Count }) {
        match("any Health Community Specialist") { resourceIs(HealthCommunitySpecialistModel::class) }
    }
}

val basicAccessMagicNumbers = policySet {
    match("can view and update and create", { action is View || action is Update || action is Create }) {
        match("any MagicNumbers") { resource is MagicNumbersModel }
    }
}

val rollbackProcedureExecution = policySet {
    match("can count", { action is Count }) {
        match("any MvAuthorizedProcedure") { resourceIs(MvAuthorizedProcedureModel::class) }
    }
    match("can delete", { action is Delete }) {
        match("any ExecutionGroup") { resource is ExecutionGroupModel }
    }
}

val confirmExecution = policySet {
    match("can view and update", { action is View || action is Update }) {
        match("any MvAuthorizedProcedure") { resource is MvAuthorizedProcedureModel }
    }
    match("can view", { action is View }) {
        match("any ExecIndicatorAuthorizer") { resource is ExecIndicatorAuthorizerModel }
        match("any ExecutionGroup") { resource is ExecutionGroupModel }
        match("any TotvsGuia") { resource is TotvsGuiaModel }
    }
}

val canViewStaffAndHealthProfessional = policySet {
    match("can view", { action is View }) {
        match("any StaffModel") { resource is StaffModel }
        match("any HealthProfessionalModel") { resource is HealthProfessionalModel }
    }
}

val canViewExternalReferral = policySet {
    describe("can view") {
        allows(ExternalReferralModel::class, View)
    }
}

val canViewMedicalSpecialty = policySet {
    describe("can view") {
        allows(MedicalSpecialtyModel::class, View)
    }
}

val canViewHealthCondition = policySet {
    describe("can view") {
        allows(HealthCondition::class, View, Count)
    }
}

val canCreateAndDeleteExternalReferral = policySet {
    describe("can create and delete") {
        allows(ExternalReferralModel::class, Create, Delete)
    }
}

val canUpdateHealthcareBundleProviderName = policySet {
    describe("can view and update bundle") {
        allows(StaffModel::class, View)
        allows(ProviderUnitModel::class, View)
        allows(HealthcareBundleModel::class, View, Update)
    }
}

val canViewCreateAndUpdateTotvsGuia = policySet {
    match("can view and update and create", { action is View || action is Update || action is Create }) {
        match("any TotvsGuia") { resource is TotvsGuiaModel }
    }
}

val canViewFileVault = policySet {
    match("can view", { action is View }) {
        match("any FileVault") { resource is FileVault }
    }
}

val canViewGlossAuthorizationInfo = policySet {
    match("can view", { action is View }) {
        match("any GlossAuthorizationInfo") { resource is GlossAuthorizationInfoModel }
    }
}

val canViewHealthcareResourceGroupAssociation = policySet {
    allows(HealthcareResourceGroupAssociationModel::class, View)
    allows(HealthcareBundleModel::class, View)
}

val canViewAndUpdateHealthcareResource = policySet {
    allows(HealthcareResourceModel::class, Count, View, Update)
}

val canViewCreateAndUpdateGuiaWithProcedures = policySet {
    allows(GuiaWithProceduresModel::class, Count, View, Update, Create)
}

val canViewGuiaWithProcedures = policySet {
    allows(GuiaWithProceduresModel::class, Count, View)
}

val canViewCreateAndUpdateAttachmentOpme = policySet {
    allows(AttachmentOpmeModel::class, Count, View, Update, Create)
}

