package br.com.alice.data.layer.models

import br.com.alice.common.core.exceptions.BadRequestException
import br.com.alice.common.core.extensions.classSimpleName
import br.com.alice.data.layer.models.BeneficiaryOnboardingPhaseType.*

interface BeneficiaryOnboardingFlow {
    fun nextPhase(currentPhase: BeneficiaryOnboardingPhaseType): BeneficiaryOnboardingPhaseType

    val initialPhase: BeneficiaryOnboardingPhaseType
    val finalPhase: BeneficiaryOnboardingPhaseType

    fun previousPhase(currentPhase: BeneficiaryOnboardingPhaseType): BeneficiaryOnboardingPhaseType {
        var prev = initialPhase
        var current = prev

        while (current != currentPhase) {
            if (current == finalPhase) throw PhaseOutOfOnboardingFlowException(this, currentPhase)

            val next = nextPhase(current)
            prev = current
            current = next
        }

        return prev
    }

    fun stepsForPhase(currentPhase: BeneficiaryOnboardingPhaseType, targetPhase: BeneficiaryOnboardingPhaseType): Int {
        var current = currentPhase
        var count = 0
        val direction = if (targetPhase > currentPhase) 1 else -1

        while (current != targetPhase) {
            if (current == finalPhase) break


            val next = if (direction > 0) nextPhase(current) else previousPhase(current)

            current = next
            count = count.plus(direction)
        }

        return count
    }

    fun allPhases(): List<BeneficiaryOnboardingPhaseType> {
        val phases = mutableListOf<BeneficiaryOnboardingPhaseType>()
        var current = initialPhase

        while (current != finalPhase) {
            phases.add(current)
            current = nextPhase(current)
        }

        phases.add(finalPhase)

        return phases
    }
}

internal object OnboardingFlowFactory {
    fun get(type: BeneficiaryOnboardingFlowType) = when (type) {
        BeneficiaryOnboardingFlowType.FULL_RISK_FLOW -> FullRiskFlow
        BeneficiaryOnboardingFlowType.PARTIAL_RISK_FLOW -> PartialRiskFlow
        BeneficiaryOnboardingFlowType.NO_RISK_FLOW -> NoRiskFlow
        BeneficiaryOnboardingFlowType.UNDEFINED -> UndefinedFlow
    }
}

internal object FullRiskFlow : BeneficiaryOnboardingFlow {
    override val initialPhase = READY_TO_ONBOARD
    override val finalPhase = FINISHED

    override fun nextPhase(currentPhase: BeneficiaryOnboardingPhaseType) = when (currentPhase) {
        READY_TO_ONBOARD -> HEALTH_DECLARATION
        HEALTH_DECLARATION -> HEALTH_DECLARATION_APPOINTMENT
        HEALTH_DECLARATION_APPOINTMENT -> HEALTH_DECLARATION_APPOINTMENT_SCHEDULED
        HEALTH_DECLARATION_APPOINTMENT_SCHEDULED -> WAITING_CPTS_APPLICATION
        WAITING_CPTS_APPLICATION -> SEND_CPTS
        SEND_CPTS -> CPTS_CONFIRMATION
        CPTS_CONFIRMATION -> CONTRACT_SIGNATURE
        CONTRACT_SIGNATURE -> REGISTRATION
        REGISTRATION -> WAITING_FOR_REVIEW
        WAITING_FOR_REVIEW -> FINISHED
        FINISHED -> FINISHED
        SEND_HEALTH_DECLARATION,
        SEND_HEALTH_DECLARATION_APPOINTMENT,
        SEND_REGISTRATION, HEALTH_CARE_TEAM_SELECTION -> throw InvalidBusinessOnboardingFlowStateException(
            this,
            currentPhase
        )
    }
}

internal object PartialRiskFlow : BeneficiaryOnboardingFlow {
    override val initialPhase = READY_TO_ONBOARD
    override val finalPhase = FINISHED

    override fun nextPhase(currentPhase: BeneficiaryOnboardingPhaseType) = when (currentPhase) {
        READY_TO_ONBOARD -> HEALTH_DECLARATION
        HEALTH_DECLARATION -> HEALTH_DECLARATION_APPOINTMENT
        HEALTH_DECLARATION_APPOINTMENT -> HEALTH_DECLARATION_APPOINTMENT_SCHEDULED
        HEALTH_DECLARATION_APPOINTMENT_SCHEDULED -> WAITING_CPTS_APPLICATION
        WAITING_CPTS_APPLICATION -> SEND_CPTS
        SEND_CPTS -> CPTS_CONFIRMATION
        CPTS_CONFIRMATION -> CONTRACT_SIGNATURE
        CONTRACT_SIGNATURE -> REGISTRATION
        REGISTRATION -> WAITING_FOR_REVIEW
        WAITING_FOR_REVIEW -> FINISHED
        FINISHED -> FINISHED
        SEND_HEALTH_DECLARATION,
        SEND_HEALTH_DECLARATION_APPOINTMENT,
        SEND_REGISTRATION, HEALTH_CARE_TEAM_SELECTION -> throw InvalidBusinessOnboardingFlowStateException(
            this,
            currentPhase
        )
    }
}

internal object NoRiskFlow : BeneficiaryOnboardingFlow {
    override val initialPhase = READY_TO_ONBOARD
    override val finalPhase = FINISHED

    override fun nextPhase(currentPhase: BeneficiaryOnboardingPhaseType) = when (currentPhase) {
        READY_TO_ONBOARD -> REGISTRATION
        REGISTRATION -> WAITING_FOR_REVIEW
        WAITING_FOR_REVIEW -> FINISHED
        FINISHED -> FINISHED
        SEND_HEALTH_DECLARATION_APPOINTMENT,
        HEALTH_DECLARATION_APPOINTMENT,
        HEALTH_DECLARATION_APPOINTMENT_SCHEDULED,
        SEND_HEALTH_DECLARATION,
        HEALTH_DECLARATION,
        WAITING_CPTS_APPLICATION,
        SEND_CPTS,
        CPTS_CONFIRMATION,
        CONTRACT_SIGNATURE,
        SEND_REGISTRATION, HEALTH_CARE_TEAM_SELECTION -> throw InvalidBusinessOnboardingFlowStateException(
            this,
            currentPhase
        )
    }
}

internal object UndefinedFlow : BeneficiaryOnboardingFlow {
    override val initialPhase get() = throw InvalidBusinessOnboardingFlowInitialStateException(this)
    override val finalPhase get() = throw InvalidBusinessOnboardingFlowInitialStateException(this)

    override fun nextPhase(currentPhase: BeneficiaryOnboardingPhaseType) =
        throw InvalidBusinessOnboardingFlowStateException(this, currentPhase)
}

class InvalidBusinessOnboardingFlowStateException(
    message: String,
    code: String = "invalid_business_onboarding_flow_state",
    cause: Throwable? = null
) : BadRequestException(message, code, cause) {
    constructor(flow: BeneficiaryOnboardingFlow, phase: BeneficiaryOnboardingPhaseType) : this(
        message = "Flow is in invalid state. Unable to move to next phase - Flow: ${flow.classSimpleName()}, Current Phase: $phase"
    )
}

class InvalidBusinessOnboardingFlowInitialStateException(
    message: String,
    code: String = "invalid_business_onboarding_flow_initial_state",
    cause: Throwable? = null
) : BadRequestException(message, code, cause) {
    constructor(flow: BeneficiaryOnboardingFlow) : this(
        message = "Flow is in invalid state. Unable to get initial phase - Flow: ${flow.classSimpleName()}"
    )
}

class PhaseOutOfOnboardingFlowException(
    message: String,
    code: String = "phase_out_of_onboarding_flow",
    cause: Throwable? = null
) : BadRequestException(message, code, cause) {
    constructor(flow: BeneficiaryOnboardingFlow, phase: BeneficiaryOnboardingPhaseType) : this(
        message = "Phase is not part of flow. Unable to find previous phase - Flow: ${flow.classSimpleName()} - Phase: $phase"
    )
}
