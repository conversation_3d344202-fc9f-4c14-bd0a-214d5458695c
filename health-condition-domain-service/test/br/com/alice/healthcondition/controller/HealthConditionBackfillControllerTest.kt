package br.com.alice.healthcondition.controller

import br.com.alice.common.data.dsl.matchers.ResponseAssert.Companion.assertThat
import br.com.alice.common.data.dsl.matchers.queryEq
import br.com.alice.common.helpers.bodyAsJson
import br.com.alice.common.helpers.coVerifyNone
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.common.helpers.returns
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.CptApplicationRule
import br.com.alice.data.layer.models.HealthConditionCodeType
import br.com.alice.data.layer.models.HealthConditionType
import br.com.alice.data.layer.services.HealthConditionDataService
import br.com.alice.healthcondition.client.HealthConditionService
import br.com.alice.healthcondition.client.HealthConditionTemplateService
import br.com.alice.healthcondition.controllers.CidAndCptApplicationRule
import br.com.alice.healthcondition.controllers.CidAndRiskRating
import br.com.alice.healthcondition.controllers.CidAndTemplate
import br.com.alice.healthcondition.controllers.HealthConditionBackfillController
import br.com.alice.healthcondition.controllers.UpdateCptApplicationRuleRequest
import br.com.alice.healthcondition.controllers.UpdateHealthConditionRequest
import br.com.alice.healthcondition.controllers.UpdateHealthConditionResponse
import br.com.alice.healthcondition.controllers.UpdateHealthConditionsRequest
import br.com.alice.healthcondition.controllers.UpdateRiskRatingRequest
import br.com.alice.healthcondition.controllers.UpsertHealthConditionTemplateRequest
import com.github.kittinunf.result.failure
import com.github.kittinunf.result.success
import io.mockk.clearAllMocks
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.mockk
import kotlinx.coroutines.runBlocking
import org.assertj.core.api.Assertions.assertThat
import java.math.BigDecimal
import kotlin.test.AfterTest
import kotlin.test.BeforeTest
import kotlin.test.Test

class HealthConditionBackfillControllerTest : RoutesTestHelper() {
    private val healthConditionService: HealthConditionService = mockk()
    private val healthConditionTemplateService: HealthConditionTemplateService = mockk()
    private val healthConditionDataService: HealthConditionDataService = mockk()

    private val controller =
        HealthConditionBackfillController(
            healthConditionService,
            healthConditionDataService,
            healthConditionTemplateService
        )

    @BeforeTest
    override fun setup() {
        super.setup()

        module.single { controller }
    }

    @AfterTest
    fun clear() {
        clearAllMocks()
    }

    @Test
    fun `#updateRiskRating - should update by record CID`() = runBlocking {
        val healthCondition1 = TestModelFactory.buildHealthCondition(
            code = "A510",
            codeType = HealthConditionCodeType.CID_10,
            riskRating = 5
        )
        val healthCondition2 = TestModelFactory.buildHealthCondition(
            code = "A520",
            codeType = HealthConditionCodeType.CID_10,
            riskRating = 5
        )
        val healthCondition3 = TestModelFactory.buildHealthCondition(
            code = "A530",
            codeType = HealthConditionCodeType.CID_10,
            riskRating = 5
        )
        val healthCondition1Updated = healthCondition1.copy(riskRating = 0, version = healthCondition1.version + 1)
        val healthCondition2Updated = healthCondition2.copy(riskRating = 1, version = healthCondition2.version + 1)

        val request = UpdateRiskRatingRequest(
            listOf(
                CidAndRiskRating("A510", 0),
                CidAndRiskRating("A520", 1),
                CidAndRiskRating("A530", 0)
            )
        )

        val expected = mapOf(
            "success_count" to 2.0,
            "errors_count" to 1.0,
            "map_errors" to mapOf("A530" to "Not found")
        )

        coEvery {
            healthConditionService.findByCodesAndType(listOf("A510", "A520", "A530"), HealthConditionCodeType.CID_10)
        } returns listOf(healthCondition2, healthCondition1, healthCondition3).success()

        coEvery {
            healthConditionService.update(match {
                it.code == "A510" && it.riskRating == 0
            })
        } returns healthCondition1Updated.success()

        coEvery {
            healthConditionService.update(match {
                it.code == "A520" && it.riskRating == 1
            })
        } returns healthCondition2Updated.success()

        coEvery {
            healthConditionService.update(match {
                it.code == "A530" && it.riskRating == 0
            })
        } returns IllegalArgumentException("Not found").failure()

        post("/backfill/update_health_condition_risk_rating", request) { response ->
            assertThat(response).isSuccessfulJson()

            val content: Map<String, Any?> = response.bodyAsJson()
            assertThat(content).isEqualTo(expected)

            coVerifyOnce { healthConditionService.findByCodesAndType(any(), any()) }
            coVerify(exactly = 3) { healthConditionService.update(any()) }
        }
    }

    @Test
    fun `#updateCptApplicationRule - should update by record CID`() = runBlocking {
        val healthCondition1 = TestModelFactory.buildHealthCondition(
            code = "A510",
            codeType = HealthConditionCodeType.CID_10,
            cptApplicationRule = CptApplicationRule.WITHOUT_SURGERY_ONLY
        )
        val healthCondition2 = TestModelFactory.buildHealthCondition(
            code = "A520",
            codeType = HealthConditionCodeType.CID_10,
            cptApplicationRule = CptApplicationRule.WITHOUT_SURGERY_ONLY
        )
        val healthCondition3 = TestModelFactory.buildHealthCondition(
            code = "A530",
            codeType = HealthConditionCodeType.CID_10,
            cptApplicationRule = CptApplicationRule.ALWAYS
        )
        val healthCondition1Updated = healthCondition1.copy(
            cptApplicationRule = CptApplicationRule.ALWAYS, version = healthCondition1.version + 1
        )
        val healthCondition2Updated = healthCondition2.copy(
            cptApplicationRule = CptApplicationRule.NEVER, version = healthCondition2.version + 1
        )

        val request = UpdateCptApplicationRuleRequest(
            listOf(
                CidAndCptApplicationRule("A510", CptApplicationRule.ALWAYS),
                CidAndCptApplicationRule("A520", CptApplicationRule.NEVER),
                CidAndCptApplicationRule("A530", CptApplicationRule.WITHOUT_SURGERY_ONLY)
            )
        )

        val expected = mapOf(
            "success_count" to 2.0,
            "errors_count" to 1.0,
            "map_errors" to mapOf("A530" to "Not found")
        )

        coEvery {
            healthConditionService.findByCodesAndType(listOf("A510", "A520", "A530"), HealthConditionCodeType.CID_10)
        } returns listOf(healthCondition2, healthCondition1, healthCondition3).success()

        coEvery {
            healthConditionService.update(match {
                it.code == "A510" && it.cptApplicationRule == CptApplicationRule.ALWAYS
            })
        } returns healthCondition1Updated.success()

        coEvery {
            healthConditionService.update(match {
                it.code == "A520" && it.cptApplicationRule == CptApplicationRule.NEVER
            })
        } returns healthCondition2Updated.success()

        coEvery {
            healthConditionService.update(match {
                it.code == "A530" && it.cptApplicationRule == CptApplicationRule.WITHOUT_SURGERY_ONLY
            })
        } returns IllegalArgumentException("Not found").failure()

        post("/backfill/update_health_condition_cpt_application_rule", request) { response ->
            assertThat(response).isSuccessfulJson()

            val content: Map<String, Any?> = response.bodyAsJson()
            assertThat(content).isEqualTo(expected)

            coVerifyOnce { healthConditionService.findByCodesAndType(any(), any()) }
            coVerify(exactly = 3) { healthConditionService.update(any()) }
        }
    }

    @Test
    fun `#upsertTemplate - should upsert by record CID`() = runBlocking {
        val healthCondition1 = TestModelFactory.buildHealthCondition(
            code = "A510",
            codeType = HealthConditionCodeType.CID_10,
            riskRating = 5
        )
        val healthCondition2 = TestModelFactory.buildHealthCondition(
            code = "A520",
            codeType = HealthConditionCodeType.CID_10,
            riskRating = 5
        )
        val healthCondition3 = TestModelFactory.buildHealthCondition(
            code = "A530",
            codeType = HealthConditionCodeType.CID_10,
            riskRating = 5
        )
        val healthConditionTemplate1 = TestModelFactory.buildHealthConditionTemplate(
            healthConditionId = healthCondition1.id,
            template = "template 1"
        )
        val healthConditionTemplate2 = TestModelFactory.buildHealthConditionTemplate(
            healthConditionId = healthCondition2.id,
            template = "template 2"
        )

        val request = UpsertHealthConditionTemplateRequest(
            listOf(
                CidAndTemplate("A510", "template 1"),
                CidAndTemplate("A520", "template 2"),
                CidAndTemplate("A530", "template 3")
            )
        )

        val expected = mapOf(
            "success_count" to 2.0,
            "errors_count" to 1.0,
            "map_errors" to mapOf("A530" to "Not found")
        )

        coEvery {
            healthConditionService.findByCodesAndType(listOf("A510", "A520", "A530"), HealthConditionCodeType.CID_10)
        } returns listOf(healthCondition2, healthCondition1, healthCondition3).success()

        coEvery {
            healthConditionTemplateService.upsert(match {
                it.healthConditionId == healthCondition1.id && it.template == "template 1"
            })
        } returns healthConditionTemplate1.success()

        coEvery {
            healthConditionTemplateService.upsert(match {
                it.healthConditionId == healthCondition2.id && it.template == "template 2"
            })
        } returns healthConditionTemplate2.success()

        coEvery {
            healthConditionTemplateService.upsert(match {
                it.healthConditionId == healthCondition3.id && it.template == "template 3"
            })
        } returns IllegalArgumentException("Not found").failure()

        post("/backfill/upsert_health_condition_template", request) { response ->
            assertThat(response).isSuccessfulJson()

            val content: Map<String, Any?> = response.bodyAsJson()
            assertThat(content).isEqualTo(expected)

            coVerifyOnce { healthConditionService.findByCodesAndType(any(), any()) }
            coVerify(exactly = 3) { healthConditionTemplateService.upsert(any()) }
        }
    }

    @Test
    fun `#updateHealthCondition - should update health condition with new values`() = runBlocking {
        val request = UpdateHealthConditionsRequest(healthConditions = buildUpdateRequest())
        val healthConditions = buildHealthConditions()
        val updatedHealthConditions = listOf(
            healthConditions.first { it.code == "A123" }.copy(
                isChronic = true,
                riskRating = 3,
                displayName = "Updated Display Name",
                memberFriendlyName = "Friendly Name",
                conditionType = HealthConditionType.LONGITUDINAL,
                suggestedMonthlyCost = BigDecimal(50.0),
                active = true
            ),
            healthConditions.first { it.code == "B456" }.copy(
                isChronic = false,
                riskRating = 2,
                displayName = "Another Condition",
                memberFriendlyName = "Another Friendly Name",
                conditionType = HealthConditionType.ACUTE,
                suggestedMonthlyCost = BigDecimal(100.0),
                active = false
            )
        )
        val expectedResponse = UpdateHealthConditionResponse(
            successCount = 2,
            errorsCount = 0,
            mapErrors = emptyMap()
        )

        coEvery {
            healthConditionDataService.find(queryEq {
                where { this.code.inList(listOf("A123", "B456")) }
            })
        } returns healthConditions

        coEvery {
            healthConditionDataService.updateList(updatedHealthConditions)
        } returns updatedHealthConditions

        post(to = "/backfill/update_health_condition", body = request) { response ->
            assertThat(response).isSuccessfulJson()

            val content: UpdateHealthConditionResponse = response.bodyAsJson()
            assertThat(content).isEqualTo(expectedResponse)
            coVerifyOnce {
                healthConditionDataService.find(any())
                healthConditionDataService.updateList(any())
            }
        }
    }

    @Test
    fun `#updateHealthCondition - should return empty response when health conditions not found`() = runBlocking {
        val request = UpdateHealthConditionsRequest(healthConditions = buildUpdateRequest())
        val expectedResponse = UpdateHealthConditionResponse(
            successCount = 0,
            errorsCount = 0,
            mapErrors = emptyMap()
        )

        coEvery {
            healthConditionDataService.find(queryEq {
                where { this.code.inList(listOf("A123", "B456")) }
            })
        } returns emptyList()

        post(to = "/backfill/update_health_condition", body = request) { response ->
            assertThat(response).isSuccessfulJson()

            val content: UpdateHealthConditionResponse = response.bodyAsJson()
            assertThat(content).isEqualTo(expectedResponse)
            coVerifyOnce { healthConditionDataService.find(any()) }
            coVerifyNone { healthConditionDataService.updateList(any()) }
        }
    }

    @Test
    fun `#updateHealthCondition - should return empty response with error when request is empty`() = runBlocking {
        val request = UpdateHealthConditionsRequest(healthConditions = emptyList())
        val expectedResponse = UpdateHealthConditionResponse(
            successCount = 0,
            errorsCount = 0,
            mapErrors = mapOf("error" to "No health conditions provided")
        )

        post(to = "/backfill/update_health_condition", body = request) { response ->
            assertThat(response).isSuccessfulJson()

            val content: UpdateHealthConditionResponse = response.bodyAsJson()
            assertThat(content).isEqualTo(expectedResponse)
            coVerifyNone { healthConditionDataService.find(any()) }
            coVerifyNone { healthConditionDataService.updateList(any()) }
        }
    }

    @Test
    fun `#updateHealthCondition - should return empty response with error when request exceed limit`() = runBlocking {
        val healthConditions = List(1001) { UpdateHealthConditionRequest(cid = "test") }
        val request = UpdateHealthConditionsRequest(healthConditions = healthConditions)
        val expectedResponse = UpdateHealthConditionResponse(
            successCount = 0,
            errorsCount = 0,
            mapOf("error" to "Too many health conditions provided, maximum is 1000")
        )

        post(to = "/backfill/update_health_condition", body = request) { response ->
            assertThat(response).isSuccessfulJson()

            val content: UpdateHealthConditionResponse = response.bodyAsJson()
            assertThat(content).isEqualTo(expectedResponse)
            coVerifyNone { healthConditionDataService.find(any()) }
            coVerifyNone { healthConditionDataService.updateList(any()) }
        }
    }

    private fun buildUpdateRequest() = listOf(
        UpdateHealthConditionRequest(
            cid = "A123",
            isChronic = true,
            riskRating = 3,
            displayName = "Updated Display Name",
            memberFriendlyName = "Friendly Name",
            conditionType = HealthConditionType.LONGITUDINAL,
            suggestedMonthlyCost = BigDecimal(50.0),
            active = true
        ),
        UpdateHealthConditionRequest(
            cid = "B456",
            isChronic = false,
            riskRating = 2,
            displayName = "Another Condition",
            memberFriendlyName = "Another Friendly Name",
            conditionType = HealthConditionType.ACUTE,
            suggestedMonthlyCost = BigDecimal(100.0),
            active = false
        )
    )

    private fun buildHealthConditions() = listOf(
        buildHealthCondition(
            code = "A123",
            isChronic = false,
            riskRating = 2,
            displayName = "Old Display Name",
            memberFriendlyName = "Old Friendly Name",
            conditionType = HealthConditionType.ACUTE,
            suggestedMonthlyCost = BigDecimal(10.0),
            active = false
        ),
        buildHealthCondition(
            code = "B456",
            isChronic = true,
            riskRating = 1,
            displayName = "Another old Condition",
            memberFriendlyName = "Another old Friendly Name",
            conditionType = HealthConditionType.LONGITUDINAL,
            suggestedMonthlyCost = BigDecimal(10.0),
            active = true
        )
    )

    private fun buildHealthCondition(
        code: String,
        isChronic: Boolean,
        riskRating: Int,
        displayName: String,
        memberFriendlyName: String? = null,
        conditionType: HealthConditionType? = null,
        suggestedMonthlyCost: BigDecimal? = null,
        active: Boolean
    ) = TestModelFactory.buildHealthCondition().copy(
        code = code,
        isChronic = isChronic,
        riskRating = riskRating,
        displayName = displayName,
        memberFriendlyName = memberFriendlyName,
        conditionType = conditionType,
        suggestedMonthlyCost = suggestedMonthlyCost,
        active = active
    )

}
