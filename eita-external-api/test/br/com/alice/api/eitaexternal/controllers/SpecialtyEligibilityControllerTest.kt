package br.com.alice.api.eitaexternal.controllers

import br.com.alice.api.eitaexternal.ControllerTestHelper
import br.com.alice.common.RangeUUID
import br.com.alice.common.core.StaffType
import br.com.alice.common.core.exceptions.NotFoundException
import br.com.alice.common.core.extensions.atEndOfTheDay
import br.com.alice.common.data.dsl.matchers.ResponseAssert.Companion.assertThat
import br.com.alice.common.featureflag.withFeatureFlag
import br.com.alice.common.helpers.coVerifyNone
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.common.Brand
import br.com.alice.data.layer.models.ExternalReferral
import br.com.alice.data.layer.models.FeatureNamespace
import br.com.alice.data.layer.models.HealthPlanTask
import br.com.alice.ehr.client.ExternalReferralService
import br.com.alice.healthplan.client.HealthPlanTaskService
import br.com.alice.nullvs.client.NullvsExternalIdService
import br.com.alice.person.client.MemberService
import br.com.alice.person.client.PersonService
import br.com.alice.product.client.ProductService
import br.com.alice.provider.client.MedicalSpecialtyService
import br.com.alice.staff.client.HealthProfessionalService
import com.github.kittinunf.result.failure
import com.github.kittinunf.result.success
import io.mockk.coEvery
import io.mockk.mockk
import kotlinx.coroutines.runBlocking
import java.time.LocalDate
import kotlin.test.BeforeTest
import kotlin.test.Test

class SpecialtyEligibilityControllerTest : ControllerTestHelper() {
    private val personService: PersonService = mockk()
    private val memberService: MemberService = mockk()
    private val healthPlanTaskService: HealthPlanTaskService = mockk()
    private val medicalSpecialtyService: MedicalSpecialtyService = mockk()
    private val externalReferralService: ExternalReferralService = mockk()
    private val healthProfessionalService: HealthProfessionalService = mockk()
    private val nullvsExternalIdService: NullvsExternalIdService = mockk()
    private val productService: ProductService = mockk()

    private val nationalId = "01234567890"
    private val cbo = "255555"

    private val person = TestModelFactory.buildPerson(nationalId = nationalId)
    private val member = TestModelFactory.buildMember(personId = person.id, brand = Brand.DUQUESA)
    private val product = TestModelFactory.buildProduct(id = member.productId)
    private val medicalSpecialty = TestModelFactory.buildMedicalSpecialty()

    private val specialtyEligibilityController = SpecialtyEligibilityController(
        personService,
        memberService,
        healthPlanTaskService,
        medicalSpecialtyService,
        externalReferralService,
        healthProfessionalService,
        nullvsExternalIdService,
        productService
    )

    @BeforeTest
    override fun setup() {
        super.setup()
        module.single { specialtyEligibilityController }
    }

    @Test
    fun `#should return not eligible when person does not exists`() = runBlocking {
        val expected = CheckSpecialityEligibilityResponse(
            eligible = false,
            cpf = nationalId,
            cbo = cbo,
        )

        coEvery { personService.findByNationalId(nationalId) } returns NotFoundException("Not found").failure()

        val body = CheckSpecialityEligibilityRequest(nationalId, cbo)

        authenticated(idToken) {
            post("/checkSpecialistEligibility", body = body) { response ->
                assertThat(response).isOKWithData(expected)
            }

            coVerifyOnce { personService.findByNationalId(nationalId) }
            coVerifyNone { memberService.findActiveMembership(any()) }
            coVerifyNone { medicalSpecialtyService.getByCBOCode(any()) }
            coVerifyNone { externalReferralService.getActiveByPersonId(any(), any()) }
            coVerifyNone { productService.getProduct(any(), any()) }
            coVerifyNone { nullvsExternalIdService.findMemberExternalId(any()) }
        }
    }

    @Test
    fun `#should return not eligible when member does not exists`() = runBlocking {
        val expected = CheckSpecialityEligibilityResponse(
            eligible = false,
            cpf = nationalId,
            cbo = cbo,
            socialName = person.fullSocialName,
            fullName = person.fullRegisterName,
        )

        coEvery { personService.findByNationalId(nationalId) } returns person.success()
        coEvery { memberService.findActiveMembership(person.id) } returns NotFoundException("Not found").failure()

        val body = CheckSpecialityEligibilityRequest(nationalId, cbo)

        authenticated(idToken) {
            post("/checkSpecialistEligibility", body = body) { response ->
                assertThat(response).isOKWithData(expected)
            }

            coVerifyOnce { personService.findByNationalId(nationalId) }
            coVerifyOnce { memberService.findActiveMembership(person.id) }
            coVerifyNone { medicalSpecialtyService.getByCBOCode(any()) }
            coVerifyNone { externalReferralService.getActiveByPersonId(any(), any()) }
            coVerifyNone { productService.getProduct(any(), any()) }
            coVerifyNone { nullvsExternalIdService.findMemberExternalId(any()) }
        }
    }

    @Test
    fun `#should return not eligible when DUQUESA member does not have referral`() = runBlocking {
        val expected = CheckSpecialityEligibilityResponse(
            eligible = false,
            cpf = nationalId,
            cbo = cbo,
            fullName = person.fullRegisterName,
            socialName = person.fullSocialName
        )

        coEvery { personService.findByNationalId(nationalId) } returns person.success()
        coEvery { memberService.findActiveMembership(person.id) } returns member.success()
        coEvery { medicalSpecialtyService.getByCBOCode(cbo) } returns medicalSpecialty.success()
        coEvery {
            externalReferralService.getActiveByPersonId(person.id, any())
        } returns emptyList<ExternalReferral>().success()

        val body = CheckSpecialityEligibilityRequest(nationalId, cbo)

        authenticated(idToken) {
            post("/checkSpecialistEligibility", body = body) { response ->
                assertThat(response).isOKWithData(expected)
            }

            coVerifyOnce { personService.findByNationalId(nationalId) }
            coVerifyOnce { medicalSpecialtyService.getByCBOCode(cbo) }
            coVerifyOnce { memberService.findActiveMembership(person.id) }
            coVerifyOnce { externalReferralService.getActiveByPersonId(person.id, any()) }
            coVerifyNone { productService.getProduct(any(), any()) }
            coVerifyNone { nullvsExternalIdService.findMemberExternalId(any()) }
        }
    }

    @Test
    fun `#should return eligible when DUQUESA member has a referral`() = runBlocking {
        val externalReferral = TestModelFactory.buildExternalReferral(
            specialty = ExternalReferral.Specialty(
                medicalSpecialty.id,
                medicalSpecialty.name
            )
        )

        val expected = CheckSpecialityEligibilityResponse(
            eligible = true,
            cpf = nationalId,
            cbo = cbo,
            fullName = person.fullRegisterName,
            socialName = person.fullSocialName,
            dob = person.dateOfBirth,
            gender = person.gender,
            eligibilityDate = externalReferral.referredAt,
            eligibilityExpiresAt = externalReferral.dueDate,
            requesterName = externalReferral.referredByName,
            requesterCouncilNumber = "0123456:CRM/SP",
            healthPlanName = product.title,
            accountNumber = "aabbccdd",
        )

        coEvery { personService.findByNationalId(nationalId) } returns person.success()
        coEvery { memberService.findActiveMembership(person.id) } returns member.success()
        coEvery { medicalSpecialtyService.getByCBOCode(cbo) } returns medicalSpecialty.success()
        coEvery {
            externalReferralService.getActiveByPersonId(person.id, any())
        } returns listOf(externalReferral).success()
        coEvery { productService.getProduct(member.productId, any()) } returns product.success()
        coEvery { nullvsExternalIdService.findMemberExternalId(member.id) } returns "aabbccdd".success()

        val body = CheckSpecialityEligibilityRequest(nationalId, cbo)

        authenticated(idToken) {
            post("/checkSpecialistEligibility", body = body) { response ->
                assertThat(response).isOKWithData(expected)
            }

            coVerifyOnce { personService.findByNationalId(nationalId) }
            coVerifyOnce { medicalSpecialtyService.getByCBOCode(cbo) }
            coVerifyOnce { memberService.findActiveMembership(person.id) }
            coVerifyOnce { externalReferralService.getActiveByPersonId(person.id, any()) }
            coVerifyOnce { productService.getProduct(member.productId, any()) }
            coVerifyOnce { nullvsExternalIdService.findMemberExternalId(member.id) }
        }
    }

    @Test
    fun `#should return not eligible when ALICE member for not available MedicalSpecialty`() = runBlocking {
        val expected = CheckSpecialityEligibilityResponse(
            eligible = false,
            cpf = nationalId,
            cbo = cbo,
            socialName = person.fullSocialName,
            fullName = person.fullRegisterName,
        )

        coEvery { personService.findByNationalId(nationalId) } returns person.success()
        coEvery { memberService.findActiveMembership(person.id) } returns member.copy(brand = Brand.ALICE).success()
        coEvery { medicalSpecialtyService.getByCBOCode(cbo) } returns medicalSpecialty.copy(id = RangeUUID.generate()).success()

        val body = CheckSpecialityEligibilityRequest(nationalId, cbo)

        authenticated(idToken) {
            post("/checkSpecialistEligibility", body = body) { response ->
                assertThat(response).isOKWithData(expected)
            }

            coVerifyOnce { personService.findByNationalId(nationalId) }
            coVerifyOnce { medicalSpecialtyService.getByCBOCode(cbo) }
            coVerifyOnce { memberService.findActiveMembership(person.id) }
            coVerifyNone { externalReferralService.getActiveByPersonId(any(), any()) }
            coVerifyNone { productService.getProduct(any(), any()) }
            coVerifyNone { nullvsExternalIdService.findMemberExternalId(any()) }
        }
    }

    @Test
    fun `#should return not eligible when ALICE member does not have referral`() = runBlocking {
        val dueDate = LocalDate.now().plusDays(1)
        val expected = CheckSpecialityEligibilityResponse(
            eligible = false,
            cpf = nationalId,
            cbo = cbo,
            fullName = person.fullRegisterName,
            socialName = person.fullSocialName
        )

        coEvery { personService.findByNationalId(nationalId) } returns person.success()
        coEvery { memberService.findActiveMembership(person.id) } returns member.copy(brand = Brand.ALICE).success()
        coEvery { medicalSpecialtyService.getByCBOCode(cbo) } returns medicalSpecialty.success()
        coEvery {
            healthPlanTaskService.getActiveReferralsByPersonAndSpecialtyIdAndDueDate(
                person.id,
                medicalSpecialty.id,
                dueDate
            )
        } returns emptyList<HealthPlanTask>().success()

        val body = CheckSpecialityEligibilityRequest(nationalId, cbo)

        withFeatureFlag(FeatureNamespace.EXEC_INDICATOR, "alice_specialties_available_for_external_eligibility", listOf(medicalSpecialty.id.toString())) {
            authenticated(idToken) {
                post("/checkSpecialistEligibility", body = body) { response ->
                    assertThat(response).isOKWithData(expected)
                }

                coVerifyOnce { personService.findByNationalId(nationalId) }
                coVerifyOnce { memberService.findActiveMembership(person.id) }
                coVerifyOnce { medicalSpecialtyService.getByCBOCode(cbo) }
                coVerifyOnce {
                    healthPlanTaskService.getActiveReferralsByPersonAndSpecialtyIdAndDueDate(
                        person.id,
                        medicalSpecialty.id,
                        dueDate
                    )
                }
                coVerifyNone { externalReferralService.getActiveByPersonId(any(), any()) }
                coVerifyNone { productService.getProduct(any(), any()) }
                coVerifyNone { nullvsExternalIdService.findMemberExternalId(any()) }
            }
        }
    }

    @Test
    fun `#should return eligible when ALICE member for available MedicalSpecialty`() = runBlocking {
        val staffId = RangeUUID.generate()
        val dueDate = LocalDate.now().plusDays(1)
        val staff = TestModelFactory.buildStaff(id = staffId, type = StaffType.PITAYA)
        val healthProfessional = TestModelFactory.buildHealthProfessional(id = staffId, staff = staff)
        val referral = TestModelFactory.buildHealthPlanTaskReferral(releasedByStaffId = staffId, dueDate = dueDate)
        val expected = CheckSpecialityEligibilityResponse(
            eligible = true,
            cpf = nationalId,
            cbo = cbo,
            fullName = person.fullRegisterName,
            socialName = person.fullSocialName,
            dob = person.dateOfBirth,
            gender = person.gender,
            eligibilityDate = referral.releasedAt,
            eligibilityExpiresAt = referral.dueDate?.atEndOfTheDay(),
            requesterName = healthProfessional.staff?.fullName,
            requesterCouncilNumber = healthProfessional.councilSignature,
            healthPlanName = product.title,
            accountNumber = "aabbccdd",
        )

        coEvery { personService.findByNationalId(nationalId) } returns person.success()
        coEvery { memberService.findActiveMembership(person.id) } returns member.copy(brand = Brand.ALICE).success()
        coEvery { medicalSpecialtyService.getByCBOCode(cbo) } returns medicalSpecialty.success()
        coEvery {
            healthPlanTaskService.getActiveReferralsByPersonAndSpecialtyIdAndDueDate(
                person.id,
                medicalSpecialty.id,
                dueDate
            )
        } returns listOf(referral).success()
        coEvery { healthProfessionalService.findByStaffId(staffId) } returns healthProfessional.success()
        coEvery { productService.getProduct(member.productId, any()) } returns product.success()
        coEvery { nullvsExternalIdService.findMemberExternalId(member.id) } returns "aabbccdd".success()

        val body = CheckSpecialityEligibilityRequest(nationalId, cbo)

        withFeatureFlag(FeatureNamespace.EXEC_INDICATOR, "alice_specialties_available_for_external_eligibility", listOf(medicalSpecialty.id.toString())) {
            authenticated(idToken) {
                post("/checkSpecialistEligibility", body = body) { response ->
                    assertThat(response).isOKWithData(expected)
                }

                coVerifyOnce { personService.findByNationalId(nationalId) }
                coVerifyOnce { memberService.findActiveMembership(person.id) }
                coVerifyOnce { medicalSpecialtyService.getByCBOCode(cbo) }
                coVerifyOnce {
                    healthPlanTaskService.getActiveReferralsByPersonAndSpecialtyIdAndDueDate(
                        person.id,
                        medicalSpecialty.id,
                        dueDate
                    )
                }
                coVerifyOnce { healthProfessionalService.findByStaffId(staffId) }
                coVerifyNone { externalReferralService.getActiveByPersonId(any(), any()) }
                coVerifyOnce { productService.getProduct(member.productId, any()) }
                coVerifyOnce { nullvsExternalIdService.findMemberExternalId(member.id) }
            }
        }
    }

    @Test
    fun `#should return eligible when DUQUESA and getting from health plan`() = runBlocking {
        withFeatureFlag(FeatureNamespace.EXEC_INDICATOR, "duqueita_use_health_plan_task_referral", true) {
            val staffId = RangeUUID.generate()
            val dueDate = LocalDate.now().plusDays(1)
            val staff = TestModelFactory.buildStaff(id = staffId, type = StaffType.PITAYA)
            val healthProfessional = TestModelFactory.buildHealthProfessional(id = staffId, staff = staff)
            val referral = TestModelFactory.buildHealthPlanTaskReferral(releasedByStaffId = staffId, dueDate = dueDate)
            val expected = CheckSpecialityEligibilityResponse(
                eligible = true,
                cpf = nationalId,
                cbo = cbo,
                fullName = person.fullRegisterName,
                socialName = person.fullSocialName,
                dob = person.dateOfBirth,
                gender = person.gender,
                eligibilityDate = referral.releasedAt,
                eligibilityExpiresAt = referral.dueDate?.atEndOfTheDay(),
                requesterName = healthProfessional.staff?.fullName,
                requesterCouncilNumber = healthProfessional.councilSignature,
                healthPlanName = product.title,
                accountNumber = "aabbccdd",
            )

            coEvery { personService.findByNationalId(nationalId) } returns person.success()
            coEvery { memberService.findActiveMembership(person.id) } returns member.copy(brand = Brand.DUQUESA)
                .success()
            coEvery { medicalSpecialtyService.getByCBOCode(cbo) } returns medicalSpecialty.success()
            coEvery {
                healthPlanTaskService.getActiveReferralsByPersonAndSpecialtyIdAndDueDate(
                    person.id,
                    medicalSpecialty.id,
                    dueDate
                )
            } returns listOf(referral).success()
            coEvery { healthProfessionalService.findByStaffId(staffId) } returns healthProfessional.success()
            coEvery { productService.getProduct(member.productId, any()) } returns product.success()
            coEvery { nullvsExternalIdService.findMemberExternalId(member.id) } returns "aabbccdd".success()

            val body = CheckSpecialityEligibilityRequest(nationalId, cbo)

            withFeatureFlag(
                FeatureNamespace.EXEC_INDICATOR,
                "alice_specialties_available_for_external_eligibility",
                listOf(medicalSpecialty.id.toString())
            ) {
                authenticated(idToken) {
                    post("/checkSpecialistEligibility", body = body) { response ->
                        assertThat(response).isOKWithData(expected)
                    }

                    coVerifyOnce { personService.findByNationalId(nationalId) }
                    coVerifyOnce { memberService.findActiveMembership(person.id) }
                    coVerifyOnce { medicalSpecialtyService.getByCBOCode(cbo) }
                    coVerifyOnce {
                        healthPlanTaskService.getActiveReferralsByPersonAndSpecialtyIdAndDueDate(
                            person.id,
                            medicalSpecialty.id,
                            dueDate
                        )
                    }
                    coVerifyOnce { healthProfessionalService.findByStaffId(staffId) }
                    coVerifyNone { externalReferralService.getActiveByPersonId(any(), any()) }
                    coVerifyOnce { productService.getProduct(member.productId, any()) }
                    coVerifyOnce { nullvsExternalIdService.findMemberExternalId(member.id) }
                }
            }
        }
    }

    @Test
    fun `#should return not eligible when member is neither ALICE or DUQUESA`() = runBlocking {
        val expected = CheckSpecialityEligibilityResponse(
            eligible = false,
            cpf = nationalId,
            cbo = cbo,
            socialName = person.fullSocialName,
            fullName = person.fullRegisterName,
        )

        coEvery { personService.findByNationalId(nationalId) } returns person.success()
        coEvery { memberService.findActiveMembership(person.id) } returns member.copy(brand = Brand.ALICE_DUQUESA).success()
        coEvery { medicalSpecialtyService.getByCBOCode(cbo) } returns medicalSpecialty.success()

        val body = CheckSpecialityEligibilityRequest(nationalId, cbo)

        authenticated(idToken) {
            post("/checkSpecialistEligibility", body = body) { response ->
                assertThat(response).isOKWithData(expected)
            }

            coVerifyOnce { personService.findByNationalId(nationalId) }
            coVerifyOnce { medicalSpecialtyService.getByCBOCode(cbo) }
            coVerifyOnce { memberService.findActiveMembership(person.id) }
            coVerifyNone { externalReferralService.getActiveByPersonId(any(), any()) }
            coVerifyNone { productService.getProduct(any(), any()) }
            coVerifyNone { nullvsExternalIdService.findMemberExternalId(any()) }
        }
    }
}
