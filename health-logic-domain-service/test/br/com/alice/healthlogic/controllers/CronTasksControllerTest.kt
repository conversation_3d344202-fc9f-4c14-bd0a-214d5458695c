package br.com.alice.healthlogic.controllers

import br.com.alice.common.core.extensions.atEndOfTheDay
import br.com.alice.common.data.dsl.matchers.ResponseAssert.Companion.assertThat
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.common.kafka.interfaces.KafkaProducerService
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.OutcomeRequestScheduling
import br.com.alice.healthlogic.client.OutcomeRequestSchedulingService
import br.com.alice.healthlogic.event.OutcomeReqSchedulingToProcessEvent
import br.com.alice.healthlogic.module
import com.github.kittinunf.result.success
import io.ktor.server.application.Application
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.confirmVerified
import io.mockk.mockk
import io.mockk.mockkStatic
import org.koin.core.context.loadKoinModules
import java.time.LocalDateTime
import kotlin.test.AfterTest
import kotlin.test.BeforeTest
import kotlin.test.Test

class CronTasksControllerTest : RoutesTestHelper() {
    private val outcomeRequestSchedulingService: OutcomeRequestSchedulingService = mockk()
    private val kafkaProducer: KafkaProducerService = mockk()
    private val controller = CronTasksController(outcomeRequestSchedulingService, kafkaProducer)
    private val mockDate = LocalDateTime.of(2021, 12, 12, 12, 0)


    override val setupFunction: Application.() -> Unit = { module(dependencyInjectionModules = listOf(module)) }
    override val moduleFunction: Application.() -> Unit = { loadKoinModules(module) }

    @BeforeTest
    override fun setup() {
        super.setup()

        module.single { controller }

        mockkStatic(LocalDateTime::class)
        coEvery { LocalDateTime.now() } returns mockDate
    }

    @AfterTest
    fun confirm() =
        confirmVerified(outcomeRequestSchedulingService, kafkaProducer)

    @Test
    fun `#processDailyOutcomeRequestScheduling emits events for each schedule`() {
        val outcomeReq1 = TestModelFactory.buildOutcomeRequestScheduling()
        val outcomeReq2 = TestModelFactory.buildOutcomeRequestScheduling()

        val expected = listOf(outcomeReq1, outcomeReq2)

        coEvery {
            outcomeRequestSchedulingService.findPendingScheduledUntil(mockDate.atEndOfTheDay())
        } returns expected.success()

        coEvery {
            kafkaProducer.produce(
                match { it: OutcomeReqSchedulingToProcessEvent ->
                    it.payload.schedulingId == outcomeReq1.id
                }
            )
        } returns mockk()

        coEvery {
            kafkaProducer.produce(
                match { it: OutcomeReqSchedulingToProcessEvent ->
                    it.payload.schedulingId == outcomeReq2.id
                }
            )
        } returns mockk()

        internalAuthentication {
            postAsPlainText("/recurring_subscribers/process_daily_outcome_request_scheduling") { response ->
                assertThat(response).isOK()
            }
        }

        coVerifyOnce { outcomeRequestSchedulingService.findPendingScheduledUntil(any()) }
        coVerify(exactly = 2) { kafkaProducer.produce(any()) }
    }

    @Test
    fun `#processDailyOutcomeRequestScheduling should not emit anything with no scheduled requests`() {

        coEvery {
            outcomeRequestSchedulingService.findPendingScheduledUntil(mockDate.atEndOfTheDay())
        } returns emptyList<OutcomeRequestScheduling>().success()

        internalAuthentication {
            postAsPlainText("/recurring_subscribers/process_daily_outcome_request_scheduling") { response ->
                assertThat(response).isOK()
            }
        }

        coVerifyOnce { outcomeRequestSchedulingService.findPendingScheduledUntil(any()) }
    }
}
