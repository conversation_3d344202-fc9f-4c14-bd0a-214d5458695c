package app.file_vault_service_test

import rego.v1

import data.app.file_vault_service

test_unauth_view_file_vault_allowed if {
    {1,2,3,4,5,6,7,8} == file_vault_service.allow with input as {
        "cases": [{
            "index": 1,
            "action": "view",
            "subject": {
                "opaType": "Unauthenticated",
                "key": "person-id"
            },
            "resource": {
                "opaType": "FileVault",
                "domain": "ehr",
                "namespace": "signed_prescription",
                "personId": "person-id"
            }
        },
        {
            "index": 2,
            "action": "view",
            "subject": {
                "opaType": "Unauthenticated",
                "key": "person-id"
            },
            "resource": {
                "opaType": "FileVault",
                "domain": "ehr",
                "namespace": "signed_excuse_notes",
                "personId": "person-id"
            }
        },
        {
            "index": 3,
            "action": "view",
            "subject": {
                "opaType": "Unauthenticated",
                "key": "appointment-domain-service"
            },
            "resource": {
                "opaType": "FileVault",
                "domain": "ehr",
                "namespace": "signed_excuse_notes"
            }
        },
        {
            "index": 4,
            "action": "create",
            "subject": {
                "opaType": "Unauthenticated",
                "key": "appointment-domain-service"
            },
            "resource": {
                "opaType": "FileVault",
                "domain": "ehr",
                "namespace": "signed_excuse_notes"
            }
        },
        {
            "index": 5,
            "action": "view",
            "subject": {
                "opaType": "Unauthenticated",
                "key": "health-plan-domain-service"
            },
            "resource": {
                "opaType": "FileVault",
                "domain": "ehr",
                "namespace": "test_request"
            }
        },
        {
            "index": 6,
            "action": "view",
            "subject": {
                "opaType": "Unauthenticated",
                "key": "health-plan-domain-service"
            },
            "resource": {
                "opaType": "FileVault",
                "domain": "ehr",
                "namespace": "signed_test_request"
            }
        },
        {
            "index": 7,
            "action": "view",
            "subject": {
                "opaType": "Unauthenticated",
                "key": "health-plan-domain-service"
            },
            "resource": {
                "opaType": "FileVault",
                "domain": "ehr",
                "namespace": "signed_prescription"
            }
        },
        {
            "index": 8,
            "action": "view",
            "subject": {
                "opaType": "Unauthenticated",
                "key": "person-id"
            },
            "resource": {
                "opaType": "FileVault",
                "domain": "ehr",
                "namespace": "signed_referral",
                "personId": "person-id"
            }
        }]
    }
}

test_email_domain_view_file_vault_allowed if {
    {1} == file_vault_service.allow with input as {
        "cases": [{
            "index": 1,
            "action": "view",
            "subject": {
                "opaType": "EmailDomain"
            },
            "resource": {
                "opaType": "FileVault",
                "namespace": "member-photo"
            }
        }]
    }
}

test_HP_naveg_CRU_file_vault_allowed if {
    {1,2,3,4} == file_vault_service.allow with input as {
        "cases": [{
            "index": 1,
            "action": "create",
            "subject": {
                "opaType": "StaffModel",
                "role": "NAVIGATOR"
            },
            "resource": {
                "opaType": "FileVault"
            }
        },
        {
            "index": 2,
            "action": "create",
            "subject": {
                "opaType": "StaffModel",
                "role": "HEALTH_PROFESSIONAL"
            },
            "resource": {
                "opaType": "FileVault"
            }
        },
        {
            "index": 3,
            "action": "create",
            "subject": {
                "opaType": "StaffModel",
                "role": "CHIEF_NAVIGATOR" #test NAVIGATOR inheritance
            },
            "resource": {
                "opaType": "FileVault"
            }
        },
        {
            "index": 4,
            "action": "create",
            "subject": {
                "opaType": "StaffModel",
                "role": "CHIEF_PHYSICIAN" #test HEALTH_PROFESSIONAL inheritance
            },
            "resource": {
                "opaType": "FileVault"
            }
        }
        ]
    }
}

test_person_subject_CRUD_file_vault_allowed if {
    {1,2,3,4} == file_vault_service.allow with input as {
        "cases": [{
            "index": 1,
            "action": "create",
            "subject": {
                "opaType": "PersonSubject",
                "id": "person-id"
            },
            "resource": {
                "opaType": "FileVault",
                "personId": "person-id"
            }
        },
        {
            "index": 2,
            "action": "create",
            "subject": {
                "opaType": "PersonSubject",
                "id": "another-id",
                "dependentPersons": ["person-id","another-id"]
            },
            "resource": {
                "opaType": "FileVault",
                "personId": "person-id"
            }
        },
        {
            "index": 3,
            "action": "delete",
            "subject": {
                "opaType": "PersonSubject",
                "id": "person-id"
            },
            "resource": {
                "opaType": "FileVault",
                "personId": "person-id"
            }
        },
        {
            "index": 4,
            "action": "delete",
            "subject": {
                "opaType": "PersonSubject",
                "id": "another-id",
                "dependentPersons": ["person-id","another-id"]
            },
            "resource": {
                "opaType": "FileVault",
                "personId": "person-id"
            }
        }
        ]
    }
}

test_prod_tech_CRU_file_vault_allowed if {
    {1,2,3,4} == file_vault_service.allow with input as {
        "cases": [{
            "index": 1,
            "action": "view",
            "subject": {
                "opaType": "StaffModel",
                "role": "PRODUCT_TECH"
            },
            "resource": {
                "opaType": "FileVault",
                "namespace": "portability"
            }
        },
        {
            "index": 2,
            "action": "view",
            "subject": {
                "opaType": "StaffModel",
                "role": "PRODUCT_TECH"
            },
            "resource": {
                "opaType": "FileVault",
                "domain": "member",
                "namespace": "contract"
            }
        },
        {
            "index": 3,
            "action": "view",
            "subject": {
                "opaType": "StaffModel",
                "role": "PRODUCT_TECH"
            },
            "resource": {
                "opaType": "FileVault",
                "domain": "member",
                "namespace": "term"
            }
        },
        {
            "index": 4,
            "action": "view",
            "subject": {
                "opaType": "StaffModel",
                "role": "PRODUCT_TECH_HEALTH"
            },
            "resource": {
                "opaType": "FileVault",
                "namespace": "portability"
            }
        },
        {
            "index": 5,
            "action": "view",
            "subject": {
                "opaType": "StaffModel" #no role
            },
            "resource": {
                "opaType": "FileVault",
                "namespace": "portability"
            }
        }
        ]
    }
}

test_member_ops_view_file_vault_allowed if {
    {1} == file_vault_service.allow with input as {
        "cases": [{
            "index": 1,
            "action": "view",
            "subject": {
                "opaType": "StaffModel",
                "role": "MEMBER_OPS"
            },
            "resource": {
                "opaType": "FileVault",
                "namespace": "portability"
            }
        }]
    }
}

test_ops_view_file_vault_allowed if {
    {1,2,3} == file_vault_service.allow with input as {
        "cases": [{
            "index": 1,
            "action": "view",
            "subject": {
                "opaType": "StaffModel",
                "role": "OPS"
            },
            "resource": {
                "opaType": "FileVault",
                "namespace": "portability"
            }
        },
        {
            "index": 2,
            "action": "view",
            "subject": {
                "opaType": "StaffModel",
                "role": "NAVIGATOR_OPS"
            },
            "resource": {
                "opaType": "FileVault",
                "domain": "member",
                "namespace": "documents"
            }
        },
        {
            "index": 3,
            "action": "view",
            "subject": {
                "opaType": "StaffModel",
                "role": "HEALTH_OPS_LEAD"
            },
            "resource": {
                "opaType": "FileVault",
                "domain": "member",
                "namespace": "contract"
            }
        }
        ]
    }
}

test_HCS_create_file_vault_allowed if {
    {1} == file_vault_service.allow with input as {
        "cases": [{
            "index": 1,
            "action": "create",
            "subject": {
                "opaType": "HealthCommunitySpecialistModel"
            },
            "resource": {
                "opaType": "FileVault"
            }
        }]
    }
}

test_HS_create_file_vault_allowed if {
    {1} == file_vault_service.allow with input as {
        "cases": [{
            "index": 1,
            "action": "create",
            "subject": {
                "opaType": "StaffSubject",
                "role": "COMMUNITY"
            },
            "resource": {
                "opaType": "FileVault"
            }
        }]
    }
}

test_MED_EX_HOPS_view_file_vault_allowed if {
    {1,2,3} == file_vault_service.allow with input as {
        "cases": [{
            "index": 1,
            "action": "view",
            "subject": {
                "opaType": "StaffModel",
                "role": "MED_EX"
            },
            "resource": {
                "opaType": "FileVault",
                "domain": "ehr"
            }
        },
        {
            "index": 2,
            "action": "view",
            "subject": {
                "opaType": "StaffModel",
                "role": "QUALITY_NURSE" #test inheritance
            },
            "resource": {
                "opaType": "FileVault",
                "namespace": "member-photo"
            }
        },
        {
            "index": 3,
            "action": "view",
            "subject": {
                "opaType": "StaffModel",
                "role": "HEALTH_OPS_LEAD"
            },
            "resource": {
                "opaType": "FileVault",
                "namespace": "member-photo"
            }
        }
        ]
    }
}

test_B2B_and_OPS_CRU_file_vault_allowed if {
    {1,2,3,4} == file_vault_service.allow with input as {
        "cases": [{
            "index": 1,
            "action": "create",
            "subject": {
                "opaType": "StaffModel",
                "role": "OPS"
            },
            "resource": {
                "opaType": "FileVault",
                "domain": "member",
                "namespace": "contract"
            }
        },
        {
            "index": 2,
            "action": "create",
            "subject": {
                "opaType": "StaffModel",
                "role": "B2B_OPS"
            },
            "resource": {
                "opaType": "FileVault",
                "domain": "member",
                "namespace": "contract"
            }
        },
        {
            "index": 3,
            "action": "create",
            "subject": {
                "opaType": "StaffModel",
                "role": "OPS"
            },
            "resource": {
                "opaType": "FileVault",
                "domain": "member",
                "namespace": "term"
            }
        },
        {
            "index": 4,
            "action": "create",
            "subject": {
                "opaType": "StaffModel",
                "role": "B2B_OPS"
            },
            "resource": {
                "opaType": "FileVault",
                "domain": "member",
                "namespace": "term"
            }
        }
        ]
    }
}

test_chief_naveg_view_file_vault_allowed if {
    {1,2,3} == file_vault_service.allow with input as {
        "cases": [{
            "index": 1,
            "action": "view",
            "subject": {
                "opaType": "StaffModel",
                "role": "CHIEF_NAVIGATOR"
            },
            "resource": {
                "opaType": "FileVault",
                "domain": "refund"
            }
        },
        {
            "index": 2,
            "action": "view",
            "subject": {
                "opaType": "StaffModel",
                "role": "CHIEF_NAVIGATOR_OPS" #test inheritance
            },
            "resource": {
                "opaType": "FileVault",
                "namespace": "refund"
            }
        },
        {
            "index": 3,
            "action": "view",
            "subject": {
                "opaType": "StaffModel",
                "role": "CHIEF_DIGITAL_CARE_NURSE" #test inheritance
            },
            "resource": {
                "opaType": "FileVault",
                "namespace": "refund"
            }
        }
        ]
    }
}

test_risk_nurse_CRU_file_vault_allowed if {
    {1,2,3} == file_vault_service.allow with input as {
        "cases": [{
            "index": 1,
            "action": "create",
            "subject": {
                "opaType": "StaffModel",
                "role": "RISK_NURSE"
            },
            "resource": {
                "opaType": "FileVault",
                "domain": "member",
                "namespace": "term"
            }
        },
        {
            "index": 2,
            "action": "create",
            "subject": {
                "opaType": "StaffModel",
                "role": "CHIEF_RISK" #test inheritance
            },
            "resource": {
                "opaType": "FileVault",
                "domain": "member",
                "namespace": "term"
            }
        },
        {
            "index": 3,
            "action": "create",
            "subject": {
                "opaType": "StaffModel",
                "role": "HEALTH_DECLARATION_NURSE" #test inheritance
            },
            "resource": {
                "opaType": "FileVault",
                "domain": "member",
                "namespace": "term"
            }
        }
        ]
    }
}

test_company_staff_CRUD_file_vault_allowed if {
    {1,2,3,4} == file_vault_service.allow with input as {
        "cases": [{
            "index": 1,
            "action": "create",
            "subject": {
                "opaType": "CompanyStaff",
                "role": "MAIN_COMPANY_STAFF"
            },
            "resource": {
                "opaType": "FileVault",
                "domain": "business",
                "namespace": "dependent_files"
            }
        },
        {
            "index": 2,
            "action": "create",
            "subject": {
                "opaType": "CompanyStaff",
                "role": "LEGAL_REPRESENTATIVE"
            },
            "resource": {
                "opaType": "FileVault",
                "domain": "business",
                "namespace": "dependent_files"
            }
        },
        {
            "index": 3,
            "action": "delete",
            "subject": {
                "opaType": "CompanyStaff",
                "role": "MAIN_COMPANY_STAFF"
            },
            "resource": {
                "opaType": "FileVault",
                "domain": "business",
                "namespace": "dependent_files"
            }
        },
        {
            "index": 4,
            "action": "delete",
            "subject": {
                "opaType": "CompanyStaff",
                "role": "LEGAL_REPRESENTATIVE"
            },
            "resource": {
                "opaType": "FileVault",
                "domain": "business",
                "namespace": "dependent_files"
            }
        }
        ]
    }
}

test_chief_risk_file_vault_allowed if {
    {1,2,3,4,5,6} == file_vault_service.allow with input as {
        "cases": [{
            "index": 1,
            "action": "create",
            "subject": {
                "opaType": "StaffModel",
                "role": "CHIEF_RISK"
            },
            "resource": {
                "opaType": "FileVault",
                "domain": "ehr"
            }
        },
        {
            "index": 2,
            "action": "create",
            "subject": {
                "opaType": "StaffModel",
                "role": "CHIEF_RISK"
            },
            "resource": {
                "opaType": "FileVault",
                "namespace": "portability"
            }
        },
        {
            "index": 3,
            "action": "view",
            "subject": {
                "opaType": "StaffModel",
                "role": "CHIEF_RISK"
            },
            "resource": {
                "opaType": "FileVault",
                "domain": "member",
                "namespace": "documents"
            }
        },
        {
            "index": 4,
            "action": "create",
            "subject": {
                "opaType": "StaffModel",
                "role": "CHIEF_RISK"
            },
            "resource": {
                "opaType": "FileVault",
                "domain": "member",
                "namespace": "contract"
            }
        },
        {
            "index": 5,
            "action": "view",
            "subject": {
                "opaType": "StaffModel",
                "role": "CHIEF_RISK"
            },
            "resource": {
                "opaType": "FileVault",
                "namespace": "member-photo"
            }
        },
        {
            "index": 6,
            "action": "view",
            "subject": {
                "opaType": "StaffModel",
                "role": "CHIEF_RISK"
            },
            "resource": {
                "opaType": "FileVault",
                "namespace": "personHealthDocument"
            }
        }
        ]
    }
}
