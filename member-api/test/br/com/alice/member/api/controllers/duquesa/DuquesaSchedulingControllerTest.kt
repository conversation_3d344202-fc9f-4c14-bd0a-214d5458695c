package br.com.alice.member.api.controllers.duquesa

import br.com.alice.app.content.model.RemoteActionMethod
import br.com.alice.common.ErrorResponse
import br.com.alice.common.core.extensions.atEndOfTheDay
import br.com.alice.common.data.dsl.matchers.ResponseAssert
import br.com.alice.common.helpers.bodyAsJson
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.ScheduleAppointmentType
import br.com.alice.data.layer.models.ScheduleProvider
import br.com.alice.data.layer.models.ScheduleType
import br.com.alice.duquesa.client.ConfirmSlotCia
import br.com.alice.duquesa.client.ExternalSchedulingService
import br.com.alice.duquesa.client.FleuryOperationRequest
import br.com.alice.duquesa.client.SlotsRequest
import br.com.alice.duquesa.exceptions.EinsteinDuplicateAppointmentException
import br.com.alice.duquesa.models.Establishment
import br.com.alice.duquesa.models.EstablishmentSlot
import br.com.alice.duquesa.models.EstablishmentsTransport
import br.com.alice.duquesa.models.ProductSlot
import br.com.alice.duquesa.models.ProfessionalSlot
import br.com.alice.duquesa.models.Slot
import br.com.alice.duquesa.models.SlotsTransport
import br.com.alice.member.api.RoutesTestHelper
import br.com.alice.member.api.ServiceConfig
import br.com.alice.member.api.controllers.duquesa.converters.FiltersConvert
import br.com.alice.member.api.controllers.duquesa.converters.getHours
import br.com.alice.member.api.controllers.duquesa.converters.toFullDateAtDuquesaApp
import br.com.alice.member.api.controllers.duquesa.models.AvailabilityResponse
import br.com.alice.member.api.controllers.duquesa.models.AvailabilityWithSlots
import br.com.alice.member.api.controllers.duquesa.models.ContentGroup
import br.com.alice.member.api.controllers.duquesa.models.EstablishmentResponse
import br.com.alice.member.api.controllers.duquesa.models.EstablishmentsResponse
import br.com.alice.member.api.controllers.duquesa.models.NextPage
import br.com.alice.member.api.controllers.duquesa.models.ProfessionalResponse
import br.com.alice.member.api.controllers.duquesa.models.SlotResponse
import br.com.alice.member.api.controllers.duquesa.models.SlotsByProfessional
import br.com.alice.member.api.controllers.duquesa.models.SlotsResponse
import br.com.alice.member.api.models.MobileRouting
import br.com.alice.member.api.models.appContent.Navigation
import br.com.alice.person.client.PersonService
import com.github.kittinunf.result.failure
import com.github.kittinunf.result.success
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.mockk
import io.mockk.mockkStatic
import org.assertj.core.api.Assertions
import java.time.LocalDate
import java.time.LocalDateTime
import kotlin.test.BeforeTest
import kotlin.test.Test


class DuquesaSchedulingControllerTest : RoutesTestHelper() {
    private val externalSchedulingService: ExternalSchedulingService = mockk()
    private val personService: PersonService = mockk()
    private val nowDate = LocalDateTime.of(2023, 11, 21, 19, 20, 21)

    @BeforeTest
    override fun setup() {
        super.setup()
        mockkStatic(LocalDateTime::class, LocalDate::class)
        coEvery { LocalDateTime.now() } returns nowDate
        coEvery { LocalDate.now() } returns nowDate.toLocalDate()
        this.module.single { DuquesaSchedulingController(externalSchedulingService, personService) }
    }

    private val person = TestModelFactory.buildPerson()
    private val token = person.id.toString()
    private val establishment = Establishment(
        id = "10",
        name = "Shop Metrô Tucuruvi",
        address = "Avenida Doutor Antônio Maria Laet 566 Shopping Metrô Tucuruvi - L1 - SL40A - São Paulo",
    )

    private val professionalSlot = ProfessionalSlot(
        id = "1",
        name = "joao"
    )
    private val establishmentSlot =
        EstablishmentSlot(establishment.id, "Shop", address = "location", mapLink = "googleMap.com.br")
    private val productSlot = ProductSlot("10", "consulta")
    private val plusDays = FiltersConvert.PLUS_DAYS
    private val availabilityUrl = "/duquesa/appointment_schedule/availability"
    private val availabilityEndpoint = ServiceConfig.url("/duquesa/appointment_schedule/availability")
    private val availabilityUrlProfessional = "/duquesa/appointment_schedule/availability/professional"
    private val availabilityEndpointProfessional = ServiceConfig.url("/duquesa/appointment_schedule/availability/professional")

    @Test
    fun `#getEstablishments should return establishment group cia`() {
        val expectedEstablishment = listOf(establishment).map { establishment ->
            EstablishmentResponse(
                id = establishment.id,
                name = establishment.name,
                address = establishment.address,
                navigation = Navigation(
                    method = RemoteActionMethod.GET,
                    mobileRoute = MobileRouting.DUQUESA_SCHEDULE_AVAILABILITY,
                    endpoint = ServiceConfig.url("/duquesa/appointment_schedule/availability?type=PRESENTIAL&appointmentType=DOCTOR_FAMILY&establishmentId=${establishment.id}")
                )
            )
        }
        val expectedContent = EstablishmentsResponse(
            groups = listOf(
                ContentGroup(
                    name = "Pelo aplicativo",
                    establishments = expectedEstablishment
                )
            )
        )
        coEvery { externalSchedulingService.getEstablishments(person.id, any()) } returns EstablishmentsTransport(
            listOf(establishment),
            ScheduleProvider.CIA
        ).success()

        authenticatedAs(token, toTestPerson(person)) {
            get("/duquesa/appointment_schedule/establishments?appointmentType=DOCTOR_FAMILY") { response ->
                ResponseAssert.assertThat(response).isSuccessfulJson()

                val content = getResponse<EstablishmentsResponse>(response)
                Assertions.assertThat(content).isEqualTo(expectedContent)
            }
        }
    }

    @Test
    fun `#getEstablishments should return badRequest when without appointmentType`() {
        authenticatedAs(token, toTestPerson(person)) {
            get("/duquesa/appointment_schedule/establishments") { response ->
                ResponseAssert.assertThat(response).isBadRequest()
                val error: ErrorResponse = response.bodyAsJson()
                Assertions.assertThat(error.code).isEqualTo("missing_type")
            }
        }
    }

    @Test
    fun `#getAvailability should return slots when with params cia`() {
        val startDate = LocalDate.now()
        val endDate = startDate.plusDays(plusDays)
        val slotRequest = SlotsRequest(
            startDate = nowDate,
            endDate = endDate.atEndOfTheDay(),
            productType = ScheduleType.PRESENTIAL,
            establishmentId = establishment.id,
            appointmentType = ScheduleAppointmentType.DOCTOR_FAMILY
        )
        val slot = Slot(
            id = "1",
            date = LocalDateTime.now(),
            professional = professionalSlot,
            establishment = establishmentSlot,
            products = listOf(productSlot),
            productType = ScheduleType.PRESENTIAL,
            appointmentType = ScheduleAppointmentType.DOCTOR_FAMILY
        )

        coEvery {
            externalSchedulingService.getSlotsAvailable(
                person.id,
                slotRequest
            )
        } returns SlotsTransport(listOf(slot), ScheduleProvider.CIA).success()

        val expectedResponse = SlotsResponse(
            listOf(
                AvailabilityResponse(
                    title = slot.date.toFullDateAtDuquesaApp(),
                    professionals = listOf(
                        ProfessionalResponse(
                            id = slot.professional!!.id,
                            name = slot.professional!!.name,
                            description = "a partir de ${slot.date.getHours()}",
                            navigation = Navigation(
                                method = RemoteActionMethod.GET,
                                mobileRoute = MobileRouting.DUQUESA_SCHEDULE_AVAILABILITY_PROFESSIONAL,
                                endpoint = "$availabilityEndpointProfessional/${slot.professional!!.id}?startDate=${
                                    startDate
                                }&endDate=${
                                    endDate
                                }&type=PRESENTIAL&appointmentType=DOCTOR_FAMILY&establishmentId=${establishment.id}"
                            )
                        )
                    )
                )
            ),
            next = NextPage(
                title = "Ver mais datas",
                navigation = Navigation(
                    method = RemoteActionMethod.GET,
                    mobileRoute = MobileRouting.DUQUESA_SCHEDULE_AVAILABILITY,
                    endpoint = "$availabilityEndpoint?startDate=${endDate.plusDays(1)}&endDate=${
                        endDate.plusDays(
                            plusDays
                        )
                    }&type=PRESENTIAL&appointmentType=DOCTOR_FAMILY&establishmentId=${establishment.id}"
                )


            )
        )
        authenticatedAs(token, toTestPerson(person)) {
            get("$availabilityUrl?startDate=$startDate&endDate=$endDate&type=PRESENTIAL&appointmentType=DOCTOR_FAMILY&establishmentId=${establishment.id}") { response ->
                ResponseAssert.assertThat(response).isSuccessfulJson()

                val content = getResponse<SlotsResponse>(response)
                Assertions.assertThat(content).isEqualTo(expectedResponse)
            }
        }
    }

    @Test
    fun `#getAvailability return original error when exception is from RFC`() {
        val startDate = LocalDate.now()
        val endDate = startDate.plusDays(plusDays)
        val slotRequest = SlotsRequest(
            startDate = nowDate,
            endDate = endDate.atEndOfTheDay(),
            productType = ScheduleType.PRESENTIAL,
            establishmentId = establishment.id,
            appointmentType = ScheduleAppointmentType.DOCTOR_FAMILY
        )

        coEvery {
            externalSchedulingService.getSlotsAvailable(
                person.id,
                slotRequest
            )
        } returns EinsteinDuplicateAppointmentException().failure()

        authenticatedAs(token, toTestPerson(person)) {
            get("$availabilityUrl?startDate=$startDate&endDate=$endDate&type=PRESENTIAL&appointmentType=DOCTOR_FAMILY&establishmentId=${establishment.id}") { response ->
                ResponseAssert.assertThat(response).isConflictWithErrorCode("concurrent_appointments", "Já existe um agendamento ativo")
            }
        }
    }

    @Test
    fun `#getAvailability should return slots when with params cia and isPregnant`() {
        val startDate = LocalDate.now()
        val endDate = startDate.plusDays(plusDays)
        val slotRequest = SlotsRequest(
            startDate = nowDate,
            endDate = endDate.atEndOfTheDay(),
            productType = ScheduleType.PRESENTIAL,
            establishmentId = establishment.id,
            appointmentType = ScheduleAppointmentType.DOCTOR_FAMILY,
            isPregnant = true,
        )
        val slot = Slot(
            id = "1",
            date = LocalDateTime.now(),
            professional = professionalSlot,
            establishment = establishmentSlot,
            products = listOf(productSlot),
            productType = ScheduleType.PRESENTIAL,
            appointmentType = ScheduleAppointmentType.DOCTOR_FAMILY
        )

        coEvery {
            externalSchedulingService.getSlotsAvailable(
                person.id,
                slotRequest
            )
        } returns SlotsTransport(listOf(slot), ScheduleProvider.CIA).success()

        val expectedResponse = SlotsResponse(
            listOf(
                AvailabilityResponse(
                    title = slot.date.toFullDateAtDuquesaApp(),
                    professionals = listOf(
                        ProfessionalResponse(
                            id = slot.professional!!.id,
                            name = slot.professional!!.name,
                            description = "a partir de ${slot.date.getHours()}",
                            navigation = Navigation(
                                method = RemoteActionMethod.GET,
                                mobileRoute = MobileRouting.DUQUESA_SCHEDULE_AVAILABILITY_PROFESSIONAL,
                                endpoint = "$availabilityEndpointProfessional/${slot.professional!!.id}?startDate=${
                                    startDate
                                }&endDate=${
                                    endDate
                                }&type=PRESENTIAL&appointmentType=DOCTOR_FAMILY&establishmentId=${establishment.id}&isPregnant=true"
                            )
                        )
                    )
                )
            ),
            next = NextPage(
                title = "Ver mais datas",
                navigation = Navigation(
                    method = RemoteActionMethod.GET,
                    mobileRoute = MobileRouting.DUQUESA_SCHEDULE_AVAILABILITY,
                    endpoint = "$availabilityEndpoint?startDate=${endDate.plusDays(1)}&endDate=${
                        endDate.plusDays(
                            plusDays
                        )
                    }&type=PRESENTIAL&appointmentType=DOCTOR_FAMILY&establishmentId=${establishment.id}&isPregnant=true"
                )


            )
        )
        authenticatedAs(token, toTestPerson(person)) {
            get("$availabilityUrl?startDate=$startDate&endDate=$endDate&type=PRESENTIAL&appointmentType=DOCTOR_FAMILY&establishmentId=${establishment.id}&isPregnant=true") { response ->
                ResponseAssert.assertThat(response).isSuccessfulJson()

                val content = getResponse<SlotsResponse>(response)
                Assertions.assertThat(content).isEqualTo(expectedResponse)
            }
        }
    }

    @Test
    fun `#getAvailability should return slots when without date params cia`() {
        val mockNow = LocalDateTime.now()
        coEvery { LocalDateTime.now() } returns mockNow
        coEvery { LocalDate.now() } returns mockNow.toLocalDate()

        val startDate = mockNow.toLocalDate()
        val endDate = mockNow.plusDays(plusDays).toLocalDate()

        val slotRequest = SlotsRequest(
            startDate = mockNow,
            endDate = mockNow.plusDays(plusDays).atEndOfTheDay(),
            productType = ScheduleType.PRESENTIAL,
            establishmentId = establishment.id,
            appointmentType = ScheduleAppointmentType.DOCTOR_FAMILY
        )
        val slot = Slot(
            id = "1",
            date = LocalDateTime.now(),
            professional = professionalSlot,
            establishment = establishmentSlot,
            products = listOf(productSlot),
            productType = ScheduleType.PRESENTIAL,
            appointmentType = ScheduleAppointmentType.DOCTOR_FAMILY
        )

        coEvery {
            externalSchedulingService.getSlotsAvailable(
                person.id,
                slotRequest
            )
        } returns SlotsTransport(listOf(slot), ScheduleProvider.CIA).success()

        val expectedResponse = SlotsResponse(
            listOf(
                AvailabilityResponse(
                    title = slot.date.toFullDateAtDuquesaApp(),
                    professionals = listOf(
                        ProfessionalResponse(
                            id = slot.professional!!.id,
                            name = slot.professional!!.name,
                            description = "a partir de ${slot.date.getHours()}",
                            navigation = Navigation(
                                method = RemoteActionMethod.GET,
                                mobileRoute = MobileRouting.DUQUESA_SCHEDULE_AVAILABILITY_PROFESSIONAL,
                                endpoint = "$availabilityEndpointProfessional/${slot.professional!!.id}?startDate=${
                                    startDate
                                }&endDate=${
                                    endDate
                                }&type=PRESENTIAL&appointmentType=DOCTOR_FAMILY&establishmentId=${establishment.id}"
                            )
                        )
                    )
                )
            ),
            next = NextPage(
                title = "Ver mais datas",
                navigation = Navigation(
                    method = RemoteActionMethod.GET,
                    mobileRoute = MobileRouting.DUQUESA_SCHEDULE_AVAILABILITY,
                    endpoint = "$availabilityEndpoint?startDate=${endDate.plusDays(1)}&endDate=${
                        endDate.plusDays(
                            plusDays
                        )
                    }&type=PRESENTIAL&appointmentType=DOCTOR_FAMILY&establishmentId=${establishment.id}"
                )


            )
        )
        authenticatedAs(token, toTestPerson(person)) {
            get("$availabilityUrl?type=PRESENTIAL&appointmentType=DOCTOR_FAMILY&establishmentId=${establishment.id}") { response ->
                ResponseAssert.assertThat(response).isSuccessfulJson()

                val content = getResponse<SlotsResponse>(response)
                Assertions.assertThat(content).isEqualTo(expectedResponse)
            }
        }

    }

    @Test
    fun `#getAvailability should return slots when with params and many slots cia`() {
        val startDate = nowDate.toLocalDate()
        val endDate = startDate.plusDays(plusDays)
        val slotRequest = SlotsRequest(
            startDate = nowDate,
            endDate = endDate.atEndOfTheDay(),
            productType = ScheduleType.PRESENTIAL,
            appointmentType = ScheduleAppointmentType.DOCTOR_FAMILY
        )
        val slot1 = Slot(
            id = "1",
            date = nowDate,
            professional = professionalSlot,
            establishment = establishmentSlot,
            products = listOf(productSlot),
            productType = ScheduleType.PRESENTIAL,
            appointmentType = ScheduleAppointmentType.DOCTOR_FAMILY

        )
        val slotWithEqualsProfessionalWithEqualsDate = Slot(
            id = "2",
            date = nowDate.plusMinutes(20),
            professional = professionalSlot,
            establishment = establishmentSlot,
            products = listOf(productSlot),
            productType = ScheduleType.PRESENTIAL,
            appointmentType = ScheduleAppointmentType.DOCTOR_FAMILY
        )
        val slotWithEqualsProfessionalWithDiffDate = Slot(
            id = "3",
            date = nowDate.plusDays(1),
            professional = professionalSlot,
            establishment = establishmentSlot,
            products = listOf(productSlot),
            productType = ScheduleType.PRESENTIAL,
            appointmentType = ScheduleAppointmentType.DOCTOR_FAMILY
        )

        val slot2 = Slot(
            id = "5",
            date = nowDate.plusDays(1),
            professional = ProfessionalSlot("2", "Mateus"),
            establishment = establishmentSlot,
            products = listOf(productSlot),
            productType = ScheduleType.PRESENTIAL,
            appointmentType = ScheduleAppointmentType.DOCTOR_FAMILY
        )

        val slot3 = Slot(
            id = "4",
            date = nowDate.plusDays(2),
            professional = ProfessionalSlot("2", "Joana"),
            establishment = establishmentSlot,
            products = listOf(productSlot),
            productType = ScheduleType.PRESENTIAL,
            appointmentType = ScheduleAppointmentType.DOCTOR_FAMILY
        )

        val expectedResponse = SlotsResponse(
            listOf(
                AvailabilityResponse(
                    title = slot1.date.toFullDateAtDuquesaApp(),
                    professionals = listOf(
                        ProfessionalResponse(
                            id = slot1.professional!!.id,
                            name = slot1.professional!!.name,
                            description = "a partir de ${slot1.date.getHours()}",
                            navigation = Navigation(
                                method = RemoteActionMethod.GET,
                                mobileRoute = MobileRouting.DUQUESA_SCHEDULE_AVAILABILITY_PROFESSIONAL,
                                endpoint = "$availabilityEndpointProfessional/${slot1.professional!!.id}?startDate=${
                                    startDate
                                }&endDate=${
                                    endDate
                                }&type=PRESENTIAL&appointmentType=DOCTOR_FAMILY"
                            )
                        )
                    )
                ), AvailabilityResponse(
                    title = slotWithEqualsProfessionalWithDiffDate.date.toFullDateAtDuquesaApp(),
                    professionals = listOf(
                        ProfessionalResponse(
                            id = slotWithEqualsProfessionalWithDiffDate.professional!!.id,
                            name = slotWithEqualsProfessionalWithDiffDate.professional!!.name,
                            description = "a partir de ${slotWithEqualsProfessionalWithDiffDate.date.getHours()}",
                            navigation = Navigation(
                                method = RemoteActionMethod.GET,
                                mobileRoute = MobileRouting.DUQUESA_SCHEDULE_AVAILABILITY_PROFESSIONAL,
                                endpoint = "$availabilityEndpointProfessional/${slotWithEqualsProfessionalWithDiffDate.professional!!.id}?startDate=${
                                    startDate
                                }&endDate=${
                                    endDate
                                }&type=PRESENTIAL&appointmentType=DOCTOR_FAMILY"
                            )
                        ), ProfessionalResponse(
                            id = slot2.professional!!.id,
                            name = slot2.professional!!.name,
                            description = "a partir de ${slot2.date.getHours()}",
                            navigation = Navigation(
                                method = RemoteActionMethod.GET,
                                mobileRoute = MobileRouting.DUQUESA_SCHEDULE_AVAILABILITY_PROFESSIONAL,
                                endpoint = "$availabilityEndpointProfessional/${slot2.professional!!.id}?startDate=${
                                    startDate
                                }&endDate=${
                                    endDate
                                }&type=PRESENTIAL&appointmentType=DOCTOR_FAMILY"
                            )
                        )
                    )
                ), AvailabilityResponse(
                    title = slot3.date.toFullDateAtDuquesaApp(),
                    professionals = listOf(
                        ProfessionalResponse(
                            id = slot3.professional!!.id,
                            name = slot3.professional!!.name,
                            description = "a partir de ${slot3.date.getHours()}",
                            navigation = Navigation(
                                method = RemoteActionMethod.GET,
                                mobileRoute = MobileRouting.DUQUESA_SCHEDULE_AVAILABILITY_PROFESSIONAL,
                                endpoint = "$availabilityEndpointProfessional/${slot3.professional!!.id}?startDate=${
                                    startDate
                                }&endDate=${
                                    endDate
                                }&type=PRESENTIAL&appointmentType=DOCTOR_FAMILY"
                            )
                        )
                    )
                )
            ),
            next = NextPage(
                title = "Ver mais datas",
                navigation = Navigation(
                    method = RemoteActionMethod.GET,
                    mobileRoute = MobileRouting.DUQUESA_SCHEDULE_AVAILABILITY,
                    endpoint = "$availabilityEndpoint?startDate=${endDate.plusDays(1)}&endDate=${
                        endDate.plusDays(
                            plusDays
                        )
                    }&type=PRESENTIAL&appointmentType=DOCTOR_FAMILY"
                )
            )
        )

        coEvery {
            externalSchedulingService.getSlotsAvailable(
                person.id,
                slotRequest
            )
        } returns SlotsTransport(
            listOf(
                slot3,
                slot1,
                slotWithEqualsProfessionalWithEqualsDate,
                slotWithEqualsProfessionalWithDiffDate,
                slot2
            ),
            ScheduleProvider.CIA
        ).success()

        authenticatedAs(token, toTestPerson(person)) {
            get("$availabilityUrl?startDate=$startDate&endDate=$endDate&type=PRESENTIAL&appointmentType=DOCTOR_FAMILY") { response ->
                ResponseAssert.assertThat(response).isSuccessfulJson()

                val content = getResponse<SlotsResponse>(response)
                Assertions.assertThat(content).isEqualTo(expectedResponse)
            }
        }
    }

    @Test
    fun `#getAvailability should return badRequest when without appointmentType`() {
        authenticatedAs(token, toTestPerson(person)) {
            get("$availabilityUrl?type=PRESENTIAL") { response ->
                ResponseAssert.assertThat(response).isBadRequest()
                val error: ErrorResponse = response.bodyAsJson()
                Assertions.assertThat(error.code).isEqualTo("missing_appointment_type")
            }
        }
    }

    @Test
    fun `#getAvailability should return badRequest when without type`() {
        authenticatedAs(token, toTestPerson(person)) {
            get("$availabilityUrl?appointmentType=DOCTOR_FAMILY") { response ->
                ResponseAssert.assertThat(response).isBadRequest()
                val error: ErrorResponse = response.bodyAsJson()
                Assertions.assertThat(error.code).isEqualTo("missing_type")
            }
        }
    }

    @Test
    fun `#getAvailability should return badRequest when startDate is past`() {
        val startDate = nowDate.toLocalDate().minusDays(1)
        val endDate = startDate.plusDays(plusDays)

        authenticatedAs(token, toTestPerson(person)) {
            get("$availabilityUrl?startDate=$startDate&endDate=$endDate&type=PRESENTIAL&appointmentType=DOCTOR_FAMILY") { response ->
                ResponseAssert.assertThat(response).isBadRequest()
                val error: ErrorResponse = response.bodyAsJson()
                Assertions.assertThat(error.code).isEqualTo("date_is_past")
            }
        }
    }

    @Test
    fun `#getAvailabilityByProfessional should return slots when with params and many slots`() {
        val availabilityUrlProfessional = "$availabilityUrlProfessional/${professionalSlot.id}"

        val startDate = nowDate.toLocalDate()
        val endDate = startDate.plusDays(plusDays)

        val slotRequest = SlotsRequest(
            startDate = nowDate,
            endDate = endDate.atEndOfTheDay(),
            productType = ScheduleType.PRESENTIAL,
            appointmentType = ScheduleAppointmentType.DOCTOR_FAMILY
        )
        val slot1 = Slot(
            id = "1",
            date = nowDate,
            professional = professionalSlot,
            establishment = establishmentSlot,
            products = listOf(productSlot),
            productType = ScheduleType.PRESENTIAL,
            appointmentType = ScheduleAppointmentType.DOCTOR_FAMILY
        )
        val slotWithEqualsProfessionalWithEqualsDate = Slot(
            id = "2",
            date = nowDate.plusMinutes(20),
            professional = professionalSlot,
            establishment = establishmentSlot,
            products = listOf(productSlot),
            productType = ScheduleType.PRESENTIAL,
            appointmentType = ScheduleAppointmentType.DOCTOR_FAMILY
        )
        val slotWithEqualsProfessionalWithDiffDate = Slot(
            id = "3",
            date = nowDate.plusDays(1),
            professional = professionalSlot,
            establishment = establishmentSlot,
            products = listOf(productSlot),
            productType = ScheduleType.PRESENTIAL,
            appointmentType = ScheduleAppointmentType.DOCTOR_FAMILY
        )


        val expectedResponse = SlotsByProfessional(
            professional = ProfessionalResponse(
                id = professionalSlot.id,
                name = professionalSlot.name,
                description = productSlot.name
            ),
            availability = listOf(
                AvailabilityWithSlots(
                    title = slot1.date.toFullDateAtDuquesaApp(),
                    slots = listOf(
                        SlotResponse(
                            id = slot1.id,
                            time = slot1.date.getHours(),
                            products = slot1.products,
                            navigation = Navigation(
                                method = RemoteActionMethod.GET,
                                endpoint =  ServiceConfig.url("/duquesa/appointment_schedule/slot/${slot1.id}?startDate=${
                                    startDate
                                }&endDate=${
                                    endDate
                                }&type=PRESENTIAL&appointmentType=DOCTOR_FAMILY"),
                                mobileRoute = MobileRouting.DUQUESA_SCHEDULE_CONFIRMATION
                            )
                        ),
                        SlotResponse(
                            id = slotWithEqualsProfessionalWithEqualsDate.id,
                            time = slotWithEqualsProfessionalWithEqualsDate.date.getHours(),
                            products = slotWithEqualsProfessionalWithEqualsDate.products,
                            navigation = Navigation(
                                method = RemoteActionMethod.GET,
                                endpoint =  ServiceConfig.url("/duquesa/appointment_schedule/slot/${slotWithEqualsProfessionalWithEqualsDate.id}?startDate=${
                                    startDate
                                }&endDate=${
                                    endDate
                                }&type=PRESENTIAL&appointmentType=DOCTOR_FAMILY"),
                                mobileRoute = MobileRouting.DUQUESA_SCHEDULE_CONFIRMATION
                            )

                        ),
                    )
                ), AvailabilityWithSlots(
                    title = slotWithEqualsProfessionalWithDiffDate.date.toFullDateAtDuquesaApp(),
                    slots = listOf(
                        SlotResponse(
                            id = slotWithEqualsProfessionalWithDiffDate.id,
                            time = slotWithEqualsProfessionalWithDiffDate.date.getHours(),
                            products = slotWithEqualsProfessionalWithDiffDate.products,
                            navigation = Navigation(
                                method = RemoteActionMethod.GET,
                                endpoint = ServiceConfig.url("/duquesa/appointment_schedule/slot/${slotWithEqualsProfessionalWithDiffDate.id}?startDate=${
                                    startDate
                                }&endDate=${
                                    endDate
                                }&type=PRESENTIAL&appointmentType=DOCTOR_FAMILY"),
                                mobileRoute = MobileRouting.DUQUESA_SCHEDULE_CONFIRMATION
                            )
                        )
                    )
                )
            ),
            next = NextPage(
                title = "Ver mais datas",
                navigation = Navigation(
                    method = RemoteActionMethod.GET,
                    mobileRoute = MobileRouting.DUQUESA_SCHEDULE_AVAILABILITY_PROFESSIONAL,
                    endpoint = "${ServiceConfig.url(availabilityUrlProfessional)}?startDate=${endDate.plusDays(1)}&endDate=${
                        endDate.plusDays(
                            plusDays
                        )
                    }&type=PRESENTIAL&appointmentType=DOCTOR_FAMILY"
                )
            )
        )

        coEvery {
            externalSchedulingService.getSlotsAvailableByProfessional(
                person.id,
                professionalSlot.id,
                slotRequest
            )
        } returns SlotsTransport(
            listOf(
                slot1,
                slotWithEqualsProfessionalWithEqualsDate,
                slotWithEqualsProfessionalWithDiffDate,
            ),
            ScheduleProvider.CIA
        ).success()

        authenticatedAs(token, toTestPerson(person)) {
            get("$availabilityUrlProfessional?startDate=$startDate&endDate=$endDate&type=PRESENTIAL&appointmentType=DOCTOR_FAMILY") { response ->
                ResponseAssert.assertThat(response).isSuccessfulJson()

                val content = getResponse<SlotsByProfessional>(response)
                Assertions.assertThat(content).isEqualTo(expectedResponse)
            }
        }
    }

    @Test
    fun `#getAvailabilityByProfessional should return badRequest when without type`() {
        val availabilityUrlProfessional = "$availabilityUrlProfessional/${professionalSlot.id}"
        authenticatedAs(token, toTestPerson(person)) {
            get("$availabilityUrlProfessional?appointmentType=DOCTOR_FAMILY") { response ->
                ResponseAssert.assertThat(response).isBadRequest()
                val error: ErrorResponse = response.bodyAsJson()
                Assertions.assertThat(error.code).isEqualTo("missing_type")
            }
        }
    }

    @Test
    fun `#getAvailabilityByProfessional should return badRequest when without appointment type`() {
        val availabilityUrlProfessional = "$availabilityUrlProfessional/${professionalSlot.id}"

        authenticatedAs(token, toTestPerson(person)) {
            get("$availabilityUrlProfessional?type=PRESENTIAL") { response ->
                ResponseAssert.assertThat(response).isBadRequest()
                val error: ErrorResponse = response.bodyAsJson()
                Assertions.assertThat(error.code).isEqualTo("missing_appointment_type")
            }
        }
    }

    @Test
    fun `#ciaCancelSlot should confirm slot`() {
        val request = ConfirmCiaSlot(
            "9100071001",
            slotId = "1235",
            productType = ScheduleType.REMOTE
        )
        coEvery {
            externalSchedulingService.confirmCiaSlot(
                ConfirmSlotCia(
                    personId = person.id,
                    slotId = request.slotId,
                    productId = request.productId,
                    productType = request.productType
                )
            )
        } returns request.slotId.toLong().success()

        authenticatedAs(token, toTestPerson(person)) {
            post("/duquesa/cia/appointment_schedule/confirm", request) { response ->
                ResponseAssert.assertThat(response).isSuccessfulJson()
                val content = getResponse<Long>(response)
                Assertions.assertThat(content).isEqualTo(1235)
            }
        }
        coVerify {
            externalSchedulingService.confirmCiaSlot(any())
        }
    }

    @Test
    fun `#cancelCiaSlot should cancel slot`() {
        val slotId = 9100071001L
        coEvery {
            externalSchedulingService.cancelCiaSlot(slotId, person.id)
        } returns true.success()

        authenticatedAs(token, toTestPerson(person)) {
            post("/duquesa/cia/appointment_schedule/cancel/$slotId") { response ->
                ResponseAssert.assertThat(response).isSuccessfulJson()
                val content = getResponse<Boolean>(response)
                Assertions.assertThat(content).isEqualTo(true)
            }
        }
        coVerify {
            externalSchedulingService.cancelCiaSlot(any(), person.id)
        }
    }

    @Test
    fun `#fleuryCancelSlot should cancel slot`() {
        val req = FleuryOperationRequest("9100071001")

        coEvery {
            externalSchedulingService.cancelFleurySlot(req.id, person.id)
        } returns true.success()

        authenticatedAs(token, toTestPerson(person)) {
            post("/duquesa/fleury/appointment_schedule/cancel", req) { response ->
                ResponseAssert.assertThat(response).isSuccessfulJson()
                val content = getResponse<Boolean>(response)
                Assertions.assertThat(content).isEqualTo(true)
            }
        }
        coVerify {
            externalSchedulingService.cancelFleurySlot(any(), person.id)
        }
    }

    @Test
    fun `#fleuryConfirmSlot should confirm slot`() {
        val req = FleuryOperationRequest("9100071001")

        coEvery {
            externalSchedulingService.confirmFleurySlot(req.id, person.id)
        } returns true.success()

        authenticatedAs(token, toTestPerson(person)) {
            post("/duquesa/fleury/appointment_schedule/confirm", req) { response ->
                ResponseAssert.assertThat(response).isSuccessfulJson()
                val content = getResponse<Boolean>(response)
                Assertions.assertThat(content).isEqualTo(true)
            }
        }
        coVerify {
            externalSchedulingService.confirmFleurySlot(any(), person.id)
        }
    }
}
