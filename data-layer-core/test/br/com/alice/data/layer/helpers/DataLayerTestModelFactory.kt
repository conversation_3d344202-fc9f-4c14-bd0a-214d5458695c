package br.com.alice.data.layer.helpers

import br.com.alice.authentication.UserType
import br.com.alice.common.BeneficiaryType
import br.com.alice.common.Brand
import br.com.alice.common.RangeUUID
import br.com.alice.common.UUIDv7
import br.com.alice.common.UpdatedBy
import br.com.alice.common.core.PersonId
import br.com.alice.common.core.Role
import br.com.alice.common.core.StaffType
import br.com.alice.common.extensions.money
import br.com.alice.common.models.Gender
import br.com.alice.common.models.Sex
import br.com.alice.common.models.SpecialistTier
import br.com.alice.common.models.State
import br.com.alice.common.storage.AliceFile
import br.com.alice.data.layer.models.*
import io.ktor.util.date.WeekDay
import java.math.BigDecimal
import java.time.LocalDate
import java.time.LocalDateTime
import java.time.LocalTime
import java.util.UUID
import kotlin.random.Random

object DataLayerTestModelFactory {

    fun buildPerson(
        personId: PersonId = PersonId(),
        firstName: String = "José",
        lastName: String = "da Silva",
        nickName: String? = "Zé",
        socialName: String? = null,
        socialFirstName: String? = null,
        socialLastName: String? = null,
        nationalId: String = "609.048.950-68",
        email: String = "<EMAIL>",
        sex: Sex? = null,
        gender: Gender? = null,
        dateOfBirth: LocalDateTime? = null,
        tags: List<String>? = null,
        identityDocument: String? = "111111112",
        identityDocumentIssuingBody: String? = "SSP-SP",
        opportunityId: UUID? = null,
        leadId: UUID? = null,
        userType: UserType = UserType.MEMBER,
        phoneNumber: String? = null,
        profilePicture: AliceFile? = null,
        acceptedTermsAt: LocalDateTime? = null,
        mothersName: String? = "Mother name",
        customPronoun: String? = null,
        updatedBy: UpdatedBy? = null,
        productInfo: ProductInfoModel? = null,
        piiInternalCode: String = PersonModel.generatePiiInternalCode(),
        changeHistory: List<PersonChangeHistoryModel>? = emptyList()
    ) = PersonModel(
        id = personId,
        firstName = firstName,
        lastName = lastName,
        nickName = nickName,
        socialName = socialName,
        socialFirstName = socialFirstName,
        socialLastName = socialLastName,
        nationalId = nationalId,
        email = email,
        sex = sex,
        gender = gender,
        dateOfBirth = dateOfBirth,
        tags = tags,
        identityDocument = identityDocument,
        identityDocumentIssuingBody = identityDocumentIssuingBody,
        opportunityId = opportunityId,
        leadId = leadId,
        userType = userType,
        phoneNumber = phoneNumber,
        profilePicture = profilePicture,
        acceptedTermsAt = acceptedTermsAt,
        mothersName = mothersName,
        customPronoun = customPronoun,
        updatedBy = updatedBy,
        productInfo = productInfo,
        piiInternalCode = piiInternalCode,
        changeHistory = changeHistory
    )

    fun buildMember(
        personId: PersonId = PersonId(),
        activationDate: LocalDateTime? = null,
        productId: UUID = RangeUUID.generate(),
        productTitle: String = "Conforto + (Reembolso)",
        productType: ProductType = ProductType.B2C,
        id: UUID = RangeUUID.generate(),
        canceledAt: LocalDateTime? = null,
        status: MemberStatus = MemberStatus.PENDING,
        brand: Brand? = Brand.ALICE,
        externalBrandAccountNumber: String? = null,
        parentMember: UUID? = null,
        parentPerson: PersonId? = null,
        createdAt: LocalDateTime = LocalDateTime.now(),
        updatedBy: UpdatedBy? = null,
        beneficiaryId: UUID? = RangeUUID.generate(),
        companyId: UUID? = RangeUUID.generate(),
        companySubContractId: UUID? = RangeUUID.generate(),
        beneficiary: MemberBeneficiaryModel? = null,
    ) = MemberModel(
        id = id,
        personId = personId,
        contract = ContractModel("https://localhost/contrato.pdf"),
        selectedProduct = MemberProduct(productId, listOf(), type = productType),
        activationDate = if (status == MemberStatus.ACTIVE && activationDate == null) LocalDateTime.now()
            .minusYears(1) else activationDate,
        canceledAt = canceledAt,
        brand = brand,
        externalBrandAccountNumber = externalBrandAccountNumber,
        parentPerson = parentPerson,
        parentMember = parentMember,
        createdAt = createdAt,
        updatedBy = updatedBy,
        status = status,
        beneficiaryId = beneficiaryId,
        companyId = companyId,
        companySubContractId = companySubContractId,
        beneficiary = beneficiary,
    )

    fun buildItauPayment(
        id: UUID = RangeUUID.generate(),
        invoicePaymentId: UUID = RangeUUID.generate(),
        pixId: String? = null,
        boletoId: String? = null,
        ourNumber: Int = 0,
        paidResponse: String? = null,
        version: Int = 0,
        createdAt: LocalDateTime = LocalDateTime.now(),
        updatedAt: LocalDateTime = LocalDateTime.now()
    ) = ItauPaymentModel(
        id = id,
        invoicePaymentId = invoicePaymentId,
        pixId = pixId,
        boletoId = boletoId,
        ourNumber = ourNumber,
        paidResponse = paidResponse,
        version = version,
        createdAt = createdAt,
        updatedAt = updatedAt
    )

    fun buildMemberProductPrice(
        id: UUID = RangeUUID.generate(),
        productPriceListingId: UUID = RangeUUID.generate(),
        memberId: UUID = RangeUUID.generate(),
        productPriceAdjustmentId: UUID? = null,
        startDate: LocalDateTime = LocalDateTime.now(),
        endDate: LocalDateTime? = null,
        sequence: Int = 0,
        items: List<PriceListingItem> = listOf(
            PriceListingItem(
                minAge = 1,
                maxAge = 18,
                amount = 100.money,
                priceAdjustment = BigDecimal("20.00")
            )
        ),
    ) = MemberProductPriceModel(
        id = id,
        productPriceListingId = productPriceListingId,
        productPriceAdjustmentId = productPriceAdjustmentId,
        memberId = memberId,
        startDate = startDate,
        endDate = endDate,
        sequence = sequence,
        items = items,
    )

    fun buildPersonDefaulter(
        personId: PersonId = PersonId(),
        value: BigDecimal = BigDecimal("10.0"),
        type: PersonDefaulterType = PersonDefaulterType.TARGET,
    ) = PersonDefaulterModel(
        personId = personId,
        value = value,
        type = type
    )

    fun buildPersonIdentityValidation(
        personId: PersonId = PersonId(),
        transactionId: String = "1234",
        score: BigDecimal? = BigDecimal("0.5"),
        requestValidationCreatedAt: LocalDateTime = LocalDateTime.now(),
        scoreCreatedAt: LocalDateTime? = LocalDateTime.now(),
        type: PersonIdentityValidationType = PersonIdentityValidationType.BIOMETRIC_SCORE,
        status: PersonIdentityValidationStatus = PersonIdentityValidationStatus.PROCESSING,
        fileVaultId: UUID? = null,
        match: Boolean? = null,
    ) = PersonIdentityValidationModel(
        personId = personId,
        requestValidationCreatedAt = requestValidationCreatedAt,
        scoreCreatedAt = scoreCreatedAt,
        score = score,
        type = type,
        status = status,
        fileVaultId = fileVaultId,
        match = match,
        transactionId = transactionId,
    )

    fun buildAddress(
        state: State = State.SP,
        city: String = "São Paulo",
        street: String = "Rua Canada",
        number: String = Random.nextInt(0, 10000).toString(),
        complement: String? = "ap ${Random.nextInt(0, 100)}",
        neighbourhood: String? = "Jardim Europa",
        postalCode: String? = "01448-040",
        lat: Double? = -23.57228442,
        lng: Double? = -46.69272687
    ) = AddressModel(
        state = state,
        city = city,
        street = street,
        number = number,
        complement = complement,
        neighbourhood = neighbourhood,
        postalCode = postalCode,
        lat = lat,
        lng = lng
    )

    fun buildStaff(
        firstName: String = "José",
        lastName: String = "Silva",
        email: String = "<EMAIL>",
        gender: Gender = Gender.MALE,
        id: UUID = RangeUUID.generate(),
        role: Role = Role.MANAGER_PHYSICIAN,
        nationalId: String? = "10201857081",
        type: StaffType = StaffType.PITAYA,
        profileImageUrl: String? = null,
    ) = StaffModel(
        id = id,
        email = email,
        firstName = firstName,
        lastName = lastName,
        gender = gender,
        role = role,
        nationalId = nationalId,
        type = type,
        profileImageUrl = profileImageUrl,
    )

    fun buildHealthCommunitySpecialist(
        specialtyId: UUID? = null,
        subSpecialtyIds: List<UUID> = emptyList(),
        email: String = "<EMAIL>",
        providerUnitIds: List<UUID> = emptyList(),
        tier: SpecialistTier? = SpecialistTier.EXPERT,
        staffId: UUID = RangeUUID.generate(),
    ) = HealthCommunitySpecialistModel(
        id = RangeUUID.generate(),
        name = "Dino da Silva Sauro",
        email = email,
        specialtyId = specialtyId,
        subSpecialtyIds = subSpecialtyIds,
        council = CouncilModel("1234", State.SP),
        phones = emptyList(),
        qualifications = emptyList(),
        imageUrl = null,
        providerUnitIds = providerUnitIds,
        tier = tier,
        staffId = staffId
    )

    fun buildHealthProfessional(
        id: UUID = RangeUUID.generate(),
        staffId: UUID = RangeUUID.generate(),
        profileBio: String? = "bio",
        council: CouncilModel = CouncilModel(
            "1234",
            State.SP
        ),
        specialtyId: UUID? = RangeUUID.generate(),
        subSpecialtyIds: List<UUID> = listOf(RangeUUID.generate()),
        internalSpecialtyId: UUID? = null,
        internalSubSpecialtyIds: List<UUID> = emptyList(),
        quote: String? = "quote",
        urlSlug: String? = "url-slug",
        imageUrl: String? = null,
        staff: StaffModel? = null,
        onCall: Boolean = false,
        providerUnitIds: List<UUID> = emptyList(),
        email: String = "<EMAIL>",
        name: String = "João",
        gender: Gender? = null,
        nationalId: String? = null,
        type: StaffType = StaffType.PITAYA,
        role: Role? = null,
        education: List<String> = emptyList(),
        qualifications: List<Qualification> = emptyList(),
        appointmentTypes: List<SpecialistAppointmentType> = emptyList(),
        addressesStructured: List<StructuredAddress>? = null,
        contactIds: List<UUID> = emptyList(),
        healthSpecialistScore: HealthSpecialistScoreEnum? = null,
        theoristTier: SpecialistTier? = null,
        tier: SpecialistTier? = null,
        onVacationUntil: LocalDateTime? = null,
        onVacationStart: LocalDateTime? = null
    ) = HealthProfessionalModel(
        id = id,
        staffId = staffId,
        profileBio = profileBio,
        council = council,
        specialtyId = specialtyId,
        subSpecialtyIds = subSpecialtyIds,
        internalSpecialtyId = internalSpecialtyId,
        internalSubSpecialtyIds = internalSubSpecialtyIds,
        quote = quote,
        education = education,
        qualifications = qualifications,
        urlSlug = urlSlug,
        imageUrl = imageUrl,
        staff = staff,
        onCall = onCall,
        providerUnitIds = providerUnitIds,
        email = email,
        name = name,
        gender = gender,
        nationalId = nationalId,
        type = type,
        role = role,
        appointmentTypes = appointmentTypes,
        addressesStructured = addressesStructured,
        contactIds = contactIds,
        healthSpecialistScore = healthSpecialistScore,
        theoristTier = theoristTier,
        tier = tier,
        onVacationUntil = onVacationUntil,
        onVacationStart = onVacationStart
    )

    fun buildHealthcareTeam(
        id: UUID = RangeUUID.generate(),
        physicianStaffId: UUID = RangeUUID.generate(),
        nurseStaffId: UUID = RangeUUID.generate(),
        digitalCareNurseStaffIds: List<UUID> = emptyList(),
        careCoordNurseStaffId: UUID? = null,
        type: HealthcareTeamModel.Type = HealthcareTeamModel.Type.STANDARD,
        segment: HealthcareTeamModel.Segment = HealthcareTeamModel.Segment.PEDIATRIC,
        maxMemberAssociation: Int? = null,
        address: StructuredAddress? = null
    ) =
        HealthcareTeamModel(
            id = id,
            physicianStaffId = physicianStaffId,
            nurseStaffId = nurseStaffId,
            digitalCareNurseStaffIds = digitalCareNurseStaffIds,
            careCoordNurseStaffId = careCoordNurseStaffId,
            type = type,
            segment = segment,
            maxMemberAssociation = maxMemberAssociation,
            address = address
        )

    fun buildCassiSpecialist(
        id: UUID = RangeUUID.generate(),
        name: String = "Iago",
        specialtyId: UUID = RangeUUID.generate(),
        subSpecialtyIds: List<UUID> = emptyList(),
        email: String? = null,
        gender: Gender? = null,
        council: CouncilModel = CouncilModel("001", State.RJ),
        phones: List<PhoneNumber> = emptyList(),
        address: List<StructuredAddress> = emptyList(),
        imageUrl: String? = null,
        minAttendanceAge: Int? = null,
        maxAttendanceAge: Int? = null,
        appointmentTypes: List<SpecialistAppointmentType> = emptyList(),
        status: SpecialistStatus = SpecialistStatus.ACTIVE,
        urlSlug: String = "iago",
        searchTokens: String? = null,
        contactIds: List<UUID> = emptyList(),
        createdAt: LocalDateTime = LocalDateTime.now(),
        updatedAt: LocalDateTime = LocalDateTime.now()
    ) = CassiSpecialistModel(
        id = id,
        name = name,
        specialtyId = specialtyId,
        subSpecialtyIds = subSpecialtyIds,
        email = email,
        gender = gender,
        council = council,
        phones = phones,
        address = address,
        imageUrl = imageUrl,
        minAttendanceAge = minAttendanceAge,
        maxAttendanceAge = maxAttendanceAge,
        appointmentTypes = appointmentTypes,
        status = status,
        urlSlug = urlSlug,
        searchTokens = searchTokens,
        contactIds = contactIds,
        createdAt = createdAt,
        updatedAt = updatedAt
    )

    fun buildLegalGuardianAssociation(
        personId: PersonId,
        status: LegalGuardianAssociationStatusModel,
        guardianId: PersonId = buildPerson().id,
        isSigned: Boolean = false
    ) = LegalGuardianAssociationModel(
        id = RangeUUID.generate(),
        personId = personId,
        guardianId = guardianId,
        degreeOfKinship = DegreeOfKinship.GUARDIAN,
        status = status.legalGuardianAssociationStatusType,
        statusHistory = listOf(status),
        isSigned = isSigned,
        createdAt = LocalDateTime.now(),
        updatedAt = LocalDateTime.now(),
        version = 0,
    )

    fun buildProduct(
        title: String = "Alice 222 [AP.NAC.02.SC.CR.EMP] - Opcional - PME",
        prices: List<ProductPriceModel> = listOf(buildProductPrice()),
        bundleIds: List<UUID>? = null,
        id: UUID = RangeUUID.generate(),
        reference: Boolean = false,
        externalIds: List<ExternalIdModel> = listOf(ExternalIdModel(ExternalIdKey.MV_PLAN, "1")),
        anchor: ProductAnchor? = null,
        priceListing: PriceListingModel? = null,
        active: Boolean = true,
        type: ProductType = ProductType.B2C,
        subType: B2BSubTypes? = null,
        minEmployeeNumber: Int? = null,
        maxEmployeeNumber: Int? = null,
        displayName: String? = "Conforto +",
        complementName: String? = "(Reembolso)",
        previousDisplayName: String? = null,
        ansNumber: String? = null,
        accommodation: AccommodationType? = null,
        hasNationalCoverage: Boolean = false,
        isVisibleForSale: Boolean = false,
        brand: Brand? = Brand.ALICE,
        primaryAttention: PrimaryAttentionType? = PrimaryAttentionType.ALICE,
        tier: TierType? = TierType.TIER_1,
        refund: RefundType = RefundType.NONE,
        coPayment: CoPaymentType = CoPaymentType.FULL,
        characteristics: List<ProductCharacteristic> = emptyList()
    ) =
        ProductModel(
            id = id,
            title = title,
            prices = prices,
            externalIds = externalIds,
            bundleIds = bundleIds,
            reference = reference,
            anchor = anchor,
            active = active,
            type = type,
            subType = subType,
            minEmployeeNumber = minEmployeeNumber,
            maxEmployeeNumber = maxEmployeeNumber,
            displayName = displayName,
            complementName = complementName,
            previousDisplayName = previousDisplayName,
            ansNumber = ansNumber,
            accommodation = accommodation,
            hasNationalCoverage = hasNationalCoverage,
            isVisibleForSale = isVisibleForSale,
            brand = brand,
            primaryAttention = primaryAttention,
            tier = tier,
            refund = refund,
            coPayment = coPayment,
            characteristics = characteristics
        ).withPriceListing(priceListing)

    fun buildProductPrice(
        id: String = "1",
        title: String = "Price Title",
        amount: BigDecimal = BigDecimal("900.00"),
        minAge: Int = 0,
        maxAge: Int = 99,
    ) = ProductPriceModel(
        id = id,
        title = title,
        amount = amount,
        minAge = minAge,
        maxAge = maxAge
    )

    fun buildCompanyRefundCostInfo(
        id: UUID = RangeUUID.generate(),
        companyId: UUID = RangeUUID.generate(),
        prices: List<CompanyRefundCostInfoPricesModel> = listOf(
            CompanyRefundCostInfoPricesModel(
                value = BigDecimal("10.00"),
                eventType = RefundEventTypeModel.THERAPY,
                event = "Psicologia",
            ),
        ),
        tier: TierType = TierType.TIER_1,
        referenceDate: LocalDate = LocalDate.now(),
        fileVaultId: UUID = RangeUUID.generate(),
        version: Int = 0,
        createdAt: LocalDateTime = LocalDateTime.now(),
        updatedAt: LocalDateTime = LocalDateTime.now(),
    ) = CompanyRefundCostInfoModel(
        id = id,
        companyId = companyId,
        prices = prices,
        tier = tier,
        referenceDate = referenceDate,
        fileVaultId = fileVaultId,
        version = version,
        createdAt = createdAt,
        updatedAt = updatedAt,
    )

    fun buildPriceListing(
        id: UUID = RangeUUID.generate(),
        title: String = "Plano 10",
        ranges: List<PriceListingItemModel> = listOf(buildPriceListingItem()),
    ) = PriceListingModel(
        title = title,
        items = ranges,
        id = id,
    )

    fun buildPriceListingItem(
        minAge: Int = 1,
        maxAge: Int = 18,
        amount: BigDecimal = 100.money,
        priceAdjustment: BigDecimal = BigDecimal("20.00"),
    ) = PriceListingItemModel(
        minAge = minAge,
        maxAge = maxAge,
        amount = amount,
        priceAdjustment = priceAdjustment
    )

    fun buildProductPriceAdjustment(
        id: UUID = RangeUUID.generate(),
        startDate: LocalDate = LocalDate.now(),
        endDate: LocalDate = LocalDate.now().plusYears(1),
        percentage: BigDecimal = 10.0.toBigDecimal(),
        regulatoryCode: String = "xyz",
        version: Int = 0,
        createdAt: LocalDateTime = LocalDateTime.now(),
        updatedAt: LocalDateTime = LocalDateTime.now(),
    ) = ProductPriceAdjustmentModel(
        id = id,
        startDate = startDate,
        endDate = endDate,
        percentage = percentage,
        regulatoryCode = regulatoryCode,
        version = version,
        createdAt = createdAt,
        updatedAt = updatedAt,
    )

    fun buildProductPriceListing(
        id: UUID = RangeUUID.generate(),
        priceListingId: UUID = RangeUUID.generate(),
        productId: UUID = RangeUUID.generate(),
        startDate: LocalDateTime = LocalDateTime.now(),
        endDate: LocalDateTime? = null,
    ) = ProductPriceListingModel(
        id = id,
        priceListingId = priceListingId,
        productId = productId,
        startDate = startDate,
        endDate = endDate,
    )

    fun buildHealthProfessionalTierHistory(
        staffId: UUID = RangeUUID.generate(),
        tier: SpecialistTier? = null,
        theoristTier: SpecialistTier? = null,
        activatedAt: LocalDateTime = LocalDateTime.now(),
        createdAt: LocalDateTime = LocalDateTime.now(),
        updatedAt: LocalDateTime = LocalDateTime.now()
    ) = HealthProfessionalTierHistoryModel(
        staffId = staffId,
        tier = tier,
        theoristTier = theoristTier,
        activatedAt = activatedAt,
        createdAt = createdAt,
        updatedAt = updatedAt
    )

    fun buildTissBatchError(
        tissBatchId: UUID = RangeUUID.generate(),
        lineNumber: Int? = null,
        description: String = "Default description"
    ) = TissBatchErrorModel(
        tissBatchId = tissBatchId,
        lineNumber = lineNumber,
        description = description
    )

    fun buildAttachmentChemotherapy(
        id: UUID = RangeUUID.generate(),
        totvsGuiaId: UUID = RangeUUID.generate(),
        status: AttachmentStatus = AttachmentStatus.PENDING,
        chemotherapyOncologicalDiagnosis: OncologicalDiagnosisModel = OncologicalDiagnosisModel(
            diagnosisDate = LocalDate.now(),
            stage = AnsStage.FIRST,
            type = AnsChemotherapyType.FIRST_LINE,
            purpose = AnsPurpose.ADJUVANT,
            tumor = AnsTumor.NAO_SE_APLICA,
            nodule = AnsNodule.NAO_SE_APLICA,
            metastasis = AnsMetastasis.NAO_SE_APLICA,
            healthCondition = ChemotherapyHealthConditionModel(
                name = "name",
                code = "123",
            ),
            ecoGt = AnsEcoGT.FULL_ACTIVE
        ),
        requestedDrugs: List<RequestedDrugsModel> = listOf(
            RequestedDrugsModel(
                startDate = LocalDate.now(),
                totalCycleDosage = 1.0,
                unitOfMeasurement = AnsUnitOfMeasurement.MG,
                administrationRoute = AnsAdministrationRoute.ORAL,
                frequency = 1,
                status = MvAuthorizedProcedureStatus.PENDING,
                drugsIdentification = DrugsIdentificationModel(
                    table = "19",
                    code = "1245678",
                    description = "droga01",
                )
            )
        ),
        cyclesQuantity: Int = 5,
        currentCycle: Int = 1,
        currentCycleDays: Int = 10,
        cyclesInterval: Int = 10,
        observation: String? = null,
        height: BigDecimal = BigDecimal("180"),
        weight: BigDecimal = BigDecimal("70"),
    ) = AttachmentChemotherapyModel(
        id = id,
        totvsGuiaId = totvsGuiaId,
        status = status,
        chemotherapyOncologicalDiagnosis = chemotherapyOncologicalDiagnosis,
        requestedDrugs = requestedDrugs,
        cyclesQuantity = cyclesQuantity,
        currentCycle = currentCycle,
        currentCycleDays = currentCycleDays,
        cyclesInterval = cyclesInterval,
        height = height,
        weight = weight,
        observation = observation,
    )

    fun buildStaffSignToken(
        id: UUID = RangeUUID.generate(),
        staffId: UUID = RangeUUID.generate(),
        token: String = "d7c00ccb6d00f246266f48f3909dc42d252291b2"
    ) = StaffSignTokenModel(
        id = id,
        staffId = staffId,
        token = token
    )

    fun buildCancelPaymentOnAcquirerScheduleModel(
        id: UUID = RangeUUID.generate(),
        requestedAt: LocalDateTime = LocalDateTime.now(),
        canceledAt: LocalDateTime? = null,
        invoicePaymentId: UUID = RangeUUID.generate(),
        createdAt: LocalDateTime = LocalDateTime.now(),
        updatedAt: LocalDateTime = LocalDateTime.now()
    ) = CancelPaymentOnAcquirerScheduleModel(
        id = id,
        requestedAt = requestedAt,
        canceledAt = canceledAt,
        invoicePaymentId = invoicePaymentId,
        createdAt = createdAt,
        updatedAt = updatedAt
    )

    fun buildFirstPaymentScheduleModel(
        id: UUID = RangeUUID.generate(),
        preActivationPaymentId: UUID = RangeUUID.generate(),
        companyId: UUID = RangeUUID.generate(),
        companySubcontractId: UUID = RangeUUID.generate(),
        memberInvoiceGroupId: UUID? = null,
        statusHistory: List<FirstPaymentScheduleStatusHistoryEntryModel> = emptyList(),
        status: FirstPaymentScheduleStatus = FirstPaymentScheduleStatus.PENDING,
        error: String? = null,
        scheduledDate: LocalDate = LocalDate.now(),
        version: Int = 0,
        createdAt: LocalDateTime = LocalDateTime.now(),
        updatedAt: LocalDateTime = LocalDateTime.now(),
        updatedBy: UpdatedBy? = null
    ) = FirstPaymentScheduleModel(
        id = id,
        preActivationPaymentId = preActivationPaymentId,
        companyId = companyId,
        companySubcontractId = companySubcontractId,
        memberInvoiceGroupId = memberInvoiceGroupId,
        statusHistory = statusHistory,
        status = status,
        error = error,
        scheduledDate = scheduledDate,
        version = version,
        createdAt = createdAt,
        updatedAt = updatedAt,
        updatedBy = updatedBy
    )

    fun buildBeneficiary(
        id: UUID = RangeUUID.generate(),
        parentBeneficiary: UUID? = null,
        personId: PersonId = PersonId(),
        memberId: UUID = RangeUUID.generate(),
        companyId: UUID = RangeUUID.generate(),
        companySubContractId: UUID? = RangeUUID.generate(),
        type: BeneficiaryType = BeneficiaryType.EMPLOYEE,
        contractType: BeneficiaryContractType? = null,
        parentBeneficiaryRelationType: ParentBeneficiaryRelationType? = null,
        activatedAt: LocalDateTime = LocalDateTime.now(),
        canceledAt: LocalDateTime? = null,
        hiredAt: LocalDateTime? = LocalDateTime.now(),
        parentBeneficiaryRelatedAt: LocalDateTime? = null,
        cnpj: String? = null,
        hasContributed: Boolean? = null,
        dependents: List<BeneficiaryModel>? = null,
        version: Int = 0,
        onboarding: BeneficiaryOnboardingModel? = null,
        canceledReason: BeneficiaryCancelationReason? = null,
        canceledDescription: String? = null,
        archived: Boolean = false,
        brand: Brand? = Brand.ALICE,
        memberStatus: MemberStatus? = MemberStatus.PENDING,
        parentPerson: PersonId? = null,
        gracePeriodType: GracePeriodType? = null,
        gracePeriodTypeReason: GracePeriodTypeReason? = null,
        updatedBy: UpdatedBy? = null
    ) = BeneficiaryModel(
        id = id,
        parentBeneficiary = parentBeneficiary,
        personId = personId,
        memberId = memberId,
        companyId = companyId,
        companySubContractId = companySubContractId,
        type = type,
        contractType = contractType,
        parentBeneficiaryRelationType = parentBeneficiaryRelationType,
        activatedAt = activatedAt,
        canceledAt = canceledAt,
        hiredAt = hiredAt,
        parentBeneficiaryRelatedAt = parentBeneficiaryRelatedAt,
        dependents = dependents,
        version = version,
        onboarding = onboarding,
        canceledReason = canceledReason,
        canceledDescription = canceledDescription,
        archived = archived,
        cnpj = cnpj,
        hasContributed = hasContributed,
        brand = brand,
        memberStatus = memberStatus,
        parentPerson = parentPerson,
        gracePeriodType = gracePeriodType,
        gracePeriodTypeReason = gracePeriodTypeReason,
        updatedBy = updatedBy
    )

    fun buildResourceSignTokenModel(
        id: UUID = RangeUUID.generate(),
        signUuid: UUIDv7,
        resourceType: ResourceSignTokenType = ResourceSignTokenType.INVOICE_PAYMENT,
        resourceId: String = "resource-id",
        version: Int = 0,
        createdAt: LocalDateTime = LocalDateTime.now(),
        updatedAt: LocalDateTime = LocalDateTime.now(),
        deletedAt: LocalDateTime? = null,
    ) = ResourceSignTokenModel(
        id = id,
        signUuid = signUuid,
        resourceType = resourceType,
        resourceId = resourceId,
        version = version,
        createdAt = createdAt,
        updatedAt = updatedAt,
        deletedAt = deletedAt
    )

    fun buildStaffSchedule(
        id: UUID = RangeUUID.generate(),
        staffId: UUID = RangeUUID.generate(),
        healthProfessionalId: UUID? = null,
        startHour: LocalTime = LocalTime.now(),
        untilHour: LocalTime = LocalTime.now(),
        weekDay: WeekDay = WeekDay.WEDNESDAY,
        createdAt: LocalDateTime = LocalDateTime.now(),
        updatedAt: LocalDateTime = LocalDateTime.now(),
        status: StaffScheduleStatus = StaffScheduleStatus.ACTIVE,
        type: StaffScheduleType = StaffScheduleType.HAD,
        providerUnitId: UUID? = null,
        alsoDigital: Boolean = true,
        exceptionEventTypes: List<UUID> = emptyList(),
        lastUpdatedBy: UUID? = null
    ) = StaffScheduleModel(
        id = id,
        staffId = staffId,
        healthProfessionalId = healthProfessionalId,
        startHour = startHour,
        untilHour = untilHour,
        weekDay = weekDay,
        createdAt = createdAt,
        updatedAt = updatedAt,
        status = status,
        type = type,
        providerUnitId = providerUnitId,
        alsoDigital = alsoDigital,
        exceptionEventTypes = exceptionEventTypes,
        lastUpdatedBy = lastUpdatedBy
    )
}
