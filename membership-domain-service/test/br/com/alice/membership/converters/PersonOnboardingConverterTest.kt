package br.com.alice.membership.converters

import br.com.alice.common.RangeUUID
import br.com.alice.common.core.PersonId
import br.com.alice.data.layer.models.OnboardingPhase
import br.com.alice.data.layer.models.PersonOnboarding
import br.com.alice.data.layer.models.PersonOnboardingModel
import org.junit.jupiter.api.Assertions.*
import kotlin.test.Test
import java.time.LocalDateTime

class PersonOnboardingConverterTest {

    val personOnboarding = PersonOnboarding(
        personId = PersonId(),
        currentPhase = OnboardingPhase.HEALTH_DECLARATION_APPOINTMENT,
        finishedAt = LocalDateTime.now(),
        slaHealthDeclarationAppointmentCompleted = 24L,
        slaContractGenerated = 48L,
        healthDeclarationAppointmentScheduledAt = LocalDateTime.now(),
        healthDeclarationAppointmentCompletedAt = LocalDateTime.now(),
        healthDeclarationAppointmentDate = LocalDateTime.now(),
        contractAvailableAt = LocalDateTime.now(),
        legalGuardianResponsibilityTermSentAt = LocalDateTime.now(),
        activeUntil = LocalDateTime.now().plusDays(30),
        archived = false,
        ignoreExpiration = false,
        expiresAt = LocalDateTime.now().plusDays(60),
        version = 1,
        id = RangeUUID.generate()
    )

    val personOnboardingModel = PersonOnboardingModel(
        personId = personOnboarding.personId,
        currentPhase = personOnboarding.currentPhase,
        finishedAt = personOnboarding.finishedAt,
        slaHealthDeclarationAppointmentCompleted = personOnboarding.slaHealthDeclarationAppointmentCompleted,
        slaContractGenerated = personOnboarding.slaContractGenerated,
        healthDeclarationAppointmentScheduledAt = personOnboarding.healthDeclarationAppointmentScheduledAt,
        healthDeclarationAppointmentCompletedAt = personOnboarding.healthDeclarationAppointmentCompletedAt,
        healthDeclarationAppointmentDate = personOnboarding.healthDeclarationAppointmentDate,
        contractAvailableAt = personOnboarding.contractAvailableAt,
        legalGuardianResponsibilityTermSentAt = personOnboarding.legalGuardianResponsibilityTermSentAt,
        activeUntil = personOnboarding.activeUntil,
        archived = personOnboarding.archived,
        ignoreExpiration = personOnboarding.ignoreExpiration,
        expiresAt = personOnboarding.expiresAt,
        version = personOnboarding.version,
        id = personOnboarding.id,
        createdAt = personOnboarding.createdAt,
        updatedAt = personOnboarding.updatedAt
    )

    @Test
    fun `#toModel`() {
        assertEquals(personOnboardingModel, personOnboarding.toModel())
    }

    @Test
    fun `#toTransport`() {
        assertEquals(personOnboarding, personOnboardingModel.toTransport())
    }
} 
