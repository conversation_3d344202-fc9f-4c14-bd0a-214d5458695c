package br.com.alice.business.controllers

import br.com.alice.business.client.BeneficiaryOnboardingService
import br.com.alice.business.client.BeneficiaryService
import br.com.alice.business.client.MemberTelegramTrackingService
import br.com.alice.business.events.MemberTelegramTrackingRegisteredEvent
import br.com.alice.business.events.PendingActivationBeneficiaryMembershipEvent
import br.com.alice.business.metrics.MembershipMetric.Status.FAILURE
import br.com.alice.business.metrics.MembershipMetric.Status.SUCCESS
import br.com.alice.business.metrics.MembershipMetric.reportActivation
import br.com.alice.business.metrics.MembershipMetric.reportInactivation
import br.com.alice.common.Response
import br.com.alice.common.core.extensions.atEndOfTheDay
import br.com.alice.common.core.extensions.isAfterEq
import br.com.alice.common.core.extensions.isBeforeEq
import br.com.alice.common.coroutine.pmap
import br.com.alice.common.extensions.coResultOf
import br.com.alice.common.extensions.mapPair
import br.com.alice.common.extensions.pmapEach
import br.com.alice.common.extensions.pmapEachNotNull
import br.com.alice.common.extensions.resultOf
import br.com.alice.common.extensions.then
import br.com.alice.common.extensions.thenError
import br.com.alice.common.foldResponse
import br.com.alice.common.kafka.interfaces.KafkaProducerService
import br.com.alice.common.logging.logger
import br.com.alice.common.toResponse
import br.com.alice.data.layer.models.Beneficiary
import br.com.alice.data.layer.models.BeneficiaryOnboardingFlowType.FULL_RISK_FLOW
import br.com.alice.data.layer.models.BeneficiaryOnboardingFlowType.NO_RISK_FLOW
import br.com.alice.data.layer.models.BeneficiaryOnboardingPhaseType.FINISHED
import br.com.alice.data.layer.models.BeneficiaryOnboardingPhaseType.REGISTRATION
import br.com.alice.data.layer.models.BeneficiaryOnboardingPhaseType.WAITING_FOR_REVIEW
import br.com.alice.data.layer.models.FeatureNamespace
import br.com.alice.data.layer.models.Member
import br.com.alice.featureconfig.core.FeatureService
import br.com.alice.person.client.MemberService
import com.github.kittinunf.result.Result
import com.github.kittinunf.result.flatMap
import com.github.kittinunf.result.lift
import com.github.kittinunf.result.map
import com.github.kittinunf.result.success
import java.time.LocalDate
import java.time.LocalDateTime
import java.util.UUID
import java.util.concurrent.atomic.AtomicInteger

class RecurrentController(
    private val beneficiaryService: BeneficiaryService,
    private val memberService: MemberService,
    private val beneficiaryOnboardingService: BeneficiaryOnboardingService,
    private val memberTelegramTrackingService: MemberTelegramTrackingService,
    private val kafkaProducerService: KafkaProducerService
) : RecurrentBaseController() {

    companion object {
        const val CHUNK_GROUP_SIZE = 200
    }

    suspend fun inactivateBeneficiaryMembership(): Response = withRecurrentEnvironment {
        inactivateMembership().foldResponse()
    }

    suspend fun triggerBeneficiariesCassiAccountNumber(): Response = withRecurrentEnvironment {
        requestBeneficiariesCassiAccountNumber().foldResponse()
    }

    suspend fun triggerBeneficiariesCassiAccountNumberOlderExpirationDate(): Response = withRecurrentEnvironment {
        requestBeneficiariesCassiAccountNumberOlderExpirationDate().foldResponse()
    }

    suspend fun activateBeneficiariesBasedOnActivationDate(): Response = withRecurrentEnvironment {
        activateBeneficiariesMemberships().foldResponse()
    }

    suspend fun pendingBeneficiariesBasedOnActivationDate(): Response = withRecurrentEnvironment {
        pendingActivationBeneficiariesMemberships().foldResponse()
    }

    suspend fun syncNotDeliveredItems(): Response = withRecurrentEnvironment {

        logger.info(
            "syncPendingTelegramItems: Start recurrent job to sync not delivered items",
        )

        memberTelegramTrackingService.findNotDeliveredItems().flatMap { items ->
            logger.info(
                "syncPendingTelegramItems: total not delivered items",
                "total_items" to items.size
            )

            items.pmap {
                kafkaProducerService.produce(MemberTelegramTrackingRegisteredEvent(it.externalId))
            }.success()
        }

        logger.info(
            "syncPendingTelegramItems: End recurrent job to sync not delivered items",
        )

        true.toResponse()
    }

    private suspend fun requestBeneficiariesCassiAccountNumber(): Result<Boolean, Throwable> {
        var offset = 0
        val limit = 200
        logger.info("Start recurrent job to retrieve Cassi account's number for beneficiaries")
        do {
            val cassiMemberSize =
                beneficiaryService.triggerCassiAccountNumberForBeneficiaries(offset = offset, limit = limit).get()

            offset += limit
        } while (cassiMemberSize == limit)

        logger.info("RecurrentJob::requestBeneficiariesCassiAccountNumber finished")

        return true.success()
    }

    private suspend fun requestBeneficiariesCassiAccountNumberOlderExpirationDate(): Result.Success<Boolean> {

        return if (shouldExecuteCassiExpirationDateRoutine()) {
            logger.info("Start recurrent job to retrieve Cassi account's number for beneficiaries with older expiration date")
                .success()
                .flatMap { beneficiaryService.triggerCassiAccountNumberForBeneficiariesWithOlderExpirationDate() }
                .fold({ true.success() }, { false.success() })
        } else {
            logger.warn("Cassi member expiration date routine is disabled")
            false.success()
        }
    }


    private suspend fun inactivateMembership() = coResultOf<Boolean, Throwable> {
        val endDate = LocalDate.now()
        val startDate = endDate.minusDays(10)
        var offset = 0
        val limit = 200
        var page = 0

        logger.info(
            "Start recurrent job to inactivate membership of beneficiaries",
            "end_date" to endDate,
            "start_date" to startDate,
        )

        do {
            val beneficiaries =
                beneficiaryService.findByCanceledAtBetweenInclusivePaginated(
                    startDate,
                    endDate,
                    offset,
                    limit
                )
                    .then {
                        logger.info(
                            "RecurrentJob::inactivateMembership paginated beneficiaries",
                            "size" to it.size,
                            "page" to page
                        )
                    }
                    .thenError {
                        logger.error("RecurrentJob::inactivateMembership error when paginate beneficiaries", it)
                    }
                    .get()

            beneficiaries.inactivateMemberShips()

            offset += limit
            page += page
        } while (beneficiaries.size == limit)

        logger.info("RecurrentJob::inactivateMembership finished")

        true
    }

    private suspend fun findForBeneficiariesPending(startDate: LocalDate, endDate: LocalDate): List<Beneficiary> {
        val beneficiaries = mutableListOf<Beneficiary>()

        var offset = 0
        val limit = 200
        var page = 0

        do {
            val fetched = beneficiaryService
                .findPending(BeneficiaryService.FindOptions(), offset, limit)
                .get()

            beneficiaries.addAll(
                fetched.filter {
                    startDate.atStartOfDay().isBeforeEq(it.activatedAt) &&
                            endDate.atEndOfTheDay().isAfterEq(it.activatedAt)
                }.pmap {
                    it.copy(onboarding = beneficiaryOnboardingService.findByBeneficiaryId(it.id).get())
                }
            )

            offset += limit
            page += page
        } while (fetched.size == limit)

        logger.info("RecurrentJob::findForBeneficiariesPending finding finished")

        return beneficiaries
    }

    private suspend fun activateBeneficiariesMemberships(): Result<Boolean, Throwable> = coResultOf {

        val dateNow = LocalDate.now()

        logRecurrentJob(dateNow)

        val beneficiaries = findForBeneficiariesPending(dateNow.minusDays(1), dateNow)

        val beneficiaryParentIds = beneficiaries.filter { it.parentBeneficiary == null }.map { it.id }
        beneficiaries.chunked(CHUNK_GROUP_SIZE)
            .map { chunkedBeneficiaries ->
                val (activatedBeneficiaries, results) = chunkedBeneficiaries
                    .shouldBeActivated(beneficiaryParentIds)
                    .then { it.logBeneficiariesToActivate() }
                    .thenError {
                        logger.error(
                            "RecurrentJob::activateBeneficiariesMemberships pagination error",
                            it,
                        )
                    }
                    .activateMemberships()
                    .get()

                activatedBeneficiaries.checkAndMoveToNextPhase()

                results
            }.reduce { acc, current -> acc + current }
            .also {
                logger.info(
                    "RecurrentController::activateBeneficiariesMemberships - result",
                    "total_register_finished" to it.successCount,
                    "total_register_failure" to it.errorCount,
                    "errors" to it.errors,
                )
            }.exposeDetails(beneficiaries)

        true
    }

    private fun JobRecurrentResult.exposeDetails(beneficiaries: List<Beneficiary>) {
        beneficiaries.groupBy { it.companyId }
            .forEach { (companyId, beneficiariesForCompany) ->

                val totalBeneficiaries = beneficiariesForCompany.size
                val beneficiaryIds = beneficiariesForCompany.map { it.id.toString() }
                val beneficiariesWithSomeError = errors.filterKeys { it in beneficiaryIds }
                val totalBeneficiariesWithSomeError = beneficiariesWithSomeError.size

                logger.info(
                    "RecurrentController::activateBeneficiariesMemberships - details for company",
                    "company_id" to companyId,
                    "total_tried" to totalBeneficiaries,
                    "total_successes" to totalBeneficiaries - totalBeneficiariesWithSomeError,
                    "total_failures" to totalBeneficiariesWithSomeError,
                    "errors" to beneficiariesWithSomeError,
                )
            }
    }

    private suspend fun List<Beneficiary>.checkAndMoveToNextPhase() =
        this.filter { beneficiary -> beneficiary.onboarding?.currentPhase?.phase == WAITING_FOR_REVIEW }
            .also {
                logger.info(
                    "Beneficiaries that should be moved to next phase",
                    "beneficiary_ids" to it.map { beneficiary -> beneficiary.id })
            }
            .pmap { beneficiaryOnboardingService.moveToNextPhase(it.id) }

    private fun List<Beneficiary>.shouldBeActivated(alreadyActivatedParentsIds: List<UUID>) =
        resultOf<List<Beneficiary>, Throwable> {
            val parents = filter { it.shouldBeActivated() && it.parentBeneficiary == null }
            val parentIds = parents.map { it.id } + alreadyActivatedParentsIds

            val dependents =
                filter { it.shouldBeActivated() && (it.parentBeneficiary != null && it.parentBeneficiary !in parentIds) }

            logger.info(
                "RecurrentJob::shouldBeActivated",
                "alreadyActivatedParentsIds" to alreadyActivatedParentsIds,
                "parent_ids" to parentIds,
                "dependent_ids" to dependents.map { it.id })

            parents.plus(dependents)
        }

    private suspend fun pendingActivationBeneficiariesMemberships() =
        LocalDate.now().success()
            .then {
                logger.info(
                    "Start recurrent job to notify pending activation of memberships of beneficiaries",
                    "reference_date" to it
                )
            }
            .map { dateNow -> findForBeneficiariesPending(dateNow, dateNow) }
            .map { beneficiaries -> beneficiaries.filter { it.isPendingActivation() } }
            .then {
                logger.info(
                    "Start memberships to producing an event",
                    "beneficiary_ids" to it.map { beneficiary -> beneficiary.id },
                    "person_ids" to it.map { person -> person.personId },
                    "total" to it.size,
                )
            }
            .map {
                if (it.isEmpty()) return true.success()
                else {
                    it
                        .chunked(CHUNK_GROUP_SIZE)
                        .map { chunkedBeneficiaries ->
                            chunkedBeneficiaries.pmap { beneficiary ->
                                kafkaProducerService.produce(PendingActivationBeneficiaryMembershipEvent(beneficiary))
                            }
                        }
                }
            }
            .fold({ true.success() }, { false.success() })

    private fun Beneficiary.isPendingActivation() =
        (
                this.onboarding?.flowType == FULL_RISK_FLOW &&
                        this.onboarding?.currentPhase?.phase != WAITING_FOR_REVIEW &&
                        this.onboarding?.currentPhase?.phase != FINISHED
                )

    private fun Beneficiary.shouldBeActivated() =
        when {
            this.onboarding?.flowType == NO_RISK_FLOW -> true
            this.onboarding?.currentPhase?.phase in listOf(WAITING_FOR_REVIEW, REGISTRATION) -> true
            else -> false
        }

    private fun logRecurrentJob(referenceDate: LocalDate) =
        logger.info(
            "Start recurrent job to activate memberships of beneficiaries",
            "reference_date" to referenceDate
        )

    private suspend fun Result<List<Beneficiary>, Throwable>.activateMemberships(): Result<Pair<List<Beneficiary>, JobRecurrentResult>, Throwable> {
        val successCount = AtomicInteger(0)
        val errorsCount = AtomicInteger(0)
        val errorInfo = mutableMapOf<String, String>()

        val beneficiaries = this.get()

        return memberService.findByIds(beneficiaries.map { it.memberId })
            .map { members -> members.filter { it.isPending } }
            .mapPair { beneficiaries.associateBy { it.memberId } }
            .flatMap { (beneficiaryMemberMap, members) ->
                members.pmap { member ->
                    val beneficiary = beneficiaryMemberMap.getValue(member.id)
                    activateMembershipAndReport(beneficiary)
                        .then { successCount.incrementAndGet() }
                        .thenError {
                            errorInfo[beneficiary.id.toString()] = it.message ?: "Unknown error"
                            errorsCount.incrementAndGet()
                        }
                }
                    .lift { success, _ -> success.success() }
                    .map { it to JobRecurrentResult(successCount.get(), errorsCount.get(), errorInfo) }
            }
    }

    private suspend fun List<Beneficiary>.inactivateMemberShips(): Result<List<Beneficiary>, Throwable> {
        val successCount = AtomicInteger(0)
        val errorsCount = AtomicInteger(0)
        val errorInfo = mutableMapOf<String, String?>()

        return memberService.findByIds(this.map { it.memberId })
            .map { members -> members.filterNot { it.isCanceled } }
            .mapPair { this.associateBy { it.memberId } }
            .flatMap { (beneficiaryMemberMap, members) ->

                beneficiaryMemberMap.values.toList().logBeneficiariesToInactivate()

                members.pmap { member ->
                    inactivateMembershipAndReport(member, beneficiaryMemberMap.getValue(member.id))
                        .then { successCount.incrementAndGet() }
                        .thenError {
                            errorInfo[member.id.toString()] = it.message
                            errorsCount.incrementAndGet()
                        }
                }.lift()
            }.thenError {
                logger.info(
                    "RecurrentController::inactivateMemberShips - result",
                    "total_register_finished" to successCount,
                    "total_register_failure" to errorsCount,
                    "errors" to errorInfo,
                )
            }.then {
                logger.info(
                    "RecurrentController::inactivateMemberShips - result",
                    "total_register_finished" to successCount,
                    "total_register_failure" to errorsCount,
                    "errors" to errorInfo,
                )
            }
    }

    private suspend fun activateMembershipAndReport(
        beneficiary: Beneficiary,
    ) = beneficiaryService.activateBeneficiary(beneficiary.id)
        .then { reportActivation(SUCCESS) }
        .then { logBeneficiaryActivatedOrInactivated(beneficiary, "activation") }
        .thenError { reportActivation(FAILURE) }
        .thenError { logBeneficiaryNotActivatedOrNotOInactivate(beneficiary, it, "activation") }
        .map { beneficiary }

    private suspend fun inactivateMembershipAndReport(
        member: Member,
        beneficiary: Beneficiary,
    ) = memberService.cancel(member, beneficiary.canceledAt ?: LocalDateTime.now())
        .then { reportInactivation(SUCCESS) }
        .then { logBeneficiaryActivatedOrInactivated(beneficiary, "inactivation") }
        .thenError { reportInactivation(FAILURE) }
        .thenError { logBeneficiaryNotActivatedOrNotOInactivate(beneficiary, it, "inactivation") }
        .map { beneficiary }


    private fun List<Beneficiary>.logBeneficiariesToActivate() {
        logger.info(
            "Start membership activation",
            "beneficiary_ids" to this.map { it.id },
            "person_ids" to this.map { it.personId },
            "total" to this.size,
        )
    }

    private fun List<Beneficiary>.logBeneficiariesToInactivate() {
        logger.info(
            "Start membership inactivation",
            "beneficiary_ids" to this.map { it.id },
            "person_ids" to this.map { it.personId },
            "total" to this.size,
        )
    }

    private fun logBeneficiaryActivatedOrInactivated(beneficiary: Beneficiary, flow: String) {
        logger.info(
            "Member $flow performed successfully",
            "beneficiary_id" to beneficiary.id,
            "person_id" to beneficiary.personId,
            "member_id" to beneficiary.memberId
        )
    }

    private fun logBeneficiaryNotActivatedOrNotOInactivate(
        beneficiary: Beneficiary,
        exception: Throwable,
        flow: String
    ) {
        logger.error(
            "Member $flow not happened due some error",
            "beneficiary_id" to beneficiary.id,
            "person_id" to beneficiary.personId,
            exception,
        )
    }

    private fun shouldExecuteCassiExpirationDateRoutine(): Boolean =
        FeatureService.get(
            FeatureNamespace.BUSINESS,
            "enable_cassi_expiration_date_routine",
            false
        )

    data class JobRecurrentResult(
        val successCount: Int,
        val errorCount: Int,
        val errors: Map<String, String>,
    ) {
        operator fun plus(other: JobRecurrentResult) =
            JobRecurrentResult(successCount + other.successCount, errorCount + other.errorCount, errors + other.errors)
    }
}
