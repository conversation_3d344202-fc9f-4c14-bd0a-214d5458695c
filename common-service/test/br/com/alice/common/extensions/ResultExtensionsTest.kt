package br.com.alice.common.extensions

import br.com.alice.common.core.exceptions.BadRequestException
import br.com.alice.common.core.exceptions.DuplicatedItemException
import br.com.alice.common.core.exceptions.InvalidArgumentException
import br.com.alice.common.core.exceptions.NotFoundException
import br.com.alice.common.core.exceptions.RfcException
import br.com.alice.common.core.suspend
import br.com.alice.common.helpers.verifyNone
import br.com.alice.common.helpers.verifyOnce
import br.com.alice.common.logging.Logger
import com.github.kittinunf.result.Kind
import com.github.kittinunf.result.Result
import com.github.kittinunf.result.failure
import com.github.kittinunf.result.isFailure
import com.github.kittinunf.result.success
import io.ktor.http.HttpStatusCode
import io.mockk.mockkObject
import io.mockk.verify
import kotlinx.coroutines.runBlocking
import org.assertj.core.api.Assertions.assertThat
import org.junit.Assert
import org.junit.jupiter.api.assertDoesNotThrow
import kotlin.test.Test
import kotlin.test.assertEquals
import kotlin.test.assertFailsWith
import kotlin.test.fail

class ResultExtensionsTest {

    @Test
    fun `#error - given any Exception in a lambda, should return a Result Failure of that object`() {
        val errorLambda: () -> Result<Any, Throwable> = {
            val exception = NotFoundException("Item not found")
            exception.failure()
        }

        val result = errorLambda()

        assertThat(result).isExactlyInstanceOf(Result.Failure::class.java)
        assertFailsWith(NotFoundException::class) { result.get() }
    }

    @Test
    fun `#error - given any Exception, should return a Result Failure of that object`() {
        val exception = NotFoundException("Item not found")
        val result = exception.failure()

        assertThat(result).isExactlyInstanceOf(Result.Failure::class.java)
        assertFailsWith(NotFoundException::class) { result.get() }
    }

    @Test
    fun `#mapFirst - given an empty list, should return a Result Failure of NotFoundException type`() {
        val result = Result.success(emptyList<Any>()).mapFirst()

        assertThat(result).isExactlyInstanceOf(Result.Failure::class.java)
        assertFailsWith(NotFoundException::class) { result.get() }
    }

    @Test
    fun `#mapFirst - given a valid list, should return a Result Success with the fist item`() {
        val firstItem = "First item"
        val result = Result.success(listOf(firstItem)).mapFirst()

        assertThat(result).isExactlyInstanceOf(Result.Success::class.java)
        assertThat(result.get()).isEqualTo(firstItem)
    }

    @Test
    fun `#foldError - given a Result error, with expected exception, should return Success lambda block result`() {
        val exception: Exception = NotFoundException("Error message", "test error")

        val result = Result.failure(exception).foldError(NotFoundException::class to { ex: Exception ->
            assertThat(ex).isEqualTo(exception)
            Result.success(ex)
        })

        assertThat(result).isExactlyInstanceOf(Result.Success::class.java)
        assertThat(result.get()).isEqualTo(exception)
    }

    @Test
    fun `#foldError - given a Result error, with expected exception, should return Failure lambda block result`() {
        val exception: Exception = NotFoundException("Error message", "test error")

        val result = Result.failure(exception).foldError(NotFoundException::class to { ex: Exception ->
            assertThat(ex).isEqualTo(exception)
            Result.failure(ex)
        })

        assertThat(result).isExactlyInstanceOf(Result.Failure::class.java)

        val resultException = assertFailsWith(NotFoundException::class) { result.get() }
        assertThat(resultException).isEqualTo(exception)
    }

    @Test
    fun `#foldError - given a Result error, with none expected exception, should not call the match block`() {
        val exception: Exception = NotFoundException("Error message", "test error")

        val result = Result.failure(exception).foldError(InvalidArgumentException::class to { ex: Exception ->
            Assert.fail("Should not reach this point")
            Result.failure(ex)
        })

        assertThat(result).isExactlyInstanceOf(Result.Failure::class.java)

        val resultException = assertFailsWith(NotFoundException::class) { result.get() }
        assertThat(resultException).isEqualTo(exception)
    }

    @Test
    fun `#foldError - given a Result success, should not call the match block`() {
        val success = "success text"

        val result = Result.success(success).foldError(Exception::class to { ex: Exception ->
            Assert.fail("Should not reach this point")
            Result.failure(ex)
        })

        assertThat(result).isExactlyInstanceOf(Result.Success::class.java)
        assertThat(result.get()).isEqualTo(success)
    }

    @Test
    fun `#coFoldError - given a Result error, with expected exception, should return Success lambda block result`() =
        runBlocking<Unit> {
            val exception: Exception = NotFoundException("Error message", "test error")

            val result = Result.failure(exception).coFoldError(NotFoundException::class to suspend() { ex: Exception ->
                assertThat(ex).isEqualTo(exception)
                Result.success(ex)
            })

            assertThat(result).isExactlyInstanceOf(Result.Success::class.java)
            assertThat(result.get()).isEqualTo(exception)
        }

    @Test
    fun `#coFoldError - given a Result error, with expected exception, should return Failure lambda block result`() =
        runBlocking<Unit> {
            val exception: Exception = NotFoundException("Error message", "test error")

            val result = Result.failure(exception).coFoldError(NotFoundException::class to suspend() { ex: Exception ->
                assertThat(ex).isEqualTo(exception)
                Result.failure(ex)
            })

            assertThat(result).isExactlyInstanceOf(Result.Failure::class.java)

            val resultException = assertFailsWith(NotFoundException::class) { result.get() }
            assertThat(resultException).isEqualTo(exception)
        }

    @Test
    fun `#coFoldError - given a Result error, with none expected exception, should not call the match block`() =
        runBlocking<Unit> {
            val exception: Exception = NotFoundException("Error message", "test error")

            val result =
                Result.failure(exception).coFoldError(InvalidArgumentException::class to suspend() { ex: Exception ->
                    Assert.fail("Should not reach this point")
                    Result.failure(ex)
                })

            assertThat(result).isExactlyInstanceOf(Result.Failure::class.java)

            val resultException = assertFailsWith(NotFoundException::class) { result.get() }
            assertThat(resultException).isEqualTo(exception)
        }

    @Test
    fun `#coFoldError - given a Result success, should not call the match block`() = runBlocking<Unit> {
        val success = "success text"

        val result = Result.success(success).coFoldError(Exception::class to suspend() { ex: Exception ->
            Assert.fail("Should not reach this point")
            Result.failure(ex)
        })

        assertThat(result).isExactlyInstanceOf(Result.Success::class.java)
        assertThat(result.get()).isEqualTo(success)
    }

    @Test
    fun `#foldNotFound - given a Result error, should call the match block and return expected string`() =
        runBlocking<Unit> {
            val success = "success text"

            val result = Result.failure(NotFoundException("Item not found"))
                .foldNotFound {
                    success.success()
                }

            assertThat(result).isExactlyInstanceOf(Result.Success::class.java)
            assertThat(result.get()).isEqualTo(success)
        }

    @Test
    fun `#foldNotFound - given a Result error, should call the match block and return expected error`() =
        runBlocking<Unit> {
            class ExpectedException :
                RfcException("expected_exception", "Validates whether this exception was created") {
                override val statusCode = HttpStatusCode.BadRequest
            }

            val result = Result.failure(NotFoundException("Item not found"))
                .foldNotFound {
                    ExpectedException().failure()
                }

            assertFailsWith(ExpectedException::class) { result.get() }
        }

    @Test
    fun `#foldNotFound - given a Result success, should not call the match block`() = runBlocking<Unit> {
        val success = "success text"

        val result = Result.success(success).foldNotFound {
            Assert.fail("Should not reach this point")
            Result.failure(Exception())
        }

        assertThat(result).isExactlyInstanceOf(Result.Success::class.java)
        assertThat(result.get()).isEqualTo(success)
    }

    @Test
    fun `#coFoldNotFound - given a Result error, should call the match block and return expected string`() =
        runBlocking<Unit> {
            val success = "success text"

            val result = Result.failure(NotFoundException("Item not found"))
                .coFoldNotFound {
                    success.success()
                }

            assertThat(result).isExactlyInstanceOf(Result.Success::class.java)
            assertThat(result.get()).isEqualTo(success)
        }

    @Test
    fun `#coFoldNotFound - given a Result error, should call the match block and return expected error`() =
        runBlocking<Unit> {
            class ExpectedException :
                RfcException("expected_exception", "Validates whether this exception was created") {
                override val statusCode = HttpStatusCode.BadRequest
            }

            val exception = NotFoundException("Item not found")

            val result = Result.failure(exception)
                .coFoldNotFound { ex ->
                    assertEquals(ex, exception)
                    ExpectedException().failure()
                }

            assertFailsWith(ExpectedException::class) { result.get() }
        }

    @Test
    fun `#coFoldNotFound - given a Result success, should not call the match block`() = runBlocking<Unit> {
        val success = "success text"

        val result = Result.success(success).coFoldNotFound {
            Assert.fail("Should not reach this point")
            Result.failure(Exception())
        }

        assertThat(result).isExactlyInstanceOf(Result.Success::class.java)
        assertThat(result.get()).isEqualTo(success)
    }

    @Test
    fun `#coFoldException - given a Result error, should call the match block and return expected string`() =
        runBlocking<Unit> {
            val success = "success text"
            val exception = NotFoundException()

            val result = Result.failure(exception)
                .coFoldException(NotFoundException::class) { ex ->
                    assertEquals(ex, exception)
                    success.success()
                }

            assertThat(result).isExactlyInstanceOf(Result.Success::class.java)
            assertThat(result.get()).isEqualTo(success)
        }

    @Test
    fun `#coFoldException - given a Result error, should call the match block and return expected string for list of exception`() =
        runBlocking<Unit> {
            val success = "success text"

            val result = Result.failure(NotFoundException())
                .coFoldException(NotFoundException::class, IllegalArgumentException::class) {
                    success.success()
                }

            assertThat(result).isExactlyInstanceOf(Result.Success::class.java)
            assertThat(result.get()).isEqualTo(success)

            val result2 = Result.failure(IllegalArgumentException())
                .coFoldException(RfcException::class, IllegalArgumentException::class) {
                    success.success()
                }

            assertThat(result2).isExactlyInstanceOf(Result.Success::class.java)
            assertThat(result2.get()).isEqualTo(success)
        }

    @Test
    fun `#coFoldException - given a Result error, should not call the match block when exception type is different`() =
        runBlocking<Unit> {

            val result = Result.failure(NotFoundException("Item not found"))
                .coFoldException(InvalidArgumentException::class) {
                    BadRequestException().failure()
                }

            assertFailsWith(NotFoundException::class) { result.get() }
        }

    @Test
    fun `#coFoldException - given a Result success, should not call the match block`() = runBlocking<Unit> {
        val success = "success text"

        val result = Result.success(success).coFoldException(InvalidArgumentException::class) {
            Assert.fail("Should not reach this point")
            Result.failure(Exception())
        }

        assertThat(result).isExactlyInstanceOf(Result.Success::class.java)
        assertThat(result.get()).isEqualTo(success)
    }


    @Test
    fun `#coFoldDuplicated - given a Result error, should call the match block and return expected string`() =
        runBlocking<Unit> {
            val success = "success text"
            val exception = DuplicatedItemException("Duplicated Item")

            val result = Result.failure(exception)
                .coFoldDuplicated { ex ->
                    assertEquals(ex, exception)
                    success.success()
                }

            assertThat(result).isExactlyInstanceOf(Result.Success::class.java)
            assertThat(result.get()).isEqualTo(success)
        }

    @Test
    fun `#coFoldDuplicated - given a Result error, should call the match block and return expected error`() =
        runBlocking<Unit> {
            class ExpectedException :
                RfcException("expected_exception", "Validates whether this exception was created") {
                override val statusCode = HttpStatusCode.BadRequest
            }

            val result = Result.failure(DuplicatedItemException("Duplicated Item"))
                .coFoldDuplicated {
                    ExpectedException().failure()
                }

            assertFailsWith(ExpectedException::class) { result.get() }
        }

    @Test
    fun `#coFoldDuplicated - given a Result success, should not call the match block`() = runBlocking<Unit> {
        val success = "success text"

        val result = Result.success(success).coFoldDuplicated {
            Assert.fail("Should not reach this point")
            Result.failure(Exception())
        }

        assertThat(result).isExactlyInstanceOf(Result.Success::class.java)
        assertThat(result.get()).isEqualTo(success)
    }

    @Test
    fun `#getOrNull - given a Result success, should just return the value`() = runBlocking<Unit> {
        val success = "success text"
        val response = Result.success(success).getOrNullIfNotFound()

        assertThat(response).isEqualTo(success)
    }

    @Test
    fun `#getOrNull - given a Result error with NotFoundException, should return null`() = runBlocking {
        val response = resultOf<Boolean, Throwable> { throw NotFoundException() }.getOrNullIfNotFound()

        assertThat(response).isNull()
    }

    @Test
    fun `#getOrNull - given a Result error with a generic Exception, should throw it`() = runBlocking<Unit> {
        val result = resultOf<Boolean, Throwable> { throw Exception() }

        assertFailsWith(Exception::class) { result.getOrNullIfNotFound() }
    }

    @Test
    fun `#coResultOf - given a valid response, should return a Result Success`() = runBlocking<Unit> {
        val success = "success text"
        val result = coResultOf<String, Throwable> { success }

        assertThat(result).isExactlyInstanceOf(Result.Success::class.java)
        assertThat(result.get()).isEqualTo(success)
    }

    @Test
    fun `#coResultOf - throwing an exception inside lambda, should return a Result Failure()`() = runBlocking<Unit> {
        class ExpectedException : RfcException("expected_exception", "Validates whether this exception was created") {
            override val statusCode = HttpStatusCode.Conflict
        }

        val result = coResultOf<String, Throwable> { throw ExpectedException() }
        assertFailsWith(ExpectedException::class) { result.get() }
    }

    @Test
    fun `#catchResult - given a valid success result, returns it self`() = runBlocking<Unit> {
        val text = "success text"
        val result = catchResult<String, Throwable> { text.success() }

        assertThat(result).isExactlyInstanceOf(Result.Success::class.java)
        assertThat(result.get()).isEqualTo(text)
    }

    @Test
    fun `#catchResult - throwing an exception inside lambda, should return a Result Failure()`() = runBlocking<Unit> {
        class ExpectedException : RfcException("expected_exception", "Validates whether this exception was created") {
            override val statusCode = HttpStatusCode.BadRequest
        }

        val result = catchResult<String, Throwable> { throw ExpectedException() }
        assertFailsWith(ExpectedException::class) { result.get() }
    }

    @Test
    fun `#mapPair - given a function, should return the Result of it paired with response`() = runBlocking<Unit> {
        val func: (String) -> String = { "U" }
        val result = Result.success("V").mapPair(func)
        val expectedResult = Pair("U", "V")

        assertThat(result).isExactlyInstanceOf(Result.Success::class.java)
        assertThat(result.get()).isEqualTo(expectedResult)
    }

    @Test
    fun `#flatMapTo - given a Result of list, return the result of the function applied to list items`() =
        runBlocking<Unit> {
            val list = Result.success(listOf("V", "V"))
            val func = { v: String -> v.lowercase().success() }
            val result = list.flatMapEach(function = func)

            assertThat(result).isExactlyInstanceOf(Result.Success::class.java)
            assertThat(result.get()).isEqualTo(listOf("v", "v"))
        }

    @Test
    fun `#flatMapTo - when there's an Result Failure and override exception`() = runBlocking<Unit> {
        val list = Result.success(listOf("OK", "error"))
        val overrideException = NotFoundException()
        val func =
            { v: String -> if (v == "error") IllegalArgumentException("").failure() else v.lowercase().success() }
        val result = list.flatMapEach(overrideException, func)

        assertThat(result).isExactlyInstanceOf(Result.Failure::class.java)
        assertFailsWith(overrideException::class) { result.get() }
    }

    @Test
    fun `#flatMapTo - when there's an Result Failure and no override exception`() = runBlocking<Unit> {
        val list = Result.success(listOf("OK", "error"))
        val func =
            { v: String -> if (v == "error") IllegalArgumentException("").failure() else v.lowercase().success() }
        val result = list.flatMapEach(function = func)

        assertThat(result).isExactlyInstanceOf(Result.Failure::class.java)
        assertFailsWith(IllegalArgumentException::class) { result.get() }
    }

    @Test
    fun `#mapEachNotNull - given a Result of list, return the result of the function applied to list items without nullables`() =
        runBlocking<Unit> {
            val list = Result.success(listOf("V", "X"))
            val func = { v: String -> v.takeIf { it == "V" } }
            val result = list.mapEachNotNull { func(it) }

            assertThat(result).isExactlyInstanceOf(Result.Success::class.java)
            assertThat(result.get()).isEqualTo(listOf("V"))
        }

    @Test
    fun `#mapEachNotNull - given a Result of list, return empty list if the function applied only return nulls`() =
        runBlocking<Unit> {
            val list = Result.success(listOf("V", "X"))
            val func = { v: String -> v.takeIf { it == "A" } }
            val result = list.mapEachNotNull { func(it) }

            assertThat(result).isExactlyInstanceOf(Result.Success::class.java)
            assertThat(result.get()).isEqualTo(emptyList<String>())
        }

    @Test
    fun `#pmapEachNotNull - given a Result of list, return the result of the function applied to list items without nullables`() =
        runBlocking<Unit> {
            val list = Result.success(listOf("V", "X"))
            val func = { v: String -> v.takeIf { it == "V" } }
            val result = list.pmapEachNotNull { func(it) }

            assertThat(result).isExactlyInstanceOf(Result.Success::class.java)
            assertThat(result.get()).isEqualTo(listOf("V"))
        }

    @Test
    fun `#pmapEachNotNull - given a Result of list, return empty list if the function applied only return nulls`() =
        runBlocking<Unit> {
            val list = Result.success(listOf("V", "X"))
            val func = { v: String -> v.takeIf { it == "A" } }
            val result = list.pmapEachNotNull { func(it) }

            assertThat(result).isExactlyInstanceOf(Result.Success::class.java)
            assertThat(result.get()).isEqualTo(emptyList<String>())
        }

    @Test
    fun `#plus - given two Results Success containing a list should return a merged list`() {
        val result = Result.success(listOf("A", "B"))
        val otherResult = Result.success(listOf("C", "D"))

        val mergedResult = result + otherResult
        assertThat(mergedResult.kind).isEqualTo(Kind.Success)
        assertThat(mergedResult.get()).isEqualTo(listOf("A", "B", "C", "D"))
    }

    @Test
    fun `#plus - given a Result containing a Success list and another Result Error should return an Error`() {
        val error = RuntimeException()

        val result = Result.success(listOf("A", "B"))
        val otherResult = Result.failure(error)

        val mergedResult = result + otherResult
        assertThat(mergedResult.kind).isEqualTo(Kind.Failure)
        assertThat(mergedResult.component2()).isEqualTo(error)
    }

    @Test
    fun `#plus - given a Result Error and another one containing a Result Success list should return an Error`() {
        val error = RuntimeException()

        val result = Result.failure(error)
        val otherResult = Result.success(listOf("B", "C"))

        val mergedResult = result + otherResult
        assertThat(mergedResult.kind).isEqualTo(Kind.Failure)
        assertThat(mergedResult.component2()).isEqualTo(error)
    }

    @Test
    fun `#plus - given two Result Errors should return the Error from the first Result`() {
        val firstError = RuntimeException()
        val secondError = RuntimeException()

        val result = Result.of<List<String>, Throwable> { throw firstError }
        val otherResult = Result.of<List<String>, Throwable> { throw secondError }

        val mergedResult = result + otherResult
        assertThat(mergedResult.kind).isEqualTo(Kind.Failure)
        assertThat(mergedResult.component2()).isEqualTo(firstError)
    }

    @Test
    fun `#foldBoolean - should return converted value when value is true`() {
        val trueResult = Result.of<Boolean, Throwable> { true }

        val result = trueResult.foldBoolean({ "foo" }, { "bar" })
        assertThat(result).isEqualTo("foo")
    }

    @Test
    fun `#foldBoolean - should return converted value when value is false`() {
        val trueResult = Result.of<Boolean, Throwable> { false }

        val result = trueResult.foldBoolean({ "foo" }, { "bar" })
        assertThat(result).isEqualTo("bar")
    }

    @Test
    fun `#foldBoolean - should throw error`() {
        val result = Result.of<Boolean, Throwable> { throw RuntimeException() }

        assertFailsWith(RuntimeException::class) { result.foldBoolean({ "foo" }, { "bar" }) }
    }

    @Test
    fun `#flatMapFalse - should return value if result value is true`() = runBlocking<Unit> {
        val falseResult = Result.of<Boolean, Throwable> { false }
        val func = { Result.of<Boolean, Throwable> { false } }
        val result = falseResult.flatMapFalse { func() }

        assertThat(result).isExactlyInstanceOf(Result.Success::class.java)
        assertThat(result.get()).isEqualTo(false)
    }

    @Test
    fun `#flatMapFalse - should execute function if result value is false`() = runBlocking<Unit> {
        val trueResult = Result.of<Boolean, Throwable> { true }
        val func = { Result.of<Boolean, Throwable> { false } }
        val result = trueResult.flatMapFalse { func() }

        assertThat(result).isExactlyInstanceOf(Result.Success::class.java)
        assertThat(result.get()).isEqualTo(true)
    }

    @Test
    fun `#flatMapFalse - should not execute function if result throw error`() = runBlocking<Unit> {
        val error = RuntimeException()
        val trueResult = Result.of<Boolean, Throwable> { throw error }
        val func = { Result.of<Boolean, Throwable> { false } }
        val result = trueResult.flatMapFalse { func() }

        assertThat(result.kind).isEqualTo(Kind.Failure)
        assertThat(result.component2()).isEqualTo(error)
    }

    @Test
    fun `#flatMapFalse - should execute function if result value is false and throw error`() = runBlocking<Unit> {
        val error = RuntimeException()
        val falseResult = Result.of<Boolean, Throwable> { false }
        val func = { Result.of<Boolean, Throwable> { throw error } }
        val result = falseResult.flatMapFalse { func() }

        assertThat(result.kind).isEqualTo(Kind.Failure)
        assertThat(result.component2()).isEqualTo(error)
    }

    @Test
    fun `#reduce - should reduce to accumulator parameter using map`() {
        val expected = mapOf(
            "even" to listOf(2, 4, 6, 8),
            "odd" to listOf(1, 3, 5, 7)
        )

        val baseResult = listOf(1, 2, 3, 4, 5, 6, 7, 8).success()

        val result = baseResult.reduce(mapOf<String, List<Int>>()) { acc, number ->
            val key = if (number.mod(2) == 0) "even" else "odd"
            val items = acc[key] ?: emptyList()
            acc.plus(key to items.plus(number))
        }

        assertThat(result.kind).isEqualTo(Kind.Success)
        assertThat(result.get()).isEqualTo(expected)
    }

    @Test
    fun `#reduce - should reduce to accumulator parameter using int`() {
        val expected = 10

        val baseResult = listOf(1, 2, 3, 4).success()

        val result = baseResult.reduce(0) { acc, number ->
            acc + number
        }

        assertThat(result.kind).isEqualTo(Kind.Success)
        assertThat(result.get()).isEqualTo(expected)
    }

    @Test
    fun `#flatMapWithTimeTaken - should return a pair with the result and the time taken on success`() {
        val result = Result.success("success")
        val transform = { s: String ->
            Thread.sleep(100)
            s.length.success()
        }

        val pair = result.flatMapWithTimeTaken(transform)

        assertThat(pair.first.get()).isEqualTo(7)
        assertThat(pair.second).isGreaterThanOrEqualTo(100) //all we can be sure of here
    }

    @Test
    fun `#flatMapWithTimeTaken - should return the failure with the time taken even if transform fails`() {
        val result = Result.success("success")
        val transform = { s: String ->
            Thread.sleep(100)
            if (true) throw RuntimeException("I DON'T FEEL SO GOOD") else s.length.success()
        }

        val pair = result.flatMapWithTimeTaken(transform)

        assertThat(pair.first.isFailure())
        assertThat(pair.first.failure() is RuntimeException)
    }

    @Test
    fun `#flatMapWithTimeTaken - should not run transform if this is a failure`() {
        val result = Result.failure(RuntimeException("I DON'T FEEL SO GOOD"))
        val transform = { s: String ->
            if (true) fail("Should not run this") else s.length.success()
        }

        val pair = result.flatMapWithTimeTaken(transform)

        assertThat(pair.first.isFailure())
        assertThat(pair.first.failure() is RuntimeException)
        assertThat(pair.second).isEqualTo(0)
    }

    @Test
    fun `#foldWithTime - should pass the time argument when success or failure`() {
        val successFun = { s: String, time: Long -> s.length + time }
        val failureFun = { e: Throwable, time: Long -> (e.message?.length ?: 0) + time }

        val resultWithTime = Result.success("success") to 100L
        val int = resultWithTime.foldWithTime(successFun) { _, _ -> fail("Should not run this") }
        assertThat(int).isEqualTo(107)

        val resultWithTimeFailure = Result.failure(RuntimeException("I DON'T FEEL SO GOOD")) to 100L
        val intFailure = resultWithTimeFailure.foldWithTime({ _, _ -> fail("Should not run this") }, failureFun)
        assertThat(intFailure).isEqualTo(120)
    }

    @Test
    fun `#thenLogOnSuccess - should log info`() {
        mockkObject(Logger)
        val message = "Success message"

        Result.success()
            .thenLogOnSuccess { message }

        verifyOnce { Logger.info(message, *arrayOf<Any>("status" to "success")) }
    }

    @Test
    fun `#thenLogOnSuccess - should log info with params`() {
        mockkObject(Logger)
        val message = "Success message"
        val params = mapOf("param1" to "value1", "param2" to "value2")

        Result.success()
            .thenLogOnSuccess(params) { message }

        verifyOnce { Logger.info(message, *arrayOf<Any>("status" to "success"), params) }
    }

    @Test
    fun `#thenLogOnFailure - should log error`() {
        mockkObject(Logger)
        val message = "Failure message"
        val throwable = RuntimeException("Error")

        throwable.failure()
            .thenLogOnFailure { message }

        verifyOnce { Logger.error(message, *arrayOf<Any>("status" to "failure", throwable)) }
    }

    @Test
    fun `#thenLogOnFailure - should log error with params`() {
        mockkObject(Logger)
        val message = "Failure message"
        val throwable = RuntimeException("Error")
        val params = mapOf("param1" to "value1", "param2" to "value2")

        throwable.failure()
            .thenLogOnFailure(params) { message }

        verifyOnce { Logger.error(message, *arrayOf<Any>("status" to "failure", throwable), params) }
    }

    @Test
    fun `thenLogOnFailure - should log error case when exception is the same declared`() {
        mockkObject(Logger)
        val throwable = RuntimeException("Error")

        throwable.failure()
            .thenLogOnFailure(RuntimeException::class) { "Log message" }

        verifyOnce { Logger.error("Log message", *arrayOf<Any>("status" to "failure", throwable)) }
    }

    @Test
    fun `thenLogOnFailure - shouldn't log case when exception is not the same declared`() {
        mockkObject(Logger)
        val throwable = RuntimeException("Error")

        throwable.failure()
            .thenLogOnFailure(IllegalArgumentException::class) { "Log message" }

        verifyNone { Logger.error("Log message", *arrayOf<Any>("status" to "failure", throwable)) }
    }

    @Test
    fun `#thenLog - should log info`() {
        mockkObject(Logger)

        Result.success()
            .thenLog { "Log message" }

        verifyOnce { Logger.info("Log message", *arrayOf<Any>("status" to "success")) }
    }

    @Test
    fun `#thenLog - should log error`() {
        mockkObject(Logger)
        val throwable = RuntimeException("Error")

        throwable.failure()
            .thenLog { "Log message" }

        verifyOnce { Logger.error("Log message", *arrayOf<Any>("status" to "failure", throwable)) }
    }
}
