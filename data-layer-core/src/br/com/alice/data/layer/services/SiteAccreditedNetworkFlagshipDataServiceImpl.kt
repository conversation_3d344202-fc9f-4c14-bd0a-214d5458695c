package br.com.alice.data.layer.services

import br.com.alice.data.layer.converters.SiteAccreditedNetworkFlagshipConverter
import br.com.alice.data.layer.models.SiteAccreditedNetworkFlagship
import br.com.alice.data.layer.pipelines.DatabasePipelineFactory
import br.com.alice.data.layer.tables.SiteAccreditedNetworkFlagshipTable

class SiteAccreditedNetworkFlagshipDataServiceImpl internal constructor(factory: DatabasePipelineFactory) :
    BaseDataServiceImpl<SiteAccreditedNetworkFlagship>(factory.get<SiteAccreditedNetworkFlagship, SiteAccreditedNetworkFlagshipTable>(
        converter = SiteAccreditedNetworkFlagshipConverter()
    )), SiteAccreditedNetworkFlagshipDataService
