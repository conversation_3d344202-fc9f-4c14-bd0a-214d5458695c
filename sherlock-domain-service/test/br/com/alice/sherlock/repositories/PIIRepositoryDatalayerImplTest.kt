package br.com.alice.sherlock.repositories

import br.com.alice.authentication.UserType
import br.com.alice.common.RangeUUID
import br.com.alice.common.UpdatedBy
import br.com.alice.common.core.PersonId
import br.com.alice.common.core.TableId
import br.com.alice.common.core.extensions.toUUID
import br.com.alice.common.data.dsl.matchers.ResultAssert.Companion.assertThat
import br.com.alice.common.models.Gender
import br.com.alice.common.models.Sex
import br.com.alice.common.range
import br.com.alice.common.storage.AliceFile
import br.com.alice.data.layer.models.PIIField
import br.com.alice.data.layer.models.Person
import br.com.alice.data.layer.models.PersonModel
import br.com.alice.data.layer.services.PersonSherlockDataService
import br.com.alice.sherlock.models.QueryResultFile
import com.github.kittinunf.result.success
import io.mockk.coEvery
import io.mockk.mockk
import io.mockk.unmockkStatic
import kotlinx.coroutines.runBlocking
import java.time.LocalDateTime
import java.util.UUID
import kotlin.test.BeforeTest
import kotlin.test.Test

class PIIRepositoryDatalayerImplTest {
    private val personDataService: PersonSherlockDataService = mockk()
    private val piiRepository: PIIRepositoryDatalayerImpl = PIIRepositoryDatalayerImpl(personDataService)

    private val person1 = buildPerson()
    private val person2 = buildPerson()
    private val person3 = buildPerson()
    private val people = listOf(person1, person2, person3)
    private val peopleIds = listOf(person1.id, person2.id, person3.id)

    private val person1HI = PersonNonPiiToken(RangeUUID.generate(RangeUUID.PERSON_NON_PII_TOKEN_RANGE))
    private val person2HI = PersonNonPiiToken(RangeUUID.generate(RangeUUID.PERSON_NON_PII_TOKEN_RANGE))
    private val person3HI = PersonNonPiiToken(RangeUUID.generate(RangeUUID.PERSON_NON_PII_TOKEN_RANGE))
    private val tokens = listOf(person1HI.id, person2HI.id, person3HI.id).map { it.toString() }

    @BeforeTest
    fun setup() {
        unmockkStatic(UUID::class)
    }

    @Test
    fun `#getPIIData should return the pii field values`() = runBlocking {
        val piiFields = listOf(PIIField.CPF, PIIField.Email)
        coEvery { personDataService.findByIds(tokens.map{ it.toUUID() }) } returns people.success()
        coEvery { personDataService.getPersonIds(tokens.map{ it.toUUID() }) } returns peopleIds.success()

        val expectedQueryResultFile = QueryResultFile(
            columns = mapOf(
                PIIField.CPF.name to listOf(person1.nationalId, person2.nationalId, person3.nationalId),
                PIIField.Email.name to listOf(person1.email, person2.email, person3.email)
            )
        )

        val result = piiRepository.getPIIData(tokens, piiFields)

        assertThat(result).isSuccessWithData(expectedQueryResultFile)
    }

    @Test
    fun `#getPIIData should return the pii field values when we have duplicated ids on positions greater than the number of distinct ids`() = runBlocking {
        val piiFields = listOf(PIIField.CPF, PIIField.Email)
        val duplicatedTokens = listOf(person1HI.id, person2HI.id, person1HI.id, person3HI.id,  person3HI.id).map { it.toString() }
        val distinctPeople = listOf(person1, person2, person3)
        val duplicatedPeopleIds = listOf(person1.id, person2.id, person1.id, person3.id,  person3.id)

        coEvery { personDataService.findByIds(duplicatedTokens.map{ it.toUUID() }) } returns distinctPeople.success()
        coEvery { personDataService.getPersonIds(duplicatedTokens.map{ it.toUUID() }) } returns duplicatedPeopleIds.success()

        val expectedQueryResultFile = QueryResultFile(
            columns = mapOf(
                PIIField.CPF.name to listOf(person1.nationalId, person2.nationalId, person3.nationalId, person1.nationalId, person3.nationalId),
                PIIField.Email.name to listOf(person1.email, person2.email, person3.email, person1.email, person3.email)
            )
        )

        val result = piiRepository.getPIIData(duplicatedTokens, piiFields)

        assertThat(result).isSuccessWithData(expectedQueryResultFile)
    }

    @Test
    fun `#getPIIData should return the pii field values when we have duplicated PersonIds`() = runBlocking {
        val piiFields = listOf(PIIField.CPF, PIIField.Email)
        val duplicatedTokens = listOf(person1.id, person1.id, person1.id, person1.id, person1.id).map { it.toString() }
        val distinctPeople = listOf(person1)
        val duplicatedPeopleIds = listOf(person1.id, person1.id, person1.id, person1.id, person1.id)

        coEvery { personDataService.findByIds(duplicatedTokens.map{ it.toUUID() }) } returns distinctPeople.success()
        coEvery { personDataService.getPersonIds(duplicatedTokens.map{ it.toUUID() }) } returns duplicatedPeopleIds.success()

        val expectedQueryResultFile = QueryResultFile(
            columns = mapOf(
                PIIField.CPF.name to listOf(person1.nationalId, person1.nationalId, person1.nationalId, person1.nationalId, person1.nationalId),
                PIIField.Email.name to listOf(person1.email, person1.email, person1.email, person1.email, person1.email)
            )
        )

        val result = piiRepository.getPIIData(duplicatedTokens, piiFields)

        assertThat(result).isSuccessWithData(expectedQueryResultFile)
    }

    @Test
    fun `#getPIIData should send dateOfBirth only with dates`() = runBlocking {
        val personWithBirthDay = buildPerson(dateOfBirth = LocalDateTime.now())
        val people = listOf(personWithBirthDay)
        val peopleIds = listOf(personWithBirthDay.id)

        val personHI = PersonNonPiiToken(RangeUUID.generate(RangeUUID.PERSON_NON_PII_TOKEN_RANGE))
        val tokens = listOf(personHI.id).map { it.toString() }

        val piiFields = listOf(PIIField.BirthDate)
        coEvery { personDataService.findByIds(tokens.map{ it.toUUID() }) } returns people.success()
        coEvery { personDataService.getPersonIds(tokens.map{ it.toUUID() }) } returns peopleIds.success()

        val expectedQueryResultFile = QueryResultFile(
            columns = mapOf(
                PIIField.BirthDate.name to listOf(
                    personWithBirthDay.dateOfBirth?.toLocalDate().toString(),
                ),
            )
        )

        val result = piiRepository.getPIIData(tokens, piiFields)

        assertThat(result).isSuccessWithData(expectedQueryResultFile)
    }

    fun buildPerson(
        personId: PersonId = PersonId(),
        firstName: String = "José",
        lastName: String = "da Silva",
        nickName: String? = "Zé",
        socialName: String? = null,
        socialFirstName: String? = null,
        socialLastName: String? = null,
        nationalId: String = "609.048.950-68",
        email: String = "<EMAIL>",
        sex: Sex? = null,
        gender: Gender? = null,
        dateOfBirth: LocalDateTime? = null,
        tags: List<String>? = null,
        identityDocument: String? = "111111112",
        identityDocumentIssuingBody: String? = "SSP-SP",
        opportunityId: UUID? = null,
        leadId: UUID? = null,
        userType: UserType = UserType.MEMBER,
        phoneNumber: String? = null,
        profilePicture: AliceFile? = null,
        acceptedTermsAt: LocalDateTime? = null,
        updatedBy: UpdatedBy? = null,
        piiInternalCode: String = Person.generatePiiInternalCode(),
    ) = PersonModel(
        id = personId,
        firstName = firstName,
        lastName = lastName,
        nickName = nickName,
        socialName = socialName,
        socialFirstName = socialFirstName,
        socialLastName = socialLastName,
        nationalId = nationalId,
        email = email,
        sex = sex,
        gender = gender,
        dateOfBirth = dateOfBirth,
        tags = tags,
        identityDocument = identityDocument,
        identityDocumentIssuingBody = identityDocumentIssuingBody,
        opportunityId = opportunityId,
        leadId = leadId,
        userType = userType,
        phoneNumber = phoneNumber,
        profilePicture = profilePicture,
        acceptedTermsAt = acceptedTermsAt,
        updatedBy = updatedBy,
        piiInternalCode = piiInternalCode
    )
}

class PersonNonPiiToken(override val id: UUID) : TableId() {

    init {
        require(id.range() == RangeUUID.PERSON_NON_PII_TOKEN_RANGE) {
            "id.range has to be in PERSON_NON_PII_TOKEN_RANGE, id=$id"
        }
    }

    override fun toString(): String =
        "PersonNonPiiToken"

    override fun equals(other: Any?): Boolean {
        if (this === other) return true
        if (javaClass != other?.javaClass) return false

        other as PersonNonPiiToken

        if (id != other.id) return false

        return true
    }

    override fun hashCode(): Int =
        id.hashCode()

}
