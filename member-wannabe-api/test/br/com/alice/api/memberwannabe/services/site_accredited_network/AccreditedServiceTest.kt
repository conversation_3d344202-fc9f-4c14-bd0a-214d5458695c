package br.com.alice.api.memberwannabe.services.site_accredited_network

import br.com.alice.api.memberwannabe.converters.AccreditedLightweightConverter
import br.com.alice.api.memberwannabe.converters.AccreditedResponseConverter
import br.com.alice.api.memberwannabe.models.AccreditedWithLocationAndRemotesResponse
import br.com.alice.api.memberwannabe.models.AccreditedWithLocationLightweight
import br.com.alice.atlas.model.Accredited
import br.com.alice.atlas.model.AccreditedCategory
import br.com.alice.atlas.model.AccreditedLatLon
import br.com.alice.atlas.model.LocationType
import br.com.alice.atlas.model.RemoteAccredited
import br.com.alice.atlas.model.RemoteAccreditedList
import br.com.alice.common.RangeUUID
import br.com.alice.common.extensions.resultOf
import br.com.alice.common.featureflag.withFeatureFlag
import br.com.alice.common.helpers.MockedTestHelper
import br.com.alice.common.helpers.coVerifyNone
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.data.layer.models.FeatureNamespace
import br.com.alice.data.layer.models.SiteAccreditedNetwork
import com.github.kittinunf.result.failure
import com.github.kittinunf.result.isFailure
import com.github.kittinunf.result.isSuccess
import com.github.kittinunf.result.success
import io.mockk.coEvery
import io.mockk.mockk
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.assertThrows
import kotlin.test.Test
import kotlin.test.assertEquals
import kotlin.test.assertFailsWith
import kotlin.test.assertTrue
import br.com.alice.atlas.services.AccreditedService as AtlasAccreditedService

class AccreditedServiceTest: MockedTestHelper() {
    private val atlasAccreditedService = mockk<AtlasAccreditedService>()
    private val accreditedLocationService = mockk<AccreditedLocationService>()
    private val findAccreditedService = mockk<FindAccreditedService>()
    private val accreditedAndPriorityListLogicService = mockk<AccreditedAndPriorityListLogicService>()
    private val accreditedFilterService = mockk<AccreditedFilterService>()

    private val accreditedService = AccreditedService(
        atlasAccreditedService,
        accreditedLocationService,
        findAccreditedService,
        accreditedAndPriorityListLogicService,
        accreditedFilterService,
    )

    private val lat = "-23.5718116"
    private val lon = "-46.6953119"
    private val placeId = "ChIJgUbEo8cfqokR5lP9_Wh_DaM"
    private val radiusInMeters = 10000
    private val siteAccreditedNetworkId = RangeUUID.generate()
    private val siteAccreditedNetworkExclusiveId = RangeUUID.generate()
    private val category = "MATERNITY"
    private val flagName = "site_accredited_network_id_default_filter"
    private val siteAccreditedNetwork = SiteAccreditedNetwork(
        id = siteAccreditedNetworkId,
        bundleIds = listOf(RangeUUID.generate()),
    )
    private val siteAccreditedNetworkExclusive = SiteAccreditedNetwork(
        id = siteAccreditedNetworkExclusiveId,
        bundleIds = listOf(RangeUUID.generate()),
    )
    private val categories = listOf(AccreditedCategory.valueOf(category))

    @Test
    fun `#findBy should filter without category and site accredited network and return a list of accredited`() = runBlocking {
        withFeatureFlag(
            FeatureNamespace.MEMBER_WANNABE,
            flagName,
            siteAccreditedNetworkExclusiveId.toString(),
        ) {
            val accredited = Accredited(
                id = RangeUUID.generate(),
                name = "Hospital",
                category = AccreditedCategory.HOSPITAL,
                referencedId = RangeUUID.generate(),
            )

            val itemsResponse = AccreditedResponseConverter.fromAccredited(accredited)
            val required = AccreditedWithLocationAndRemotesResponse(
                items = listOf(itemsResponse),
                defaultItems = emptyList(),
                totalItems = 1,
            )
            val filter = AtlasAccreditedService.Filter(
                geoLocation = AtlasAccreditedService.GeoLocationFilter(
                    radiusInMeters,
                    lat,
                    lon
                ),
                siteAccreditedNetwork = siteAccreditedNetworkExclusive,
                limit = 10,
                offset = 0,
            )

            coEvery {
                accreditedFilterService.createFilter(
                    siteAccreditedNetworkId = null,
                    categories = null,
                    specialityId = null,
                    lat = lat,
                    lon = lon,
                    radiusInMeters = radiusInMeters,
                    limit = 10,
                    offset = 0,
                )
            } returns filter.success()
            coEvery { atlasAccreditedService.findBy(filter) } returns resultOf { listOf(accredited) }
            coEvery {
                atlasAccreditedService.countBy(filter)
            } returns resultOf { 1 }
            coEvery {
                accreditedLocationService.findLocation(placeId)
            } returns AccreditedLatLon(
                lat = lat,
                lon = lon,
                type = LocationType.BUILDING,
                placeId = placeId,
            ).success()
            coEvery {
                accreditedAndPriorityListLogicService.getPrioritizedAccreditedList(
                    range = IntRange(0, 9),
                    accredited = listOf(accredited),
                    placeId = placeId,
                    categories = null,
                    specialityId = null,
                    siteAccreditedNetworkId = null,
                    lat = lat,
                    lon = lon,
                    radiusInMeters = radiusInMeters,
                )
            } returns listOf(accredited).success()

            // When
            val result = accreditedService.findBy(
                siteAccreditedNetworkId = null,
                categories = null,
                specialtyId = null,
                placeId = placeId,
                lat = "",
                lon = "",
                radiusInMeters = radiusInMeters,
                isDefaultSearch = false,
                range = IntRange(0, 9),
            )

            // Then
            assertTrue(result.isSuccess())
            assertEquals(required, result.get())

            coVerifyOnce { accreditedFilterService.createFilter(any(), any(), any(), any(), any(), any(), any(), any()) }
            coVerifyOnce { atlasAccreditedService.findBy(any()) }
            coVerifyOnce { atlasAccreditedService.countBy(any()) }
        }
    }

    @Test
    fun `#findBy should return an empty list of accredited`() = runBlocking {
        val remoteAccredited = RemoteAccredited(
            name = "Hospital",
            referencedId = RangeUUID.generate(),
        )
        val remoteAccreditedList = RemoteAccreditedList(listOf(remoteAccredited))
        val required = AccreditedWithLocationAndRemotesResponse(
            items = emptyList(),
            defaultItems = listOf(remoteAccredited),
            totalItems = 1,
        )
        val filter = AtlasAccreditedService.Filter(
            categories = listOf(AccreditedCategory.MATERNITY),
            geoLocation = AtlasAccreditedService.GeoLocationFilter(
                radiusInMeters,
                lat,
                lon
            ),
            siteAccreditedNetwork = siteAccreditedNetwork,
            limit = 10,
            offset = 0,
        )

        coEvery {
            accreditedFilterService.createFilter(
                siteAccreditedNetworkId = siteAccreditedNetworkId,
                categories = listOf(AccreditedCategory.valueOf(category)),
                specialityId = null,
                lat = lat,
                lon = lon,
                radiusInMeters = radiusInMeters,
                limit = 10,
                offset = 0,
            )
        } returns filter.success()
        coEvery { atlasAccreditedService.findBy(filter) } returns resultOf { emptyList() }
        coEvery { atlasAccreditedService.findRemotes(filter) } returns resultOf { remoteAccreditedList }
        coEvery {
            atlasAccreditedService.countBy(filter)
        } returns resultOf { 1 }
        coEvery {
            accreditedLocationService.findLocation(placeId)
        } returns AccreditedLatLon(
            lat = lat,
            lon = lon,
            type = LocationType.BUILDING,
            placeId = placeId,
        ).success()
        coEvery {
            accreditedAndPriorityListLogicService.getPrioritizedAccreditedList(
                range = IntRange(0, 9),
                accredited = emptyList(),
                placeId = placeId,
                categories = categories,
                specialityId = null,
                siteAccreditedNetworkId = siteAccreditedNetworkId,
                lat = lat,
                lon = lon,
                radiusInMeters = radiusInMeters,
            )
        } returns emptyList<Accredited>().success()

        // When
        val result = accreditedService.findBy(
            siteAccreditedNetworkId = siteAccreditedNetworkId,
            categories = listOf(AccreditedCategory.valueOf(category)),
            specialtyId = null,
            placeId = placeId,
            lat = "",
            lon = "",
            radiusInMeters = radiusInMeters,
            isDefaultSearch = false,
            range = IntRange(0, 9),
        )

        // Then
        assertTrue(result.isSuccess())
        assertEquals(required, result.get())

        coVerifyOnce { accreditedFilterService.createFilter(any(), any(), any(), any(), any(), any(), any(), any()) }
        coVerifyOnce { atlasAccreditedService.findBy(any()) }
        coVerifyOnce { atlasAccreditedService.findRemotes(any()) }
        coVerifyOnce { atlasAccreditedService.countBy(any()) }
    }

    @Test
    fun `#findBy fail when try to get site accredited network then should return failure`() = runBlocking {
        coEvery {
            accreditedFilterService.createFilter(
                siteAccreditedNetworkId = siteAccreditedNetworkId,
                categories = listOf(AccreditedCategory.valueOf(category)),
                specialityId = null,
                lat = lat,
                lon = lon,
                radiusInMeters = radiusInMeters,
            )
        } returns IllegalArgumentException("Invalid site accredited network id").failure()
        coEvery {
            accreditedLocationService.findLocation(placeId)
        } returns AccreditedLatLon(
            lat = lat,
            lon = lon,
            type = LocationType.BUILDING,
            placeId = placeId,
        ).success()

        // When
        val result = accreditedService.findBy(
            siteAccreditedNetworkId = siteAccreditedNetworkId,
            categories = listOf(AccreditedCategory.valueOf(category)),
            specialtyId = null,
            placeId = placeId,
            lat = "",
            lon = "",
            radiusInMeters = radiusInMeters,
            isDefaultSearch = false,
        )

        // Then
        assertTrue(result.isFailure())
        assertThrows<IllegalArgumentException> { result.get() }

        coVerifyNone { atlasAccreditedService.findBy(any()) }
    }

    @Test
    fun `#findBy fail when try to get accredited list then should return failure`() = runBlocking {
        withFeatureFlag(
            FeatureNamespace.MEMBER_WANNABE,
            flagName,
            siteAccreditedNetworkExclusiveId.toString(),
        ) {
            coEvery {
                accreditedFilterService.createFilter(
                    siteAccreditedNetworkId = null,
                    categories = null,
                    specialityId = null,
                    lat = lat,
                    lon = lon,
                    radiusInMeters = radiusInMeters,
                )
            } returns AtlasAccreditedService.Filter(
                categories = null,
                geoLocation = AtlasAccreditedService.GeoLocationFilter(
                    radiusInMeters,
                    lat,
                    lon
                ),
                siteAccreditedNetwork = siteAccreditedNetworkExclusive,
            ).success()
            coEvery {
                atlasAccreditedService.findBy(
                    AtlasAccreditedService.Filter(
                        geoLocation = AtlasAccreditedService.GeoLocationFilter(
                            radiusInMeters,
                            lat,
                            lon
                        ),
                        siteAccreditedNetwork = siteAccreditedNetworkExclusive,
                    )
                )
            } returns IllegalArgumentException("Invalid filter").failure()
            coEvery {
                accreditedLocationService.findLocation(placeId)
            } returns AccreditedLatLon(
                lat = lat,
                lon = lon,
                type = LocationType.BUILDING,
                placeId = placeId,
            ).success()

            // Then
            assertFailsWith<IllegalArgumentException> {
                accreditedService.findBy(
                    siteAccreditedNetworkId = null,
                    categories = null,
                    specialtyId = null,
                    placeId = placeId,
                    lat = "",
                    lon = "",
                    radiusInMeters = radiusInMeters,
                    isDefaultSearch = false,
                )
            }

            coVerifyOnce { accreditedFilterService.createFilter(any(), any(), any(), any(), any(), any()) }
            coVerifyOnce { atlasAccreditedService.findBy(any()) }
        }
    }

    @Test
    fun `#findBy fail when google maps service doesn't find the place then should return failure`() = runBlocking {
        coEvery {
            accreditedLocationService.findLocation(placeId)
        } returns IllegalArgumentException("Invalid place id").failure()

        assertFailsWith<IllegalArgumentException> {
            accreditedService.findBy(
                siteAccreditedNetworkId = null,
                categories = null,
                specialtyId = null,
                placeId = placeId,
                lat = "",
                lon = "",
                radiusInMeters = radiusInMeters,
                isDefaultSearch = false,
                range = IntRange(0, 9)
            )
        }

        coVerifyNone { atlasAccreditedService.findBy(any()) }
    }

    @Test
    fun `#findBy with speciality filter then returns OK`() = runBlocking {
        withFeatureFlag(
            FeatureNamespace.MEMBER_WANNABE,
            flagName,
            siteAccreditedNetworkExclusiveId.toString(),
        ) {
            val speciality = RangeUUID.generate()
            val accredited = Accredited(
                id = RangeUUID.generate(),
                name = "Hospital",
                category = AccreditedCategory.HOSPITAL,
                referencedId = RangeUUID.generate(),
            )
            val itemsResponse = AccreditedResponseConverter.fromAccredited(accredited)
            val required = AccreditedWithLocationAndRemotesResponse(
                items = listOf(itemsResponse),
                defaultItems = emptyList(),
                totalItems = 1,
            )
            val filter = AtlasAccreditedService.Filter(
                categories = null,
                specialtyId = speciality,
                geoLocation = AtlasAccreditedService.GeoLocationFilter(
                    radiusInMeters,
                    lat,
                    lon
                ),
                siteAccreditedNetwork = null,
                limit = 10,
                offset = 0,
            )

            coEvery {
                accreditedFilterService.createFilter(
                    siteAccreditedNetworkId = null,
                    categories = null,
                    specialityId = speciality,
                    lat = lat,
                    lon = lon,
                    radiusInMeters = radiusInMeters,
                    limit = 10,
                    offset = 0,
                )
            } returns filter.success()
            coEvery {
                atlasAccreditedService.findBy(filter)
            } returns resultOf { listOf(accredited) }
            coEvery {
                atlasAccreditedService.countBy(filter)
            } returns resultOf { 1 }
            coEvery {
                accreditedLocationService.findLocation(placeId)
            } returns AccreditedLatLon(
                lat = lat,
                lon = lon,
                type = LocationType.BUILDING,
                placeId = placeId,
            ).success()
            coEvery {
                accreditedAndPriorityListLogicService.getPrioritizedAccreditedList(
                    range = IntRange(0, 9),
                    accredited = listOf(accredited),
                    placeId = placeId,
                    specialityId = speciality,
                    siteAccreditedNetworkId = null,
                    lat = lat,
                    lon = lon,
                    radiusInMeters = radiusInMeters,
                )
            } returns listOf(accredited).success()

            // When
            val result = accreditedService.findBy(
                siteAccreditedNetworkId = null,
                categories = null,
                specialtyId = speciality,
                placeId = placeId,
                lat = "",
                lon = "",
                radiusInMeters = radiusInMeters,
                isDefaultSearch = false,
                range = IntRange(0, 9),
            )

            // Then
            assertTrue(result.isSuccess())
            assertEquals(required, result.get())

            coVerifyOnce { accreditedFilterService.createFilter(any(), any(), any(), any(), any(), any(), any(), any()) }
            coVerifyOnce { atlasAccreditedService.findBy(any()) }
            coVerifyOnce { atlasAccreditedService.countBy(any()) }
        }
    }

    @Test
    fun `#findBy returning paginated total items`() = runBlocking {
        withFeatureFlag(
            FeatureNamespace.MEMBER_WANNABE,
            flagName,
            siteAccreditedNetworkExclusiveId.toString(),
        ) {
            val speciality = RangeUUID.generate()
            val accredited = Accredited(
                id = RangeUUID.generate(),
                name = "Hospital",
                category = AccreditedCategory.HOSPITAL,
                referencedId = RangeUUID.generate(),
            )

            val itemsResponse = AccreditedResponseConverter.fromAccredited(accredited)
            val required = AccreditedWithLocationAndRemotesResponse(
                items = listOf(itemsResponse),
                defaultItems = emptyList(),
                totalItems = 10,
            )
            val filter = AtlasAccreditedService.Filter(
                categories = null,
                specialtyId = speciality,
                geoLocation = AtlasAccreditedService.GeoLocationFilter(
                    radiusInMeters,
                    lat,
                    lon
                ),
                siteAccreditedNetwork = siteAccreditedNetworkExclusive,
                limit = 10,
                offset = 0,
            )

            coEvery {
                accreditedFilterService.createFilter(
                    siteAccreditedNetworkId = null,
                    categories = null,
                    specialityId = speciality,
                    lat = lat,
                    lon = lon,
                    radiusInMeters = radiusInMeters,
                    limit = 10,
                    offset = 0,
                )
            } returns filter.success()
            coEvery {
                atlasAccreditedService.findBy(filter)
            } returns resultOf { listOf(accredited) }
            coEvery {
                atlasAccreditedService.countBy(filter)
            } returns resultOf { 10 }
            coEvery {
                accreditedLocationService.findLocation(placeId)
            } returns AccreditedLatLon(
                lat = lat,
                lon = lon,
                type = LocationType.BUILDING,
                placeId = placeId,
            ).success()
            coEvery {
                accreditedAndPriorityListLogicService.getPrioritizedAccreditedList(
                    range = IntRange(0, 9),
                    accredited = listOf(accredited),
                    placeId = placeId,
                    categories = null,
                    specialityId = speciality,
                    siteAccreditedNetworkId = null,
                    lat = lat,
                    lon = lon,
                    radiusInMeters = radiusInMeters,
                )
            } returns listOf(accredited).success()

            // When
            val result = accreditedService.findBy(
                siteAccreditedNetworkId = null,
                categories = null,
                specialtyId = speciality,
                placeId = placeId,
                lat = "",
                lon = "",
                radiusInMeters = radiusInMeters,
                isDefaultSearch = false,
                range = IntRange(0, 9),
            )

            // Then
            assertTrue(result.isSuccess())
            assertEquals(required, result.get())

            coVerifyOnce { accreditedFilterService.createFilter(any(), any(), any(), any(), any(), any(), any(), any()) }
            coVerifyOnce { atlasAccreditedService.findBy(any()) }
            coVerifyOnce { atlasAccreditedService.countBy(any()) }
        }
    }

    @Test
    fun `#findBy should return remotes when accredited list is empty`() = runBlocking {
        val siteAccreditedNetwork = SiteAccreditedNetwork(
            id = siteAccreditedNetworkId,
            bundleIds = listOf(RangeUUID.generate())
        )
        val remoteAccredited = RemoteAccredited(
            name = "Hospital",
            referencedId = RangeUUID.generate(),
        )
        val remoteAccreditedList = RemoteAccreditedList(listOf(remoteAccredited))
        val flagship = Accredited(
            id = RangeUUID.generate(),
            name = "Hospital",
            category = AccreditedCategory.HOSPITAL,
            referencedId = RangeUUID.generate(),
        )

        val required = AccreditedWithLocationAndRemotesResponse(
            items = emptyList(),
            defaultItems = listOf(remoteAccredited),
            totalItems = 1,
        )
        val filter = AtlasAccreditedService.Filter(
            categories = listOf(AccreditedCategory.MATERNITY),
            geoLocation = AtlasAccreditedService.GeoLocationFilter(
                radiusInMeters,
                lat,
                lon
            ),
            siteAccreditedNetwork = siteAccreditedNetwork,
            limit = 10,
            offset = 0,
        )

        coEvery {
            accreditedFilterService.createFilter(
                siteAccreditedNetworkId = siteAccreditedNetworkId,
                categories = listOf(AccreditedCategory.valueOf(category)),
                specialityId = null,
                lat = lat,
                lon = lon,
                radiusInMeters = radiusInMeters,
                limit = 10,
                offset = 0,
            )
        } returns filter.success()
        coEvery {
            atlasAccreditedService.findBy(filter)
        } returns resultOf { emptyList() }
        coEvery {
            atlasAccreditedService.findRemotes(filter)
        } returns resultOf { remoteAccreditedList }
        coEvery {
            atlasAccreditedService.countBy(filter)
        } returns resultOf { 1 }
        coEvery {
            accreditedLocationService.findLocation(placeId)
        } returns AccreditedLatLon(
            lat = lat,
            lon = lon,
            type = LocationType.BUILDING,
            placeId = placeId,
        ).success()
        coEvery {
            accreditedAndPriorityListLogicService.getPrioritizedAccreditedList(
                range = IntRange(0, 9),
                accredited = emptyList(),
                placeId = placeId,
                categories = categories,
                specialityId = null,
                siteAccreditedNetworkId = siteAccreditedNetworkId,
                lat = lat,
                lon = lon,
                radiusInMeters = radiusInMeters,
            )
        } returns listOf(flagship).success()

        // When
        val result = accreditedService.findBy(
            siteAccreditedNetworkId = siteAccreditedNetworkId,
            categories = listOf(AccreditedCategory.valueOf(category)),
            specialtyId = null,
            placeId = placeId,
            lat = "",
            lon = "",
            radiusInMeters = radiusInMeters,
            isDefaultSearch = false,
            range = IntRange(0, 9),
        )

        // Then
        assertTrue(result.isSuccess())
        assertEquals(required, result.get())

        coVerifyOnce { accreditedFilterService.createFilter(any(), any(), any(), any(), any(), any(), any(), any()) }
        coVerifyOnce { atlasAccreditedService.findBy(any()) }
        coVerifyOnce { atlasAccreditedService.findRemotes(any()) }
        coVerifyOnce { atlasAccreditedService.countBy(any()) }
    }

    @Test
    fun `#findBy fail when findRemotes throws error then should return failure`(): Unit = runBlocking {
        val siteAccreditedNetwork = SiteAccreditedNetwork(
            id = siteAccreditedNetworkId,
            bundleIds = listOf(RangeUUID.generate())
        )
        val filter = AtlasAccreditedService.Filter(
            categories = listOf(AccreditedCategory.MATERNITY),
            geoLocation = AtlasAccreditedService.GeoLocationFilter(
                radiusInMeters,
                lat,
                lon
            ),
            siteAccreditedNetwork = siteAccreditedNetwork,
            limit = 10,
            offset = 0,
        )

        coEvery {
            accreditedFilterService.createFilter(
                siteAccreditedNetworkId = siteAccreditedNetworkId,
                categories = listOf(AccreditedCategory.valueOf(category)),
                specialityId = null,
                lat = lat,
                lon = lon,
                radiusInMeters = radiusInMeters,
                limit = 10,
                offset = 0,
            )
        } returns filter.success()
        coEvery { atlasAccreditedService.findBy(any()) } returns resultOf { emptyList<Accredited>() }
        coEvery { atlasAccreditedService.findRemotes(any()) } returns IllegalArgumentException("Invalid filter").failure()
        coEvery {
            accreditedLocationService.findLocation(placeId)
        } returns AccreditedLatLon(
            lat = lat,
            lon = lon,
            type = LocationType.BUILDING,
            placeId = placeId,
        ).success()
        coEvery {
            atlasAccreditedService.countBy(filter)
        } returns resultOf { 0 }
        coEvery {
            accreditedAndPriorityListLogicService.getPrioritizedAccreditedList(
                range = IntRange(0, 9),
                accredited = emptyList(),
                placeId = placeId,
                categories = categories,
                specialityId = null,
                siteAccreditedNetworkId = siteAccreditedNetworkId,
                lat = lat,
                lon = lon,
                radiusInMeters = radiusInMeters,
            )
        } returns emptyList<Accredited>().success()

        assertFailsWith<IllegalArgumentException> {
            accreditedService.findBy(
                siteAccreditedNetworkId = siteAccreditedNetworkId,
                categories = listOf(AccreditedCategory.valueOf(category)),
                specialtyId = null,
                placeId = placeId,
                lat = "",
                lon = "",
                radiusInMeters = radiusInMeters,
                isDefaultSearch = false,
                range = IntRange(0, 9),
            )
        }

        coVerifyOnce { accreditedFilterService.createFilter(any(), any(), any(), any(), any(), any(), any(), any()) }
        coVerifyOnce { atlasAccreditedService.findBy(any()) }
        coVerifyOnce { atlasAccreditedService.findRemotes(any()) }
        coVerifyOnce { atlasAccreditedService.countBy(any()) }
    }

    @Test
    fun `findBy should return items from findBy when isDefaultSearch is false`(): Unit = runBlocking {
        withFeatureFlag(
            FeatureNamespace.MEMBER_WANNABE,
            flagName,
            siteAccreditedNetworkExclusiveId.toString(),
        ) {
            val accredited = Accredited(
                id = RangeUUID.generate(),
                name = "Hospital",
                category = AccreditedCategory.HOSPITAL,
                referencedId = RangeUUID.generate(),
            )
            val accreditedList = listOf(accredited)
            val filter = AtlasAccreditedService.Filter(
                categories = null,
                geoLocation = AtlasAccreditedService.GeoLocationFilter(
                    radiusInMeters,
                    lat,
                    lon
                ),
                siteAccreditedNetwork = siteAccreditedNetworkExclusive,
                limit = 10,
                offset = 0,
            )

            coEvery {
                accreditedFilterService.createFilter(
                    siteAccreditedNetworkId = null,
                    categories = null,
                    specialityId = null,
                    lat = lat,
                    lon = lon,
                    radiusInMeters = radiusInMeters,
                    limit = 10,
                    offset = 0,
                )
            } returns filter.success()
            coEvery {
                accreditedLocationService.findLocation(placeId)
            } returns AccreditedLatLon(
                lat = lat,
                lon = lon,
                type = LocationType.BUILDING,
                placeId = placeId,
            ).success()
            coEvery {
                atlasAccreditedService.findBy(filter)
            } returns accreditedList.success()
            coEvery {
                atlasAccreditedService.countBy(filter)
            } returns resultOf { 1 }
            coEvery {
                accreditedAndPriorityListLogicService.getPrioritizedAccreditedList(
                    range = IntRange(0, 9),
                    accredited = accreditedList,
                    placeId = placeId,
                    categories = null,
                    specialityId = null,
                    siteAccreditedNetworkId = null,
                    lat = lat,
                    lon = lon,
                    radiusInMeters = radiusInMeters,
                )
            } returns accreditedList.success()

            val result = accreditedService.findBy(
                siteAccreditedNetworkId = null,
                categories = null,
                specialtyId = null,
                placeId = placeId,
                lat = "",
                lon = "",
                radiusInMeters = radiusInMeters,
                isDefaultSearch = false,
                range = IntRange(0, 9),
            )

            val itemsResponse = AccreditedResponseConverter.fromAccredited(accredited)
            assertEquals(result.get().items, listOf(itemsResponse))

            coVerifyOnce { accreditedFilterService.createFilter(any(), any(), any(), any(), any(), any(), any(), any()) }
            coVerifyOnce { atlasAccreditedService.findBy(any()) }
            coVerifyOnce { atlasAccreditedService.countBy(any()) }
        }
    }

    @Test
    fun `findBy should return failure when findByWithCache fails and isDefaultSearch is true`(): Unit = runBlocking {
        val filter = AtlasAccreditedService.Filter(
            categories = null,
            geoLocation = AtlasAccreditedService.GeoLocationFilter(
                radiusInMeters,
                lat,
                lon
            ),
            siteAccreditedNetwork = null
        )
        val exception = Exception("findByWithCache failed")

        coEvery { atlasAccreditedService.findByWithCache(filter) } throws exception

        assertFailsWith<Exception> {
            accreditedService.findBy(null, null, null,
                placeId, "", "", radiusInMeters, true)
        }
        coVerifyNone { atlasAccreditedService.countBy(any()) }
    }

    @Test
    fun `findBy should return failure when findBy fails and isDefaultSearch is false`(): Unit = runBlocking {
        val filter = AtlasAccreditedService.Filter(
            categories = null,
            geoLocation = AtlasAccreditedService.GeoLocationFilter(
                radiusInMeters,
                lat,
                lon
            ),
            siteAccreditedNetwork = null
        )
        val exception = Exception("findBy failed")

        coEvery { atlasAccreditedService.findByWithCache(filter) } throws exception

        assertFailsWith<Exception> {
            accreditedService.findBy(null, null, null,
                placeId, "", "", radiusInMeters, true)
        }
        coVerifyNone { atlasAccreditedService.countBy(any()) }
    }

    @Test
    fun `#findByLightweight should return lightweight accredited list for valid parameters`() = runBlocking {
        val location = AccreditedLatLon(lat, lon, LocationType.BUILDING)
        val accredited = Accredited(
            id = RangeUUID.generate(),
            name = "Hospital",
            category = AccreditedCategory.HOSPITAL,
            referencedId = RangeUUID.generate(),
        )
        val lightweight = AccreditedLightweightConverter.fromAccredited(accredited)

        coEvery { accreditedLocationService.findLocation(placeId) } returns location.success()
        coEvery { findAccreditedService.findLocalAccredited(siteAccreditedNetworkId, null, null, location, radiusInMeters, false, null) } returns listOf(accredited).success()

        val result = accreditedService.findByLightweight(siteAccreditedNetworkId, null, null, placeId, lat, lon, radiusInMeters, false, null)

        assertTrue(result.isSuccess())
        assertEquals(AccreditedWithLocationLightweight(listOf(lightweight), location), result.get())

        coVerifyOnce { accreditedLocationService.findLocation(any()) }
        coVerifyOnce { findAccreditedService.findLocalAccredited(any(), any(), any(), any(), any(), any(), any()) }
    }

    @Test
    fun `#findByLightweight should return failure for invalid placeId`(): Unit = runBlocking {
        val exception = IllegalArgumentException("Invalid placeId")

        coEvery { accreditedLocationService.findLocation(placeId) } returns exception.failure()

        assertFailsWith<IllegalArgumentException> {
            accreditedService.findByLightweight(siteAccreditedNetworkId, null, null, placeId, lat, lon, radiusInMeters, false, null)
        }
    }
}
