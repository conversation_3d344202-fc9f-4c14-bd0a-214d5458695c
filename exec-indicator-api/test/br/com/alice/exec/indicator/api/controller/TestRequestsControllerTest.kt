package br.com.alice.exec.indicator.api.controller

import br.com.alice.common.RangeUUID
import br.com.alice.common.core.PersonId
import br.com.alice.common.core.exceptions.NotFoundException
import br.com.alice.common.data.dsl.matchers.ResponseAssert
import br.com.alice.common.helpers.bodyAsJson
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.MvAuthorizedProcedure
import br.com.alice.exec.indicator.client.MvAuthorizedProcedureService
import br.com.alice.healthplan.client.HealthPlanService
import br.com.alice.healthplan.client.HealthPlanTaskService
import com.github.kittinunf.result.failure
import com.github.kittinunf.result.success
import io.mockk.clearAllMocks
import io.mockk.coEvery
import io.mockk.mockk
import io.mockk.spyk
import java.util.UUID
import kotlin.test.AfterTest
import kotlin.test.BeforeTest
import kotlin.test.Test
import kotlin.test.assertEquals
import kotlin.test.assertTrue

internal class TestRequestsControllerTest : ControllerTestHelper() {
    private val healthPlanService: HealthPlanService = mockk()
    private val healthPlanTaskService: HealthPlanTaskService = mockk()
    private val mvAuthorizedProcedureService: MvAuthorizedProcedureService = mockk()

    private val controller = spyk(
        TestRequestsController(
            healthPlanService,
            healthPlanTaskService,
            mvAuthorizedProcedureService
        )
    )

    private val personId = PersonId()

    // --------
    // STAFF 01
    // --------
    private val staffId01 = RangeUUID.generate()

    // task-procedure 01
    private val task01Staff01 = TestModelFactory.buildHealthPlanTask(
        staffId = staffId01
    )
    private val procedure01Staff01 = MvAuthorizedProcedure.buildForAuthorizationRequest(
        personId,
        "procedure",
        testRequestId = task01Staff01.id
    ).copy(id = RangeUUID.generate())

    // --------
    // STAFF 02
    // --------
    private val staffId02 = RangeUUID.generate()

    // task-procedure 01
    private val task01Staff02 = TestModelFactory.buildHealthPlanTask(
        staffId = staffId02
    )
    private val procedure01Staff02 = MvAuthorizedProcedure.buildForAuthorizationRequest(
        personId,
        "procedure",
        testRequestId = task01Staff02.id
    ).copy(id = RangeUUID.generate())

    // task-procedure 02
    private val task02Staff02 = TestModelFactory.buildHealthPlanTask(
        staffId = staffId02
    )
    private val procedure02Staff02 = MvAuthorizedProcedure.buildForAuthorizationRequest(
        personId,
        "procedure",
        testRequestId = task02Staff02.id
    ).copy(id = RangeUUID.generate())

    // --------
    // STAFF 03
    // --------
    private val staffId03 = RangeUUID.generate()

    // task-procedure 01
    private val task01Staff03 = TestModelFactory.buildHealthPlanTask(
        staffId = staffId03
    )
    private val procedure01Staff03 = MvAuthorizedProcedure.buildForAuthorizationRequest(
        personId,
        "procedure",
        testRequestId = task01Staff03.id
    ).copy(id = RangeUUID.generate())

    // task-procedure 02
    private val task02Staff03 = TestModelFactory.buildHealthPlanTask(
        staffId = staffId03
    )
    private val procedure02Staff03 = MvAuthorizedProcedure.buildForAuthorizationRequest(
        personId,
        "procedure",
        testRequestId = task02Staff03.id
    ).copy(id = RangeUUID.generate())

    // task-procedure 03
    private val task03Staff03 = TestModelFactory.buildHealthPlanTask(
        staffId = staffId03
    )
    private val procedure03Staff03 = MvAuthorizedProcedure.buildForAuthorizationRequest(
        personId,
        "procedure",
        testRequestId = task03Staff03.id
    ).copy(id = RangeUUID.generate())

    // PROCEDURE WITHOUT TASK
    private val procedureWithoutTask = MvAuthorizedProcedure.buildForAuthorizationRequest(
        personId,
        "procedure",
        testRequestId = RangeUUID.generate()
    ).copy(id = RangeUUID.generate())


    @BeforeTest
    fun setUp() {
        super.setup()
        module.single { controller }
    }

    @AfterTest
    fun tearDown() {
        clearAllMocks()
    }

    @Test
    fun `#separateTestRequestsByRequester() - One tasks requested by Staff01`() {

        coEvery {
            mvAuthorizedProcedureService.getByIds(match {
                it == listOf(procedure01Staff01.id)
            })
        } returns listOf(procedure01Staff01).success()

        coEvery {
            healthPlanTaskService.getAllByIds(match {
                it == listOf(task01Staff01.id)
            })
        } returns listOf(task01Staff01).success()

        val queryParams = generateQueryParams(
            procedure01Staff01.id
        )

        authenticatedAs(token, staff) {
            get(url = "/separate_test_requests_by_requester/${queryParams}") { response ->
                ResponseAssert.assertThat(response).isSuccessfulJson()
                val separatedProcedures: List<List<UUID>> = response.bodyAsJson()
                assertEquals(expected = 1, actual = separatedProcedures.flatten().size)
                assertEquals(expected = 1, actual = separatedProcedures.size)
                assertEquals(expected = setOf(1), actual = separatedProcedures.map { it.size }.toSet())
            }
        }
    }

    @Test
    fun `#separateTestRequestsByRequester() - Two tasks requested by Staff02`() {

        coEvery {
            mvAuthorizedProcedureService.getByIds(match {
                it == listOf(procedure01Staff02.id, procedure02Staff02.id)
            })
        } returns listOf(procedure01Staff02, procedure02Staff02).success()

        coEvery {
            healthPlanTaskService.getAllByIds(match {
                it == listOf(
                    task01Staff02.id, task02Staff02.id
                )
            })
        } returns listOf(task01Staff02, task02Staff02).success()

        val queryParams = generateQueryParams(
            procedure01Staff02.id, procedure02Staff02.id
        )

        authenticatedAs(token, staff) {
            get(url = "/separate_test_requests_by_requester/${queryParams}") { response ->
                ResponseAssert.assertThat(response).isSuccessfulJson()
                val separatedProcedures: List<List<UUID>> = response.bodyAsJson()
                assertEquals(expected = 2, actual = separatedProcedures.flatten().size)
                assertEquals(expected = 1, actual = separatedProcedures.size)
                assertEquals(expected = setOf(2), actual = separatedProcedures.map { it.size }.toSet())
            }
        }
    }

    @Test
    fun `#separateTestRequestsByRequester() - Three tasks requested by Staff03`() {

        coEvery {
            mvAuthorizedProcedureService.getByIds(match {
                it == listOf(procedure01Staff03.id, procedure02Staff03.id, procedure03Staff03.id)
            })
        } returns listOf(procedure01Staff03, procedure02Staff03, procedure03Staff03).success()

        coEvery {
            healthPlanTaskService.getAllByIds(match {
                it == listOf(
                    task01Staff03.id, task02Staff03.id, task03Staff03.id
                )
            })
        } returns listOf(task01Staff03, task02Staff03, task03Staff03).success()

        val queryParams = generateQueryParams(
            procedure01Staff03.id, procedure02Staff03.id, procedure03Staff03.id
        )

        authenticatedAs(token, staff) {
            get(url = "/separate_test_requests_by_requester/${queryParams}") { response ->
                ResponseAssert.assertThat(response).isSuccessfulJson()
                val separatedProcedures: List<List<UUID>> = response.bodyAsJson()
                assertEquals(expected = 3, actual = separatedProcedures.flatten().size)
                assertEquals(expected = 1, actual = separatedProcedures.size)
                assertEquals(expected = setOf(3), actual = separatedProcedures.map { it.size }.toSet())
            }
        }
    }


    @Test
    fun `#separateTestRequestsByRequester() - One task requested by Staff01, two requested by Staff02 and three requested by Staff03`() {

            coEvery {
                mvAuthorizedProcedureService.getByIds(match {
                    it == listOf(
                        procedure01Staff01.id,
                        procedure01Staff02.id, procedure02Staff02.id,
                        procedure01Staff03.id, procedure02Staff03.id, procedure03Staff03.id
                    )
                })
            } returns listOf(
                procedure01Staff01,
                procedure01Staff02, procedure02Staff02,
                procedure01Staff03, procedure02Staff03, procedure03Staff03
            ).success()

            coEvery {
                healthPlanTaskService.getAllByIds(match {
                    it == listOf(
                        task01Staff01.id,
                        task01Staff02.id, task02Staff02.id,
                        task01Staff03.id, task02Staff03.id, task03Staff03.id
                    )
                })
            } returns listOf(
                task01Staff01,
                task01Staff02, task02Staff02,
                task01Staff03, task02Staff03, task03Staff03
            ).success()

            val queryParams = generateQueryParams(
                procedure01Staff01.id,
                procedure01Staff02.id,
                procedure02Staff02.id,
                procedure01Staff03.id,
                procedure02Staff03.id,
                procedure03Staff03.id
            )

            authenticatedAs(token, staff) {
                get(url = "/separate_test_requests_by_requester/${queryParams}") { response ->
                    ResponseAssert.assertThat(response).isSuccessfulJson()
                    val separatedProcedures: List<List<UUID>> = response.bodyAsJson()
                    assertEquals(expected = 6, actual = separatedProcedures.flatten().size)
                    assertEquals(expected = 3, actual = separatedProcedures.size)
                    assertEquals(expected = setOf(1, 2, 3), actual = separatedProcedures.map { it.size }.toSet())
                }
            }
        }

    @Test
    fun `#separateTestRequestsByRequester() - Two tasks requested by Staff02 and three requested by Staff03`() {

            coEvery {
                mvAuthorizedProcedureService.getByIds(match {
                    it == listOf(
                        procedure01Staff02.id, procedure02Staff02.id,
                        procedure01Staff03.id, procedure02Staff03.id, procedure03Staff03.id
                    )
                })
            } returns listOf(
                procedure01Staff02, procedure02Staff02,
                procedure01Staff03, procedure02Staff03, procedure03Staff03
            ).success()

            coEvery {
                healthPlanTaskService.getAllByIds(match {
                    it == listOf(
                        task01Staff02.id, task02Staff02.id,
                        task01Staff03.id, task02Staff03.id, task03Staff03.id
                    )
                })
            } returns listOf(
                task01Staff02, task02Staff02,
                task01Staff03, task02Staff03, task03Staff03
            ).success()

            val queryParams = generateQueryParams(
                procedure01Staff02.id,
                procedure02Staff02.id,
                procedure01Staff03.id,
                procedure02Staff03.id,
                procedure03Staff03.id
            )

            authenticatedAs(token, staff) {
                get(url = "/separate_test_requests_by_requester/${queryParams}") { response ->
                    ResponseAssert.assertThat(response).isSuccessfulJson()
                    val separatedProcedures: List<List<UUID>> = response.bodyAsJson()
                    assertEquals(expected = 5, actual = separatedProcedures.flatten().size)
                    assertEquals(expected = 2, actual = separatedProcedures.size)
                    assertEquals(expected = setOf(2, 3), actual = separatedProcedures.map { it.size }.toSet())
                }
            }
        }

    @Test
    fun `#separateTestRequestsByRequester() - One task requested by Staff01 and another that does not exist`() {

            coEvery {
                mvAuthorizedProcedureService.getByIds(match {
                    it == listOf(procedure01Staff01.id, procedureWithoutTask.id)
                })
            } returns listOf(procedure01Staff01, procedureWithoutTask).success()

            coEvery {
                healthPlanTaskService.getAllByIds(match {
                    it == listOf(
                        task01Staff01.id,
                        procedureWithoutTask.testRequestId
                    )
                })
            } returns NotFoundException("not found").failure()

            val queryParams = generateQueryParams(
                procedure01Staff01.id,
                procedureWithoutTask.id
            )

            authenticatedAs(token, staff) {
                get(url = "/separate_test_requests_by_requester/${queryParams}") { response ->
                    ResponseAssert.assertThat(response).isNotFound()
                }
            }
        }

    @Test
    fun `#separateTestRequestsByRequester() - Empty list of testRequestIds`() {
        authenticatedAs(token, staff) {
            get(url = "/separate_test_requests_by_requester/") { response ->
                ResponseAssert.assertThat(response).isSuccessfulJson()
                val separatedProcedures: List<List<UUID>> = response.bodyAsJson()
                assertTrue { separatedProcedures.isEmpty() }
            }
        }
    }

    private fun generateQueryParams(vararg testRequestIds: UUID) =
        testRequestIds.joinToString(prefix = "?", separator = "&") { testRequestId -> "id=${testRequestId}" }
}
