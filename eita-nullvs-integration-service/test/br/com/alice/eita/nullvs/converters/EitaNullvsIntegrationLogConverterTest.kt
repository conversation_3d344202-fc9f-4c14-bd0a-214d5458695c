package br.com.alice.eita.nullvs.converters

import br.com.alice.common.RangeUUID
import br.com.alice.data.layer.models.BatchType
import br.com.alice.data.layer.models.EitaNullvsExternalModelType
import br.com.alice.data.layer.models.EitaNullvsIntegrationLog
import br.com.alice.data.layer.models.EitaNullvsIntegrationLogModel
import br.com.alice.data.layer.models.EitaNullvsInternalModelType
import br.com.alice.data.layer.models.LogStatus
import org.assertj.core.api.Assertions.assertThat
import kotlin.test.Test

class EitaNullvsIntegrationLogConverterTest {

    private val nullvsIntegrationLog = EitaNullvsIntegrationLog(
        eventId = RangeUUID.generate(),
        eventName = "eventName",
        integrationEventName = "integrationEventName",
        internalId = RangeUUID.generate(),
        internalModelName = EitaNullvsInternalModelType.HEALTHCARE_BUNDLE,
        externalModelName = EitaNullvsExternalModelType.BUNDLE,
        batchId = "batchId",
        idSoc = "idSoc",
        batchType = BatchType.CREATE,
        payloadSequenceId = 1,
        description = "description",
        status = LogStatus.PENDING,
        hash = "hash",
        groupId = RangeUUID.generate()
    )

    private val nullvsIntegrationLogModel = EitaNullvsIntegrationLogModel(
        id = nullvsIntegrationLog.id,
        version = nullvsIntegrationLog.version,
        createdAt = nullvsIntegrationLog.createdAt,
        updatedAt = nullvsIntegrationLog.updatedAt,
        eventId = nullvsIntegrationLog.eventId,
        eventName = "eventName",
        integrationEventName = "integrationEventName",
        internalId = nullvsIntegrationLog.internalId,
        internalModelName = EitaNullvsInternalModelType.HEALTHCARE_BUNDLE,
        externalModelName = EitaNullvsExternalModelType.BUNDLE,
        batchId = "batchId",
        idSoc = "idSoc",
        batchType = BatchType.CREATE,
        payloadSequenceId = 1,
        description = "description",
        status = LogStatus.PENDING,
        hash = "hash",
        groupId = nullvsIntegrationLog.groupId
    )

    @Test
    fun testToTransport() {
        assertThat(nullvsIntegrationLogModel.toTransport()).isEqualTo(nullvsIntegrationLog)
    }

    @Test
    fun testToModel() {
        assertThat(nullvsIntegrationLog.toModel()).isEqualTo(nullvsIntegrationLogModel)
    }

}
