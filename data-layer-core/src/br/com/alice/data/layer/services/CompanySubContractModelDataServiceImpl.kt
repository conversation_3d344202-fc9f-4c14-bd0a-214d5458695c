package br.com.alice.data.layer.services

import br.com.alice.data.layer.models.CompanySubContractModel
import br.com.alice.data.layer.pipelines.DatabasePipelineFactory
import br.com.alice.data.layer.tables.CompanySubContractTable

class CompanySubContractModelDataServiceImpl internal constructor(factory: DatabasePipelineFactory) :
    BaseDataServiceImpl<CompanySubContractModel>(factory.get<CompanySubContractModel, CompanySubContractTable>()),
    CompanySubContractModelDataService
