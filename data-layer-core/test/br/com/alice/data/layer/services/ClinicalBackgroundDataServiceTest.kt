package br.com.alice.data.layer.services

import br.com.alice.common.core.PersonId
import br.com.alice.common.core.Role
import br.com.alice.common.core.StaffType
import br.com.alice.common.core.extensions.toUUID
import br.com.alice.common.data.dsl.matchers.ResultAssert.Companion.assertThat
import br.com.alice.common.models.Gender
import br.com.alice.data.layer.helpers.DataServiceTestHelper
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.helpers.TestTableFactory
import br.com.alice.data.layer.repositories.JdbiRepository
import br.com.alice.data.layer.tables.AppointmentTable
import br.com.alice.data.layer.tables.StaffTable
import kotlinx.coroutines.runBlocking
import kotlin.test.Test

class ClinicalBackgroundDataServiceTest : DataServiceTestHelper() {
    private val appointmentRepo = JdbiRepository(mainJdbi, AppointmentTable::class)
    private val staffRepo = JdbiRepository(mainJdbi, StaffTable::class)
    private val client = ClinicalBackgroundDataServiceClient(httpInvoker)
    private val personTokenService = PersonTokenServiceImpl(tokenJdbi) as PersonTokenService

    @Test
    fun `should add new entity and query by id and filters`() = runBlocking {
        val personId = PersonId()
        val personToken = personTokenService.createForPersonId(personId).get()

        val staffTable = StaffTable(
            id = "b049a7f7-3260-4b1b-b1f9-560e8a7846b8".toUUID(),
            email = "<EMAIL>",
            firstName = "Henry",
            lastName = "Porta Hirschfeld",
            gender = Gender.MALE,
            role = Role.MANAGER_PHYSICIAN,
            type = StaffType.PITAYA
        )

        staffRepo.add(staffTable)

        val appointmentTable = TestTableFactory.buildAppointmentTable(
            personId = personToken.personHiToken,
            staffId = staffTable.id
        )
        appointmentRepo.add(appointmentTable)

        val clinicalBackground = TestModelFactory.buildUnstructuredClinicalBackground(
            personId = personToken.personId,
            addedByStaffId = staffTable.id,
            appointmentId = appointmentTable.id
        )

        assertThat(client.add(clinicalBackground)).isSuccess()

        val retrieved = client.get(clinicalBackground.id)
        assertThat(retrieved).isSuccessWithDataIgnoringGivenFields(clinicalBackground, "createdAt", "updatedAt")

        val backgroundsByPerson = client.find { where { this.personId.eq(personId) } }
        assertThat(backgroundsByPerson).isSuccessWithDataIgnoringGivenFields(listOf(clinicalBackground), "createdAt", "updatedAt")

        val backgroundsByStatus = client.find { where { this.status.eq(clinicalBackground.status) } }
        assertThat(backgroundsByStatus).isSuccessWithDataIgnoringGivenFields(listOf(clinicalBackground), "createdAt", "updatedAt")

        val backgroundsByDeniedList = client.find { where { this.denied.eq(false) } }
        assertThat(backgroundsByDeniedList).isSuccessWithDataIgnoringGivenFields(listOf(clinicalBackground), "createdAt", "updatedAt")

        val backgroundsByType = client.find { where { this.type.eq(clinicalBackground.type) } }
        assertThat(backgroundsByType).isSuccessWithDataIgnoringGivenFields(listOf(clinicalBackground), "createdAt", "updatedAt")
    }

}
