package br.com.alice.wanda.services

import br.com.alice.appointment.client.AppointmentService
import br.com.alice.common.RangeUUID
import br.com.alice.common.core.PersonId
import br.com.alice.common.core.exceptions.NotFoundException
import br.com.alice.common.core.extensions.classSimpleName
import br.com.alice.common.core.extensions.toUUID
import br.com.alice.common.data.dsl.matchers.ResultAssert.Companion.assertThat
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.AppointmentType
import br.com.alice.data.layer.models.PersonHealthEvent
import br.com.alice.data.layer.models.PersonHealthEventCategory
import br.com.alice.data.layer.models.PersonHealthEventReferenceModel.APPOINTMENT_PERSON_HEALTH_EVENT_SCRIPT_ACTION
import br.com.alice.data.layer.models.PersonHealthEventScriptAction
import br.com.alice.data.layer.models.PersonHealthEventStatus
import br.com.alice.data.layer.models.ReferencedLink
import br.com.alice.data.layer.models.ScriptAction
import br.com.alice.data.layer.models.ScriptActionType
import br.com.alice.healthlogic.models.ETAResultEvent
import br.com.alice.healthlogic.models.ETATriggerEvent
import br.com.alice.healthlogic.models.TriggerActionType
import br.com.alice.staff.client.HealthcareTeamService
import br.com.alice.staff.models.HealthcareTeamInfo
import br.com.alice.staff.models.StaffInfo
import br.com.alice.wanda.logics.AutomaticActionPersonHealthEventLogic
import br.com.alice.wanda.services.internal.InternalPersonHealthEventService
import com.github.kittinunf.result.failure
import com.github.kittinunf.result.success
import io.mockk.Called
import io.mockk.clearAllMocks
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.every
import io.mockk.mockk
import io.mockk.mockkObject
import kotlinx.coroutines.runBlocking
import java.time.LocalDateTime
import kotlin.test.AfterTest
import kotlin.test.BeforeTest
import kotlin.test.Test


internal class AutomaticTaskHealthEventServiceTest {
    private val internalPersonHealthEventService: InternalPersonHealthEventService = mockk()
    private val appointmentService: AppointmentService = mockk()
    private val healthcareTeamService: HealthcareTeamService = mockk()

    private val automaticTaskHealthEventService = AutomaticTaskHealthEventService(
        internalPersonHealthEventService,
        appointmentService,
        healthcareTeamService
    )

    private val appointment = TestModelFactory.buildAppointment(
        type = AppointmentType.ASSISTANCE_CARE
    ).copy(channelId = "channelId")
    private val healthcareTeam = TestModelFactory.buildHealthcareTeam(nurseStaffId = RangeUUID.generate())
    private val healthCareTeamInfo = HealthcareTeamInfo(
        id = healthcareTeam.id,
        physicianInfo = StaffInfo(
            id = healthcareTeam.physicianStaffId,
            firstName = "Test",
            profileImageUrl = "url"
        ),
        nurseInfo = StaffInfo(
            id = healthcareTeam.nurseStaffId!!,
            firstName = "Test",
            profileImageUrl = "url"
        ),
        digitalCareNurses = emptyList()
    )
    private val action = ScriptAction(
        id = RangeUUID.generate(),
        type = ScriptActionType.PERSON_HEALTH_EVENT,
        content = mapOf("dueDate" to 3, "name" to "FUP D3", "responsible" to "MANAGER_PHYSICIAN")
    )
    private val specializedAction = action.specialize<PersonHealthEventScriptAction>()

    private val etaTriggerEvent = ETATriggerEvent(
        trigger = TriggerActionType.APPOINTMENT,
        triggerId = RangeUUID.generate().toString(),
        conditions = mapOf(
            "appointment_type" to "ASSISTANCE_CARE",
            "ciaps" to listOf("N01"),
            "outcome" to "1"
        ),
        personId = PersonId()
    )
    private val event = ETAResultEvent(
        action = action,
        originEvent = etaTriggerEvent
    )
    private val expectedPersonHealthEvent = PersonHealthEvent(
        personId = appointment.personId,
        staffId = healthcareTeam.physicianStaffId,
        healthcareTeamId = healthcareTeam.id,
        category = PersonHealthEventCategory.AA_FOLLOW_UP,
        title = specializedAction.name,
        description = appointment.content,
        eventDate = appointment.createdAt,
        referencedModelId = "${appointment.id}_${specializedAction.id}",

        referencedModelClass = APPOINTMENT_PERSON_HEALTH_EVENT_SCRIPT_ACTION,
        dueDate = LocalDateTime.of(2021, 1, 14, 11, 0),
        status = PersonHealthEventStatus.NOT_STARTED,
        referencedLinks = listOf(
            ReferencedLink(
                id = specializedAction.id.toString(),
                model = specializedAction.classSimpleName()
            )
        )
    )

    @BeforeTest
    fun setup() {
        mockkObject(AutomaticActionPersonHealthEventLogic)
    }

    @AfterTest
    fun after() {
        clearAllMocks()
    }

    @Test
    fun `#upsertAppointmentAutomaticTaskHealthEvent should return error if appointment is not found`() = runBlocking {
        coEvery {
            appointmentService.get(event.originEvent.triggerId.toUUID())
        } returns NotFoundException("resource_not_found").failure()

        val result = automaticTaskHealthEventService.upsertAppointmentAutomaticTaskHealthEvent(event)
        assertThat(result).isFailureOfType(NotFoundException::class)
        coVerify { healthcareTeamService wasNot Called }
        coVerify { internalPersonHealthEventService wasNot Called }
    }

    @Test
    fun `#upsertAppointmentAutomaticTaskHealthEvent when automatic task result is processed`() = runBlocking {
        every {
            AutomaticActionPersonHealthEventLogic.appointmentActionToPersonHealthEvent(
                any(),
                any(),
                any(),
                any(),
            )
        } returns expectedPersonHealthEvent

        coEvery { appointmentService.get(event.originEvent.triggerId.toUUID()) } returns appointment.success()

        coEvery { healthcareTeamService.getHealthcareTeamInformationByPersonId(appointment.personId) } returns healthCareTeamInfo.success()

        coEvery { internalPersonHealthEventService.upsertPersonHealthEvent(any()) } returns expectedPersonHealthEvent.success()

        val result = automaticTaskHealthEventService.upsertAppointmentAutomaticTaskHealthEvent(event)
        assertThat(result).isSuccessWithData(expectedPersonHealthEvent)

        coVerify(exactly = 1) { internalPersonHealthEventService.upsertPersonHealthEvent(expectedPersonHealthEvent) }
    }

    @Test
    fun `#upsertAppointmentAutomaticTaskHealthEvent - appointment is not assistance care`() = runBlocking {
        val appointment = appointment.copy(type = AppointmentType.FOLLOW_UP_VISIT)
        every {
            AutomaticActionPersonHealthEventLogic.appointmentActionToPersonHealthEvent(any(), any(), any(), any())
        } returns expectedPersonHealthEvent

        coEvery { appointmentService.get(event.originEvent.triggerId.toUUID()) } returns appointment.success()

        coEvery { healthcareTeamService.getHealthcareTeamInformationByPersonId(appointment.personId) } returns healthCareTeamInfo.success()

        coEvery { internalPersonHealthEventService.upsertPersonHealthEvent(any()) } returns expectedPersonHealthEvent.success()

        val result = automaticTaskHealthEventService.upsertAppointmentAutomaticTaskHealthEvent(event)
        assertThat(result).isSuccessWithData(expectedPersonHealthEvent)

        coVerify(exactly = 1) { internalPersonHealthEventService.upsertPersonHealthEvent(expectedPersonHealthEvent) }
    }

    @Test
    fun `#upsertAppointmentAutomaticTaskHealthEvent - healthcare team is null`() = runBlocking {
        val appointment = appointment.copy(type = AppointmentType.FOLLOW_UP_VISIT)
        every {
            AutomaticActionPersonHealthEventLogic.appointmentActionToPersonHealthEvent(any(), any(), null, any())
        } returns expectedPersonHealthEvent

        coEvery { appointmentService.get(event.originEvent.triggerId.toUUID()) } returns appointment.success()

        coEvery { healthcareTeamService.getHealthcareTeamInformationByPersonId(any()) } returns NotFoundException().failure()

        coEvery { internalPersonHealthEventService.upsertPersonHealthEvent(any()) } returns expectedPersonHealthEvent.success()

        val result = automaticTaskHealthEventService.upsertAppointmentAutomaticTaskHealthEvent(event)
        assertThat(result).isSuccessWithData(expectedPersonHealthEvent)

        coVerify(exactly = 1) { internalPersonHealthEventService.upsertPersonHealthEvent(expectedPersonHealthEvent) }
    }
}
