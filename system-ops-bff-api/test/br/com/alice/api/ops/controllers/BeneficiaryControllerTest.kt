package br.com.alice.api.ops.controllers

import br.com.alice.api.ops.ControllerTestHelper
import br.com.alice.api.ops.converters.BeneficiaryConverter.toBeneficiaryAndPersonRequest
import br.com.alice.api.ops.converters.BeneficiaryConverter.toBeneficiaryInfoResponse
import br.com.alice.api.ops.converters.BeneficiaryConverter.toBeneficiaryTransport
import br.com.alice.api.ops.converters.BeneficiaryConverter.toLocalDateStartOfDay
import br.com.alice.api.ops.converters.CassiMemberConverter.updateFields
import br.com.alice.api.ops.models.BeneficiaryAndPersonRequest
import br.com.alice.api.ops.models.BeneficiaryAndPersonRequestBatch
import br.com.alice.api.ops.models.BeneficiaryBatchResponse
import br.com.alice.api.ops.models.BeneficiaryIdsRequest
import br.com.alice.api.ops.models.BeneficiaryInfoResponse
import br.com.alice.api.ops.models.BeneficiaryRequest
import br.com.alice.api.ops.models.BeneficiarySimpleAndPersonBatchRequest
import br.com.alice.api.ops.models.BeneficiaryWithPerson
import br.com.alice.api.ops.models.CancelMemberActivationRequest
import br.com.alice.api.ops.models.CompanyResponse
import br.com.alice.business.client.BeneficiaryHubspotService
import br.com.alice.business.client.BeneficiaryOnboardingService
import br.com.alice.business.client.BeneficiaryService
import br.com.alice.business.client.BeneficiaryService.FindOptions
import br.com.alice.business.client.CassiMemberInfo
import br.com.alice.business.client.CassiMemberService
import br.com.alice.business.client.CompanyService
import br.com.alice.business.client.MemberContractService
import br.com.alice.business.exceptions.InvalidBeneficiaryOnboardingFlowState
import br.com.alice.business.model.toBeneficiary
import br.com.alice.common.ErrorResponse
import br.com.alice.common.RangeUUID
import br.com.alice.common.core.PersonId
import br.com.alice.common.core.exceptions.NotFoundException
import br.com.alice.common.data.dsl.matchers.ResponseAssert
import br.com.alice.common.helpers.bodyAsJson
import br.com.alice.common.helpers.coVerifyNone
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.common.helpers.returns
import br.com.alice.common.models.State
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.Address
import br.com.alice.data.layer.models.Beneficiary
import br.com.alice.data.layer.models.BeneficiaryOnboardingFlowType
import br.com.alice.data.layer.models.BeneficiaryOnboardingPhase
import br.com.alice.data.layer.models.BeneficiaryOnboardingPhaseType
import br.com.alice.data.layer.models.FileVault
import br.com.alice.data.layer.models.MemberStatus
import br.com.alice.filevault.client.FileVaultActionService
import br.com.alice.filevault.models.FileType
import br.com.alice.filevault.models.PersonVaultUploadByteArray
import br.com.alice.moneyin.client.BillingAccountablePartyService
import br.com.alice.person.br.com.alice.person.exception.MemberActivationDateIsEarlierThanContractStartedDateException
import br.com.alice.person.client.MemberService
import br.com.alice.person.client.PersonService
import br.com.alice.product.client.ProductService
import com.github.kittinunf.result.failure
import com.github.kittinunf.result.success
import io.ktor.http.HttpHeaders
import io.ktor.http.HttpMethod
import io.mockk.clearAllMocks
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.every
import io.mockk.mockk
import io.mockk.mockkObject
import kotlinx.coroutines.runBlocking
import org.assertj.core.api.Assertions
import org.assertj.core.api.Assertions.assertThat
import java.time.LocalDateTime
import kotlin.test.AfterTest
import kotlin.test.BeforeTest
import kotlin.test.Test
import kotlin.test.assertTrue
import br.com.alice.business.metrics.BeneficiaryMetric as ClientBeneficiaryMetric

class BeneficiaryControllerTest : ControllerTestHelper() {
    private val beneficiaryService: BeneficiaryService = mockk()
    private val beneficiaryOnboardingService: BeneficiaryOnboardingService = mockk()
    private val personService: PersonService = mockk()
    private val billingAccountablePartyService: BillingAccountablePartyService = mockk()
    private val companyService: CompanyService = mockk()
    private val beneficiaryHubspotService: BeneficiaryHubspotService = mockk()
    private val memberService: MemberService = mockk()
    private val cassiMemberService: CassiMemberService = mockk()
    private val fileActionService: FileVaultActionService = mockk()
    private val memberContractService: MemberContractService = mockk()

    private val productService: ProductService = mockk()

    private val address = Address(
        street = "Rua Lisboa",
        number = "123",
        city = "São Paulo",
        state = State.SP,
        postalCode = "12345-678",
        complement = "Apto. 10",
        neighbourhood = "Pinheiros",
    )

    private val controller =
        BeneficiaryController(
            beneficiaryService,
            beneficiaryOnboardingService,
            personService,
            memberService,
            billingAccountablePartyService,
            companyService,
            beneficiaryHubspotService,
            productService,
            cassiMemberService,
            fileActionService,
            memberContractService,
        )

    @BeforeTest
    override fun setup() {
        super.setup()
        mockkObject(logger)
        mockkObject(ClientBeneficiaryMetric)
        module.single { controller }
    }

    @AfterTest
    fun clean() {
        super.setup()
        clearAllMocks()
        module.single { controller }
    }

    private val request = BeneficiaryRequest(
        companyId = RangeUUID.generate(),
        activatedAt = "2022-01-01",
        flowType = BeneficiaryOnboardingFlowType.NO_RISK_FLOW,
        cnpj = null
    )

    private val beneficiaryAndPersonRequest = BeneficiaryAndPersonRequest(
        companyId = RangeUUID.generate(),
        email = "<EMAIL>",
        nationalId = "***********",
        firstName = "Alice",
        lastName = "Madness",
        addressStreet = "Rua Lisboa",
        addressNumber = 123,
        addressCity = "São Paulo",
        addressState = "SP",
        activatedAt = "2022-01-01",
        flowType = BeneficiaryOnboardingFlowType.FULL_RISK_FLOW,
        initialProductId = RangeUUID.generate(),
        cassiAccountNumber = null,
        cassiExpirationDate = null,
        cassiStartDate = null,
        cnpj = null,
        mothersName = "Mother name"
    )

    private val beneficiarySimpleAndPersonRequest = BeneficiarySimpleAndPersonBatchRequest(
        companyCnpj = "****************",
        email = "<EMAIL>",
        nationalId = "***********",
        firstName = "Alice",
        lastName = "Madness",
        addressCity = address.city,
        addressNumber = address.number.toInt(),
        addressPostalCode = address.postalCode.orEmpty(),
        addressState = address.state.toString(),
        addressStreet = address.street,
        activatedAt = "2022-01-01",
        flowType = BeneficiaryOnboardingFlowType.FULL_RISK_FLOW,
        initialProductName = "Product",
        cassiAccountNumber = null,
        cassiExpirationDate = null,
        cassiStartDate = null,
        cnpj = null,
        mothersName = "Mother name"
    )

    @Test
    fun `#upload - health declaration term contract`() {
        mockkObject(BeneficiaryController.TimeHelper)

        val currentMillis = 1234L
        every { BeneficiaryController.TimeHelper.currentTimeMillis() } returns currentMillis

        val person = TestModelFactory.buildPerson()
        val member = TestModelFactory.buildMember(personId = person.id)
        val healthDeclarationFile = TestModelFactory.buildFileVault()
        val memberContractId = RangeUUID.generate()
        val memberContractTerm =
            TestModelFactory.buildMemberContractTerm(memberId = member.id, memberContractId = memberContractId)
        val memberContract = TestModelFactory.buildMemberContract(terms = listOf(memberContractTerm))
        val beneficiary = TestModelFactory.buildBeneficiary(personId = person.id, memberId = member.id)
        val ansOrientationLetter = javaClass.classLoader
            .getResource("contract/ans_orientation_letter.pdf")!!
            .readBytes()


        val fileStoreRequest = PersonVaultUploadByteArray(
            personId = member.personId,
            domain = "member",
            namespace = "term",
            originalFileName = "${currentMillis}_ANS_ORIENTATION_LETTER.pdf",
            fileType = FileType.PDF,
            fileContent = ansOrientationLetter,
            fileSize = ansOrientationLetter.size.toLong()
        )

        val ansOrientationLetterFile = FileVault(
            personId = member.personId,
            domain = "domain",
            fileType = "PDF",
            url = "https://alice.com.br"
        )

        coEvery { beneficiaryService.get(any()) } returns beneficiary
        coEvery { memberService.get(any()) } returns member
        coEvery {
            fileActionService.uploadFile(match {
                it.personId == fileStoreRequest.personId && it.originalFileName?.contains(
                    "_HEALTH_DECLARATION"
                ) == true
            })
        } returns healthDeclarationFile
        coEvery { memberContractService.create(any()) } returns memberContract
        coEvery {
            fileActionService.uploadFile(match {
                it.personId == fileStoreRequest.personId &&
                        it.originalFileName?.contains("_ANS_ORIENTATION_LETTER") == true
            })
        } returns ansOrientationLetterFile

        authenticatedAs(idToken, staff) {
            multipart(
                HttpMethod.Post, "/business/beneficiaries/${beneficiary.id}/contracts/upload_health_declaration",
                fileName = "apple.png",
            ) { response ->
                ResponseAssert.assertThat(response).isOKWithData(memberContract)
            }
        }
    }

    @Test
    fun `#index - should list and count beneficiaries with filters`() {
        val personId1 = PersonId()
        val personId2 = PersonId()
        val person1 = TestModelFactory.buildPerson(personId1)
        val person2 = TestModelFactory.buildPerson(personId2)
        val member1 = TestModelFactory.buildMember(personId1)
        val member2 = TestModelFactory.buildMember(personId2)
        val beneficiaries = listOf(
            TestModelFactory.buildBeneficiary(personId = personId1, memberId = member1.id),
            TestModelFactory.buildBeneficiary(personId = personId2, memberId = member2.id)
        )
        val companyId = RangeUUID.generate()
        val parentId = RangeUUID.generate()
        val currentPhase = BeneficiaryOnboardingPhaseType.READY_TO_ONBOARD
        val cassiMember1 = TestModelFactory.buildCassiMember(memberId = member1.id)
        val beneficiariesHubspot = listOf(
            TestModelFactory.buildBeneficiaryHubspot(beneficiaryId = beneficiaries[0].id),
            TestModelFactory.buildBeneficiaryHubspot(beneficiaryId = beneficiaries[1].id),
        )

        coEvery { beneficiaryService.findByFilters(any(), any(), any(), any(), any(), any()) } returns beneficiaries
        coEvery { beneficiaryService.countByFilters(any(), any(), any(), any()) } returns beneficiaries.size
        coEvery { personService.findByIds(any(), any(), any()) } returns listOf(person1, person2)
        coEvery { memberService.findByIds(any()) } returns listOf(member1, member2)
        coEvery { cassiMemberService.findByMemberIds(any()) } returns listOf(cassiMember1)
        coEvery { beneficiaryHubspotService.findByBeneficiaryIds(any()) } returns beneficiariesHubspot

        authenticatedAs(idToken, staff) {
            get("/business/beneficiaries?filter={\"parent_id\":\"$parentId\",\"person_id\":\"$personId1\",\"company_id\":\"$companyId\",\"current_phase\":\"$currentPhase\"}") { response ->
                ResponseAssert.assertThat(response).isSuccessfulJson()

                val content: List<BeneficiaryInfoResponse> = response.bodyAsJson()
                assertThat(content.size).isEqualTo(2)
                assertThat(content[0].id).isEqualTo(beneficiaries[0].id)
                assertThat(content[1].id).isEqualTo(beneficiaries[1].id)

                assertThat(response.headers[HttpHeaders.ContentRange]).isEqualTo("2")
            }
        }

        coVerifyOnce { memberService.findByIds(listOf(member1.id, member2.id)) }

        coVerifyOnce {
            beneficiaryService.findByFilters(
                personId1, companyId, parentId, BeneficiaryOnboardingPhaseType.READY_TO_ONBOARD, IntRange(0, 19),
                findOptions = FindOptions(withOnboarding = true)
            )
        }

        coVerifyOnce {
            personService.findByIds(
                listOf(personId1.toString(), personId2.toString()),
                withUserType = false,
                includeTest = false
            )
        }
        coVerifyOnce { cassiMemberService.findByMemberIds(listOf(member1.id, member2.id)) }
    }

    @Test
    fun `#index - should list and count beneficiaries by ids`() {
        val personId = PersonId()
        val person1 = TestModelFactory.buildPerson(personId)
        val member = TestModelFactory.buildMember(personId)
        val beneficiary = TestModelFactory.buildBeneficiary(personId = personId, memberId = member.id)
        val beneficiaryId = beneficiary.id
        val cassiMember = TestModelFactory.buildCassiMember(memberId = member.id)
        val beneficiaryHubspot = TestModelFactory.buildBeneficiaryHubspot(beneficiaryId = beneficiary.id)

        coEvery { beneficiaryService.findByIds(any(), any()) } returns listOf(beneficiary)
        coEvery { beneficiaryService.countByIds(any()) } returns listOf(beneficiary).size
        coEvery { personService.findByIds(any(), any(), any()) } returns listOf(person1)
        coEvery { memberService.findByIds(any()) } returns listOf(member)
        coEvery { cassiMemberService.findByMemberIds(any()) } returns listOf(cassiMember)
        coEvery { beneficiaryHubspotService.findByBeneficiaryIds(any()) } returns listOf(beneficiaryHubspot)

        authenticatedAs(idToken, staff) {
            get("/business/beneficiaries?filter={\"id\":[\"$beneficiaryId\"]}") { response ->
                ResponseAssert.assertThat(response).isSuccessfulJson()

                val content: List<CompanyResponse> = response.bodyAsJson()
                assertThat(content.size).isEqualTo(1)
                assertThat(content[0].id).isEqualTo(beneficiary.id)

                assertThat(response.headers[HttpHeaders.ContentRange]).isEqualTo("1")
            }
        }

        coVerifyOnce {
            beneficiaryService.findByIds(
                listOf(beneficiaryId),
                FindOptions(withOnboarding = true)
            )
        }
        coVerifyOnce { beneficiaryService.countByIds(listOf(beneficiaryId)) }
        coVerifyOnce { memberService.findByIds(listOf(member.id)) }
        coVerifyOnce {
            personService.findByIds(listOf(beneficiary.personId.toString()), withUserType = false, includeTest = false)
        }
        coVerifyOnce { cassiMemberService.findByMemberIds(listOf(member.id)) }
        coVerifyOnce { beneficiaryHubspotService.findByBeneficiaryIds(listOf(beneficiaryId)) }
        coVerifyNone { beneficiaryService.findByFilters(any(), any(), any(), any(), any()) }
        coVerifyNone { beneficiaryService.countByFilters(any(), any(), any(), any()) }
    }

    @Test
    fun `#index - should list beneficiaries by nationalIdTerm`() {
        val personId1 = PersonId()
        val personId2 = PersonId()
        val person1 = TestModelFactory.buildPerson(personId1)
        val person2 = TestModelFactory.buildPerson(personId2)
        val member1 = TestModelFactory.buildMember(personId1)
        val member2 = TestModelFactory.buildMember(personId2)
        val beneficiaries = listOf(
            TestModelFactory.buildBeneficiary(personId = personId1, memberId = member1.id),
            TestModelFactory.buildBeneficiary(personId = personId2, memberId = member2.id),
        )
        val nationalIdTerm = "test"
        val cassiMember1 = TestModelFactory.buildCassiMember(memberId = member1.id)
        val cassiMember2 = TestModelFactory.buildCassiMember(memberId = member2.id)
        val beneficiariesHubspot = listOf(
            TestModelFactory.buildBeneficiaryHubspot(beneficiaryId = beneficiaries[0].id),
            TestModelFactory.buildBeneficiaryHubspot(beneficiaryId = beneficiaries[1].id),
        )
        coEvery { personService.findBySearchTokens(any()) } returns listOf(person1, person2)
        coEvery { beneficiaryService.findByPersonIds(any(), any()) } returns beneficiaries
        coEvery { beneficiaryService.countByFilters(any(), any(), any(), any()) } returns beneficiaries.count()
        coEvery { personService.findByIds(any(), any(), any()) } returns listOf(person1, person2)
        coEvery { memberService.findByIds(any()) } returns listOf(member1, member2)
        coEvery { cassiMemberService.findByMemberIds(any()) } returns listOf(cassiMember1, cassiMember2)
        coEvery { beneficiaryHubspotService.findByBeneficiaryIds(any()) } returns beneficiariesHubspot

        authenticatedAs(idToken, staff) {
            get("/business/beneficiaries?filter={\"national_id_term\":\"$nationalIdTerm\"}") { response ->
                ResponseAssert.assertThat(response).isSuccessfulJson()

                val content: List<BeneficiaryInfoResponse> = response.bodyAsJson()
                assertThat(content.size).isEqualTo(2)
                assertThat(content[0].id).isEqualTo(beneficiaries[0].id)
                assertThat(content[1].id).isEqualTo(beneficiaries[1].id)

                assertThat(response.headers[HttpHeaders.ContentRange]).isEqualTo("2")
            }
        }

        coVerifyOnce { personService.findBySearchTokens(nationalIdTerm) }
        coVerifyOnce {
            beneficiaryService.findByPersonIds(
                listOf(personId1, personId2),
                FindOptions(withOnboarding = true)
            )
        }
        coVerifyOnce { memberService.findByIds(listOf(member1.id, member2.id)) }

        coVerifyOnce {
            personService.findByIds(
                listOf(personId1.toString(), personId2.toString()),
                withUserType = false,
                includeTest = false
            )
        }
        coVerifyOnce { cassiMemberService.findByMemberIds(listOf(member1.id, member2.id)) }
        coVerifyOnce {
            beneficiaryHubspotService.findByBeneficiaryIds(
                listOf(
                    beneficiaries[0].id,
                    beneficiaries[1].id
                )
            )
        }
    }

    @Test
    fun `#create - should create beneficiary`() = runBlocking {
        val createOptions = BeneficiaryService.CreateOptions(changeProduct = true, ignoreMembershipValidation = true)
        val cassiAccountNumber = "12345"
        val cassiStartDate = "2022-03-16"
        val cassiExpirationDate = "2023-03-16"
        val cassiMemberInfo = CassiMemberInfo(
            accountNumber = cassiAccountNumber,
            startDate = cassiStartDate,
            expirationDate = cassiExpirationDate,
        )
        val body = beneficiaryAndPersonRequest.copy(
            cassiAccountNumber = cassiMemberInfo.accountNumber,
            cassiStartDate = cassiMemberInfo.startDate,
            cassiExpirationDate = cassiMemberInfo.expirationDate
        )
        val beneficiaryTransport = body.toBeneficiaryTransport()
        val beneficiary = beneficiaryTransport.toBeneficiary(RangeUUID.generate(), PersonId())

        coEvery { beneficiaryService.createBeneficiary(any(), any(), any(), any(), any()) } returns beneficiary

        authenticatedAs(idToken, staff) {
            post("/business/beneficiaries", body = body) { response ->
                ResponseAssert.assertThat(response).isSuccessfulJson()

                val content: Beneficiary = response.bodyAsJson()
                assertThat(content.id).isEqualTo(beneficiary.id)
            }
        }

        coVerifyOnce {
            beneficiaryService.createBeneficiary(
                beneficiaryTransport,
                beneficiaryAndPersonRequest.initialProductId,
                beneficiaryAndPersonRequest.flowType,
                cassiMemberInfo,
                createOptions
            )
        }
    }

    @Test
    fun `#create - should return error when flow request is undefined`() {
        val body = beneficiaryAndPersonRequest.copy(
            flowType = BeneficiaryOnboardingFlowType.UNDEFINED,
        )

        authenticatedAs(idToken, staff) {
            post("/business/beneficiaries", body = body) { response ->
                ResponseAssert.assertThat(response).isBadRequest()

                val content: ErrorResponse = response.bodyAsJson()
                assertThat(content.code).isEqualTo("invalid_beneficiary_onboarding_flow_state")
                assertThat(content.message).isEqualTo("Fluxo de onboarding de beneficiário não é utilizado. Não foi possível atualizar o fluxo: ${body.flowType}")
            }
        }
    }

    @Test
    fun `#update - update beneficiary from request`() {
        val person = TestModelFactory.buildPerson()
        val member = TestModelFactory.buildMember(personId = person.id)
        val beneficiary = TestModelFactory.buildBeneficiary(personId = person.id, memberId = member.id)
        val cassiMember = TestModelFactory.buildCassiMember(memberId = member.id)
        val personBillingAccountableParty = TestModelFactory.buildPersonBillingAccountableParty()
        val beneficiaryHubspot = TestModelFactory.buildBeneficiaryHubspot(beneficiaryId = beneficiary.id)
        coEvery { beneficiaryService.get(any(), any()) } returns beneficiary
        coEvery { beneficiaryService.validateToUpdate(any()) } returns beneficiary
        coEvery { billingAccountablePartyService.getCurrentAssign(any()) } returns personBillingAccountableParty
        coEvery { beneficiaryService.get(any(), any()) } returns beneficiary
        coEvery { billingAccountablePartyService.getCurrentAssign(any()) } returns personBillingAccountableParty
        coEvery { personService.findByIds(any(), any(), any()) } returns listOf(person)
        coEvery { memberService.findByIds(any()) } returns listOf(member)
        coEvery { cassiMemberService.findByMemberIds(any()) } returns listOf(cassiMember)
        coEvery { beneficiaryHubspotService.findByBeneficiaryIds(any()) } returns listOf(beneficiaryHubspot)

        authenticatedAs(idToken, staff) {
            put("/business/beneficiaries/${beneficiary.id}", body = request) { response ->
                ResponseAssert.assertThat(response).isSuccessfulJson()

                val content: BeneficiaryInfoResponse = response.bodyAsJson()
                assertThat(content.id).isEqualTo(beneficiary.id)
            }
        }

        coVerify(exactly = 2) { beneficiaryService.get(beneficiary.id, FindOptions(withOnboarding = true)) }
        coVerifyOnce { beneficiaryService.validateToUpdate(match { it.id == beneficiary.id }) }
        coVerifyOnce {
            personService.findByIds(
                listOf(beneficiary.personId.toString()),
                withUserType = false,
                includeTest = false
            )
        }
        coVerifyOnce { memberService.findByIds(listOf(member.id)) }
        coVerifyOnce { cassiMemberService.findByMemberIds(listOf(member.id)) }
        coVerifyOnce { billingAccountablePartyService.getCurrentAssign(person.id) }
        coVerifyOnce { beneficiaryHubspotService.findByBeneficiaryIds(listOf(beneficiary.id)) }
    }

    @Test
    fun `#update - update cassiMember from request if needed`() {
        val person = TestModelFactory.buildPerson()
        val member = TestModelFactory.buildMember(personId = person.id)
        val beneficiary = TestModelFactory.buildBeneficiary(personId = person.id, memberId = member.id)
        val cassiMember = TestModelFactory.buildCassiMember(memberId = member.id)
        val personBillingAccountableParty = TestModelFactory.buildPersonBillingAccountableParty()
        val newAccountNumber = "********"
        val newStartDate = "1997-06-02"
        val newExpirationDate = "2022-06-02"
        val requestWithCassiInfo = request.copy(
            cassiAccountNumber = newAccountNumber,
            cassiStartDate = newStartDate,
            cassiExpirationDate = newExpirationDate
        )
        val cassiMemberUpdated = cassiMember.updateFields(
            accountNumber = newAccountNumber,
            startDate = newStartDate,
            expirationDate = newExpirationDate,
        )
        val beneficiaryHubspot = TestModelFactory.buildBeneficiaryHubspot(beneficiaryId = beneficiary.id)

        coEvery { beneficiaryService.get(any(), any()) } returns beneficiary
        coEvery { beneficiaryService.validateToUpdate(any()) } returns beneficiary
        coEvery { cassiMemberService.getByPersonId(any()) } returns cassiMember
        coEvery { cassiMemberService.update(any()) } returns cassiMemberUpdated
        coEvery { billingAccountablePartyService.getCurrentAssign(any()) } returns personBillingAccountableParty
        coEvery { personService.findByIds(any(), any(), any()) } returns listOf(person)
        coEvery { memberService.findByIds(any()) } returns listOf(member)
        coEvery { cassiMemberService.findByMemberIds(any()) } returns listOf(cassiMember)
        coEvery { beneficiaryHubspotService.findByBeneficiaryIds(any()) } returns listOf(beneficiaryHubspot)

        authenticatedAs(idToken, staff) {
            put("/business/beneficiaries/${beneficiary.id}", body = requestWithCassiInfo) { response ->
                ResponseAssert.assertThat(response).isSuccessfulJson()

                val content: BeneficiaryInfoResponse = response.bodyAsJson()
                assertThat(content.id).isEqualTo(beneficiary.id)
            }
        }
        coVerify(exactly = 2) { beneficiaryService.get(beneficiary.id, FindOptions(withOnboarding = true)) }
        coVerifyOnce { beneficiaryHubspotService.findByBeneficiaryIds(listOf(beneficiary.id)) }
        coVerifyOnce { beneficiaryService.validateToUpdate(match { it.id == beneficiary.id }) }
        coVerifyOnce { cassiMemberService.getByPersonId(beneficiary.personId) }
        coVerifyOnce { cassiMemberService.update(cassiMemberUpdated) }
        coVerifyOnce {
            personService.findByIds(
                listOf(beneficiary.personId.toString()),
                withUserType = false,
                includeTest = false
            )
        }
        coVerifyOnce { memberService.findByIds(listOf(member.id)) }
        coVerifyOnce { cassiMemberService.findByMemberIds(listOf(member.id)) }
    }

    @Test
    fun `#update - add cassiMember from request if needed`() {
        val person = TestModelFactory.buildPerson()
        val member = TestModelFactory.buildMember(personId = person.id)
        val beneficiary = TestModelFactory.buildBeneficiary(personId = person.id, memberId = member.id)
        val cassiMember = TestModelFactory.buildCassiMember(
            memberId = member.id,
            startDate = LocalDateTime.of(1997, 6, 2, 0, 0, 0),
            expirationDate = LocalDateTime.of(2022, 6, 2, 0, 0, 0)
        )
        val personBillingAccountableParty = TestModelFactory.buildPersonBillingAccountableParty()
        val requestWithCassiInfo = request.copy(
            cassiAccountNumber = cassiMember.accountNumber,
            cassiStartDate = "1997-06-02",
            cassiExpirationDate = "2022-06-02",
        )
        val beneficiaryHubspot = TestModelFactory.buildBeneficiaryHubspot(beneficiaryId = beneficiary.id)

        coEvery { beneficiaryService.get(any(), any()) } returns beneficiary
        coEvery { beneficiaryService.validateToUpdate(any()) } returns beneficiary
        coEvery { cassiMemberService.getByPersonId(any()) } returns NotFoundException()
        coEvery { cassiMemberService.add(any()) } returns cassiMember
        coEvery { billingAccountablePartyService.getCurrentAssign(any()) } returns personBillingAccountableParty
        coEvery { memberService.getCurrent(any()) } returns member
        coEvery { personService.findByIds(any(), any(), any()) } returns listOf(person)
        coEvery { memberService.findByIds(any()) } returns listOf(member)
        coEvery { cassiMemberService.findByMemberIds(any()) } returns listOf(cassiMember)
        coEvery { beneficiaryHubspotService.findByBeneficiaryIds(any()) } returns listOf(beneficiaryHubspot)

        authenticatedAs(idToken, staff) {
            put("/business/beneficiaries/${beneficiary.id}", body = requestWithCassiInfo) { response ->
                ResponseAssert.assertThat(response).isSuccessfulJson()

                val content: BeneficiaryInfoResponse = response.bodyAsJson()
                assertThat(content.id).isEqualTo(beneficiary.id)
            }
        }
        coVerify(exactly = 2) { beneficiaryService.get(beneficiary.id, FindOptions(withOnboarding = true)) }
        coVerifyOnce { beneficiaryService.validateToUpdate(match { it.id == beneficiary.id }) }
        coVerifyOnce { cassiMemberService.getByPersonId(beneficiary.personId) }
        coVerifyOnce { memberService.getCurrent(beneficiary.personId) }
        coVerifyOnce { beneficiaryHubspotService.findByBeneficiaryIds(listOf(beneficiary.id)) }
        coVerifyOnce {
            cassiMemberService.add(match {
                it.accountNumber == cassiMember.accountNumber &&
                        it.memberId == cassiMember.memberId &&
                        it.expirationDate == cassiMember.expirationDate &&
                        it.startDate == cassiMember.startDate
            })
        }
        coVerifyOnce {
            personService.findByIds(
                listOf(beneficiary.personId.toString()),
                withUserType = false,
                includeTest = false
            )
        }
        coVerifyOnce { memberService.findByIds(listOf(member.id)) }
        coVerifyOnce { cassiMemberService.findByMemberIds(listOf(member.id)) }
    }

    @Test
    fun `#update - update hiredAt from request if needed`() {
        val person = TestModelFactory.buildPerson()
        val member = TestModelFactory.buildMember(personId = person.id)
        val hiredAt = "2022-01-01".toLocalDateStartOfDay()
        val beneficiary =
            TestModelFactory.buildBeneficiary(personId = person.id, memberId = member.id, hiredAt = hiredAt)
        val cassiMember = TestModelFactory.buildCassiMember(memberId = member.id)
        val personBillingAccountableParty = TestModelFactory.buildPersonBillingAccountableParty()
        val beneficiaryHubspot = TestModelFactory.buildBeneficiaryHubspot(beneficiaryId = beneficiary.id)
        val requestWithHiredAt = request.copy(
            hiredAt = "2022-01-01"
        )

        println(requestWithHiredAt)

        coEvery { beneficiaryService.get(any(), any()) } returns beneficiary
        coEvery { beneficiaryService.validateToUpdate(any()) } returns beneficiary
        coEvery { billingAccountablePartyService.getCurrentAssign(any()) } returns personBillingAccountableParty
        coEvery { beneficiaryService.get(any(), any()) } returns beneficiary
        coEvery { billingAccountablePartyService.getCurrentAssign(any()) } returns personBillingAccountableParty
        coEvery { personService.findByIds(any(), any(), any()) } returns listOf(person)
        coEvery { memberService.findByIds(any()) } returns listOf(member)
        coEvery { cassiMemberService.findByMemberIds(any()) } returns listOf(cassiMember)
        coEvery { beneficiaryHubspotService.findByBeneficiaryIds(any()) } returns listOf(beneficiaryHubspot)

        authenticatedAs(idToken, staff) {
            put("/business/beneficiaries/${beneficiary.id}", body = requestWithHiredAt) { response ->
                ResponseAssert.assertThat(response).isSuccessfulJson()

                val content: BeneficiaryInfoResponse = response.bodyAsJson()
                assertThat(content.id).isEqualTo(beneficiary.id)
                assertThat(content.hiredAt).isEqualTo(requestWithHiredAt.hiredAt)
                println(content.hiredAt)
            }
        }

        coVerify(exactly = 2) { beneficiaryService.get(beneficiary.id, FindOptions(withOnboarding = true)) }
        coVerifyOnce { beneficiaryService.validateToUpdate(match { it.id == beneficiary.id }) }
        coVerifyOnce {
            personService.findByIds(
                listOf(beneficiary.personId.toString()),
                withUserType = false,
                includeTest = false
            )
        }
        coVerifyOnce { memberService.findByIds(listOf(member.id)) }
        coVerifyOnce { cassiMemberService.findByMemberIds(listOf(member.id)) }
        coVerifyOnce { billingAccountablePartyService.getCurrentAssign(person.id) }
        coVerifyOnce { beneficiaryHubspotService.findByBeneficiaryIds(listOf(beneficiary.id)) }
    }

    @Test
    fun `update - should update undefined flow successfully`() {
        val person = TestModelFactory.buildPerson()
        val member = TestModelFactory.buildMember(personId = person.id)
        val cassiMember = TestModelFactory.buildCassiMember(memberId = member.id)
        val personBillingAccountableParty = TestModelFactory.buildPersonBillingAccountableParty()

        val onboarding = TestModelFactory.buildBeneficiaryOnboarding(flowType = BeneficiaryOnboardingFlowType.UNDEFINED)
        val beneficiary =
            TestModelFactory.buildBeneficiary(personId = person.id, onboarding = onboarding, memberId = member.id)
        val onboardingWithNewFlowType = onboarding.copy(flowType = BeneficiaryOnboardingFlowType.FULL_RISK_FLOW)
        val beneficiaryWithNewOnboarding = beneficiary.copy(onboarding = onboardingWithNewFlowType)
        val beneficiaryHubspot = TestModelFactory.buildBeneficiaryHubspot(beneficiaryId = beneficiary.id)

        val body = request.copy(flowType = BeneficiaryOnboardingFlowType.FULL_RISK_FLOW)

        coEvery { beneficiaryHubspotService.findByBeneficiaryIds(any()) } returns listOf(beneficiaryHubspot)
        coEvery {
            beneficiaryService.get(
                any(),
                any()
            )
        } returns beneficiary andThen beneficiaryWithNewOnboarding.success()
        coEvery { beneficiaryService.validateToUpdate(any()) } returns beneficiary
        coEvery { beneficiaryOnboardingService.update(any()) } returns onboardingWithNewFlowType
        coEvery { billingAccountablePartyService.getCurrentAssign(any()) } returns personBillingAccountableParty
        coEvery { personService.findByIds(any(), any(), any()) } returns listOf(person)
        coEvery { memberService.findByIds(any()) } returns listOf(member)
        coEvery { cassiMemberService.findByMemberIds(any()) } returns listOf(cassiMember)

        val expectedResponse = beneficiaryWithNewOnboarding
            .toBeneficiaryInfoResponse(
                person,
                member,
                cassiMember,
                beneficiaryHubspot.externalDealId,
            )
            .copy(billingAccountablePartyId = personBillingAccountableParty.billingAccountablePartyId)

        authenticatedAs(idToken, staff) {
            put("/business/beneficiaries/${beneficiary.id}", body = body) { response ->
                ResponseAssert.assertThat(response).isOKWithData(expectedResponse)
                assertThat(expectedResponse.flowType).isEqualTo(body.flowType)
            }
        }

        coVerify(exactly = 2) { beneficiaryService.get(beneficiary.id, FindOptions(withOnboarding = true)) }
        coVerifyOnce { beneficiaryHubspotService.findByBeneficiaryIds(listOf(beneficiary.id)) }
        coVerifyOnce { beneficiaryService.validateToUpdate(match { it.id == beneficiary.id }) }
        coVerifyOnce { beneficiaryOnboardingService.update(onboardingWithNewFlowType) }
        coVerifyOnce {
            personService.findByIds(
                listOf(beneficiary.personId.toString()),
                withUserType = false,
                includeTest = false
            )
        }
        coVerifyOnce { memberService.findByIds(listOf(member.id)) }
        coVerifyOnce { cassiMemberService.findByMemberIds(listOf(member.id)) }
        coVerifyOnce { billingAccountablePartyService.getCurrentAssign(person.id) }
    }

    @Test
    fun `update - should not call onboardingUpdate when the requested flow is same as current flow type`() {
        val person = TestModelFactory.buildPerson()
        val member = TestModelFactory.buildMember(personId = person.id)
        val cassiMember = TestModelFactory.buildCassiMember(memberId = member.id)
        val personBillingAccountableParty = TestModelFactory.buildPersonBillingAccountableParty()

        val onboarding = TestModelFactory.buildBeneficiaryOnboarding(flowType = BeneficiaryOnboardingFlowType.UNDEFINED)
        val beneficiary =
            TestModelFactory.buildBeneficiary(personId = person.id, onboarding = onboarding, memberId = member.id)
        val onboardingWithNewFlowType = onboarding.copy(flowType = BeneficiaryOnboardingFlowType.UNDEFINED)
        val beneficiaryWithNewOnboarding = beneficiary.copy(onboarding = onboardingWithNewFlowType)
        val beneficiaryHubspot = TestModelFactory.buildBeneficiaryHubspot(beneficiaryId = beneficiary.id)

        val body = request.copy(flowType = BeneficiaryOnboardingFlowType.UNDEFINED)

        coEvery {
            beneficiaryService.get(
                any(),
                any()
            )
        } returns beneficiary andThen beneficiaryWithNewOnboarding.success()
        coEvery { beneficiaryService.validateToUpdate(any()) } returns beneficiary
        coEvery { billingAccountablePartyService.getCurrentAssign(any()) } returns personBillingAccountableParty
        coEvery { personService.findByIds(any(), any(), any()) } returns listOf(person)
        coEvery { memberService.findByIds(any()) } returns listOf(member)
        coEvery { cassiMemberService.findByMemberIds(any()) } returns listOf(cassiMember)
        coEvery { beneficiaryHubspotService.findByBeneficiaryIds(any()) } returns listOf(beneficiaryHubspot)

        val expectedResponse = beneficiaryWithNewOnboarding
            .toBeneficiaryInfoResponse(
                person,
                member,
                cassiMember,
                beneficiaryHubspot.externalDealId,
            )
            .copy(billingAccountablePartyId = personBillingAccountableParty.billingAccountablePartyId)

        authenticatedAs(idToken, staff) {
            put("/business/beneficiaries/${beneficiary.id}", body = body) { response ->
                ResponseAssert.assertThat(response).isOKWithData(expectedResponse)
                assertThat(expectedResponse.flowType).isEqualTo(body.flowType)
            }
        }

        coVerify(exactly = 2) { beneficiaryService.get(beneficiary.id, FindOptions(withOnboarding = true)) }
        coVerifyOnce { beneficiaryService.validateToUpdate(match { it.id == beneficiary.id }) }
        coVerifyOnce { beneficiaryHubspotService.findByBeneficiaryIds(listOf(beneficiary.id)) }
        coVerifyNone { beneficiaryOnboardingService.update(any()) }
        coVerifyOnce {
            personService.findByIds(
                listOf(beneficiary.personId.toString()),
                withUserType = false,
                includeTest = false
            )
        }
        coVerifyOnce { memberService.findByIds(listOf(member.id)) }
        coVerifyOnce { cassiMemberService.findByMemberIds(listOf(member.id)) }
        coVerifyOnce { billingAccountablePartyService.getCurrentAssign(person.id) }
    }

    @Test
    fun `update - should not update flow when beneficiary has no onboarding`() {
        val person = TestModelFactory.buildPerson()
        val member = TestModelFactory.buildMember(personId = person.id)
        val cassiMember = TestModelFactory.buildCassiMember(memberId = member.id)
        val personBillingAccountableParty = TestModelFactory.buildPersonBillingAccountableParty()
        val beneficiary = TestModelFactory.buildBeneficiary(personId = person.id, memberId = member.id)
        val beneficiaryHubspot = TestModelFactory.buildBeneficiaryHubspot(beneficiaryId = beneficiary.id)
        val body = request.copy(flowType = BeneficiaryOnboardingFlowType.FULL_RISK_FLOW)

        coEvery { beneficiaryService.get(any(), any()) } returns beneficiary
        coEvery { beneficiaryService.validateToUpdate(any()) } returns beneficiary
        coEvery { beneficiaryOnboardingService.update(any()) } returns InvalidBeneficiaryOnboardingFlowState(
            BeneficiaryOnboardingFlowType.FULL_RISK_FLOW
        )
        coEvery { billingAccountablePartyService.getCurrentAssign(any()) } returns personBillingAccountableParty
        coEvery { personService.findByIds(any(), any(), any()) } returns listOf(person)
        coEvery { memberService.findByIds(any()) } returns listOf(member)
        coEvery { cassiMemberService.findByMemberIds(any()) } returns listOf(cassiMember)
        coEvery { beneficiaryHubspotService.findByBeneficiaryIds(any()) } returns listOf(beneficiaryHubspot)

        val expectedResponse = beneficiary
            .toBeneficiaryInfoResponse(
                person,
                member,
                cassiMember,
                beneficiaryHubspot.externalDealId,
            )
            .copy(billingAccountablePartyId = personBillingAccountableParty.billingAccountablePartyId)

        authenticatedAs(idToken, staff) {
            put("/business/beneficiaries/${beneficiary.id}", body = body) { response ->
                ResponseAssert.assertThat(response).isOKWithData(expectedResponse)
                assertThat(expectedResponse.flowType).isNotEqualTo(body.flowType)
            }
        }

        coVerify(exactly = 2) { beneficiaryService.get(beneficiary.id, FindOptions(withOnboarding = true)) }
        coVerifyOnce { beneficiaryService.validateToUpdate(match { it.id == beneficiary.id }) }
        coVerifyNone { beneficiaryOnboardingService.update(any()) }
        coVerifyOnce {
            personService.findByIds(
                listOf(beneficiary.personId.toString()),
                withUserType = false,
                includeTest = false
            )
        }
        coVerifyOnce { memberService.findByIds(listOf(member.id)) }
        coVerifyOnce { cassiMemberService.findByMemberIds(listOf(member.id)) }
        coVerifyOnce { billingAccountablePartyService.getCurrentAssign(person.id) }
    }


    @Test
    fun `update - should not update flow when try update flow already defined`() {
        val person = TestModelFactory.buildPerson()
        val member = TestModelFactory.buildMember(personId = person.id)
        val cassiMember = TestModelFactory.buildCassiMember(memberId = member.id)
        val personBillingAccountableParty = TestModelFactory.buildPersonBillingAccountableParty()

        val onboarding =
            TestModelFactory.buildBeneficiaryOnboarding(flowType = BeneficiaryOnboardingFlowType.NO_RISK_FLOW)
        val beneficiary =
            TestModelFactory.buildBeneficiary(personId = person.id, onboarding = onboarding, memberId = member.id)

        val onboardingWithNewFlowType = onboarding.copy(flowType = BeneficiaryOnboardingFlowType.FULL_RISK_FLOW)
        val beneficiaryHubspot = TestModelFactory.buildBeneficiaryHubspot(beneficiaryId = beneficiary.id)
        val body = request.copy(flowType = BeneficiaryOnboardingFlowType.FULL_RISK_FLOW)

        coEvery { beneficiaryService.get(any(), any()) } returns beneficiary
        coEvery { beneficiaryService.validateToUpdate(any()) } returns beneficiary
        coEvery { beneficiaryOnboardingService.update(any()) } returns InvalidBeneficiaryOnboardingFlowState(
            BeneficiaryOnboardingFlowType.FULL_RISK_FLOW
        )

        coEvery { beneficiaryService.get(any(), any()) } returns beneficiary
        coEvery { billingAccountablePartyService.getCurrentAssign(any()) } returns personBillingAccountableParty
        coEvery { personService.findByIds(any(), any(), any()) } returns listOf(person)
        coEvery { memberService.findByIds(any()) } returns listOf(member)
        coEvery { cassiMemberService.findByMemberIds(any()) } returns listOf(cassiMember)
        coEvery { beneficiaryHubspotService.findByBeneficiaryIds(any()) } returns listOf(beneficiaryHubspot)

        val expectedResponse = beneficiary
            .toBeneficiaryInfoResponse(
                person,
                member,
                cassiMember,
                beneficiaryHubspot.externalDealId,
            )
            .copy(billingAccountablePartyId = personBillingAccountableParty.billingAccountablePartyId)

        authenticatedAs(idToken, staff) {
            put("/business/beneficiaries/${beneficiary.id}", body = body) { response ->
                ResponseAssert.assertThat(response).isOKWithData(expectedResponse)
                assertThat(expectedResponse.flowType).isNotEqualTo(body.flowType)
            }
        }

        coVerify(exactly = 2) { beneficiaryService.get(beneficiary.id, FindOptions(withOnboarding = true)) }
        coVerifyOnce { beneficiaryService.validateToUpdate(match { it.id == beneficiary.id }) }
        coVerifyOnce { beneficiaryOnboardingService.update(onboardingWithNewFlowType) }
        coVerifyOnce {
            personService.findByIds(
                listOf(beneficiary.personId.toString()),
                withUserType = false,
                includeTest = false
            )
        }
        coVerifyOnce { memberService.findByIds(listOf(member.id)) }
        coVerifyOnce { cassiMemberService.findByMemberIds(listOf(member.id)) }
        coVerifyOnce { billingAccountablePartyService.getCurrentAssign(person.id) }
        coVerifyOnce { beneficiaryHubspotService.findByBeneficiaryIds(listOf(beneficiary.id)) }
    }

    @Test
    fun `#get - get beneficiary`() {
        val personBillingAccountableParty = TestModelFactory.buildPersonBillingAccountableParty()
        val person = TestModelFactory.buildPerson()
        val member = TestModelFactory.buildMember(personId = person.id)
        val beneficiary = TestModelFactory.buildBeneficiary(personId = person.id, memberId = member.id)
        val cassiMember = TestModelFactory.buildCassiMember(memberId = member.id)
        val beneficiaryHubspot = TestModelFactory.buildBeneficiaryHubspot(beneficiaryId = beneficiary.id)

        coEvery { beneficiaryService.get(any(), any()) } returns beneficiary
        coEvery { billingAccountablePartyService.getCurrentAssign(any()) } returns personBillingAccountableParty
        coEvery { personService.findByIds(any(), any(), any()) } returns listOf(person)
        coEvery { memberService.findByIds(any()) } returns listOf(member)
        coEvery { cassiMemberService.findByMemberIds(any()) } returns listOf(cassiMember)
        coEvery { beneficiaryHubspotService.findByBeneficiaryIds(any()) } returns listOf(beneficiaryHubspot)

        authenticatedAs(idToken, staff) {
            get("/business/beneficiaries/${beneficiary.id}") { response ->
                ResponseAssert.assertThat(response).isSuccessfulJson()

                val content: BeneficiaryInfoResponse = response.bodyAsJson()
                assertThat(content.id).isEqualTo(beneficiary.id)
            }
        }

        coVerifyOnce { beneficiaryService.get(beneficiary.id, FindOptions(withOnboarding = true)) }
        coVerifyOnce {
            personService.findByIds(
                listOf(beneficiary.personId.toString()),
                withUserType = false,
                includeTest = false
            )
        }
        coVerifyOnce { memberService.findByIds(listOf(member.id)) }
        coVerifyOnce { cassiMemberService.findByMemberIds(listOf(member.id)) }
        coVerifyOnce { billingAccountablePartyService.getCurrentAssign(person.id) }
        coVerifyOnce { beneficiaryHubspotService.findByBeneficiaryIds(listOf(beneficiary.id)) }
    }

    @Test
    fun `#addBatch - add batch beneficiary`() {
        val createOptions = BeneficiaryService.CreateOptions(changeProduct = true, ignoreMembershipValidation = true)
        val parentNationalId = "parent-national-id"
        val otherPerson = TestModelFactory.buildPerson(nationalId = "**********")
        val person = TestModelFactory.buildPerson()
        val parentPerson = TestModelFactory.buildPerson(nationalId = parentNationalId)
        val parentBeneficiary = TestModelFactory.buildBeneficiary(personId = parentPerson.id)
        val company = TestModelFactory.buildCompany()
        val product = TestModelFactory.buildProduct(title = beneficiarySimpleAndPersonRequest.initialProductName!!)
        val cassiAccountNumber = "12345"
        val cassiStartDate = "2022-03-16"
        val cassiExpirationDate = "2023-03-16"
        val beneficiarySimpleAndPersonRequest = beneficiarySimpleAndPersonRequest.copy(
            companyCnpj = company.cnpj,
            parentNationalId = parentNationalId,
            nationalId = person.nationalId
        )
        val cassiMemberInfo = CassiMemberInfo(
            accountNumber = cassiAccountNumber,
            startDate = cassiStartDate,
            expirationDate = cassiExpirationDate,
        )

        val otherBeneficiarySimpleAndPersonRequest = beneficiarySimpleAndPersonRequest.copy(
            nationalId = otherPerson.nationalId,
            email = "<EMAIL>",
            parentNationalId = parentNationalId,
            cassiAccountNumber = cassiAccountNumber,
            cassiStartDate = cassiStartDate,
            cassiExpirationDate = cassiExpirationDate
        )
        val beneficiaryTransport = beneficiarySimpleAndPersonRequest
            .toBeneficiaryAndPersonRequest(
                listOf(BeneficiaryWithPerson(parentBeneficiary, parentPerson)),
                listOf(company),
                product.id
            ).toBeneficiaryTransport()
        val otherBeneficiaryTransport = otherBeneficiarySimpleAndPersonRequest
            .toBeneficiaryAndPersonRequest(
                listOf(BeneficiaryWithPerson(parentBeneficiary, parentPerson)),
                listOf(company),
                product.id
            ).toBeneficiaryTransport()

        val beneficiary = beneficiaryTransport.toBeneficiary(RangeUUID.generate(), person.id)
        val otherBeneficiary = beneficiaryTransport.toBeneficiary(RangeUUID.generate(), otherPerson.id)

        val body = BeneficiaryAndPersonRequestBatch(
            listOf(
                beneficiarySimpleAndPersonRequest,
                otherBeneficiarySimpleAndPersonRequest
            )
        )

        coEvery { companyService.findByCnpjs(any()) } returns listOf(company)
        coEvery { personService.findByNationalIds(any()) } returns listOf(parentPerson)
        coEvery { beneficiaryService.findByPersonIds(any()) } returns listOf(parentBeneficiary)
        coEvery {
            beneficiaryService.createBeneficiary(
                beneficiaryTransport, product.id, BeneficiaryOnboardingFlowType.FULL_RISK_FLOW, null, createOptions
            )
        } returns beneficiary
        coEvery {
            beneficiaryService.createBeneficiary(
                otherBeneficiaryTransport,
                product.id,
                BeneficiaryOnboardingFlowType.FULL_RISK_FLOW,
                cassiMemberInfo,
                createOptions
            )
        } returns otherBeneficiary
        coEvery { companyService.getRootCompany(any()) } returns company
        coEvery { productService.getByTitles(any()) } returns listOf(product)

        authenticatedAs(idToken, staff) {
            post("/business/beneficiaries/batch", body = body) { response ->
                ResponseAssert.assertThat(response).isSuccessfulJson()

                val content: BeneficiaryBatchResponse = response.bodyAsJson()
                assertThat(content.failures).isEqualTo(emptyMap<String, String>())
            }
        }

        coVerifyOnce { companyService.findByCnpjs(listOf(beneficiarySimpleAndPersonRequest.companyCnpj)) }
        coVerifyOnce { beneficiaryService.findByPersonIds(listOf(parentPerson.id)) }
        coVerifyOnce { productService.getByTitles(listOf(product.title)) }
    }

    @Test
    fun `#addBatch - if company cnpj is invalid`() {
        val person = TestModelFactory.buildPerson()
        val billingAccountableParty = TestModelFactory.buildBillingAccountableParty()
        val company = TestModelFactory.buildCompany()
        val personBillingAccountableParty = TestModelFactory.buildPersonBillingAccountableParty()
        val product = TestModelFactory.buildProduct(title = beneficiarySimpleAndPersonRequest.initialProductName!!)

        val body = BeneficiaryAndPersonRequestBatch(
            listOf(
                beneficiarySimpleAndPersonRequest
            )
        )
        coEvery { companyService.findByCnpjs(any()) } returns listOf(company)
        coEvery { personService.create(any()) } returns person
        coEvery { companyService.getRootCompany(any()) } returns company
        coEvery { billingAccountablePartyService.get(any()) } returns billingAccountableParty
        coEvery { billingAccountablePartyService.assign(any(), any()) } returns personBillingAccountableParty
        coEvery { productService.getByTitles(any()) } returns listOf(product)

        authenticatedAs(idToken, staff) {
            post("/business/beneficiaries/batch", body = body) { response ->
                ResponseAssert.assertThat(response).isBadRequest()
            }
        }

        coVerifyOnce { companyService.findByCnpjs(listOf(beneficiarySimpleAndPersonRequest.companyCnpj)) }
        coVerifyNone { personService.findByNationalIds(any()) }
        coVerifyNone { personService.create(any()) }
        coVerifyNone { billingAccountablePartyService.get(any()) }
        coVerifyNone { billingAccountablePartyService.assign(any(), any()) }
        coVerifyOnce { productService.getByTitles(listOf(product.title)) }
    }

    @Test
    fun `#addBatch - if parent national id is invalid`() {
        val person = TestModelFactory.buildPerson()
        val parentBeneficiary = TestModelFactory.buildBeneficiary(personId = person.id)
        val billingAccountableParty = TestModelFactory.buildBillingAccountableParty()
        val company = TestModelFactory.buildCompany()
        val personBillingAccountableParty = TestModelFactory.buildPersonBillingAccountableParty()
        val product = TestModelFactory.buildProduct(title = beneficiarySimpleAndPersonRequest.initialProductName!!)

        val beneficiarySimpleAndPersonRequest = beneficiarySimpleAndPersonRequest.copy(parentNationalId = "123")
        val body = BeneficiaryAndPersonRequestBatch(
            listOf(
                beneficiarySimpleAndPersonRequest
            )
        )

        coEvery { companyService.findByCnpjs(any()) } returns listOf(company)
        coEvery { personService.findByNationalIds(any()) } returns listOf(person)
        coEvery { personService.create(any()) } returns person
        coEvery { beneficiaryService.findByPersonIds(any()) } returns listOf(parentBeneficiary)
        coEvery { companyService.getRootCompany(any()) } returns company
        coEvery { billingAccountablePartyService.get(any()) } returns billingAccountableParty
        coEvery { billingAccountablePartyService.assign(any(), any()) } returns personBillingAccountableParty
        coEvery { productService.getByTitles(any()) } returns listOf(product)

        authenticatedAs(idToken, staff) {
            post("/business/beneficiaries/batch", body = body) { response ->
                ResponseAssert.assertThat(response).isBadRequest()
            }
        }

        coVerifyOnce { companyService.findByCnpjs(listOf(beneficiarySimpleAndPersonRequest.companyCnpj)) }
        coVerifyOnce { personService.findByNationalIds(listOf(beneficiarySimpleAndPersonRequest.parentNationalId!!)) }
        coVerifyNone { personService.create(any()) }
        coVerifyNone { billingAccountablePartyService.get(any()) }
        coVerifyNone { billingAccountablePartyService.assign(any(), any()) }
        coVerifyOnce { productService.getByTitles(listOf(product.title)) }
    }

    @Test
    fun `#addBatch - add batch beneficiary with one error inside batch`() {
        val company = TestModelFactory.buildCompany()
        val product = TestModelFactory.buildProduct(title = beneficiarySimpleAndPersonRequest.initialProductName!!)
        val createOptions = BeneficiaryService.CreateOptions(changeProduct = true, ignoreMembershipValidation = true)
        val beneficiarySimpleAndPersonRequest = beneficiarySimpleAndPersonRequest.copy(companyCnpj = company.cnpj)
        val otherBeneficiarySimpleAndPersonRequest = beneficiarySimpleAndPersonRequest.copy(
            nationalId = "************",
            email = "<EMAIL>",
        )
        val body = BeneficiaryAndPersonRequestBatch(
            listOf(
                beneficiarySimpleAndPersonRequest,
                otherBeneficiarySimpleAndPersonRequest
            )
        )
        val beneficiaryTransport = beneficiarySimpleAndPersonRequest
            .toBeneficiaryAndPersonRequest(emptyList(), listOf(company), product.id).toBeneficiaryTransport()
        val beneficiary = beneficiaryTransport.toBeneficiary(RangeUUID.generate(), PersonId())
        val otherBeneficiaryTransport = otherBeneficiarySimpleAndPersonRequest
            .toBeneficiaryAndPersonRequest(emptyList(), listOf(company), product.id).toBeneficiaryTransport()

        coEvery { companyService.findByCnpjs(any()) } returns listOf(company)
        coEvery {
            beneficiaryService.createBeneficiary(
                beneficiaryTransport, product.id, BeneficiaryOnboardingFlowType.FULL_RISK_FLOW, null, createOptions
            )
        } returns beneficiary
        coEvery {
            beneficiaryService.createBeneficiary(
                otherBeneficiaryTransport, product.id, BeneficiaryOnboardingFlowType.FULL_RISK_FLOW, null, createOptions
            )
        } returns Exception("")
        coEvery { companyService.getRootCompany(any()) } returns company
        coEvery { productService.getByTitles(any()) } returns listOf(product)

        authenticatedAs(idToken, staff) {
            post("/business/beneficiaries/batch", body = body) { response ->
                ResponseAssert.assertThat(response).isSuccessfulJson()

                val content: BeneficiaryBatchResponse = response.bodyAsJson()
                assertThat(content.failures.size).isEqualTo(1)
            }
        }

        coVerifyOnce { companyService.findByCnpjs(listOf(beneficiarySimpleAndPersonRequest.companyCnpj)) }
        coVerifyOnce { productService.getByTitles(listOf(product.title)) }
    }

    @Test
    fun `#moveToNextPhaseByIds - should move beneficiaries to next phase succesfully`() {
        val beneficiary = TestModelFactory.buildBeneficiary(memberStatus = MemberStatus.PENDING)
        val beneficiaryOP =
            TestModelFactory.buildBeneficiaryOnboardingPhase(phase = BeneficiaryOnboardingPhaseType.READY_TO_ONBOARD)
        val beneficiaryOnboarding =
            TestModelFactory.buildBeneficiaryOnboarding(beneficiaryId = beneficiary.id, phases = listOf(beneficiaryOP))

        val otherBeneficiary = TestModelFactory.buildBeneficiary(memberStatus = MemberStatus.PENDING)
        val otherBeneficiaryOP =
            TestModelFactory.buildBeneficiaryOnboardingPhase(phase = BeneficiaryOnboardingPhaseType.HEALTH_DECLARATION)
        val otherBeneficiaryOnboarding =
            TestModelFactory.buildBeneficiaryOnboarding(
                beneficiaryId = otherBeneficiary.id,
                phases = listOf(otherBeneficiaryOP)
            )

        val canceledBeneficiary = TestModelFactory.buildBeneficiary(memberStatus = MemberStatus.CANCELED)
        val canceledBeneficiaryOP =
            TestModelFactory.buildBeneficiaryOnboardingPhase(phase = BeneficiaryOnboardingPhaseType.HEALTH_DECLARATION)
        val canceledBeneficiaryOnboarding =
            TestModelFactory.buildBeneficiaryOnboarding(
                beneficiaryId = canceledBeneficiary.id,
                phases = listOf(canceledBeneficiaryOP)
            )

        val body = BeneficiaryIdsRequest(
            beneficiaryIds = listOf(
                beneficiary.id,
                otherBeneficiary.id,
                canceledBeneficiary.id,
            )
        )

        coEvery {
            beneficiaryOnboardingService.findByBeneficiaryIds(
                listOf(
                    beneficiary.id,
                    otherBeneficiary.id,
                    canceledBeneficiary.id,
                )
            )
        } returns listOf(
            beneficiaryOnboarding,
            otherBeneficiaryOnboarding,
            canceledBeneficiaryOnboarding,
        )
        coEvery {
            beneficiaryService.findByIds(
                listOf(
                    beneficiaryOnboarding.beneficiaryId,
                    otherBeneficiaryOnboarding.beneficiaryId,
                    canceledBeneficiaryOnboarding.beneficiaryId,
                )
            )
        } returns listOf(beneficiary, otherBeneficiary, canceledBeneficiary)

        coEvery { beneficiaryOnboardingService.moveToNextPhase(beneficiary.id) } returns beneficiaryOP
        coEvery { beneficiaryOnboardingService.moveToNextPhase(otherBeneficiary.id) } returns otherBeneficiaryOP

        authenticatedAs(idToken, staff) {
            post("/business/beneficiaries/move_to_next_phase", body = body) { response ->
                ResponseAssert.assertThat(response).isSuccessfulJson()

                val content: List<BeneficiaryOnboardingPhase> = response.bodyAsJson()
                assertThat(content.size).isEqualTo(2)
                assertTrue { content.any { it.id == beneficiaryOP.id } }
                assertTrue { content.any { it.id == otherBeneficiaryOP.id } }
            }
        }

        coVerify(exactly = 2) { beneficiaryOnboardingService.moveToNextPhase(any()) }
        coVerifyNone { beneficiaryOnboardingService.moveToNextPhase(canceledBeneficiary.id) }
    }

    @Test
    fun `#moveToNextPhaseByIds - should allow beneficiary move to next phase when membership is activated`() {
        val person = TestModelFactory.buildPerson()
        val member = TestModelFactory.buildMember(personId = person.id, status = MemberStatus.ACTIVE)
        val beneficiary = TestModelFactory.buildBeneficiary(
            personId = person.id,
            memberId = member.id,
            memberStatus = MemberStatus.ACTIVE,
        )
        val beneficiaryOP =
            TestModelFactory.buildBeneficiaryOnboardingPhase(phase = BeneficiaryOnboardingPhaseType.WAITING_FOR_REVIEW)
        val beneficiaryOnboarding =
            TestModelFactory.buildBeneficiaryOnboarding(beneficiaryId = beneficiary.id, phases = listOf(beneficiaryOP))

        val body = BeneficiaryIdsRequest(
            beneficiaryIds = listOf(
                beneficiary.id,
            )
        )

        coEvery { memberService.get(member.id) } returns member
        coEvery { beneficiaryService.get(beneficiary.id) } returns beneficiary
        coEvery { beneficiaryOnboardingService.findByBeneficiaryIds(listOf(beneficiary.id)) } returns listOf(
            beneficiaryOnboarding
        )
        coEvery {
            beneficiaryService.findByIds(
                listOf(
                    beneficiaryOnboarding.beneficiaryId,
                )
            )
        } returns listOf(beneficiary)
        coEvery { beneficiaryOnboardingService.moveToNextPhase(beneficiary.id) } returns beneficiaryOP

        authenticatedAs(idToken, staff) {
            post("/business/beneficiaries/move_to_next_phase", body = body) { response ->
                ResponseAssert.assertThat(response).isSuccessfulJson()

                val content: List<BeneficiaryOnboardingPhase> = response.bodyAsJson()
                assertTrue { content.any { it.id == beneficiaryOP.id } }
            }
        }
    }

    @Test
    fun `#moveToNextPhaseById - should throw error when move beneficiary to next phase with undefined flow`() {
        val beneficiary = TestModelFactory.buildBeneficiary(memberStatus = MemberStatus.PENDING)
        val beneficiaryOP =
            TestModelFactory.buildBeneficiaryOnboardingPhase(phase = BeneficiaryOnboardingPhaseType.READY_TO_ONBOARD)
        val beneficiaryOnboarding = TestModelFactory.buildBeneficiaryOnboarding(
            beneficiaryId = beneficiary.id,
            phases = listOf(beneficiaryOP),
            flowType = BeneficiaryOnboardingFlowType.UNDEFINED,
        )

        val body = BeneficiaryIdsRequest(beneficiaryIds = listOf(beneficiary.id))

        coEvery { beneficiaryOnboardingService.findByBeneficiaryIds(listOf(beneficiary.id)) } returns listOf(
            beneficiaryOnboarding
        )
        coEvery {
            beneficiaryService.findByIds(
                listOf(
                    beneficiaryOnboarding.beneficiaryId,
                )
            )
        } returns listOf(beneficiary)

        coEvery { beneficiaryOnboardingService.moveToNextPhase(beneficiary.id) } returns beneficiaryOP

        authenticatedAs(idToken, staff) {
            post("/business/beneficiaries/move_to_next_phase", body = body) { response ->
                ResponseAssert.assertThat(response)
                    .isBadRequestWithErrorCode("invalid_beneficiary_onboarding_move_phase")
            }
        }
    }

    @Test
    fun `#moveToNextPhaseById - should throw an error when trying to move the beneficiary to the next phase it is on WAITING_FOR_REVIEW phase but its status is PENDING`() {
        val beneficiary = TestModelFactory.buildBeneficiary(memberStatus = MemberStatus.PENDING)
        val beneficiaryOP =
            TestModelFactory.buildBeneficiaryOnboardingPhase(phase = BeneficiaryOnboardingPhaseType.WAITING_FOR_REVIEW)
        val beneficiaryOnboarding = TestModelFactory.buildBeneficiaryOnboarding(
            beneficiaryId = beneficiary.id,
            phases = listOf(beneficiaryOP),
        )

        val body = BeneficiaryIdsRequest(beneficiaryIds = listOf(beneficiary.id))

        coEvery { beneficiaryOnboardingService.findByBeneficiaryIds(listOf(beneficiary.id)) } returns listOf(
            beneficiaryOnboarding
        )
        coEvery {
            beneficiaryService.findByIds(
                listOf(
                    beneficiaryOnboarding.beneficiaryId,
                )
            )
        } returns listOf(beneficiary)

        coEvery { beneficiaryOnboardingService.moveToNextPhase(beneficiary.id) } returns beneficiaryOP

        authenticatedAs(idToken, staff) {
            post("/business/beneficiaries/move_to_next_phase", body = body) { response ->
                ResponseAssert.assertThat(response)
                    .isBadRequestWithErrorCode("invalid_beneficiary_onboarding_move_phase")
            }
        }
    }

    @Test
    fun `#activateMembers - try to activate inapt beneficiary`() {
        val beneficiary = TestModelFactory.buildBeneficiary()
        val otherBeneficiary = TestModelFactory.buildBeneficiary()
        val body = BeneficiaryIdsRequest(listOf(beneficiary.id, otherBeneficiary.id))

        val currentPhase =
            TestModelFactory.buildBeneficiaryOnboardingPhase(phase = BeneficiaryOnboardingPhaseType.READY_TO_ONBOARD)
        val onboarding = TestModelFactory.buildBeneficiaryOnboarding(phases = listOf(currentPhase))
        val beneficiaryWithOnboarding = beneficiary.copy(onboarding = onboarding)

        val otherMember = TestModelFactory.buildMember()
        val otherCcurrentPhase =
            TestModelFactory.buildBeneficiaryOnboardingPhase(phase = BeneficiaryOnboardingPhaseType.REGISTRATION)
        val otherOnboarding = TestModelFactory.buildBeneficiaryOnboarding(phases = listOf(otherCcurrentPhase))
        val otherBneficiaryWithOnboarding = otherBeneficiary.copy(onboarding = otherOnboarding)

        coEvery {
            beneficiaryService.get(beneficiary.id, FindOptions(withOnboarding = true, withDependents = false))
        } returns beneficiaryWithOnboarding
        coEvery {
            beneficiaryService.get(otherBeneficiary.id, FindOptions(withOnboarding = true, withDependents = false))
        } returns otherBneficiaryWithOnboarding
        coEvery { memberService.findPendingMembership(otherBeneficiary.personId) } returns otherMember
        coEvery { memberService.activateMember(otherMember) } returns otherMember

        authenticatedAs(idToken, staff) {
            put("/business/beneficiaries/activate_membership", body) { response ->
                ResponseAssert.assertThat(response).isBadRequest()
            }
        }
        coVerify(exactly = 2) { beneficiaryService.get(any(), any()) }
        coVerifyOnce { memberService.findPendingMembership(any()) }
        coVerifyOnce { memberService.activateMember(any()) }
    }

    @Test
    fun `#activateMembers - try to activate not found member`() {
        val beneficiary = TestModelFactory.buildBeneficiary()
        val otherBeneficiary = TestModelFactory.buildBeneficiary()
        val otherMember = TestModelFactory.buildMember()
        val body = BeneficiaryIdsRequest(listOf(beneficiary.id, otherBeneficiary.id))

        val currentPhase =
            TestModelFactory.buildBeneficiaryOnboardingPhase(phase = BeneficiaryOnboardingPhaseType.REGISTRATION)
        val onboarding = TestModelFactory.buildBeneficiaryOnboarding(phases = listOf(currentPhase))
        val beneficiaryWithOnboarding = beneficiary.copy(onboarding = onboarding)
        val otherBneficiaryWithOnboarding = otherBeneficiary.copy(onboarding = onboarding)

        coEvery {
            beneficiaryService.get(beneficiary.id, FindOptions(withOnboarding = true, withDependents = false))
        } returns beneficiaryWithOnboarding
        coEvery {
            beneficiaryService.get(otherBeneficiary.id, FindOptions(withOnboarding = true, withDependents = false))
        } returns otherBneficiaryWithOnboarding
        coEvery { memberService.findPendingMembership(beneficiary.personId) } returns NotFoundException()
        coEvery { memberService.findPendingMembership(otherBeneficiary.personId) } returns otherMember
        coEvery { memberService.activateMember(otherMember) } returns otherMember

        authenticatedAs(idToken, staff) {
            put("/business/beneficiaries/activate_membership", body) { response ->
                ResponseAssert.assertThat(response).isNotFound()
            }
        }

        coVerify(exactly = 2) { beneficiaryService.get(any(), any()) }
        coVerify(exactly = 2) { memberService.findPendingMembership(any()) }
        coVerifyOnce { memberService.activateMember(any()) }
    }

    @Test
    fun `activateMembers - activate members successfully`() {
        val beneficiary = TestModelFactory.buildBeneficiary()
        val otherBeneficiary = TestModelFactory.buildBeneficiary()
        val member = TestModelFactory.buildMember()
        val otherMember = TestModelFactory.buildMember()
        val body = BeneficiaryIdsRequest(listOf(beneficiary.id, otherBeneficiary.id))

        val currentPhase =
            TestModelFactory.buildBeneficiaryOnboardingPhase(phase = BeneficiaryOnboardingPhaseType.REGISTRATION)
        val onboarding = TestModelFactory.buildBeneficiaryOnboarding(phases = listOf(currentPhase))
        val beneficiaryWithOnboarding = beneficiary.copy(onboarding = onboarding)
        val otherBneficiaryWithOnboarding = otherBeneficiary.copy(onboarding = onboarding)

        coEvery {
            beneficiaryService.get(beneficiary.id, FindOptions(withOnboarding = true, withDependents = false))
        } returns beneficiaryWithOnboarding
        coEvery {
            beneficiaryService.get(otherBeneficiary.id, FindOptions(withOnboarding = true, withDependents = false))
        } returns otherBneficiaryWithOnboarding
        coEvery { memberService.findPendingMembership(beneficiary.personId) } returns member
        coEvery { memberService.findPendingMembership(otherBeneficiary.personId) } returns otherMember
        coEvery { memberService.activateMember(member) } returns member
        coEvery { memberService.activateMember(otherMember) } returns otherMember

        authenticatedAs(idToken, staff) {
            put("/business/beneficiaries/activate_membership", body = body) { response ->
                ResponseAssert.assertThat(response).isSuccessfulJson()
            }
        }

        coVerify(exactly = 2) { beneficiaryService.get(any(), any()) }
        coVerify(exactly = 2) { memberService.findPendingMembership(any()) }
        coVerify(exactly = 2) { memberService.activateMember(any()) }
    }

    @Test
    fun `activateMembers - should return expected error when onboarding phase is not valid`() {
        val beneficiary = TestModelFactory.buildBeneficiary()
        val otherBeneficiary = TestModelFactory.buildBeneficiary()
        val body = BeneficiaryIdsRequest(listOf(beneficiary.id, otherBeneficiary.id))

        val currentPhase =
            TestModelFactory.buildBeneficiaryOnboardingPhase(phase = BeneficiaryOnboardingPhaseType.HEALTH_DECLARATION)
        val onboarding = TestModelFactory.buildBeneficiaryOnboarding(phases = listOf(currentPhase))
        val beneficiaryWithOnboarding = beneficiary.copy(onboarding = onboarding)
        val otherBneficiaryWithOnboarding = otherBeneficiary.copy(onboarding = onboarding)

        coEvery {
            beneficiaryService.get(beneficiary.id, FindOptions(withOnboarding = true, withDependents = false))
        } returns beneficiaryWithOnboarding
        coEvery {
            beneficiaryService.get(otherBeneficiary.id, FindOptions(withOnboarding = true, withDependents = false))
        } returns otherBneficiaryWithOnboarding

        authenticatedAs(idToken, staff) {
            put("/business/beneficiaries/activate_membership", body = body) { response ->
                ResponseAssert.assertThat(response).isBadRequestWithErrorCode("beneficiary_must_be_apt_to_be_activated")
            }
        }

        coVerify(exactly = 2) { beneficiaryService.get(any(), any()) }
        coVerifyNone { memberService.findPendingMembership(any()) }
        coVerifyNone { memberService.activateMember(any()) }
    }

    @Test
    fun `activateMembers - throw exception and log when activation is expected to a near future`() {
        val futureBeneficiary = TestModelFactory.buildBeneficiary(
            activatedAt = LocalDateTime.now().plusDays(1),
        )
        val body = BeneficiaryIdsRequest(listOf(futureBeneficiary.id))
        val currentPhase =
            TestModelFactory.buildBeneficiaryOnboardingPhase(phase = BeneficiaryOnboardingPhaseType.REGISTRATION)
        val onboarding = TestModelFactory.buildBeneficiaryOnboarding(phases = listOf(currentPhase))
        val beneficiaryWithOnboarding = futureBeneficiary.copy(onboarding = onboarding)

        coEvery {
            beneficiaryService.get(futureBeneficiary.id, FindOptions(withOnboarding = true, withDependents = false))
        } returns beneficiaryWithOnboarding

        authenticatedAs(idToken, staff) {
            put("/business/beneficiaries/activate_membership", body = body) { response ->
                ResponseAssert.assertThat(response).isBadRequestWithErrorCode("beneficiary_must_be_apt_to_be_activated")
            }
        }
        coVerifyNone { memberService.findPendingMembership(any()) }
        coVerifyNone { memberService.activateMember(any()) }
    }

    @Test
    fun `activateMembers - activate member shoud failt for member who is being activate before company contract start`() {
        val company = TestModelFactory.buildCompany(contractStartedAt = LocalDateTime.of(2020, 1, 1, 0, 0, 0))
        val beneficiary = TestModelFactory.buildBeneficiary(companyId = company.id)
        val member = TestModelFactory.buildMember()
        val body = BeneficiaryIdsRequest(listOf(beneficiary.id))

        val currentPhase =
            TestModelFactory.buildBeneficiaryOnboardingPhase(phase = BeneficiaryOnboardingPhaseType.REGISTRATION)
        val onboarding = TestModelFactory.buildBeneficiaryOnboarding(phases = listOf(currentPhase))
        val beneficiaryWithOnboarding = beneficiary.copy(onboarding = onboarding)

        coEvery {
            beneficiaryService.get(beneficiary.id, FindOptions(withOnboarding = true, withDependents = false))
        } returns beneficiaryWithOnboarding
        coEvery { memberService.findPendingMembership(beneficiary.personId) } returns member
        coEvery { memberService.activateMember(member) } returns MemberActivationDateIsEarlierThanContractStartedDateException(
            "Error"
        ).failure()

        authenticatedAs(idToken, staff) {
            put("/business/beneficiaries/activate_membership", body = body) { response ->
                ResponseAssert.assertThat(response).isBadRequest()
            }
        }

        coVerify(exactly = 1) { beneficiaryService.get(any(), any()) }
        coVerify(exactly = 1) { memberService.findPendingMembership(any()) }
        coVerify(exactly = 1) { memberService.activateMember(any()) }
    }

    @Test
    fun `#reactivateMembers - reactivateMember successfully`() {
        val beneficiary = TestModelFactory.buildBeneficiary()
        val requestBody = BeneficiaryIdsRequest(listOf(beneficiary.id))
        val newBeneficiary = beneficiary.copy(
            canceledAt = null, canceledDescription = null,
            canceledReason = null, hasContributed = null
        )

        coEvery { beneficiaryService.reactivateMembership(beneficiary.id) } returns newBeneficiary
        authenticatedAs(idToken, staff) {
            put("/business/beneficiaries/reactivate_membership", body = requestBody) { response ->
                ResponseAssert.assertThat(response).isOKWithData(listOf(newBeneficiary))
            }
        }

        coVerifyOnce { beneficiaryService.reactivateMembership(beneficiary.id) }
    }

    @Test
    fun `#cancelMembersActivation should call service as expected`() {
        val beneficiary = TestModelFactory.buildBeneficiary()
        val requestBody = CancelMemberActivationRequest(listOf(beneficiary.id))

        coEvery { beneficiaryService.cancelActivationById(beneficiary.id) } returns beneficiary

        authenticatedAs(idToken, staff) {
            put("/business/beneficiaries/cancel_membership_activation", body = requestBody) { response ->
                ResponseAssert.assertThat(response).isOKWithData(listOf(beneficiary))
            }
        }

        coVerifyOnce { beneficiaryService.cancelActivationById(beneficiary.id) }
    }

    @Test
    fun `#createOnboarding - Create onboarding info with undefined flow and first step`() {
        val beneficiaryOnboarding = TestModelFactory.buildBeneficiaryOnboarding()
        val personBillingAccountableParty = TestModelFactory.buildPersonBillingAccountableParty()
        val person = TestModelFactory.buildPerson()
        val member = TestModelFactory.buildMember(personId = person.id)
        val beneficiary = TestModelFactory.buildBeneficiary(personId = person.id, memberId = member.id)
        val cassiMember = TestModelFactory.buildCassiMember(memberId = member.id)
        val company = TestModelFactory.buildCompany()
        val beneficiaryHubspot = TestModelFactory.buildBeneficiaryHubspot(beneficiaryId = beneficiary.id)

        coEvery {
            beneficiaryOnboardingService.addWithPhase(
                any(),
                any(),
                any(),
                any(),
                any()
            )
        } returns beneficiaryOnboarding
        coEvery { companyService.get(any()) } returns company
        coEvery { beneficiaryHubspotService.findByBeneficiaryIds(any()) } returns listOf(beneficiaryHubspot)
        coEvery { beneficiaryService.get(any(), any()) } returns beneficiary
        coEvery { billingAccountablePartyService.getCurrentAssign(any()) } returns personBillingAccountableParty
        coEvery { personService.findByIds(any(), any(), any()) } returns listOf(person)
        coEvery { memberService.findByIds(any()) } returns listOf(member)
        coEvery { cassiMemberService.findByMemberIds(any()) } returns listOf(cassiMember)

        authenticatedAs(idToken, staff) {
            post("/business/beneficiaries/${beneficiary.id}/onboarding") { response ->
                ResponseAssert.assertThat(response).isSuccessfulJson()
            }
        }

        coVerifyOnce {
            beneficiaryOnboardingService.addWithPhase(
                beneficiaryId = beneficiary.id,
                flowType = BeneficiaryOnboardingFlowType.UNDEFINED,
                initialProductId = company.defaultProductId!!,
                phaseType = BeneficiaryOnboardingPhaseType.READY_TO_ONBOARD,
                transactedAt = any()
            )
        }
    }
}
