package br.com.alice.channel.core.consumers

import br.com.alice.channel.client.ChannelFupService
import br.com.alice.channel.core.context.FirestoreContextUsage
import br.com.alice.channel.core.services.ChannelFollowUp
import br.com.alice.channel.core.services.internal.FollowUpHistoryService
import br.com.alice.channel.core.services.internal.firestore.ChannelFirestoreService
import br.com.alice.channel.event.AutomaticFollowUpSentEvent
import br.com.alice.channel.event.PersonHealthEventUpdateRequestEvent
import br.com.alice.channel.models.ChannelDocument
import br.com.alice.channel.models.Origin
import br.com.alice.channel.models.inProgressTag
import br.com.alice.common.core.PersonId
import br.com.alice.common.core.extensions.toPersonId
import br.com.alice.common.kafka.interfaces.KafkaProducerService
import br.com.alice.communication.firebase.FirebasePush
import br.com.alice.communication.firebase.PushService
import br.com.alice.data.layer.models.AutomaticFollowUpContent
import br.com.alice.data.layer.models.ChannelFollowUpOptions
import br.com.alice.data.layer.models.Device
import br.com.alice.data.layer.models.FeatureNamespace
import br.com.alice.data.layer.models.FollowUpHistory
import br.com.alice.data.layer.models.Person
import br.com.alice.data.layer.models.PersonHealthEvent
import br.com.alice.data.layer.models.PersonHealthEventStatus
import br.com.alice.featureconfig.core.FeatureService
import br.com.alice.membership.client.DeviceService
import br.com.alice.person.client.PersonService
import br.com.alice.wanda.event.AutomaticFollowUpEvent
import com.github.kittinunf.result.flatMap
import com.github.kittinunf.result.flatMapError
import com.github.kittinunf.result.map
import com.github.kittinunf.result.success

class AutomaticFollowUpConsumer(
    private val personService: PersonService,
    private val channelFirestoreService: ChannelFirestoreService,
    private val deviceService: DeviceService,
    private val pushService: PushService,
    private val kafkaProducerService: KafkaProducerService,
    private val channelFupService: ChannelFupService,
    private val followUpHistoryService: FollowUpHistoryService
) : Consumer() {

    companion object {
        private const val defaultFupMessage = "<h4>Como você está se sentindo em relação à sua última queixa?</h4>"
    }

    suspend fun sendFupMessage(event: AutomaticFollowUpEvent) =
        withSubscribersEnvironment(event.payload.personHealthEvent.personId.toString()) {
            val personHealthEvent = event.payload.personHealthEvent

            val channelLink = personHealthEvent.referencedLinks.firstOrNull { it.model.lowercase() == "channel" } ?: run {
                publishPersonHealthEventStatus(personHealthEvent.copy(status = PersonHealthEventStatus.CANCELLED))
                return@withSubscribersEnvironment true.success()
            }

            channelFirestoreService.getChannel(channelLink.id).flatMap { channel ->
                require(channel.status.isActive()) { "Channel not active" }

                personService.get(personHealthEvent.personId, withUserType = false).map { person ->
                    val questionFupMessage = getFupQuestionMessage(personHealthEvent, person.contactName)

                    setAutoFupAttribute(
                        personHealthEvent,
                        channel.id!!,
                        questionFupMessage,
                        channel,
                        person.contactName
                    )

                    sendFupPushMessage(channel, person)

                    if (personHealthEvent.version >= 0) {
                        publishPersonHealthEventStatus(personHealthEvent.copy(status = PersonHealthEventStatus.FINISHED))
                        publishAutomaticFollowUpSentEvent(channel.id!!, questionFupMessage, personHealthEvent.personId)
                    }

                    channel.id!!
                }
            }.flatMapError {
                publishPersonHealthEventStatus(personHealthEvent.copy(status = PersonHealthEventStatus.CANCELLED))
                true.success()
            }
        }

    private fun ChannelDocument.isValidToSendFup() =
        this.id != null
                && this.staffIds?.isNotEmpty() == true
                && !this.tags.contains(inProgressTag)
                && this.lastPreviewableMessage != null
                && this.lastPreviewableMessage?.userId != this.channelPersonId

    private fun getFupQuestionMessage(personHealthEvent: PersonHealthEvent, contactName: String): String =
        personHealthEvent.automaticFollowUpContent?.message ?: run {
            personHealthEvent.automaticFollowUpMessage?.let { message ->
                message.trim().ifBlank { "Oi, $contactName,$defaultFupMessage" }
            } ?: "Oi, $contactName,$defaultFupMessage"
        }

    private suspend fun buildAutoFupAttribute(
        personHealthEvent: PersonHealthEvent,
        questionFupMessage: String,
        personContactName: String
    ): ChannelFollowUp =
        personHealthEvent.automaticFollowUpContent?.let { content ->
            if (content.channelFupId != null) {
                getFupById(content.channelFupId.toString(), personHealthEvent, questionFupMessage, personContactName)
            } else {
                getFupByType(content, personHealthEvent, questionFupMessage, personContactName)
            }
        } ?: run {
            buildDefaultFupAttribute(personHealthEvent, questionFupMessage)
        }

    private suspend fun getFupById(
        channelFupId: String,
        personHealthEvent: PersonHealthEvent,
        questionFupMessage: String,
        personContactName: String
    ) =
        channelFupService.get(channelFupId).map { channelFup ->
            val formattedAnswers = channelFup.answers.map {
                ChannelFollowUpOptions(
                    icon = it.icon.orEmpty(),
                    value = it.key,
                    label = it.label,
                    nextChannelFup = it.nextChannelFup.orEmpty(),
                    starred = it.starred ?: false
                )
            }

            ChannelFollowUp(
                id = personHealthEvent.id.toString(),
                title = questionFupMessage,
                options = getAnswersIdentified(formattedAnswers, personContactName)
            )
        }.flatMapError {
            buildDefaultFupAttribute(personHealthEvent, questionFupMessage).success()
        }.get()


    private suspend fun getFupByType(
        content: AutomaticFollowUpContent,
        personHealthEvent: PersonHealthEvent,
        questionFupMessage: String,
        personContactName: String
    ) =
        content.type?.let {
            channelFupService.getByType(it).fold(
                { channelFollowUp ->
                    ChannelFollowUp(
                        id = personHealthEvent.id.toString(),
                        title = questionFupMessage,
                        options = getAnswersIdentified(channelFollowUp.answers, personContactName)
                    )
                },
                {
                    buildDefaultFupAttribute(personHealthEvent, questionFupMessage)
                }
            )
        } ?: run {
            buildDefaultFupAttribute(personHealthEvent, questionFupMessage)
        }

    private fun getAnswersIdentified(
        answersDeIdentified: List<ChannelFollowUpOptions>,
        personContactName: String
    ) = answersDeIdentified.map {
        it.copy(label = it.label.replace("@nickname", personContactName))
    }

    private fun buildDefaultFupAttribute(
        personHealthEvent: PersonHealthEvent,
        questionFupMessage: String
    ): ChannelFollowUp {
        val fupIFeelBetterIcon = FeatureService.get(
            namespace = FeatureNamespace.CHANNELS,
            key = "fup_i_fell_better_icon",
            defaultValue = ""
        )
        val fupIDidNotSeeImprovementIcon = FeatureService.get(
            namespace = FeatureNamespace.CHANNELS,
            key = "fup_i_didnt_see_improvement_icon",
            defaultValue = ""
        )
        val fupIFeelWorseIcon = FeatureService.get(
            namespace = FeatureNamespace.CHANNELS,
            key = "fup_i_feel_worse_icon",
            defaultValue = ""
        )

        return ChannelFollowUp(
            id = personHealthEvent.id.toString(),
            title = questionFupMessage,
            options = listOf(
                ChannelFollowUpOptions(
                    icon = fupIFeelBetterIcon,
                    label = "Me sinto melhor",
                    value = "fup_i_fell_better"
                ),
                ChannelFollowUpOptions(
                    icon = fupIDidNotSeeImprovementIcon,
                    label = "Não percebi melhora",
                    value = "fup_i_didnt_see_improvement"
                ),
                ChannelFollowUpOptions(
                    icon = fupIFeelWorseIcon,
                    label = "Me sinto pior",
                    value = "fup_i_feel_worse"
                )
            )
        )
    }

    private suspend fun sendFupPushMessage(channelDocument: ChannelDocument, person: Person) =
        channelDocument.id?.let { channelId ->
            val channelCategory = if (channelDocument.isChat())
                Origin.getByValue(channelDocument.origin)
            else
                Origin.CHANNELS.description

            val firebasePushData = mapOf(
                "path_to_navigate" to "channel",
                "parameter" to channelId,
                "properties" to "{\"category\": \"$channelCategory\"}"
            )

            val title = channelDocument.name ?: "Mensagem do seu Time"

            val fupPushMessage = FeatureService.get(
                namespace = FeatureNamespace.CHANNELS,
                key = "fup_push_message",
                defaultValue = "@personName, seu Time de Saúde quer saber como você está se sentindo. Conta pra gente?"
            ).replace("@personName", person.contactName)

            deviceService.getDeviceByPerson(person.id.toString())
                .map {
                    sendPush(device = it, title = title, message = fupPushMessage, data = firebasePushData)
                }
        }

    private fun sendPush(device: Device, title: String, message: String, data: Map<String, String>? = null) =
        pushService.send(
            FirebasePush(
                deviceToken = device.deviceId,
                title = title,
                body = message,
                data = data
            )
        )

    @OptIn(FirestoreContextUsage::class)
    private suspend fun setAutoFupAttribute(
        personHealthEvent: PersonHealthEvent,
        channelId: String,
        questionFupMessage: String,
        channelDocument: ChannelDocument,
        personContactName: String
    ) {
        val autoFupAttribute = buildAutoFupAttribute(personHealthEvent, questionFupMessage, personContactName)

        channelFirestoreService.updateFields(
            channelId,
            mapOf(ChannelDocument::followUp to autoFupAttribute)
        ).get()

        followUpHistoryService.create(
            FollowUpHistory(
                personId = channelDocument.personId.toPersonId(),
                channelId = channelId,
                wandaTaskId = personHealthEvent.id,
                staffId = personHealthEvent.staffId!!,
                followUpId = personHealthEvent.automaticFollowUpContent?.channelFupId,
                question = questionFupMessage,
                options = autoFupAttribute.options,
            )
        )
    }

    private suspend fun publishPersonHealthEventStatus(healthEvent: PersonHealthEvent) =
        kafkaProducerService.produce(PersonHealthEventUpdateRequestEvent(healthEvent))

    private suspend fun publishAutomaticFollowUpSentEvent(
        channelId: String,
        questionFupMessage: String,
        personId: PersonId
    ) {
        kafkaProducerService.produce(AutomaticFollowUpSentEvent(channelId, questionFupMessage, personId))
    }
}
