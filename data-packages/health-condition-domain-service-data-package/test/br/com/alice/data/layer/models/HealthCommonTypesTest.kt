package br.com.alice.data.layer.models

import br.com.alice.common.Disease
import org.assertj.core.api.Assertions.assertThat
import kotlin.test.Test

class HealthCommonTypesTest {

    @Test
    fun `getDescription should return formatted string with type, value and description`() {
        val diseaseDetails = DiseaseDetails(
            id = "123",
            type = Disease.Type.CID_10,
            value = "A10",
            description = "Some disease"
        )

        val result = diseaseDetails.getDiseaseDescription()

        assertThat(result).isEqualTo("CID A10 - Some disease")
    }

    @Test
    fun `getDescription should handle null description`() {
        val diseaseDetails = DiseaseDetails(
            id = "123",
            type = Disease.Type.CIAP_2,
            value = "R21",
            description = null
        )

        val result = diseaseDetails.getDiseaseDescription()

        assertThat(result).isEqualTo("CIAP R21 - null")
    }
}
