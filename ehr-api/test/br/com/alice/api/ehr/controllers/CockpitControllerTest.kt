package br.com.alice.api.ehr.controllers

import br.com.alice.api.ehr.ControllerTestHelper
import br.com.alice.api.ehr.controllers.model.CockpitSummaryResponse
import br.com.alice.api.ehr.controllers.model.CockpitSummaryType
import br.com.alice.api.ehr.controllers.model.PoolFilter
import br.com.alice.api.ehr.controllers.model.PoolFilterType
import br.com.alice.api.ehr.controllers.model.SummaryByCategory
import br.com.alice.common.core.Role.DIGITAL_CARE_NURSE
import br.com.alice.common.core.Role.DIGITAL_CARE_PHYSICIAN
import br.com.alice.common.core.Role.HEALTHCARE_TEAM_NURSE
import br.com.alice.common.core.Role.HEALTH_DECLARATION_NURSE
import br.com.alice.common.core.Role.MANAGER_PHYSICIAN
import br.com.alice.common.core.Role.PRODUCT_TECH
import br.com.alice.common.data.dsl.matchers.ResponseAssert.Companion.assertThat
import br.com.alice.common.featureflag.withFeatureFlag
import br.com.alice.common.helpers.bodyAsJson
import br.com.alice.data.layer.helpers.TestModelFactory.buildHealthcareAdditionalTeam
import br.com.alice.data.layer.models.FeatureNamespace
import br.com.alice.data.layer.models.PersonHealthEventCategory.AA_HEALTH_FOLLOW_UP
import br.com.alice.data.layer.models.PersonHealthEventCategory.AA_IMMERSION_HEALTH_FOLLOW_UP
import br.com.alice.data.layer.models.PersonHealthEventCategory.APPOINTMENT_COMMUNITY
import br.com.alice.data.layer.models.PersonHealthEventCategory.APPOINTMENT_SCHEDULE_FOLLOW_UP
import br.com.alice.data.layer.models.PersonHealthEventCategory.APPOINTMENT_SCHEDULE_IMMERSION
import br.com.alice.data.layer.models.PersonHealthEventCategory.HEALTH_PLAN_TEST_RESULT
import br.com.alice.data.layer.models.PersonHealthEventCategory.SUMMARY_EMERGENCY
import br.com.alice.data.layer.models.PersonHealthEventCategory.SUMMARY_HOSPITALIZATION
import br.com.alice.staff.client.HealthcareAdditionalTeamService
import br.com.alice.staff.client.StaffService
import br.com.alice.wanda.client.PersonHealthEventService
import br.com.alice.wanda.model.HealthEventSummary
import com.github.kittinunf.result.success
import io.mockk.clearMocks
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.confirmVerified
import io.mockk.mockk
import io.mockk.mockkStatic
import kotlinx.coroutines.runBlocking
import org.assertj.core.api.Assertions.assertThat
import java.time.LocalDateTime
import java.time.LocalTime
import kotlin.test.BeforeTest
import kotlin.test.Test

class CockpitControllerTest : ControllerTestHelper() {

    private val personHealthEventService: PersonHealthEventService = mockk()
    private val staffService: StaffService = mockk()
    private val healthcareAdditionalTeamService: HealthcareAdditionalTeamService = mockk()
    private val cockpitController = CockpitController(
        personHealthEventService,
        staffService,
        healthcareAdditionalTeamService
    )
    private val healthEventSummary = HealthEventSummary(total = 2, due = 1, auto = 0)
    private val additionalTeam = buildHealthcareAdditionalTeam(
        staffIds = listOf(staff.id)
    )
    private val dueDate = LocalDateTime.of(2021, 1, 11, 11, 11)

    private val expectedPoolFilters = listOf(
        PoolFilter(
            title = "Mostrar todas",
            query = "filter={\"health_care_additional_team_id\"" +
                ":[\"${additionalTeam.id}\"]," +
                "\"status\":[\"IN_PROGRESS\",\"NOT_STARTED\"]," +
                "\"due_date\":{" +
                "\"second\":\"${dueDate.with(LocalTime.MAX)}\"}" +
                "}",
            type = PoolFilterType.ALL
        ),
        PoolFilter(
            title = "Somente minhas tarefas",
            query = "filter={\"health_care_additional_team_id\"" +
                ":[\"${additionalTeam.id}\"]," +
                "\"staff_id\":[\"${staff.id}\"]," +
                "\"status\":[\"IN_PROGRESS\",\"NOT_STARTED\"]," +
                "\"due_date\":{" +
                "\"second\":\"${dueDate.with(LocalTime.MAX)}\"}" +
                "}",
            type = PoolFilterType.MINE
        )
    )

//    private val cockpitFeatureConfig = FeatureConfig(
//        key = "show_pool_cockpit",
//        namespace = FeatureNamespace.WANDA,
//        type = FeatureType.BOOLEAN,
//        value = "true",
//        active = true,
//        description = ""
//    )

    @BeforeTest
    override fun setup() {
        super.setup()
        mockkStatic(LocalDateTime::class)

        clearMocks(personHealthEventService, staffService)
        module.single { cockpitController }

        coEvery {
            healthcareAdditionalTeamService.getByStaffId(staff.id)
        } returns listOf(additionalTeam).success()

        coEvery { LocalDateTime.now() } returns dueDate

        coEvery {
            personHealthEventService.getSummaryByPool(
                dueDateTime = dueDate,
                healthcareAdditionalTeamIds = listOf(additionalTeam.id)
            )
        } returns healthEventSummary.success()

//        FeatureConfigCache.put(cockpitFeatureConfig)
    }

//    @AfterTest
//    fun clean() {
//        FeatureConfigCache.clear()
//    }

    @Test
    fun `#getSummary should return empty list if not health professional`() {
        val nutritionist = staff.copy(role = PRODUCT_TECH)

        authenticatedWithRolesAs(idToken, toTest(nutritionist)) {
            get("/ehr/staff/cockpit") { response ->
                assertThat(response).isSuccessfulJson()
                val body: CockpitSummaryResponse = response.bodyAsJson()
                assertThat(body.summaries).isEqualTo(emptyList<SummaryByCategory>())
            }
            confirmVerified(healthcareAdditionalTeamService, personHealthEventService)
        }
    }

    @Test
    fun `#getSummary should return list of summaries if physician`() = runBlocking {
        val expected = CockpitSummaryResponse(
            pool = expectedPoolFilters,
            summaries = listOf(
                SummaryByCategory(
                    title = "Demandas do pool",
                    query = "filter={\"health_care_additional_team_id\"" +
                        ":[\"${additionalTeam.id}\"]," +
                        "\"status\":[\"IN_PROGRESS\",\"NOT_STARTED\"]," +
                        "\"due_date\":{\"second\":\"${dueDate.with(LocalTime.MAX)}\"}}",
                    summary = healthEventSummary,
                    type = CockpitSummaryType.POOL
                ),
                SummaryByCategory(
                    title = "Imersão",
                    query = "filter={\"category\":[\"APPOINTMENT_SCHEDULE_IMMERSION\"]," +
                        "\"staff_id\":[\"${staff.id}\"]," +
                        "\"status\":[\"IN_PROGRESS\",\"NOT_STARTED\"]," +
                        "\"due_date\":{\"second\":\"${dueDate.with(LocalTime.MAX)}\"}}",
                    summary = healthEventSummary
                ),
                SummaryByCategory(
                    title = "Retorno",
                    query = "filter={\"category\":[\"APPOINTMENT_SCHEDULE_FOLLOW_UP\"]," +
                        "\"staff_id\":[\"${staff.id}\"]," +
                        "\"status\":[\"IN_PROGRESS\",\"NOT_STARTED\"]," +
                        "\"due_date\":{\"second\":\"${dueDate.with(LocalTime.MAX)}\"}}",
                    summary = healthEventSummary
                ),
                SummaryByCategory(
                    title = "Exames",
                    query = "filter={\"category\":[\"HEALTH_PLAN_TEST_RESULT\"]," +
                        "\"staff_id\":[\"${staff.id}\"]," +
                        "\"status\":[\"IN_PROGRESS\",\"NOT_STARTED\"]," +
                        "\"due_date\":{\"second\":\"${dueDate.with(LocalTime.MAX)}\"}}",
                    summary = healthEventSummary
                ),
                SummaryByCategory(
                    title = "Contrarreferência",
                    query = "filter={\"category\":[\"APPOINTMENT_COMMUNITY\"]," +
                        "\"staff_id\":[\"${staff.id}\"]," +
                        "\"status\":[\"IN_PROGRESS\",\"NOT_STARTED\"]," +
                        "\"due_date\":{\"second\":\"${dueDate.with(LocalTime.MAX)}\"}}",
                    summary = healthEventSummary
                ),
                SummaryByCategory(
                    title = "Todas Atividades",
                    query = "filter={\"staff_id\":[\"${staff.id}\"]," +
                        "\"status\":[\"IN_PROGRESS\",\"NOT_STARTED\"]," +
                        "\"due_date\":{\"second\":\"${dueDate.with(LocalTime.MAX)}\"}}",
                    summary = healthEventSummary
                )
            )
        )

        coEvery {
            personHealthEventService.getSummaryByCategories(
                staffId = staff.id,
                dueDateTime = dueDate,
                categories = any()
            )
        } returns healthEventSummary.success()

        withFeatureFlag(FeatureNamespace.WANDA, key = "show_pool_cockpit", true) {
            authenticatedWithRolesAs(
                idToken,
                staffTest,
                roles = listOf(
                    MANAGER_PHYSICIAN.name,
                    DIGITAL_CARE_PHYSICIAN.name
                )
            ) {
                get("/ehr/staff/cockpit") { response ->
                    assertThat(response).isOKWithData(expected)
                }
            }
        }

        coVerify(exactly = 1) {
            personHealthEventService.getSummaryByCategories(
                staffId = staff.id,
                dueDateTime = dueDate,
                categories = listOf(
                    APPOINTMENT_SCHEDULE_IMMERSION
                )
            )
        }
        coVerify(exactly = 1) {
            personHealthEventService.getSummaryByCategories(
                staffId = staff.id,
                dueDateTime = dueDate,
                categories = listOf(
                    APPOINTMENT_SCHEDULE_FOLLOW_UP
                )
            )
        }
        coVerify(exactly = 1) {
            personHealthEventService.getSummaryByCategories(
                staffId = staff.id,
                dueDateTime = dueDate,
                categories = listOf(
                    HEALTH_PLAN_TEST_RESULT
                )
            )
        }
        coVerify(exactly = 1) {
            personHealthEventService.getSummaryByCategories(
                staffId = staff.id,
                dueDateTime = dueDate,
                categories = listOf(
                    APPOINTMENT_COMMUNITY
                )
            )
        }
        coVerify(exactly = 1) {
            personHealthEventService.getSummaryByCategories(
                staffId = staff.id,
                dueDateTime = dueDate,
                categories = emptyList()
            )
        }
        coVerify(exactly = 5) {
            personHealthEventService.getSummaryByCategories(
                any(),
                any(),
                any()
            )
        }
        coVerify(exactly = 1) {
            personHealthEventService.getSummaryByPool(
                any(),
                any()
            )
        }
        coVerify(exactly = 1) {
            healthcareAdditionalTeamService.getByStaffId(
                any()
            )
        }
        confirmVerified(
            healthcareAdditionalTeamService,
            personHealthEventService
        )
    }

    @Test
    fun `#getSummary should return list of summaries if digital care nurse`() = runBlocking {
        val expected = CockpitSummaryResponse(
            pool = expectedPoolFilters,
            summaries = listOf(
                SummaryByCategory(
                    title = "Demandas do pool",
                    type = CockpitSummaryType.POOL,
                    query = "filter={\"health_care_additional_team_id\"" +
                        ":[\"${additionalTeam.id}\"]," +
                        "\"status\":[\"IN_PROGRESS\",\"NOT_STARTED\"]," +
                        "\"due_date\":{\"second\":\"${dueDate.with(LocalTime.MAX)}\"}}",
                    summary = healthEventSummary
                ),
                SummaryByCategory(
                    title = "Sumários: Internação",
                    query = "filter={\"category\":[\"SUMMARY_HOSPITALIZATION\"]," +
                        "\"staff_id\":[\"${staff.id}\"]," +
                        "\"status\":[\"IN_PROGRESS\",\"NOT_STARTED\"]," +
                        "\"due_date\":{\"second\":\"${dueDate.with(LocalTime.MAX)}\"}}",
                    summary = healthEventSummary
                ),
                SummaryByCategory(
                    title = "Sumários: PA",
                    query = "filter={\"category\":[\"SUMMARY_EMERGENCY\"]," +
                        "\"staff_id\":[\"${staff.id}\"]," +
                        "\"status\":[\"IN_PROGRESS\",\"NOT_STARTED\"]," +
                        "\"due_date\":{\"second\":\"${dueDate.with(LocalTime.MAX)}\"}}",
                    summary = healthEventSummary
                ),
                SummaryByCategory(
                    title = "Acompanhamentos",
                    query = "filter={\"category\":[\"AA_HEALTH_FOLLOW_UP\"," +
                        "\"AA_IMMERSION_HEALTH_FOLLOW_UP\"]," +
                        "\"staff_id\":[\"${staff.id}\"]," +
                        "\"status\":[\"IN_PROGRESS\",\"NOT_STARTED\"]," +
                        "\"due_date\":{\"second\":\"${dueDate.with(LocalTime.MAX)}\"}}",
                    summary = healthEventSummary
                ),
                SummaryByCategory(
                    title = "Exames",
                    query = "filter={\"category\":[\"HEALTH_PLAN_TEST_RESULT\"]," +
                        "\"staff_id\":[\"${staff.id}\"]," +
                        "\"status\":[\"IN_PROGRESS\",\"NOT_STARTED\"]," +
                        "\"due_date\":{\"second\":\"${dueDate.with(LocalTime.MAX)}\"}}",
                    summary = healthEventSummary
                ),
                SummaryByCategory(
                    title = "Todas Atividades",
                    query = "filter={\"staff_id\":[\"${staff.id}\"]," +
                        "\"status\":[\"IN_PROGRESS\",\"NOT_STARTED\"]," +
                        "\"due_date\":{\"second\":\"${dueDate.with(LocalTime.MAX)}\"}}",
                    summary = healthEventSummary
                )
            )
        )

        coEvery {
            personHealthEventService.getSummaryByCategories(
                staffId = staff.id,
                dueDateTime = dueDate,
                categories = any()
            )
        } returns healthEventSummary.success()

        withFeatureFlag(FeatureNamespace.WANDA, key = "show_pool_cockpit", true) {
            authenticatedWithRolesAs(idToken, staffTest, roles = listOf(DIGITAL_CARE_NURSE.name)) {
                get("/ehr/staff/cockpit") { response ->
                    assertThat(response).isOKWithData(expected)
                }
            }
        }
        coVerify(exactly = 1) {
            personHealthEventService.getSummaryByCategories(
                staffId = staff.id,
                dueDateTime = dueDate,
                categories = listOf(
                    SUMMARY_HOSPITALIZATION
                )
            )
        }
        coVerify(exactly = 1) {
            personHealthEventService.getSummaryByCategories(
                staffId = staff.id,
                dueDateTime = dueDate,
                categories = listOf(
                    SUMMARY_EMERGENCY
                )
            )
        }
        coVerify(exactly = 1) {
            personHealthEventService.getSummaryByCategories(
                staffId = staff.id,
                dueDateTime = dueDate,
                categories = listOf(
                    HEALTH_PLAN_TEST_RESULT
                )
            )
        }
        coVerify(exactly = 1) {
            personHealthEventService.getSummaryByCategories(
                staffId = staff.id,
                dueDateTime = dueDate,
                categories = listOf(
                    AA_HEALTH_FOLLOW_UP,
                    AA_IMMERSION_HEALTH_FOLLOW_UP
                )
            )
        }
        coVerify(exactly = 1) {
            personHealthEventService.getSummaryByCategories(
                staffId = staff.id,
                dueDateTime = dueDate,
                categories = emptyList()
            )
        }
        coVerify(exactly = 5) {
            personHealthEventService.getSummaryByCategories(
                any(),
                any(),
                any()
            )
        }
        coVerify(exactly = 1) {
            personHealthEventService.getSummaryByPool(
                any(),
                any()
            )
        }
        coVerify(exactly = 1) {
            healthcareAdditionalTeamService.getByStaffId(
                any()
            )
        }
        confirmVerified(
            healthcareAdditionalTeamService,
            personHealthEventService
        )
    }

    @Test
    fun `#getSummary should return list of summaries if healthcare team nurse`() = runBlocking {
        val expected = CockpitSummaryResponse(
            pool = expectedPoolFilters,
            summaries = listOf(
                SummaryByCategory(
                    title = "Demandas do pool",
                    query = "filter={\"health_care_additional_team_id\"" +
                        ":[\"${additionalTeam.id}\"]," +
                        "\"status\":[\"IN_PROGRESS\",\"NOT_STARTED\"]," +
                        "\"due_date\":{\"second\":\"${dueDate.with(LocalTime.MAX)}\"}}",
                    summary = healthEventSummary,
                    type = CockpitSummaryType.POOL
                ),
                SummaryByCategory(
                    title = "Acompanhamentos",
                    query = "filter={\"category\":[\"APPOINTMENT_SCHEDULE_FOLLOW_UP\"]," +
                        "\"staff_id\":[\"${staff.id}\"]," +
                        "\"status\":[\"IN_PROGRESS\",\"NOT_STARTED\"]," +
                        "\"due_date\":{\"second\":\"${dueDate.with(LocalTime.MAX)}\"}}",
                    summary = healthEventSummary
                ),
                SummaryByCategory(
                    title = "Exames",
                    query = "filter={\"category\":[\"HEALTH_PLAN_TEST_RESULT\"]," +
                        "\"staff_id\":[\"${staff.id}\"]," +
                        "\"status\":[\"IN_PROGRESS\",\"NOT_STARTED\"]," +
                        "\"due_date\":{\"second\":\"${dueDate.with(LocalTime.MAX)}\"}}",
                    summary = healthEventSummary
                ),
                SummaryByCategory(
                    title = "Contrarreferências",
                    query = "filter={\"category\":[\"APPOINTMENT_COMMUNITY\"]," +
                        "\"staff_id\":[\"${staff.id}\"]," +
                        "\"status\":[\"IN_PROGRESS\",\"NOT_STARTED\"]," +
                        "\"due_date\":{\"second\":\"${dueDate.with(LocalTime.MAX)}\"}}",
                    summary = healthEventSummary
                ),
                SummaryByCategory(
                    title = "Todas Atividades",
                    query = "filter={\"staff_id\":[\"${staff.id}\"]," +
                        "\"status\":[\"IN_PROGRESS\",\"NOT_STARTED\"]," +
                        "\"due_date\":{\"second\":\"${dueDate.with(LocalTime.MAX)}\"}}",
                    summary = healthEventSummary
                )
            )
        )

        coEvery {
            personHealthEventService.getSummaryByCategories(
                staffId = staff.id,
                dueDateTime = dueDate,
                categories = any()
            )
        } returns healthEventSummary.success()

        withFeatureFlag(FeatureNamespace.WANDA, key = "show_pool_cockpit", true) {
            authenticatedWithRolesAs(
                idToken,
                staffTest,
                roles = listOf(
                    HEALTHCARE_TEAM_NURSE.name,
                    PRODUCT_TECH.name
                )
            ) {
                get("/ehr/staff/cockpit") { response ->
                    assertThat(response).isOKWithData(expected)
                }
            }
        }

        coVerify(exactly = 1) {
            personHealthEventService.getSummaryByCategories(
                staffId = staff.id,
                dueDateTime = dueDate,
                categories = listOf(APPOINTMENT_SCHEDULE_FOLLOW_UP)
            )
        }
        coVerify(exactly = 1) {
            personHealthEventService.getSummaryByCategories(
                staffId = staff.id,
                dueDateTime = dueDate,
                categories = listOf(HEALTH_PLAN_TEST_RESULT)
            )
        }
        coVerify(exactly = 1) {
            personHealthEventService.getSummaryByCategories(
                staffId = staff.id,
                dueDateTime = dueDate,
                categories = listOf(APPOINTMENT_COMMUNITY)
            )
        }
        coVerify(exactly = 1) {
            personHealthEventService.getSummaryByCategories(
                staffId = staff.id,
                dueDateTime = dueDate,
                categories = emptyList()
            )
        }
        coVerify(exactly = 4) {
            personHealthEventService.getSummaryByCategories(
                any(),
                any(),
                any()
            )
        }
        coVerify(exactly = 1) {
            personHealthEventService.getSummaryByPool(
                any(),
                any()
            )
        }
        coVerify(exactly = 1) {
            healthcareAdditionalTeamService.getByStaffId(
                any()
            )
        }
        confirmVerified(
            healthcareAdditionalTeamService,
            personHealthEventService
        )
    }

    @Test
    fun `#getSummary should return default summaries`() = runBlocking {
        val expected = CockpitSummaryResponse(
            pool = expectedPoolFilters,
            summaries = listOf(
                SummaryByCategory(
                    title = "Demandas do pool",
                    query = "filter={\"health_care_additional_team_id\"" +
                        ":[\"${additionalTeam.id}\"]," +
                        "\"status\":[\"IN_PROGRESS\",\"NOT_STARTED\"]," +
                        "\"due_date\":{\"second\":\"${dueDate.with(LocalTime.MAX)}\"}}",
                    summary = healthEventSummary,
                    type = CockpitSummaryType.POOL
                ),
                SummaryByCategory(
                    title = "Todas Atividades",
                    query = "filter={\"staff_id\":[\"${staff.id}\"]," +
                        "\"status\":[\"IN_PROGRESS\",\"NOT_STARTED\"]," +
                        "\"due_date\":{\"second\":\"${dueDate.with(LocalTime.MAX)}\"}}",
                    summary = healthEventSummary
                )
            )
        )

        coEvery {
            personHealthEventService.getSummaryByCategories(
                staffId = staff.id,
                dueDateTime = dueDate,
                categories = any()
            )
        } returns healthEventSummary.success()

        withFeatureFlag(FeatureNamespace.WANDA, key = "show_pool_cockpit", true) {
            authenticatedWithRolesAs(idToken, staffTest, roles = listOf(HEALTH_DECLARATION_NURSE.name)) {
                get("/ehr/staff/cockpit") { response ->
                    assertThat(response).isOKWithData(expected)
                }
            }
        }

        coVerify(exactly = 1) {
            personHealthEventService.getSummaryByCategories(
                staffId = staff.id,
                dueDateTime = dueDate,
                categories = emptyList()
            )
        }
        coVerify(exactly = 1) {
            personHealthEventService.getSummaryByCategories(
                any(),
                any(),
                any()
            )
        }
        coVerify(exactly = 1) {
            personHealthEventService.getSummaryByPool(
                any(),
                any()
            )
        }
        coVerify(exactly = 1) {
            healthcareAdditionalTeamService.getByStaffId(
                any()
            )
        }
        confirmVerified(
            healthcareAdditionalTeamService,
            personHealthEventService
        )
    }
}
