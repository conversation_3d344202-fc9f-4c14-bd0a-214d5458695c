package br.com.alice.channel.core.consumers

import br.com.alice.channel.client.ChannelFupService
import br.com.alice.channel.core.context.FirestoreContextUsage
import br.com.alice.channel.core.services.ChannelFollowUp
import br.com.alice.channel.core.services.internal.FollowUpHistoryService
import br.com.alice.channel.core.services.internal.firestore.ChannelFirestoreService
import br.com.alice.channel.event.AutomaticFollowUpSentEvent
import br.com.alice.channel.event.PersonHealthEventUpdateRequestEvent
import br.com.alice.channel.models.ChannelDocument
import br.com.alice.channel.models.ChannelFollowUpMessage
import br.com.alice.channel.models.ChannelFollowUpResponse
import br.com.alice.channel.models.MessageDocument
import br.com.alice.channel.models.MessageType
import br.com.alice.channel.models.Origin
import br.com.alice.common.RangeUUID
import br.com.alice.common.core.PersonId
import br.com.alice.common.core.exceptions.NotFoundException
import br.com.alice.common.data.dsl.matchers.ResultAssert.Companion.assertThat
import br.com.alice.common.featureflag.withFeatureFlags
import br.com.alice.common.helpers.mockRangeUuidAndDateTime
import br.com.alice.common.kafka.interfaces.KafkaProducerService
import br.com.alice.communication.firebase.FirebasePush
import br.com.alice.communication.firebase.PushService
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.AutomaticFollowUpContent
import br.com.alice.data.layer.models.AutomaticFollowUpType
import br.com.alice.data.layer.models.ChannelFollowUpOptions
import br.com.alice.data.layer.models.ChannelFupAnswer
import br.com.alice.data.layer.models.ChannelStatus
import br.com.alice.data.layer.models.Device
import br.com.alice.data.layer.models.FeatureNamespace
import br.com.alice.data.layer.models.FollowUpHistory
import br.com.alice.data.layer.models.PersonHealthEvent
import br.com.alice.data.layer.models.PersonHealthEventStatus
import br.com.alice.data.layer.models.ReferencedLink
import br.com.alice.membership.client.DeviceService
import br.com.alice.person.client.PersonService
import br.com.alice.wanda.event.AutomaticFollowUpEvent
import com.github.kittinunf.result.failure
import com.github.kittinunf.result.success
import io.mockk.called
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.confirmVerified
import io.mockk.mockk
import kotlinx.coroutines.runBlocking
import java.time.LocalDateTime
import kotlin.test.AfterTest
import kotlin.test.Test

class AutomaticFollowUpConsumerTest : ConsumerTest() {

    private val personService: PersonService = mockk()
    private val channelFirestoreService: ChannelFirestoreService = mockk()
    private val deviceService: DeviceService = mockk()
    private val pushService: PushService = mockk()
    private val kafkaProducerService: KafkaProducerService = mockk()
    private val channelFupService: ChannelFupService = mockk()
    private val followUpHistoryService: FollowUpHistoryService = mockk()

    private val consumer = AutomaticFollowUpConsumer(
        personService,
        channelFirestoreService,
        deviceService,
        pushService,
        kafkaProducerService,
        channelFupService,
        followUpHistoryService
    )

    private val personId = PersonId()
    private val person = TestModelFactory.buildPerson(personId)
    private val channelPersonId = RangeUUID.generate().toString()
    private val channelId = "channel_id"
    private val channelDocument = ChannelDocument(
        id = channelId,
        channelPersonId = channelPersonId,
        staffIds = listOf(RangeUUID.generate().toString()),
        tags = emptyList(),
        personId = personId.toString(),
        appVersion = "1.52.2 (9701) android",
        lastPreviewableMessage = MessageDocument(
            userId = RangeUUID.generate().toString(),
            type = MessageType.TEXT
        )
    )
    private val channelFup = TestModelFactory.buildChannelFup(
        answers = listOf(
            ChannelFupAnswer(icon = "icon_01", key = "key_01", label = "label_01"),
            ChannelFupAnswer(icon = "icon_02", key = "key_02", label = "label_02")
        )
    )
    private val personHealthEvent = TestModelFactory.buildPersonHealthEvent(
        personId = personId,
        referencedLinks = listOf(ReferencedLink(id = channelId, model = "Channel")),
        automaticFollowUp = true,
        automaticFollowUpMessage = "<h4>fup_message</h4>",
        automaticFollowUpContent = AutomaticFollowUpContent(
            message = "<h4>automatic_fup_message</h4>",
            type = AutomaticFollowUpType.ACUTE_CASE
        )
    )
    private val event = AutomaticFollowUpEvent(personHealthEvent)
    private val device = Device(personId = personId, deviceId = "device_id")
    private val sentMessageId = "sent_message_id"

    private val now = LocalDateTime.now()
    private val uuid = RangeUUID.generate()

    @AfterTest
    fun confirmMocks() = confirmVerified(
        personService,
        channelFirestoreService,
        deviceService,
        pushService,
        kafkaProducerService,
        channelFupService,
        followUpHistoryService
    )

    @OptIn(FirestoreContextUsage::class)
    @Test
    fun `#sendFupMessage should send fup message by type`() = mocks {
        val personHealthEvent = personHealthEvent.copy(
            automaticFollowUpContent = AutomaticFollowUpContent(
                message = "Sobre a introdução alimentar: como está a aceitação dos alimentos?",
                type = AutomaticFollowUpType.CHILDREN_8_MONTHS
            )
        )
        val expectChannelFollowUpResponse = ChannelFollowUpResponse(
            type = AutomaticFollowUpType.CHILDREN_8_MONTHS,
            description = "Crianças - 8 meses - Introdução alimentar",
            question = "Sobre a introdução alimentar: como está a aceitação dos alimentos?",
            answers = listOf(
                ChannelFollowUpOptions(
                    label = "Está sendo ótimo!",
                    value = "fup_i_fell_better"
                ),
                ChannelFollowUpOptions(
                    label = "Tem sido difícil",
                    value = "fup_i_didnt_see_improvement"
                ),
                ChannelFollowUpOptions(
                    label = "Tem dias e dias...",
                    value = "fup_i_feel_worse"
                )
            )
        )
        val fupMessage = personHealthEvent.automaticFollowUpContent?.message.orEmpty()
        val fupAttribute = buildFupAttribute(personHealthEvent, fupMessage)
        val fupHistory = FollowUpHistory(
            id = uuid,
            createdAt = now,
            updatedAt = now,
            personId = personId,
            channelId = channelId,
            wandaTaskId = personHealthEvent.id,
            staffId = personHealthEvent.staffId!!,
            question = fupMessage,
            options = fupAttribute.options,
            sentAt = now,
        )
        val push = FirebasePush(
            deviceToken = device.deviceId,
            title = "Mensagem do seu Time",
            body = "",
            data = mapOf(
                "path_to_navigate" to "channel",
                "parameter" to channelId,
                "properties" to "{\"category\": \"${Origin.CHANNELS.description}\"}"
            )
        )

        coEvery { channelFirestoreService.getChannel(channelId) } returns channelDocument.success()
        coEvery { personService.get(personId, withUserType = false) } returns person.success()
        coEvery {
            channelFupService.getByType(AutomaticFollowUpType.CHILDREN_8_MONTHS)
        } returns expectChannelFollowUpResponse.success()
        coEvery {
            channelFirestoreService.updateFields(channelId, mapOf(ChannelDocument::followUp to fupAttribute))
        } returns channelId.success()
        coEvery { deviceService.getDeviceByPerson(personId.toString()) } returns device.success()
        coEvery { pushService.send(push) } returns sentMessageId.success()
        coEvery { followUpHistoryService.create(fupHistory) } returns fupHistory.success()
        coEvery {
            kafkaProducerService.produce(match { event: PersonHealthEventUpdateRequestEvent ->
                event.payload.personHealthEvent.status == PersonHealthEventStatus.FINISHED
            })
        } returns mockk()
        coEvery {
            kafkaProducerService.produce(match { event: AutomaticFollowUpSentEvent ->
                event.payload.channelId == channelId
                        && event.payload.questionFupMessage == fupMessage
                        && event.payload.personId == personId
            })
        } returns mockk()

        val result = consumer.sendFupMessage(AutomaticFollowUpEvent(personHealthEvent))
        assertThat(result).isSuccessWithData(channelId)

        coVerify(exactly = 1) { channelFirestoreService.getChannel(any()) }
        coVerify(exactly = 1) { personService.get(any(), any()) }
        coVerify(exactly = 1) { channelFupService.getByType(any()) }
        coVerify(exactly = 1) { channelFirestoreService.updateFields(any(), any()) }
        coVerify(exactly = 1) { deviceService.getDeviceByPerson(any()) }
        coVerify(exactly = 1) { pushService.send(any()) }
        coVerify(exactly = 1) { followUpHistoryService.create(any()) }
        coVerify(exactly = 2) { kafkaProducerService.produce(any()) }
    }

    @OptIn(FirestoreContextUsage::class)
    @Test
    fun `#sendFupMessage should send fup message by fup id`() = mocks {
        val channelFupId = RangeUUID.generate()
        val personHealthEvent = personHealthEvent.copy(
            automaticFollowUpContent = AutomaticFollowUpContent(
                message = "Sobre a introdução alimentar: como está a aceitação dos alimentos?",
                type = AutomaticFollowUpType.CHILDREN_8_MONTHS,
                channelFupId = channelFupId
            )
        )

        val fupMessage = personHealthEvent.automaticFollowUpContent?.message.orEmpty()
        val fupAttribute = buildFupAttribute(personHealthEvent, fupMessage)
        val now = LocalDateTime.now()
        val uuid = RangeUUID.generate()
        val fupHistory = FollowUpHistory(
            id = uuid,
            createdAt = now,
            updatedAt = now,
            personId = personId,
            channelId = channelId,
            wandaTaskId = personHealthEvent.id,
            staffId = personHealthEvent.staffId!!,
            followUpId = personHealthEvent.automaticFollowUpContent?.channelFupId,
            question = fupMessage,
            options = fupAttribute.options,
            sentAt = now,
        )
        val push = FirebasePush(
            deviceToken = device.deviceId,
            title = "Mensagem do seu Time",
            body = "",
            data = mapOf(
                "path_to_navigate" to "channel",
                "parameter" to channelId,
                "properties" to "{\"category\": \"${Origin.CHANNELS.description}\"}"
            )
        )

        coEvery { channelFirestoreService.getChannel(channelId) } returns channelDocument.success()
        coEvery { personService.get(personId, withUserType = false) } returns person.success()
        coEvery { channelFupService.get(channelFupId.toString()) } returns channelFup.success()
        coEvery {
            channelFirestoreService.updateFields(channelId, mapOf(ChannelDocument::followUp to fupAttribute))
        } returns channelId.success()
        coEvery { deviceService.getDeviceByPerson(personId.toString()) } returns device.success()
        coEvery { pushService.send(push) } returns sentMessageId.success()
        coEvery { followUpHistoryService.create(fupHistory) } returns fupHistory.success()
        coEvery {
            kafkaProducerService.produce(match { event: PersonHealthEventUpdateRequestEvent ->
                event.payload.personHealthEvent.status == PersonHealthEventStatus.FINISHED
            })
        } returns mockk()
        coEvery {
            kafkaProducerService.produce(match { event: AutomaticFollowUpSentEvent ->
                event.payload.channelId == channelId
                        && event.payload.questionFupMessage == fupMessage
                        && event.payload.personId == personId
            })
        } returns mockk()

        val result = consumer.sendFupMessage(AutomaticFollowUpEvent(personHealthEvent))
        assertThat(result).isSuccessWithData(channelId)

        coVerify(exactly = 1) { channelFirestoreService.getChannel(any()) }
        coVerify(exactly = 1) { personService.get(any(), any()) }
        coVerify(exactly = 1) { channelFupService.get(any()) }
        coVerify(exactly = 1) { channelFirestoreService.updateFields(any(), any()) }
        coVerify(exactly = 1) { deviceService.getDeviceByPerson(any()) }
        coVerify(exactly = 1) { pushService.send(any()) }
        coVerify(exactly = 1) { followUpHistoryService.create(any()) }
        coVerify(exactly = 2) { kafkaProducerService.produce(any()) }
    }

    @Test
    fun `#sendFupMessage should not send message when channel id is invalid`() = runBlocking {
        val expectedHealthEvent = personHealthEvent.copy(
            referencedLinks = emptyList(),
            status = PersonHealthEventStatus.CANCELLED
        )
        val invalidEvent = AutomaticFollowUpEvent(expectedHealthEvent)

        coEvery {
            kafkaProducerService.produce(match { event: PersonHealthEventUpdateRequestEvent ->
                event.payload.personHealthEvent.status == PersonHealthEventStatus.CANCELLED
                        && event.payload.personHealthEvent.referencedLinks.isEmpty()
            })
        } returns mockk()

        val result = consumer.sendFupMessage(invalidEvent)
        assertThat(result).isSuccessWithData(true)

        coVerify(exactly = 1) { kafkaProducerService.produce(any()) }
    }

    @Test
    fun `#sendFupMessage should not send message when exception is thrown`() = runBlocking {
        coEvery { channelFirestoreService.getChannel(channelId) } returns channelDocument.success()
        coEvery { personService.get(personId, withUserType = false) } returns Exception().failure()
        coEvery {
            kafkaProducerService.produce(match { event: PersonHealthEventUpdateRequestEvent ->
                event.payload.personHealthEvent.status == PersonHealthEventStatus.CANCELLED
            })
        } returns mockk()


        val result = consumer.sendFupMessage(event)
        assertThat(result).isSuccessWithData(true)

        coVerify(exactly = 1) { channelFirestoreService.getChannel(any()) }
        coVerify(exactly = 1) { personService.get(any(), any()) }
        coVerify(exactly = 1) { kafkaProducerService.produce(any()) }
    }

    @OptIn(FirestoreContextUsage::class)
    @Test
    fun `#sendFupMessage should not send fup message and push when person is invalid`() = runBlocking {
        coEvery { channelFirestoreService.getChannel(channelId) } returns channelDocument.success()
        coEvery {
            personService.get(personHealthEvent.personId, withUserType = false)
        } returns NotFoundException("person_not_found").failure()
        coEvery {
            kafkaProducerService.produce(match { event: PersonHealthEventUpdateRequestEvent ->
                event.payload.personHealthEvent.status == PersonHealthEventStatus.CANCELLED
            })
        } returns mockk()

        val result = consumer.sendFupMessage(event)
        assertThat(result).isSuccessWithData(true)

        coVerify(exactly = 1) { channelFirestoreService.getChannel(any()) }
        coVerify(exactly = 1) { personService.get(any(), any()) }
        coVerify(exactly = 0) { channelFirestoreService.updateFields(any(), any()) }
        coVerify { deviceService wasNot called }
        coVerify { pushService wasNot called }
        coVerify { followUpHistoryService wasNot called }
        coVerify(exactly = 1) { kafkaProducerService.produce(any()) }
    }

    @OptIn(FirestoreContextUsage::class)
    @Test
    fun `#sendFupMessage should not send fup message when channel is not active`() = runBlocking {
        coEvery {
            channelFirestoreService.getChannel(channelId)
        } returns channelDocument.copy(status = ChannelStatus.ARCHIVED).success()
        coEvery {
            kafkaProducerService.produce(match { event: PersonHealthEventUpdateRequestEvent ->
                event.payload.personHealthEvent.status == PersonHealthEventStatus.CANCELLED
            })
        } returns mockk()

        val result = consumer.sendFupMessage(event)
        assertThat(result).isSuccessWithData(true)

        coVerify(exactly = 1) { channelFirestoreService.getChannel(any()) }
        coVerify(exactly = 0) { channelFirestoreService.updateFields(any(), any()) }
        coVerify(exactly = 1) { kafkaProducerService.produce(any()) }
        coVerify { deviceService wasNot called }
        coVerify { pushService wasNot called }
        coVerify { personService wasNot called }
    }

    private fun buildFupAttribute(healthEvent: PersonHealthEvent, questionFupMessage: String) =
        healthEvent.automaticFollowUpContent?.let { content ->
            if (content.channelFupId != null) {
                val formattedAnswers = channelFup.answers.map {
                    ChannelFollowUpOptions(
                        icon = it.icon,
                        value = it.key,
                        label = it.label
                    )
                }
                ChannelFollowUp(
                    id = personHealthEvent.id.toString(),
                    title = questionFupMessage,
                    options = getAnswersIdentified(formattedAnswers, person.contactName)
                )
            } else {
                val answers = ChannelFollowUpMessage.build(content.type).answers
                ChannelFollowUp(
                    id = personHealthEvent.id.toString(),
                    title = questionFupMessage,
                    options = answers.map {
                        val label = it.label.replace("@nickname", person.contactName)
                        it.copy(label = label)
                    }
                )
            }
        } ?: run {
            ChannelFollowUp(
                id = personHealthEvent.id.toString(),
                title = questionFupMessage,
                options = listOf(
                    ChannelFollowUpOptions(
                        label = "Me sinto melhor",
                        value = "fup_i_fell_better"
                    ),
                    ChannelFollowUpOptions(
                        label = "Não percebi melhora",
                        value = "fup_i_didnt_see_improvement"
                    ),
                    ChannelFollowUpOptions(
                        label = "Me sinto pior",
                        value = "fup_i_feel_worse"
                    )
                )
            )
        }

    private fun getAnswersIdentified(
        answersDeIdentified: List<ChannelFollowUpOptions>,
        personContactName: String
    ) = answersDeIdentified.map {
        val label = it.label.replace("@nickname", personContactName)
        it.copy(label = label)
    }

    private fun mocks(testFunction: suspend () -> Unit) =
        mockRangeUuidAndDateTime(uuid, now) {
            withFeatureFlags(
                namespace = FeatureNamespace.CHANNELS,
                mapOf(
                    "fup_i_fell_better_icon" to "",
                    "fup_i_didnt_see_improvement_icon" to "",
                    "fup_i_feel_worse_icon" to "",
                    "fup_push_message" to "",
                    "should_send_automatic_fups" to true,
                    "app_version_release_automatic_fup" to "1.52.0"
                )
            ) {
                testFunction()
            }
        }
}
