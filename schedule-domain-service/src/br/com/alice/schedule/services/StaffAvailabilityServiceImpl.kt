package br.com.alice.schedule.services

import br.com.alice.common.core.PersonId
import br.com.alice.common.core.extensions.atBeginningOfTheDay
import br.com.alice.common.core.extensions.atEndOfTheDay
import br.com.alice.common.core.extensions.isBeforeEq
import br.com.alice.common.core.extensions.timeDiff
import br.com.alice.common.core.extensions.toCustomFormat
import br.com.alice.common.core.extensions.toSaoPauloTimeZone
import br.com.alice.common.coroutine.pmap
import br.com.alice.common.extensions.thenError
import br.com.alice.common.logging.logger
import br.com.alice.common.observability.recordResult
import br.com.alice.common.observability.setAttribute
import br.com.alice.data.layer.models.AppointmentScheduleEventType
import br.com.alice.data.layer.models.AppointmentScheduleType
import br.com.alice.data.layer.models.FeatureNamespace
import br.com.alice.data.layer.models.Staff
import br.com.alice.data.layer.models.StaffSchedule
import br.com.alice.data.layer.models.Weekday
import br.com.alice.featureconfig.core.FeatureService
import br.com.alice.schedule.client.AppointmentScheduleEventTypeService
import br.com.alice.schedule.client.EventTypeProviderUnitService
import br.com.alice.schedule.client.StaffAvailabilityService
import br.com.alice.schedule.model.SlotForSpecificDuration
import br.com.alice.schedule.model.StaffForSlot
import br.com.alice.schedule.services.internal.SlotsGenerator
import br.com.alice.staff.client.HealthcareTeamService
import br.com.alice.staff.client.StaffService
import com.github.kittinunf.result.Result
import com.github.kittinunf.result.flatMap
import com.github.kittinunf.result.map
import com.github.kittinunf.result.success
import io.ktor.util.date.WeekDay
import io.opentelemetry.api.trace.Span
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.async
import kotlinx.coroutines.awaitAll
import kotlinx.coroutines.coroutineScope
import java.time.LocalDate
import java.time.LocalDateTime
import java.time.LocalTime
import java.util.UUID


class StaffAvailabilityServiceImpl(
    private val appointmentScheduleEventTypeService: AppointmentScheduleEventTypeService,
    private val appointmentScheduleOptionService: AppointmentScheduleOptionServiceImpl,
    private val staffService: StaffService,
    private val appointmentScheduleEventTypeDateExceptionService: AppointmentScheduleEventTypeDateExceptionServiceImpl,
    private val staffScheduleService: StaffScheduleServiceImpl,
    private val externalCalendarEventService: ExternalCalendarEventServiceImpl,
    private val eventTypeProviderUnitService: EventTypeProviderUnitService,
    private val healthcareTeamService: HealthcareTeamService,
) : StaffAvailabilityService {

    override suspend fun getForStaffAndEventTypeAndPeriod(
        staffId: UUID,
        startDateTime: LocalDateTime,
        endDateTime: LocalDateTime,
        appointmentScheduleEventTypeId: UUID,
        appointmentScheduleEventType: AppointmentScheduleEventType?,
        providerUnitId: UUID?,
        byPassSchedulingConstraints: Boolean,
        filterWeekday: WeekDay?,
    ): Result<List<SlotForSpecificDuration>, Throwable> = span("getForStaffAndEventTypeAndPeriod") { span ->
        coroutineScope {
            val eventType = appointmentScheduleEventType
                ?: appointmentScheduleEventTypeService.get(appointmentScheduleEventTypeId).get()

            val schedulingParameters = getSchedulingParametersForEventTypeAndProviderUnit(
                eventType,
                providerUnitId
            )
                .recordResult(span)
                .get()
            span.setSchedulingParameters(schedulingParameters)
            val shouldLogDebugInformation = FeatureService.get(
                namespace = FeatureNamespace.SCHEDULE,
                key = "should_log_debug_info_staff_availability",
                defaultValue = false
            )
            span.setAttribute("should_log_debug_information", shouldLogDebugInformation)
            val staffSchedules = getStaffSchedulesDedicatedToAppointments(
                appointmentScheduleEventTypeId = eventType.id,
                staffIds = listOf(staffId),
                providerUnitIds = providerUnitId?.let { listOf(it) },
                schedulingParameters = schedulingParameters,
                filterWeekday = filterWeekday,
            )

            val minimumTimeToScheduleBeforeAppointmentTime =
                schedulingParameters.minimumTimeToScheduleBeforeAppointmentTime

            val startPeriod =
                getStartPeriod(byPassSchedulingConstraints, startDateTime, minimumTimeToScheduleBeforeAppointmentTime)
            val endPeriod = getEndPeriod(byPassSchedulingConstraints, endDateTime, schedulingParameters)
            val externalCalendarEventsDeferred = getExternalCalendarEventsAsync(staffId, startPeriod, endPeriod)
            val dateExceptions = getDateExceptions(eventType.id, startPeriod, endPeriod)
                .get()
                .map { it.exceptionDate }
            val externalCalendarEvents = externalCalendarEventsDeferred.await().get()

            if (shouldLogDebugInformation)
                span.setDebugInfo(
                    eventType.id,
                    byPassSchedulingConstraints,
                    startPeriod,
                    endPeriod,
                    staffSchedules,
                    dateExceptions,
                    filterWeekday ?: WeekDay.valueOf(startDateTime.dayOfWeek.toString()),
                    providerUnitId
                )

            val slotsForSpecificDuration = SlotsGenerator.generate(
                staffSchedules = staffSchedules[staffId] ?: emptyList(),
                externalCalendarEvents = externalCalendarEvents,
                startDateTime = startPeriod,
                endDateTime = endPeriod,
                dateExceptions = dateExceptions,
                eventTypeId = eventType.id,
                providerUnitId = providerUnitId,
                schedulingParameters = schedulingParameters
            )

            slotsForSpecificDuration.success()
        }
    }

    private fun CoroutineScope.getExternalCalendarEventsAsync(
        staffId: UUID,
        startPeriod: LocalDateTime,
        endPeriod: LocalDateTime
    ) = async {
        externalCalendarEventService.getActiveBusyEventsForStaffForPeriod(
            staffId,
            startPeriod.toLocalDate().atStartOfDay().minusDays(1),
            endPeriod.toLocalDate().atEndOfTheDay().plusDays(1),
        )
    }

    private fun getEndPeriod(
        byPassSchedulingConstraints: Boolean,
        endDateTime: LocalDateTime,
        schedulingParameters: SchedulingParameters
    ) = if (byPassSchedulingConstraints) {
        endDateTime
    } else {
        buildEndPeriod(schedulingParameters.numberOfDaysFromNowToAllowScheduling, endDateTime)
    }

    private fun getStartPeriod(
        byPassSchedulingConstraints: Boolean,
        startDateTime: LocalDateTime,
        minimumTimeToScheduleBeforeAppointmentTime: Int
    ) = if (byPassSchedulingConstraints) {
        startDateTime
    } else {
        getStartPeriodToBuildSlots(minimumTimeToScheduleBeforeAppointmentTime, startDateTime)
    }

    private fun Span.setDebugInfo(
        appointmentScheduleEventTypeId: UUID,
        byPassSchedulingConstraints: Boolean,
        startPeriod: LocalDateTime,
        endPeriod: LocalDateTime,
        staffSchedules: Map<UUID, List<StaffSchedule>>,
        dateExceptions: List<LocalDate>,
        filterWeekday: WeekDay,
        providerUnitId: UUID?
    ) {
        setAttribute("appointment_schedule_event_type_id", appointmentScheduleEventTypeId)
        setAttribute("by_pass_scheduling_constraints", byPassSchedulingConstraints)
        setAttribute("start_period", startPeriod)
        setAttribute("end_period", endPeriod)
        setAttribute("staff_schedules", staffSchedules.values.size)
        setAttribute("date_exceptions", dateExceptions.size)
        setAttribute("filter_weekday", filterWeekday)
        setAttribute("provider_unit_id", providerUnitId)
    }

    override suspend fun getSlotsAtSpecificDateTime(
        staffId: UUID,
        startDateTime: LocalDateTime,
        endDateTime: LocalDateTime,
        appointmentScheduleEventTypeId: UUID,
        appointmentScheduleEventType: AppointmentScheduleEventType?,
        providerUnitId: UUID?,
        numberOfSessions: Int
    ): Result<List<SlotForSpecificDuration>, Throwable> =
        getForStaffAndEventTypeAndPeriod(
            staffId,
            startDateTime,
            endDateTime,
            appointmentScheduleEventTypeId,
            appointmentScheduleEventType,
            providerUnitId,
            byPassSchedulingConstraints = true,
            filterWeekday = WeekDay.valueOf(startDateTime.dayOfWeek.toString())
        ).flatMap {
            val startDateTimeAtSaoPauloTimeZone = startDateTime.toSaoPauloTimeZone()
            val endDateTimeAtSaoPauloTimeZone = endDateTime.toSaoPauloTimeZone()
            Result.of {
                it.filter {
                    it.startTime.toLocalTime() == startDateTimeAtSaoPauloTimeZone.toLocalTime() &&
                            it.endTime.toLocalTime() == endDateTimeAtSaoPauloTimeZone.toLocalTime() &&
                            it.startTime.dayOfWeek == startDateTimeAtSaoPauloTimeZone.dayOfWeek
                }
            }
        }

    override suspend fun getForEventTypeAndPeriod(
        startDate: LocalDate,
        endDate: LocalDate,
        appointmentScheduleEventTypeId: UUID,
        appointmentScheduleEventType: AppointmentScheduleEventType?,
        providerUnitIds: List<UUID>?,
        personId: PersonId?
    ): Result<List<SlotForSpecificDuration>, Throwable> = span("getForEventTypeAndPeriod") { span ->
        span.setAttribute("person_id", personId)
        span.setAttribute("appointment_schedule_event_type_id", appointmentScheduleEventTypeId)
        span.setAttribute("start_date", startDate.toString())
        span.setAttribute("end_date", endDate.toString())
        val eventType = appointmentScheduleEventType
            ?: appointmentScheduleEventTypeService.get(appointmentScheduleEventTypeId).get()
        span.setAttribute("provider_unit_ids", providerUnitIds?.map { it })

        getStaffsAvailabilityForEventTypeForPeriodWithDurationFromServer(
            appointmentScheduleEventTypeId,
            eventType,
            providerUnitIds,
            startDate,
            endDate,
            personId
        )
    }

    suspend fun getStaffsAvailabilityForEventTypeForPeriodWithDurationFromServer(
        appointmentScheduleEventTypeId: UUID,
        appointmentScheduleEventType: AppointmentScheduleEventType?,
        providerUnitIds: List<UUID>?,
        startDate: LocalDate,
        endDate: LocalDate,
        personId: PersonId? = null
    ): Result<List<SlotForSpecificDuration>, Throwable> = coroutineScope {
        logger.info(
            "StaffAvailabilityServiceImpl::getStaffsAvailabilityForEventTypeForPeriodWithDurationFromServer",
            "appointment_schedule_event_type_id" to appointmentScheduleEventTypeId,
            "provider_unit_id" to providerUnitIds?.map { it }.orEmpty(),
            "start_date" to startDate,
            "end_date" to endDate
        )

        val eventType = appointmentScheduleEventType
            ?: appointmentScheduleEventTypeService.get(appointmentScheduleEventTypeId).get()

        val schedulingParameters = getSchedulingParametersForEventTypeAndProviderUnit(
            eventType,
            providerUnitIds?.firstOrNull()
        ).get()

        val minimumTimeToScheduleBeforeAppointmentTime =
            schedulingParameters.minimumTimeToScheduleBeforeAppointmentTime
        val numberOfDaysFromNowToAllowScheduling = schedulingParameters.numberOfDaysFromNowToAllowScheduling

        val startPeriod =
            getStartPeriodToBuildSlots(minimumTimeToScheduleBeforeAppointmentTime, startDate.atStartOfDay())

        val endPeriod = buildEndPeriod(numberOfDaysFromNowToAllowScheduling, endDate.atEndOfTheDay())

        val appointmentScheduleOptions = appointmentScheduleOptionService
            .getByAppointmentScheduleEventType(
                eventType.id,
                checkHealthcareTeamPhysicianId(eventType, personId)
            ).get()

        val staffIds = appointmentScheduleOptions.mapNotNull { it.staffId }

        val staffSchedulesDeferred = async {
            getStaffSchedulesDedicatedToAppointments(
                appointmentScheduleEventTypeId = eventType.id,
                staffIds = staffIds,
                providerUnitIds = providerUnitIds,
                schedulingParameters = schedulingParameters
            )
        }

        val externalCalendarEventsDeferred = async {
            getExternalCalendarEvents(
                staffIds,
                startPeriod.atBeginningOfTheDay().minusDays(1),
                endPeriod.plusDays(1),
            )
        }

        val staffsDeferred = async { staffService.findByList(staffIds) }

        val dateExceptionsDeferred = async {
            getDateExceptions(eventType.id, startPeriod, endPeriod)
        }

        val staffs = staffsDeferred.await().get().filter { it.active }.associateBy { it.id }

        val externalCalendarEventsResponse =
            externalCalendarEventsDeferred.await().awaitAll().map { it.get() }.flatten()
        val externalCalendarEvents = externalCalendarEventsResponse.groupBy { it.staffId }
        val dateExceptions = dateExceptionsDeferred.await().get().map { it.exceptionDate }
        val idlenessByStaff = mutableMapOf<UUID, MutableMap<LocalDate, Double>>()
        val staffSchedules = staffSchedulesDeferred.await()

        val slotsForSpecificDuration = staffs.values.pmap { staff ->
            val slots = SlotsGenerator.generate(
                staffSchedules = staffSchedules[staff.id] ?: emptyList(),
                externalCalendarEvents = externalCalendarEvents[staff.id] ?: emptyList(),
                startDateTime = startPeriod,
                endDateTime = endPeriod,
                dateExceptions = dateExceptions,
                eventTypeId = eventType.id,
                providerUnitId = if (providerUnitIds?.size == 1) providerUnitIds.first() else null,
                schedulingParameters = schedulingParameters
            )

            val totalWorkingHorsForStaff = calculateTotalWorkingHoursForStaff(staffSchedules[staff.id])

            val slotsByDate = slots.groupBy { it.startTime.toLocalDate() }

            slotsByDate.map { (date, slotsByDate) ->
                val calculatedIdleness =
                    (slotsByDate.size * (schedulingParameters.duration / 60.0)) / totalWorkingHorsForStaff

                if (!idlenessByStaff.contains(staff.id)) idlenessByStaff[staff.id] = mutableMapOf()
                idlenessByStaff[staff.id]?.let { it[date] = calculatedIdleness }
            }

            slots
        }.flatten()

        return@coroutineScope Result.of {
            slotsWithSortedStaffForDateTimeByIdleness(slotsForSpecificDuration, staffs, idlenessByStaff)
        }
    }

    private suspend fun getDateExceptions(
        appointmentScheduleEventTypeId: UUID,
        startPeriod: LocalDateTime,
        endPeriod: LocalDateTime
    ) = appointmentScheduleEventTypeDateExceptionService.getForEventTypeForPeriod(
        appointmentScheduleEventTypeId = appointmentScheduleEventTypeId,
        fromDate = startPeriod.toLocalDate(),
        toDate = endPeriod.toLocalDate(),
    )

    private suspend fun checkHealthcareTeamPhysicianId(
        event: AppointmentScheduleEventType,
        personId: PersonId?
    ) = span("checkHealthcareTeamPhysicianId") { span ->
        span.setAttribute("person_id", personId)
        span.setAttribute("event_category", event.category)
        span.setAttribute("event_is_multi_professional_referral", event.isMultiProfessionalReferral)

        if (event.category == AppointmentScheduleType.HEALTHCARE_TEAM && !event.isMultiProfessionalReferral && personId != null) {
            val healthcareTeam = healthcareTeamService.getHealthcareTeamByPerson(personId).get()
            span.setAttribute("healthcare_team_id", healthcareTeam.id)
            span.setAttribute("healthcare_team_physician_id", healthcareTeam.physicianStaffId)
            listOf(healthcareTeam.physicianStaffId)
        } else null
    }

    fun getDatesCacheKeyPart(startDate: LocalDate, endDate: LocalDate): String {
        val dates = mutableListOf<LocalDate>()
        var current = startDate
        while (current.isBeforeEq(endDate)) {
            dates.add(current)
            current = current.plusDays(1)
        }

        return dates.joinToString("_") {
            it.toCustomFormat("YYYY-MM-dd")
        }
    }

    private fun calculateTotalWorkingHoursForStaff(staffSchedules: List<StaffSchedule>?) =
        staffSchedules?.let {
            it.fold(0.0) { acc, curr ->
                acc + timeDiff(curr.startHour, curr.untilHour)
            } / 60.0
        } ?: 0.0

    private fun slotsWithSortedStaffForDateTimeByIdleness(
        slotsForSpecificDuration: List<SlotForSpecificDuration>,
        staffs: Map<UUID, Staff>,
        idlenessByStaff: MutableMap<UUID, MutableMap<LocalDate, Double>>
    ) =
        slotsForSpecificDuration
            .fold(mutableMapOf<LocalDateTime, SlotForSpecificDuration>()) { acc, slotForSpecificDuration ->
                slotForSpecificDuration.staffId?.let { staffId ->
                    staffs[slotForSpecificDuration.staffId]?.let {
                        val staffForSlot = StaffForSlot(
                            name = it.fullName,
                            id = staffId
                        )

                        val slotForStartTimeWithStaffs = acc[slotForSpecificDuration.startTime]
                        slotForStartTimeWithStaffs?.let {
                            acc[slotForSpecificDuration.startTime] = slotForStartTimeWithStaffs.copy(
                                staffs = slotForStartTimeWithStaffs.staffs
                                    .plus(staffForSlot)
                                    .sortedByDescending { staffForSlot ->
                                        idlenessByStaff[staffForSlot.id]?.let { idleness ->
                                            idleness[slotForSpecificDuration.startTime.toLocalDate()]
                                        }
                                    }
                            )
                        } ?: run {
                            acc[slotForSpecificDuration.startTime] = slotForSpecificDuration.copy(
                                staffs = listOf(staffForSlot)
                            )
                        }
                    } ?: { acc }
                }

                acc
            }
            .values
            .toList()
            .sortedBy { it.startTime }

    private suspend fun getStaffSchedulesDedicatedToAppointments(
        appointmentScheduleEventTypeId: UUID,
        staffIds: List<UUID>,
        providerUnitIds: List<UUID>?,
        schedulingParameters: SchedulingParameters,
        filterWeekday: WeekDay? = null,
    ) =
        staffScheduleService.getActiveByStaffs(staffIds).get().filter { it.isDedicatedToAppointments() }
            .filter { staffSchedule ->
                val staffScheduleIsDigital = staffSchedule.providerUnitId == null
                val searchingForDigitalSchedule = providerUnitIds.isNullOrEmpty()
                val hasTheSameProviderUnit = when {
                    searchingForDigitalSchedule -> {
                        // staff schedule is digital and searching for digital schedules
                        staffScheduleIsDigital
                    }

                    else -> {
                        // staff schedule is digital and not searching for digital schedules
                        if (staffScheduleIsDigital) false
                        // staff schedule is not digital and searching for not digital schedules
                        else providerUnitIds?.contains(staffSchedule.providerUnitId) ?: false
                    }
                }
                val considerAlsoDigital =
                    if (searchingForDigitalSchedule) staffSchedule.alsoDigital
                    else false

                val hasValidWeekday = schedulingParameters.availableWeekDays
                    .map { it.toString() }
                    .contains(staffSchedule.weekDay.toString())
                val hasNotExceptionForEvent =
                    !staffSchedule.exceptionEventTypes.contains(appointmentScheduleEventTypeId)
                val hasSameProviderUnitOrUseAsDigital = hasTheSameProviderUnit || considerAlsoDigital
                val shouldFilterWeekday = (filterWeekday?.let { it == staffSchedule.weekDay } ?: true)

                hasNotExceptionForEvent && hasSameProviderUnitOrUseAsDigital && hasValidWeekday && shouldFilterWeekday
            }
            .groupBy { it.staffId }

    private fun getStartPeriodToBuildSlots(
        minimumTimeToScheduleBeforeAppointmentTime: Int,
        startDateTime: LocalDateTime
    ): LocalDateTime {
        val firstDateTimeToAllowScheduling = LocalDateTime.now().plusMinutes(
            minimumTimeToScheduleBeforeAppointmentTime.toLong()
        )

        val startPeriod = if (startDateTime.isBefore(firstDateTimeToAllowScheduling)) {
            firstDateTimeToAllowScheduling
        } else startDateTime
        return startPeriod
    }

    private fun buildEndPeriod(
        numberOfDaysFromNowToAllowScheduling: Int,
        endDateTime: LocalDateTime
    ): LocalDateTime {
        val hasValidNumberOfDaysFromNowToAllowScheduling = numberOfDaysFromNowToAllowScheduling > 0

        if (hasValidNumberOfDaysFromNowToAllowScheduling) {
            val maxDate = LocalDate.now().plusDays(numberOfDaysFromNowToAllowScheduling.toLong()).atEndOfTheDay()
            if (maxDate.isBefore(endDateTime)) {
                return maxDate
            }
        }

        return endDateTime
    }

    private suspend fun getSchedulingParametersForEventTypeAndProviderUnit(
        eventType: AppointmentScheduleEventType,
        providerUnitId: UUID?,
    ): Result<SchedulingParameters, Throwable> {
        logger.info(
            "StaffAvailabilityServiceImpl::getSchedulingParametersForEventTypeAndProviderUnit",
            "event_type_id" to eventType.id,
            "provider_unit_id" to providerUnitId
        )
        return eventTypeProviderUnitService.getForEventType(eventType.id)
            .map { eventTypeProviderUnits ->
                val eventTypeProviderUnit = eventTypeProviderUnits.associateBy { it.providerUnitId }[providerUnitId]
                SchedulingParameters(
                    duration = eventTypeProviderUnit?.duration ?: eventType.duration,
                    minimumTimeToScheduleBeforeAppointmentTime = eventTypeProviderUnit?.minimumTimeToScheduleBeforeAppointmentTime
                        ?: eventType.minimumTimeToScheduleBeforeAppointmentTime,
                    numberOfDaysFromNowToAllowScheduling = eventTypeProviderUnit?.numberOfDaysFromNowToAllowScheduling
                        ?: eventType.numberOfDaysFromNowToAllowScheduling,
                    availableWeekDays = eventTypeProviderUnit?.availableWeekDays ?: eventType.availableWeekDays,
                    availabilityStartTime = eventTypeProviderUnit?.availabilityStartTime,
                    availabilityEndTime = eventTypeProviderUnit?.availabilityEndTime
                )
            }.thenError {
                logger.error(
                    "StaffAvailabilityServiceImpl::getSchedulingParametersForEventTypeAndProviderUnit error",
                    "message" to it.message
                )
            }
    }

    private suspend fun getExternalCalendarEvents(
        staffIds: List<UUID>,
        startPeriod: LocalDateTime,
        endPeriod: LocalDateTime
    ) = coroutineScope {
        return@coroutineScope staffIds.chunked(5).map {
            async {
                externalCalendarEventService.getBusyForStaffs(
                    it,
                    startPeriod,
                    endPeriod,
                )
            }
        }
    }

    private fun Span.setSchedulingParameters(schedulingParameters: SchedulingParameters) {
        setAttribute("duration", schedulingParameters.duration)
        setAttribute(
            "minimum_time_to_schedule_before_appointment_time",
            schedulingParameters.minimumTimeToScheduleBeforeAppointmentTime
        )
        setAttribute(
            "number_of_days_from_now_to_allow_scheduling",
            schedulingParameters.numberOfDaysFromNowToAllowScheduling
        )
        setAttribute("available_week_days", schedulingParameters.availableWeekDays)
        setAttribute("availability_start_time", schedulingParameters.availabilityStartTime)
        setAttribute("availability_end_time", schedulingParameters.availabilityEndTime)
    }

    data class SchedulingParameters(
        val duration: Int,
        val minimumTimeToScheduleBeforeAppointmentTime: Int = 1,
        val numberOfDaysFromNowToAllowScheduling: Int = 90,
        val availableWeekDays: List<Weekday> = Weekday.values().toList(),
        val availabilityStartTime: LocalTime? = null,
        val availabilityEndTime: LocalTime? = null
    )

}
