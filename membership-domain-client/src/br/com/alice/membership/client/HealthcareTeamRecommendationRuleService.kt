package br.com.alice.membership.client

import br.com.alice.common.rfc.RemoteService
import br.com.alice.common.rfc.Service
import br.com.alice.data.layer.models.HealthcareTeamRecommendationRule
import com.github.kittinunf.result.Result
import java.util.UUID

@RemoteService
interface HealthcareTeamRecommendationRuleService: Service {
    override val namespace get() = "membership"
    override val serviceName get() = "healthcare_team_recommendation_rule"

    suspend fun list(): Result<List<HealthcareTeamRecommendationRule>, Throwable>
    suspend fun create(healthcareTeamRecommendationRule: HealthcareTeamRecommendationRule): Result<HealthcareTeamRecommendationRule, Throwable>
    suspend fun update(healthcareTeamRecommendationRule: HealthcareTeamRecommendationRule): Result<HealthcareTeamRecommendationRule, Throwable>
    suspend fun get(id: UUID): Result<HealthcareTeamRecommendationRule, Throwable>
    suspend fun delete(id: UUID): Result<Boolean, Throwable>
    suspend fun listWithRecommendedTeams(): Result<List<HealthcareTeamRecommendationRule>, Throwable>
}
