package br.com.alice.nullvs.controllers

import br.com.alice.business.client.BeneficiaryService
import br.com.alice.business.client.CompanyContractService
import br.com.alice.business.client.CompanyService
import br.com.alice.business.client.CompanySubContractService
import br.com.alice.common.RangeUUID
import br.com.alice.common.core.exceptions.DuplicatedItemException
import br.com.alice.common.data.dsl.matchers.ResponseAssert
import br.com.alice.common.helpers.bodyAsJson
import br.com.alice.common.helpers.coVerifyNone
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.common.helpers.returns
import br.com.alice.common.kafka.interfaces.KafkaProducerService
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.CompanyContract
import br.com.alice.data.layer.models.CompanySubContract
import br.com.alice.data.layer.models.ExternalModelType
import br.com.alice.data.layer.models.InternalModelType
import br.com.alice.data.layer.models.InvoiceStatus
import br.com.alice.data.layer.models.MemberStatus
import br.com.alice.moneyin.client.BillingAccountablePartyService
import br.com.alice.moneyin.client.InvoiceItemService
import br.com.alice.moneyin.client.InvoicePaymentService
import br.com.alice.moneyin.client.InvoicesService
import br.com.alice.moneyin.client.MemberInvoiceGroupService
import br.com.alice.moneyin.event.MemberInvoiceGroupPaidEvent
import br.com.alice.nullvs.common.NullvsActionType
import br.com.alice.nullvs.events.NullvsCreateIntegrationRecordEvent
import br.com.alice.nullvs.events.NullvsSyncClientRequestEvent
import br.com.alice.nullvs.events.NullvsSyncCompanyContractRequestEvent
import br.com.alice.nullvs.events.NullvsSyncCompanySubcontractRequestEvent
import br.com.alice.nullvs.events.NullvsSyncInvoiceRequestEvent
import br.com.alice.nullvs.events.NullvsSyncMemberRequestEvent
import br.com.alice.nullvs.models.BindBeneficiaryToSubcontractRequest
import br.com.alice.nullvs.models.CancelWaitingLogsRequest
import br.com.alice.nullvs.models.ClientsSyncRequest
import br.com.alice.nullvs.models.ContractSubcontract
import br.com.alice.nullvs.models.ContractsSyncRequest
import br.com.alice.nullvs.models.CreateContractSubcontractForCompanyRequest
import br.com.alice.nullvs.models.DeleteNullvsIntegrationRecordRequest
import br.com.alice.nullvs.models.FailedOrPendingInvoiceItemsRequest
import br.com.alice.nullvs.models.InvoicesSyncRequest
import br.com.alice.nullvs.models.MembersSyncRequest
import br.com.alice.nullvs.models.NullvsIntegrationRecordPayload
import br.com.alice.nullvs.models.NullvsIntegrationRecordRequest
import br.com.alice.nullvs.models.RecurrentPaymentSyncRequest
import br.com.alice.nullvs.models.SubcontractsSyncRequest
import br.com.alice.nullvs.models.SyncInvoiceItemsFromStartMonthRequest
import br.com.alice.nullvs.models.SyncResponse
import br.com.alice.nullvs.models.SyncStatus
import br.com.alice.nullvs.services.internals.NullvsIntegrationRecordService
import br.com.alice.nullvs.services.internals.NullsIntegrationLogReprocessService
import br.com.alice.nullvs.services.internals.TotvsPriceListingService
import br.com.alice.person.client.MemberService
import com.github.kittinunf.result.success
import io.ktor.client.statement.bodyAsText
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.coVerifyAll
import io.mockk.mockk
import kotlinx.coroutines.runBlocking
import org.assertj.core.api.Assertions
import org.junit.jupiter.api.Nested
import java.time.LocalDate
import java.time.LocalDateTime
import java.util.UUID
import kotlin.test.BeforeTest
import kotlin.test.Test

class BackfillControllerTest : ControllerTestHelper() {
    private val memberService: MemberService = mockk()
    private val billingAccountablePartyService: BillingAccountablePartyService = mockk()
    private val kafkaProducerService: KafkaProducerService = mockk()
    private val nullvsIntegrationRecordService: NullvsIntegrationRecordService = mockk()
    private val invoicesService: InvoicesService = mockk()
    private val companyService: CompanyService = mockk()
    private val companyContractService: CompanyContractService = mockk()
    private val companySubcontractService: CompanySubContractService = mockk()
    private val beneficiaryService: BeneficiaryService = mockk()
    private val nullsIntegrationLogReprocessService: NullsIntegrationLogReprocessService = mockk()
    private val memberInvoiceGroupService: MemberInvoiceGroupService = mockk()
    private val invoicePaymentService: InvoicePaymentService = mockk()
    private val authorizationHeader = mapOf("Authorization" to "Basic YWxpY2U6YWxpY2U=")
    private val totvsPriceListingService: TotvsPriceListingService = mockk()
    private val invoiceItemService: InvoiceItemService = mockk()

    private val controller =
        BackfillController(
            memberService,
            billingAccountablePartyService,
            kafkaProducerService,
            nullvsIntegrationRecordService,
            invoicesService,
            companyService,
            companyContractService,
            companySubcontractService,
            beneficiaryService,
            nullsIntegrationLogReprocessService,
            memberInvoiceGroupService,
            invoicePaymentService,
            totvsPriceListingService,
            invoiceItemService
        )

    @BeforeTest
    override fun setup() {
        super.setup()
        module.single { controller }
    }

    companion object {
        @JvmStatic
        fun syncMemberDependingOnStatus() = listOf(
            arrayOf(MemberStatus.ACTIVE, true, 1),
            arrayOf(MemberStatus.PENDING, false, 0)
        )
    }

    @Test
    fun `#should return 401 Unauthorized when Authorization header is not found`() {

        val memberId = RangeUUID.generate()
        val request = MembersSyncRequest(
            ids = listOf(memberId),
            action = NullvsActionType.CREATE
        )

        post(
            "/backfill/sync/members",
            body = request,
            headers = mapOf()
        ) {
            ResponseAssert.assertThat(it).isUnauthorized()
        }
    }

    @Test
    fun `#syncMembersTowardsTotvs - backfill to sync an active member towards Totvs`() = runBlocking {
        val memberId = RangeUUID.generate()
        val member = TestModelFactory.buildMember(id = memberId)
        val request = MembersSyncRequest(
            ids = listOf(memberId),
            action = NullvsActionType.CREATE
        )

        coEvery { memberService.findByIds(request.ids) } returns listOf(member)
        coEvery { kafkaProducerService.produce(any()) } returns mockk()

        internalAuthentication {
            post("/backfill/sync/members", body = request, headers = authorizationHeader) { response ->
                ResponseAssert.assertThat(response).isOK()
                val content = gson.fromJson(response.bodyAsText(), SyncResponse::class.java)
                Assertions.assertThat(content.response[0].success).isEqualTo(true)
            }
        }

        coVerify(exactly = 1) {
            kafkaProducerService.produce(match { it: NullvsSyncMemberRequestEvent ->
                it.payload.member.id == member.id
            })
        }

        coVerifyOnce { memberService.findByIds(request.ids) }
    }

    @Test
    fun `#syncMembersTowardsTotvs - member is not synced with Totvs due to an error`() = runBlocking {
        val memberId = RangeUUID.generate()
        val member = TestModelFactory.buildMember(id = memberId, status = MemberStatus.ACTIVE)
        val request = MembersSyncRequest(
            ids = listOf(memberId),
            action = NullvsActionType.CREATE
        )

        coEvery { memberService.findByIds(request.ids) } returns Exception("")

        internalAuthentication {
            post("/backfill/sync/members", body = request, headers = authorizationHeader) { response ->
                ResponseAssert.assertThat(response).isInternalServerError()
            }
        }

        coVerifyNone {
            kafkaProducerService.produce(match { it: NullvsSyncMemberRequestEvent ->
                it.payload.member.id == member.id
            })
        }

        coVerifyOnce { memberService.findByIds(request.ids) }
    }

    @Test
    fun `#syncRecurrentPaymentTowardsTotvs - backfill to sync a recurrent payment towards Totvs`() = runBlocking {
        val memberInvoiceGroupIds = listOf(RangeUUID.generate(), RangeUUID.generate())
        val memberInvoiceGroups = listOf(TestModelFactory.buildMemberInvoiceGroup(id = memberInvoiceGroupIds[0]), TestModelFactory.buildMemberInvoiceGroup(id = memberInvoiceGroupIds[1]))
        val invoicePayments = listOf(TestModelFactory.buildInvoicePayment(invoiceGroupId = memberInvoiceGroupIds[0]), TestModelFactory.buildInvoicePayment(invoiceGroupId = memberInvoiceGroupIds[1]))
        val request = RecurrentPaymentSyncRequest(
            memberInvoiceGroupIds = memberInvoiceGroupIds
        )

        coEvery { memberInvoiceGroupService.getByIds(request.memberInvoiceGroupIds) } returns memberInvoiceGroups.success()
        coEvery { invoicePaymentService.getByInvoiceGroupIds(request.memberInvoiceGroupIds) } returns invoicePayments.success()
        coEvery { kafkaProducerService.produce(any()) } returns mockk()

        internalAuthentication {
            post("/backfill/sync/invoice-group-payments", body = request, headers = authorizationHeader) { response ->
                ResponseAssert.assertThat(response).isOK()
                val content = gson.fromJson(response.bodyAsText(), SyncResponse::class.java)
                Assertions.assertThat(content.response[0].success).isEqualTo(true)
            }
        }

        coVerifyAll {
            kafkaProducerService.produce(match { it: MemberInvoiceGroupPaidEvent ->
                it.payload.memberInvoiceGroup.id == memberInvoiceGroups.first().id
                it.payload.invoicePayment.id == invoicePayments.first().id
            })
            kafkaProducerService.produce(match { it: MemberInvoiceGroupPaidEvent ->
                it.payload.memberInvoiceGroup.id == memberInvoiceGroups.last().id
                it.payload.invoicePayment.id == invoicePayments.last().id
            })
        }

        coVerifyOnce { memberInvoiceGroupService.getByIds(any()) }
        coVerifyOnce { invoicePaymentService.getByInvoiceGroupIds(any())}
    }

    @Nested
    inner class SyncContractTotvs {
        @Test
        fun `#sync a contract to towards Totvs`() = runBlocking {
            val contractId = RangeUUID.generate()
            val contract = TestModelFactory.buildCompanyContract(id = contractId)
            val request = ContractsSyncRequest(
                ids = listOf(contractId),
                action = NullvsActionType.CREATE,
            )

            coEvery { companyContractService.findByIds(request.ids) } returns listOf(contract)
            coEvery { kafkaProducerService.produce(any()) } returns mockk()

            internalAuthentication {
                post("/backfill/sync/contracts", body = request, headers = authorizationHeader) { response ->
                    ResponseAssert.assertThat(response).isOK()
                    val content = gson.fromJson(response.bodyAsText(), SyncResponse::class.java)
                    Assertions.assertThat(content.response[0].success).isEqualTo(true)
                }
            }

            coVerifyOnce {
                kafkaProducerService.produce(match { it: NullvsSyncCompanyContractRequestEvent ->
                    it.payload.contract.id == contract.id
                })
            }

            coVerifyOnce { companyContractService.findByIds(request.ids) }
        }

        @Test
        fun `#contract is not synced with Totvs due to an error`() = runBlocking {
            val contractId = RangeUUID.generate()
            val contract = TestModelFactory.buildCompanyContract(id = contractId)
            val request = ContractsSyncRequest(
                ids = listOf(contractId),
                action = NullvsActionType.CREATE,
            )

            coEvery { companyContractService.findByIds(request.ids) } returns Exception("")

            internalAuthentication {
                post("/backfill/sync/contracts", body = request, headers = authorizationHeader) { response ->
                    ResponseAssert.assertThat(response).isInternalServerError()
                }
            }

            coVerifyNone {
                kafkaProducerService.produce(match { it: NullvsSyncCompanyContractRequestEvent ->
                    it.payload.contract.id == contract.id
                })
            }

            coVerifyOnce { companyContractService.findByIds(request.ids) }
        }
    }

    @Nested
    inner class SyncSubcontractTotvs {
        @Test
        fun `#sync a subcontract to towards Totvs`() = runBlocking {
            val subcontractId = RangeUUID.generate()
            val subcontract = TestModelFactory.buildCompanySubContract(id = subcontractId)
            val request = SubcontractsSyncRequest(
                ids = listOf(subcontractId),
                action = NullvsActionType.CREATE,
            )

            coEvery { companySubcontractService.findByIds(request.ids) } returns listOf(subcontract)
            coEvery { kafkaProducerService.produce(any()) } returns mockk()

            internalAuthentication {
                post("/backfill/sync/subcontracts", body = request, headers = authorizationHeader) { response ->
                    ResponseAssert.assertThat(response).isOK()
                    val content = gson.fromJson(response.bodyAsText(), SyncResponse::class.java)
                    Assertions.assertThat(content.response[0].success).isEqualTo(true)
                }
            }

            coVerifyOnce {
                kafkaProducerService.produce(match { it: NullvsSyncCompanySubcontractRequestEvent ->
                    it.payload.subcontract.id == subcontract.id
                })
            }

            coVerifyOnce { companySubcontractService.findByIds(request.ids) }
        }

        @Test
        fun `#subcontract is not synced with Totvs due to an error`() = runBlocking {
            val subcontractId = RangeUUID.generate()
            val subcontract = TestModelFactory.buildCompanySubContract(id = subcontractId)
            val request = SubcontractsSyncRequest(
                ids = listOf(subcontractId),
                action = NullvsActionType.CREATE,
            )

            coEvery { companySubcontractService.findByIds(request.ids) } returns Exception("")

            internalAuthentication {
                post("/backfill/sync/subcontracts", body = request, headers = authorizationHeader) { response ->
                    ResponseAssert.assertThat(response).isInternalServerError()
                }
            }

            coVerifyNone {
                kafkaProducerService.produce(match { it: NullvsSyncCompanySubcontractRequestEvent ->
                    it.payload.subcontract.id == subcontract.id
                })
            }

            coVerifyOnce { companySubcontractService.findByIds(request.ids) }
        }
    }


    @Test
    fun `#syncMembersTowardsTotvs - should sync an active member towards Totvs and return to the API those not possible to sync`() =
        runBlocking {
            val memberId1 = RangeUUID.generate()
            val memberId2 = RangeUUID.generate()
            val memberActive = TestModelFactory.buildMember(id = memberId1, status = MemberStatus.ACTIVE)
            val memberPending = TestModelFactory.buildMember(id = memberId2, status = MemberStatus.PENDING)
            val request = MembersSyncRequest(
                ids = listOf(memberId1, memberId2),
                action = NullvsActionType.CREATE
            )

            coEvery { memberService.findByIds(request.ids) } returns listOf(memberActive, memberPending)
            coEvery { kafkaProducerService.produce(any()) } returns mockk()

            internalAuthentication {
                post("/backfill/sync/members", body = request, headers = authorizationHeader) { response ->
                    ResponseAssert.assertThat(response).isOK()
                    val content = gson.fromJson(response.bodyAsText(), SyncResponse::class.java)
                    Assertions.assertThat(content.response[0].success).isEqualTo(true)
                    Assertions.assertThat(content.response[1].success).isEqualTo(true)
                }
            }

            coVerifyOnce {
                kafkaProducerService.produce(match { it: NullvsSyncMemberRequestEvent ->
                    it.payload.member.id == memberActive.id
                })
            }

            coVerifyOnce {
                kafkaProducerService.produce(match { it: NullvsSyncMemberRequestEvent ->
                    it.payload.member.id == memberPending.id
                })
            }

            coVerifyOnce { memberService.findByIds(request.ids) }
        }

    @Test
    fun `#syncClientsTowardsTotvs - produced events for clients to sync with Totvs`() = runBlocking {
        val billingAccountableParty = TestModelFactory.buildBillingAccountableParty()
        val billingAccountableParty2 = TestModelFactory.buildBillingAccountableParty()

        val request = ClientsSyncRequest(
            billingAccountablePartyIds = listOf(billingAccountableParty.id, billingAccountableParty2.id),
        )

        coEvery {
            billingAccountablePartyService.findById(request.billingAccountablePartyIds)
        } returns listOf(billingAccountableParty, billingAccountableParty2)
        coEvery { kafkaProducerService.produce(any()) } returns mockk()

        internalAuthentication {
            post("/backfill/sync/clients", body = request, headers = authorizationHeader) { response ->
                ResponseAssert.assertThat(response).isOKWithData(
                    listOf(
                        SyncStatus(billingAccountableParty.id, true),
                        SyncStatus(billingAccountableParty2.id, true)
                    )
                )
            }
        }

        coVerifyOnce { billingAccountablePartyService.findById(request.billingAccountablePartyIds) }
        coVerifyOnce {
            kafkaProducerService.produce(match { it: NullvsSyncClientRequestEvent ->
                it.payload.billingAccountableParty.id == billingAccountableParty.id
            })
        }
        coVerifyOnce {
            kafkaProducerService.produce(match { it: NullvsSyncClientRequestEvent ->
                it.payload.billingAccountableParty.id == billingAccountableParty2.id
            })
        }
    }

    @Test
    fun `#createNullvsIntegrationRecord - should produce NullvsCreateIntegrationRecordEvent for all items on payload`() =
        runBlocking {
            val payload1 = NullvsIntegrationRecordPayload(
                internalId = RangeUUID.generate(),
                internalModelName = InternalModelType.MEMBER,
                externalId = "test1",
                externalModelName = ExternalModelType.BENEFICIARY
            )
            val payload2 = NullvsIntegrationRecordPayload(
                internalId = RangeUUID.generate(),
                internalModelName = InternalModelType.BILLING_ACCOUNTABLE_PARTY,
                externalId = "test1",
                externalModelName = ExternalModelType.CLIENT
            )
            val request = NullvsIntegrationRecordRequest(listOf(payload1, payload2))

            coEvery { kafkaProducerService.produce(any()) } returns mockk()

            internalAuthentication {
                post(
                    "/backfill/create-nullvs-integration-record",
                    body = request,
                    headers = authorizationHeader
                ) { response ->
                    ResponseAssert.assertThat(response).isOK()
                }
            }

            coVerifyOnce {
                kafkaProducerService.produce(match { it: NullvsCreateIntegrationRecordEvent ->
                    it.payload.nullvsIntegrationRecord.internalId == payload1.internalId
                })
            }
            coVerifyOnce {
                kafkaProducerService.produce(match { it: NullvsCreateIntegrationRecordEvent ->
                    it.payload.nullvsIntegrationRecord.internalId == payload2.internalId
                })
            }
        }

    @Test
    fun `#deleteNullvsIntegrarionRecord - should delete nullvsIntegrationRecord successfully`() =
        runBlocking {
            val nullvsIntegrationRecord = TestModelFactory.buildNullvsIntegrationRecord()
            val request = DeleteNullvsIntegrationRecordRequest(nullvsIntegrationRecord.id)

            coEvery { nullvsIntegrationRecordService.deleteById(any()) } returns true

            internalAuthentication {
                post(
                    "/backfill/delete-nullvs-integration-record",
                    body = request,
                    headers = authorizationHeader
                ) { response ->
                    ResponseAssert.assertThat(response).isOK()
                }
            }

            coVerifyOnce { nullvsIntegrationRecordService.deleteById(nullvsIntegrationRecord.id) }

        }

    @Test
    fun `#syncInvoicesTowardsTotvs - should request sync when invoice is already paid`() =
        runBlocking {
            val memberInvoice1 =
                TestModelFactory.buildMemberInvoice(status = InvoiceStatus.PAID).copy(paidAt = LocalDateTime.now())
            val memberInvoice2 = TestModelFactory.buildMemberInvoice()
            val memberInvoiceId1 = memberInvoice1.id
            val memberInvoiceId2 = memberInvoice2.id

            val request = InvoicesSyncRequest(
                ids = listOf(memberInvoiceId1, memberInvoiceId2),
            )

            coEvery { invoicesService.findInvoicesByIds(request.ids) } returns listOf(memberInvoice1, memberInvoice2)
            coEvery { kafkaProducerService.produce(any()) } returns mockk()

            internalAuthentication {
                post("/backfill/sync/invoices", body = request, headers = authorizationHeader) { response ->
                    ResponseAssert.assertThat(response).isOK()

                    val content: List<SyncStatus> = response.bodyAsJson()
                    Assertions.assertThat(content[0].success).isEqualTo(true)
                    Assertions.assertThat(content[1].success).isEqualTo(false)
                }
            }

            coVerifyOnce {
                kafkaProducerService.produce(match { it: NullvsSyncInvoiceRequestEvent ->
                    it.payload.memberInvoice.id == memberInvoiceId1
                })
            }

            coVerifyOnce { invoicesService.findInvoicesByIds(request.ids) }
        }

    @Test
    fun `#bindBeneficiaryWithSubcontract - should request to bind beneficiary a some subcontract`() = runBlocking {
        val memberId1 = RangeUUID.generate()
        val memberId2 = RangeUUID.generate()
        val externalContractId = "3000000021"
        val externalSubcontractId = "0*********"
        val groupCompany = "0004"
        val beneficiary1 = TestModelFactory.buildBeneficiary(memberId = memberId1)
        val beneficiary2 = TestModelFactory.buildBeneficiary(memberId = memberId2)
        val contract =
            TestModelFactory.buildCompanyContract(externalId = externalContractId, groupCompany = groupCompany)
        val subcontract =
            TestModelFactory.buildCompanySubContract(externalId = externalSubcontractId, contractId = contract.id)

        val beneficiaryUpdated1 = beneficiary1.copy(companySubContractId = subcontract.id)
        val beneficiaryUpdated2 = beneficiary2.copy(companySubContractId = subcontract.id)

        val request = BindBeneficiaryToSubcontractRequest(
            list = listOf(
                BindBeneficiaryToSubcontractRequest.BeneficiaryAndContractAndSubcontract(
                    contractNumber = externalContractId,
                    groupCompany = groupCompany,
                    subcontractNumber = externalSubcontractId,
                    memberId = memberId1,
                ),
                BindBeneficiaryToSubcontractRequest.BeneficiaryAndContractAndSubcontract(
                    contractNumber = externalContractId,
                    groupCompany = groupCompany,
                    subcontractNumber = externalSubcontractId,
                    memberId = memberId2,
                )
            )
        )

        coEvery { beneficiaryService.findByMemberIds(listOf(memberId1, memberId2)) } returns listOf(
            beneficiary1,
            beneficiary2,
        )
        coEvery {
            companyContractService.getByExternalIdAndGroupCompany(
                externalContractId,
                groupCompany
            )
        } returns contract
        coEvery { companySubcontractService.findByContractIds(listOf(contract.id)) } returns listOf(subcontract)
        coEvery { beneficiaryService.update(beneficiaryUpdated1) } returns beneficiaryUpdated1
        coEvery { beneficiaryService.update(beneficiaryUpdated2) } returns beneficiaryUpdated2


        internalAuthentication {
            post(
                "/backfill/bind-beneficiary-to-subcontract",
                body = request,
                headers = authorizationHeader
            ) { response ->
                ResponseAssert.assertThat(response).isOK()
            }
        }

        coVerifyOnce { beneficiaryService.findByMemberIds(listOf(memberId1, memberId2)) }
        coVerifyOnce { companyContractService.getByExternalIdAndGroupCompany(any(), any()) }
        coVerifyOnce { companySubcontractService.findByContractIds(listOf(contract.id)) }
        coVerify(exactly = 2) { beneficiaryService.update(any()) }
    }

    fun `#createContractAndSubcontract - should request create contract and subcontract`() =
        runBlocking {
            val contractExternalId = "300000000435"
            val subcontractExternalId = "*********"
            val contractGroupCompany = "0004"
            val request = CreateContractSubcontractForCompanyRequest(
                listOf(
                    ContractSubcontract(
                        contractGroupCompany = contractGroupCompany,
                        contractNumber = contractExternalId,
                        contractStartedAt = "19102023",
                        contractName = "Some company",
                        contractType = "3",
                        contractIsBillingLevel = "0",
                        contractCodeClient = "",
                        contractNature = "20009",
                        contractDueDate = 10,
                        contractMode = "2",
                        contractExchange = "0",
                        contractSendANS = "1",
                        contractRefund = "1",
                        subcontractNumber = subcontractExternalId,
                        subcontractStartedAt = "11102023",
                        subcontractCnpj = "**************",
                        subcontractDescription = "Some company",
                        subcontractShortDescription = "Some company",
                        subcontractIsBillingLevel = "1",
                        subcontractCodeClient = "000008",
                        subcontractNature = "20009",
                        subcontractType = "3",
                        subcontractDueDate = 20,
                        subcontractBillingGroup = "1",
                        subcontractRefund = "1",
                        subcontractReadjustmentMonth = "10",
                        subcontractHasProRata = "1",
                        subcontractSendANS = "1",
                        subcontractDueType = "2",
                        subcontractRetroactiveCharge = "1",
                    )
                )
            )

            val billingAccountableParty = TestModelFactory.buildBillingAccountableParty()
            val record = TestModelFactory.buildNullvsIntegrationRecord(
                internalId = billingAccountableParty.id,
                externalId = "000008"
            )

            val company = TestModelFactory.buildCompany(cnpj = "**************")
            val contract = TestModelFactory.buildCompanyContract(
                externalId = contractExternalId,
                groupCompany = contractGroupCompany,
            )
            val subcontract = TestModelFactory.buildCompanySubContract(externalId = subcontractExternalId)
            val companyWithContract = company.copy(contractIds = listOf(contract.id))
            val contractRecord = TestModelFactory.buildNullvsIntegrationRecord(
                internalId = contract.id,
                internalModelName = InternalModelType.CONTRACT,
                externalId = contract.externalId!!,
                externalModelName = ExternalModelType.CONTRACT,
            )
            val subcontractRecord = TestModelFactory.buildNullvsIntegrationRecord(
                internalId = subcontract.id,
                internalModelName = InternalModelType.SUBCONTRACT,
                externalId = subcontract.externalId!!,
                externalModelName = ExternalModelType.SUBCONTRACT,
            )

            coEvery {
                nullvsIntegrationRecordService.findByExternalIdsAndModel(
                    listOf("000008"),
                    ExternalModelType.CLIENT,
                )
            } returns listOf(record)

            coEvery {
                billingAccountablePartyService.findById(listOf(billingAccountableParty.id))
            } returns listOf(billingAccountableParty)

            coEvery { companyService.findByCnpjs(listOf("**************")) } returns listOf(company)

            coEvery {
                companyContractService.add(match { it.externalId == contractExternalId && it.groupCompany == contractGroupCompany })
            } returns contract

            coEvery {
                nullvsIntegrationRecordService.add(match {
                    it.externalId == contractExternalId && it.internalId == contract.id
                })
            } returns contractRecord

            coEvery {
                companyService.update(match { it.id == company.id && it.contractIds == listOf(contract.id) })
            } returns companyWithContract


            coEvery {
                companySubcontractService.add(match {
                    it.externalId == subcontractExternalId && it.contractId == contract.id && it.companyId == company.id
                })
            } returns subcontract

            coEvery {
                nullvsIntegrationRecordService.add(match {
                    it.externalId == subcontractExternalId && it.internalId == subcontract.id
                })
            } returns subcontractRecord

            internalAuthentication {
                post(
                    "/backfill/create-contract-subcontract",
                    body = request,
                    headers = authorizationHeader
                ) { response ->
                    ResponseAssert.assertThat(response).isOK()

                    val content: List<Pair<CompanyContract, CompanySubContract>> = response.bodyAsJson()
                }
            }
        }

    @Test
    fun `#createContractAndSubcontract - should request create contract and subcontract even when already created`() =
        runBlocking {
            val contractExternalId = "300000000435"
            val subcontractExternalId = "*********"
            val contractGroupCompany = "0004"
            val request = CreateContractSubcontractForCompanyRequest(
                listOf(
                    ContractSubcontract(
                        contractGroupCompany = contractGroupCompany,
                        contractNumber = contractExternalId,
                        contractStartedAt = "19102023",
                        contractName = "Some company",
                        contractType = "3",
                        contractIsBillingLevel = "0",
                        contractCodeClient = "",
                        contractNature = "20009",
                        contractDueDate = 10,
                        contractMode = "2",
                        contractExchange = "0",
                        contractSendANS = "1",
                        contractRefund = "1",
                        subcontractNumber = subcontractExternalId,
                        subcontractStartedAt = "11102023",
                        subcontractCnpj = "**************",
                        subcontractDescription = "Some company",
                        subcontractShortDescription = "Some company",
                        subcontractIsBillingLevel = "1",
                        subcontractCodeClient = "000008",
                        subcontractNature = "20009",
                        subcontractType = "3",
                        subcontractDueDate = 20,
                        subcontractBillingGroup = "1",
                        subcontractRefund = "1",
                        subcontractReadjustmentMonth = "10",
                        subcontractHasProRata = "1",
                        subcontractSendANS = "1",
                        subcontractDueType = "2",
                        subcontractRetroactiveCharge = "1",
                    )
                )
            )

            val billingAccountableParty = TestModelFactory.buildBillingAccountableParty()
            val record = TestModelFactory.buildNullvsIntegrationRecord(
                internalId = billingAccountableParty.id,
                externalId = "000008"
            )

            val company = TestModelFactory.buildCompany(cnpj = "**************")
            val contract = TestModelFactory.buildCompanyContract(
                externalId = contractExternalId,
                groupCompany = contractGroupCompany,
            )
            val subcontract = TestModelFactory.buildCompanySubContract(externalId = subcontractExternalId)
            val companyWithContract = company.copy(contractIds = listOf(contract.id))
            val contractRecord = TestModelFactory.buildNullvsIntegrationRecord(
                internalId = contract.id,
                internalModelName = InternalModelType.CONTRACT,
                externalId = contract.externalId!!,
                externalModelName = ExternalModelType.CONTRACT,
            )
            val subcontractRecord = TestModelFactory.buildNullvsIntegrationRecord(
                internalId = subcontract.id,
                internalModelName = InternalModelType.SUBCONTRACT,
                externalId = subcontract.externalId!!,
                externalModelName = ExternalModelType.SUBCONTRACT,
            )

            coEvery {
                nullvsIntegrationRecordService.findByExternalIdsAndModel(
                    listOf("000008"),
                    ExternalModelType.CLIENT,
                )
            } returns listOf(record)

            coEvery {
                billingAccountablePartyService.findById(listOf(billingAccountableParty.id))
            } returns listOf(billingAccountableParty)

            coEvery { companyService.findByCnpjs(listOf("**************")) } returns listOf(company)

            coEvery {
                companyContractService.add(
                    match { it.externalId == contractExternalId && it.groupCompany == contractGroupCompany },
                    false
                )
            } returns DuplicatedItemException("")

            coEvery {
                companyContractService.getByExternalIdAndGroupCompany(contract.externalId!!, contract.groupCompany!!)
            } returns contract

            coEvery {
                companyContractService.update(
                    match { it.externalId == contractExternalId && it.groupCompany == contractGroupCompany },
                    false
                )
            } returns contract

            coEvery {
                nullvsIntegrationRecordService.add(match {
                    it.externalId == contractExternalId && it.internalId == contract.id
                })
            } returns contractRecord

            coEvery {
                companyService.update(match { it.id == company.id && it.contractIds == listOf(contract.id) })
            } returns companyWithContract

            coEvery {
                companySubcontractService.add(match {
                    it.externalId == subcontractExternalId && it.contractId == contract.id && it.companyId == company.id
                }, false)
            } returns DuplicatedItemException("")

            coEvery {
                companySubcontractService.update(match {
                    it.externalId == subcontractExternalId &&
                            it.contractId == contract.id &&
                            it.companyId == company.id
                }, false)
            } returns subcontract

            coEvery {
                companySubcontractService.findByContractIdAndExternalId(
                    contract.id,
                    subcontract.externalId!!,
                )
            } returns subcontract

            coEvery {
                nullvsIntegrationRecordService.add(match {
                    it.externalId == subcontractExternalId && it.internalId == subcontract.id
                })
            } returns subcontractRecord

            internalAuthentication {
                post(
                    "/backfill/create-contract-subcontract",
                    body = request,
                    headers = authorizationHeader
                ) { response ->
                    ResponseAssert.assertThat(response).isOK()

                    val content: List<Pair<CompanyContract, CompanySubContract>> = response.bodyAsJson()
                }
            }
        }

    @Test
    fun `#cancelWaitingLogs - should execute request to cancel waiting logs`() =
        runBlocking {

            val request = CancelWaitingLogsRequest(
                ids = listOf(UUID.randomUUID(), UUID.randomUUID()),
                internalModelName = InternalModelType.MEMBER
            )

            coEvery {
                nullsIntegrationLogReprocessService.cancelWaitingLogs(request)
            } returns Unit

            internalAuthentication {
                post(
                    "/backfill/reprocess-queue/cancel-waiting-logs",
                    body = request,
                    headers = authorizationHeader
                ) { response ->
                    ResponseAssert.assertThat(response).isOK()
                }
            }

        }

    @Test
    fun `#cancelAllWaitingLogs - should execute request to cancel waiting logs`() =
        runBlocking {

            coEvery {
                nullsIntegrationLogReprocessService.cancelAllWaitingLogs()
            } returns Unit

            internalAuthentication {
                post(
                    "/backfill/reprocess-queue/cancel-waiting-logs/all",
                    headers = authorizationHeader
                ) { response ->
                    ResponseAssert.assertThat(response).isOK()
                }
            }

        }

    @Test
    fun `#syncInvoiceItemByTypeFromStartMonth - should sync member invoice items`() = runBlocking {
        val request = SyncInvoiceItemsFromStartMonthRequest(
            type = "member",
            startMonth = "2021-01-01"
        )

        coEvery {
            invoiceItemService.syncMemberInvoiceItemsFromStartMonth(
                LocalDate.parse(request.startMonth)
            )
        } returns emptyList()

        internalAuthentication {
            post(
                "/backfill/sync/invoice-items",
                body = request,
                headers = authorizationHeader
            ) { response ->
                ResponseAssert.assertThat(response).isOK()
            }
        }
    }

    @Test
    fun `#syncInvoiceItemByTypeFromStartMonth - should sync subcontract invoice items`() = runBlocking {
        val request = SyncInvoiceItemsFromStartMonthRequest(
            type = "subcontract",
            startMonth = "2021-01-01"
        )

        coEvery {
            invoiceItemService.syncSubcontractInvoiceItemsFromStartMonth(
                LocalDate.parse(request.startMonth)
            )
        } returns emptyList()

        internalAuthentication {
            post(
                "/backfill/sync/invoice-items",
                body = request,
                headers = authorizationHeader
            ) { response ->
                ResponseAssert.assertThat(response).isOK()
            }
        }
    }

    @Test
    fun `#reprocessFailedOrPendingInvoiceItems - should reprocess invoice items by ids`() = runBlocking {
        val request = FailedOrPendingInvoiceItemsRequest(
            ids = listOf(UUID.randomUUID())
        )

        coEvery {
            invoiceItemService.reprocessFailedOrPendingInvoiceItemsByIds(request.ids!!, false)
        } returns emptyList()

        internalAuthentication {
            post(
                "/backfill/reprocess-failed-invoice-items",
                body = request,
                headers = authorizationHeader
            ) { response ->
                ResponseAssert.assertThat(response).isOK()
            }
        }
    }

    @Test
    fun `#reprocessFailedOrPendingInvoiceItems - should reprocess invoice items by member type and start month`() = runBlocking {
        val request = FailedOrPendingInvoiceItemsRequest(
            type = "member",
            startMonth = "2021-01-01"
        )

        coEvery {
            invoiceItemService.reprocessFailedOrPendingMemberInvoiceItemsFromStartMonth(
                LocalDate.parse(request.startMonth!!)
            )
        } returns emptyList()

        internalAuthentication {
            post(
                "/backfill/reprocess-failed-invoice-items",
                body = request,
                headers = authorizationHeader
            ) { response ->
                ResponseAssert.assertThat(response).isOK()
            }
        }
    }

    @Test
    fun `#reprocessFailedOrPendingInvoiceItems - should reprocess invoice items by subcontract type and start month`() = runBlocking {
        val request = FailedOrPendingInvoiceItemsRequest(
            type = "subcontract",
            startMonth = "2021-01-01"
        )

        coEvery {
            invoiceItemService.reprocessFailedOrPendingSubcontractInvoiceItemsFromStartMonth(
                LocalDate.parse(request.startMonth!!)
            )
        } returns emptyList()

        internalAuthentication {
            post(
                "/backfill/reprocess-failed-invoice-items",
                body = request,
                headers = authorizationHeader
            ) { response ->
                ResponseAssert.assertThat(response).isOK()
            }
        }
    }

}
