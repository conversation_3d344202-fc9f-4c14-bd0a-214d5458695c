package br.com.alice.channel.assistance.services.internal

import br.com.alice.channel.assistance.services.internal.firestore.VirtualClinicQueueFirestoreService
import br.com.alice.channel.core.FirestoreTransactionalTestHelper
import br.com.alice.channel.core.context.FirestoreContextUsage
import br.com.alice.channel.core.mockTimestamp
import br.com.alice.channel.core.services.internal.ChannelNotificationService
import br.com.alice.channel.core.services.internal.firestore.ChannelFirestoreService
import br.com.alice.channel.core.services.internal.firestore.MessageFirestoreService
import br.com.alice.channel.core.services.internal.firestore.StaffFirestoreService
import br.com.alice.channel.extensions.toTimestamp
import br.com.alice.channel.models.ChannelDocument
import br.com.alice.channel.models.ChannelSegment
import br.com.alice.channel.models.MessageDocument
import br.com.alice.channel.models.MessageType
import br.com.alice.channel.models.QuestionContent
import br.com.alice.channel.models.StaffDocument
import br.com.alice.channel.models.VirtualClinicQueueDocument
import br.com.alice.common.core.Role
import br.com.alice.common.core.exceptions.InvalidArgumentException
import br.com.alice.common.data.dsl.matchers.ResultAssert.Companion.assertThat
import br.com.alice.common.featureflag.withFeatureFlag
import br.com.alice.common.featureflag.withFeatureFlags
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.common.helpers.verifyOnce
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.ChannelChangeAction.VIRTUAL_CLINIC_NEXT_OF_QUEUE_MESSAGE_SENT
import br.com.alice.data.layer.models.ChannelChangeAction.VIRTUAL_CLINIC_QUEUED_MESSAGE_SENT
import br.com.alice.data.layer.models.ChannelChangeAction.VIRTUAL_CLINIC_READY_TO_START_MESSAGE_SENT
import br.com.alice.data.layer.models.FeatureNamespace
import br.com.alice.data.layer.models.HealthFormQuestionType
import br.com.alice.data.layer.models.VirtualClinicStatusControl
import br.com.alice.data.layer.models.VirtualClinicStatusControl.MISSING_MEMBER_INTERACTION
import br.com.alice.data.layer.models.VirtualClinicStatusControl.NEXT_IN_QUEUE_MESSAGE_SENT
import br.com.alice.data.layer.models.VirtualClinicStatusControl.QUEUED
import br.com.alice.data.layer.models.VirtualClinicStatusControl.VIDEO_CALL_REJECTED
import br.com.alice.data.layer.models.VirtualClinicStatusControl.WAITING_TIME_MESSAGE_SENT
import br.com.alice.person.client.PersonService
import br.com.alice.questionnaire.models.HealthFormQuestionOptionResponse
import br.com.alice.questionnaire.models.QuestionnaireQuestionInputResponse
import br.com.alice.staff.client.StaffService
import com.github.kittinunf.result.success
import com.google.cloud.Timestamp
import com.google.cloud.firestore.CollectionReference
import com.google.cloud.firestore.Query
import io.mockk.Runs
import io.mockk.called
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.confirmVerified
import io.mockk.every
import io.mockk.just
import io.mockk.mockk
import io.mockk.slot
import io.mockk.verify
import kotlinx.coroutines.runBlocking
import java.time.LocalDateTime
import kotlin.test.AfterTest
import kotlin.test.Test

class VirtualClinicMessagesServiceTest: FirestoreTransactionalTestHelper() {

    private val virtualClinicQueueFirestoreService: VirtualClinicQueueFirestoreService = mockk()
    private val messageFirestoreService: MessageFirestoreService = mockk()
    private val staffFirestoreService: StaffFirestoreService = mockk()
    private val channelNotificationService: ChannelNotificationService = mockk()
    private val staffService: StaffService = mockk()
    private val personService: PersonService = mockk()
    private val channelFirestoreService: ChannelFirestoreService = mockk()

    private val virtualClinicMessagesService = VirtualClinicMessagesService(
        virtualClinicQueueFirestoreService,
        messageFirestoreService,
        staffFirestoreService,
        channelNotificationService,
        staffService,
        personService,
        channelFirestoreService
    )

    private val timestamp = Timestamp.now()
    private val staffDocument = StaffDocument(role = Role.VIRTUAL_CLINIC_PHYSICIAN)
    private val staff = TestModelFactory.buildStaff()
    private val person = TestModelFactory.buildPerson()
    private val message = MessageDocument(
        userId = "",
        content = "",
        type = MessageType.TEXT,
        createdAt = timestamp
    )

    private val virtualClinicQueueDocument =
        VirtualClinicQueueDocument(
            id = channelId,
            createdAt = Timestamp.now(),
            queuedAt = Timestamp.now()
        )

    @AfterTest
    fun confirmMocks() = confirmVerified(
        virtualClinicQueueFirestoreService,
        messageFirestoreService,
        staffFirestoreService,
        channelNotificationService,
        channelFirestoreService
    )

    @OptIn(FirestoreContextUsage::class)
    @Test
    fun `#sendQueuedMessage return message id and send message when item is first from queue`() =
        mockChannelStatusControl(QUEUED) {
            mockTimestamp(timestamp) {
                val message = message.copy(content = "message_for_top_of_queue")
                val segment = ChannelSegment.PEDIATRIC

                withFeatureFlag(
                    FeatureNamespace.CHANNELS,
                    "message_for_top_of_queue",
                    "message_for_top_of_queue"
                ) {

                    coEvery {
                        virtualClinicQueueFirestoreService.getQueue(segment)
                    } returns listOf(virtualClinicQueueDocument).success()
                    coEvery { messageFirestoreService.add(channelId, message) } returns messageId.success()
                    coEvery {
                        channelNotificationService.notify(
                            channelId,
                            VIRTUAL_CLINIC_QUEUED_MESSAGE_SENT
                        )
                    } just Runs

                    val result = virtualClinicMessagesService.sendQueuedMessage(channelId, segment)
                    assertThat(result).isSuccessWithData(messageId)

                    coVerifyOnce { staffService wasNot called }
                    coVerifyOnce { virtualClinicQueueFirestoreService.getQueue(any()) }
                    coVerifyOnce { messageFirestoreService.add(any(), any()) }
                    coVerifyOnce { channelNotificationService.notify(any<String>(), any()) }
                    coVerify { staffFirestoreService wasNot called }
                }
            }
        }

    @OptIn(FirestoreContextUsage::class)
    @Test
    fun `#sendQueuedMessage return error when message not registered in flag`() = mockChannelStatusControl(QUEUED) {
        val segment = ChannelSegment.ADULT

        coEvery {
            virtualClinicQueueFirestoreService.getQueue(segment)
        } returns listOf(virtualClinicQueueDocument).success()

        val result = virtualClinicMessagesService.sendQueuedMessage(channelId, segment)
        assertThat(result).isFailureOfType(InvalidArgumentException::class)

        coVerifyOnce { staffService wasNot called }
        coVerifyOnce { virtualClinicQueueFirestoreService.getQueue(any()) }
        coVerify { messageFirestoreService wasNot called }
        coVerify { staffFirestoreService wasNot called }
    }

    @OptIn(FirestoreContextUsage::class)
    @Test
    fun `#sendQueuedMessage return error when channel is not queued`() = mockChannelStatusControl(QUEUED) {
        val segment = ChannelSegment.ADULT

        coEvery {
            virtualClinicQueueFirestoreService.getQueue(segment)
        } returns emptyList<VirtualClinicQueueDocument>().success()

        val result = virtualClinicMessagesService.sendQueuedMessage(channelId, segment)
        assertThat(result).isFailureOfType(InvalidArgumentException::class)

        coVerifyOnce { staffService wasNot called }
        coVerifyOnce { virtualClinicQueueFirestoreService.getQueue(any()) }
        coVerify { messageFirestoreService wasNot called }
        coVerify { staffFirestoreService wasNot called }
    }

    @OptIn(FirestoreContextUsage::class)
    @Test
    fun `#sendQueuedMessage return message id and send message when item not is first or second from queue`() =
        mockChannelStatusControl(QUEUED) {
            mockTimestamp(timestamp) {
                val message = message.copy(content = "message_for_queued 3 15")
                val segment = ChannelSegment.ADULT

                val virtualClinicQueueDocumentExtra = virtualClinicQueueDocument.copy(
                    id = "extra",
                    queuedAt = LocalDateTime.now().minusMinutes(10).toTimestamp()
                )

                withFeatureFlag(
                    FeatureNamespace.CHANNELS,
                    "message_for_queued",
                    "message_for_queued @total_queued @time_to_wait"
                ) {

                    val functionSlot = slot<suspend (CollectionReference) -> Query>()

                    every { collectionReference.whereEqualTo("status", "AVAILABLE") } returns collectionReference
                    every {
                        collectionReference.whereEqualTo(
                            "role",
                            "VIRTUAL_CLINIC_PHYSICIAN"
                        )
                    } returns collectionReference

                    coEvery {
                        virtualClinicQueueFirestoreService.getQueue(segment)
                    } returns listOf(
                        virtualClinicQueueDocument,
                        virtualClinicQueueDocumentExtra,
                        virtualClinicQueueDocumentExtra
                    ).success()
                    coEvery { staffFirestoreService.find(capture(functionSlot)) } returns listOf(
                        staffDocument,
                        staffDocument
                    ).success()
                    coEvery { messageFirestoreService.add(channelId, message) } returns messageId.success()
                    coEvery {
                        channelNotificationService.notify(
                            channelId,
                            VIRTUAL_CLINIC_QUEUED_MESSAGE_SENT
                        )
                    } just Runs

                    val result = virtualClinicMessagesService.sendQueuedMessage(channelId, segment)
                    assertThat(result).isSuccessWithData(messageId)

                    functionSlot.captured.invoke(collectionReference)

                    verify(exactly = 2) { collectionReference.whereEqualTo(any<String>(), any()) }
                    coVerifyOnce { staffService wasNot called }
                    coVerifyOnce { virtualClinicQueueFirestoreService.getQueue(any()) }
                    coVerifyOnce { messageFirestoreService.add(any(), any()) }
                    coVerifyOnce { staffFirestoreService.find(any()) }
                    coVerifyOnce { channelNotificationService.notify(any<String>(), any()) }
                }
            }
        }

    @OptIn(FirestoreContextUsage::class)
    @Test
    fun `#sendQueuedMessage return message id and send message when item not is first from queue and no staff online`() =
        mockChannelStatusControl(QUEUED) {
            mockTimestamp(timestamp) {
                val message = message.copy(content = "message_for_queued 3 30")
                val segment = ChannelSegment.ADULT

                val virtualClinicQueueDocumentExtra = virtualClinicQueueDocument.copy(
                    id = "extra",
                    queuedAt = LocalDateTime.now().minusMinutes(10).toTimestamp()
                )

                withFeatureFlag(
                    FeatureNamespace.CHANNELS,
                    "message_for_queued",
                    "message_for_queued @total_queued @time_to_wait"
                ) {

                    val functionSlot = slot<suspend (CollectionReference) -> Query>()

                    every { collectionReference.whereEqualTo("status", "AVAILABLE") } returns collectionReference
                    every {
                        collectionReference.whereEqualTo(
                            "role",
                            "VIRTUAL_CLINIC_PHYSICIAN"
                        )
                    } returns collectionReference

                    coEvery {
                        virtualClinicQueueFirestoreService.getQueue(segment)
                    } returns listOf(
                        virtualClinicQueueDocument,
                        virtualClinicQueueDocumentExtra,
                        virtualClinicQueueDocumentExtra
                    ).success()
                    coEvery { staffFirestoreService.find(capture(functionSlot)) } returns emptyList<StaffDocument>().success()
                    coEvery { messageFirestoreService.add(channelId, message) } returns messageId.success()
                    coEvery {
                        channelNotificationService.notify(
                            channelId,
                            VIRTUAL_CLINIC_QUEUED_MESSAGE_SENT
                        )
                    } just Runs

                    val result = virtualClinicMessagesService.sendQueuedMessage(channelId, segment)
                    assertThat(result).isSuccessWithData(messageId)

                    functionSlot.captured.invoke(collectionReference)

                    verify(exactly = 2) { collectionReference.whereEqualTo(any<String>(), any()) }
                    coVerifyOnce { staffService wasNot called }
                    coVerifyOnce { virtualClinicQueueFirestoreService.getQueue(any()) }
                    coVerifyOnce { messageFirestoreService.add(any(), any()) }
                    coVerifyOnce { staffFirestoreService.find(any()) }
                    coVerifyOnce { channelNotificationService.notify(any<String>(), any()) }
                }
            }
        }

    @OptIn(FirestoreContextUsage::class)
    @Test
    fun `#sendQueuedMessage return message id and send message and force time to wait equals base time when calculation result is less than base time`() =
        mockChannelStatusControl(QUEUED) {
            mockTimestamp(timestamp) {
                val message = message.copy(content = "message_for_queued 3 10")
                val segment = ChannelSegment.ADULT

                val virtualClinicQueueDocumentExtra = virtualClinicQueueDocument.copy(
                    id = "extra",
                    queuedAt = LocalDateTime.now().minusMinutes(10).toTimestamp()
                )

                withFeatureFlag(
                    FeatureNamespace.CHANNELS,
                    "message_for_queued",
                    "message_for_queued @total_queued @time_to_wait"
                ) {

                    val functionSlot = slot<suspend (CollectionReference) -> Query>()

                    every { collectionReference.whereEqualTo("status", "AVAILABLE") } returns collectionReference
                    every {
                        collectionReference.whereEqualTo(
                            "role",
                            "VIRTUAL_CLINIC_PHYSICIAN"
                        )
                    } returns collectionReference

                    coEvery {
                        staffService.get(staff.id)
                    } returns staff.success()

                    coEvery {
                        virtualClinicQueueFirestoreService.getQueue(segment)
                    } returns listOf(
                        virtualClinicQueueDocument,
                        virtualClinicQueueDocumentExtra,
                        virtualClinicQueueDocumentExtra
                    ).success()
                    coEvery {
                        staffFirestoreService.find(capture(functionSlot))
                    } returns listOf(
                        staffDocument,
                        staffDocument,
                        staffDocument,
                        staffDocument,
                        staffDocument
                    ).success()
                    coEvery { messageFirestoreService.add(channelId, message) } returns messageId.success()
                    coEvery {
                        channelNotificationService.notify(
                            channelId,
                            VIRTUAL_CLINIC_QUEUED_MESSAGE_SENT
                        )
                    } just Runs

                    val result = virtualClinicMessagesService.sendQueuedMessage(channelId, segment)
                    assertThat(result).isSuccessWithData(messageId)

                    functionSlot.captured.invoke(collectionReference)

                    verify(exactly = 2) { collectionReference.whereEqualTo(any<String>(), any()) }
                    coVerifyOnce { staffService wasNot called }
                    coVerifyOnce { virtualClinicQueueFirestoreService.getQueue(any()) }
                    coVerifyOnce { staffFirestoreService.find(any()) }
                    coVerifyOnce { messageFirestoreService.add(any(), any()) }
                    coVerifyOnce { channelNotificationService.notify(any<String>(), any()) }
                }
            }
        }

    @OptIn(FirestoreContextUsage::class)
    @Test
    fun `#sendQueuedMessage return message id and send message when item not is first or second from child queue`() =
        mockChannelStatusControl(QUEUED) {
            mockTimestamp(timestamp) {
                val message = message.copy(content = "message_for_queued 3 30")
                val segment = ChannelSegment.PEDIATRIC

                val virtualClinicQueueDocumentExtra = virtualClinicQueueDocument.copy(
                    id = "extra",
                    queuedAt = LocalDateTime.now().minusMinutes(10).toTimestamp()
                )

                withFeatureFlag(
                    FeatureNamespace.CHANNELS,
                    "message_for_queued",
                    "message_for_queued @total_queued @time_to_wait"
                ) {

                    val functionSlot = slot<suspend (CollectionReference) -> Query>()

                    every { collectionReference.whereEqualTo("status", "AVAILABLE") } returns collectionReference
                    every {
                        collectionReference.whereEqualTo(
                            "role",
                            "VIRTUAL_CLINIC_PEDIATRIC_PHYSICIAN"
                        )
                    } returns collectionReference

                    coEvery {
                        virtualClinicQueueFirestoreService.getQueue(segment)
                    } returns listOf(
                        virtualClinicQueueDocument,
                        virtualClinicQueueDocumentExtra,
                        virtualClinicQueueDocumentExtra
                    ).success()
                    coEvery { staffFirestoreService.find(capture(functionSlot)) } returns listOf(staffDocument).success()
                    coEvery { messageFirestoreService.add(channelId, message) } returns messageId.success()
                    coEvery {
                        channelNotificationService.notify(
                            channelId,
                            VIRTUAL_CLINIC_QUEUED_MESSAGE_SENT
                        )
                    } just Runs

                    val result = virtualClinicMessagesService.sendQueuedMessage(channelId, segment)
                    assertThat(result).isSuccessWithData(messageId)

                    functionSlot.captured.invoke(collectionReference)

                    verify(exactly = 2) { collectionReference.whereEqualTo(any<String>(), any()) }
                    coVerifyOnce { staffService wasNot called }
                    coVerifyOnce { virtualClinicQueueFirestoreService.getQueue(any()) }
                    coVerifyOnce { messageFirestoreService.add(any(), any()) }
                    coVerifyOnce { staffFirestoreService.find(any()) }
                    coVerifyOnce { channelNotificationService.notify(any<String>(), any()) }
                }
            }
        }

    @OptIn(FirestoreContextUsage::class)
    @Test
    fun `#sendQueuedMessage return channel id when status control is invalid`() =
        mockChannelStatusControl(WAITING_TIME_MESSAGE_SENT) {

            val result = virtualClinicMessagesService.sendQueuedMessage(channelId, ChannelSegment.ADULT)
            assertThat(result).isSuccessWithData(channelId)

            coVerify { virtualClinicQueueFirestoreService wasNot called }
            coVerify { messageFirestoreService wasNot called }
            coVerify { channelNotificationService wasNot called }
            coVerify { staffFirestoreService wasNot called }
        }

    @OptIn(FirestoreContextUsage::class)
    @Test
    fun `#sendMessageForNextInQueue returns message id and send message`() =
        mockChannelStatusControl(WAITING_TIME_MESSAGE_SENT) {
            mockTimestamp(timestamp) {
                val message = message.copy(content = "message_for_next_in_queue")

                withFeatureFlag(
                    FeatureNamespace.CHANNELS,
                    "message_for_next_in_queue",
                    "message_for_next_in_queue"
                ) {
                    coEvery { messageFirestoreService.add(channelId, message) } returns messageId.success()
                    coEvery {
                        channelNotificationService.notify(
                            channelId,
                            VIRTUAL_CLINIC_NEXT_OF_QUEUE_MESSAGE_SENT
                        )
                    } just Runs

                    val result = virtualClinicMessagesService.sendMessageForNextInQueue(channelId)
                    assertThat(result).isSuccessWithData(messageId)

                    coVerifyOnce { messageFirestoreService.add(any(), any()) }
                    coVerifyOnce { channelNotificationService.notify(any<String>(), any()) }
                    coVerify { virtualClinicQueueFirestoreService wasNot called }
                    coVerify { staffFirestoreService wasNot called }
                }
            }
        }

    @OptIn(FirestoreContextUsage::class)
    @Test
    fun `#sendMessageForNextInQueue return channel id when status control is invalid`() =
        mockChannelStatusControl(QUEUED) {

            val result = virtualClinicMessagesService.sendMessageForNextInQueue(channelId)
            assertThat(result).isSuccessWithData(channelId)

            coVerify { virtualClinicQueueFirestoreService wasNot called }
            coVerify { messageFirestoreService wasNot called }
            coVerify { channelNotificationService wasNot called }
            coVerify { staffFirestoreService wasNot called }
        }

    @OptIn(FirestoreContextUsage::class)
    @Test
    fun `#sendQuestionToStartVideoCall returns message id and send message`() =
        mockChannelStatusControl(WAITING_TIME_MESSAGE_SENT) {
            mockTimestamp(timestamp) {
                val content = "message_question_to_start_video_call ${person.nickName} ${staff.fullName}"
                val message = message.copy(
                    content = content,
                    type = MessageType.QUESTION,
                    question = QuestionContent(
                        question = content,
                        input = QuestionnaireQuestionInputResponse(
                            action = "",
                            displayAttributes = null,
                            options = listOf(
                                HealthFormQuestionOptionResponse(
                                    value = "1",
                                    label = "label_1"
                                ),
                                HealthFormQuestionOptionResponse(
                                    value = "-1",
                                    label = "label_-1"
                                ),
                                HealthFormQuestionOptionResponse(
                                    value = "-2",
                                    label = "label_-2"
                                )
                            ),
                            type = HealthFormQuestionType.OPTION_BUTTONS
                        ),
                        required = true
                    )
                )

                withFeatureFlags(
                    FeatureNamespace.CHANNELS,
                    mapOf(
                        "message_question_to_start_video_call" to "message_question_to_start_video_call @nickname @staff_name",
                        "message_question_options_to_start_video_call" to "[{\"value\":\"1\",\"label\":\"label_1\"},{\"value\":\"-1\",\"label\":\"label_-1\"},{\"value\":\"-2\",\"label\":\"label_-2\"}]"
                    )
                ) {
                    coEvery { staffService.get(staff.id) } returns staff.success()
                    coEvery { personService.get(person.id) } returns person.success()
                    coEvery { messageFirestoreService.add(channelId, message) } returns messageId.success()
                    coEvery {
                        channelNotificationService.notify(
                            channelId,
                            VIRTUAL_CLINIC_READY_TO_START_MESSAGE_SENT
                        )
                    } just Runs

                    val result = virtualClinicMessagesService.sendQuestionToStartVideoCall(
                        channelId,
                        staff.id,
                        person.id
                    )
                    assertThat(result).isSuccessWithData(messageId)

                    coVerifyOnce { staffService.get(any()) }
                    coVerifyOnce { personService.get(any()) }
                    coVerifyOnce { messageFirestoreService.add(any(), any()) }
                    coVerifyOnce { channelNotificationService.notify(any<String>(), any()) }
                    coVerify { staffFirestoreService wasNot called }
                    coVerify { virtualClinicQueueFirestoreService wasNot called }
                }
            }
        }

    @OptIn(FirestoreContextUsage::class)
    @Test
    fun `#sendQuestionToStartVideoCall returns message id and send message when status is NEXT_IN_QUEUE_MESSAGE_SENT`() =
        mockChannelStatusControl(NEXT_IN_QUEUE_MESSAGE_SENT) {
            mockTimestamp(timestamp) {
                val content = "message_question_to_start_video_call ${person.nickName} ${staff.fullName}"
                val message = message.copy(
                    content = content,
                    type = MessageType.QUESTION,
                    question = QuestionContent(
                        question = content,
                        input = QuestionnaireQuestionInputResponse(
                            action = "",
                            displayAttributes = null,
                            options = listOf(
                                HealthFormQuestionOptionResponse(
                                    value = "1",
                                    label = "label_1"
                                ),
                                HealthFormQuestionOptionResponse(
                                    value = "-1",
                                    label = "label_-1"
                                ),
                                HealthFormQuestionOptionResponse(
                                    value = "-2",
                                    label = "label_-2"
                                )
                            ),
                            type = HealthFormQuestionType.OPTION_BUTTONS
                        ),
                        required = true
                    )
                )

                withFeatureFlags(
                    FeatureNamespace.CHANNELS,
                    mapOf(
                        "message_question_to_start_video_call" to "message_question_to_start_video_call @nickname @staff_name",
                        "message_question_options_to_start_video_call" to "[{\"value\":\"1\",\"label\":\"label_1\"},{\"value\":\"-1\",\"label\":\"label_-1\"},{\"value\":\"-2\",\"label\":\"label_-2\"}]"
                    )
                ) {
                    coEvery { staffService.get(staff.id) } returns staff.success()
                    coEvery { personService.get(person.id) } returns person.success()
                    coEvery { messageFirestoreService.add(channelId, message) } returns messageId.success()
                    coEvery {
                        channelNotificationService.notify(
                            channelId,
                            VIRTUAL_CLINIC_READY_TO_START_MESSAGE_SENT
                        )
                    } just Runs

                    val result = virtualClinicMessagesService.sendQuestionToStartVideoCall(
                        channelId,
                        staff.id,
                        person.id
                    )
                    assertThat(result).isSuccessWithData(messageId)

                    coVerifyOnce { staffService.get(any()) }
                    coVerifyOnce { personService.get(any()) }
                    coVerifyOnce { messageFirestoreService.add(any(), any()) }
                    coVerifyOnce { channelNotificationService.notify(any<String>(), any()) }
                    coVerify { staffFirestoreService wasNot called }
                    coVerify { virtualClinicQueueFirestoreService wasNot called }
                }
            }
        }

    @OptIn(FirestoreContextUsage::class)
    @Test
    fun `#sendQuestionToStartVideoCall returns error when option is invalid`() =
        mockChannelStatusControl(WAITING_TIME_MESSAGE_SENT) {
            mockTimestamp(timestamp) {
                withFeatureFlags(
                    FeatureNamespace.CHANNELS,
                    mapOf(
                        "message_question_to_start_video_call" to "message_question_to_start_video_call @nickname @staff_name",
                        "message_question_options_to_start_video_call" to "[{\"value\":1},{\"label\":\"label_2\"}]"
                    )
                ) {

                    coEvery { staffService.get(staff.id) } returns staff.success()
                    coEvery { personService.get(person.id) } returns person.success()

                    val result = virtualClinicMessagesService.sendQuestionToStartVideoCall(
                        channelId,
                        staff.id,
                        person.id
                    )
                    assertThat(result).isFailureOfType(InvalidArgumentException::class)

                    coVerifyOnce { staffService.get(any()) }
                    coVerifyOnce { personService.get(any()) }
                    coVerify { messageFirestoreService wasNot called }
                    coVerify { channelNotificationService wasNot called }
                    coVerify { staffFirestoreService wasNot called }
                    coVerify { virtualClinicQueueFirestoreService wasNot called }
                }
            }
        }

    @OptIn(FirestoreContextUsage::class)
    @Test
    fun `#sendQuestionToStartVideoCall return channel id when status control is invalid`() =
        mockChannelStatusControl(QUEUED) {

            val result = virtualClinicMessagesService.sendQuestionToStartVideoCall(
                channelId,
                staff.id,
                person.id
            )
            assertThat(result).isSuccessWithData(channelId)

            coVerify { virtualClinicQueueFirestoreService wasNot called }
            coVerify { messageFirestoreService wasNot called }
            coVerify { channelNotificationService wasNot called }
            coVerify { staffFirestoreService wasNot called }
        }

    @OptIn(FirestoreContextUsage::class)
    @Test
    fun `#sendMessageArchiveByMemberVideoCallRejected return message id and send message`() =
        mockChannelStatusControl(VIDEO_CALL_REJECTED) {
            mockTimestamp(timestamp) {
                val message = message.copy(content = "message_archive_by_member_video_call_rejected ${person.nickName}")

                withFeatureFlag(
                    FeatureNamespace.CHANNELS,
                    "message_archive_by_member_video_call_rejected",
                    "message_archive_by_member_video_call_rejected @nickname"
                ) {
                    coEvery { personService.get(person.id) } returns person.success()
                    coEvery { messageFirestoreService.add(channelId, message) } returns messageId.success()

                    val result =
                        virtualClinicMessagesService.sendMessageArchiveByMemberVideoCallRejected(channelId, person.id)
                    assertThat(result).isSuccessWithData(messageId)

                    coVerifyOnce { messageFirestoreService.add(any(), any()) }
                    coVerifyOnce { personService.get(any()) }
                    coVerify { channelNotificationService wasNot called }
                    coVerify { virtualClinicQueueFirestoreService wasNot called }
                    coVerify { staffFirestoreService wasNot called }
                }
            }
        }

    @OptIn(FirestoreContextUsage::class)
    @Test
    fun `#sendMessageArchiveByMissingMemberInteraction return message id and send message`() =
        mockChannelStatusControl(MISSING_MEMBER_INTERACTION) {
            mockTimestamp(timestamp) {
                val message = message.copy(content = "message_archive_by_missing_member_interaction ${person.nickName}")

                withFeatureFlag(
                    FeatureNamespace.CHANNELS,
                    "message_archive_by_missing_member_interaction",
                    "message_archive_by_missing_member_interaction @nickname"
                ) {
                    val functionSlot = slot<(CollectionReference) -> Query>()

                    coEvery { personService.get(person.id) } returns person.success()
                    coEvery { messageFirestoreService.add(channelId, message) } returns messageId.success()
                    every { collectionReference.whereEqualTo("type", "QUESTION") } returns collectionReference
                    coEvery {
                        messageFirestoreService.find(channelId, false, capture(functionSlot))
                    } returns listOf(message).success()

                    val result =
                        virtualClinicMessagesService.sendMessageArchiveByMissingMemberInteraction(channelId, person.id)
                    assertThat(result).isSuccessWithData(messageId)

                    functionSlot.captured.invoke(collectionReference)

                    coVerifyOnce { messageFirestoreService.add(any(), any()) }
                    coVerifyOnce { personService.get(any()) }
                    coVerifyOnce { messageFirestoreService.find(any(), any(), any()) }
                    verifyOnce { collectionReference.whereEqualTo(any<String>(), any()) }
                    coVerify { channelNotificationService wasNot called }
                    coVerify { virtualClinicQueueFirestoreService wasNot called }
                    coVerify { staffFirestoreService wasNot called }
                }
            }
        }

    @OptIn(FirestoreContextUsage::class)
    @Test
    fun `#sendMessageArchiveByMissingMemberInteraction return message id and send message and hide question`() =
        mockChannelStatusControl(MISSING_MEMBER_INTERACTION) {
            mockTimestamp(timestamp) {
                val questionMessageId = "questionMessageId"
                val questionMessage = message.copy(
                    id = questionMessageId,
                    type = MessageType.QUESTION,
                    question = QuestionContent(
                        question = "content",
                        input = QuestionnaireQuestionInputResponse(
                            action = "",
                            displayAttributes = null,
                            options = emptyList(),
                            type = HealthFormQuestionType.OPTION_BUTTONS
                        ),
                        isVisible = true
                    )
                )
                val message = message.copy(content = "message_archive_by_missing_member_interaction ${person.nickName}")

                withFeatureFlag(
                    FeatureNamespace.CHANNELS,
                    "message_archive_by_missing_member_interaction",
                    "message_archive_by_missing_member_interaction @nickname"
                ) {
                    val functionSlot = slot<(CollectionReference) -> Query>()

                    coEvery { personService.get(person.id) } returns person.success()
                    coEvery { messageFirestoreService.add(channelId, message) } returns messageId.success()
                    every { collectionReference.whereEqualTo("type", "QUESTION") } returns collectionReference
                    coEvery {
                        messageFirestoreService.find(channelId, false, capture(functionSlot))
                    } returns listOf(questionMessage).success()
                    coEvery {
                        messageFirestoreService.updateFields(
                            channelId,
                            questionMessageId,
                            false,
                            mapOf(MessageDocument::question to questionMessage.question!!.copy(isVisible = false))
                        )
                    } returns questionMessageId.success()

                    val result =
                        virtualClinicMessagesService.sendMessageArchiveByMissingMemberInteraction(channelId, person.id)
                    assertThat(result).isSuccessWithData(messageId)

                    functionSlot.captured.invoke(collectionReference)

                    coVerifyOnce { messageFirestoreService.add(any(), any()) }
                    coVerifyOnce { personService.get(any()) }
                    coVerifyOnce { messageFirestoreService.find(any(), any(), any()) }
                    verifyOnce { collectionReference.whereEqualTo(any<String>(), any()) }
                    coVerifyOnce { messageFirestoreService.updateFields(any(), any(), any(), any()) }
                    coVerify { channelNotificationService wasNot called }
                    coVerify { virtualClinicQueueFirestoreService wasNot called }
                    coVerify { staffFirestoreService wasNot called }
                }
            }
        }

    private fun mockChannelStatusControl(
        status: VirtualClinicStatusControl,
        test: suspend () -> Unit
    ) = runBlocking {
        val channel = ChannelDocument(
            channelPersonId = "",
            personId = "",
            virtualClinicStatusControl = status
        )
        coEvery { channelFirestoreService.getChannel(channelId) } returns channel.success()

        test()

        coVerifyOnce { channelFirestoreService.getChannel(any()) }
    }
}
