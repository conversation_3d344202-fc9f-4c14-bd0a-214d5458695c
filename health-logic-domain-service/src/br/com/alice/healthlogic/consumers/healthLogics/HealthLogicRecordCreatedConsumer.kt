package br.com.alice.healthlogic.consumers.healthLogics

import br.com.alice.common.extensions.then
import br.com.alice.common.kafka.interfaces.KafkaProducerService
import br.com.alice.common.logging.logger
import br.com.alice.healthlogic.client.HealthLogicRecordService
import br.com.alice.healthlogic.consumers.Consumer
import br.com.alice.healthlogic.event.HealthLogicRecordCreatedEvent
import br.com.alice.healthlogic.event.HealthLogicResultEvent
import br.com.alice.healthlogic.services.healthLogics.HealthLogicsService
import com.github.kittinunf.result.flatMap
import com.github.kittinunf.result.success

class HealthLogicRecordCreatedConsumer(
    private val healthLogicsService: HealthLogicsService,
    private val kafkaProducerService: KafkaProducerService,
    private val healthLogicRecordService: HealthLogicRecordService,
): Consumer() {

    suspend fun createHealthLogicRequestEvent(event: HealthLogicRecordCreatedEvent) =
        withSubscribersEnvironment {
            val record = event.payload.healthLogicRecord
            val caseRecordId = event.payload.caseRecordId

            if (!record.hasValidStatus()) return@withSubscribersEnvironment true.success()

            healthLogicRecordService.isFirstOfProgram(record).flatMap { isFirst ->
                if (!isFirst) return@withSubscribersEnvironment true.success()

                healthLogicsService.getProgramActions(record.currentNodeId).then {
                    it.forEach { action ->
                        logger.info(
                            "HealthLogicRecordCreatedConsumer.createHealthLogicRequestEvent: sending event",
                            "health_logic_record" to event.payload.healthLogicRecord,
                            "action" to action,
                            "caseRecordId" to caseRecordId,
                        )
                        kafkaProducerService.produce(
                            HealthLogicResultEvent(
                                healthLogicRecord = record,
                                action = action,
                                caseRecordId = caseRecordId
                            )
                        )
                    }
                }
            }
        }
}
