package br.com.alice.client.datagateway

import br.com.alice.common.core.extensions.isNotNullOrEmpty


internal object DataGatewayParamsBuilder {

    fun buildParams(params: DataGatewayFilterParams) =
        listOfNotNull(
            params.filters.takeIf { it.isNotNullOrEmpty() }?.let { buildFilters(it) },
            params.sort.takeIf { it.isNotNullOrEmpty() }?.let { buildSort(it) },
            params.fields.takeIf { it.isNotNullOrEmpty() }?.let { buildFields(it) },
            params.pageNumber?.let { buildPageNumber(it) },
            params.pageSize?.let { buildPageSize(it) }
        ).let { values ->
            if (values.isEmpty()) "" else "?${values.joinToString("&")}"
        }


    private fun buildSort(sort: List<DataGatewaySort>) =
        "sort=${sort.joinToString(",") { it.getValue() }}"

    private fun buildFilters(filters: List<DataGatewayFilter<*>>) =
        filters.joinToString("&") { buildFilter(it) }

    private fun buildFields(fields: List<String>) =
        "fields=${fields.joinToString(",")}"

    private fun buildPageNumber(value: Long) =
        "page[number]=${value}"

    private fun buildPageSize(value: Long) =
        "page[size]=${value}"

    private fun <T> buildFilter(filter: DataGatewayFilter<T>) =
        "filter[${filter.column}]${filter.operator.filterKey}${filter.getValue()}"

    private fun DataGatewaySort.getValue() =
        if (order == DataGatewaySortOrder.ASC) column else "-${column}"

    private fun <T> DataGatewayFilter<T>.getValue() =
        when (value) {
            null -> "="
            is List<*> -> "=${value.joinToString(separator = ",")}"
            else -> "=$value"
        }
}
