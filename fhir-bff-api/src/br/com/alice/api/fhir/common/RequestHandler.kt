package br.com.alice.api.fhir.common

import br.com.alice.common.Response
import br.com.alice.common.convertParameter
import br.com.alice.common.core.extensions.fromJson
import br.com.alice.common.principalPathKey
import br.com.alice.common.rateLimited
import br.com.alice.common.respond
import br.com.alice.common.serialization.gson
import br.com.alice.common.withContexts
import br.com.alice.fhir.commons.extractVersionFromHeader
import br.com.alice.fhir.commons.fhirContentParser
import br.com.alice.fhir.model.PatchOperation
import io.ktor.http.HttpHeaders
import io.ktor.server.application.ApplicationCall
import io.ktor.server.application.call
import io.ktor.server.request.receive
import io.ktor.util.pipeline.PipelineContext
import org.hl7.fhir.instance.model.api.IBaseResource


/**
 * Group of br.com.alice.api.fhir.common
 *
 * This function handle with request and make available at controller the original payload and the Data Class converted
 *
 * @param T1 the type of original payload
 * @param T2 the data class will be used at controller
 *
 * @property controllerAction function of controller who handle with request
 * @property parseAction function who will handle with serialize between payload and Data Class
 * @property times times the request can be handler by application
 * @property duration duration between the first request and the second request with the same key
 * @property keyGenerator the key who will be identifier the request
 *
 * @return Response Data class who send the message and the status code
 *
 * Example Route:
 * fhirHandlerLimited(controllerExample::doThis, parseFunction::doThat)
 *
 * Example Controller:
 * class ControllerExample(requestJson: String, data: DataClass): Response {}
 */
suspend inline fun <reified T2 : IBaseResource> PipelineContext<*, ApplicationCall>.fhirHandlerLimited(
    crossinline controllerAction: suspend (String, T2) -> Response,
    times: Int? = null,
    duration: Long? = null,
    noinline keyGenerator: ((String) -> String)? = null
) = withContexts {
    val defaultKeyGenerator: (String) -> String = { p -> p.hashCode().toString() }

    val acceptHeader = call.request.headers[HttpHeaders.Accept]
    val contentTypeHeader = call.request.headers[HttpHeaders.ContentType]
    val requestBody = call.receive<String>()

    val fhirVersionRequest = extractVersionFromHeader(acceptHeader)
    val parserInstance = fhirContentParser(fhirVersionRequest, contentTypeHeader)
    val genericParameter = parserInstance.parseResource(T2::class.java, requestBody)

    val key =
        keyGenerator?.invoke(requestBody) ?: principalPathKey() ?: defaultKeyGenerator.invoke(requestBody)

    rateLimited(key = key, times = times, duration = duration) {
        controllerAction(requestBody, genericParameter)
    }.apply { respond(this) }
}

/*
    FHIR updates receives an array of operations and an identifier on query param
    This function is very specific if needed it to be made generic, consider create one that handles body as a root list and query param
 */
suspend inline fun <reified T1 : List<PatchOperation>, reified T2 : String> PipelineContext<*, ApplicationCall>.fhirPatchLimited(
    crossinline controllerAction: suspend (T1, T2) -> Response,
    times: Int? = null,
    duration: Long? = null,
    noinline keyGenerator: ((T1, T2) -> String)? = null
) =
    withContexts {
        val defaultKeyGenerator: (T1, T2) -> String = { p1, p2 -> p1.hashCode().toString() + p2.hashCode().toString() }

        val body = gson.fromJson<T1>(call.receive())
        val identifier = convertParameter<T2>(call.parameters["identifier"]!!)

        val key =
            keyGenerator?.invoke(body, identifier) ?: principalPathKey() ?: defaultKeyGenerator.invoke(body, identifier)

        rateLimited(
            key = key,
            times = times,
            duration = duration
        ) {
            controllerAction(body, identifier)
        }.apply {
            respond(this)
        }
    }
