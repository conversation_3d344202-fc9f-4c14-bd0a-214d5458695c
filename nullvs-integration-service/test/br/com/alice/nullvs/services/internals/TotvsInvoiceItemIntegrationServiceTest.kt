package br.com.alice.nullvs.services.internals

import br.com.alice.business.client.BeneficiaryService
import br.com.alice.business.client.CompanyService
import br.com.alice.business.client.CompanySubContractService
import br.com.alice.common.RangeUUID
import br.com.alice.common.core.exceptions.NotFoundException
import br.com.alice.common.data.dsl.matchers.ResultAssert.Companion.assertThat
import br.com.alice.common.helpers.coVerifyNone
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.common.helpers.returns
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.ExternalModelType
import br.com.alice.data.layer.models.InternalModelType
import br.com.alice.data.layer.models.InvoiceItem
import br.com.alice.data.layer.models.InvoiceItemOperation
import br.com.alice.data.layer.models.InvoiceItemStatus
import br.com.alice.data.layer.models.InvoiceItemType
import br.com.alice.moneyin.client.InvoiceItemService
import br.com.alice.moneyin.client.InvoicesService
import br.com.alice.moneyin.client.MemberInvoiceGroupService
import br.com.alice.nullvs.common.NullvsActionType
import br.com.alice.nullvs.exceptions.FirstPaymentInvoiceItemException
import br.com.alice.nullvs.exceptions.InvalidInvoiceItemIntegration
import br.com.alice.nullvs.exceptions.InvoiceItemAlreadyIntegrated
import br.com.alice.nullvs.exceptions.InvoiceItemWasNotIntegrated
import br.com.alice.nullvs.exceptions.SubcontractIsNotActivatedYet
import br.com.alice.nullvs.models.Meta
import br.com.alice.nullvs.models.payment.Level
import br.com.alice.nullvs.models.payment.NullvsInvoiceItemCanceled
import br.com.alice.nullvs.models.payment.NullvsInvoiceItemCreated
import br.com.alice.nullvs.models.payment.ValueType
import br.com.alice.person.client.PersonService
import com.github.kittinunf.result.failure
import com.github.kittinunf.result.success
import io.mockk.clearAllMocks
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.mockk
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.TestInstance
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.MethodSource
import java.math.BigDecimal
import java.time.LocalDateTime
import kotlin.test.AfterTest
import kotlin.test.Test

@TestInstance(TestInstance.Lifecycle.PER_CLASS)
class TotvsInvoiceItemIntegrationServiceTest {

    private val companyService = mockk<CompanyService>()
    private val subcontractService = mockk<CompanySubContractService>()
    private val personService = mockk<PersonService>()
    private val invoiceItemService = mockk<InvoiceItemService>()
    private val memberInvoiceGroupService = mockk<MemberInvoiceGroupService>()
    private val invoicesService = mockk<InvoicesService>()
    private val beneficiaryService = mockk<BeneficiaryService>()
    private val service = TotvsInvoiceItemIntegrationService(
        companyService,
        subcontractService,
        personService,
        invoiceItemService,
        memberInvoiceGroupService,
        invoicesService,
        beneficiaryService
    )

    @AfterTest
    fun setup() = clearAllMocks()

    companion object {
        @JvmStatic
        fun status() = listOf(
            arrayOf(InvoiceItemStatus.CANCELED, 1),
            arrayOf(InvoiceItemStatus.ACTIVE, 1),
            arrayOf(InvoiceItemStatus.PENDING, 0),
        )
    }

    @Nested
    inner class CreateItem {

        private val meta = Meta(
            eventId = RangeUUID.generate(),
            eventName = "event01",
            internalId = RangeUUID.generate(),
            internalModelName = InternalModelType.INVOICE_ITEM,
            integrationEventName = "integration01",
            externalId = null,
            externalModelName = ExternalModelType.INVOICE_ITEM,
            integratedAt = LocalDateTime.now().minusDays(1),
            originalTopic = "original01",
        )

        @ParameterizedTest
        @MethodSource("br.com.alice.nullvs.services.internals.TotvsInvoiceItemIntegrationServiceTest#status")
        fun `should create a NullvsInvoiceItemCreated from subcontract item`(
            status: InvoiceItemStatus,
            exactly: Int
        ) = runBlocking<Unit> {
            val company = TestModelFactory.buildCompany()
            val subcontract = TestModelFactory.buildCompanySubContract(
                companyId = company.id,
                externalId = "0001",
            )

            val invoiceItem = TestModelFactory.buildInvoiceItem(
                companyId = company.id,
                subcontractId = subcontract.id,
                percentageValue = BigDecimal("10.00"),
                operation = InvoiceItemOperation.DISCOUNT,
                type = InvoiceItemType.SALES,
                personId = null,
                status = status,
            )

            val pendingInvoiceItem = invoiceItem.copy(status = InvoiceItemStatus.PENDING)

            coEvery { subcontractService.get(subcontract.id) } returns subcontract
            coEvery { companyService.get(company.id) } returns company
            coEvery { invoiceItemService.get(invoiceItem.id) } returns invoiceItem
            coEvery { invoiceItemService.listActiveInvoiceItemsBySubcontractId(subcontract.id) } returns listOf(TestModelFactory.buildInvoiceItem()).success()
            coEvery { invoiceItemService.updateInvoiceItem(pendingInvoiceItem) } returns pendingInvoiceItem

            val result = service.createItem(meta, invoiceItem)

            assertThat(result).isSuccessWithData(
                NullvsInvoiceItemCreated(
                    meta,
                    NullvsActionType.CREATE,
                    invoiceItemCreated =
                    NullvsInvoiceItemCreated.InvoiceItemCreated(
                        level = Level.SUBCONTRACT,
                        cpfCnpj = company.cnpj,
                        month = invoiceItem.referenceDate.monthValue,
                        year = invoiceItem.referenceDate.year,
                        code = "116",
                        valueType = ValueType.PERCENTAGE,
                        value = 10.00,
                        numberOfInstallments = "1",
                        observation = "",
                        idAddBilling = invoiceItem.id,
                        subcontractId = invoiceItem.companySubcontractId
                    )
                )
            )

            coVerify(exactly = exactly) {
                invoiceItemService.updateInvoiceItem(pendingInvoiceItem)
            }
        }

        @ParameterizedTest
        @MethodSource("br.com.alice.nullvs.services.internals.TotvsInvoiceItemIntegrationServiceTest#status")
        fun `should create a NullvsInvoiceItemCreated from person item`(
            status: InvoiceItemStatus,
            exactly: Int
        ) = runBlocking<Unit> {
            val person = TestModelFactory.buildPerson()

            val invoiceItem = TestModelFactory.buildInvoiceItem(
                companyId = null,
                subcontractId = null,
                percentageValue = BigDecimal("10.00"),
                operation = InvoiceItemOperation.DISCOUNT,
                type = InvoiceItemType.SALES,
                personId = person.id,
                status = status,
            )

            val pendingInvoiceItem = invoiceItem.copy(status = InvoiceItemStatus.PENDING)

            coEvery { personService.get(person.id) } returns person

            coEvery { invoiceItemService.get(invoiceItem.id) } returns invoiceItem

            coEvery { invoiceItemService.listActiveInvoiceItemsByPersonId(person.id) } returns listOf(TestModelFactory.buildInvoiceItem()).success()

            coEvery { invoiceItemService.updateInvoiceItem(pendingInvoiceItem) } returns pendingInvoiceItem

            val result = service.createItem(meta, invoiceItem)

            assertThat(result).isSuccessWithData(
                NullvsInvoiceItemCreated(
                    meta,
                    NullvsActionType.CREATE,
                    invoiceItemCreated =
                    NullvsInvoiceItemCreated.InvoiceItemCreated(
                        level = Level.MEMBER,
                        cpfCnpj = person.nationalId,
                        month = invoiceItem.referenceDate.monthValue,
                        year = invoiceItem.referenceDate.year,
                        code = "116",
                        valueType = ValueType.PERCENTAGE,
                        value = 10.00,
                        numberOfInstallments = "1",
                        observation = "",
                        idAddBilling = invoiceItem.id,
                    )
                )
            )

            coVerify(exactly = exactly) {
                invoiceItemService.updateInvoiceItem(pendingInvoiceItem)
            }
        }

        @Test
        fun `should not create the NullvsInvoiceItemCreated when the item does not belongs to any subcontract or person`() =
            runBlocking<Unit> {
                val invoiceItem = TestModelFactory.buildInvoiceItem(
                    subcontractId = null,
                    personId = null,
                    status = InvoiceItemStatus.PENDING,
                )

                coEvery { invoiceItemService.get(invoiceItem.id) } returns invoiceItem

                val result = service.createItem(meta, invoiceItem)

                assertThat(result).isFailureOfType(InvalidInvoiceItemIntegration::class)

                coVerifyNone {
                    subcontractService.get(any())
                    companyService.get(any())
                }
            }

        @Test
        fun `should not create the NullvsInvoiceItemCreated when the item is already integrated`() = runBlocking<Unit> {
            val subcontractId = RangeUUID.generate()
            val invoiceItem = TestModelFactory.buildInvoiceItem(
                percentageValue = BigDecimal("10.00"),
                operation = InvoiceItemOperation.DISCOUNT,
                type = InvoiceItemType.SALES,
                externalId = "0001",
                subcontractId = subcontractId,
                personId = null,
                status = InvoiceItemStatus.PENDING,
            )

            coEvery { invoiceItemService.get(invoiceItem.id) } returns invoiceItem

            val result = service.createItem(meta, invoiceItem)

            assertThat(result).isFailureOfType(InvoiceItemAlreadyIntegrated::class)

            coVerifyNone {
                subcontractService.get(any())
                companyService.get(any())
            }
        }

        @Test
        fun `should not create the NullvsInvoiceItemCreated when the subcontract is not active`() = runBlocking<Unit> {
            val subcontract = TestModelFactory.buildCompanySubContract(
                externalId = null,
            )

            val invoiceItem = TestModelFactory.buildInvoiceItem(
                subcontractId = subcontract.id,
                percentageValue = BigDecimal("10.00"),
                operation = InvoiceItemOperation.DISCOUNT,
                type = InvoiceItemType.SALES,
                personId = null,
                status = InvoiceItemStatus.PENDING,
            )

            coEvery { invoiceItemService.get(invoiceItem.id) } returns invoiceItem

            coEvery { subcontractService.get(subcontract.id) } returns subcontract

            val result = service.createItem(meta, invoiceItem)

            assertThat(result).isFailureOfType(SubcontractIsNotActivatedYet::class)

            coVerifyNone {
                companyService.get(any())
            }
        }

        @Test
        fun `should not create the NullvsInvoiceItemCreated when there is no payment by person B2C`() = runBlocking<Unit> {
            val person = TestModelFactory.buildPerson()

            val invoiceItem = TestModelFactory.buildInvoiceItem(
                companyId = null,
                subcontractId = null,
                percentageValue = BigDecimal("10.00"),
                operation = InvoiceItemOperation.DISCOUNT,
                type = InvoiceItemType.SALES,
                personId = person.id,
                status = InvoiceItemStatus.PENDING,
            )

            coEvery { personService.get(person.id) } returns person

            coEvery { invoiceItemService.get(invoiceItem.id) } returns invoiceItem

            coEvery { invoiceItemService.listActiveInvoiceItemsByPersonId(person.id) } returns emptyList<InvoiceItem>().success()

            coEvery { beneficiaryService.findByPersonId(person.id) } returns NotFoundException("not_found").failure()

            coEvery { invoicesService.listInvoiceByPerson(person.id) } returns listOf(TestModelFactory.buildMemberInvoice()).success()

            val result = service.createItem(meta, invoiceItem)

            assertThat(result).isFailureOfType(FirstPaymentInvoiceItemException::class)

            coVerifyNone {
                invoiceItemService.updateInvoiceItem(any())
            }
        }

        @Test
        fun `should not create the NullvsInvoiceItemCreated when there is no payment by person B2B`() = runBlocking<Unit> {
            val person = TestModelFactory.buildPerson()
            val beneficiary = TestModelFactory.buildBeneficiary(personId = person.id)

            val invoiceItem = TestModelFactory.buildInvoiceItem(
                companyId = null,
                subcontractId = null,
                percentageValue = BigDecimal("10.00"),
                operation = InvoiceItemOperation.DISCOUNT,
                type = InvoiceItemType.SALES,
                personId = person.id,
                status = InvoiceItemStatus.PENDING,
            )

            coEvery { personService.get(person.id) } returns person

            coEvery { invoiceItemService.get(invoiceItem.id) } returns invoiceItem

            coEvery { invoiceItemService.listActiveInvoiceItemsByPersonId(person.id) } returns emptyList<InvoiceItem>().success()

            coEvery { beneficiaryService.findByPersonId(person.id) } returns beneficiary.success()

            coEvery { invoiceItemService.listActiveInvoiceItemsBySubcontractId(beneficiary.companySubContractId!!) } returns emptyList<InvoiceItem>().success()

            coEvery { invoicesService.listInvoiceByPerson(person.id) } returns listOf(TestModelFactory.buildMemberInvoice()).success()

            val result = service.createItem(meta, invoiceItem)

            assertThat(result).isFailureOfType(FirstPaymentInvoiceItemException::class)

            coVerifyNone {
                invoiceItemService.updateInvoiceItem(any())
            }
        }

        @Test
        fun `should not create the NullvsInvoiceItemCreated when there is no payment by subcontract`() = runBlocking<Unit> {
            val company = TestModelFactory.buildCompany()
            val subcontract = TestModelFactory.buildCompanySubContract(
                companyId = company.id,
                externalId = "0001",
            )

            val invoiceItem = TestModelFactory.buildInvoiceItem(
                companyId = company.id,
                subcontractId = subcontract.id,
                percentageValue = BigDecimal("10.00"),
                operation = InvoiceItemOperation.DISCOUNT,
                type = InvoiceItemType.SALES,
                personId = null,
                status = InvoiceItemStatus.PENDING,
            )

            coEvery { subcontractService.get(subcontract.id) } returns subcontract
            coEvery { companyService.get(company.id) } returns company
            coEvery { invoiceItemService.get(invoiceItem.id) } returns invoiceItem
            coEvery { invoiceItemService.listActiveInvoiceItemsBySubcontractId(subcontract.id) } returns emptyList<InvoiceItem>().success()
            coEvery { memberInvoiceGroupService.getBySubcontractId(subcontract.id) } returns listOf(TestModelFactory.buildMemberInvoiceGroup()).success()

            val result = service.createItem(meta, invoiceItem)

            assertThat(result).isFailureOfType(FirstPaymentInvoiceItemException::class)

            coVerifyNone {
                invoiceItemService.updateInvoiceItem(any())
            }
        }

        @Test
        fun `should create the NullvsInvoiceItemCreated when there is subcontract discount by person B2B`() = runBlocking<Unit> {
            val person = TestModelFactory.buildPerson()
            val beneficiary = TestModelFactory.buildBeneficiary(personId = person.id)

            val invoiceItem = TestModelFactory.buildInvoiceItem(
                companyId = null,
                subcontractId = null,
                percentageValue = BigDecimal("10.00"),
                operation = InvoiceItemOperation.DISCOUNT,
                type = InvoiceItemType.SALES,
                personId = person.id,
            )

            val pendingInvoiceItem = invoiceItem.copy(status = InvoiceItemStatus.PENDING)

            coEvery { personService.get(person.id) } returns person

            coEvery { invoiceItemService.get(invoiceItem.id) } returns invoiceItem

            coEvery { invoiceItemService.listActiveInvoiceItemsByPersonId(person.id) } returns emptyList<InvoiceItem>().success()

            coEvery { beneficiaryService.findByPersonId(person.id) } returns beneficiary.success()

            coEvery { invoiceItemService.listActiveInvoiceItemsBySubcontractId(beneficiary.companySubContractId!!) } returns listOf(TestModelFactory.buildInvoiceItem()).success()

            coEvery { invoiceItemService.updateInvoiceItem(pendingInvoiceItem) } returns pendingInvoiceItem

            val result = service.createItem(meta, invoiceItem)

            assertThat(result).isSuccessWithData(
                NullvsInvoiceItemCreated(
                    meta,
                    NullvsActionType.CREATE,
                    invoiceItemCreated =
                    NullvsInvoiceItemCreated.InvoiceItemCreated(
                        level = Level.MEMBER,
                        cpfCnpj = person.nationalId,
                        month = invoiceItem.referenceDate.monthValue,
                        year = invoiceItem.referenceDate.year,
                        code = "116",
                        valueType = ValueType.PERCENTAGE,
                        value = 10.00,
                        numberOfInstallments = "1",
                        observation = "",
                        idAddBilling = invoiceItem.id,
                    )
                )
            )

            coVerifyOnce { invoiceItemService.updateInvoiceItem(pendingInvoiceItem) }
        }
    }

    @Nested
    inner class CancelItem {

        private val meta = Meta(
            eventId = RangeUUID.generate(),
            eventName = "event01",
            internalId = RangeUUID.generate(),
            internalModelName = InternalModelType.INVOICE_ITEM,
            integrationEventName = "integration01",
            externalId = null,
            externalModelName = ExternalModelType.INVOICE_ITEM,
            integratedAt = LocalDateTime.now().minusDays(1),
            originalTopic = "original01",
        )

        @Test
        fun `should create a NullvsInvoiceItemCanceled for a subcontract item`() = runBlocking<Unit> {
            val company = TestModelFactory.buildCompany()
            val subcontract = TestModelFactory.buildCompanySubContract(
                companyId = company.id,
                externalId = "0001",
            )

            val invoiceItem = TestModelFactory.buildInvoiceItem(
                companyId = company.id,
                subcontractId = subcontract.id,
                percentageValue = BigDecimal("10.00"),
                operation = InvoiceItemOperation.DISCOUNT,
                type = InvoiceItemType.SALES,
                externalId = "0001",
                personId = null
            )

            coEvery { subcontractService.get(subcontract.id) } returns subcontract
            coEvery { companyService.get(company.id) } returns company

            val result = service.cancelItem(meta, invoiceItem)

            assertThat(result).isSuccessWithData(
                NullvsInvoiceItemCanceled(
                    meta,
                    NullvsActionType.UPDATE,
                    invoiceItemCanceled =
                    NullvsInvoiceItemCanceled.InvoiceItemCanceled(
                        cpf = company.cnpj,
                        idTotvs = null,
                        idAddBilling = invoiceItem.id,
                        level = Level.SUBCONTRACT
                    )
                )
            )
        }

        @Test
        fun `should create a NullvsInvoiceItemCanceled for a person item`() = runBlocking<Unit> {
            val person = TestModelFactory.buildPerson()

            val invoiceItem = TestModelFactory.buildInvoiceItem(
                companyId = null,
                subcontractId = null,
                percentageValue = BigDecimal("10.00"),
                operation = InvoiceItemOperation.DISCOUNT,
                type = InvoiceItemType.SALES,
                externalId = "0001",
                personId = person.id
            )

            coEvery { personService.get(person.id) } returns person

            val result = service.cancelItem(meta, invoiceItem)

            assertThat(result).isSuccessWithData(
                NullvsInvoiceItemCanceled(
                    meta,
                    NullvsActionType.UPDATE,
                    invoiceItemCanceled =
                    NullvsInvoiceItemCanceled.InvoiceItemCanceled(
                        cpf = person.nationalId,
                        idTotvs = invoiceItem.externalId,
                        idAddBilling = invoiceItem.id,
                        level = Level.MEMBER
                    )
                )
            )
        }

        @Test
        fun `should not create the NullvsInvoiceItemCanceled when the item does not belongs to any subcontract and any person`() =
            runBlocking<Unit> {
                val invoiceItem = TestModelFactory.buildInvoiceItem(
                    subcontractId = null,
                    personId = null,
                )

                val result = service.cancelItem(meta, invoiceItem)

                assertThat(result).isFailureOfType(InvalidInvoiceItemIntegration::class)

                coVerifyNone {
                    subcontractService.get(any())
                    companyService.get(any())
                }
            }

        @Test
        fun `should not create the NullvsInvoiceItemCanceled when the item was not integrated`() = runBlocking<Unit> {
            val subcontractId = RangeUUID.generate()
            val invoiceItem = TestModelFactory.buildInvoiceItem(
                percentageValue = BigDecimal("10.00"),
                operation = InvoiceItemOperation.DISCOUNT,
                type = InvoiceItemType.SALES,
                subcontractId = subcontractId,
                externalId = null,
                personId = null
            )

            val result = service.cancelItem(meta, invoiceItem)

            assertThat(result).isFailureOfType(InvoiceItemWasNotIntegrated::class)

            coVerifyNone {
                subcontractService.get(any())
                companyService.get(any())
            }
        }
    }

}
