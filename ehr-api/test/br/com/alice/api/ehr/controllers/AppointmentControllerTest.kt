package br.com.alice.api.ehr.controllers

import br.com.alice.api.ehr.ControllerTestHelper
import br.com.alice.api.ehr.birdIdToken
import br.com.alice.api.ehr.controllers.model.AppointmentResponse
import br.com.alice.api.ehr.controllers.model.AppointmentStaffResponse
import br.com.alice.api.ehr.controllers.model.CaseRecordDetailsAppointmentResponse
import br.com.alice.api.ehr.controllers.model.CouncilResponse
import br.com.alice.api.ehr.controllers.model.CreateAppointmentRequest
import br.com.alice.api.ehr.controllers.model.OutcomeResponse
import br.com.alice.api.ehr.controllers.model.SessionResponse
import br.com.alice.api.ehr.converters.AppointmentRequestConverter
import br.com.alice.api.ehr.converters.AppointmentValidationResponseConverter
import br.com.alice.api.ehr.services.internal.AppointmentExecutedInternalService
import br.com.alice.api.ehr.services.internal.ProcedureGroupType
import br.com.alice.api.ehr.services.internal.ProcedureResponse
import br.com.alice.appointment.client.AppointmentEvolutionService
import br.com.alice.appointment.client.AppointmentService
import br.com.alice.appointment.client.ExternalFileTransport
import br.com.alice.appointment.client.PublishExcuseNotesRequest
import br.com.alice.appointment.client.UpdateAppointmentRequest
import br.com.alice.appointment.models.AppointmentValidationError
import br.com.alice.appointment.models.AppointmentWithValidation
import br.com.alice.common.Disease
import br.com.alice.common.RangeUUID
import br.com.alice.common.core.exceptions.ConflictException
import br.com.alice.common.core.exceptions.NotFoundException
import br.com.alice.common.core.extensions.fromJson
import br.com.alice.common.core.extensions.toSaoPauloTimeZone
import br.com.alice.common.core.extensions.toUUID
import br.com.alice.common.data.dsl.matchers.ResponseAssert.Companion.assertThat
import br.com.alice.common.helpers.coVerifyNone
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.common.serialization.gsonBuilder
import br.com.alice.common.service.serialization.isoDateGson
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.*
import br.com.alice.data.layer.models.AppointmentFinishType.MANUAL
import br.com.alice.data.layer.models.AppointmentType.ASSISTANCE_CARE
import br.com.alice.data.layer.models.AppointmentType.COUNTER_REFERRAL
import br.com.alice.data.layer.models.AppointmentType.DEFAULT
import br.com.alice.data.layer.models.AppointmentType.ON_SITE
import br.com.alice.healthcondition.client.HealthConditionTemplateService
import br.com.alice.healthcondition.client.PersonCaseService
import br.com.alice.healthlogic.client.ScriptService
import br.com.alice.healthplan.client.HealthPlanTaskService
import br.com.alice.healthplan.converters.HealthPlanTaskTransportConverter
import br.com.alice.healthplan.models.HealthPlanTaskGroupTransport
import br.com.alice.healthplan.models.HealthPlanTaskTransport
import br.com.alice.healthplan.models.HealthPlanTasksTransport
import br.com.alice.provider.client.MedicalSpecialtyService
import br.com.alice.secondary.attention.client.HealthPlanTaskReferralsService
import br.com.alice.secondary.attention.models.HealthPlanTaskSessionsCount
import br.com.alice.staff.client.HealthProfessionalService
import br.com.alice.staff.client.StaffService
import br.com.alice.staff.converters.StaffGenderDescriptionConverter
import br.com.alice.staff.models.StaffWithHealthProfessional
import com.github.kittinunf.result.failure
import com.github.kittinunf.result.success
import com.google.gson.Gson
import com.google.gson.TypeAdapter
import com.google.gson.stream.JsonReader
import com.google.gson.stream.JsonWriter
import io.ktor.client.statement.bodyAsText
import io.ktor.http.HttpHeaders
import io.mockk.called
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.confirmVerified
import io.mockk.every
import io.mockk.mockk
import io.mockk.mockkObject
import io.mockk.mockkStatic
import kotlinx.coroutines.runBlocking
import org.assertj.core.api.Assertions.assertThat
import java.io.IOException
import java.time.LocalDateTime
import java.time.format.DateTimeFormatter
import java.util.UUID
import kotlin.test.AfterTest
import kotlin.test.BeforeTest
import kotlin.test.Test

class AppointmentControllerTest : ControllerTestHelper() {

    private val staffService: StaffService = mockk()
    private val healthProfessionalService: HealthProfessionalService = mockk()
    private val appointmentService: AppointmentService = mockk()
    private val appointmentEvolutionService: AppointmentEvolutionService = mockk()
    private val healthPlanTaskService: HealthPlanTaskService = mockk()
    private val scriptService: ScriptService = mockk()
    private val personCaseService: PersonCaseService = mockk()
    private val healthConditionTemplateService: HealthConditionTemplateService = mockk()
    private val appointmentExecutedInternalService: AppointmentExecutedInternalService = mockk()
    private val medicalSpecialtyService: MedicalSpecialtyService = mockk()
    private val referralsService: HealthPlanTaskReferralsService = mockk()

    private val appointmentController = AppointmentController(
        appointmentService,
        healthProfessionalService,
        scriptService,
        appointmentEvolutionService,
        healthPlanTaskService,
        personCaseService,
        healthConditionTemplateService,
        appointmentExecutedInternalService,
        medicalSpecialtyService,
        referralsService,
        staffService
    )

    private val appointment = TestModelFactory.buildAppointment(staffId = staff.id).copy(
        outcome = Outcome.DC_DISCHARGED,
        treatedBy = TreatedBy.SCREENING_NURSE,
        clinicalEvaluation = "Ta dodói",
        channelId = "Id do canal",
        caseRecordDetails = listOf(
            CaseRecordDetails(
                caseId = RangeUUID.generate(),
                cipes = listOf(
                    DiseaseDetails(
                        type = Disease.Type.CIPE,
                        value = "1000000",
                        description = "Cipe 1000000",
                    )
                ),
                description = DiseaseDetails(
                    id = RangeUUID.generate().toString(),
                    type = Disease.Type.CID_10,
                    value = "10",
                    description = "Unha Encravada",
                ),
                severity = CaseSeverity.ONGOING,
            )
        ),
        externalFiles = emptyList(),
        contractualRisks = listOf(
            AppointmentContractualRisk(
                caseId = RangeUUID.generate(),
                description = HealthConditionDescription(
                    type = Disease.Type.CID_10,
                    value = "MockValue",
                    id = RangeUUID.generate(),
                ),
                baseRiskRating = 1,
                factor = 2,
                finalRiskRating = 3,
                reason = "Sinusite Crônica"
            )
        )
    )

    private val caseRecordsDetails = appointment.caseRecordDetails?.first().let {
        CaseRecordDetails(
            caseId = it?.caseId,
            description = it?.description!!,
            cipes = it.cipes,
            severity = it.severity,
            follow = it.follow,
            observation = it.observation,
            channel = it.channel,
            specialistOpinion = it.specialistOpinion,
            seriousness = it.seriousness,
        )
    }

    private val personId = appointment.personId
    private val healthPlan = TestModelFactory.buildHealthPlan(personId = personId)
    private val group = HealthPlanTaskGroupTransport(
        id = RangeUUID.generate(),
        healthPlanId = healthPlan.id,
        name = "Correr Maratona",
        personId = healthPlan.personId
    )
    private val eatingTask =
        TestModelFactory.buildHealthPlanTask(
            personId = personId,
            staffId = staff.id,
            healthPlanId = healthPlan.id,
            type = HealthPlanTaskType.EATING,
            groupId = group.id,
            caseId = caseRecordsDetails.caseId,
            caseRecordDetails = listOf(caseRecordsDetails),
            content = emptyMap()
        )
    private val followUp = TestModelFactory.buildHealthPlanTask(
        personId = personId,
        staffId = staff.id,
        type = HealthPlanTaskType.FOLLOW_UP_REQUEST,
        deadline = Deadline(
            unit = PeriodUnit.DAY,
            date = LocalDateTime.now().plusDays(1),
            quantity = 1
        ),
        caseRecordDetails = listOf(caseRecordsDetails),
    )

    private val healthPlanTasksTransport = HealthPlanTasksTransport(
        eating = listOf(
            HealthPlanTaskTransportConverter.convert(
                eatingTask.specialize(),
                mapOf(staff.id to staff),
                mapOf(group.id!! to group)
            )
        ),
        followUpRequest = listOf(
            HealthPlanTaskTransportConverter.convert(
                followUp.specialize(),
                mapOf(staff.id to staff),
                mapOf(group.id!! to group)
            )
        )
    )

    private val caseRecordsResponse = CaseRecordDetailsAppointmentResponse(
        caseId = caseRecordsDetails.caseId,
        description = caseRecordsDetails.description,
        cipes = caseRecordsDetails.cipes,
        severity = caseRecordsDetails.severity,
        follow = caseRecordsDetails.follow,
        observation = caseRecordsDetails.observation,
        channel = caseRecordsDetails.channel,
        specialistOpinion = caseRecordsDetails.specialistOpinion,
        seriousness = caseRecordsDetails.seriousness,
        healthPlanTasks = listOfNotNull(
            healthPlanTasksTransport.eating,
            healthPlanTasksTransport.followUpRequest
        ).flatten(),
    )

    private val healthProfessional = TestModelFactory.buildHealthProfessional(
        staffId = staff.id,
        staff = staff
    )
    private val staffWithHealthProfessional = StaffWithHealthProfessional(staff, healthProfessional)

    private val externalFileTransport = ExternalFileTransport(
        id = RangeUUID.generate().toString(),
        store = true,
        type = ExternalFileType.IMAGE
    )

    private val externalFile = ExternalFile(
        id = RangeUUID.generate(),
        store = true,
        type = ExternalFileType.IMAGE,
        origin = ExternalFileOrigin.CHANNELS
    )

    private val procedureExecuted = ProcedureResponse(
        id = RangeUUID.generate(),
        name = "Consulta",
        tussCode = "10101012",
        aliceCode = "**********",
        groupType = ProcedureGroupType.CONSULTA,
        isPriced = true
    )

    @BeforeTest
    override fun setup() {
        super.setup()
        module.single { appointmentController }
    }

    @AfterTest
    fun confirmMocks() = confirmVerified(
        appointmentService,
        healthProfessionalService,
        scriptService,
        appointmentEvolutionService,
        staffService
    )

    @Test
    fun `#createAppointment should create Appointment`() = runBlocking {
        val request = CreateAppointmentRequest(
            personId = appointment.personId.toString(),
            type = DEFAULT
        )

        val localDateTimeNow = LocalDateTime.now()
        val expectedToCreate = AppointmentRequestConverter.convert(request, staff).copy(
            createdAt = localDateTimeNow,
            updatedAt = localDateTimeNow
        )

        mockkObject(RangeUUID) {
            mockkStatic(LocalDateTime::class) {
                every { RangeUUID.generate() } returns expectedToCreate.id
                every { LocalDateTime.now() } returns localDateTimeNow

                coEvery { staffService.get(staff.id) } returns staff.success()
                coEvery { appointmentService.add(expectedToCreate) } returns appointment.success()
                coEvery {
                    healthProfessionalService.findByStaffId(
                        staff.id, HealthProfessionalService.FindOptions(
                            withStaff = false,
                            withContact = false
                        )
                    )
                } returns healthProfessional.success()

                authenticatedAs(idToken, staffTest) {
                    post(to = "/ehr/appointments", body = request) { response ->
                        assertThat(response).isOKWithData(appointment)
                    }
                }

                coVerifyOnce { appointmentService.add(any()) }
                coVerifyOnce { staffService.get(any()) }
                coVerifyOnce { healthProfessionalService.findByStaffId(any(), any()) }

                coVerify { scriptService wasNot called }
                coVerify { appointmentEvolutionService wasNot called }
            }
        }
    }

    @Test
    fun `#createAppointment should create Appointment even if health professional does not exists`() = runBlocking {
        val request = CreateAppointmentRequest(
            personId = appointment.personId.toString(),
            type = DEFAULT
        )

        val localDateTimeNow = LocalDateTime.now()
        val expectedToCreate = AppointmentRequestConverter.convert(request, staff).copy(
            createdAt = localDateTimeNow,
            updatedAt = localDateTimeNow
        )

        mockkObject(RangeUUID) {
            mockkStatic(LocalDateTime::class) {
                every { RangeUUID.generate() } returns expectedToCreate.id
                every { LocalDateTime.now() } returns localDateTimeNow

                coEvery { staffService.get(staff.id) } returns staff.success()
                coEvery { appointmentService.add(expectedToCreate) } returns appointment.success()
                coEvery {
                    healthProfessionalService.findByStaffId(
                        staff.id, HealthProfessionalService.FindOptions(
                            withStaff = false,
                            withContact = false
                        )
                    )
                } returns NotFoundException("Health Professional not found").failure()

                authenticatedAs(idToken, staffTest) {
                    post(to = "/ehr/appointments", body = request) { response ->
                        assertThat(response).isOKWithData(appointment)
                    }
                }

                coVerifyOnce { appointmentService.add(any()) }
                coVerifyOnce { staffService.get(any()) }
                coVerifyOnce { healthProfessionalService.findByStaffId(any(), any()) }
                coVerify { scriptService wasNot called }
                coVerify { appointmentEvolutionService wasNot called }
            }
        }
    }

    @Test
    fun `#createAppointment should fill the description with CID templates when appointment type is STATEMENT_OF_HEALTH`() =
        runBlocking {
            val request = CreateAppointmentRequest(
                personId = appointment.personId.toString(),
                type = AppointmentType.STATEMENT_OF_HEALTH
            )

            val conditionId1 = RangeUUID.generate()
            val conditionId2 = RangeUUID.generate()
            val personCases = listOf(
                TestModelFactory.buildPersonCase(
                    personId = appointment.personId,
                    healthConditionId = conditionId1
                ),
                TestModelFactory.buildPersonCase(
                    personId = appointment.personId,
                    healthConditionId = conditionId2
                )
            )
            val template1 = TestModelFactory.buildHealthConditionTemplate(
                healthConditionId = conditionId1,
                template = "Template 1\nTest"
            )
            val template2 = TestModelFactory.buildHealthConditionTemplate(
                healthConditionId = conditionId2,
                template = "Template 2"
            )

            val localDateTimeNow = LocalDateTime.now()
            val expectedToCreate = AppointmentRequestConverter.convert(request, staff).copy(
                createdAt = localDateTimeNow,
                updatedAt = localDateTimeNow,
                description = "Template 1\n\nTest\n\n---\n\nTemplate 2"
            )

            val appointmentWithDescription = appointment.copy(description = expectedToCreate.description)

            mockkObject(RangeUUID) {
                mockkStatic(LocalDateTime::class) {
                    every { RangeUUID.generate() } returns expectedToCreate.id
                    every { LocalDateTime.now() } returns localDateTimeNow

                    coEvery { staffService.get(staff.id) } returns staff.success()
                    coEvery { personCaseService.getByPersonId(appointmentWithDescription.personId) } returns personCases.success()
                    coEvery {
                        healthConditionTemplateService.findByHealthConditionIds(
                            listOf(
                                conditionId1,
                                conditionId2
                            )
                        )
                    } returns listOf(template1, template2).success()
                    coEvery { appointmentService.add(expectedToCreate) } returns appointmentWithDescription.success()
                    coEvery {
                        healthProfessionalService.findByStaffId(
                            staff.id, HealthProfessionalService.FindOptions(
                                withStaff = false,
                                withContact = false
                            )
                        )
                    } returns healthProfessional.success()
                    authenticatedAs(idToken, staffTest) {
                        post(to = "/ehr/appointments", body = request) { response ->
                            assertThat(response).isOKWithData(appointmentWithDescription)
                        }
                    }

                    coVerifyOnce { appointmentService.add(any()) }
                    coVerifyOnce { staffService.get(any()) }
                    coVerifyOnce { personCaseService.getByPersonId(any()) }
                    coVerifyOnce { healthConditionTemplateService.findByHealthConditionIds(any()) }
                    coVerifyOnce { healthProfessionalService.findByStaffId(any(), any()) }
                }
            }
        }

    @Test
    fun `#createAppointment should fill the started_at with now when appointment type is COUNTER_REFERRAL`() =
        runBlocking {
            val request = CreateAppointmentRequest(
                personId = appointment.personId.toString(),
                type = COUNTER_REFERRAL
            )
            val specialty =
                TestModelFactory.buildMedicalSpecialty(id = healthProfessional.specialtyId!!, name = "Cardiologia")

            val localDateTimeNow = LocalDateTime.now()
            val expectedToCreate = AppointmentRequestConverter.convert(request, staff).copy(
                createdAt = localDateTimeNow,
                updatedAt = localDateTimeNow,
                appointmentDate = localDateTimeNow.toSaoPauloTimeZone().toLocalDate(),
                name = "Consulta de ${specialty.name}",
            )
            val procedureResponse = ProcedureResponse(
                id = RangeUUID.generate(),
                name = "Consulta",
                tussCode = "10101012",
                aliceCode = "**********",
                groupType = ProcedureGroupType.CONSULTA,
                isPriced = true
            )
            mockkObject(RangeUUID) {
                mockkStatic(LocalDateTime::class) {
                    every { RangeUUID.generate() } returns expectedToCreate.id
                    every { LocalDateTime.now() } returns localDateTimeNow

                    coEvery { staffService.get(staff.id) } returns staff.success()
                    coEvery { appointmentService.add(expectedToCreate) } returns appointment.success()
                    coEvery {
                        healthProfessionalService.findByStaffId(
                            staff.id, HealthProfessionalService.FindOptions(
                                withStaff = false,
                                withContact = false
                            )
                        )
                    } returns healthProfessional.success()
                    coEvery {
                        appointmentExecutedInternalService.getDefaultProcedure(
                            healthProfessional.specialtyId!!
                        )
                    } returns procedureResponse.success()
                    coEvery {
                        appointmentExecutedInternalService.addProcedureDefault(appointment.id, procedureResponse)
                    } returns listOf(procedureResponse).success()
                    coEvery {
                        medicalSpecialtyService.getById(healthProfessional.specialtyId!!)
                    } returns specialty.success()

                    authenticatedAs(idToken, staffTest) {
                        post(to = "/ehr/appointments", body = request) { response ->
                            assertThat(response).isOKWithData(appointment)
                        }
                    }

                    coVerifyOnce { appointmentService.add(any()) }
                    coVerifyOnce { staffService.get(any()) }
                    coVerifyOnce { healthProfessionalService.findByStaffId(any(), any()) }
                    coVerifyOnce { appointmentExecutedInternalService.getDefaultProcedure(any()) }
                    coVerifyOnce { appointmentExecutedInternalService.addProcedureDefault(any(), any()) }
                    coVerifyOnce { medicalSpecialtyService.getById(any()) }
                }
            }
        }

    @Test
    fun `#updateAppointment returns update Appointment using new update`() {
        val date = LocalDateTime.now()

        val appointment = Appointment(
            staffId = staff.id,
            ownerStaffIds = setOf(staff.id),
            personId = appointment.personId,
            type = DEFAULT,
            description = "hi",
            guidance = "",
            version = 1,
            createdAt = date,
            updatedAt = date,
            emptyEventReason = "Reason",
            externalFiles = listOf(
                ExternalFile(
                    id = externalFileTransport.id.toUUID(),
                    store = externalFileTransport.store,
                    type = externalFileTransport.type,
                    origin = ExternalFileOrigin.CHANNELS
                )
            )
        )

        val request = UpdateAppointmentRequest(
            personId = appointment.personId.toString(),
            description = appointment.content,
            guidance = appointment.guidance.orEmpty(),
            excuseNotes = emptyList(),
            completed = false,
            type = DEFAULT,
            version = 1,
            emptyEventReason = "Reason",
            externalFiles = listOf(externalFileTransport)
        )

        mockkStatic(LocalDateTime::class) {
            every { LocalDateTime.now() } returns date

            coEvery { staffService.get(staff.id) } returns staff.success()
            coEvery {
                appointmentService.update(staff.id, appointment.id, appointment)
            } returns appointment.success()

            authenticatedAs(idToken, staffTest) {
                put(to = "/ehr/appointments/${appointment.id}", body = request) { response ->
                    assertThat(response).isOKWithData(appointment)
                }
            }

            coVerifyOnce { appointmentService.update(any(), any(), any()) }
            coVerifyOnce { staffService.get(any()) }
            coVerify { scriptService wasNot called }
            coVerify { healthProfessionalService wasNot called }
            coVerify { appointmentEvolutionService wasNot called }
        }

    }

    @Test
    fun `#updateAppointment should update valid Appointment`() {
        val localDateTimeNow = LocalDateTime.now()

        val request = UpdateAppointmentRequest(
            personId = appointment.personId.toString(),
            description = appointment.content,
            guidance = appointment.guidance.orEmpty(),
            excuseNotes = appointment.excuseNotes,
            completed = true,
            version = 0,
            startedAt = appointment.startedAt,
            endedAt = appointment.endedAt,
            type = appointment.type,
            specialty = AppointmentSpecialty("Test", RangeUUID.generate()),
            specialist = AppointmentSpecialist("Test", RangeUUID.generate()),
            outcome = appointment.outcome,
            emptyEventReason = "Reason",
            caseRecordDetails = appointment.caseRecordDetails
        )

        val appointment = Appointment(
            id = appointment.id,
            staffId = appointment.staffId,
            ownerStaffIds = setOf(appointment.staffId),
            personId = appointment.personId,
            description = request.description,
            guidance = request.guidance,
            excuseNotes = request.excuseNotes!!,
            completedAt = localDateTimeNow,
            version = 0,
            startedAt = request.startedAt,
            endedAt = request.endedAt,
            type = request.type!!,
            specialty = request.specialty,
            specialist = request.specialist,
            outcome = appointment.outcome,
            createdAt = localDateTimeNow,
            updatedAt = localDateTimeNow,
            emptyEventReason = "Reason",
            caseRecordDetails = appointment.caseRecordDetails
        )

        mockkStatic(LocalDateTime::class) {
            every { LocalDateTime.now() } returns localDateTimeNow
            coEvery { staffService.get(staff.id) } returns staff.success()
            coEvery {
                appointmentService.update(staff.id, appointment.id, appointment)
            } returns appointment.success()

            authenticatedAs(idToken, staffTest) {
                put(to = "/ehr/appointments/${appointment.id}", body = request) { response ->
                    assertThat(response).isOKWithData(appointment)
                }
            }

            coVerifyOnce { appointmentService.update(any(), any(), any()) }
            coVerifyOnce { staffService.get(any()) }
            coVerify { scriptService wasNot called }
            coVerify { healthProfessionalService wasNot called }
            coVerify { appointmentEvolutionService wasNot called }
        }
    }

    @Test
    fun `#updateAppointment should update with 1 version difference and same user`() {
        val localDateTimeNow = LocalDateTime.now()

        val request = UpdateAppointmentRequest(
            personId = appointment.personId.toString(),
            description = appointment.content,
            guidance = appointment.guidance.orEmpty(),
            excuseNotes = appointment.excuseNotes,
            completed = true,
            version = 0,
            type = appointment.type,
            emptyEventReason = "Reason"
        )

        val appointment = Appointment(
            id = appointment.id,
            staffId = appointment.staffId,
            ownerStaffIds = setOf(appointment.staffId),
            personId = appointment.personId,
            description = request.description,
            guidance = request.guidance,
            excuseNotes = request.excuseNotes!!,
            completedAt = localDateTimeNow,
            version = 0,
            type = request.type!!,
            createdAt = localDateTimeNow,
            updatedAt = localDateTimeNow,
            emptyEventReason = "Reason"
        )

        mockkStatic(LocalDateTime::class) {
            every { LocalDateTime.now() } returns localDateTimeNow
            coEvery { staffService.get(staff.id) } returns staff.success()
            coEvery {
                appointmentService.update(staff.id, appointment.id, appointment)
            } returns appointment.success()

            authenticatedAs(idToken, staffTest) {
                put(to = "/ehr/appointments/${appointment.id}", body = request) { response ->
                    assertThat(response).isOKWithData(appointment)
                }
            }

            coVerifyOnce { appointmentService.update(any(), any(), any()) }
            coVerifyOnce { staffService.get(any()) }
            coVerify { scriptService wasNot called }
            coVerify { healthProfessionalService wasNot called }
            coVerify { appointmentEvolutionService wasNot called }
        }
    }

    @Test
    fun `#updateAppointment should not update Appointment and call FollowUpService when conflict happens`() {
        val localDateTimeNow = LocalDateTime.now()

        val request = UpdateAppointmentRequest(
            personId = appointment.personId.toString(),
            version = 0,
            type = appointment.type,
            emptyEventReason = "Reason"
        )

        val appointment = Appointment(
            id = appointment.id,
            staffId = appointment.staffId,
            ownerStaffIds = setOf(appointment.staffId),
            personId = appointment.personId,
            version = 0,
            type = appointment.type,
            createdAt = localDateTimeNow,
            updatedAt = localDateTimeNow,
            emptyEventReason = "Reason"
        )

        mockkStatic(LocalDateTime::class) {
            every { LocalDateTime.now() } returns localDateTimeNow

            coEvery { staffService.get(staff.id) } returns staff.success()
            coEvery {
                appointmentService.update(staff.id, appointment.id, appointment)
            } returns ConflictException("Appointment ${appointment.id} is conflicted").failure()

            authenticatedAs(idToken, staffTest) {
                put(to = "/ehr/appointments/${appointment.id}", body = request) { response ->
                    assertThat(response).isBadRequestWithErrorCode("appointment_conflict")
                }
            }

            coVerifyOnce { appointmentService.update(any(), any(), any()) }
            coVerifyOnce { staffService.get(any()) }
            coVerify { healthProfessionalService wasNot called }
            coVerify { scriptService wasNot called }
            coVerify { appointmentEvolutionService wasNot called }
        }
    }

    @Test
    fun `#getAppointment should get Appointment`() {
        val otherStaff = TestModelFactory.buildHealthcareTeamPhysician().copy(
            email = "<EMAIL>",
            firstName = "João",
            lastName = "de Andrade"
        )
        val otherHealthProfessional = TestModelFactory.buildHealthProfessional(
            staffId = otherStaff.id,
            staff = otherStaff
        )
        val otherStaffWithHealthProfessional = StaffWithHealthProfessional(otherStaff, otherHealthProfessional)

        val retrievedAppointment = appointment.copy(
            staffId = otherStaff.id,
            completedAt = LocalDateTime.now(),
            createdAt = LocalDateTime.of(2020, 1, 20, 10, 10, 10),
            ownerStaffIds = emptySet(),
            emptyEventReason = "Reason",
            externalFiles = listOf(externalFile)
        )

        coEvery {
            healthProfessionalService.findStaffsWithHealthProfessionalIfExists(retrievedAppointment.getStaffIds())
        } returns listOf(otherStaffWithHealthProfessional).success()
        coEvery { appointmentService.get(retrievedAppointment.id) } returns retrievedAppointment.success()
        coEvery {
            appointmentEvolutionService.getByAppointment(retrievedAppointment.id)
        } returns emptyList<AppointmentEvolution>().success()
        coEvery { appointmentExecutedInternalService.getByAppointmentId(retrievedAppointment.id) } returns listOf(
            procedureExecuted
        ).success()

        val expectedResponse = buildAppointmentResponse(
            otherStaffWithHealthProfessional,
            retrievedAppointment,
            staff.id,
            listOf(caseRecordsResponse.copy(healthPlanTasks = null)),
            listOf(procedureExecuted)
        ).copy(
            externalFiles = listOf(
                ExternalFileTransport(
                    id = externalFile.id.toString(),
                    store = externalFile.store,
                    type = externalFile.type
                )
            )
        )

        authenticatedAs(idToken, staffTest) {
            get("/ehr/appointments/${retrievedAppointment.id}") { response ->
                assertThat(response).isOKWithData(expectedResponse)
            }
        }

        coVerifyOnce { healthProfessionalService.findStaffsWithHealthProfessionalIfExists(any()) }
        coVerifyOnce { appointmentService.get(any()) }
        coVerifyOnce { appointmentEvolutionService.getByAppointment(any()) }
        coVerifyOnce { appointmentExecutedInternalService.getByAppointmentId(any()) }
        coVerify { scriptService wasNot called }
        coVerify { staffService wasNot called }
    }

    @Test
    fun `#validateAppointment returns true when appointment is ready to finish`() {
        val appointmentWithValidation = AppointmentWithValidation(appointment, true)
        coEvery {
            appointmentService.getAndValidate(appointment.id, staffTest.id)
        } returns appointmentWithValidation.success()

        val expectedResponse = AppointmentValidationResponseConverter.convert(appointmentWithValidation)

        authenticatedAs(idToken, staffTest) {
            post(
                "/ehr/appointments/${appointment.id}/validate",
            ) { response ->
                assertThat(response).isOKWithData(expectedResponse)
            }
        }

        coVerifyOnce { appointmentService.getAndValidate(any(), any()) }
        coVerify { staffService wasNot called }
    }

    @Test
    fun `#validateAppointment returns true when appointment is ready no show`() {
        val appointmentWithValidation = AppointmentWithValidation(
            appointment = appointment.copy(
                discardedType = AppointmentDiscardedType.NO_SHOW
            ),
            isValid = true
        )
        coEvery {
            appointmentService.getAndValidate(appointment.id, staffTest.id)
        } returns appointmentWithValidation.success()

        val expectedResponse = AppointmentValidationResponseConverter.convert(appointmentWithValidation)

        authenticatedAs(idToken, staffTest) {
            post(
                "/ehr/appointments/${appointment.id}/validate",
            ) { response ->
                assertThat(response).isOKWithData(expectedResponse)
            }
        }

        coVerifyOnce { appointmentService.getAndValidate(any(), any()) }
        coVerify { staffService wasNot called }
    }

    @Test
    fun `#validateAppointment returns bad request when appointment is not valid`() {
        val appointmentWithValidation = AppointmentWithValidation(
            appointment = appointment,
            isValid = false,
            errors = listOf(
                AppointmentValidationError(
                    code = "invalid_field_values",
                    message = "Invalid field values",
                )
            )
        )

        val expectedResponse = AppointmentValidationResponseConverter.convert(appointmentWithValidation)

        coEvery {
            appointmentService.getAndValidate(appointment.id, staffTest.id)
        } returns appointmentWithValidation.success()

        authenticatedAs(idToken, staffTest) {
            post(
                "/ehr/appointments/${appointment.id}/validate",
            ) { response ->
                assertThat(response).isBadRequestWithData(expectedResponse)
            }
        }

        coVerifyOnce { appointmentService.getAndValidate(any(), any()) }
        coVerify { staffService wasNot called }
    }

    @Test
    fun `#finish returns true and finish the appointment`() {
        val birdIdToken = "birdIdToken"
        val appointmentWithValidation = AppointmentWithValidation(appointment, true)
        coEvery {
            appointmentService.getAndValidate(
                appointment.id,
                staffTest.id
            )
        } returns appointmentWithValidation.success()
        coEvery { appointmentService.finishNew(appointment.id, staff.id, MANUAL, birdIdToken) } returns true.success()

        authenticatedAs(idToken, staffTest) {
            post(
                "/ehr/appointments/${appointment.id}/finish",
                headers = mapOf(HttpHeaders.birdIdToken to birdIdToken)
            ) { response ->
                assertThat(response).isOKWithData(true)
            }
        }

        coVerifyOnce { appointmentService.getAndValidate(any(), any()) }
        coVerifyOnce { appointmentService.finishNew(any(), any(), any(), any()) }
        coVerify { healthProfessionalService wasNot called }
        coVerify { scriptService wasNot called }
        coVerify { appointmentEvolutionService wasNot called }
        coVerify { staffService wasNot called }
    }

    @Test
    fun `#finish returns bad request when the appointment is invalid`() {
        val birdIdToken = "birdIdToken"
        val appointmentWithValidation = AppointmentWithValidation(
            appointment = appointment,
            isValid = false,
            errors = listOf(
                AppointmentValidationError(
                    code = "invalid_field_values",
                    message = "Invalid field values",
                )
            )
        )
        coEvery {
            appointmentService.getAndValidate(
                appointment.id,
                staffTest.id
            )
        } returns appointmentWithValidation.success()

        val expectedResponse = mapOf(
            "code" to "invalid_field_values",
            "message" to "Invalid field values"
        )


        authenticatedAs(idToken, staffTest) {
            post(
                "/ehr/appointments/${appointment.id}/finish",
                headers = mapOf(HttpHeaders.birdIdToken to birdIdToken)
            ) { response ->
                assertThat(response).isBadRequestWithData(expectedResponse)
            }
        }

        coVerifyOnce { appointmentService.getAndValidate(any(), any()) }
        coVerify { healthProfessionalService wasNot called }
        coVerify { scriptService wasNot called }
        coVerify { appointmentEvolutionService wasNot called }
        coVerify { staffService wasNot called }
    }

    @Test
    fun `#finish returns bad request with default error when the appointment is invalid`() {
        val birdIdToken = "birdIdToken"
        val appointmentWithValidation = AppointmentWithValidation(
            appointment = appointment,
            isValid = false,
        )
        coEvery {
            appointmentService.getAndValidate(
                appointment.id,
                staffTest.id
            )
        } returns appointmentWithValidation.success()

        val expectedResponse = mapOf(
            "code" to "invalid_appointment",
            "message" to "Invalid Appointment"
        )


        authenticatedAs(idToken, staffTest) {
            post(
                "/ehr/appointments/${appointment.id}/finish",
                headers = mapOf(HttpHeaders.birdIdToken to birdIdToken)
            ) { response ->
                assertThat(response).isBadRequestWithData(expectedResponse)
            }
        }

        coVerifyOnce { appointmentService.getAndValidate(any(), any()) }
        coVerify { healthProfessionalService wasNot called }
        coVerify { scriptService wasNot called }
        coVerify { appointmentEvolutionService wasNot called }
        coVerify { staffService wasNot called }
    }

    @Test
    fun `#finishV2 returns bad request when the appointment is invalid`() {
        val birdIdToken = "birdIdToken"
        val appointmentWithValidation = AppointmentWithValidation(
            appointment = appointment,
            isValid = false,
            errors = listOf(
                AppointmentValidationError(
                    code = "invalid_field_values",
                    message = "Invalid field values",
                )
            )
        )
        coEvery {
            appointmentService.getAndValidate(
                appointment.id,
                staffTest.id
            )
        } returns appointmentWithValidation.success()

        val expectedResponse = AppointmentValidationResponseConverter.convert(appointmentWithValidation)

        authenticatedAs(idToken, staffTest) {
            post(
                "/ehr/v2/appointments/${appointment.id}/finish",
                headers = mapOf(HttpHeaders.birdIdToken to birdIdToken)
            ) { response ->
                assertThat(response).isBadRequestWithData(expectedResponse)
            }
        }

        coVerifyOnce { appointmentService.getAndValidate(any(), any()) }
        coVerify { healthProfessionalService wasNot called }
        coVerify { scriptService wasNot called }
        coVerify { appointmentEvolutionService wasNot called }
        coVerify { staffService wasNot called }
    }

    @Test
    fun `#finish returns true and delete the appointment when no-show`() {
        val birdIdToken = "birdIdToken"
        val appointment = appointment.copy(
            discardedType = AppointmentDiscardedType.NO_SHOW,
            event = null,
            caseRecordDetails = null,
            description = null,
        )
        val appointmentWithValidation = AppointmentWithValidation(appointment, true)
        coEvery {
            appointmentService.getAndValidate(
                appointment.id,
                staffTest.id
            )
        } returns appointmentWithValidation.success()
        coEvery {
            appointmentService.delete(
                appointment.id,
                appointment.discardedType!!,
                appointment.discardedReason
            )
        } returns appointment.success()

        authenticatedAs(idToken, staffTest) {
            post(
                "/ehr/appointments/${appointment.id}/finish",
                headers = mapOf(HttpHeaders.birdIdToken to birdIdToken)
            ) { response ->
                assertThat(response).isOKWithData(true)
            }
        }

        coVerifyOnce { appointmentService.getAndValidate(any(), any()) }
        coVerifyOnce { appointmentService.delete(any(), any(), any()) }
        coVerifyNone { appointmentService.finishNew(any(), any(), any(), any()) }
        coVerify { healthProfessionalService wasNot called }
        coVerify { scriptService wasNot called }
        coVerify { appointmentEvolutionService wasNot called }
        coVerify { staffService wasNot called }
    }


    @Test
    fun `#getOutcomes should get all outcomes for onSite`() {
        val type = ON_SITE.toString()

        val expected = Outcome.values().toList()
            .filter { it.appointmentType == ON_SITE }
            .map {
                OutcomeResponse(
                    it.toString(),
                    it.description,
                    it.severity
                )
            }

        authenticatedAs(idToken, staffTest) {
            get("/ehr/outcomes/$type") { response ->
                assertThat(response).isOKWithData(expected)
            }
        }

        coVerify { appointmentService wasNot called }
        coVerify { healthProfessionalService wasNot called }
        coVerify { scriptService wasNot called }
        coVerify { appointmentEvolutionService wasNot called }
        coVerify { staffService wasNot called }
    }

    @Test
    fun `#getAppointment returns appointment not editable when is on invalid interval after completed`() {
        val retrievedAppointment = appointment.copy(
            completedAt = LocalDateTime.now().minusMinutes(10)
        )

        coEvery {
            healthProfessionalService.findStaffsWithHealthProfessionalIfExists(retrievedAppointment.getStaffIds())
        } returns listOf(staffWithHealthProfessional).success()

        coEvery { appointmentService.get(retrievedAppointment.id) } returns retrievedAppointment.success()

        coEvery {
            appointmentEvolutionService.getByAppointment(retrievedAppointment.id)
        } returns emptyList<AppointmentEvolution>().success()

        coEvery { appointmentExecutedInternalService.getByAppointmentId(retrievedAppointment.id) } returns listOf(
            procedureExecuted
        ).success()


        val expectedResponse = buildAppointmentResponse(
            staffWithHealthProfessional,
            retrievedAppointment,
            staff.id,
            listOf(caseRecordsResponse.copy(healthPlanTasks = null)),
            listOf(procedureExecuted)
        )

        authenticatedAs(idToken, staffTest) {
            get("/ehr/appointments/${retrievedAppointment.id}") { response ->
                assertThat(response).isOKWithData(expectedResponse)
            }
        }

        coVerifyOnce { healthProfessionalService.findStaffsWithHealthProfessionalIfExists(any()) }
        coVerifyOnce { appointmentService.get(any()) }
        coVerifyOnce { appointmentEvolutionService.getByAppointment(any()) }
        coVerifyOnce { appointmentExecutedInternalService.getByAppointmentId(any()) }
        coVerify { scriptService wasNot called }
        coVerify { staffService wasNot called }
    }

    @Test
    fun `#getAppointment returns appointment editable when is on valid interval after completed`() {
        val retrievedAppointment = appointment.copy(
            completedAt = LocalDateTime.now().minusMinutes(10)
        )

        coEvery {
            healthProfessionalService.findStaffsWithHealthProfessionalIfExists(listOf(staff.id))
        } returns listOf(staffWithHealthProfessional).success()

        coEvery { appointmentService.get(retrievedAppointment.id) } returns retrievedAppointment.success()

        coEvery {
            appointmentEvolutionService.getByAppointment(retrievedAppointment.id)
        } returns emptyList<AppointmentEvolution>().success()

        coEvery { appointmentExecutedInternalService.getByAppointmentId(retrievedAppointment.id) } returns listOf(
            procedureExecuted
        ).success()

        val expectedResponse =
            buildAppointmentResponse(
                staffWithHealthProfessional,
                retrievedAppointment,
                staff.id,
                listOf(caseRecordsResponse.copy(healthPlanTasks = null)),
                listOf(procedureExecuted)
            )

        authenticatedAs(idToken, staffTest) {
            get("/ehr/appointments/${retrievedAppointment.id}") { response ->
                assertThat(response).isOKWithData(expectedResponse)
            }
        }

        coVerifyOnce { healthProfessionalService.findStaffsWithHealthProfessionalIfExists(any()) }
        coVerifyOnce { appointmentService.get(any()) }
        coVerifyOnce { appointmentEvolutionService.getByAppointment(any()) }
        coVerifyOnce { appointmentExecutedInternalService.getByAppointmentId(any()) }
        coVerify { scriptService wasNot called }
        coVerify { staffService wasNot called }
    }

    @Test
    fun `#publishExcuseNotesPdf should return list of excuse notes`() {
        val excuseNote = ExcuseNote("test")
        val request = ExcuseNotesPublishRequest("idToken", excuseNote)
        val expected = listOf(excuseNote)

        coEvery {
            appointmentService.publishExcuseNotes(
                PublishExcuseNotesRequest(
                    appointment.id,
                    staff.id,
                    "idToken",
                    excuseNote
                )
            )
        } returns expected.success()

        authenticatedAs(idToken, staffTest) {
            post("/ehr/appointments/${appointment.id}/excuse_notes", request) { response ->
                assertThat(response).isOKWithData(expected)
            }
        }

        coVerifyOnce { appointmentService.publishExcuseNotes(any()) }
        coVerify { healthProfessionalService wasNot called }
        coVerify { scriptService wasNot called }
        coVerify { appointmentEvolutionService wasNot called }
        coVerify { staffService wasNot called }
    }

    @Test
    fun `#publishExcuseNotesPdf should return bad requests`() {
        val excuseNote = ExcuseNote("test")
        val request = ExcuseNotesPublishRequest("idToken", excuseNote)

        coEvery {
            appointmentService.publishExcuseNotes(
                PublishExcuseNotesRequest(
                    appointment.id,
                    staff.id,
                    "idToken",
                    excuseNote
                )
            )
        } returns IllegalArgumentException("").failure()

        authenticatedAs(idToken, staffTest) {
            post("/ehr/appointments/${appointment.id}/excuse_notes", request) { response ->
                assertThat(response).isBadRequest()
            }
        }

        coVerifyOnce { appointmentService.publishExcuseNotes(any()) }
        coVerify { healthProfessionalService wasNot called }
        coVerify { scriptService wasNot called }
        coVerify { appointmentEvolutionService wasNot called }
        coVerify { staffService wasNot called }
    }

    @Test
    fun `#getExcuseNoteById returns excuse note file url`() {
        val id = RangeUUID.generate()
        val url = "url"

        val expected = ExcuseNoteResponse(url)

        coEvery { appointmentService.getExcuseNoteById(id) } returns url.success()

        authenticatedAs(idToken, staffTest) {
            get("/ehr/appointments/excuse_notes/$id") { response ->
                assertThat(response).isOKWithData(expected)
            }
        }

        coVerifyOnce { appointmentService.getExcuseNoteById(any()) }
        coVerify { healthProfessionalService wasNot called }
        coVerify { scriptService wasNot called }
        coVerify { appointmentEvolutionService wasNot called }
        coVerify { staffService wasNot called }
    }

    @Test
    fun `#getAppointment should return 200 if appointment is assistance care`() {
        val otherStaff = TestModelFactory.buildHealthcareTeamPhysician().copy(
            email = "<EMAIL>",
            firstName = "João",
            lastName = "de Andrade"
        )
        val otherHealthProfessional = TestModelFactory.buildHealthProfessional(
            staffId = otherStaff.id,
            staff = otherStaff
        )
        val otherStaffWithHealthProfessional = StaffWithHealthProfessional(otherStaff, otherHealthProfessional)

        val retrievedAppointment = appointment.copy(
            staffId = otherStaff.id,
            completedAt = LocalDateTime.now(),
            createdAt = LocalDateTime.of(2020, 1, 20, 10, 10, 10),
            ownerStaffIds = emptySet(),
            type = ASSISTANCE_CARE,
            channelId = "Id do canal"
        )

        coEvery {
            healthProfessionalService.findStaffsWithHealthProfessionalIfExists(retrievedAppointment.getStaffIds())
        } returns listOf(otherStaffWithHealthProfessional).success()

        coEvery { appointmentService.get(retrievedAppointment.id) } returns retrievedAppointment.success()

        coEvery {
            appointmentEvolutionService.getByAppointment(retrievedAppointment.id)
        } returns emptyList<AppointmentEvolution>().success()

        coEvery {
            appointmentExecutedInternalService.getByAppointmentId(retrievedAppointment.id)
        } returns listOf(procedureExecuted).success()

        val expectedResponse = buildAppointmentResponse(
            otherStaffWithHealthProfessional,
            retrievedAppointment,
            staff.id,
            listOf(caseRecordsResponse.copy(healthPlanTasks = null)),
            listOf(procedureExecuted)
        )

        authenticatedAs(idToken, staffTest) {
            get("/ehr/appointments/${appointment.id}") { response ->
                assertThat(response).isOKWithData(expectedResponse)
            }
        }

        coVerifyOnce { healthProfessionalService.findStaffsWithHealthProfessionalIfExists(any()) }
        coVerifyOnce { appointmentService.get(any()) }
        coVerifyOnce { appointmentEvolutionService.getByAppointment(any()) }
        coVerifyOnce { appointmentExecutedInternalService.getByAppointmentId(any()) }
        coVerify { scriptService wasNot called }
        coVerify { staffService wasNot called }
    }

    @Test
    fun `#getAppointment should return 200 if appointment is on site`() {
        val otherStaff = TestModelFactory.buildHealthcareTeamPhysician().copy(
            email = "<EMAIL>",
            firstName = "João",
            lastName = "de Andrade"
        )
        val otherHealthProfessional = TestModelFactory.buildHealthProfessional(
            staffId = otherStaff.id,
            staff = otherStaff
        )
        val otherStaffWithHealthProfessional = StaffWithHealthProfessional(otherStaff, otherHealthProfessional)

        val retrievedAppointment = appointment.copy(
            staffId = otherStaff.id,
            completedAt = LocalDateTime.now(),
            createdAt = LocalDateTime.of(2020, 1, 20, 10, 10, 10),
            ownerStaffIds = emptySet(),
            type = ON_SITE,
            channelId = "Id do canal"
        )

        coEvery {
            healthProfessionalService.findStaffsWithHealthProfessionalIfExists(retrievedAppointment.getStaffIds())
        } returns listOf(otherStaffWithHealthProfessional).success()

        coEvery {
            appointmentService.get(retrievedAppointment.id)
        } returns retrievedAppointment.success()

        coEvery {
            appointmentEvolutionService.getByAppointment(retrievedAppointment.id)
        } returns emptyList<AppointmentEvolution>().success()

        coEvery { appointmentExecutedInternalService.getByAppointmentId(retrievedAppointment.id) } returns listOf(
            procedureExecuted
        ).success()

        val expectedResponse = buildAppointmentResponse(
            otherStaffWithHealthProfessional,
            retrievedAppointment,
            staff.id,
            listOf(caseRecordsResponse.copy(healthPlanTasks = null)),
            listOf(procedureExecuted)
        )

        authenticatedAs(idToken, staffTest) {
            get("/ehr/appointments/${appointment.id}") { response ->
                assertThat(response).isOKWithData(expectedResponse)
            }
        }

        coVerifyOnce { healthProfessionalService.findStaffsWithHealthProfessionalIfExists(any()) }
        coVerifyOnce { appointmentService.get(any()) }
        coVerifyOnce { appointmentEvolutionService.getByAppointment(any()) }
        coVerifyOnce { appointmentExecutedInternalService.getByAppointmentId(any()) }

        coVerify { scriptService wasNot called }
        coVerify { staffService wasNot called }
    }

    @Test
    fun `#getAppointment should return 200 and HPT task with load tasks params`() {
        val otherStaff = TestModelFactory.buildHealthcareTeamPhysician().copy(
            email = "<EMAIL>",
            firstName = "João",
            lastName = "de Andrade"
        )
        val otherHealthProfessional = TestModelFactory.buildHealthProfessional(
            staffId = otherStaff.id,
            staff = otherStaff
        )
        val otherStaffWithHealthProfessional = StaffWithHealthProfessional(otherStaff, otherHealthProfessional)

        val retrievedAppointment = appointment.copy(
            staffId = otherStaff.id,
            completedAt = LocalDateTime.now(),
            createdAt = LocalDateTime.of(2020, 1, 20, 10, 10, 10),
            ownerStaffIds = emptySet(),
            type = ON_SITE,
            channelId = "Id do canal"
        )

        coEvery {
            healthProfessionalService.findStaffsWithHealthProfessionalIfExists(retrievedAppointment.getStaffIds())
        } returns listOf(otherStaffWithHealthProfessional).success()

        coEvery {
            appointmentService.get(retrievedAppointment.id)
        } returns retrievedAppointment.success()

        coEvery {
            appointmentEvolutionService.getByAppointment(retrievedAppointment.id)
        } returns emptyList<AppointmentEvolution>().success()

        coEvery {
            healthPlanTaskService.getByPerson(
                retrievedAppointment.personId,
                mapOf(
                    "status" to listOf(
                        HealthPlanTaskStatus.ACTIVE.name,
                        HealthPlanTaskStatus.DRAFT.name,
                        HealthPlanTaskStatus.DONE.name,
                        HealthPlanTaskStatus.DELETED_BY_MEMBER.name
                    ),
                    "appointment_id" to listOf(retrievedAppointment.id.toString())
                )
            )
        } returns healthPlanTasksTransport.success()

        coEvery { appointmentExecutedInternalService.getByAppointmentId(retrievedAppointment.id) } returns listOf(
            procedureExecuted
        ).success()

        val expectedResponse = buildAppointmentResponse(
            otherStaffWithHealthProfessional,
            retrievedAppointment,
            staff.id,
            listOf(caseRecordsResponse),
            listOf(procedureExecuted),
            null
        )

        authenticatedAs(idToken, staffTest) {
            get("/ehr/appointments/${retrievedAppointment.id}?load_tasks=true") { response ->
                assertThat(response).isOK()

                val body: AppointmentResponse = appointmentControllerTestGson.fromJson(response.bodyAsText())
                assertThat(body).isEqualTo(expectedResponse)
            }
        }

        coVerifyOnce { healthProfessionalService.findStaffsWithHealthProfessionalIfExists(any()) }
        coVerifyOnce { appointmentService.get(any()) }
        coVerifyOnce { appointmentEvolutionService.getByAppointment(any()) }
        coVerifyOnce { healthPlanTaskService.getByPerson(any(), any()) }
        coVerifyOnce { appointmentExecutedInternalService.getByAppointmentId(any()) }
        coVerify { scriptService wasNot called }
        coVerify { staffService wasNot called }
    }

    @Test
    fun `#getAppointment should return 200 and HPT task with load tasks params and type is counter referral with session`() {
        val referral = TestModelFactory.buildHealthPlanTaskReferral(
            personId = appointment.personId
        )
        val session = HealthPlanTaskSessionsCount(
            healthPlanTask = referral.specialize(),
            totalSessionsQuantity = 2,
            completedSessionsQuantity = 1,
            availableSessionsQuantity = 1
        )
        val sessionResponse = SessionResponse(
            totalQuantity = session.totalSessionsQuantity,
            completedQuantity = session.completedSessionsQuantity,
            availableQuantity = session.availableSessionsQuantity
        )

        val otherStaff = TestModelFactory.buildHealthcareTeamPhysician().copy(
            email = "<EMAIL>",
            firstName = "João",
            lastName = "de Andrade"
        )
        val otherHealthProfessional = TestModelFactory.buildHealthProfessional(
            staffId = otherStaff.id,
            staff = otherStaff
        )
        val otherStaffWithHealthProfessional = StaffWithHealthProfessional(otherStaff, otherHealthProfessional)

        val retrievedAppointment = appointment.copy(
            staffId = otherStaff.id,
            completedAt = LocalDateTime.now(),
            createdAt = LocalDateTime.of(2020, 1, 20, 10, 10, 10),
            ownerStaffIds = emptySet(),
            type = COUNTER_REFERRAL,
            channelId = "Id do canal",
            event = AppointmentEventDetail(
                referenceModelId = referral.id.toString(),
                referenceModel = AppointmentEventReferenceModel.HEALTH_PLAN_TASK,
                name = referral.title!!,
            )
        )

        coEvery {
            healthProfessionalService.findStaffsWithHealthProfessionalIfExists(retrievedAppointment.getStaffIds())
        } returns listOf(otherStaffWithHealthProfessional).success()

        coEvery {
            appointmentService.get(retrievedAppointment.id)
        } returns retrievedAppointment.success()

        coEvery {
            appointmentEvolutionService.getByAppointment(retrievedAppointment.id)
        } returns emptyList<AppointmentEvolution>().success()

        coEvery {
            healthPlanTaskService.getByPerson(
                retrievedAppointment.personId,
                mapOf(
                    "status" to listOf(
                        HealthPlanTaskStatus.ACTIVE.name,
                        HealthPlanTaskStatus.DRAFT.name,
                        HealthPlanTaskStatus.DONE.name,
                        HealthPlanTaskStatus.DELETED_BY_MEMBER.name
                    ),
                    "appointment_id" to listOf(retrievedAppointment.id.toString())
                )
            )
        } returns healthPlanTasksTransport.success()

        coEvery { appointmentExecutedInternalService.getByAppointmentId(retrievedAppointment.id) } returns listOf(
            procedureExecuted
        ).success()

        coEvery {
            referralsService.countByHealthPlanTaskId(referral.id)
        } returns session.success()

        val expectedResponse = buildAppointmentResponse(
            otherStaffWithHealthProfessional,
            retrievedAppointment,
            staff.id,
            listOf(caseRecordsResponse),
            listOf(procedureExecuted),
            sessionResponse
        )

        authenticatedAs(idToken, staffTest) {
            get("/ehr/appointments/${retrievedAppointment.id}?load_tasks=true") { response ->
                assertThat(response).isOK()

                val body: AppointmentResponse = appointmentControllerTestGson.fromJson(response.bodyAsText())
                assertThat(body).isEqualTo(expectedResponse)
            }
        }

        coVerifyOnce { healthProfessionalService.findStaffsWithHealthProfessionalIfExists(any()) }
        coVerifyOnce { appointmentService.get(any()) }
        coVerifyOnce { appointmentEvolutionService.getByAppointment(any()) }
        coVerifyOnce { healthPlanTaskService.getByPerson(any(), any()) }
        coVerifyOnce { appointmentExecutedInternalService.getByAppointmentId(any()) }
        coVerifyOnce { referralsService.countByHealthPlanTaskId(any()) }
        coVerify { scriptService wasNot called }
        coVerify { staffService wasNot called }
    }

    @Test
    fun `#getAppointment should return 200 and HPT task with load tasks params and type is counter referral without session`() {
        val referral = TestModelFactory.buildHealthPlanTaskReferral(
            personId = appointment.personId
        )
        val session = HealthPlanTaskSessionsCount(
            healthPlanTask = referral.specialize(),
            totalSessionsQuantity = 1,
            completedSessionsQuantity = 0,
            availableSessionsQuantity = 1
        )

        val otherStaff = TestModelFactory.buildHealthcareTeamPhysician().copy(
            email = "<EMAIL>",
            firstName = "João",
            lastName = "de Andrade"
        )
        val otherHealthProfessional = TestModelFactory.buildHealthProfessional(
            staffId = otherStaff.id,
            staff = otherStaff
        )
        val otherStaffWithHealthProfessional = StaffWithHealthProfessional(otherStaff, otherHealthProfessional)

        val retrievedAppointment = appointment.copy(
            staffId = otherStaff.id,
            completedAt = LocalDateTime.now(),
            createdAt = LocalDateTime.of(2020, 1, 20, 10, 10, 10),
            ownerStaffIds = emptySet(),
            type = COUNTER_REFERRAL,
            channelId = "Id do canal",
            event = AppointmentEventDetail(
                referenceModelId = referral.id.toString(),
                referenceModel = AppointmentEventReferenceModel.HEALTH_PLAN_TASK,
                name = referral.title!!,
            )
        )

        coEvery {
            healthProfessionalService.findStaffsWithHealthProfessionalIfExists(retrievedAppointment.getStaffIds())
        } returns listOf(otherStaffWithHealthProfessional).success()

        coEvery {
            appointmentService.get(retrievedAppointment.id)
        } returns retrievedAppointment.success()

        coEvery {
            appointmentEvolutionService.getByAppointment(retrievedAppointment.id)
        } returns emptyList<AppointmentEvolution>().success()

        coEvery {
            healthPlanTaskService.getByPerson(
                retrievedAppointment.personId,
                mapOf(
                    "status" to listOf(
                        HealthPlanTaskStatus.ACTIVE.name,
                        HealthPlanTaskStatus.DRAFT.name,
                        HealthPlanTaskStatus.DONE.name,
                        HealthPlanTaskStatus.DELETED_BY_MEMBER.name
                    ),
                    "appointment_id" to listOf(retrievedAppointment.id.toString())
                )
            )
        } returns healthPlanTasksTransport.success()

        coEvery { appointmentExecutedInternalService.getByAppointmentId(retrievedAppointment.id) } returns listOf(
            procedureExecuted
        ).success()

        coEvery {
            referralsService.countByHealthPlanTaskId(referral.id)
        } returns session.success()

        val expectedResponse = buildAppointmentResponse(
            otherStaffWithHealthProfessional,
            retrievedAppointment,
            staff.id,
            listOf(caseRecordsResponse),
            listOf(procedureExecuted),
            null
        )

        authenticatedAs(idToken, staffTest) {
            get("/ehr/appointments/${retrievedAppointment.id}?load_tasks=true") { response ->
                assertThat(response).isOK()

                val body: AppointmentResponse = appointmentControllerTestGson.fromJson(response.bodyAsText())
                assertThat(body).isEqualTo(expectedResponse)
            }
        }

        coVerifyOnce { healthProfessionalService.findStaffsWithHealthProfessionalIfExists(any()) }
        coVerifyOnce { appointmentService.get(any()) }
        coVerifyOnce { appointmentEvolutionService.getByAppointment(any()) }
        coVerifyOnce { healthPlanTaskService.getByPerson(any(), any()) }
        coVerifyOnce { appointmentExecutedInternalService.getByAppointmentId(any()) }
        coVerifyOnce { referralsService.countByHealthPlanTaskId(any()) }
        coVerify { scriptService wasNot called }
        coVerify { staffService wasNot called }
    }

    private fun buildAppointmentResponse(
        staffWithHealthProfessional: StaffWithHealthProfessional,
        appointment: Appointment,
        loggedStaffId: UUID,
        caseRecordDetails: List<CaseRecordDetailsAppointmentResponse>? = null,
        proceduresExecuted: List<ProcedureResponse>,
        sessionResponse: SessionResponse? = null
    ): AppointmentResponse =
        AppointmentResponse(
            physician = AppointmentStaffResponse(
                id = staffWithHealthProfessional.staff.id.toString(),
                firstName = staffWithHealthProfessional.staff.firstName,
                fullName = staffWithHealthProfessional.staff.fullName,
                profileImageUrl = staffWithHealthProfessional.staff.profileImageUrl,
                description = StaffGenderDescriptionConverter.convert(staffWithHealthProfessional.staff),
                council = CouncilResponse(
                    name = staffWithHealthProfessional.healthProfessional?.councilName ?: "",
                    number = staffWithHealthProfessional.healthProfessional?.council?.number ?: "",
                    state = staffWithHealthProfessional.healthProfessional?.council?.state?.name ?: "",
                )
            ),
            personId = appointment.personId.toString(),
            description = appointment.content,
            guidance = appointment.guidance.orEmpty(),
            excuseNotes = appointment.excuseNotes,
            excuseNotesCount = appointment.excuseNotes.size,
            id = appointment.id.toString(),
            version = appointment.version,
            createdAt = appointment.createdAt.format(DateTimeFormatter.ISO_LOCAL_DATE_TIME),
            updatedAt = appointment.updatedAt.format(DateTimeFormatter.ISO_LOCAL_DATE_TIME),
            isEditable = appointment.staffId == loggedStaffId,
            startedAt = appointment.startedAt,
            endedAt = appointment.endedAt,
            type = appointment.type,
            event = appointment.event,
            specialty = appointment.specialty,
            specialist = appointment.specialist,
            subjectiveCodes = appointment.subjectiveCodes,
            clinicalEvaluationCodes = emptyList(),
            status = appointment.status,
            referencedLinks = appointment.referencedLinks,
            clinicalEvaluation = appointment.clinicalEvaluation,
            outcome = appointment.outcome,
            outcomeResponse = appointment.outcome?.let {
                OutcomeResponse(
                    it.toString(),
                    it.description, it.severity
                )
            },
            treatedBy = appointment.treatedBy,
            channelId = appointment.channelId,
            plan = appointment.plan,
            objective = appointment.objective,
            complaint = appointment.content,
            conduct = appointment.guidance.orEmpty(),
            completed = (appointment.completedAt != null),
            emptyEventReason = appointment.emptyEventReason,
            caseRecordDetails = caseRecordDetails,
            contractualRisks = appointment.contractualRisks,
            proceduresExecuted = proceduresExecuted,
            session = sessionResponse
        )
}

val appointmentControllerTestGson: Gson = gsonBuilder()
    .registerTypeHierarchyAdapter(HealthPlanTaskTransport::class.java, HealthPlanTaskTransportAdapter())
    .create()

/**
 * This class is only used to parser HealthPlanTaskTransport inside appointment because it is an interface
 */
@Suppress("UNCHECKED_CAST")
class HealthPlanTaskTransportAdapter : TypeAdapter<HealthPlanTaskTransport?>() {

    override fun write(out: JsonWriter?, value: HealthPlanTaskTransport?) {
        // unnecessary write
    }

    @Throws(IOException::class)
    override fun read(jsonReader: JsonReader): HealthPlanTaskTransport {
        val raw = isoDateGson.fromJson(jsonReader, Map::class.java) as Map<String, Any?>

        val className = raw.getValue("type").run { HealthPlanTaskType.valueOf(this as String).kClass.simpleName }
        val klass =
            Class.forName("br.com.alice.healthplan.models.${className}Transport") as Class<HealthPlanTaskTransport>
        return isoDateGson.fromJson(isoDateGson.toJson(raw), klass)
    }
}
