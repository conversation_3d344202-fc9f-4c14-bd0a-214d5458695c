package br.com.alice.nullvs.consumers.invoice

import br.com.alice.business.client.CompanyContractService
import br.com.alice.business.client.CompanySubContractService
import br.com.alice.common.core.exceptions.BadRequestException
import br.com.alice.common.core.exceptions.NotFoundException
import br.com.alice.common.extensions.coFoldError
import br.com.alice.common.extensions.coResultOf
import br.com.alice.common.extensions.flatMapPair
import br.com.alice.common.extensions.foldError
import br.com.alice.common.extensions.then
import br.com.alice.common.extensions.thenError
import br.com.alice.common.kafka.interfaces.KafkaProducerService
import br.com.alice.common.logging.logger
import br.com.alice.data.layer.models.FeatureNamespace
import br.com.alice.data.layer.models.InternalModelType
import br.com.alice.data.layer.models.MemberInvoiceGroup
import br.com.alice.featureconfig.core.FeatureService
import br.com.alice.moneyin.event.MemberInvoiceGroupPaidEvent
import br.com.alice.moneyin.event.PreActivationPaymentPaidEvent
import br.com.alice.nullvs.common.NullvsActionType
import br.com.alice.nullvs.consumers.AutoRetryableConsumer
import br.com.alice.nullvs.converters.NullvsInvoiceConverter.toNullvsInvoiceRequest
import br.com.alice.nullvs.converters.NullvsInvoiceConverter.toNullvsInvoiceRequestEvent
import br.com.alice.nullvs.events.NullvsInvoiceRequestEvent
import br.com.alice.nullvs.exceptions.DependsOnModelException
import br.com.alice.nullvs.exceptions.ExternalClientNotFound
import br.com.alice.nullvs.models.company.CompanyContractInfo
import br.com.alice.nullvs.services.internals.NullvsIntegrationRecordService
import com.github.kittinunf.result.Result
import com.github.kittinunf.result.failure
import com.github.kittinunf.result.flatMap
import com.github.kittinunf.result.map
import com.github.kittinunf.result.mapError
import com.github.kittinunf.result.success
import kotlinx.coroutines.async
import kotlinx.coroutines.coroutineScope
import java.util.UUID

class InvoiceConsumer(
    private val nullvsIntegrationRecordService: NullvsIntegrationRecordService,
    private val kafkaProducerService: KafkaProducerService,
    private val companySubContractService: CompanySubContractService,
    private val companyContractService: CompanyContractService,
) : AutoRetryableConsumer(ExternalClientNotFound::class) {

    suspend fun processPreActivationPayment(event: PreActivationPaymentPaidEvent) = withSubscribersEnvironment {
        coResultOf<Boolean, Throwable> {
            coroutineScope {
                logger.info(
                    "Creating NullvsInvoiceRequest from PreActivationPaymentPaidEvent",
                    "event" to event,
                )

                val preActivationPayment = event.payload.preActivationPayment
                val invoicePayment = event.payload.invoicePayment
                val billingAccountablePartyId = preActivationPayment.billingAccountablePartyId
                val subcontractId = preActivationPayment.companySubContractId

                val clientDef = async {
                    findClient(billingAccountablePartyId)
                        .mapError {
                            logger.error(
                                "InvoiceConsumer::processPreActivationPayment - error when tried to find the client record",
                                "pre_activation_payment_id" to preActivationPayment.id,
                                "invoice_payment_id" to invoicePayment.id,
                                "billing_accountable_party_id" to billingAccountablePartyId,
                                "company_id" to preActivationPayment.companyId,
                                "company_sub_contract_id" to preActivationPayment.companySubContractId,
                                "error_message" to it.message,
                                "error" to it,
                            )

                            it
                        }
                        .get()
                }

                val companyContractInfoDef = async {
                    getCompanyContractInfo(subcontractId)
                        .mapError {
                            logger.error(
                                "InvoiceConsumer::processPreActivationPayment - error when tried to find the contract info",
                                "pre_activation_payment_id" to preActivationPayment.id,
                                "invoice_payment_id" to invoicePayment.id,
                                "billing_accountable_party_id" to billingAccountablePartyId,
                                "company_id" to preActivationPayment.companyId,
                                "company_sub_contract_id" to preActivationPayment.companySubContractId,
                                "error_message" to it.message,
                                "error" to it,
                            )

                            it
                        }
                        .get()
                }

                val totvsClientRecord = clientDef.await()
                val companyContractInfo = companyContractInfoDef.await()

                val nullvsInvoiceRequest = preActivationPayment.toNullvsInvoiceRequest(
                    event = event,
                    invoicePayment = invoicePayment,
                    clientCode = totvsClientRecord.externalId,
                    actionType = NullvsActionType.CREATE,
                    companyContractInfo = companyContractInfo,
                )

                kafkaProducerService.produce(NullvsInvoiceRequestEvent(nullvsInvoiceRequest))

                true
            }
        }.foldError(DependsOnModelException::class to {
            true.success()
        })
    }

    suspend fun processFirstPaymentFromGroup(event: MemberInvoiceGroupPaidEvent) = withSubscribersEnvironment {
        logger.info(
            "Creating NullvsInvoiceRequest from MemberInvoiceGroupPaidEvent first payment",
            "event" to event,
        )

        val memberInvoiceGroup = event.payload.memberInvoiceGroup
        val invoicePayment = event.payload.invoicePayment
        val billingAccountablePartyId = memberInvoiceGroup.billingAccountablePartyId
        val subcontractId = memberInvoiceGroup.companySubcontractId

        checkFirstGroupPaymentEvent(event)
            .flatMap { findClient(billingAccountablePartyId) }
            .flatMapPair { getCompanyContractInfo(subcontractId) }
            .map { (companyContractInfo, totvsClientRecord) ->
                memberInvoiceGroup.toNullvsInvoiceRequest(
                    event,
                    invoicePayment,
                    clientCode = totvsClientRecord.externalId,
                    actionType = NullvsActionType.CREATE,
                    companyContractInfo = companyContractInfo,
                )
            }.map {
                logger.info(
                    "NullvsInvoiceRequest generated",
                    "payload" to it
                )
                kafkaProducerService.produce(NullvsInvoiceRequestEvent(it))
                true
            }.thenError {
                logger.error(
                    "Error processing MemberInvoiceGroupPaidEvent for first invoice paid",
                    "member_invoice_group_id" to memberInvoiceGroup.id,
                )
            }.foldError(
                IgnoreEventException::class to {
                    logger.info("Ignoring processFirstPaymentFromGroup consume event because it is not the first payment")
                    true.success()
                },
                DependsOnModelException::class to {
                    true.success()
                }
            )
    }

    suspend fun processRecurrentPaymentFromGroup(event: MemberInvoiceGroupPaidEvent) = withSubscribersEnvironment {
        logger.info(
            "Creating NullvsInvoiceRequest from MemberInvoiceGroupPaidEvent",
            "event" to event,
        )

        val memberInvoiceGroup = event.payload.memberInvoiceGroup
        val invoicePayment = event.payload.invoicePayment

        checkRecurrentGroupPaymentEvent(event)
            .flatMap { findClient(memberInvoiceGroup.billingAccountablePartyId) }
            .map { totvsClientRecord ->
                memberInvoiceGroup.toNullvsInvoiceRequestEvent(
                    event,
                    invoicePayment,
                    clientCode = totvsClientRecord.externalId,
                    actionType = NullvsActionType.UPDATE_PAYMENT,
                )
            }
            .map {
                logger.info(
                    "NullvsInvoiceRequest generated",
                    "payload" to it
                )

                kafkaProducerService.produce(NullvsInvoiceRequestEvent(it))
                true
            }.thenError {
                logger.error(
                    "Error processing MemberInvoiceGroupPaidEvent for recurrent invoice paid",
                    "member_invoice_group_id" to memberInvoiceGroup.id,
                )
            }.foldError(
                IgnoreEventException::class to {
                    logger.info("Ignoring processRecurrentPaymentFromGroup consume event for first payment")
                    true.success()
                },
                DependsOnModelException::class to {
                    true.success()
                }
            )
    }

    private fun shouldUseNullvsDependencySystem() =
        FeatureService.get(FeatureNamespace.NULLVS, "use_dependency_service", true)

    private suspend fun findClient(billingAccountablePartyId: UUID) =
        nullvsIntegrationRecordService.findByInternalIdAndModel(
            billingAccountablePartyId,
            InternalModelType.BILLING_ACCOUNTABLE_PARTY,
        ).coFoldError(NotFoundException::class to {
            if (shouldUseNullvsDependencySystem())
                DependsOnModelException(
                    billingAccountablePartyId,
                    InternalModelType.BILLING_ACCOUNTABLE_PARTY
                ).failure()
            else
                ExternalClientNotFound(billingAccountablePartyId).failure()
        }).then {
            logger.info(
                "The TOTVS client was found by BillingAccountableParty",
                "totvs_client" to it.externalId,
                "alice_billing_accountable_party_id" to it.internalId,
            )
        }.thenError {
            logger.warn(
                "The TOTVS client was not found by BillingAccoutableParty",
                "alice_billing_accountable_party_id" to billingAccountablePartyId,
            )
        }

    private suspend fun getCompanyContractInfo(subcontractId: UUID?) = subcontractId?.let {
        logger.info("Finding for the company contract info")
        companySubContractService.get(subcontractId)
            .flatMapPair {
                companyContractService.get(it.contractId)
            }.map { (contract, subcontract) ->
                CompanyContractInfo(
                    groupCompany = contract.groupCompany,
                    contractNumber = contract.externalId,
                    subcontractNumber = subcontract.externalId,
                )
            }
    } ?: CompanyContractInfo(
        groupCompany = null,
        contractNumber = null,
        subcontractNumber = null,
    ).success()

    private fun checkFirstGroupPaymentEvent(memberInvoiceGroupPaidEvent: MemberInvoiceGroupPaidEvent): Result<MemberInvoiceGroup, Throwable> {
        return if (memberInvoiceGroupPaidEvent.payload.invoicePayment.isFirstPayment) {
            memberInvoiceGroupPaidEvent.payload.memberInvoiceGroup.success()
        } else {
            logger.info("This is not the first group payment")
            IgnoreEventException("not first group invoice paid").failure()
        }
    }

    private fun checkRecurrentGroupPaymentEvent(memberInvoiceGroupPaidEvent: MemberInvoiceGroupPaidEvent): Result<MemberInvoiceGroup, Throwable> {
        return if (!memberInvoiceGroupPaidEvent.payload.invoicePayment.isFirstPayment) {
            memberInvoiceGroupPaidEvent.payload.memberInvoiceGroup.success()
        } else {
            logger.info("This is the first group payment")
            IgnoreEventException("first group invoice paid").failure()
        }
    }

    class IgnoreEventException(
        message: String,
        code: String = "ignore_invoice_paid_event",
        cause: Throwable? = null
    ) : BadRequestException(message, code, cause) {
        constructor(reason: String) : this(
            message = "Ignoring invoicePaidEvent due reason: $reason"
        )
    }
}
