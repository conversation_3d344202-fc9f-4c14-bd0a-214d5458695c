{"rules": [{"conditions": ["${subject.opaType} == Unauthenticated"], "allow": [{"resources": ["NationalReceiptModel", "InvoiceModel"], "actions": ["view", "update"]}, {"actions": ["view"], "resources": ["StaffModel", "ProviderUnitModel", "StructuredAddress", "ContactModel", "ExecIndicatorAuthorizerModel", "HealthProfessionalModel", "HealthProfessionalTierHistoryModel"]}, {"actions": ["create"], "resources": ["HealthProfessionalTierHistoryModel", "HealthProfessionalModel"]}]}]}