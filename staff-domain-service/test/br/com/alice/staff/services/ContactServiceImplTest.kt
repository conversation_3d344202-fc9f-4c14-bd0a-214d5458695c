package br.com.alice.staff.services

import br.com.alice.common.core.exceptions.NotFoundException
import br.com.alice.common.data.dsl.matchers.ResultAssert
import br.com.alice.common.data.dsl.matchers.queryEq
import br.com.alice.common.helpers.coVerifyNone
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.coverage.client.AddressService
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.ModalityType
import br.com.alice.data.layer.models.withAddress
import br.com.alice.data.layer.services.ContactModelDataService
import br.com.alice.staff.client.ContactService
import br.com.alice.staff.converters.toModel
import com.github.kittinunf.result.failure
import com.github.kittinunf.result.success
import io.mockk.coEvery
import io.mockk.mockk
import kotlinx.coroutines.runBlocking
import kotlin.test.Test


class ContactServiceImplTest {
    private val addressService: AddressService = mockk()
    private val contactService: ContactModelDataService = mockk()

    val service = ContactServiceImpl(
        addressService,
        contactService
    )
    private val address = TestModelFactory.buildStructuredAddress()
    private val presentialContact = TestModelFactory.buildContact(
        modality = ModalityType.PRESENTIAL,
        addressId = address.id
    ).withAddress(address)
    val presentialContactModel = presentialContact.toModel()

    private val remoteContact = TestModelFactory.buildContact(
        modality = ModalityType.REMOTE,
        addressId = null
    )
    private val remoteContactModel = remoteContact.toModel()

    @Test
    fun `get should return contact when presential contact`() = runBlocking {
        coEvery { contactService.get(presentialContact.id) } returns presentialContactModel.copy(address = null).success()
        coEvery { addressService.findById(address.id) } returns address.success()

        val result = service.get(presentialContact.id)
        ResultAssert.assertThat(result).isSuccessWithData(presentialContact)

        coVerifyOnce { contactService.get(any()) }
        coVerifyOnce { addressService.findById(any()) }
    }

    @Test
    fun `get should return contact when remote contact`() = runBlocking {
        coEvery { contactService.get(remoteContact.id) } returns remoteContactModel.success()

        val result = service.get(remoteContact.id)
        ResultAssert.assertThat(result).isSuccessWithData(remoteContact)

        coVerifyOnce { contactService.get(any()) }
        coVerifyNone { addressService.findById(any()) }
    }

    @Test
    fun `upsertWithAddress should update presential contact`() = runBlocking {
        coEvery { addressService.upsert(address) } returns address.success()
        coEvery { contactService.get(presentialContact.id) } returns presentialContactModel.copy(address = null).success()
        coEvery { contactService.update(presentialContactModel) } returns presentialContactModel.success()

        val result = service.upsertWithAddress(presentialContact, address)
        ResultAssert.assertThat(result).isSuccessWithData(presentialContact)

        coVerifyOnce { contactService.get(any()) }
        coVerifyOnce { addressService.upsert(any()) }
        coVerifyOnce { contactService.update(any()) }
    }

    @Test
    fun `upsertWithAddress should add presential contact`() = runBlocking {
        coEvery { addressService.upsert(address) } returns address.success()
        coEvery { contactService.get(presentialContact.id) } returns NotFoundException().failure()
        coEvery { contactService.add(presentialContactModel) } returns presentialContactModel.success()

        val result = service.upsertWithAddress(presentialContact, address)
        ResultAssert.assertThat(result).isSuccessWithData(presentialContact)

        coVerifyOnce { contactService.get(any()) }
        coVerifyOnce { addressService.upsert(any()) }
        coVerifyOnce { contactService.add(any()) }
    }

    @Test
    fun `upsertWithAddress should update remote contact`() = runBlocking {
        coEvery { contactService.get(remoteContact.id) } returns remoteContactModel.success()
        coEvery { contactService.update(remoteContactModel) } returns remoteContactModel.success()

        val result = service.upsertWithAddress(remoteContact, null)
        ResultAssert.assertThat(result).isSuccessWithData(remoteContact)

        coVerifyOnce { contactService.get(any()) }
        coVerifyOnce { contactService.update(any()) }
    }

    @Test
    fun `upsertWithAddress should add remote contact`() = runBlocking {
        coEvery { contactService.get(remoteContact.id) } returns NotFoundException().failure()
        coEvery { contactService.add(remoteContactModel) } returns remoteContactModel.success()

        val result = service.upsertWithAddress(remoteContact, null)
        ResultAssert.assertThat(result).isSuccessWithData(remoteContact)

        coVerifyOnce { contactService.get(any()) }
        coVerifyOnce { contactService.add(any()) }
    }

    @Test
    fun `inactiveWithAddress should inactive presential contact`() = runBlocking {
        coEvery { contactService.softDelete(presentialContactModel) } returns true.success()
        coEvery { addressService.inactiveById(address.id) } returns true.success()

        val result = service.inactiveWithAddress(presentialContact)
        ResultAssert.assertThat(result).isSuccessWithData(true)

        coVerifyOnce { contactService.softDelete(any()) }
        coVerifyOnce { addressService.inactiveById(any()) }
    }

    @Test
    fun `inactiveWithAddress should inactive remote contact`() = runBlocking {
        coEvery { contactService.softDelete(remoteContactModel) } returns true.success()

        val result = service.inactiveWithAddress(remoteContact)
        ResultAssert.assertThat(result).isSuccessWithData(true)

        coVerifyOnce { contactService.softDelete(any()) }
        coVerifyNone { addressService.inactiveById(any()) }
    }

    @Test
    fun `findByIds should return presential contact with address`() = runBlocking {
        coEvery { contactService.find(queryEq { where { this.id.inList(listOf(presentialContact.id)) } }) } returns listOf(
            presentialContactModel.copy(address = null)
        ).success()
        coEvery { addressService.findByIds(listOf(presentialContact.addressId!!)) } returns listOf(address).success()

        val result = service.findByIds(listOf(presentialContact.id), ContactService.FieldOptions(includeAddress = true))
        ResultAssert.assertThat(result).isSuccessWithData(listOf(presentialContact))

        coVerifyOnce { contactService.find(any()) }
        coVerifyOnce { addressService.findByIds(any()) }
    }

    @Test
    fun `findByIds should return presential contact without address`() = runBlocking {
        coEvery { contactService.find(queryEq { where { this.id.inList(listOf(presentialContactModel.id)) } }) } returns listOf(
            presentialContactModel.copy(address = null)
        ).success()

        val result =
            service.findByIds(listOf(presentialContact.id), ContactService.FieldOptions(includeAddress = false))
        ResultAssert.assertThat(result).isSuccessWithData(listOf(presentialContact.copy(address = null)))

        coVerifyOnce { contactService.find(any()) }
        coVerifyNone { addressService.findByIds(any()) }
    }

    @Test
    fun `findByIds should return remote contact`() = runBlocking {
        coEvery { contactService.find(queryEq { where { this.id.inList(listOf(remoteContact.id)) } }) } returns listOf(
            remoteContactModel
        ).success()

        val result = service.findByIds(listOf(remoteContact.id), ContactService.FieldOptions(includeAddress = true))
        ResultAssert.assertThat(result).isSuccessWithData(listOf(remoteContact))

        coVerifyOnce { contactService.find(any()) }
        coVerifyNone { addressService.findByIds(any()) }
    }
}
