package br.com.alice.api.ehr.controllers

import br.com.alice.api.ehr.converters.CaseRecordDetailResponseConverter
import br.com.alice.api.ehr.model.CaseRecordDetailResponse
import br.com.alice.api.ehr.model.CaseRecordEditRequest
import br.com.alice.api.ehr.model.CaseRecordLightEditRequest
import br.com.alice.api.ehr.model.CaseRecordLightRequest
import br.com.alice.api.ehr.model.CaseRecordRequest
import br.com.alice.common.ErrorResponse
import br.com.alice.common.RangeUUID
import br.com.alice.common.Response
import br.com.alice.common.core.PersonId
import br.com.alice.common.core.Role.CARE_COORD_NURSE
import br.com.alice.common.core.Role.Companion.physicianRoles
import br.com.alice.common.core.exceptions.InvalidArgumentException
import br.com.alice.common.core.extensions.toUUID
import br.com.alice.common.extensions.flatMapPair
import br.com.alice.common.extensions.resultOf
import br.com.alice.common.foldResponse
import br.com.alice.common.getErrorMessage
import br.com.alice.common.toResponse
import br.com.alice.data.layer.models.CaseRecord
import br.com.alice.data.layer.models.CaseRecordReference
import br.com.alice.data.layer.models.CaseRecordReferenceModel
import br.com.alice.data.layer.models.CaseSeverity
import br.com.alice.data.layer.models.CaseStatus
import br.com.alice.data.layer.models.CaseStatus.ACTIVE
import br.com.alice.data.layer.models.CaseStatus.CANCELLED
import br.com.alice.data.layer.models.CaseStatus.PENDING
import br.com.alice.common.Disease
import br.com.alice.common.Disease.Type.CIAP_2
import br.com.alice.common.Disease.Type.CID_10
import br.com.alice.common.Disease.Type.GOAL
import br.com.alice.healthcondition.client.CaseRecordService
import br.com.alice.healthcondition.client.DuplicatedDescriptionException
import br.com.alice.staff.client.StaffService
import com.github.kittinunf.result.failure
import com.github.kittinunf.result.flatMap
import com.github.kittinunf.result.getOrElse
import com.github.kittinunf.result.map
import com.github.kittinunf.result.success
import io.ktor.http.Parameters
import java.time.LocalDateTime
import java.util.UUID

class CaseRecordController(
    private val staffService: StaffService,
    private val caseRecordService: CaseRecordService
) : StaffController(staffService) {

    private val rolesThatCanCreateAndActivateCIDs = (physicianRoles() + CARE_COORD_NURSE)

    suspend fun getCaseRecords(personId: PersonId) =
        caseRecordService.getCasesByPersonId(personId)
            .map { records -> records.map { enhanceWithStaffInfo(listOf(it)) } }
            .foldResponse()

    @Deprecated("Migrate to HealthDemandController")
    suspend fun create(personId: PersonId, caseRecord: CaseRecordRequest) =
        caseRecordService.add(
            CaseRecord(
                personId = personId,
                caseId = RangeUUID.generate(),
                addedByStaffId = currentStaffId(),
                responsibleStaffId = caseRecord.responsibleStaffId,
                description = caseRecord.description,
                observation = caseRecord.observation,
                severity = caseRecord.severity,
                status = validateCreationStatus(caseRecord.description.type),
                addedAt = LocalDateTime.now(),
                startedAt = caseRecord.startedAt ?: LocalDateTime.now()
            )
        ).map { enhanceWithStaffInfo(listOf(it)) }.foldResponse(
            { it },
            DuplicatedDescriptionException::class to {
                it as DuplicatedDescriptionException
                ErrorResponse(it.code, it.getErrorMessage(it.code, true))
            }
        )

    @Deprecated("Migrate to HealthDemandController")
    suspend fun createFromReference(
        personId: PersonId,
        referenceModel: String,
        referenceId: String,
        request: CaseRecordLightRequest
    ): Response = createCaseRecordReference(referenceId, referenceModel)
        .flatMap {
            caseRecordService.addFromReference(
                CaseRecord(
                    personId = personId,
                    caseId = RangeUUID.generate(),
                    addedByStaffId = currentStaffId(),
                    responsibleStaffId = currentStaffId(),
                    description = request.description,
                    observation = "",
                    severity = request.severity,
                    status = validateCreationStatus(request.description.type),
                    addedAt = LocalDateTime.now(),
                    startedAt = LocalDateTime.now()
                ),
                it
            )
        }.flatMap { getCaseDetailEnhanced(it.caseId) }.foldResponse()

    @Deprecated("Migrate to HealthDemandController")
    suspend fun editFromReference(
        personId: PersonId,
        caseId: UUID,
        referenceModel: String,
        referenceId: String,
        request: CaseRecordLightEditRequest
    ): Response {
        return createCaseRecordReference(referenceId, referenceModel)
            .flatMapPair { caseRecordService.getCaseByPersonId(personId, caseId) }
            .flatMap { containsReference(it.first, it.second) }
            .map {
                CaseRecord(
                    personId = it.personId,
                    severity = request.severity,
                    addedAt = LocalDateTime.now(),
                    addedByStaffId = currentStaffId(),
                    referencedLinks = it.referencedLinks,
                    status = it.status,
                    caseId = it.caseId,
                    responsibleStaffId = it.responsibleStaffId,
                    description = it.description,
                    observation = "",
                    startedAt = it.startedAt,
                )
            }.flatMap { caseRecordService.add(it) }
            .flatMap { getCaseDetailEnhanced(it.caseId) }
            .foldResponse()
    }

    @Deprecated("Migrate to HealthDemandController")
    suspend fun deleteFromReference(
        personId: PersonId,
        caseId: UUID,
        referenceModel: String,
        referenceId: String,
    ) = createCaseRecordReference(referenceId, referenceModel)
        .flatMapPair { caseRecordService.getCaseByPersonId(personId, caseId) }
        .flatMap { getBasedOnReferenceRemoval(it.first, it.second) }
        .flatMap { caseRecordService.add(it) }
        .map { enhanceWithStaffInfo(listOf(it)) }
        .foldResponse(
            { it },
            DuplicatedDescriptionException::class to {
                it as DuplicatedDescriptionException
                ErrorResponse(it.code, it.getErrorMessage(it.code, true))
            }
        )

    private suspend fun getBasedOnReferenceRemoval(
        caseRecord: CaseRecord,
        reference: CaseRecordReference
    ) = containsReference(caseRecord, reference)
        .map {
            val newRecord = CaseRecord(
                referencedLinks = it.referencedLinks.subtract(listOf(reference)).toList(),
                addedAt = LocalDateTime.now(),
                addedByStaffId = currentStaffId(),
                status = it.status,
                personId = it.personId,
                caseId = it.caseId,
                responsibleStaffId = it.responsibleStaffId,
                description = it.description,
                severity = it.severity,
                startedAt = it.startedAt,
            )
            if (newRecord.referencedLinks.isEmpty() && wasCreatedByReference(caseRecord.caseId, reference)) newRecord.copy(status = CANCELLED)
            else newRecord
        }

    private suspend fun wasCreatedByReference(caseId: UUID, reference: CaseRecordReference) =
        caseRecordService.getDetail(caseId).flatMap {
            val firstRecord = it.last()
            containsReference(firstRecord, reference).map { true }
        }.getOrElse { false }

    private fun createCaseRecordReference(
        referenceId: String,
        referenceModel: String
    ) = resultOf<CaseRecordReference, Throwable> {
        CaseRecordReference(
            referenceId,
            CaseRecordReferenceModel.valueOf(referenceModel)
        )
    }

    private fun containsReference(caseRecord: CaseRecord, reference: CaseRecordReference) =
        if (caseRecord.referencedLinks.contains(reference)) caseRecord.success()
        else InvalidArgumentException(message = "Case without reference", code = "case_without_referecne").failure()

    @Deprecated("Migrate to HealthDemandController")
    suspend fun delete(personId: PersonId, caseId: String) =
        caseRecordService.getCaseByPersonId(personId, caseId.toUUID())
            .map {
                CaseRecord(
                    personId = it.personId,
                    caseId = it.caseId,
                    addedByStaffId = currentStaffId(),
                    responsibleStaffId = it.responsibleStaffId,
                    description = it.description,
                    observation = "",
                    severity = it.severity,
                    status = CANCELLED,
                    addedAt = LocalDateTime.now(),
                    startedAt = it.startedAt
                )
            }
            .flatMap { caseRecordService.add(it) }
            .map { enhanceWithStaffInfo(listOf(it)) }.foldResponse()

    @Deprecated("Migrate to HealthDemandController")
    suspend fun edit(personId: PersonId, caseId: UUID, editRequest: CaseRecordEditRequest) =
        caseRecordService.getCaseByPersonId(personId, caseId)
            .map {
                CaseRecord(
                    personId = it.personId,
                    caseId = it.caseId,
                    addedByStaffId = currentStaffId(),
                    responsibleStaffId = editRequest.responsibleStaffId,
                    description = editRequest.description,
                    observation = editRequest.observation,
                    severity = editRequest.severity,
                    status = validateUpdateStatus(it, editRequest),
                    addedAt = LocalDateTime.now(),
                    startedAt = editRequest.startedAt
                )
            }
            .flatMap { caseRecordService.add(it) }
            .flatMap { getCaseDetailEnhanced(it.caseId) }
            .foldResponse(
                { it },
                DuplicatedDescriptionException::class to {
                    it as DuplicatedDescriptionException
                    ErrorResponse(it.code, it.getErrorMessage(it.code, true))
                }
            )

    suspend fun getCaseDetail(caseId: UUID) = getCaseDetailEnhanced(caseId).foldResponse()

    @Deprecated("Migrate to HealthDemandController")
    suspend fun getCaseRecordsByFilters(personId: PersonId, parameters: Parameters): Response {
        val type = parseFilter<String>(parameters, "type")

        val cases = if (type != null) caseRecordService.getByDiseaseType(
            personId,
            if (type.lowercase() == "condition") listOf(CID_10, CIAP_2) else listOf(GOAL)
        )
        else caseRecordService.getCasesByPersonId(personId)

        return cases.get().sortedWith { first, second ->
            if (first.description.type == GOAL && second.description.type != GOAL) -1
            else if (first.description.type != GOAL && second.description.type == GOAL) 1
            else if (first.severity == CaseSeverity.DECOMPENSATED && second.severity != CaseSeverity.DECOMPENSATED) -1
            else if (first.severity != CaseSeverity.DECOMPENSATED && second.severity == CaseSeverity.DECOMPENSATED) 1
            else second.addedAt.compareTo(first.addedAt)
        }.map {
            enhanceWithStaffInfo(listOf(it))
        }.toResponse()
    }

    private suspend fun getCaseDetailEnhanced(caseId: UUID) =
        caseRecordService.getDetail(caseId)
            .map { enhanceWithStaffInfo(it) }

    private suspend fun enhanceWithStaffInfo(caseRecords: List<CaseRecord>): CaseRecordDetailResponse {
        val staffIds = caseRecords.flatMap { it.staffIds }.filterNotNull().distinct()
        val staffs = staffService.findByList(staffIds).getOrElse { emptyList() }.associateBy { it.id }
        return CaseRecordDetailResponseConverter.convert(caseRecords, staffs)
    }

    private suspend fun validateCreationStatus(type: Disease.Type) =
        if (type == CIAP_2 || type == GOAL) ACTIVE
        else defineStatusBasedOnRole()

    private suspend fun validateUpdateStatus(previous: CaseRecord, new: CaseRecordEditRequest): CaseStatus =
        if (previous.status == PENDING && new.status == ACTIVE) {
            defineStatusBasedOnRole()
        } else if (previous.description != new.description) {
            validateCreationStatus(new.description.type)
        } else new.status

    private suspend fun defineStatusBasedOnRole() =
        currentStaffRoles().let { if (currentStaffRoles().any(rolesThatCanCreateAndActivateCIDs::contains)) ACTIVE else PENDING }
}
