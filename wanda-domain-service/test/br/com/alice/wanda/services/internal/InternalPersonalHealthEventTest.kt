package br.com.alice.wanda.services.internal

import br.com.alice.common.RangeUUID
import br.com.alice.common.core.exceptions.NotFoundException
import br.com.alice.common.core.extensions.atBeginningOfTheDay
import br.com.alice.common.core.extensions.atEndOfTheDay
import br.com.alice.common.core.extensions.toUUID
import br.com.alice.common.data.dsl.matchers.ResultAssert.Companion.assertThat
import br.com.alice.common.data.dsl.matchers.queryEq
import br.com.alice.common.helpers.coVerifyNone
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.common.helpers.mockLocalDateTime
import br.com.alice.common.kafka.interfaces.KafkaProducerService
import br.com.alice.common.service.data.dsl.and
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.Attachment
import br.com.alice.data.layer.models.PersonHealthEvent
import br.com.alice.data.layer.models.PersonHealthEventCategory
import br.com.alice.data.layer.models.PersonHealthEventReferenceModel.APPOINTMENT
import br.com.alice.data.layer.models.PersonHealthEventReferenceModel.APP_AVAILABLE
import br.com.alice.data.layer.models.PersonHealthEventReferenceModel.CASE_RECORD
import br.com.alice.data.layer.models.PersonHealthEventReferenceModel.SCHEDULING
import br.com.alice.data.layer.models.PersonHealthEventStatus.CANCELLED
import br.com.alice.data.layer.models.PersonHealthEventStatus.FINISHED
import br.com.alice.data.layer.models.PersonHealthEventStatus.IN_PROGRESS
import br.com.alice.data.layer.models.PersonHealthEventStatus.NOT_STARTED
import br.com.alice.data.layer.models.PersonHealthEventStatus.NO_SHOW
import br.com.alice.data.layer.models.PersonHealthEventUpdatedBy
import br.com.alice.data.layer.models.PersonHealthEventUpdatedByType
import br.com.alice.data.layer.models.ReferencedLink
import br.com.alice.data.layer.services.PersonHealthEventDataService
import br.com.alice.wanda.event.PersonHealthEventCreatedEvent
import br.com.alice.wanda.event.PersonHealthEventUpdatedEvent
import br.com.alice.wanda.logics.PersonHealthEventLogic
import com.github.kittinunf.result.failure
import com.github.kittinunf.result.success
import io.mockk.called
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.confirmVerified
import io.mockk.every
import io.mockk.mockk
import io.mockk.mockkObject
import kotlinx.coroutines.runBlocking
import org.assertj.core.api.Assertions.assertThat
import java.time.LocalDate
import java.time.LocalDateTime
import kotlin.test.AfterTest
import kotlin.test.Test

class InternalPersonHealthEventServiceTest {

    private val personHealthEventDataService: PersonHealthEventDataService = mockk()

    private val kafkaProducerService: KafkaProducerService = mockk()

    private val personHealthEventService = InternalPersonHealthEventService(
        personHealthEventDataService,
        kafkaProducerService
    )

    private val personHealthEvent = TestModelFactory.buildPersonHealthEvent(
        referencedModelClass = SCHEDULING,
        referencedModelId = "referencedModelId",
        eventDate = LocalDateTime.now().minusHours(10),
    )
    private val insertedPersonHealthEvent = TestModelFactory.buildPersonHealthEvent()

    @AfterTest
    fun confirmMocks() = confirmVerified(
        personHealthEventDataService,
        kafkaProducerService
    )

    @Test
    fun `#upsertPersonHealthEvent should add PersonHealthEvent when it does not exist`() = runBlocking {
        coEvery {
            personHealthEventDataService.findOne(
                queryEq {
                    where {
                        referencedModelId.eq("referencedModelId")
                            .and(referencedModelClass.eq(SCHEDULING))
                    }
                }
            )
        } returns NotFoundException().failure()
        coEvery { personHealthEventDataService.add(personHealthEvent) } returns personHealthEvent.success()
        coEvery { kafkaProducerService.produce(PersonHealthEventCreatedEvent(personHealthEvent)) } returns mockk()

        val result = personHealthEventService.upsertPersonHealthEvent(personHealthEvent, updateOnlyIfNewer = true)
        assertThat(result).isSuccessWithData(personHealthEvent)

        coVerifyOnce { personHealthEventDataService.findOne(any()) }
        coVerifyOnce { personHealthEventDataService.add(any()) }
        coVerifyOnce { kafkaProducerService.produce(any()) }
        coVerifyNone { personHealthEventDataService.update(any()) }
    }

    @Test
    fun `#upsertPersonHealthEvent should update PersonHealthEvent when it exists and it is before`() =
        mockLocalDateTime { now ->
            mockkObject(PersonHealthEventLogic) {
                val insertedPersonHealthEvent = insertedPersonHealthEvent.copy(
                    eventDate = personHealthEvent.eventDate.minusMinutes(1)
                )

                coEvery {
                    personHealthEventDataService.findOne(
                        queryEq {
                            where {
                                referencedModelId.eq("referencedModelId")
                                    .and(referencedModelClass.eq(SCHEDULING))
                            }
                        }
                    )
                } returns insertedPersonHealthEvent.success()
                every {
                    PersonHealthEventLogic.updatePersonHealthEvent(
                        insertedPersonHealthEvent,
                        personHealthEvent,
                        now,
                        true
                    )
                } returns personHealthEvent
                coEvery { personHealthEventDataService.update(personHealthEvent) } returns personHealthEvent.success()

                val result =
                    personHealthEventService.upsertPersonHealthEvent(personHealthEvent, updateOnlyIfNewer = true)
                assertThat(result).isSuccessWithData(personHealthEvent)

                coVerifyOnce { personHealthEventDataService.findOne(any()) }
                coVerifyNone { personHealthEventDataService.add(any()) }
                coVerifyOnce { personHealthEventDataService.update(any()) }
                coVerify { kafkaProducerService wasNot called }
            }
        }

    @Test
    fun `#upsertPersonHealthEvent should not update PersonHealthEvent when it exists and it is after`() = runBlocking {
        val insertedPersonHealthEvent = insertedPersonHealthEvent.copy(
            eventDate = personHealthEvent.eventDate.plusMinutes(1)
        )

        coEvery {
            personHealthEventDataService.findOne(
                queryEq {
                    where {
                        referencedModelId.eq("referencedModelId").and(referencedModelClass.eq(SCHEDULING))
                    }
                }
            )
        } returns insertedPersonHealthEvent.success()

        val result = personHealthEventService.upsertPersonHealthEvent(personHealthEvent, updateOnlyIfNewer = true)
        assertThat(result).isSuccessWithData(insertedPersonHealthEvent)

        coVerifyOnce { personHealthEventDataService.findOne(any()) }
        coVerifyNone { personHealthEventDataService.add(any()) }
        coVerifyNone { personHealthEventDataService.update(any()) }
        coVerify { kafkaProducerService wasNot called }
    }

    @Test
    fun `#upsertPersonHealthEvent - should not update when it exists and it is at same time`() = runBlocking {
        val insertedPersonHealthEvent = insertedPersonHealthEvent.copy(eventDate = personHealthEvent.eventDate)

        coEvery {
            personHealthEventDataService.findOne(
                queryEq {
                    where {
                        referencedModelId.eq("referencedModelId")
                            .and(referencedModelClass.eq(SCHEDULING))
                    }
                }
            )
        } returns insertedPersonHealthEvent.success()

        val result = personHealthEventService.upsertPersonHealthEvent(personHealthEvent, updateOnlyIfNewer = true)
        assertThat(result).isSuccessWithData(insertedPersonHealthEvent)

        coVerifyOnce { personHealthEventDataService.findOne(any()) }
        coVerifyNone { personHealthEventDataService.add(any()) }
        coVerifyNone { personHealthEventDataService.update(any()) }
        coVerify { kafkaProducerService wasNot called }
    }

    @Test
    fun `#upsertPersonHealthEvent - should update when it exists at same time, but with different attachments`() =
        runBlocking {
            val attachmentId = "db3094ba-36a6-4652-b7d3-2f06140e7050".toUUID()
            val anotherAttachmentId = "db3094ba-36a6-4652-b7d3-2f06140e7060".toUUID()
            val insertedPersonHealthEvent = personHealthEvent.copy(
                attachments = listOf(
                    Attachment(
                        id = attachmentId,
                        type = "type",
                        fileName = "attachment1"
                    )
                )
            )
            val personHealthEventToUpdate = personHealthEvent.copy(
                attachments = listOf(
                    Attachment(
                        id = attachmentId,
                        type = "type",
                        fileName = "file_name"
                    ),
                    Attachment(
                        id = anotherAttachmentId,
                        type = "type",
                        fileName = "attachment2"
                    )
                )
            )

            val finalPersonHealthEvent = personHealthEvent.copy(
                attachments = insertedPersonHealthEvent.attachments + personHealthEventToUpdate.attachments
            )

            coEvery {
                personHealthEventDataService.findOne(
                    queryEq {
                        where {
                            referencedModelId.eq("referencedModelId")
                                .and(referencedModelClass.eq(SCHEDULING))
                        }
                    }
                )
            } returns insertedPersonHealthEvent.success()

            coEvery {
                personHealthEventDataService.update(
                    match {
                        it.id == personHealthEventToUpdate.id &&
                                it.attachments.map { attachment -> attachment.id }
                                    .containsAll(listOf(attachmentId, anotherAttachmentId))
                    }
                )
            } returns finalPersonHealthEvent.success()

            val result = personHealthEventService.upsertPersonHealthEvent(
                personHealthEventToUpdate,
                updateOnlyIfNewer = true
            )
            assertThat(result).isSuccessWithData(finalPersonHealthEvent)

            coVerifyOnce { personHealthEventDataService.findOne(any()) }
            coVerifyNone { personHealthEventDataService.add(any()) }
            coVerifyOnce { personHealthEventDataService.update(any()) }
            coVerify { kafkaProducerService wasNot called }
        }

    @Test
    fun `#upsertPersonHealthEvent - should update when it exists at same time, but with different titles`() =
        runBlocking {
            val expectedTitle = "${personHealthEvent.title}_"
            val personHealthEventToUpdate = personHealthEvent.copy(
                id = insertedPersonHealthEvent.id,
                title = expectedTitle
            )

            coEvery {
                personHealthEventDataService.findOne(
                    queryEq {
                        where {
                            referencedModelId.eq("referencedModelId")
                                .and(referencedModelClass.eq(SCHEDULING))
                        }
                    }
                )
            } returns insertedPersonHealthEvent.success()

            coEvery {
                personHealthEventDataService.update(
                    match {
                        it.id == personHealthEventToUpdate.id &&
                                it.title == personHealthEventToUpdate.title
                    }
                )
            } returns personHealthEventToUpdate.success()

            val result = personHealthEventService.upsertPersonHealthEvent(
                personHealthEventToUpdate,
                updateOnlyIfNewer = true
            )
            assertThat(result).isSuccessWithData(personHealthEventToUpdate)

            coVerifyOnce { personHealthEventDataService.findOne(any()) }
            coVerifyNone { personHealthEventDataService.add(any()) }
            coVerifyOnce { personHealthEventDataService.update(any()) }
            coVerify { kafkaProducerService wasNot called }
        }

    @Test
    fun `#upsertPersonHealthEvent - should update when it exists at same time, but with different referencedLinks`() =
        runBlocking {
            val linkId = "LINK_ID"
            val anotherLinkId = "ANOTHER_LINK_ID"
            val insertedPersonHealthEvent = personHealthEvent.copy(
                referencedLinks = listOf(
                    ReferencedLink(
                        id = linkId,
                        model = "video_call_url"
                    )
                )
            )
            val personHealthEventToUpdate = personHealthEvent.copy(
                referencedLinks = listOf(
                    ReferencedLink(
                        id = linkId,
                        model = "video_call_url"
                    ),
                    ReferencedLink(
                        id = anotherLinkId,
                        model = "video_call_url"
                    )
                )
            )

            val finalPersonHealthEvent = personHealthEvent.copy(
                referencedLinks = insertedPersonHealthEvent.referencedLinks.union(personHealthEventToUpdate.referencedLinks)
                    .toList()
            )

            coEvery {
                personHealthEventDataService.findOne(
                    queryEq {
                        where {
                            referencedModelId.eq("referencedModelId")
                                .and(referencedModelClass.eq(SCHEDULING))
                        }
                    }
                )
            } returns insertedPersonHealthEvent.success()

            coEvery {
                personHealthEventDataService.update(
                    match {
                        it.id == personHealthEventToUpdate.id &&
                                it.referencedLinks.map { link -> link.id }
                                    .containsAll(listOf(linkId, anotherLinkId))
                    }
                )
            } returns finalPersonHealthEvent.success()

            val result = personHealthEventService.upsertPersonHealthEvent(
                personHealthEventToUpdate,
                updateOnlyIfNewer = true
            )
            assertThat(result).isSuccessWithData(finalPersonHealthEvent)

            coVerifyOnce { personHealthEventDataService.findOne(any()) }
            coVerifyNone { personHealthEventDataService.add(any()) }
            coVerifyOnce { personHealthEventDataService.update(any()) }
            coVerify { kafkaProducerService wasNot called }
        }

    @Test
    fun `#updatePersonHealthEventById should update PersonHealthEvent when it exists`() = mockLocalDateTime { now ->
        mockkObject(PersonHealthEventLogic) {
            coEvery { personHealthEventDataService.get(personHealthEvent.id) } returns insertedPersonHealthEvent.success()
            every {
                PersonHealthEventLogic.updatePersonHealthEvent(
                    insertedPersonHealthEvent,
                    personHealthEvent,
                    now,
                    false
                )
            } returns personHealthEvent
            coEvery { personHealthEventDataService.update(personHealthEvent) } returns personHealthEvent.success()

            val result = personHealthEventService.updatePersonHealthEventById(personHealthEvent)
            assertThat(result).isSuccessWithData(personHealthEvent)

            coVerifyOnce { personHealthEventDataService.get(any()) }
            coVerifyOnce { personHealthEventDataService.update(any()) }
            coVerify { kafkaProducerService wasNot called }
        }
    }

    @Test
    fun `#updatePersonHealthEventById should not update PersonHealthEvent when it exists`() = runBlocking {
        coEvery { personHealthEventDataService.get(personHealthEvent.id) } returns NotFoundException().failure()

        val result = personHealthEventService.updatePersonHealthEventById(personHealthEvent)
        assertThat(result).isFailureOfType(NotFoundException::class)

        coVerifyOnce { personHealthEventDataService.get(any()) }
        coVerifyNone { personHealthEventDataService.update(any()) }
        coVerify { kafkaProducerService wasNot called }
    }

    @Test
    fun `#updateListPersonHealthEvents should call data service after chunk the list limited by 50 elements`() =
        runBlocking {
            val events = mutableListOf<PersonHealthEvent>()
            repeat(60) { events.add(personHealthEvent) }

            val chunked = events.chunked(50)

            coEvery { personHealthEventDataService.updateList(chunked.first()) } returns chunked.first().success()
            coEvery { personHealthEventDataService.updateList(chunked.last()) } returns chunked.last().success()

            val result = personHealthEventService.updateListPersonHealthEvents(events)
            assertThat(result).isEqualTo(events)

            coVerify(exactly = 2) { personHealthEventDataService.updateList(any()) }
            coVerify { kafkaProducerService wasNot called }
        }

    @Test
    fun `#cancelPersonHealthEventByReference`() = mockLocalDateTime { now ->
        val newPersonHealthEvent = personHealthEvent.copy(status = CANCELLED, finishedAt = now)

        coEvery {
            personHealthEventDataService.findOne(
                queryEq {
                    where { referencedModelId.eq("modelId") and referencedModelClass.eq(APPOINTMENT) }
                }
            )
        } returns personHealthEvent.success()
        coEvery { personHealthEventDataService.update(newPersonHealthEvent) } returns newPersonHealthEvent.success()
        coEvery {
            kafkaProducerService.produce(
                match { event: PersonHealthEventUpdatedEvent ->
                    event.payload.personHealthEvent == newPersonHealthEvent
                }
            )
        } returns mockk()

        val result = personHealthEventService.cancelPersonHealthEventByReference("modelId", APPOINTMENT)
        assertThat(result).isSuccessWithData(newPersonHealthEvent)

        coVerifyOnce { personHealthEventDataService.findOne(any()) }
        coVerifyOnce { personHealthEventDataService.update(any()) }
        coVerifyOnce { kafkaProducerService.produce(any()) }
    }

    @Test
    fun `#cancelPersonHealthEventByReference with NO_SHOW status`() = runBlocking {
        val newPersonHealthEvent = personHealthEvent.copy(status = NO_SHOW, finishedAt = null)

        coEvery {
            personHealthEventDataService.findOne(
                queryEq {
                    where { referencedModelId.eq("modelId") and referencedModelClass.eq(APPOINTMENT) }
                }
            )
        } returns personHealthEvent.success()
        coEvery { personHealthEventDataService.update(newPersonHealthEvent) } returns newPersonHealthEvent.success()
        coEvery {
            kafkaProducerService.produce(
                match { event: PersonHealthEventUpdatedEvent ->
                    event.payload.personHealthEvent == newPersonHealthEvent
                }
            )
        } returns mockk()

        val result = personHealthEventService.cancelPersonHealthEventByReference("modelId", APPOINTMENT, NO_SHOW)
        assertThat(result).isSuccessWithData(newPersonHealthEvent)

        coVerifyOnce { personHealthEventDataService.findOne(any()) }
        coVerifyOnce { personHealthEventDataService.update(any()) }
        coVerifyOnce { kafkaProducerService.produce(any()) }
    }

    @Test
    fun `#findAppAvailableByPersonId should return one PersonHealthEvent when it exists`() = runBlocking {
        coEvery {
            personHealthEventDataService.findOneOrNull(
                queryEq {
                    where {
                        personId.eq(personHealthEvent.personId)
                            .and(referencedModelClass.eq(APP_AVAILABLE))
                    }
                }
            )
        } returns personHealthEvent

        val result = personHealthEventService.findAppAvailableByPersonId(personHealthEvent.personId)
        assertThat(result).isEqualTo(personHealthEvent)

        coVerifyOnce { personHealthEventDataService.findOneOrNull(any()) }
        coVerify { kafkaProducerService wasNot called }
    }

    @Test
    fun `#findAppAvailableByPersonId should return null when a PersonHealthEvent is not found`() = runBlocking {
        coEvery {
            personHealthEventDataService.findOneOrNull(
                queryEq {
                    where {
                        personId.eq(personHealthEvent.personId)
                            .and(referencedModelClass.eq(APP_AVAILABLE))
                    }
                }
            )
        } returns null

        val result = personHealthEventService.findAppAvailableByPersonId(personHealthEvent.personId)
        assertThat(result).isNull()

        coVerifyOnce { personHealthEventDataService.findOneOrNull(any()) }
        coVerify { kafkaProducerService wasNot called }
    }

    @Test
    fun `#findByCategoryPersonIdAndTime returns null when a PersonHealthEvent is not found`() = runBlocking {
        coEvery {
            personHealthEventDataService.findOneOrNull(
                queryEq {
                    where {
                        this.personId.eq(personHealthEvent.personId) and
                                this.category.eq(personHealthEvent.category) and
                                eventDate.greater(personHealthEvent.createdAt) and
                                status.inList(listOf(NOT_STARTED, IN_PROGRESS))
                    }
                }
            )
        } returns null

        val result = personHealthEventService.findByCategoryPersonIdAndTime(
            personId = personHealthEvent.personId,
            category = personHealthEvent.category,
            from = personHealthEvent.createdAt
        )
        assertThat(result).isNull()

        coVerifyOnce { personHealthEventDataService.findOneOrNull(any()) }
        coVerify { kafkaProducerService wasNot called }
    }

    @Test
    fun `#findByCategoryPersonIdAndTime returns PersonHealthEvent when is found`() = runBlocking {
        coEvery {
            personHealthEventDataService.findOneOrNull(
                queryEq {
                    where {
                        this.personId.eq(personHealthEvent.personId) and
                                this.category.eq(personHealthEvent.category) and
                                eventDate.greater(personHealthEvent.createdAt) and
                                status.inList(listOf(NOT_STARTED, IN_PROGRESS))
                    }
                }
            )
        } returns personHealthEvent

        val result = personHealthEventService.findByCategoryPersonIdAndTime(
            personId = personHealthEvent.personId,
            category = personHealthEvent.category,
            from = personHealthEvent.createdAt
        )
        assertThat(result).isEqualTo(personHealthEvent)

        coVerifyOnce { personHealthEventDataService.findOneOrNull(any()) }
        coVerify { kafkaProducerService wasNot called }
    }

    @Test
    fun `#countAutomaticFollowUpsForCurrentDay should return number of automatic fups for today`() = runBlocking {
        coEvery {
            personHealthEventDataService.count(
                queryEq {
                    where {
                        this.automaticFollowUp.eq(true) and
                                this.status.eq(NOT_STARTED) and
                                this.dueDate.greater(LocalDate.now().atBeginningOfTheDay()) and
                                this.dueDate.less(LocalDate.now().atEndOfTheDay())
                    }
                }
            )
        } returns 1.success()

        val result = personHealthEventService.countAutomaticFollowUpsForCurrentDay()
        assertThat(result).isSuccessWithData(1)

        coVerifyOnce { personHealthEventDataService.count(any()) }
        coVerify { kafkaProducerService wasNot called }
    }

    @Test
    fun `#findAutomaticFollowUpsBatch should return automatic fups batch`() = runBlocking {
        val batch = 1

        coEvery {
            personHealthEventDataService.find(
                queryEq {
                    where {
                        this.automaticFollowUp.eq(true) and
                                this.status.eq(NOT_STARTED) and
                                this.dueDate.greater(LocalDate.now().atBeginningOfTheDay()) and
                                this.dueDate.less(LocalDate.now().atEndOfTheDay())
                    }.limit { batch }
                }
            )
        } returns listOf(personHealthEvent).success()

        val result = personHealthEventService.findAutomaticFollowUpsBatch(batch)
        assertThat(result).isSuccessWithData(listOf(personHealthEvent))

        coVerifyOnce { personHealthEventDataService.find(any()) }
        coVerify { kafkaProducerService wasNot called }
    }

    @Test
    fun `#findAutomaticFollowUpsByPersonIdAndChannelId should return list of PersonHealthEvent`() =
        mockLocalDateTime { now ->
            val channelId = "channelId"
            val personId = personHealthEvent.personId
            val automaticFollowUp = personHealthEvent.copy(
                status = NOT_STARTED,
                automaticFollowUp = true,
                dueDate = LocalDateTime.now().plusDays(1),
                referencedLinks = listOf(ReferencedLink(id = channelId, model = "Channel"))
            )

            coEvery {
                personHealthEventDataService.find(
                    queryEq {
                        where {
                            this.automaticFollowUp.eq(true) and
                                    this.personId.eq(personId) and
                                    this.channelId.eq(channelId) and
                                    this.status.eq(NOT_STARTED) and
                                    this.dueDate.greater(now)
                        }
                    }
                )
            } returns listOf(automaticFollowUp).success()

            val result = personHealthEventService.findAutomaticFollowUpsByPersonIdAndChannelId(personId, channelId)
            assertThat(result).isSuccessWithData(listOf(automaticFollowUp))

            coVerifyOnce { personHealthEventDataService.find(any()) }
            coVerify { kafkaProducerService wasNot called }
        }

    @Test
    fun `#findCaseRecordTasksByPersonId should return list of person health event`() = runBlocking {
        val personHealthEventCopy = personHealthEvent.copy(
            staffId = null,
            referencedModelClass = CASE_RECORD,
        )
        val personId = personHealthEventCopy.personId
        val listOfPersonHealthEvent = listOf(personHealthEventCopy)

        coEvery {
            personHealthEventDataService.find(
                queryEq {
                    where {
                        this.personId.eq(personId) and
                                this.referencedModelClass.eq(CASE_RECORD) and
                                this.staffId.isNull()
                    }
                }
            )
        } returns listOfPersonHealthEvent.success()

        val result = personHealthEventService.findCaseRecordTasksByPersonId(personId)
        assertThat(result).isSuccessWithData(listOfPersonHealthEvent)

        coVerifyOnce { personHealthEventDataService.find(any()) }
        coVerify { kafkaProducerService wasNot called }
    }

    @Test
    fun `#addPersonHealthEvent - should return created person health event`() = runBlocking {
        coEvery { personHealthEventDataService.add(personHealthEvent) } returns personHealthEvent.success()
        coEvery {
            kafkaProducerService.produce(
                match { event: PersonHealthEventCreatedEvent ->
                    event.payload.personHealthEvent == personHealthEvent
                }
            )
        } returns mockk()

        val result = personHealthEventService.addPersonHealthEvent(personHealthEvent)
        assertThat(result).isSuccessWithData(personHealthEvent)

        coVerifyOnce { personHealthEventDataService.add(any()) }
        coVerifyOnce { kafkaProducerService.produce(any()) }
    }

    @Test
    fun `#addPersonHealthEvent - should return created person health event event with finished status when it has specific category`() =
        runBlocking {
            val task = personHealthEvent.copy(status = FINISHED, category = PersonHealthEventCategory.SUMMARY_EMERGENCY)

            coEvery { personHealthEventDataService.add(task) } returns task.success()
            coEvery {
                kafkaProducerService.produce(
                    match { event: PersonHealthEventCreatedEvent ->
                        event.payload.personHealthEvent == task
                    }
                )
            } returns mockk()

            val result = personHealthEventService.addPersonHealthEvent(task)
            assertThat(result).isSuccessWithData(task)

            coVerifyOnce { personHealthEventDataService.add(any()) }
            coVerifyOnce { kafkaProducerService.produce(any()) }
        }

    @Test
    fun `#addPersonHealthEvent - should return error if task is create with status finished and it has category that should check status`() =
        runBlocking {
            val task = personHealthEvent.copy(status = FINISHED)

            val result = personHealthEventService.addPersonHealthEvent(task)
            assertThat(result).isFailureOfType(IllegalArgumentException::class)

            coVerifyNone { personHealthEventDataService.add(any()) }
            coVerifyNone { kafkaProducerService.produce(any()) }
            coVerify { kafkaProducerService wasNot called }
        }

    @Test
    fun `#finishPersonHealthEvent returns phe after update status to finished`() = mockLocalDateTime { now ->
        val newPersonHealthEvent = personHealthEvent.copy(status = FINISHED, finishedAt = now)

        coEvery { personHealthEventDataService.get(personHealthEvent.id) } returns personHealthEvent.success()
        coEvery { personHealthEventDataService.update(newPersonHealthEvent) } returns newPersonHealthEvent.success()
        coEvery {
            kafkaProducerService.produce(
                match { event: PersonHealthEventUpdatedEvent ->
                    event.payload.personHealthEvent == newPersonHealthEvent
                }
            )
        } returns mockk()

        val result = personHealthEventService.finishPersonHealthEvent(personHealthEvent.id)
        assertThat(result).isSuccessWithData(newPersonHealthEvent)

        coVerifyOnce { personHealthEventDataService.get(any()) }
        coVerifyOnce { personHealthEventDataService.update(any()) }
        coVerifyOnce { kafkaProducerService.produce(any()) }
    }

    @Test
    fun `#finishPersonHealthEvent returns phe after update status to finished with referenced link and updatedBy`() =
        mockLocalDateTime { now ->
            val referencedLink = ReferencedLink("appointment_id", "APPOINTMENT")
            val updateBy = PersonHealthEventUpdatedBy(
                id = RangeUUID.generate(),
                type = PersonHealthEventUpdatedByType.STAFF,
                date = now
            )
            val newPersonHealthEvent = personHealthEvent.copy(
                status = FINISHED,
                finishedAt = now,
                referencedLinks = personHealthEvent.referencedLinks.plus(referencedLink),
                updatedByStaffIds = personHealthEvent.updatedByStaffIds.plus(updateBy)
            )

            coEvery { personHealthEventDataService.get(personHealthEvent.id) } returns personHealthEvent.success()
            coEvery { personHealthEventDataService.update(newPersonHealthEvent) } returns newPersonHealthEvent.success()
            coEvery {
                kafkaProducerService.produce(
                    match { event: PersonHealthEventUpdatedEvent ->
                        event.payload.personHealthEvent == newPersonHealthEvent
                    }
                )
            } returns mockk()

            val result =
                personHealthEventService.finishPersonHealthEvent(personHealthEvent.id, referencedLink, updateBy)
            assertThat(result).isSuccessWithData(newPersonHealthEvent)

            coVerifyOnce { personHealthEventDataService.get(any()) }
            coVerifyOnce { personHealthEventDataService.update(any()) }
            coVerifyOnce { kafkaProducerService.produce(any()) }
        }

    @Test
    fun `#finishPersonHealthEvent returns error when PHE not is active`() = mockLocalDateTime { now ->
        val newPersonHealthEvent = personHealthEvent.copy(status = FINISHED, finishedAt = now)

        coEvery { personHealthEventDataService.get(personHealthEvent.id) } returns newPersonHealthEvent.success()

        val result = personHealthEventService.finishPersonHealthEvent(personHealthEvent.id)
        assertThat(result).isFailureOfType(IllegalArgumentException::class)

        coVerifyOnce { personHealthEventDataService.get(any()) }
        coVerify { kafkaProducerService wasNot called }
    }

}
