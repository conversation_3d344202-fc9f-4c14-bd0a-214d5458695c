package br.com.alice.haocintegrationservice.service

import br.com.alice.common.RangeUUID
import br.com.alice.common.core.exceptions.DuplicatedItemException
import br.com.alice.common.core.extensions.toSha256
import br.com.alice.common.data.dsl.matchers.ResultAssert.Companion.assertThat
import br.com.alice.common.data.dsl.matchers.queryEq
import br.com.alice.common.kafka.interfaces.KafkaProducerService
import br.com.alice.common.notification.NotificationEvent
import br.com.alice.common.service.data.dsl.and
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.HaocClaimProcess
import br.com.alice.data.layer.models.HaocClaimStatus
import br.com.alice.data.layer.models.HaocClaimType
import br.com.alice.data.layer.models.HaocDocument
import br.com.alice.data.layer.models.HaocDocumentType
import br.com.alice.data.layer.models.HaocUnit
import br.com.alice.data.layer.services.HaocClaimProcessDataService
import br.com.alice.data.layer.services.HaocDocumentDataService
import br.com.alice.haoc.client.HaocProntoAtendimentoResultService
import br.com.alice.haoc.client.HaocSumarioDeAltaResultService
import br.com.alice.haoc.events.HaocEmergencyCreatedEventPayload
import br.com.alice.haoc.events.HaocHospitalizationCreatedEventPayload
import br.com.alice.haoc.events.HaocSummaryUpsertedEventPayload
import br.com.alice.haocintegrationservice.clients.HaocClient
import br.com.alice.haocintegrationservice.converters.HaocPAConverterException
import br.com.alice.haocintegrationservice.converters.SumarioDeAltaConverter
import br.com.alice.haocintegrationservice.converters.SumarioProntoAtendimentoConverter
import br.com.alice.haocintegrationservice.metrics.ClaimProcessMetric
import br.com.alice.haocintegrationservice.metrics.ClaimProcessResult
import br.com.alice.haocintegrationservice.metrics.CreateDocumentResult
import br.com.alice.haocintegrationservice.metrics.DocumentMetric
import br.com.alice.haocintegrationservice.metrics.GetSummariesResult
import br.com.alice.haocintegrationservice.metrics.SummaryMetric
import br.com.alice.person.client.PersonService
import com.github.kittinunf.result.failure
import com.github.kittinunf.result.success
import io.mockk.called
import io.mockk.clearAllMocks
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.every
import io.mockk.mockk
import io.mockk.mockkObject
import io.mockk.unmockkAll
import kotlinx.coroutines.runBlocking
import kotlin.test.AfterTest
import kotlin.test.BeforeTest
import kotlin.test.Test

class HaocClaimServiceTest {
    private var personService: PersonService = mockk()
    private var pixService: PixService = mockk()
    private var haocClaimProcessDataService: HaocClaimProcessDataService = mockk()
    private val haocDocumentDataService: HaocDocumentDataService = mockk()
    private val haocSumarioService: HaocSumarioService = mockk()
    private val haocClient: HaocClient = mockk()
    private val kafkaProducer: KafkaProducerService = mockk()
    private val haocProntoAtendimentoResultService: HaocProntoAtendimentoResultService = mockk()
    private val haocSumarioDeAltaResultService: HaocSumarioDeAltaResultService = mockk()

    private var haocClaimService = HaocClaimService(
        personService,
        pixService,
        haocSumarioService,
        haocClaimProcessDataService,
        haocDocumentDataService,
        haocClient,
        kafkaProducer,
        haocProntoAtendimentoResultService,
        haocSumarioDeAltaResultService
    )
    private val person = TestModelFactory.buildPerson()
    private val anotherPerson = TestModelFactory.buildPerson(nationalId = "80180061054")
    private val claimProcess1 = HaocClaimProcess(
        personId = person.id,
        status = HaocClaimStatus.PENDING,
        type = HaocClaimType.HOSPITALIZATION,
        unit = HaocUnit.PAULISTA,
        claimId = 12345
    )
    private val claimProcess2 = HaocClaimProcess(
        personId = anotherPerson.id,
        status = HaocClaimStatus.PENDING,
        type = HaocClaimType.HOSPITALIZATION_EXTENSION,
        unit = HaocUnit.VERGUEIRO,
        claimId = 12346
    )

    private val claimProcess3 = HaocClaimProcess(
        personId = person.id,
        status = HaocClaimStatus.PENDING,
        type = HaocClaimType.EMERGENCY_CARE,
        unit = HaocUnit.PAULISTA
    )

    private val haocPAResult = TestModelFactory.buildHaocProntoAtendimentoResult()
    private val haocAltaResult = TestModelFactory.buildHaocSumarioDeAltaResult()

    @BeforeTest
    fun setup() {
        clearAllMocks()
        coEvery { haocClient.refreshAccessToken() } answers { nothing }
        mockkObject(SummaryMetric)
        coEvery { SummaryMetric.incrementGetSummariesMetric(any()) } returns true
        mockkObject(ClaimProcessMetric)
        coEvery { ClaimProcessMetric.incrementClaimProcessMetric(any()) } returns true
        mockkObject(DocumentMetric)
        coEvery { DocumentMetric.incrementCreateDocumentMetric(any()) } returns true
    }

    @AfterTest
    fun after() {
        unmockkAll()
    }

    @Test
    fun `#registerClaim should return success when add Patient in HAOC and in data service`() = runBlocking {
        coEvery { personService.get(person.id) } returns person.success()
        coEvery { pixService.addPatient(any(), 12345) } returns true
        coEvery { haocClaimProcessDataService.findOneOrNull(any()) } returns null
        coEvery { haocClaimProcessDataService.add(any()) } returns claimProcess1.success()

        assertThat(
            haocClaimService.registerClaim(
                person.id,
                HaocClaimType.HOSPITALIZATION,
                HaocUnit.PAULISTA
            )
        ).isSuccess()

        coVerify(exactly = 1) {
            haocClaimProcessDataService.add(match {
                it.personId == person.id
                        && it.status == HaocClaimStatus.PENDING
                        && it.type == HaocClaimType.HOSPITALIZATION
            })
        }
    }

    @Test
    fun `#registerClaim should return failure when add Patient in HAOC but not add in data service`() =
        runBlocking {
            coEvery { personService.get(person.id) } returns person.success()
            coEvery { haocClaimProcessDataService.findOneOrNull(any()) } returns null
            coEvery { haocClaimProcessDataService.add(any()) } returns Exception().failure()

            assertThat(
                haocClaimService.registerClaim(
                    person.id,
                    HaocClaimType.HOSPITALIZATION,
                    HaocUnit.PAULISTA
                )
            ).isFailure()

            coVerify(exactly = 1) {
                haocClaimProcessDataService.add(match {
                    it.personId == person.id
                            && it.status == HaocClaimStatus.PENDING
                            && it.type == HaocClaimType.HOSPITALIZATION
                })
            }
        }

    @Test
    fun `#registerClaim should return success when add process without claimId`() = runBlocking {
        coEvery { personService.get(person.id) } returns person.success()
        coEvery { pixService.addPatient(any(), any()) } returns true
        coEvery { haocClaimProcessDataService.add(any()) } returns claimProcess3.success()

        assertThat(
            haocClaimService.registerClaim(
                person.id,
                HaocClaimType.EMERGENCY_CARE,
                HaocUnit.PAULISTA
            )
        ).isSuccess()

        coVerify(exactly = 1) {
            haocClaimProcessDataService.add(match {
                it.personId == person.id
                        && it.status == HaocClaimStatus.PENDING
                        && it.type == HaocClaimType.EMERGENCY_CARE
                        && it.unit == HaocUnit.PAULISTA
            })
        }
        coVerify(exactly = 0) { haocClaimProcessDataService.update(any()) }
    }

    @Test
    fun `#registerClaim should return success when claimProcess is duplicated`() = runBlocking {
        coEvery { personService.get(person.id) } returns person.success()
        coEvery { pixService.addPatient(any(), 12345) } returns true
        coEvery { haocClaimProcessDataService.findOneOrNull(any()) } returns null
        coEvery { haocClaimProcessDataService.add(any()) } returns DuplicatedItemException("").failure()

        assertThat(
            haocClaimService.registerClaim(
                person.id,
                HaocClaimType.HOSPITALIZATION,
                HaocUnit.PAULISTA
            )
        ).isSuccess()

        coVerify(exactly = 1) {
            haocClaimProcessDataService.add(match {
                it.personId == person.id
                        && it.status == HaocClaimStatus.PENDING
                        && it.type == HaocClaimType.HOSPITALIZATION
            })
        }
    }

    @Test
    fun `#processAllPending should save SumarioDeAlta when it is avaiable and update HaocClaimProcess`() = runBlocking {
        coEvery { pixService.addPatient(any(), 12345) } returns true
        val pendingClaims = listOf(claimProcess1, claimProcess2)

        val haocSumarioDeAlta = HaocSumario(
            documentId = "documentId",
            documentType = HaocDocumentType.SUMARIO_DE_ALTA,
            content = "content"
        )
        val peopleIds = listOf(claimProcess1.personId.toString(), claimProcess2.personId.toString())

        coEvery { personService.findByIds(any(), false) } returns listOf(person).success()
        coEvery { haocSumarioService.getSumarios(person.nationalId, any(), HaocClaimType.HOSPITALIZATION) } returns listOf(
            haocSumarioDeAlta
        ).success()

        coEvery { personService.get(anotherPerson.id) } returns anotherPerson.success()
        coEvery { haocSumarioService.getSumarios(anotherPerson.nationalId, any(), HaocClaimType.HOSPITALIZATION) } returns listOf<HaocSumario>().success()

        val haocSumarioDeAltaDocument = HaocDocument(
            personId = person.id,
            content = "content",
            documentType = HaocDocumentType.SUMARIO_DE_ALTA,
            documentIdHash = "hash",
            claimId = claimProcess1.claimId
        )

        coEvery { haocDocumentDataService.add(any()) } returns haocSumarioDeAltaDocument.success()

        coEvery { haocClaimProcessDataService.find(queryEq {
            where {
                this.personId.eq(claimProcess1.personId) and
                        this.type.eq(claimProcess1.type) and
                        this.status.eq(claimProcess1.status)
            }
        }) } returns listOf(claimProcess1).success()

        coEvery { haocClaimProcessDataService.find(queryEq {
            where {
                this.personId.eq(claimProcess2.personId) and
                        this.type.eq(claimProcess2.type) and
                        this.status.eq(claimProcess2.status)
            }
        }) } returns listOf(claimProcess2).success()

        coEvery { haocClaimProcessDataService.update(any()) } returns claimProcess1.success()

        haocClaimService.processAllPending(pendingClaims)

        coVerify(exactly = 1) {
            personService.findByIds(peopleIds, withUserType = false)
        }
        coVerify(exactly = 2) {
            haocClient.refreshAccessToken()
        }
        coVerify(exactly = 1) {
            haocDocumentDataService.add(match {
                it.personId == person.id
                        && it.content == "content"
                        && it.documentType == HaocDocumentType.SUMARIO_DE_ALTA
            })
        }
        coVerify(exactly = 1) {
            haocClaimProcessDataService.update(match {
                it.id == claimProcess1.id && it.status == HaocClaimStatus.DONE
            })
        }
        coVerify(exactly = 0) {
            haocClaimProcessDataService.update(match {
                it.id == claimProcess2.id && it.status == HaocClaimStatus.DONE
            })
        }
        coVerify(exactly = 1) {
            kafkaProducer.produce(match { it: NotificationEvent<HaocSummaryUpsertedEventPayload> ->
                it.payload.haocDocument.personId == person.id
                        && it.payload.haocDocument.content == "content"
                        && it.payload.haocDocument.documentType == HaocDocumentType.SUMARIO_DE_ALTA

            })
        }
    }

    @Test
    fun `#processAllPending should save ProntoAtendimento when it is avaiable and update HaocClaimProcess`() =
        runBlocking {
            coEvery { pixService.addPatient(any(), 12345) } returns true
            val pendingClaims = listOf(claimProcess1.copy(type = HaocClaimType.EMERGENCY_CARE))

            val haocSumarioProntoAtendimento = HaocSumario(
                documentId = "documentId",
                documentType = HaocDocumentType.PRONTO_ATENDIMENTO,
                content = "content"
            )
            val peopleIds = listOf(claimProcess1.personId.toString())

            coEvery { personService.findByIds(any(), withUserType = false) } returns listOf(person).success()
            coEvery { haocSumarioService.getSumarios(person.nationalId, any(),HaocClaimType.EMERGENCY_CARE) } returns listOf(
                haocSumarioProntoAtendimento
            ).success()

            mockkObject(SumarioProntoAtendimentoConverter)
            coEvery { SumarioProntoAtendimentoConverter.convert(any()) } returns haocPAResult.success()
            coEvery { haocProntoAtendimentoResultService.add(any()) } returns haocPAResult.success()

            val haocSumarioProntoAtendimentoDocument = HaocDocument(
                personId = person.id,
                content = "content",
                documentType = HaocDocumentType.PRONTO_ATENDIMENTO,
                documentIdHash = "hash",
                claimId = claimProcess1.claimId
            )

            coEvery { haocDocumentDataService.add(any()) } returns haocSumarioProntoAtendimentoDocument.success()

            coEvery { haocClaimProcessDataService.find(queryEq {
                where {
                    this.personId.eq(claimProcess1.personId) and
                            this.type.eq(HaocClaimType.EMERGENCY_CARE) and
                            this.status.eq(claimProcess1.status)
                }
            }) } returns listOf(claimProcess1).success()

            coEvery { haocClaimProcessDataService.update(any()) } returns claimProcess1.success()

            haocClaimService.processAllPending(pendingClaims)

            coVerify(exactly = 1) {
                personService.findByIds(peopleIds, withUserType = false)
            }
            coVerify(exactly = 1) {
                haocDocumentDataService.add(match {
                    it.personId == person.id
                            && it.content == "content"
                            && it.documentType == HaocDocumentType.PRONTO_ATENDIMENTO
                })
            }
            coVerify(exactly = 1) {
                haocClaimProcessDataService.update(match {
                    it.id == claimProcess1.id && it.status == HaocClaimStatus.DONE
                })
            }

            coVerify(exactly = 2) {
                kafkaProducer.produce(match {
                    when(val payload = it.payload) {
                        is HaocSummaryUpsertedEventPayload -> {
                            payload.haocDocument.personId == person.id
                                    && payload.haocDocument.content == "content"
                                    && payload.haocDocument.documentType == HaocDocumentType.PRONTO_ATENDIMENTO
                        }
                        is HaocEmergencyCreatedEventPayload -> {
                            payload.haocProntoAtendimentoResult == haocPAResult
                        }
                        else -> false
                    }
                })
            }

            coVerify(exactly = 1) {
                haocProntoAtendimentoResultService.add(haocPAResult)
            }

        }

    @Test
    fun `#processAllPending save ProntoAtendimento if HaocDocument is duplicated`() =
        runBlocking {
            coEvery { pixService.addPatient(any(), 12345) } returns true
            val pendingClaims = listOf(claimProcess1.copy(type = HaocClaimType.EMERGENCY_CARE))

            val haocSumarioProntoAtendimento = HaocSumario(
                documentId = "documentId",
                documentType = HaocDocumentType.PRONTO_ATENDIMENTO,
                content = "content"
            )
            val haocDocument = HaocDocument(
                personId = person.id,
                content = "content",
                documentType = HaocDocumentType.PRONTO_ATENDIMENTO,
                documentIdHash = "hash",
                claimId = claimProcess1.claimId
            )

            coEvery { personService.findByIds(any(), withUserType = false) } returns listOf(person).success()
            coEvery { haocSumarioService.getSumarios(person.nationalId, any(), HaocClaimType.EMERGENCY_CARE) } returns listOf(
                haocSumarioProntoAtendimento
            ).success()

            coEvery { haocDocumentDataService.add(any()) } returns DuplicatedItemException("").failure()

            coEvery { haocDocumentDataService.findOne(any()) } returns haocDocument.success()
            mockkObject(SumarioProntoAtendimentoConverter)
            coEvery { SumarioProntoAtendimentoConverter.convert(any()) } returns haocPAResult.success()
            coEvery { haocProntoAtendimentoResultService.add(any()) } returns haocPAResult.success()


            haocClaimService.processAllPending(pendingClaims)

            coVerify(exactly = 0) { haocClaimProcessDataService.update(any()) }
            coVerify(exactly = 1) { haocDocumentDataService.add(any()) }
            coVerify(exactly = 1) {
                kafkaProducer.produce(match { it: NotificationEvent<HaocEmergencyCreatedEventPayload> ->
                    it.payload.haocProntoAtendimentoResult == haocPAResult
                })
            }
            coVerify(exactly = 1) { haocProntoAtendimentoResultService.add(haocPAResult) }

        }

    @Test
    fun `#processAllPending save SumarioDeAlta result if HaocDocument is duplicated`() =
        runBlocking {
            coEvery { pixService.addPatient(any(), 12345) } returns true
            val pendingClaims = listOf(claimProcess1)

            val haocSumarioAlta = HaocSumario(
                documentId = "documentId",
                documentType = HaocDocumentType.SUMARIO_DE_ALTA,
                content = "content"
            )
            val haocDocument = HaocDocument(
                personId = person.id,
                content = "content",
                documentType = HaocDocumentType.SUMARIO_DE_ALTA,
                documentIdHash = "hash",
                claimId = claimProcess1.claimId
            )

            coEvery { personService.findByIds(any(), withUserType = false) } returns listOf(person).success()
            coEvery { haocSumarioService.getSumarios(person.nationalId, any(), HaocClaimType.HOSPITALIZATION) } returns listOf(
                haocSumarioAlta
            ).success()

            coEvery { haocDocumentDataService.add(any()) } returns DuplicatedItemException("").failure()
            coEvery { haocDocumentDataService.findOne(any()) } returns haocDocument.success()

            mockkObject(SumarioDeAltaConverter)
            coEvery { SumarioDeAltaConverter.convert(any()) } returns haocAltaResult.success()
            coEvery { haocSumarioDeAltaResultService.add(any()) } returns haocAltaResult.success()

            haocClaimService.processAllPending(pendingClaims)

            coVerify(exactly = 1) {
                haocDocumentDataService.add(any())
            }
            coVerify(exactly = 0) {
                haocClaimProcessDataService.update(any())
            }

            coVerify(exactly = 1) {
                kafkaProducer.produce(match { it: NotificationEvent<HaocHospitalizationCreatedEventPayload> ->
                    it.payload.haocSumarioDeAltaResult == haocAltaResult
                })
            }

            coVerify(exactly = 1) {
                haocSumarioDeAltaResultService.add(haocAltaResult)
            }

        }

    @Test
    fun `#processAllPending should not update HaocClaimProcess when there is none document available`() =
        runBlocking {
            coEvery { pixService.addPatient(any(), 12345) } returns true
            val pendingClaims = listOf(claimProcess1, claimProcess2)

            val peopleIds = listOf(claimProcess1.personId.toString(), claimProcess2.personId.toString())

            coEvery { personService.findByIds(any(), withUserType = false) } returns listOf(person).success()
            coEvery { haocSumarioService.getSumarios(person.nationalId, any(), any()) } returns listOf<HaocSumario>().success()
            coEvery { haocSumarioService.getSumarios(person.nationalId, any(), any()) } returns listOf<HaocSumario>().success()

            coEvery { haocSumarioService.getSumarios(anotherPerson.nationalId, any(), any()) } returns listOf<HaocSumario>().success()
            coEvery { haocSumarioService.getSumarios(anotherPerson.nationalId, any(), any()) } returns listOf<HaocSumario>().success()

            coEvery { haocClaimProcessDataService.update(match { it.id == claimProcess1.id }) } returns claimProcess1.success()
            coEvery { haocClaimProcessDataService.update(match { it.id == claimProcess2.id }) } returns claimProcess2.success()

            haocClaimService.processAllPending(pendingClaims)

            coVerify(exactly = 1) {
                personService.findByIds(peopleIds, withUserType = false)
            }
            coVerify(exactly = 0) { haocDocumentDataService.add(any()) }
            coVerify(exactly = 0) { haocClaimProcessDataService.update(any()) }
            coVerify { kafkaProducer wasNot called }

        }

    @Test
    fun `#processAllPending should not update HaocClaimProcess when there is error getting the summary`() =
        runBlocking {
            coEvery { pixService.addPatient(any(), 12345) } returns true
            val pendingClaims = listOf(claimProcess1, claimProcess2)

            val peopleIds = listOf(claimProcess1.personId.toString(), claimProcess2.personId.toString())

            coEvery { personService.findByIds(any(), withUserType = false) } returns listOf(person).success()
            coEvery { haocSumarioService.getSumarios(person.nationalId, any(), any()) } returns GettingSumarioException("error").failure()
            coEvery { haocSumarioService.getSumarios(person.nationalId, any(), any()) } returns GettingSumarioException("error").failure()

            coEvery { haocSumarioService.getSumarios(anotherPerson.nationalId, any(), any()) } returns GettingSumarioException("error").failure()
            coEvery { haocSumarioService.getSumarios(anotherPerson.nationalId, any(), any()) } returns GettingSumarioException("error").failure()

            coEvery { haocClaimProcessDataService.update(match { it.id == claimProcess1.id }) } returns claimProcess1.success()
            coEvery { haocClaimProcessDataService.update(match { it.id == claimProcess2.id }) } returns claimProcess2.success()

            haocClaimService.processAllPending(pendingClaims)

            coVerify(exactly = 1) {
                personService.findByIds(peopleIds, withUserType = false)
            }
            coVerify(exactly = 0) { haocDocumentDataService.add(any()) }
            coVerify(exactly = 0) { haocClaimProcessDataService.update(any()) }
            coVerify { kafkaProducer wasNot called }

        }

    @Test
    fun `#processAllPending should not update HaocClaimProcess when error occur while adding documents`() =
        runBlocking {
            coEvery { pixService.addPatient(any(), 12345) } returns true
            val pendingClaims = listOf(claimProcess1, claimProcess2)

            val haocSumarioDeAlta1 = HaocSumario(
                documentId = "documentId1",
                documentType = HaocDocumentType.SUMARIO_DE_ALTA,
                content = "content1"
            )
            val peopleIds = listOf(claimProcess1.personId.toString(), claimProcess2.personId.toString())

            coEvery { personService.findByIds(any(), withUserType = false) } returns listOf(person).success()
            coEvery { haocSumarioService.getSumarios(person.nationalId, any(), HaocClaimType.HOSPITALIZATION) } returns listOf(
                haocSumarioDeAlta1
            ).success()

            val haocSumarioDeAlta2 = HaocSumario(
                documentId = "documentId2",
                documentType = HaocDocumentType.SUMARIO_DE_ALTA,
                content = "content2"
            )

            coEvery { haocSumarioService.getSumarios(anotherPerson.nationalId, any(), HaocClaimType.HOSPITALIZATION_EXTENSION) } returns listOf(
                haocSumarioDeAlta2
            ).success()

            coEvery { haocSumarioService.getSumarios(any(), any(), HaocClaimType.EMERGENCY_CARE) } returns listOf<HaocSumario>().success()

            val saModel = HaocDocument(
                personId = person.id,
                content = "content",
                documentType = HaocDocumentType.SUMARIO_DE_ALTA,
                documentIdHash = "hash",
                claimId = claimProcess1.claimId
            )
            coEvery {
                haocDocumentDataService.add(match {
                    it.documentIdHash == haocSumarioDeAlta1.documentId.toSha256("some salt")
                })
            } returns saModel.success()

            coEvery {
                haocDocumentDataService.add(match {
                    it.documentIdHash == haocSumarioDeAlta2.documentId.toSha256("some salt")
                })
            } returns Exception().failure()

            coEvery { haocClaimProcessDataService.find(queryEq {
                where {
                    this.personId.eq(claimProcess1.personId) and
                            this.type.eq(claimProcess1.type) and
                            this.status.eq(claimProcess1.status)
                }
            }) } returns listOf(claimProcess1).success()

            coEvery { haocClaimProcessDataService.find(queryEq {
                where {
                    this.personId.eq(claimProcess2.personId) and
                            this.type.eq(claimProcess2.type) and
                            this.status.eq(claimProcess2.status)
                }
            }) } returns listOf(claimProcess2).success()

            coEvery { haocClaimProcessDataService.update(match { it.id == claimProcess1.id }) } returns claimProcess1.success()

            haocClaimService.processAllPending(pendingClaims)

            coVerify(exactly = 1) {
                personService.findByIds(peopleIds, withUserType = false)
            }
            coVerify(exactly = 1) { haocClaimProcessDataService.update(any()) }
            coVerify(exactly = 1) { kafkaProducer.produce(any()) }
        }

    @Test
    fun `#processAllPending should not update HaocClaimProcess when error occur while finding claims from the same person and type`() =
        runBlocking {
            coEvery { pixService.addPatient(any(), 12345) } returns true
            val pendingClaims = listOf(claimProcess1)

            val haocSumarioDeAlta1 = HaocSumario(
                documentId = "documentId1",
                documentType = HaocDocumentType.SUMARIO_DE_ALTA,
                content = "content1"
            )
            val peopleIds = listOf(claimProcess1.personId.toString())

            coEvery { personService.findByIds(any(), withUserType = false) } returns listOf(person).success()
            coEvery { haocSumarioService.getSumarios(person.nationalId, any(), HaocClaimType.HOSPITALIZATION) } returns listOf(
                haocSumarioDeAlta1
            ).success()

            coEvery { haocSumarioService.getSumarios(any(), any(), HaocClaimType.EMERGENCY_CARE) } returns listOf<HaocSumario>().success()

            val saModel = HaocDocument(
                personId = person.id,
                content = "content",
                documentType = HaocDocumentType.SUMARIO_DE_ALTA,
                documentIdHash = "hash",
                claimId = claimProcess1.claimId
            )
            coEvery {
                haocDocumentDataService.add(match {
                    it.documentIdHash == haocSumarioDeAlta1.documentId.toSha256("some salt")
                })
            } returns saModel.success()

            coEvery { haocClaimProcessDataService.find(queryEq {
                where {
                    this.personId.eq(claimProcess1.personId) and
                            this.type.eq(claimProcess1.type) and
                            this.status.eq(claimProcess1.status)
                }
            }) } returns Exception().failure()

            haocClaimService.processAllPending(pendingClaims)

            coVerify(exactly = 1) {
                personService.findByIds(peopleIds, withUserType = false)
            }
            coVerify(exactly = 1) { kafkaProducer.produce(any()) }
            coVerify(exactly = 0) { haocClaimProcessDataService.update(any()) }
        }

    @Test
    fun `#processAllPending should update HaocClaimProcess even if it is duplicated item`() = runBlocking {
        coEvery { pixService.addPatient(any(), 12345) } returns true
        val pendingClaims = listOf(claimProcess1, claimProcess2)

        val haocSumarioDeAlta1 = HaocSumario(
            documentId = "documentId1",
            documentType = HaocDocumentType.SUMARIO_DE_ALTA,
            content = "content1"
        )
        val peopleIds = listOf(claimProcess1.personId.toString(), claimProcess2.personId.toString())

        coEvery { personService.findByIds(any(), withUserType = false) } returns listOf(person).success()
        coEvery { haocSumarioService.getSumarios(person.nationalId, any(), HaocClaimType.HOSPITALIZATION) } returns listOf(
            haocSumarioDeAlta1
        ).success()

        val haocSumarioDeAlta2 = HaocSumario(
            documentId = "documentId2",
            documentType = HaocDocumentType.SUMARIO_DE_ALTA,
            content = "content2"
        )

        coEvery { personService.get(anotherPerson.id) } returns anotherPerson.success()
        coEvery { haocSumarioService.getSumarios(anotherPerson.nationalId, any(), HaocClaimType.HOSPITALIZATION_EXTENSION) } returns listOf(
            haocSumarioDeAlta2
        ).success()

        coEvery { haocSumarioService.getSumarios(any(), any(), HaocClaimType.EMERGENCY_CARE) } returns listOf<HaocSumario>().success()

        val saModel = HaocDocument(
            personId = person.id,
            content = "content",
            documentType = HaocDocumentType.SUMARIO_DE_ALTA,
            documentIdHash = "hash",
            claimId = claimProcess1.claimId
        )
        coEvery {
            haocDocumentDataService.add(match {
                it.documentIdHash == haocSumarioDeAlta1.documentId.toSha256("some salt")
            })
        } returns saModel.success()

        coEvery {
            haocDocumentDataService.add(match {
                it.documentIdHash == haocSumarioDeAlta2.documentId.toSha256("some salt")
            })
        } returns DuplicatedItemException("", "").failure()

        coEvery { haocClaimProcessDataService.find(queryEq {
            where {
                this.personId.eq(claimProcess1.personId) and
                        this.type.eq(claimProcess1.type) and
                        this.status.eq(claimProcess1.status)
            }
        }) } returns listOf(claimProcess1).success()

        coEvery { haocClaimProcessDataService.find(queryEq {
            where {
                this.personId.eq(claimProcess2.personId) and
                        this.type.eq(claimProcess2.type) and
                        this.status.eq(claimProcess2.status)
            }
        }) } returns listOf(claimProcess2).success()

        coEvery { haocClaimProcessDataService.update(match { it.id == claimProcess1.id }) } returns claimProcess1.success()

        haocClaimService.processAllPending(pendingClaims)

        coVerify(exactly = 1) {
            personService.findByIds(peopleIds, withUserType = false)
        }
        coVerify(exactly = 1) {
            haocClaimProcessDataService.update(match {
                it.id == claimProcess1.id && it.status == HaocClaimStatus.DONE
            })
        }
        coVerify(exactly = 0) {
            haocClaimProcessDataService.update(match {
                it.id == claimProcess2.id && it.status == HaocClaimStatus.DONE
            })
        }
        coVerify(exactly = 1) { kafkaProducer.produce(any()) }
    }

    @Test
    fun `processAllPending should failure get summaries haoc`() = runBlocking {
        coEvery { pixService.addPatient(any(), 12345) } returns true

        val pendingClaims = listOf(claimProcess1)

        coEvery { personService.findByIds(any(), withUserType = false) } returns listOf(person).success()

        coEvery {
            haocSumarioService.getSumarios(any(), any(), any())
        } throws Exception()

        haocClaimService.processAllPending(pendingClaims)

        coVerify(exactly = 1) {
            personService.findByIds(any(), withUserType = false)
        }
        coVerify(exactly = 0) {
            haocDocumentDataService.add(any())
            haocClaimProcessDataService.update(any())
        }
        coVerify { kafkaProducer wasNot called }
        coVerify (exactly = 1){
            SummaryMetric.incrementGetSummariesMetric(match { it == GetSummariesResult.FAILURE })
            ClaimProcessMetric.incrementClaimProcessMetric(match { it == ClaimProcessResult.FAILURE })
        }
    }

    @Test
    fun `#processAllPending should failure when add document ProntoAtendimento`() = runBlocking {
        coEvery { pixService.addPatient(any(), 12345) } returns true
        val pendingClaims = listOf(claimProcess1)

        val haocSumario = HaocSumario(
            documentId = "documentId",
            documentType = HaocDocumentType.PRONTO_ATENDIMENTO,
            content = "content"
        )

        coEvery { personService.findByIds(any(), withUserType = false) } returns listOf(person).success()
        coEvery { haocSumarioService.getSumarios(person.nationalId, any(), any()) } returns listOf(
            haocSumario
        ).success()

        val haocDocument = HaocDocument(
            personId = person.id,
            content = "content",
            documentType = HaocDocumentType.PRONTO_ATENDIMENTO,
            documentIdHash = "hash",
            claimId = claimProcess1.claimId
        )

        coEvery { haocDocumentDataService.add(any()) } returns DuplicatedItemException("duplicated_item").failure()
        coEvery { haocDocumentDataService.findOne(any()) } returns haocDocument.success()
        mockkObject(SumarioProntoAtendimentoConverter)
        every { SumarioProntoAtendimentoConverter.convert(any()) } returns HaocPAConverterException(haocDocumentId = haocDocument.id, Exception()).failure()

        haocClaimService.processAllPending(pendingClaims)

        coVerify { kafkaProducer wasNot called }
        coVerify (exactly = 1){
            DocumentMetric.incrementCreateDocumentMetric(match { it == CreateDocumentResult.FAILURE })
            ClaimProcessMetric.incrementClaimProcessMetric(match { it == ClaimProcessResult.FAILURE })
        }
        coVerify (exactly = 0) {
            DocumentMetric.incrementCreateDocumentMetric(match { it == CreateDocumentResult.SUCCESS })
        }
        coVerify(exactly = 0) { haocClaimProcessDataService.update(any()) }
    }

    @Test
    fun `#processAllPending should failure when add document SumarioDeAlta`() = runBlocking {
        coEvery { pixService.addPatient(any(), 12345) } returns true
        val pendingClaims = listOf(claimProcess1)

        val haocSumario = HaocSumario(
            documentId = "documentId",
            documentType = HaocDocumentType.SUMARIO_DE_ALTA,
            content = "content"
        )

        coEvery { personService.findByIds(any(), withUserType = false) } returns listOf(person).success()
        coEvery { haocSumarioService.getSumarios(person.nationalId, any(), any()) } returns listOf(
            haocSumario
        ).success()

        val haocDocument = HaocDocument(
            personId = person.id,
            content = "content",
            documentType = HaocDocumentType.SUMARIO_DE_ALTA,
            documentIdHash = "hash",
            claimId = claimProcess1.claimId
        )

        coEvery { haocDocumentDataService.add(any()) } returns DuplicatedItemException("duplicated_item").failure()
        coEvery { haocDocumentDataService.findOne(any()) } returns haocDocument.success()
        mockkObject(SumarioDeAltaConverter)
        every { SumarioDeAltaConverter.convert(any()) } returns HaocPAConverterException(haocDocumentId = haocDocument.id, Exception()).failure()

        haocClaimService.processAllPending(pendingClaims)

        coVerify (exactly = 1){
            DocumentMetric.incrementCreateDocumentMetric(match { it == CreateDocumentResult.FAILURE })
            ClaimProcessMetric.incrementClaimProcessMetric(match { it == ClaimProcessResult.FAILURE })
        }
        coVerify (exactly = 0) {
            DocumentMetric.incrementCreateDocumentMetric(match { it == CreateDocumentResult.SUCCESS })
        }
        coVerify(exactly = 0) { haocClaimProcessDataService.update(any()) }
    }

    @Test
    fun `#processAllPending should return success when add document SumarioDeAlta when the summary already exists`() = runBlocking {
        coEvery { pixService.addPatient(any(), 12345) } returns true
        val pendingClaims = listOf(claimProcess1)

        val haocSumario = HaocSumario(
            documentId = "documentId",
            documentType = HaocDocumentType.SUMARIO_DE_ALTA,
            content = "content"
        )

        coEvery { personService.findByIds(any(), withUserType = false) } returns listOf(person).success()
        coEvery { haocSumarioService.getSumarios(person.nationalId, any(), any()) } returns listOf(
            haocSumario
        ).success()

        val haocDocument = HaocDocument(
            personId = person.id,
            content = "content",
            documentType = HaocDocumentType.SUMARIO_DE_ALTA,
            documentIdHash = "hash",
            claimId = claimProcess1.claimId
        )

        coEvery { haocDocumentDataService.add(any()) } returns DuplicatedItemException("duplicated_item").failure()
        coEvery { haocDocumentDataService.findOne(any()) } returns haocDocument.success()
        mockkObject(SumarioDeAltaConverter)
        coEvery { SumarioDeAltaConverter.convert(any()) } returns haocAltaResult.success()
        coEvery { haocSumarioDeAltaResultService.add(any()) } returns DuplicatedItemException("duplicated_item").failure()
        coEvery { haocClaimProcessDataService.find(queryEq {
            where {
                this.personId.eq(claimProcess1.personId) and
                        this.type.eq(claimProcess1.type) and
                        this.status.eq(claimProcess1.status)
            }
        }) } returns listOf(claimProcess1).success()
        coEvery { haocClaimProcessDataService.update(claimProcess1.copy(status = HaocClaimStatus.DONE)) } returns claimProcess1.success()

        haocClaimService.processAllPending(pendingClaims)

        coVerify (exactly = 1){
            DocumentMetric.incrementCreateDocumentMetric(match { it == CreateDocumentResult.DUPLICATED })
            ClaimProcessMetric.incrementClaimProcessMetric(match { it == ClaimProcessResult.DONE })
        }
        coVerify (exactly = 0) {
            DocumentMetric.incrementCreateDocumentMetric(match { it == CreateDocumentResult.FAILURE })
        }
        coVerify(exactly = 1) { haocClaimProcessDataService.update(any()) }
    }

    @Test
    fun `#processAllPending should return success when add document ProntoAtendimento when the summary already exists`() = runBlocking {
        coEvery { pixService.addPatient(any(), 12345) } returns true
        val pendingClaims = listOf(claimProcess1)

        val haocSumario = HaocSumario(
            documentId = "documentId",
            documentType = HaocDocumentType.PRONTO_ATENDIMENTO,
            content = "content"
        )

        coEvery { personService.findByIds(any(), withUserType = false) } returns listOf(person).success()
        coEvery { haocSumarioService.getSumarios(person.nationalId, any(), any()) } returns listOf(
            haocSumario
        ).success()

        val haocDocument = HaocDocument(
            personId = person.id,
            content = "content",
            documentType = HaocDocumentType.PRONTO_ATENDIMENTO,
            documentIdHash = "hash",
            claimId = claimProcess1.claimId
        )

        coEvery { haocDocumentDataService.add(any()) } returns DuplicatedItemException("duplicated_item").failure()
        coEvery { haocDocumentDataService.findOne(any()) } returns haocDocument.success()
        mockkObject(SumarioProntoAtendimentoConverter)
        coEvery { SumarioProntoAtendimentoConverter.convert(any()) } returns haocPAResult.success()
        coEvery { haocProntoAtendimentoResultService.add(any()) } returns DuplicatedItemException("duplicated_item").failure()
        coEvery { haocClaimProcessDataService.find(queryEq {
            where {
                this.personId.eq(claimProcess1.personId) and
                        this.type.eq(claimProcess1.type) and
                        this.status.eq(claimProcess1.status)
            }
        }) } returns listOf(claimProcess1).success()
        coEvery { haocClaimProcessDataService.update(claimProcess1.copy(status = HaocClaimStatus.DONE)) } returns claimProcess1.success()

        haocClaimService.processAllPending(pendingClaims)

        coVerify (exactly = 1){
            DocumentMetric.incrementCreateDocumentMetric(match { it == CreateDocumentResult.DUPLICATED })
            ClaimProcessMetric.incrementClaimProcessMetric(match { it == ClaimProcessResult.DONE })
        }
        coVerify (exactly = 0) {
            DocumentMetric.incrementCreateDocumentMetric(match { it == CreateDocumentResult.FAILURE })
        }
        coVerify(exactly = 1) { haocClaimProcessDataService.update(any()) }
    }

    @Test
    fun `#processAllPending should update all HaocClaimProcess from the same person and type`() = runBlocking {
        coEvery { pixService.addPatient(any(), 12345) } returns true
        val pendingClaims = listOf(claimProcess1)

        val haocSumarioDeAlta = HaocSumario(
            documentId = "documentId",
            documentType = HaocDocumentType.SUMARIO_DE_ALTA,
            content = "content"
        )
        val peopleIds = listOf(claimProcess1.personId.toString())

        coEvery { personService.findByIds(any(), withUserType = false) } returns listOf(person).success()
        coEvery { haocSumarioService.getSumarios(person.nationalId, any(), any()) } returns listOf(
            haocSumarioDeAlta
        ).success()

        val haocSumarioDeAltaDocument = HaocDocument(
            personId = person.id,
            content = "content",
            documentType = HaocDocumentType.SUMARIO_DE_ALTA,
            documentIdHash = "hash",
            claimId = claimProcess1.claimId
        )

        coEvery { haocDocumentDataService.add(any()) } returns haocSumarioDeAltaDocument.success()

        val copyOfClaimProcess1 = claimProcess1.copy(id = RangeUUID.generate(), claimId = 54321)

        coEvery { haocClaimProcessDataService.find(queryEq {
            where {
                this.personId.eq(claimProcess1.personId) and
                        this.type.eq(claimProcess1.type) and
                        this.status.eq(claimProcess1.status)
            }
        }) } returns listOf(claimProcess1, copyOfClaimProcess1).success()

        coEvery { haocClaimProcessDataService.update(claimProcess1.copy(status = HaocClaimStatus.DONE)) } returns claimProcess1.success()
        coEvery { haocClaimProcessDataService.update(copyOfClaimProcess1.copy(status = HaocClaimStatus.DONE)) } returns copyOfClaimProcess1.success()

        haocClaimService.processAllPending(pendingClaims)

        coVerify(exactly = 1) {
            personService.findByIds(peopleIds, withUserType = false)
        }
        coVerify(exactly = 1) {
            haocDocumentDataService.add(match {
                it.personId == person.id
                        && it.content == "content"
                        && it.documentType == HaocDocumentType.SUMARIO_DE_ALTA
            })
        }
        coVerify(exactly = 1) {
            haocClaimProcessDataService.update(match {
                it.id == claimProcess1.id && it.status == HaocClaimStatus.DONE
            })
        }
        coVerify(exactly = 1) {
            haocClaimProcessDataService.update(match {
                it.id == copyOfClaimProcess1.id && it.status == HaocClaimStatus.DONE
            })
        }
        coVerify(exactly = 1) {
            kafkaProducer.produce(match { it: NotificationEvent<HaocSummaryUpsertedEventPayload> ->
                it.payload.haocDocument.personId == person.id
                        && it.payload.haocDocument.content == "content"
                        && it.payload.haocDocument.documentType == HaocDocumentType.SUMARIO_DE_ALTA

            })
        }
    }

    @Test
    fun `#reprocess should save SumarioDeAlta when it is avaiable and update HaocClaimProcess`() = runBlocking {
        val person = person.copy(id = claimProcess1.personId )
        val claimProcess = claimProcess1.copy(status = HaocClaimStatus.DONE)

        coEvery { pixService.addPatient(any(), 12345) } returns true
        coEvery { haocClaimProcessDataService.get(claimProcess.id) } returns claimProcess.success()
        coEvery { personService.get(claimProcess.personId) } returns person.success()

        val haocSumarioDeAlta = HaocSumario(
            documentId = "documentId",
            documentType = HaocDocumentType.SUMARIO_DE_ALTA,
            content = "content"
        )

        coEvery { haocSumarioService.getSumarios(person.nationalId, any(), HaocClaimType.HOSPITALIZATION) } returns listOf(
            haocSumarioDeAlta
        ).success()

        coEvery { personService.get(anotherPerson.id) } returns anotherPerson.success()
        coEvery { haocSumarioService.getSumarios(anotherPerson.nationalId, any(), HaocClaimType.HOSPITALIZATION) } returns listOf<HaocSumario>().success()

        val haocSumarioDeAltaDocument = HaocDocument(
            personId = person.id,
            content = "content",
            documentType = HaocDocumentType.SUMARIO_DE_ALTA,
            documentIdHash = "hash",
            claimId = claimProcess.claimId
        )

        coEvery { haocDocumentDataService.add(any()) } returns haocSumarioDeAltaDocument.success()

        coEvery { haocClaimProcessDataService.find(queryEq {
            where {
                this.personId.eq(claimProcess.personId) and
                        this.type.eq(claimProcess.type) and
                        this.status.eq(claimProcess.status)
            }
        }) } returns listOf(claimProcess).success()

        coEvery { haocClaimProcessDataService.update(any()) } returns claimProcess.success()

        haocClaimService.reprocessSingleClaim(claimProcess.id)

        coVerify(exactly = 1) {
            personService.get(claimProcess.personId)
        }
        coVerify(exactly = 1) {
            haocClient.refreshAccessToken()
        }
        coVerify(exactly = 1) {
            haocDocumentDataService.add(match {
                it.personId == person.id
                        && it.content == "content"
                        && it.documentType == HaocDocumentType.SUMARIO_DE_ALTA
            })
        }
        coVerify(exactly = 1) {
            haocClaimProcessDataService.update(match {
                it.id == claimProcess.id && it.status == HaocClaimStatus.DONE
            })
        }

        coVerify(exactly = 1) {
            kafkaProducer.produce(match { it: NotificationEvent<HaocSummaryUpsertedEventPayload> ->
                it.payload.haocDocument.personId == person.id
                        && it.payload.haocDocument.content == "content"
                        && it.payload.haocDocument.documentType == HaocDocumentType.SUMARIO_DE_ALTA

            })
        }
    }
}
