package br.com.alice.bff.business.controllers

import br.com.alice.bff.business.ControllerTestHelper
import br.com.alice.bff.business.controllers.v1.BeneficiaryController
import br.com.alice.bff.business.converters.v1.BeneficiaryConverter
import br.com.alice.bff.business.converters.v1.BeneficiaryConverter.toBeneficiaryTransport
import br.com.alice.bff.business.models.v1.BasicInfoResponse
import br.com.alice.bff.business.models.v1.BeneficiaryDependentRequest
import br.com.alice.bff.business.models.v1.BeneficiaryEmployeeRequest
import br.com.alice.bff.business.models.v1.BeneficiaryEmployeeSimpleRequest
import br.com.alice.bff.business.models.v1.ScheduleCancelationRequest
import br.com.alice.bff.business.models.v1.ValidatePersonRequest
import br.com.alice.business.client.BeneficiaryCompiledViewFilters
import br.com.alice.business.client.BeneficiaryCompiledViewService
import br.com.alice.business.client.BeneficiaryService
import br.com.alice.business.client.CompanyService
import br.com.alice.business.client.CompanyStaffService
import br.com.alice.business.client.MailerService
import br.com.alice.business.exceptions.AlreadyAliceMemberException
import br.com.alice.business.exceptions.BeneficiaryAlreadyExistsException
import br.com.alice.business.exceptions.InvalidBeneficiaryCanceledRequest
import br.com.alice.business.metrics.BeneficiaryMetric.BeneficiaryCreationFlow.HR_PORTAL
import br.com.alice.business.model.BeneficiaryCancelation
import br.com.alice.business.model.toBeneficiary
import br.com.alice.common.RangeUUID
import br.com.alice.common.core.PersonId
import br.com.alice.common.core.exceptions.NotFoundException
import br.com.alice.common.data.dsl.matchers.ResponseAssert.Companion.assertThat
import br.com.alice.common.featureflag.withFeatureFlag
import br.com.alice.common.helpers.coVerifyNone
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.common.helpers.returns
import br.com.alice.common.helpers.verifyOnce
import br.com.alice.common.models.Sex
import br.com.alice.common.models.State
import br.com.alice.communication.email.model.EmailAddress
import br.com.alice.communication.email.model.EmailReceipt
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.Address
import br.com.alice.data.layer.models.BeneficiaryCancelationReason
import br.com.alice.data.layer.models.BeneficiaryContractType
import br.com.alice.data.layer.models.BeneficiaryOnboardingFlowType.FULL_RISK_FLOW
import br.com.alice.data.layer.models.BeneficiaryOnboardingFlowType.UNDEFINED
import br.com.alice.common.BeneficiaryType
import br.com.alice.data.layer.models.BeneficiaryViewInsuranceStatus
import br.com.alice.data.layer.models.CompanySize
import br.com.alice.data.layer.models.DealChannel
import br.com.alice.data.layer.models.FeatureNamespace
import br.com.alice.data.layer.models.MemberStatus
import br.com.alice.data.layer.models.ParentBeneficiaryRelationType
import br.com.alice.person.client.MemberService
import br.com.alice.person.client.PersonService
import br.com.alice.sales_channel.service.OngoingCompanyDealService
import com.github.kittinunf.result.failure
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.mockk
import io.mockk.mockkObject
import kotlinx.coroutines.runBlocking
import java.time.LocalDate
import java.time.LocalDateTime
import kotlin.test.BeforeTest
import kotlin.test.Test
import br.com.alice.business.metrics.BeneficiaryMetric as ClientBeneficiaryMetric

class BeneficiaryControllerTest : ControllerTestHelper() {
    private val beneficiaryCompiledViewService: BeneficiaryCompiledViewService = mockk()
    private val companyStaffService: CompanyStaffService = mockk()
    private val companyService: CompanyService = mockk()
    private val beneficiaryService: BeneficiaryService = mockk()
    private val memberService: MemberService = mockk()
    private val personService: PersonService = mockk()
    private val ongoingCompanyDealService: OngoingCompanyDealService = mockk()
    private val mailerService: MailerService = mockk()

    private val controller =
        BeneficiaryController(
            companyStaffService,
            beneficiaryCompiledViewService,
            beneficiaryService,
            memberService,
            personService,
            companyService,
            ongoingCompanyDealService,
            mailerService
        )

    @BeforeTest
    override fun setup() {
        super.setup()
        mockkObject(BeneficiaryConverter)
        mockkObject(ClientBeneficiaryMetric)
        module.single { controller }
        coEvery { companyStaffService.getLatestByEmail(staff.email) } returns companyStaff
    }

    private val productId = RangeUUID.generate()

    private val address = Address(
        city = " Rio de Janeiro \t",
        number = "1",
        postalCode = "12376990  \t",
        state = State.RJ,
        street = "Rua da Felicidade",
        neighbourhood = "Batatinha",
        complement = "fundos",
    )

    private val requestEmployeeToBatch = BeneficiaryEmployeeSimpleRequest(
        firstName = "Marco",
        lastName = "Costa",
        nationalId = "*********02",
        email = "<EMAIL>",
        sex = "Masculino",
        birthDate = "11/12/1997",
        mothersName = "Maria Costa",
        phoneNumber = "31994658111",
        activatedAt = "12/12/2100",
        addressCity = address.city,
        addressNumber = address.number,
        addressPostalCode = address.postalCode.orEmpty(),
        addressState = address.state.toString(),
        addressStreet = address.street,
        productDisplayName = "plano basicao",
        contractType = "CLT",
        hiredAt = "12/12/2021",
        productId = null,
        cnpj = ""
    )

    private val emailReceipt = EmailReceipt(id = RangeUUID.generate().toString())

    @Test
    fun `#addBeneficiaryDependent - should create beneficiary`() {
        val parentBeneficiary = RangeUUID.generate()
        val personId = PersonId()
        val request = BeneficiaryDependentRequest(
            firstName = "Marco",
            lastName = "Costa",
            mothersName = "Maria Costa",
            nationalId = "*********02",
            email = "<EMAIL>",
            sex = Sex.MALE,
            birthDate = LocalDateTime.of(1997, 6, 2, 0, 0, 0),
            phoneNumber = "31994658111",
            activatedAt = LocalDateTime.of(2022, 6, 10, 0, 0, 0),
            address = Address(State.MG, "Ipatinga", "Mario de Andrade", "76", "ap"),
            productId = productId,
            parentBeneficiary = parentBeneficiary,
            parentBeneficiaryRelationType = ParentBeneficiaryRelationType.CHILD,
            parentBeneficiaryRelatedAt = LocalDateTime.of(1997, 6, 2, 0, 0, 0),
            cnpj = null,
            subcontractId = null,
        )
        val beneficiaryTransport = request.toBeneficiaryTransport(company.id)
        val beneficiary = beneficiaryTransport.toBeneficiary(RangeUUID.generate(), personId)
        val sendTo = EmailAddress(
            email = companyStaff.email,
            name = companyStaff.fullName()
        )

        coEvery { companyService.get(company.id) } returns company
        coEvery { beneficiaryService.createBeneficiary(any(), any(), any(), any(), any()) } returns beneficiary
        coEvery { ongoingCompanyDealService.getByCompanyId(company.id) } returns listOf()
        coEvery { mailerService.sendInclusionEmail(sendTo, beneficiaryTransport) } returns emailReceipt

        authenticatedAs(idToken, staffTest) {
            post("/company/${company.id}/beneficiaries/dependents", body = request) { response ->
                assertThat(response).isCreated()
            }
        }
        verifyOnce { ClientBeneficiaryMetric.beneficiaryCreatedIncrement(any(), HR_PORTAL) }
        coVerifyOnce { beneficiaryService.createBeneficiary(beneficiaryTransport, productId, UNDEFINED, null) }
    }

    @Test
    fun `#addBeneficiaryDependent - should return bad request`() {
        val personId = PersonId()
        val parentBeneficiary = RangeUUID.generate()
        val request = BeneficiaryDependentRequest(
            firstName = "Marco",
            lastName = "Costa",
            mothersName = "Maria Costa",
            nationalId = "*********02",
            email = "<EMAIL>",
            sex = Sex.MALE,
            birthDate = LocalDateTime.of(1997, 6, 2, 0, 0, 0),
            phoneNumber = "31994658111",
            activatedAt = LocalDateTime.of(2022, 6, 10, 0, 0, 0),
            address = Address(State.MG, "Ipatinga", "Mario de Andrade", "76", "ap"),
            productId = productId,
            parentBeneficiary = parentBeneficiary,
            parentBeneficiaryRelationType = ParentBeneficiaryRelationType.CHILD,
            parentBeneficiaryRelatedAt = LocalDateTime.of(1997, 6, 2, 0, 0, 0),
            cnpj = null,
            subcontractId = null,
        )
        val beneficiaryTransport = request.toBeneficiaryTransport(company.id)

        coEvery { companyService.get(company.id) } returns company
        coEvery {
            beneficiaryService.createBeneficiary(any(), any(), any(), any(), any())
        } returns BeneficiaryAlreadyExistsException(personId, RangeUUID.generate())
        coEvery { ongoingCompanyDealService.getByCompanyId(company.id) } returns listOf()

        authenticatedAs(idToken, staffTest) {
            post("/company/${company.id}/beneficiaries/dependents", body = request) { response ->
                assertThat(response).isBadRequest()
            }
        }

        coVerifyOnce { beneficiaryService.createBeneficiary(beneficiaryTransport, productId, UNDEFINED, null) }
    }

    @Test
    fun `#addBeneficiaryDependent - should return bad request when duplicate dependent`() {
        val personId = PersonId()
        val parentBeneficiary = RangeUUID.generate()
        val request = BeneficiaryDependentRequest(
            firstName = "Marco",
            lastName = "Costa",
            mothersName = "Maria Costa",
            nationalId = "*********02",
            email = "<EMAIL>",
            sex = Sex.MALE,
            birthDate = LocalDateTime.of(1997, 6, 2, 0, 0, 0),
            phoneNumber = "31994658111",
            activatedAt = LocalDateTime.of(2022, 6, 10, 0, 0, 0),
            address = Address(State.MG, "Ipatinga", "Mario de Andrade", "76", "ap"),
            productId = productId,
            parentBeneficiary = parentBeneficiary,
            parentBeneficiaryRelationType = ParentBeneficiaryRelationType.CHILD,
            parentBeneficiaryRelatedAt = LocalDateTime.of(1997, 6, 2, 0, 0, 0),
            cnpj = null,
            subcontractId = null,
        )
        val beneficiaryTransport = request.toBeneficiaryTransport(company.id)

        coEvery { companyService.get(company.id) } returns company
        coEvery {
            beneficiaryService.createBeneficiary(any(), any(), any(), any(), any())
        } returns AlreadyAliceMemberException(personName = "${request.firstName} ${request.lastName}")
        coEvery { ongoingCompanyDealService.getByCompanyId(company.id) } returns listOf()

        authenticatedAs(idToken, staffTest) {
            post("/company/${company.id}/beneficiaries/dependents", body = request) { response ->
                assertThat(response)
                    .isBadRequestWithErrorCode(
                        expectedErrorCode = "already_alice_member_exception",
                        expectedErrorMessage = "Não foi possível incluir Marco Costa, pois já existe um plano ativo para esse CPF. \n" +
                                "Em breve, nossa equipe de suporte entrará em contato para te ajudar com esse caso."
                    )
            }
        }

        coVerifyOnce { beneficiaryService.createBeneficiary(beneficiaryTransport, productId, UNDEFINED, null) }
    }

    @Test
    fun `#addBeneficiaryHolder - should create beneficiary`() {
        val productId = RangeUUID.generate()
        val personId = PersonId()
        val request = BeneficiaryEmployeeRequest(
            firstName = "Marco",
            lastName = "Costa",
            mothersName = "Maria Costa",
            nationalId = "*********02",
            email = "<EMAIL>",
            sex = Sex.MALE,
            birthDate = LocalDateTime.of(1997, 6, 2, 0, 0, 0),
            phoneNumber = "31994658111",
            activatedAt = LocalDateTime.of(2022, 6, 10, 0, 0, 0),
            address = Address(State.MG, "Ipatinga", "Mario de Andrade", "76", "ap"),
            productId = productId,
            contractType = BeneficiaryContractType.CLT,
            hiredAt = LocalDateTime.of(2022, 6, 10, 0, 0, 0),
            cnpj = null,
            subcontractId = null,
        )
        val beneficiaryTransport = request.toBeneficiaryTransport(company.id)
        val beneficiary = beneficiaryTransport.toBeneficiary(RangeUUID.generate(), personId)
        val sendTo = EmailAddress(
            email = companyStaff.email,
            name = companyStaff.fullName()
        )

        coEvery { companyService.get(company.id) } returns company
        coEvery { beneficiaryService.createBeneficiary(any(), any(), any(), any(), any()) } returns beneficiary
        coEvery { ongoingCompanyDealService.getByCompanyId(company.id) } returns listOf()
        coEvery { mailerService.sendInclusionEmail(sendTo, beneficiaryTransport) } returns emailReceipt

        authenticatedAs(idToken, staffTest) {
            post("/company/${company.id}/beneficiaries/holders", body = request) { response ->
                assertThat(response).isOKWithData(beneficiary)
            }
        }

        verifyOnce { ClientBeneficiaryMetric.beneficiaryCreatedIncrement(any(), HR_PORTAL) }
        coVerifyOnce { beneficiaryService.createBeneficiary(beneficiaryTransport, productId, UNDEFINED, null) }
    }

    @Test
    fun `#addBeneficiaryHolder - should return bad request`() {
        val productId = RangeUUID.generate()
        val personId = PersonId()
        val request = BeneficiaryEmployeeRequest(
            firstName = "Marco",
            lastName = "Costa",
            mothersName = "Maria Costa",
            nationalId = "*********02",
            email = "<EMAIL>",
            sex = Sex.MALE,
            birthDate = LocalDateTime.of(1997, 6, 2, 0, 0, 0),
            phoneNumber = "31994658111",
            activatedAt = LocalDateTime.of(2022, 6, 10, 0, 0, 0),
            address = Address(State.MG, "Ipatinga", "Mario de Andrade", "76", "ap"),
            productId = productId,
            contractType = BeneficiaryContractType.CLT,
            hiredAt = LocalDateTime.of(2022, 6, 10, 0, 0, 0),
            cnpj = null,
            subcontractId = null,
        )
        val beneficiaryTransport = request.toBeneficiaryTransport(company.id)
        val beneficiary = beneficiaryTransport.toBeneficiary(RangeUUID.generate(), personId)

        coEvery { companyService.get(company.id) } returns company
        coEvery {
            beneficiaryService.createBeneficiary(any(), any(), any(), any(), any())
        } returns BeneficiaryAlreadyExistsException(personId, beneficiary.memberId)
        coEvery { ongoingCompanyDealService.getByCompanyId(company.id) } returns listOf()

        authenticatedAs(idToken, staffTest) {
            post("/company/${company.id}/beneficiaries/holders", body = request) { response ->
                assertThat(response).isBadRequest()
            }
        }

        coVerifyOnce {
            beneficiaryService.createBeneficiary(beneficiaryTransport, productId, UNDEFINED, null)
        }
    }

    @Test
    fun `#addBeneficiaryHolder - should return bad request when there is already a member with active plan`() {
        val productId = RangeUUID.generate()
        val personId = PersonId()
        val request = BeneficiaryEmployeeRequest(
            firstName = "Marco",
            lastName = "Costa",
            mothersName = "Maria Costa",
            nationalId = "*********02",
            email = "<EMAIL>",
            sex = Sex.MALE,
            birthDate = LocalDateTime.of(1997, 6, 2, 0, 0, 0),
            phoneNumber = "31994658111",
            activatedAt = LocalDateTime.of(2022, 6, 10, 0, 0, 0),
            address = Address(State.MG, "Ipatinga", "Mario de Andrade", "76", "ap"),
            productId = productId,
            contractType = BeneficiaryContractType.CLT,
            hiredAt = LocalDateTime.of(2022, 6, 10, 0, 0, 0),
            cnpj = null,
            subcontractId = null,
        )
        val beneficiaryTransport = request.toBeneficiaryTransport(company.id)
        val beneficiary = beneficiaryTransport.toBeneficiary(RangeUUID.generate(), personId)

        coEvery { companyService.get(company.id) } returns company
        coEvery {
            beneficiaryService.createBeneficiary(any(), any(), any(), any(), any())
        } returns AlreadyAliceMemberException(personName = "${request.firstName} ${request.lastName}")
        coEvery { ongoingCompanyDealService.getByCompanyId(company.id) } returns listOf()

        authenticatedAs(idToken, staffTest) {
            post("/company/${company.id}/beneficiaries/holders", body = request) { response ->
                assertThat(response)
                    .isBadRequestWithErrorCode(
                        expectedErrorCode = "already_alice_member_exception",
                        expectedErrorMessage = "Não foi possível incluir Marco Costa, pois já existe um plano ativo para esse CPF. \n" +
                                "Em breve, nossa equipe de suporte entrará em contato para te ajudar com esse caso."
                    )
            }
        }

        coVerifyOnce {
            beneficiaryService.createBeneficiary(beneficiaryTransport, productId, UNDEFINED, null)
        }
    }

    @Test
    fun `#getBasicInfo - should list basic info of beneficiaryCompiledView by searchToken`() {
        val searchToken = "Marco"
        val person1 = TestModelFactory.buildPerson(firstName = "Marco", lastName = "Costa")
        val person2 = TestModelFactory.buildPerson(firstName = "Marcos", lastName = "Antonio")
        val beneficiary1 = TestModelFactory.buildBeneficiary(personId = person1.id)
        val beneficiary2 = TestModelFactory.buildBeneficiary(personId = person2.id)
        val people = listOf(person1, person2)

        val beneficiaryView1 = TestModelFactory.buildBeneficiaryCompiledView(
            companyId = company.id,
            personId = person1.id,
            beneficiaryType = BeneficiaryType.EMPLOYEE,
        )
        val dependent = TestModelFactory.buildBeneficiary(parentBeneficiary = beneficiaryView1.beneficiaryId)

        val beneficiaryView2 = TestModelFactory.buildBeneficiaryCompiledView(
            companyId = company.id,
            personId = person2.id,
            beneficiaryType = BeneficiaryType.EMPLOYEE,
        )
        val beneficiaries = listOf(beneficiaryView1, beneficiaryView2)

        val response1 = BasicInfoResponse(
            beneficiaryId = beneficiaryView1.beneficiaryId,
            fullName = person1.fullSocialName,
            nationalId = person1.nationalId,
            email = person1.email,
            dependentsCount = 1,
            address = person1.mainAddress,
            productId = beneficiaryView1.productId,
            subcontractId = beneficiary1.companySubContractId
        )
        val response2 = BasicInfoResponse(
            beneficiaryId = beneficiaryView2.beneficiaryId,
            fullName = person2.fullSocialName,
            nationalId = person2.nationalId,
            email = person2.email,
            dependentsCount = 0,
            address = person2.mainAddress,
            productId = beneficiaryView2.productId,
            subcontractId = beneficiary2.companySubContractId
        )

        val filters = BeneficiaryCompiledViewFilters(
            search = searchToken,
            companyId = company.id,
            beneficiaryType = listOf(BeneficiaryType.EMPLOYEE),
            insuranceStatus = listOf(
                BeneficiaryViewInsuranceStatus.ACTIVE,
                BeneficiaryViewInsuranceStatus.ACTIVATING,
                BeneficiaryViewInsuranceStatus.ACTIVE_WITH_ISSUES,
                BeneficiaryViewInsuranceStatus.LATE_ACTIVATION,
                BeneficiaryViewInsuranceStatus.PENDING_ACTIVATION
            ),
            range = IntRange(0, 100)
        )

        val expected = listOf(response1, response2)

        coEvery { companyService.get(company.id) } returns company
        coEvery { beneficiaryCompiledViewService.findByFilters(filters) } returns beneficiaries
        coEvery { personService.findByIds(beneficiaries.map { it.personId.toString() }) } returns people
        coEvery { beneficiaryService.findByParentId(beneficiaryView1.beneficiaryId) } returns listOf(dependent)
        coEvery { beneficiaryService.findByParentId(beneficiaryView2.beneficiaryId) } returns NotFoundException().failure()
        coEvery { beneficiaryService.findByPersonIds(beneficiaries.map{ it.personId }) } returns listOf(beneficiary1, beneficiary2)

        authenticatedAs(idToken, staffTest) {
            get("/company/${company.id}/beneficiaries/basic_infos?search_token=$searchToken") { response ->
                assertThat(response).isOKWithData(expected)
            }
        }

        coVerifyOnce { beneficiaryCompiledViewService.findByFilters(filters) }
        coVerifyOnce { personService.findByIds(beneficiaries.map { it.personId.toString() }) }
        coVerifyOnce { beneficiaryService.findByParentId(beneficiaryView1.beneficiaryId) }
        coVerifyOnce { beneficiaryService.findByParentId(beneficiaryView2.beneficiaryId) }
        coVerifyOnce { beneficiaryService.findByPersonIds(beneficiaries.map{ it.personId }) }
    }

    @Test
    fun `#getBasicInfo - should list basic info of people by searchToken filtering with ignorePersonIds based on FF`() =
        runBlocking {
            val personId = PersonId()
            withFeatureFlag(
                FeatureNamespace.BUSINESS,
                "should_be_ignored_person_ids_in_company",
                listOf(personId)
            ) {
                val searchToken = "Marco"
                val person1 = TestModelFactory.buildPerson(firstName = "Marco", lastName = "Costa")
                val person2 = TestModelFactory.buildPerson(firstName = "Marcos", lastName = "Antonio")
                val beneficiary1 = TestModelFactory.buildBeneficiary(personId = person1.id)
                val beneficiary2 = TestModelFactory.buildBeneficiary(personId = person2.id)
                val people = listOf(person1, person2)

                val beneficiaryView1 = TestModelFactory.buildBeneficiaryCompiledView(
                    companyId = company.id,
                    personId = person1.id,
                    beneficiaryType = BeneficiaryType.EMPLOYEE,
                )
                val dependent = TestModelFactory.buildBeneficiary(parentBeneficiary = beneficiaryView1.beneficiaryId)

                val beneficiaryView2 = TestModelFactory.buildBeneficiaryCompiledView(
                    companyId = company.id,
                    personId = person2.id,
                    beneficiaryType = BeneficiaryType.EMPLOYEE,
                )
                val beneficiaries = listOf(beneficiaryView1, beneficiaryView2)

                val response1 = BasicInfoResponse(
                    beneficiaryId = beneficiaryView1.beneficiaryId,
                    fullName = person1.fullSocialName,
                    nationalId = person1.nationalId,
                    email = person1.email,
                    dependentsCount = 1,
                    address = person1.mainAddress,
                    productId = beneficiaryView1.productId,
                    subcontractId = beneficiary1.companySubContractId
                )
                val response2 = BasicInfoResponse(
                    beneficiaryId = beneficiaryView2.beneficiaryId,
                    fullName = person2.fullSocialName,
                    nationalId = person2.nationalId,
                    email = person2.email,
                    dependentsCount = 0,
                    address = person2.mainAddress,
                    productId = beneficiaryView2.productId,
                    subcontractId = beneficiary2.companySubContractId
                )

                val filters = BeneficiaryCompiledViewFilters(
                    search = searchToken,
                    companyId = company.id,
                    ignorePersonIds = listOf(personId),
                    beneficiaryType = listOf(BeneficiaryType.EMPLOYEE),
                    insuranceStatus = listOf(
                        BeneficiaryViewInsuranceStatus.ACTIVE,
                        BeneficiaryViewInsuranceStatus.ACTIVATING,
                        BeneficiaryViewInsuranceStatus.ACTIVE_WITH_ISSUES,
                        BeneficiaryViewInsuranceStatus.LATE_ACTIVATION,
                        BeneficiaryViewInsuranceStatus.PENDING_ACTIVATION
                    ),
                    range = IntRange(0, 100)
                )

                val expected = listOf(response1, response2)

                coEvery { beneficiaryCompiledViewService.findByFilters(filters) } returns beneficiaries
                coEvery { personService.findByIds(beneficiaries.map { it.personId.toString() }) } returns people
                coEvery { beneficiaryService.findByParentId(beneficiaryView1.beneficiaryId) } returns listOf(dependent)
                coEvery { beneficiaryService.findByParentId(beneficiaryView2.beneficiaryId) } returns NotFoundException().failure()
                coEvery { beneficiaryService.findByPersonIds(beneficiaries.map{ it.personId }) } returns listOf(beneficiary1, beneficiary2)

                authenticatedAs(idToken, staffTest) {
                    get("/company/${company.id}/beneficiaries/basic_infos?search_token=$searchToken") { response ->
                        assertThat(response).isOKWithData(expected)
                    }
                }

                coVerifyOnce { beneficiaryCompiledViewService.findByFilters(filters) }
                coVerifyOnce { personService.findByIds(beneficiaries.map { it.personId.toString() }) }
                coVerifyOnce { beneficiaryService.findByParentId(beneficiaryView1.beneficiaryId) }
                coVerifyOnce { beneficiaryService.findByParentId(beneficiaryView2.beneficiaryId) }
                coVerifyOnce { beneficiaryService.findByPersonIds(beneficiaries.map{ it.personId }) }
            }
        }

    @Test
    fun `#getBasicInfo - should throw error when no searchToken is passed`() {
        authenticatedAs(idToken, staffTest) {
            get("/company/${company.id}/beneficiaries/basic_infos") { response ->
                assertThat(response).isBadRequest()
            }
        }
    }

    @Test
    fun `scheduleCancellation - schedule cancellation successfully`() {
        val canceledAt = LocalDate.now()
        val body = ScheduleCancelationRequest(
            canceledAt = canceledAt,
            canceledReason = BeneficiaryCancelationReason.ANOTHER,
            canceledDescription = "Empresa alterou CNPJ",
            hasContributed = false
        )
        val beneficiary = TestModelFactory.buildBeneficiary()

        val beneficiaryCanceled = beneficiary.copy(
            canceledReason = body.canceledReason,
            canceledDescription = body.canceledDescription,
        )

        coEvery { beneficiaryService.get(beneficiary.id, any()) } returns beneficiary
        coEvery { beneficiaryService.cancelBeneficiaryById(any(), any()) } returns beneficiaryCanceled
        coEvery { beneficiaryService.countActiveWithNoPendingCancellation(any()) } returns 100

        authenticatedAs(idToken, staffTest) {
            post(
                "/company/${company.id}/beneficiaries/${beneficiary.id}/schedule_cancellation",
                body = body
            ) { response ->
                assertThat(response).isOK()
            }
        }

        coVerifyOnce {
            beneficiaryService.get(beneficiary.id, any())
        }
        coVerifyOnce {
            beneficiaryService.cancelBeneficiaryById(
                beneficiary.id,
                BeneficiaryCancelation(
                    canceledAt,
                    BeneficiaryCancelationReason.ANOTHER,
                    "Empresa alterou CNPJ",
                    false
                )
            )
        }
    }

    @Test
    fun `scheduleCancellation - schedule cancellation return not found when person id from beneficiary should be ignored`() =
        runBlocking {
            val personId = PersonId()
            withFeatureFlag(
                FeatureNamespace.BUSINESS,
                "should_be_ignored_person_ids_in_company",
                listOf(personId)
            ) {
                val canceledAt = LocalDate.now()
                val body = ScheduleCancelationRequest(
                    canceledAt = canceledAt,
                    canceledReason = BeneficiaryCancelationReason.ANOTHER,
                    canceledDescription = "Empresa alterou CNPJ",
                    hasContributed = false
                )
                val beneficiary = TestModelFactory.buildBeneficiary(personId = personId)

                val beneficiaryCanceled = beneficiary.copy(
                    canceledReason = body.canceledReason,
                    canceledDescription = body.canceledDescription,
                )

                coEvery { beneficiaryService.get(beneficiary.id, any()) } returns beneficiary
                coEvery { beneficiaryService.cancelBeneficiaryById(any(), any()) } returns beneficiaryCanceled
                coEvery { beneficiaryService.countActiveWithNoPendingCancellation(any()) } returns 100

                authenticatedAs(idToken, staffTest) {
                    post(
                        "/company/${company.id}/beneficiaries/${beneficiary.id}/schedule_cancellation",
                        body = body
                    ) { response ->
                        assertThat(response).isNotFound()
                    }
                }

                coVerifyOnce {
                    beneficiaryService.get(beneficiary.id, any())
                }
                coVerifyNone {
                    beneficiaryService.cancelBeneficiaryById(
                        beneficiary.id,
                        BeneficiaryCancelation(
                            canceledAt,
                            BeneficiaryCancelationReason.ANOTHER,
                            "Empresa alterou CNPJ",
                            false
                        )
                    )
                }
            }
        }

    @Test
    fun `scheduleCancellation - schedule cancellation successfully when cancellation date is after today`() {
        val canceledAt = LocalDate.now().plusDays(1)
        val body = ScheduleCancelationRequest(
            canceledAt = canceledAt,
            canceledReason = BeneficiaryCancelationReason.EXCLUSION_REQUEST,
            canceledDescription = null,
            hasContributed = true
        )

        val beneficiary = TestModelFactory.buildBeneficiary()

        val beneficiaryCanceled = beneficiary.copy(
            canceledReason = body.canceledReason,
            canceledDescription = body.canceledDescription,
        )

        coEvery { beneficiaryService.get(beneficiary.id, any()) } returns beneficiary
        coEvery { beneficiaryService.cancelBeneficiaryById(any(), any()) } returns beneficiaryCanceled
        coEvery { beneficiaryService.countActiveWithNoPendingCancellation(any()) } returns 100


        authenticatedAs(idToken, staffTest) {
            post(
                "/company/${company.id}/beneficiaries/${beneficiary.id}/schedule_cancellation",
                body = body
            ) { response ->
                assertThat(response).isOK()
            }
        }

        coVerifyOnce { beneficiaryService.get(beneficiary.id, any()) }
        coVerifyOnce {
            beneficiaryService.cancelBeneficiaryById(
                beneficiary.id,
                BeneficiaryCancelation(
                    canceledAt,
                    BeneficiaryCancelationReason.EXCLUSION_REQUEST,
                    null,
                    true
                )
            )
        }
    }

    @Test
    fun `scheduleCancellation - should return expected error when is cancellation date is before today`() {
        val canceledAt = LocalDate.now().minusDays(1)
        val today = LocalDateTime.now().toLocalDate()

        val body = ScheduleCancelationRequest(
            canceledAt = canceledAt,
            canceledReason = BeneficiaryCancelationReason.EXCLUSION_REQUEST,
            canceledDescription = null,
            hasContributed = false
        )

        val beneficiary = TestModelFactory.buildBeneficiary()

        coEvery { beneficiaryService.get(beneficiary.id, any()) } returns beneficiary
        coEvery {
            beneficiaryService.cancelBeneficiaryById(any(), any())
        } returns InvalidBeneficiaryCanceledRequest(beneficiary.id, canceledAt, today)
        coEvery { beneficiaryService.countActiveWithNoPendingCancellation(any()) } returns 100

        authenticatedAs(idToken, staffTest) {
            post(
                "/company/${company.id}/beneficiaries/${beneficiary.id}/schedule_cancellation",
                body = body
            ) { response ->
                assertThat(response)
                    .isBadRequestWithErrorCode("invalid_beneficiary_canceled_request")
            }
        }

        coVerifyOnce { beneficiaryService.get(beneficiary.id, any()) }
        coVerifyOnce {
            beneficiaryService.cancelBeneficiaryById(
                beneficiary.id,
                BeneficiaryCancelation(
                    canceledAt,
                    BeneficiaryCancelationReason.EXCLUSION_REQUEST,
                    null,
                    false
                )
            )
        }
    }

    @Test
    fun `#scheduleCancellation should return error when company is not able to cancel members`() = runBlocking {
        val canceledAt = LocalDate.now()
        val body = ScheduleCancelationRequest(
            canceledAt = canceledAt,
            canceledReason = BeneficiaryCancelationReason.ANOTHER,
            canceledDescription = "Empresa alterou CNPJ",
            hasContributed = false
        )
        val beneficiary = TestModelFactory.buildBeneficiary(
            type = BeneficiaryType.EMPLOYEE,
            memberStatus = MemberStatus.ACTIVE
        )

        coEvery { beneficiaryService.get(beneficiary.id, any()) } returns beneficiary
        coEvery { beneficiaryService.countActiveWithNoPendingCancellation(any()) } returns 0

        authenticatedAs(idToken, staffTest) {
            post(
                "/company/${company.id}/beneficiaries/${beneficiary.id}/schedule_cancellation",
                body = body
            ) { response ->
                assertThat(response).isBadRequestWithErrorCode("company_not_allowed_to_cancel_beneficiary")
            }
        }

        coVerifyOnce { beneficiaryService.get(beneficiary.id, any()) }
        coVerifyNone { beneficiaryService.cancelBeneficiaryById(any(), any()) }
    }


    @Test
    fun `#validatePersonEntry - should return success when no validation is applied`() = runBlocking {
        val validatePersonRequestBody = ValidatePersonRequest()
        authenticatedAs(idToken, staffTest) {
            post("/company/${company.id}/beneficiaries/validate_person_entry", validatePersonRequestBody) { response ->
                assertThat(response).isOK()
            }

        }
    }

    @Test
    fun `#validatePersonEntry - should return the expected error when the national id is already used by the beneficiary holder`() =
        runBlocking {
            val person = TestModelFactory.buildPerson()
            val member = TestModelFactory.buildMember(personId = person.id)
            val parentBeneficiary = TestModelFactory.buildBeneficiary(personId = person.id)
            val beneficiary = TestModelFactory.buildBeneficiary(personId = person.id, memberId = member.id)

            coEvery { personService.findByNationalId(person.nationalId) } returns person
            coEvery { memberService.getCurrent(person.id) } returns member
            coEvery {
                beneficiaryService.getByIdAndPersonId(
                    parentBeneficiary.id,
                    person.id
                )
            } returns parentBeneficiary
            coEvery { beneficiaryService.findByCompanyIdAndPersonIds(company.id, listOf(person.id)) } returns listOf(
                beneficiary
            )

            val validatePersonRequestBody = ValidatePersonRequest(
                nationalId = person.nationalId,
                parentBeneficiaryId = parentBeneficiary.id,
            )

            authenticatedAs(idToken, staffTest) {
                post(
                    "/company/${company.id}/beneficiaries/validate_person_entry",
                    validatePersonRequestBody
                ) { response ->
                    assertThat(response)
                        .isUnprocessableEntity()
                        .withError(
                            "national_id_already_used_by_holder",
                            "Este CPF já foi usado pelo titular. Tente outro CPF"
                        )
                }
            }

            coVerifyOnce { personService.findByNationalId(person.nationalId) }
            coVerify { memberService.getCurrent(person.id) }
            coVerifyOnce { beneficiaryService.getByIdAndPersonId(parentBeneficiary.id, person.id) }
            coVerifyOnce { beneficiaryService.findByCompanyIdAndPersonIds(company.id, listOf(person.id)) }
        }

    @Test
    fun `#validatePersonEntry - should return the expected error when the national id is already used by some user`() =
        runBlocking {
            val person = TestModelFactory.buildPerson(email = "<EMAIL>")
            val member = TestModelFactory.buildMember(personId = person.id)

            coEvery { personService.findByNationalId(person.nationalId) } returns person
            coEvery { memberService.getCurrent(person.id) } returns member
            coEvery {
                beneficiaryService.findByCompanyIdAndPersonIds(
                    company.id,
                    listOf(person.id)
                )
            } returns NotFoundException()
            coEvery { companyService.get(company.id) } returns company

            val validatePersonRequestBody = ValidatePersonRequest(
                nationalId = person.nationalId,
            )
            authenticatedAs(idToken, staffTest) {
                post(
                    "/company/${company.id}/beneficiaries/validate_person_entry",
                    validatePersonRequestBody
                ) { response ->
                    assertThat(response)
                        .isUnprocessableEntity()
                        .withError(
                            expectedErrorCode = "already_alice_member_exception",
                            expectedErrorMessage = "Esse CPF já possui um plano ativo. Em breve entraremos em contato para te ajudar.",
                        )
                }
            }

            coVerifyOnce { personService.findByNationalId(person.nationalId) }
            coVerify { memberService.getCurrent(person.id) }
            coVerify { beneficiaryService.findByCompanyIdAndPersonIds(company.id, listOf(person.id)) }
        }

    @Test
    fun `#validatePersonEntry - should return success when the national id is already used by some user but its membership is canceled`() =
        runBlocking {
            val person = TestModelFactory.buildPerson(email = "<EMAIL>")
            val member = TestModelFactory.buildMember(personId = person.id, status = MemberStatus.CANCELED)
            val beneficiary = TestModelFactory.buildBeneficiary(
                personId = person.id,
                canceledAt = LocalDateTime.now(),
                memberId = member.id,
            )

            coEvery { personService.findByNationalId(person.nationalId) } returns person
            coEvery { memberService.getCurrent(person.id) } returns member
            coEvery { beneficiaryService.findByCompanyIdAndPersonIds(company.id, listOf(person.id)) } returns listOf(
                beneficiary
            )

            val validatePersonRequestBody = ValidatePersonRequest(
                nationalId = person.nationalId,
            )

            authenticatedAs(idToken, staffTest) {
                post(
                    "/company/${company.id}/beneficiaries/validate_person_entry",
                    validatePersonRequestBody,
                ) { response ->
                    assertThat(response).isOK()
                }
            }

            coVerifyOnce { personService.findByNationalId(person.nationalId) }
            coVerify { memberService.getCurrent(person.id) }
            coVerifyNone { beneficiaryService.findByCompanyIdAndPersonIds(company.id, listOf(person.id)) }
        }

    @Test
    fun `#validatePersonEntry - should return the expected error when the national id is already used by some user registered on the company`() =
        runBlocking {
            val person = TestModelFactory.buildPerson(email = "<EMAIL>")
            val member = TestModelFactory.buildMember(personId = person.id)
            val beneficiary = TestModelFactory.buildBeneficiary(personId = person.id, memberId = member.id)

            coEvery { personService.findByNationalId(person.nationalId) } returns person
            coEvery { memberService.getCurrent(person.id) } returns member
            coEvery { beneficiaryService.findByCompanyIdAndPersonIds(company.id, listOf(person.id)) } returns listOf(
                beneficiary
            )

            val validatePersonRequestBody = ValidatePersonRequest(
                nationalId = person.nationalId,
            )

            authenticatedAs(idToken, staffTest) {
                post(
                    "/company/${company.id}/beneficiaries/validate_person_entry",
                    validatePersonRequestBody
                ) { response ->
                    assertThat(response)
                        .isUnprocessableEntity()
                        .withError(
                            expectedErrorCode = "already_company_member",
                            expectedErrorMessage = "Esta pessoa já está cadastrada na sua empresa",
                        )
                }
            }

            coVerifyOnce { personService.findByNationalId(person.nationalId) }
            coVerify { beneficiaryService.findByCompanyIdAndPersonIds(company.id, listOf(person.id)) }
        }

    @Test
    fun `#validatePersonEntry - should return success when the nationalId is not used by some user`() = runBlocking {
        val person = TestModelFactory.buildPerson()
        val member = TestModelFactory.buildMember(personId = person.id)

        coEvery { personService.findByNationalId(person.nationalId) } returns NotFoundException()
        coEvery { memberService.getCurrent(person.id) } returns member
        coEvery {
            beneficiaryService.findByCompanyIdAndPersonIds(
                company.id,
                listOf(person.id)
            )
        } returns NotFoundException()


        val validatePersonRequestBody = ValidatePersonRequest(
            nationalId = person.nationalId
        )

        authenticatedAs(idToken, staffTest) {
            post("/company/${company.id}/beneficiaries/validate_person_entry", validatePersonRequestBody) { response ->
                assertThat(response)
                    .isOK()
            }
        }

        coVerifyOnce { personService.findByNationalId(person.nationalId) }
        coVerifyNone { memberService.getCurrent(person.id) }
        coVerifyNone { beneficiaryService.findByCompanyIdAndPersonIds(any(), any()) }
    }

    @Test
    fun `#validatePersonEntry - should return 422 when person has an active membership in another company`() = runBlocking {
        val person = TestModelFactory.buildPerson()
        val member = TestModelFactory.buildMember(personId = person.id)
        val anotherCompany = TestModelFactory.buildCompany()

        coEvery { personService.findByNationalId(person.nationalId) } returns person
        coEvery { memberService.getCurrent(person.id) } returns member
        coEvery { companyService.get(any()) } returns anotherCompany
        coEvery {
            beneficiaryService.findByCompanyIdAndPersonIds(
                company.id,
                listOf(person.id)
            )
        } returns NotFoundException()

        val validatePersonRequest = ValidatePersonRequest(
            nationalId = person.nationalId
        )

        authenticatedAs(idToken, staffTest) {
            post("/company/${company.id}/beneficiaries/validate_person_entry", validatePersonRequest) { response ->
                assertThat(response)
                    .isUnprocessableEntity()
                    .withError(
                        "already_alice_member_exception",
                        "Esse CPF já possui um plano ativo. Em breve entraremos em contato para te ajudar."
                    )
            }
        }

        coVerify { personService.findByNationalId(person.nationalId) }
        coVerify { memberService.getCurrent(person.id) }
        coVerify { beneficiaryService.findByCompanyIdAndPersonIds(company.id, listOf(person.id)) }
    }

    @Test
    fun `#addBeneficiaryHolder - should create beneficiary when company is small`() {
        val company = company.copy(companySize = CompanySize.SMALL)
        val productId = RangeUUID.generate()
        val personId = PersonId()
        val request = BeneficiaryEmployeeRequest(
            firstName = "Marco",
            lastName = "Costa",
            mothersName = "Maria Costa",
            nationalId = "*********02",
            email = "<EMAIL>",
            sex = Sex.MALE,
            birthDate = LocalDateTime.of(1997, 6, 2, 0, 0, 0),
            phoneNumber = "31994658111",
            activatedAt = LocalDateTime.of(2022, 6, 10, 0, 0, 0),
            address = Address(State.MG, "Ipatinga", "Mario de Andrade", "76", "ap"),
            productId = productId,
            contractType = BeneficiaryContractType.CLT,
            hiredAt = LocalDateTime.of(2022, 6, 10, 0, 0, 0),
            cnpj = null,
            subcontractId = null,
        )
        val beneficiaryTransport = request.toBeneficiaryTransport(company.id)
        val beneficiary = beneficiaryTransport.toBeneficiary(RangeUUID.generate(), personId)
        val sendTo = EmailAddress(
            email = companyStaff.email,
            name = companyStaff.fullName()
        )

        coEvery { companyService.get(company.id) } returns company
        coEvery { beneficiaryService.createBeneficiary(any(), any(), any(), any(), any()) } returns beneficiary
        coEvery { ongoingCompanyDealService.getByCompanyId(company.id) } returns listOf()
        coEvery { mailerService.sendInclusionEmail(sendTo, beneficiaryTransport) } returns emailReceipt

        authenticatedAs(idToken, staffTest) {
            post("/company/${company.id}/beneficiaries/holders", body = request) { response ->
                assertThat(response).isOKWithData(beneficiary)
            }
        }

        verifyOnce { ClientBeneficiaryMetric.beneficiaryCreatedIncrement(any(), HR_PORTAL) }
        coVerifyOnce { beneficiaryService.createBeneficiary(beneficiaryTransport, productId, FULL_RISK_FLOW, null) }
    }

    @Test
    fun `#addBeneficiaryDependent - should create beneficiary when company is small`() {
        val company = company.copy(companySize = CompanySize.SMALL)
        val parentBeneficiary = RangeUUID.generate()
        val personId = PersonId()
        val request = BeneficiaryDependentRequest(
            firstName = "Marco",
            lastName = "Costa",
            mothersName = "Maria Costa",
            nationalId = "*********02",
            email = "<EMAIL>",
            sex = Sex.MALE,
            birthDate = LocalDateTime.of(1997, 6, 2, 0, 0, 0),
            phoneNumber = "31994658111",
            activatedAt = LocalDateTime.of(2022, 6, 10, 0, 0, 0),
            address = Address(State.MG, "Ipatinga", "Mario de Andrade", "76", "ap"),
            productId = productId,
            parentBeneficiary = parentBeneficiary,
            parentBeneficiaryRelationType = ParentBeneficiaryRelationType.CHILD,
            parentBeneficiaryRelatedAt = LocalDateTime.of(1997, 6, 2, 0, 0, 0),
            cnpj = null,
            subcontractId = null,
        )
        val beneficiaryTransport = request.toBeneficiaryTransport(company.id)
        val beneficiary = beneficiaryTransport.toBeneficiary(RangeUUID.generate(), personId)
        val sendTo = EmailAddress(
            email = companyStaff.email,
            name = companyStaff.fullName()
        )

        coEvery { companyService.get(company.id) } returns company
        coEvery { beneficiaryService.createBeneficiary(any(), any(), any(), any(), any()) } returns beneficiary
        coEvery { ongoingCompanyDealService.getByCompanyId(company.id) } returns listOf()
        coEvery { mailerService.sendInclusionEmail(sendTo, beneficiaryTransport) } returns emailReceipt

        authenticatedAs(idToken, staffTest) {
            post("/company/${company.id}/beneficiaries/dependents", body = request) { response ->
                assertThat(response).isCreated()
            }
        }
        verifyOnce { ClientBeneficiaryMetric.beneficiaryCreatedIncrement(any(), HR_PORTAL) }
        coVerifyOnce { beneficiaryService.createBeneficiary(beneficiaryTransport, productId, FULL_RISK_FLOW, null) }
    }

    @Test
    fun `#addBeneficiaryHolder - should create beneficiary with tags when it came from broker sale`() {
        val productId = RangeUUID.generate()
        val personId = PersonId()
        val request = BeneficiaryEmployeeRequest(
            firstName = "Marco",
            lastName = "Costa",
            mothersName = "Maria Costa",
            nationalId = "*********02",
            email = "<EMAIL>",
            sex = Sex.MALE,
            birthDate = LocalDateTime.of(1997, 6, 2, 0, 0, 0),
            phoneNumber = "31994658111",
            activatedAt = LocalDateTime.of(2022, 6, 10, 0, 0, 0),
            address = Address(State.MG, "Ipatinga", "Mario de Andrade", "76", "ap"),
            productId = productId,
            contractType = BeneficiaryContractType.CLT,
            hiredAt = LocalDateTime.of(2022, 6, 10, 0, 0, 0),
            cnpj = null,
            subcontractId = null,
        )
        val beneficiaryTransport = request.toBeneficiaryTransport(company.id)
        val beneficiary = beneficiaryTransport.toBeneficiary(RangeUUID.generate(), personId)
        val ongoingCompanyDeal = TestModelFactory.buildOngoingCompanyDeal(companyId = company.id, channel = DealChannel.BROKER)

        coEvery { companyService.get(company.id) } returns company
        coEvery { beneficiaryService.createBeneficiary(any(), any(), any(), any(), any()) } returns beneficiary
        coEvery { ongoingCompanyDealService.getByCompanyId(company.id) } returns listOf(ongoingCompanyDeal)
        coEvery { mailerService.sendInclusionEmail(any(), any()) } returns emailReceipt

        authenticatedAs(idToken, staffTest) {
            post("/company/${company.id}/beneficiaries/holders", body = request) { response ->
                assertThat(response).isOKWithData(beneficiary)
            }
        }

        verifyOnce { ClientBeneficiaryMetric.beneficiaryCreatedIncrement(any(), HR_PORTAL) }
        coVerifyOnce {
            beneficiaryService.createBeneficiary(
                beneficiaryTransport.copy(tags = listOf("3p")),
                productId,
                UNDEFINED,
                null
            )
        }
    }

    @Test
    fun `#addBeneficiaryDependent - should create beneficiary with tags when it came from broker sale`() {
        val parentBeneficiary = RangeUUID.generate()
        val personId = PersonId()
        val request = BeneficiaryDependentRequest(
            firstName = "Marco",
            lastName = "Costa",
            mothersName = "Maria Costa",
            nationalId = "*********02",
            email = "<EMAIL>",
            sex = Sex.MALE,
            birthDate = LocalDateTime.of(1997, 6, 2, 0, 0, 0),
            phoneNumber = "31994658111",
            activatedAt = LocalDateTime.of(2022, 6, 10, 0, 0, 0),
            address = Address(State.MG, "Ipatinga", "Mario de Andrade", "76", "ap"),
            productId = productId,
            parentBeneficiary = parentBeneficiary,
            parentBeneficiaryRelationType = ParentBeneficiaryRelationType.CHILD,
            parentBeneficiaryRelatedAt = LocalDateTime.of(1997, 6, 2, 0, 0, 0),
            cnpj = null,
            subcontractId = null,
        )
        val beneficiaryTransport = request.toBeneficiaryTransport(company.id)
        val beneficiary = beneficiaryTransport.toBeneficiary(RangeUUID.generate(), personId)
        val ongoingCompanyDeal = TestModelFactory.buildOngoingCompanyDeal(companyId = company.id, channel = DealChannel.BROKER)

        coEvery { companyService.get(company.id) } returns company
        coEvery { beneficiaryService.createBeneficiary(any(), any(), any(), any(), any()) } returns beneficiary
        coEvery { ongoingCompanyDealService.getByCompanyId(company.id) } returns listOf(ongoingCompanyDeal)
        coEvery { mailerService.sendInclusionEmail(any(), any()) } returns emailReceipt

        authenticatedAs(idToken, staffTest) {
            post("/company/${company.id}/beneficiaries/dependents", body = request) { response ->
                assertThat(response).isCreated()
            }
        }
        verifyOnce { ClientBeneficiaryMetric.beneficiaryCreatedIncrement(any(), HR_PORTAL) }
        coVerifyOnce {
            beneficiaryService.createBeneficiary(
                beneficiaryTransport.copy(tags = listOf("3p")),
                productId,
                UNDEFINED,
                null
            )
        }
    }
}
