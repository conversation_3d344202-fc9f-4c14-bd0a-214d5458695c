package br.com.alice.zendeskintegrationservice.clients

import br.com.alice.common.RangeUUID
import br.com.alice.common.data.dsl.matchers.ResultAssert
import br.com.alice.common.service.serialization.gsonSnakeCase
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.MemberStatus
import br.com.alice.zendesk.transport.MultiSelectOption
import br.com.alice.zendesk.transport.SunshineConversationMessageAuthorType
import br.com.alice.zendesk.transport.SunshineConversationMessageContentType
import br.com.alice.zendesk.transport.ZendeskConversationMessage
import br.com.alice.zendesk.transport.ZendeskConversationMessageAuthor
import br.com.alice.zendesk.transport.ZendeskConversationMessageContent
import br.com.alice.zendesk.transport.ZendeskOrganization
import br.com.alice.zendesk.transport.ZendeskOrganizationRequest
import br.com.alice.zendesk.transport.ZendeskTicket
import br.com.alice.zendesk.transport.ZendeskTicketCustomField
import br.com.alice.zendesk.transport.ZendeskTicketCustomFieldRequest
import br.com.alice.zendesk.transport.ZendeskTicketField
import br.com.alice.zendesk.transport.ZendeskTicketFieldRequest
import br.com.alice.zendesk.transport.ZendeskUser
import br.com.alice.zendesk.transport.ZendeskUserRequest
import br.com.alice.zendeskintegrationservice.exceptions.TicketWithoutConversationIdException
import br.com.alice.zendeskintegrationservice.exceptions.ZendeskDuplicatedOrganizationNameException
import io.ktor.client.HttpClient
import io.ktor.client.engine.mock.MockEngine
import io.ktor.client.engine.mock.respond
import io.ktor.client.plugins.contentnegotiation.ContentNegotiation
import io.ktor.http.HttpHeaders
import io.ktor.http.HttpMethod
import io.ktor.http.HttpStatusCode
import io.ktor.http.headersOf
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.TestInstance
import java.math.BigInteger
import kotlin.test.Test

@TestInstance(TestInstance.Lifecycle.PER_CLASS)
class ZendeskClientTest {
    private val billingAccountableParty = TestModelFactory.buildBillingAccountableParty()
    private val product = TestModelFactory.buildProduct()
    private val person = TestModelFactory.buildPerson()
    private val zendeskUserRequest =
        ZendeskUserRequest(
            person = person,
            externalId = RangeUUID.generate(),
            organizationId = "12345",
            memberStatus = MemberStatus.ACTIVE,
            billingAccountablePartyEmail = billingAccountableParty.email,
            product = product
        )

    private val sunshineConversationMessageRequest = ZendeskConversationMessage(
        ZendeskConversationMessageAuthor(SunshineConversationMessageAuthorType.business),
        ZendeskConversationMessageContent(SunshineConversationMessageContentType.text, "Hi test")
    )

    private val company = TestModelFactory.buildCompany()
    private val zendeskOrganizationRequest = ZendeskOrganizationRequest(
        company,
        RangeUUID.generate()
    )

    private val ticketFieldRequest = ZendeskTicketFieldRequest.multiSelect(
        id = BigInteger.valueOf(**************),
        customFieldOptions = listOf(
            MultiSelectOption(
                name = "option_1",
                value = "value_1"
            ),
            MultiSelectOption(
                name = "option_2",
                value = "value_2"
            )
        )
    )

    private val ticketCustomFieldRequest =
        ZendeskTicketCustomFieldRequest(
            id = BigInteger.valueOf(**************),
            listOf(
                ZendeskTicketCustomField(BigInteger.valueOf(**************), "XXXXXXAAAAMMDDNNNNNN"),
            )
        )

    @Test
    fun `#createOrUpdateUser should return user response successfully`() = runBlocking {
        val httpClient =
            httpClientMock(
                responseContent = "{\"user\": {\"id\": 1234}}",
                httpMethod = HttpMethod.Post,
                url = "https://alice7091703598970.zendesk.com/api/v2/users/create_or_update"
            )

        val client = ZendeskClient(httpClient)
        val result = client.createOrUpdateUser(zendeskUserRequest)

        val contentExpected = ZendeskUser(1234)

        ResultAssert.assertThat(result).isSuccessWithData(contentExpected)
    }

    @Test
    fun `#createOrUpdateUser should handle server error`(): Unit = runBlocking {
        val httpClient =
            httpClientMock(
                responseContent = "{\"error\": \"Server error message\"}", // Simulate an error response
                statusCode = HttpStatusCode.InternalServerError, // Simulate a server error status code
                httpMethod = HttpMethod.Post,
                url = "https://alice7091703598970.zendesk.com/api/v2/users/create_or_update"
            )

        val client = ZendeskClient(httpClient)
        val result = client.createOrUpdateUser(zendeskUserRequest)

        ResultAssert.assertThat(result).isFailure()
    }

    @Test
    fun `#createOrUpdateOrganization should return organization response successfully`() = runBlocking {
        val httpClient =
            httpClientMock(
                responseContent = "{  \"organization\": { id: 1234}}",
                httpMethod = HttpMethod.Post,
                url = "https://alice7091703598970.zendesk.com/api/v2/organizations/create_or_update"
            )

        val client = ZendeskClient(httpClient)
        val result = client.createOrUpdateOrganization(zendeskOrganizationRequest)

        val contentExpected = ZendeskOrganization(1234)
        ResultAssert.assertThat(result).isSuccessWithData(contentExpected)
    }

    @Test
    fun `#createOrUpdateOrganization should handle internal server error`(): Unit = runBlocking {
        val httpClient =
            httpClientMock(
                responseContent = "{\"error\": \"Server error message\"}",
                statusCode = HttpStatusCode.InternalServerError,
                httpMethod = HttpMethod.Post,
                url = "https://alice7091703598970.zendesk.com/api/v2/organizations/create_or_update"
            )

        val client = ZendeskClient(httpClient)
        val result = client.createOrUpdateOrganization(zendeskOrganizationRequest)

        ResultAssert.assertThat(result).isFailure()
    }

    @Test
    fun `#createOrUpdateOrganization should handle duplicated organization name`(): Unit = runBlocking {
        val httpClient =
            httpClientMock(
                responseContent = "{\"error\":\"RecordInvalid\",\"description\":\"Record validation errors\",\"details\":{\"name\":[{\"description\":\"Name: Dois Irmãos has already been taken\",\"error\":\"DuplicateValue\"}]}}",
                statusCode = HttpStatusCode.UnprocessableEntity,
                httpMethod = HttpMethod.Post,
                url = "https://alice7091703598970.zendesk.com/api/v2/organizations/create_or_update"
            )

        val client = ZendeskClient(httpClient)
        val result = client.createOrUpdateOrganization(zendeskOrganizationRequest)

        ResultAssert.assertThat(result).isFailureOfType(ZendeskDuplicatedOrganizationNameException::class)
    }

    @Test
    fun `#updateTicketField should return ticket field response successfully`() = runBlocking {
        val httpClient =
            httpClientMock(
                responseContent = "{\"ticket_field\": {\"id\": **************}}",
                httpMethod = HttpMethod.Put,
                url = "https://alice7091703598970.zendesk.com/api/v2/ticket_fields/${ticketFieldRequest.id}"
            )

        val client = ZendeskClient(httpClient)
        val result = client.updateTicketField(ticketFieldRequest)

        val contentExpected = ZendeskTicketField(BigInteger.valueOf(**************))

        ResultAssert.assertThat(result).isSuccessWithData(contentExpected)
    }

    @Test
    fun `#updateTicketField should handle server error`(): Unit = runBlocking {
        val httpClient =
            httpClientMock(
                responseContent = "{\"error\": \"Server error message\"}", // Simulate an error response
                statusCode = HttpStatusCode.InternalServerError, // Simulate a server error status code
                httpMethod = HttpMethod.Put,
                url = "https://alice7091703598970.zendesk.com/api/v2/ticket_fields/${ticketFieldRequest.id}"
            )

        val client = ZendeskClient(httpClient)
        val result = client.updateTicketField(ticketFieldRequest)

        ResultAssert.assertThat(result).isFailure()
    }

    @Test
    fun `#sendConversationMessage should return response successfully`() = runBlocking {
        val httpClient =
            httpClientMock(
                responseContent = "{\"author\":{\"type\":\"business\"},\"content\":{\"type\":\"text\",\"text\":\"Hi test\"}}",
                httpMethod = HttpMethod.Post,
                url = "https://api.smooch.io/v2/apps/658adb9e4c6a97e7e78da601/conversations/123/messages"
            )

        val client = ZendeskClient(httpClient)
        val result = client.sendConversationMessage("123", sunshineConversationMessageRequest)

        ResultAssert.assertThat(result).isSuccess()
        return@runBlocking
    }

    @Test
    fun `#sendConversationMessage should handle server error`(): Unit = runBlocking {
        val httpClient =
            httpClientMock(
                responseContent = "{\"author\":{\"type\":\"business\"},\"content\":{\"type\":\"text\",\"text\":\"Hi test\"}}",
                httpMethod = HttpMethod.Post,
                url = "https://api.smooch.io/v2/apps/658adb9e4c6a97e7e78da601/conversations/123/messages"
            )

        val client = ZendeskClient(httpClient)
        val result = client.sendConversationMessage("1234", sunshineConversationMessageRequest)
        ResultAssert.assertThat(result).isFailure()
    }

    @Test
    fun `#updateTicketCustomField should return ticket response successfully`() = runBlocking {
        val httpClient =
            httpClientMock(
                responseContent = "{\"ticket\": {\"id\": 23270479307028}}",
                httpMethod = HttpMethod.Put,
                url = "https://alice7091703598970.zendesk.com/api/v2/tickets/${ticketCustomFieldRequest.id}"
            )

        val client = ZendeskClient(httpClient)
        val result = client.updateTicketCustomField(ticketCustomFieldRequest)

        val contentExpected = ZendeskTicket(BigInteger.valueOf(23270479307028))

        ResultAssert.assertThat(result).isSuccessWithData(contentExpected)
    }

    @Test
    fun `#updateTicketCustomField should handle server error`(): Unit = runBlocking {
        val httpClient =
            httpClientMock(
                responseContent = "{\"error\": \"Server error message\"}", // Simulate an error response
                statusCode = HttpStatusCode.InternalServerError, // Simulate a server error status code
                httpMethod = HttpMethod.Put,
                url = "https://alice7091703598970.zendesk.com/api/v2/tickets/${ticketCustomFieldRequest.id}"
            )

        val client = ZendeskClient(httpClient)
        val result = client.updateTicketCustomField(ticketCustomFieldRequest)

        ResultAssert.assertThat(result).isFailure()
    }

    @Test
    fun `#getConversationIdByTicketId should return conversationId successfully`() = runBlocking {
        val httpClient =
            httpClientMock(
                responseContent = "{\"audits\":[{\"events\":[{\"type\":\"ChatStartedEvent\",\"value\":{\"conversation_id\":\"65c27389ef54ab1a71a2ca62\"}}]}]}",
                httpMethod = HttpMethod.Get ,
                url = "https://alice7091703598970.zendesk.com/api/v2/tickets/123/audits.json"
            )

        val client = ZendeskClient(httpClient)
        val result = client.getConversationIdByTicketId("123")

        val contentExpected = "65c27389ef54ab1a71a2ca62"

        ResultAssert.assertThat(result).isSuccessWithData(contentExpected)
    }

    @Test
    fun `#getConversationIdByTicketId should return conversationId in the second event successfully`() = runBlocking {
        val httpClient =
            httpClientMock(
                responseContent = "{\"audits\":[{\"events\":[{\"type\":\"ChatInitEvent\",\"value\":\"123\"},{\"type\":\"ChatStartedEvent\",\"value\":{\"conversation_id\":\"65c27389ef54ab1a71a2ca62\"}}]}]}",
                httpMethod = HttpMethod.Get ,
                url = "https://alice7091703598970.zendesk.com/api/v2/tickets/123/audits.json"
            )

        val client = ZendeskClient(httpClient)
        val result = client.getConversationIdByTicketId("123")

        val contentExpected = "65c27389ef54ab1a71a2ca62"

        ResultAssert.assertThat(result).isSuccessWithData(contentExpected)
    }

    @Test
    fun `#getConversationIdByTicketId should return conversationId in the second audit node successfully`() = runBlocking {
        val httpClient =
            httpClientMock(
                responseContent = "{\"audits\":[{\"events\":[{\"type\":\"ChatInitEvent\",\"value\":\"123\"}]},{\"events\":[{\"type\":\"ChatStartedEvent\",\"value\":{\"conversation_id\":\"65c27389ef54ab1a71a2ca62\"}}]}]}",
                httpMethod = HttpMethod.Get ,
                url = "https://alice7091703598970.zendesk.com/api/v2/tickets/123/audits.json"
            )

        val client = ZendeskClient(httpClient)
        val result = client.getConversationIdByTicketId("123")

        val contentExpected = "65c27389ef54ab1a71a2ca62"

        ResultAssert.assertThat(result).isSuccessWithData(contentExpected)
    }

    @Test
    fun `#getConversationIdByTicketId should throw TicketWithoutConversationIdException`() = runBlocking {
        val httpClient =
            httpClientMock(
                responseContent = "{\"audits\":[{\"events\":[{\"type\":\"WrongEvent\",\"value\":{\"conversation_id\":\"65c27389ef54ab1a71a2ca62\"}}]}]}",
                httpMethod = HttpMethod.Get ,
                url = "https://alice7091703598970.zendesk.com/api/v2/tickets/123/audits.json"
            )

        val client = ZendeskClient(httpClient)
        val result = client.getConversationIdByTicketId("123")

        ResultAssert.assertThat(result).isFailureOfType(TicketWithoutConversationIdException::class)
    }

    @Test
    fun `#getConversationIdByTicketId should handle server error`(): Unit = runBlocking {
        val httpClient =
            httpClientMock(
                responseContent = "{\"audits\":[{\"events\":[{\"type\":\"ChatStartedEvent\",\"value\":{\"conversation_id\":\"65c27389ef54ab1a71a2ca62\"}}]}]}",
                httpMethod = HttpMethod.Get ,
                url = "https://alice7091703598970.zendesk.com/api/v2/tickets/1/audits.json"
            )

        val client = ZendeskClient(httpClient)
        val result = client.getConversationIdByTicketId("123")

        ResultAssert.assertThat(result).isFailure()
    }

    private fun httpClientMock(
        responseContent: String = "",
        statusCode: HttpStatusCode = HttpStatusCode.OK,
        url: String,
        httpMethod: HttpMethod,
    ): HttpClient {
        return HttpClient(MockEngine) {
            install(ContentNegotiation) {
                gsonSnakeCase()
            }
            engine {
                addHandler { request ->
                    if ((request.method == httpMethod && request.url.toString() == url)) {
                        respond(
                            responseContent,
                            statusCode,
                            headers = headersOf(HttpHeaders.ContentType, "application/json")
                        )
                    } else {
                        error("unknown request")
                    }
                }
            }
        }
    }
}
