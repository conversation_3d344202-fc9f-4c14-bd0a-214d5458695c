package br.com.alice.channel.event

import br.com.alice.channel.SERVICE_NAME
import br.com.alice.common.notification.NotificationEvent
import br.com.alice.data.layer.models.HealthcareTeamChannel

class HealthcareTeamChannelUpsertedEvent(healthcareTeamChannel: HealthcareTeamChannel) : NotificationEvent<HealthcareTeamUpsertedPayload>(
    name = name,
    producer = SERVICE_NAME,
    payload = HealthcareTeamUpsertedPayload(healthcareTeamChannel = healthcareTeamChannel)
) {
    companion object {
        const val name = "healthcare-team-channel-upserted"
    }
}

data class HealthcareTeamUpsertedPayload(
    val healthcareTeamChannel: HealthcareTeamChannel
)
