package br.com.alice.member.api.converters.onboarding

import br.com.alice.app.content.model.ActionRouting
import br.com.alice.app.content.model.RemoteActionMethod
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.MemberOnboarding.MemberOnboardingStepType.COVER
import br.com.alice.data.layer.models.MemberOnboarding.MemberOnboardingStepType.CONCLUSION
import br.com.alice.data.layer.models.MemberOnboarding.MemberOnboardingStepStatus
import br.com.alice.data.layer.models.MemberOnboarding.MemberOnboardingStepStatus.PENDING
import br.com.alice.data.layer.models.MemberOnboarding.MemberOnboardingStepStatus.BLOCKED
import br.com.alice.data.layer.models.Step
import br.com.alice.member.api.models.appContent.RemoteAction
import br.com.alice.member.api.models.onboarding.v2.MemberOnboardingResponse
import br.com.alice.member.api.models.onboarding.v2.MemberOnboardingStepResponse
import br.com.alice.member.onboarding.factory.OnboardingFactory
import br.com.alice.member.onboarding.model.OnboardingCoverButton
import br.com.alice.member.onboarding.model.OnboardingTemplate
import br.com.alice.member.onboarding.model.OnboardingVersion
import org.assertj.core.api.Assertions.assertThat
import java.time.LocalDateTime
import kotlin.test.Test

class MemberOnboardingConverterTest {

   private val defaultChildSteps = listOf(
      Step(
         templateType = COVER,
         status = PENDING
      ),
      Step(
         templateType = CONCLUSION,
         status = BLOCKED
      )
   )

   private val memberOnboarding = TestModelFactory.buildMemberOnboarding(steps = defaultChildSteps)
   private val memberActivationDate = LocalDateTime.now()
   private val stepsResponse = defaultChildSteps.map {
      buildStepResponse(
         template = OnboardingFactory.get(
            type = it.templateType,
            flowType = memberOnboarding.flowType,
            version = OnboardingVersion.V1,
            memberActivationDate = memberActivationDate
         ),
         status = it.status
      )
   }
    
   @Test
   fun `#convert converts MemberOnboarding to MemberOnboardingResponse correctly`() {
      val expected = MemberOnboardingResponse(
         id = memberOnboarding.id,
         title = "Complete esta etapa",
         description = "Conclua a etapa abaixo para desbloquear o primeiro Plano de Ação do seu dependente.",
         stepsDone = 0,
         isMemberActive = false,
         steps = stepsResponse
      )

      val result = MemberOnboardingConverter.convert(
         source = memberOnboarding,
         isMemberActive = false,
         memberActivationDate = memberActivationDate
      )

      assertThat(result).isEqualTo(expected)
   }

   @Test
   fun `#convert converts MemberOnboarding to MemberOnboardingResponse with button when member is active`() {
      val expected = MemberOnboardingResponse(
         id = memberOnboarding.id,
         title = "Complete esta etapa",
         description = "Conclua a etapa abaixo para desbloquear o primeiro Plano de Ação do seu dependente.",
         stepsDone = 0,
         isMemberActive = true,
         steps = stepsResponse,
         button = OnboardingCoverButton(
            label = "Confira detalhes do plano",
            mobileRoute = ActionRouting.CHESHIRE_SCREEN,
            params = mapOf(
               "action" to RemoteAction(
                  method = RemoteActionMethod.GET,
                  endpoint = "http://localhost/app_content/screen/plan_details_menu",
                  params = mapOf("screen_id" to "plan_details_menu")
               )
            )
         )
      )

      val result = MemberOnboardingConverter.convert(
         source = memberOnboarding,
         version = OnboardingVersion.V1,
         isMemberActive = true,
         memberActivationDate = memberActivationDate
      )

      assertThat(result).isEqualTo(expected)
   }

   private fun buildStepResponse(template: OnboardingTemplate, status: MemberOnboardingStepStatus) =
      MemberOnboardingStepResponse(
         type = template.type,
         status = status,
         title = template.title,
         timeToComplete = template.timeToComplete,
         url = template.url,
         path = template.path,
         bottomSheet = template.bottomSheet,
         backgroundImage = template.backgroundImage,
      )
}
