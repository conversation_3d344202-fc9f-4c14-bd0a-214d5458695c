package br.com.alice.questionnaire.services

import br.com.alice.app.content.client.AppContentScreenDetailService
import br.com.alice.common.data.dsl.matchers.ResultAssert
import br.com.alice.common.helpers.coVerifyNone
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.communication.crm.analytics.AnalyticsEventName
import br.com.alice.communication.crm.analytics.AnalyticsTrackerResult
import br.com.alice.communication.crm.analytics.CrmAnalyticsTracker
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.HealthFormAnswerSourceType
import br.com.alice.data.layer.models.ScreenDetailSectionContext
import br.com.alice.data.layer.models.ScreenDetailSectionType
import br.com.alice.data.layer.models.ScreenDetailStatus
import br.com.alice.data.layer.models.ScreenDetailType
import br.com.alice.person.client.PersonService
import com.github.kittinunf.result.success
import io.mockk.coEvery
import io.mockk.mockk
import kotlinx.coroutines.runBlocking
import java.time.LocalDateTime
import kotlin.test.Test

class HomeCardFormServiceImplTest {
    private val appContentScreenDetailService: AppContentScreenDetailService = mockk()
    private val personService: PersonService = mockk()
    private val crmAnalyticsTracker: CrmAnalyticsTracker = mockk()

    private val homeCardFormService = HomeCardFormServiceImpl(
        appContentScreenDetailService,
        personService,
        crmAnalyticsTracker,
    )

    private val person = TestModelFactory.buildPerson()
    private val appContentScreenDetail = TestModelFactory.buildAppContentScreenDetail(
        personId = person.id,
        screenType = ScreenDetailType.HOME,
        sectionType = ScreenDetailSectionType.QUESTIONNAIRE_SECTION,
        sectionContent = "",
    )

    @Test
    fun `#createHomeCardForm - Should create home card form`() = runBlocking {
        coEvery { personService.get(person.id) } returns person.success()
        coEvery {
            appContentScreenDetailService.create(
                match {
                    it.screenType == ScreenDetailType.HOME &&
                            it.sectionType == ScreenDetailSectionType.QUESTIONNAIRE_SECTION &&
                            it.personId == person.id &&
                            it.sectionContext == ScreenDetailSectionContext.QUESTIONNAIRE &&
                            it.position == ScreenDetailSectionContext.QUESTIONNAIRE.position
                }
            )
        } returns appContentScreenDetail.success()

        coEvery {
            crmAnalyticsTracker.sendEvent(
                nationalId = person.nationalId,
                event = match {
                    it.name == AnalyticsEventName.TRIGGER_PUSH_NOTIFICATION
                }
            )
        } returns AnalyticsTrackerResult(true)

        val result = homeCardFormService.createHomeCardForm(
            personId = person.id,
            questionnaireKey = "TEST_QUEST",
            startAt = LocalDateTime.now(),
            healthDemandName = "robson"
        )
        ResultAssert.assertThat(result).isSuccess()

        coVerifyOnce {
            appContentScreenDetailService.create(
                match {
                    it.screenType == ScreenDetailType.HOME &&
                            it.sectionType == ScreenDetailSectionType.QUESTIONNAIRE_SECTION &&
                            it.personId == person.id &&
                            it.healthDemandName == "robson" &&
                            it.sectionContext == ScreenDetailSectionContext.QUESTIONNAIRE &&
                            it.position == ScreenDetailSectionContext.QUESTIONNAIRE.position
                }
            )
        }
        coVerifyNone { personService.get(person.id) }
        coVerifyNone {
            crmAnalyticsTracker.sendEvent(
                nationalId = person.nationalId,
                event = match {
                    it.name == AnalyticsEventName.TRIGGER_PUSH_NOTIFICATION
                }
            )
        }
    }

    @Test
    fun `#createHomeCardForm - Should create home card form with push notification`() = runBlocking {
        coEvery { personService.get(person.id) } returns person.success()
        coEvery {
            appContentScreenDetailService.create(
                match {
                    it.screenType == ScreenDetailType.HOME &&
                            it.sectionType == ScreenDetailSectionType.QUESTIONNAIRE_SECTION &&
                            it.personId == person.id &&
                            it.sectionContext == ScreenDetailSectionContext.QUESTIONNAIRE &&
                            it.position == ScreenDetailSectionContext.QUESTIONNAIRE.position
                }
            )
        } returns appContentScreenDetail.success()

        coEvery {
            crmAnalyticsTracker.sendEvent(
                nationalId = person.nationalId,
                event = match {
                    it.name == AnalyticsEventName.TRIGGER_PUSH_NOTIFICATION
                }
            )
        } returns AnalyticsTrackerResult(true)

        val result = homeCardFormService.createHomeCardForm(
            personId = person.id,
            questionnaireKey = "MELHORA_REPORTADA_DESFECHO",
            startAt = LocalDateTime.now(),
            sourceId = "Teste",
            sourceType = HealthFormAnswerSourceType.CHANNEL,
        )
        ResultAssert.assertThat(result).isSuccess()

        coVerifyOnce {
            appContentScreenDetailService.create(
                match {
                    it.screenType == ScreenDetailType.HOME &&
                            it.sectionType == ScreenDetailSectionType.QUESTIONNAIRE_SECTION &&
                            it.personId == person.id &&
                            it.sectionContext == ScreenDetailSectionContext.QUESTIONNAIRE &&
                            it.position == ScreenDetailSectionContext.QUESTIONNAIRE.position
                }
            )
        }
        coVerifyOnce { personService.get(person.id) }
        coVerifyOnce {
            crmAnalyticsTracker.sendEvent(
                nationalId = person.nationalId,
                event = match {
                    it.name == AnalyticsEventName.TRIGGER_PUSH_NOTIFICATION
                }
            )
        }
    }

    @Test
    fun `#inactivatePreviousHomeCardsByKey - Should inactivate active home card forms by key`() = runBlocking {
        coEvery {
            appContentScreenDetailService.checkHomeCardFormsByPersonIdAndQuestionnaireKey(
                personId = person.id,
                questionnaireKey = "TEST_QUEST"
            )
        } returns true.success()

        coEvery {
            appContentScreenDetailService.getHomeCardFormsByPersonIdAndQuestionnaireKey(
                personId = person.id,
                questionnaireKey = "TEST_QUEST"
            )
        } returns listOf(appContentScreenDetail).success()

        coEvery {
            appContentScreenDetailService.updateList(listOf(appContentScreenDetail.copy(status=ScreenDetailStatus.INACTIVE)))
        } returns listOf(appContentScreenDetail.copy(status=ScreenDetailStatus.INACTIVE)).success()

        val result = homeCardFormService.inactivatePreviousHomeCardsByKey(
            personId = person.id,
            questionnaireKey = "TEST_QUEST"
        )
        ResultAssert.assertThat(result).isSuccessWithData(listOf(appContentScreenDetail.copy(status=ScreenDetailStatus.INACTIVE)))

        coVerifyOnce {
            appContentScreenDetailService.checkHomeCardFormsByPersonIdAndQuestionnaireKey(
                personId = person.id,
                questionnaireKey = "TEST_QUEST"
            )
        }

        coVerifyOnce {
            appContentScreenDetailService.getHomeCardFormsByPersonIdAndQuestionnaireKey(
                personId = person.id,
                questionnaireKey = "TEST_QUEST"
            )
        }

        coVerifyOnce {
            appContentScreenDetailService.updateList(listOf(appContentScreenDetail.copy(status=ScreenDetailStatus.INACTIVE)))
        }
    }

    @Test
    fun `#inactivatePreviousHomeCardsByKey - Should not inactivate active home card forms by key when there is none`() = runBlocking {
        coEvery {
            appContentScreenDetailService.checkHomeCardFormsByPersonIdAndQuestionnaireKey(
                personId = person.id,
                questionnaireKey = "TEST_QUEST"
            )
        } returns false.success()

        val result = homeCardFormService.inactivatePreviousHomeCardsByKey(
            personId = person.id,
            questionnaireKey = "TEST_QUEST"
        )
        ResultAssert.assertThat(result).isSuccessWithData(emptyList())

        coVerifyOnce {
            appContentScreenDetailService.checkHomeCardFormsByPersonIdAndQuestionnaireKey(
                personId = person.id,
                questionnaireKey = "TEST_QUEST"
            )
        }

        coVerifyNone {
            appContentScreenDetailService.getHomeCardFormsByPersonIdAndQuestionnaireKey(
                personId = person.id,
                questionnaireKey = "TEST_QUEST"
            )
        }

        coVerifyNone {
            appContentScreenDetailService.updateList(any())
        }
    }

}
