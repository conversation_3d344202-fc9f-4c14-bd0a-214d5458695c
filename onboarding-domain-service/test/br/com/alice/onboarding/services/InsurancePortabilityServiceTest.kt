package br.com.alice.onboarding.services

import br.com.alice.common.ApplicationTest
import br.com.alice.common.RangeUUID
import br.com.alice.common.core.PersonId
import br.com.alice.common.core.exceptions.NotFoundException
import br.com.alice.common.core.extensions.then
import br.com.alice.common.data.dsl.matchers.ResultAssert.Companion.assertThat
import br.com.alice.common.models.Person
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.*
import br.com.alice.data.layer.services.InsurancePortabilityRequestModelDataService
import br.com.alice.membership.client.onboarding.OnboardingService
import br.com.alice.onboarding.ApplicationModule
import br.com.alice.onboarding.SERVICE_NAME
import br.com.alice.onboarding.client.InsurancePortabilityNotRequestedException
import br.com.alice.onboarding.client.InsurancePortabilityService
import br.com.alice.onboarding.client.PortabilityCannotBeApprovedException
import br.com.alice.onboarding.client.PortabilityCannotBeCancelledException
import br.com.alice.onboarding.client.PortabilityCannotBeChangedException
import br.com.alice.onboarding.client.PortabilityCannotBeDeclinedException
import br.com.alice.onboarding.client.PortabilityCannotBeRequestedException
import br.com.alice.onboarding.client.PortabilityMissingDocumentsCannotBeEmptyException
import br.com.alice.onboarding.client.PortabilityRequirementsCannotBeChangedException
import br.com.alice.onboarding.converters.toModel
import br.com.alice.onboarding.module
import br.com.alice.onboarding.services.portability.InsurancePortabilityQuestionFactory
import br.com.alice.onboarding.services.portability.questions.InsurancePortabilityQuestion
import br.com.alice.onboarding.services.portability.questions.InsurancePortabilityValidation
import com.github.kittinunf.result.failure
import com.github.kittinunf.result.success
import io.ktor.server.application.Application
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.mockk
import io.mockk.mockkObject
import io.mockk.mockkStatic
import io.mockk.slot
import kotlinx.coroutines.runBlocking
import org.assertj.core.api.Assertions
import org.koin.core.module.Module
import org.koin.ktor.ext.getKoin
import java.time.LocalDateTime
import kotlin.test.BeforeTest
import kotlin.test.Test

class InsurancePortabilityServiceTest : ApplicationTest() {

    override val serviceName = SERVICE_NAME

    private val data: InsurancePortabilityRequestModelDataService = mockk()

    private val onboardingService: OnboardingService = mockk()

    override val moduleFunction = { application: Application, module: Module ->
        val modules = ApplicationModule.dependencyInjectionModules + module +
                org.koin.dsl.module(createdAtStart = true) {
                    single { data }
                    single { onboardingService }
                }

        application.module(modules)
    }

    private val person = Person("<EMAIL>", PersonId())
    private val onboarding = TestModelFactory.buildPersonOnboarding(person.id)
    private val portabilityRequest = TestModelFactory.buildInsurancePortabilityRequest(person.id)
    private val portabilityRequestModel = portabilityRequest.toModel()
    private val token = person.id.toString()

    @BeforeTest
    override fun setup() {
        super.setup()
        mockkStatic(LocalDateTime::class)
        val now = LocalDateTime.now()
        coEvery { LocalDateTime.now() } returns now
    }

    @Test
    fun `#create should add a new pending review portability request`() = runBlocking {
        val portabilityRequest = TestModelFactory.buildInsurancePortabilityRequest(
            status = InsurancePortabilityRequestStatus.PENDING,
            step = InsurancePortabilityRequestStep.ANALYSIS,
            type = InsurancePortabilityRequestType.NORMAL,
            pendingAt = LocalDateTime.now(),
        )
        val portabilityRequestModel = portabilityRequest.toModel()

        val slot = slot<InsurancePortabilityRequestModel>()
        coEvery { data.findAllByPerson(portabilityRequest.personId) } returns emptyList<InsurancePortabilityRequestModel>().success()
        coEvery { data.add(capture(slot)) } returns portabilityRequestModel.success()

        authenticatedAs(token, person) {
            val insurancePortabilityService = testEngine.application.getKoin().get<InsurancePortabilityService>()
            val portabilityRequestResult =
                insurancePortabilityService.create(portabilityRequest.personId, InsurancePortabilityRequestType.NORMAL)
            val actual = slot.captured

            Assertions.assertThat(actual).usingRecursiveComparison().ignoringFields("id", "createdAt", "updatedAt")
                .isEqualTo(portabilityRequest)
            assertThat(portabilityRequestResult).isSuccessWithData(portabilityRequest)
        }
    }

    @Test
    fun `#create should throw error when person has pending request`() = runBlocking {
        val pendingPortabilityRequest = TestModelFactory.buildInsurancePortabilityRequest(status = InsurancePortabilityRequestStatus.PENDING)
        val pendingPortabilityRequestModel = pendingPortabilityRequest.toModel()
        
        coEvery { data.findAllByPerson(pendingPortabilityRequest.personId) } returns listOf(pendingPortabilityRequestModel).success()

        authenticatedAs(token, person) {
            val insurancePortabilityService = testEngine.application.getKoin().get<InsurancePortabilityService>()
            val portabilityRequestResult = insurancePortabilityService.create(
                pendingPortabilityRequest.personId,
                InsurancePortabilityRequestType.NORMAL
            )

            assertThat(portabilityRequestResult).isFailureOfType(PortabilityCannotBeRequestedException::class)
        }
    }

    @Test
    fun `#requestPortability should add when not found`() = runBlocking {
        val answers = listOf(
            InsurancePortabilityRequestAnswer(
                answer = "sim",
                questionType = InsurancePortabilityRequestQuestionType.MIN_GRACE_PERIOD
            )
        )

        val answersV2 = listOf(
            InsurancePortabilityRequestAnswerV2(
                answer = "sulamerica",
                questionType = InsurancePortabilityRequestQuestionTypeV2.CURRENT_HEALTH_INSURANCE
            )
        )

        val slot = slot<InsurancePortabilityRequestModel>()
        coEvery { data.add(capture(slot)) } returns portabilityRequestModel.success()
        coEvery { data.findAllByPerson(person.id) } returns emptyList<InsurancePortabilityRequestModel>().success()

        authenticatedAs(token, person) {
            val insurancePortabilityService = testEngine.application.getKoin().get<InsurancePortabilityService>()
            val portabilityRequestResult = insurancePortabilityService.request(person.id, answers, answersV2)
            val actual = slot.captured

            val expected = InsurancePortabilityRequest(
                personId = person.id,
                answers = answers,
                answersV2 = answersV2,
                status = InsurancePortabilityRequestStatus.CREATED
            )

            Assertions.assertThat(actual).usingRecursiveComparison().ignoringFields("id", "createdAt", "updatedAt")
                .isEqualTo(expected)
            assertThat(portabilityRequestResult).isSuccessWithData(portabilityRequest)
        }
    }

    @Test
    fun `#requestPortability should return error when pending request exists`() = runBlocking {
        val pendingRequest = TestModelFactory.buildInsurancePortabilityRequest(status = InsurancePortabilityRequestStatus.PENDING)
        val pendingRequestModel = pendingRequest.toModel()

        val answers = listOf(
            InsurancePortabilityRequestAnswer(
                answer = "sim",
                questionType = InsurancePortabilityRequestQuestionType.MIN_GRACE_PERIOD
            )
        )

        val answersV2 = listOf(
            InsurancePortabilityRequestAnswerV2(
                answer = "sulamerica",
                questionType = InsurancePortabilityRequestQuestionTypeV2.CURRENT_HEALTH_INSURANCE
            )
        )
        coEvery { data.findAllByPerson(person.id) } returns listOf(pendingRequestModel).success()

        authenticatedAs(token, person) {
            val insurancePortabilityService = testEngine.application.getKoin().get<InsurancePortabilityService>()
            val portabilityRequestResult = insurancePortabilityService.request(person.id, answers, answersV2)

            assertThat(portabilityRequestResult).isFailureOfType(PortabilityCannotBeRequestedException::class)

            coVerify(exactly = 0) { data.add(any()) }
        }
    }

    @Test
    fun `#requestPortability should return error when approved request for same product exists`() = runBlocking {
        val pendingRequest = TestModelFactory.buildInsurancePortabilityRequest(
            status = InsurancePortabilityRequestStatus.APPROVED,
            productId = RangeUUID.generate()
        )
        val pendingRequestModel = pendingRequest.toModel()

        val answers = listOf(
            InsurancePortabilityRequestAnswer(
                answer = "sim",
                questionType = InsurancePortabilityRequestQuestionType.MIN_GRACE_PERIOD
            )
        )

        val answersV2 = listOf(
            InsurancePortabilityRequestAnswerV2(
                answer = "sulamerica",
                questionType = InsurancePortabilityRequestQuestionTypeV2.CURRENT_HEALTH_INSURANCE
            )
        )
        coEvery { data.findAllByPerson(person.id) } returns listOf(pendingRequestModel).success()

        authenticatedAs(token, person) {
            val insurancePortabilityService = testEngine.application.getKoin().get<InsurancePortabilityService>()
            val portabilityRequestResult =
                insurancePortabilityService.request(person.id, answers, answersV2, pendingRequest.productId)


            assertThat(portabilityRequestResult).isFailureOfType(PortabilityCannotBeRequestedException::class)

            coVerify(exactly = 0) { data.add(any()) }
        }
    }

    @Test
    fun `#approve should return success when a portability was already approved`() = runBlocking {
        val approvedRequest = portabilityRequest.approve()

        authenticatedAs(token, person) {
            val insurancePortabilityService = testEngine.application.getKoin().get<InsurancePortabilityService>()
            val changeStatusResult = insurancePortabilityService.approve(approvedRequest, "")
            assertThat(changeStatusResult).isSuccessWithData(approvedRequest)

            coVerify(exactly = 0) { data.update(any()) }
        }
    }

    @Test
    fun `#approve should return failure when a person already received a contract`() = runBlocking {
        coEvery { onboardingService.findByPerson(person.id) } returns onboarding.copy(
            currentPhase = OnboardingPhase.PAYMENT
        ).success()

        authenticatedAs(token, person) {
            val insurancePortabilityService = testEngine.application.getKoin().get<InsurancePortabilityService>()
            val changeStatusResult = insurancePortabilityService.approve(portabilityRequest, "")
            assertThat(changeStatusResult).isFailureOfType(PortabilityCannotBeChangedException::class)

            coVerify(exactly = 0) { data.update(any()) }
        }
    }

    @Test
    fun `#approve should return update a portability and move person to WAITING_FOR_REVIEW phase when a portability was not approved`() =
        runBlocking {
            val slot = slot<InsurancePortabilityRequestModel>()

            val waitingForReviewOnboarding = TestModelFactory.buildPersonOnboarding(person.id).copy(
                currentPhase = OnboardingPhase.WAITING_FOR_REVIEW
            )

            val expectedPortabilityRequest = portabilityRequest.approve()
            val expectedPortabilityRequestModel = expectedPortabilityRequest.toModel()

            coEvery { onboardingService.findByPerson(person.id) } returns onboarding.copy(
                currentPhase = OnboardingPhase.HEALTH_DECLARATION_APPOINTMENT
            ).success()

            coEvery { data.update(capture(slot)) } returns expectedPortabilityRequestModel.success()
            coEvery {
                onboardingService.changePhaseTo(
                    person.id,
                    OnboardingPhase.WAITING_FOR_REVIEW
                )
            } returns waitingForReviewOnboarding.success()

            authenticatedAs(token, person) {
                val insurancePortabilityService = testEngine.application.getKoin().get<InsurancePortabilityService>()
                val changeStatusResult = insurancePortabilityService.approve(portabilityRequest, "")
                val actual = slot.captured

                Assertions.assertThat(actual).isEqualTo(expectedPortabilityRequestModel)
                assertThat(changeStatusResult).isSuccessWithData(expectedPortabilityRequest)

                coVerify(exactly = 1) { onboardingService.changePhaseTo(person.id, OnboardingPhase.WAITING_FOR_REVIEW) }
            }
        }

    @Test
    fun `#approveV3 should update a portability and change step`() =
        runBlocking {
            val slot = slot<InsurancePortabilityRequestModel>()

            val expectedPortabilityRequest = portabilityRequest
                .approve(
                    InsurancePortabilityRequestApprovedPackage.A1,
                    "insurance-code",
                    "ans-code"
                )
                .changeStep(InsurancePortabilityRequestStep.FINISHED)
                .copy(finishedAt = LocalDateTime.now())
            val expectedPortabilityRequestModel = expectedPortabilityRequest.toModel()

            coEvery { data.update(capture(slot)) } returns expectedPortabilityRequestModel.success()

            authenticatedAs(token, person) {
                val insurancePortabilityService = testEngine.application.getKoin().get<InsurancePortabilityService>()

                val changeStatusResult = insurancePortabilityService.approveV3(
                    portabilityRequest,
                    InsurancePortabilityRequestApprovedPackage.A1,
                    "insurance-code",
                    "ans-code"
                )
                val actual = slot.captured

                Assertions.assertThat(actual).usingRecursiveComparison().ignoringFields("id", "createdAt", "updatedAt")
                    .isEqualTo(expectedPortabilityRequest)
                assertThat(changeStatusResult).isSuccessWithDataIgnoringGivenFields(
                    expectedPortabilityRequest,
                    "id",
                    "createdAt",
                    "updatedAt"
                )
            }
        }

    @Test
    fun `#approveV3 should returns error if portability's already been approved`() =
        runBlocking {
            authenticatedAs(token, person) {
                val insurancePortabilityService = testEngine.application.getKoin().get<InsurancePortabilityService>()

                val changeStatusResult = insurancePortabilityService.approveV3(
                    portabilityRequest.copy(status = InsurancePortabilityRequestStatus.APPROVED),
                    InsurancePortabilityRequestApprovedPackage.A1,
                    "insurance-code",
                    "ans-code"
                )

                assertThat(changeStatusResult).isFailureOfType(PortabilityCannotBeApprovedException::class)
                coVerify(exactly = 0) { data.update(any()) }
            }
        }

    @Test
    fun `#approveV3 should returns error if portability's been declined`() =
        runBlocking {
            authenticatedAs(token, person) {
                val insurancePortabilityService = testEngine.application.getKoin().get<InsurancePortabilityService>()

                val changeStatusResult = insurancePortabilityService.approveV3(
                    portabilityRequest.copy(status = InsurancePortabilityRequestStatus.DECLINED),
                    InsurancePortabilityRequestApprovedPackage.A1,
                    "insurance-code",
                    "ans-code"
                )

                assertThat(changeStatusResult).isFailureOfType(PortabilityCannotBeApprovedException::class)
                coVerify(exactly = 0) { data.update(any()) }
            }
        }

    @Test
    fun `#approveSpecific should update a specific portability and change step`() =
        runBlocking {
            val slot = slot<InsurancePortabilityRequestModel>()

            val expectedPortabilityRequest = portabilityRequest
                .copy(
                    status = InsurancePortabilityRequestStatus.APPROVED,
                    type = InsurancePortabilityRequestType.SPECIFIC,
                    finishedAt = LocalDateTime.now(),
                )
                .changeStep(InsurancePortabilityRequestStep.FINISHED)
            val expectedPortabilityRequestModel = expectedPortabilityRequest.toModel()

            coEvery { data.update(capture(slot)) } returns expectedPortabilityRequestModel.success()

            authenticatedAs(token, person) {
                val insurancePortabilityService = testEngine.application.getKoin().get<InsurancePortabilityService>()

                val changeStatusResult = insurancePortabilityService.approveSpecific(
                    portabilityRequest.copy(
                        type = InsurancePortabilityRequestType.SPECIFIC,
                        finishedAt = LocalDateTime.now(),
                    )
                )
                val actual = slot.captured

                Assertions.assertThat(actual).isEqualTo(expectedPortabilityRequestModel)
                assertThat(changeStatusResult).isSuccessWithData(expectedPortabilityRequest)
            }
        }

    @Test
    fun `#approveSpecific should fail if portability is not specific`() =
        runBlocking {
            authenticatedAs(token, person) {
                val insurancePortabilityService = testEngine.application.getKoin().get<InsurancePortabilityService>()

                val changeStatusResult = insurancePortabilityService.approveSpecific(
                    portabilityRequest.copy(type = InsurancePortabilityRequestType.NORMAL),
                )

                assertThat(changeStatusResult).isFailureOfType(PortabilityCannotBeApprovedException::class)
                coVerify(exactly = 0) { data.update(any()) }
            }
        }

    @Test
    fun `#decline should return success when a portability was already declined`() = runBlocking {
        val declined =
            portabilityRequest.decline(reason = InsurancePortabilityRequestDeclineReason.MIN_GRACE_PERIOD_NOT_ACHIEVED)

        authenticatedAs(token, person) {
            val insurancePortabilityService = testEngine.application.getKoin().get<InsurancePortabilityService>()
            val changeStatusResult = insurancePortabilityService.decline(
                declined,
                InsurancePortabilityRequestDeclineReason.MIN_GRACE_PERIOD_NOT_ACHIEVED,
                ""
            )
            assertThat(changeStatusResult).isSuccessWithData(declined)

            coVerify(exactly = 0) { onboardingService.update(any()) }
        }
    }

    @Test
    fun `#decline should return failure when a person didn't choose a product`() = runBlocking {
        val onboarding = TestModelFactory.buildPersonOnboarding(person.id).copy(
            currentPhase = OnboardingPhase.SHOPPING
        )

        coEvery { onboardingService.findByPerson(person.id) } returns onboarding.success()

        authenticatedAs(token, person) {
            val insurancePortabilityService = testEngine.application.getKoin().get<InsurancePortabilityService>()
            val changeStatusResult = insurancePortabilityService.decline(
                portabilityRequest,
                InsurancePortabilityRequestDeclineReason.MIN_GRACE_PERIOD_NOT_ACHIEVED,
                ""
            )
            assertThat(changeStatusResult).isFailureOfType(PortabilityCannotBeChangedException::class)

            coVerify(exactly = 0) { onboardingService.changePhaseTo(person.id, OnboardingPhase.REGISTRATION) }
        }
    }

    @Test
    fun `#decline should return failure when a person already signed contract`() = runBlocking {
        val onboarding = TestModelFactory.buildPersonOnboarding(person.id).copy(
            currentPhase = OnboardingPhase.REGISTRATION
        )

        coEvery { onboardingService.findByPerson(person.id) } returns onboarding
            .copy(currentPhase = OnboardingPhase.PAYMENT)
            .success()

        authenticatedAs(token, person) {
            val insurancePortabilityService = testEngine.application.getKoin().get<InsurancePortabilityService>()
            val changeStatusResult = insurancePortabilityService.decline(
                portabilityRequest,
                InsurancePortabilityRequestDeclineReason.MIN_GRACE_PERIOD_NOT_ACHIEVED,
                ""
            )
            assertThat(changeStatusResult).isFailureOfType(PortabilityCannotBeChangedException::class)

            coVerify(exactly = 0) { onboardingService.changePhaseTo(person.id, OnboardingPhase.REGISTRATION) }
        }
    }

    @Test
    fun `#decline should return update a portability and move person to REGISTRATION phase when a portability was pending`() =
        runBlocking {
            val slot = slot<InsurancePortabilityRequestModel>()

            val onboarding = TestModelFactory.buildPersonOnboarding(person.id).copy(
                currentPhase = OnboardingPhase.REGISTRATION
            )

            val expectedPortabilityRequest =
                portabilityRequest.decline(reason = InsurancePortabilityRequestDeclineReason.MIN_GRACE_PERIOD_NOT_ACHIEVED)
            val expectedPortabilityRequestModel = expectedPortabilityRequest.toModel()

            coEvery { data.update(capture(slot)) } returns expectedPortabilityRequestModel.success()

            coEvery { onboardingService.findByPerson(person.id) } returns onboarding
                .copy(currentPhase = OnboardingPhase.HEALTH_DECLARATION_APPOINTMENT)
                .success()

            coEvery {
                onboardingService.changePhaseTo(
                    person.id,
                    OnboardingPhase.REGISTRATION
                )
            } returns onboarding.success()

            authenticatedAs(token, person) {
                val insurancePortabilityService = testEngine.application.getKoin().get<InsurancePortabilityService>()
                val changeStatusResult = insurancePortabilityService.decline(
                    portabilityRequest,
                    InsurancePortabilityRequestDeclineReason.MIN_GRACE_PERIOD_NOT_ACHIEVED,
                    ""
                )
                val actual = slot.captured

                Assertions.assertThat(actual).isEqualTo(expectedPortabilityRequestModel)
                assertThat(changeStatusResult).isSuccessWithData(expectedPortabilityRequest)

                coVerify(exactly = 1) { onboardingService.changePhaseTo(person.id, OnboardingPhase.REGISTRATION) }
            }
        }

    @Test
    fun `#declineV3 should update portability and move person to REGISTRATION phase when a portability was pending`() =
        runBlocking {
            val slot = slot<InsurancePortabilityRequestModel>()

            val expectedPortabilityRequest = portabilityRequest
                .decline(reasons = listOf(InsurancePortabilityRequestDeclineReason.MIN_GRACE_PERIOD_NOT_ACHIEVED))
                .changeStep(InsurancePortabilityRequestStep.FINISHED)
            val expectedPortabilityRequestModel = expectedPortabilityRequest.toModel()

            coEvery { data.update(capture(slot)) } returns expectedPortabilityRequestModel.success()

            authenticatedAs(token, person) {
                val insurancePortabilityService = testEngine.application.getKoin().get<InsurancePortabilityService>()
                val changeStatusResult = insurancePortabilityService.declineV3(
                    portabilityRequest,
                    listOf(InsurancePortabilityRequestDeclineReason.MIN_GRACE_PERIOD_NOT_ACHIEVED),
                )
                val actual = slot.captured

                Assertions.assertThat(actual).usingRecursiveComparison().ignoringFields("id", "createdAt", "updatedAt")
                    .isEqualTo(expectedPortabilityRequest)
                assertThat(changeStatusResult).isSuccessWithDataIgnoringGivenFields(
                    expectedPortabilityRequest,
                    "id",
                    "createdAt",
                    "updatedAt"
                )
            }
        }

    @Test
    fun `#declineV3 should return error if it has already been declined`() =
        runBlocking {
            authenticatedAs(token, person) {
                val insurancePortabilityService = testEngine.application.getKoin().get<InsurancePortabilityService>()
                val changeStatusResult = insurancePortabilityService.declineV3(
                    portabilityRequest.copy(status = InsurancePortabilityRequestStatus.DECLINED),
                    listOf(InsurancePortabilityRequestDeclineReason.MIN_GRACE_PERIOD_NOT_ACHIEVED),
                )

                assertThat(changeStatusResult).isFailureOfType(PortabilityCannotBeDeclinedException::class)
                coVerify(exactly = 0) { data.update(any()) }
                coVerify(exactly = 0) { onboardingService.changePhaseTo(any(), any()) }
            }
        }

    @Test
    fun `#declineV3 should return error if it has been approved`() =
        runBlocking {
            authenticatedAs(token, person) {
                val insurancePortabilityService = testEngine.application.getKoin().get<InsurancePortabilityService>()
                val changeStatusResult = insurancePortabilityService.declineV3(
                    portabilityRequest.copy(status = InsurancePortabilityRequestStatus.APPROVED),
                    listOf(InsurancePortabilityRequestDeclineReason.MIN_GRACE_PERIOD_NOT_ACHIEVED),
                )

                assertThat(changeStatusResult).isFailureOfType(PortabilityCannotBeDeclinedException::class)

                coVerify(exactly = 0) { data.update(any()) }
                coVerify(exactly = 0) { onboardingService.changePhaseTo(any(), any()) }
            }
        }

    @Test
    fun `#submitToReview should return success when a portability was already PENDING`() = runBlocking {
        val approvedRequest = portabilityRequest.submitToReview()

        authenticatedAs(token, person) {
            val insurancePortabilityService = testEngine.application.getKoin().get<InsurancePortabilityService>()
            val changeStatusResult = insurancePortabilityService.submitToReview(approvedRequest, "")
            assertThat(changeStatusResult).isSuccessWithData(approvedRequest)

            coVerify(exactly = 0) { data.update(any()) }
        }
    }

    @Test
    fun `#submitToReview should update a portability and move person to PORTABILITY_REVIEW phase when a portability wasn't already pending`() =
        runBlocking {
            val slot = slot<InsurancePortabilityRequestModel>()

            val onboarding =
                TestModelFactory.buildPersonOnboarding(person.id, currentPhase = OnboardingPhase.PORTABILITY_REVIEW)
            val expectedPortabilityRequest = portabilityRequest.submitToReview()
            val expectedPortabilityRequestModel = expectedPortabilityRequest.toModel()

            coEvery { data.update(capture(slot)) } returns expectedPortabilityRequestModel.success()
            coEvery {
                onboardingService.changePhaseTo(
                    person.id,
                    OnboardingPhase.PORTABILITY_REVIEW
                )
            } returns onboarding.success()

            authenticatedAs(token, person) {
                val insurancePortabilityService = testEngine.application.getKoin().get<InsurancePortabilityService>()
                val changeStatusResult = insurancePortabilityService.submitToReview(portabilityRequest)
                val actual = slot.captured

                Assertions.assertThat(actual).usingRecursiveComparison().ignoringFields("id", "createdAt", "updatedAt")
                    .isEqualTo(expectedPortabilityRequest)
                assertThat(changeStatusResult).isSuccessWithDataIgnoringGivenFields(
                    expectedPortabilityRequest,
                    "id",
                    "createdAt",
                    "updatedAt"
                )

                coVerify(exactly = 1) { onboardingService.changePhaseTo(person.id, OnboardingPhase.PORTABILITY_REVIEW) }
            }
        }

    @Test
    fun `#answer with not valid answer, should update PortabilityRequest and change its phase to REGISTRATION`() =
        runBlocking {
            val portabilityRequest = TestModelFactory.buildInsurancePortabilityRequest(personId = person.id)
            val portabilityRequestModel = portabilityRequest.toModel()
            coEvery { data.findByPerson(person.id) } returns portabilityRequestModel.success()
            coEvery { onboardingService.findByPerson(person.id) } returns onboarding.copy(currentPhase = OnboardingPhase.PORTABILITY)
                .success()

            val declineReason = InsurancePortabilityRequestDeclineReason.MIN_GRACE_PERIOD_NOT_ACHIEVED
            val answer =
                TestModelFactory.buildInsurancePortabilityRequestAnswer(questionType = InsurancePortabilityRequestQuestionType.MIN_GRACE_PERIOD)
            val expectedPortabilityRequest = portabilityRequest.answer(answer).decline(declineReason)
            val expectedPortabilityRequestModel = expectedPortabilityRequest.toModel()

            mockkObject(InsurancePortabilityQuestionFactory) {
                val question: InsurancePortabilityQuestion = mockk()

                coEvery { InsurancePortabilityQuestionFactory.createQuestion(answer.questionType) } returns question
                coEvery { question.validate(answer.answer) } returns InsurancePortabilityValidation(
                    valid = false,
                    declineReason = declineReason
                )

                val slot = slot<InsurancePortabilityRequestModel>()
                coEvery { data.update(capture(slot)) } returns expectedPortabilityRequestModel.success()

                val onboarding = TestModelFactory.buildPersonOnboarding(
                    personId = person.id,
                    currentPhase = OnboardingPhase.REGISTRATION
                )
                coEvery {
                    onboardingService.changePhaseTo(
                        person.id,
                        OnboardingPhase.REGISTRATION
                    )
                } returns onboarding.success()

                authenticatedAs(token, person) {
                    val insurancePortabilityService =
                        testEngine.application.getKoin().get<InsurancePortabilityService>()
                    val result = insurancePortabilityService.answer(person.id, answer)
                    val actual = slot.captured

                    Assertions.assertThat(actual).isEqualTo(expectedPortabilityRequestModel)
                    assertThat(result).isSuccessWithData(expectedPortabilityRequest)

                    coVerify(exactly = 1) { data.update(expectedPortabilityRequestModel) }
                    coVerify(exactly = 1) { onboardingService.changePhaseTo(person.id, OnboardingPhase.REGISTRATION) }
                }
            }
        }

    @Test
    fun `#answer with valid answer and with no next question, should update PortabilityRequest and change its phase to PORTABILITY_REVIEW`() =
        runBlocking {
            val portabilityRequest = TestModelFactory.buildInsurancePortabilityRequest(personId = person.id, pendingAt = LocalDateTime.now())
            val portabilityRequestModel = portabilityRequest.toModel()
            coEvery { data.findByPerson(person.id) } returns portabilityRequestModel.success()

            val answer =
                TestModelFactory.buildInsurancePortabilityRequestAnswer(questionType = InsurancePortabilityRequestQuestionType.CURRENT_HEALTH_INSURANCE_HOSPITALS)
            val expectedPortabilityRequest = portabilityRequest.answer(answer).submitToReview()
            val expectedPortabilityRequestModel = expectedPortabilityRequest.toModel()

            mockkObject(InsurancePortabilityQuestionFactory) {
                val question: InsurancePortabilityQuestion = mockk()

                coEvery { InsurancePortabilityQuestionFactory.createQuestion(answer.questionType) } returns question
                coEvery { question.validate(answer.answer) } returns InsurancePortabilityValidation(valid = true)

                val slot = slot<InsurancePortabilityRequestModel>()
                coEvery { data.update(capture(slot)) } returns expectedPortabilityRequestModel.success()

                val onboarding = TestModelFactory.buildPersonOnboarding(
                    personId = person.id,
                    currentPhase = OnboardingPhase.PORTABILITY_REVIEW
                )
                coEvery {
                    onboardingService.changePhaseTo(
                        person.id,
                        OnboardingPhase.PORTABILITY_REVIEW
                    )
                } returns onboarding.success()

                authenticatedAs(token, person) {
                    val insurancePortabilityService =
                        testEngine.application.getKoin().get<InsurancePortabilityService>()
                    val result = insurancePortabilityService.answer(person.id, answer)
                    val actual = slot.captured

                    Assertions.assertThat(actual).usingRecursiveComparison()
                        .ignoringFields("id", "createdAt", "updatedAt").isEqualTo(expectedPortabilityRequest)
                    assertThat(result).isSuccessWithDataIgnoringGivenFields(
                        expectedPortabilityRequest,
                        "id",
                        "createdAt",
                        "updatedAt"
                    )

                    coVerify(exactly = 1) { data.update(any()) }
                    coVerify(exactly = 1) {
                        onboardingService.changePhaseTo(
                            person.id,
                            OnboardingPhase.PORTABILITY_REVIEW
                        )
                    }
                }
            }
        }

    @Test
    fun `#answer with valid answer and with next question, should update PortabilityRequest and change its phase to PORTABILITY`() =
        runBlocking {
            val portabilityRequest = TestModelFactory.buildInsurancePortabilityRequest(personId = person.id)
            val portabilityRequestModel = portabilityRequest.toModel()
            coEvery { data.findByPerson(person.id) } returns portabilityRequestModel.success()

            val answer =
                TestModelFactory.buildInsurancePortabilityRequestAnswer(questionType = InsurancePortabilityRequestQuestionType.MIN_GRACE_PERIOD)
            val expectedPortabilityRequest = portabilityRequest.answer(answer)
            val expectedPortabilityRequestModel = expectedPortabilityRequest.toModel()

            mockkObject(InsurancePortabilityQuestionFactory) {
                val question: InsurancePortabilityQuestion = mockk()

                coEvery { InsurancePortabilityQuestionFactory.createQuestion(answer.questionType) } returns question
                coEvery { question.validate(answer.answer) } returns InsurancePortabilityValidation(valid = true)

                val slot = slot<InsurancePortabilityRequestModel>()
                coEvery { data.update(capture(slot)) } returns expectedPortabilityRequestModel.success()

                val onboarding = TestModelFactory.buildPersonOnboarding(
                    personId = person.id,
                    currentPhase = OnboardingPhase.PORTABILITY_REVIEW
                )
                coEvery {
                    onboardingService.changePhaseTo(
                        person.id,
                        OnboardingPhase.PORTABILITY
                    )
                } returns onboarding.success()

                authenticatedAs(token, person) {
                    val insurancePortabilityService =
                        testEngine.application.getKoin().get<InsurancePortabilityService>()
                    val result = insurancePortabilityService.answer(person.id, answer)
                    val actual = slot.captured

                    Assertions.assertThat(actual).isEqualTo(expectedPortabilityRequestModel)
                    assertThat(result).isSuccessWithData(expectedPortabilityRequest)

                    coVerify(exactly = 1) { data.update(expectedPortabilityRequestModel) }
                    coVerify(exactly = 1) { onboardingService.changePhaseTo(person.id, OnboardingPhase.PORTABILITY) }
                }
            }
        }

    @Test
    fun `#answer for an already DECLINED PortabilityRequest, should update it IN_PROGRESS status and change its phase to PORTABILITY`() =
        runBlocking {
            val portabilityRequest = TestModelFactory.buildInsurancePortabilityRequest(
                personId = person.id,
                status = InsurancePortabilityRequestStatus.DECLINED
            )
            val portabilityRequestModel = portabilityRequest.toModel()
            coEvery { data.findByPerson(person.id) } returns portabilityRequestModel.success()

            val answer =
                TestModelFactory.buildInsurancePortabilityRequestAnswer(questionType = InsurancePortabilityRequestQuestionType.MIN_GRACE_PERIOD)
            val expectedPortabilityRequest = portabilityRequest.answer(answer)
            val expectedPortabilityRequestModel = expectedPortabilityRequest.toModel()

            mockkObject(InsurancePortabilityQuestionFactory) {
                val question: InsurancePortabilityQuestion = mockk()

                coEvery { InsurancePortabilityQuestionFactory.createQuestion(answer.questionType) } returns question
                coEvery { question.validate(answer.answer) } returns InsurancePortabilityValidation(valid = true)

                val slot = slot<InsurancePortabilityRequestModel>()
                coEvery { data.update(capture(slot)) } returns expectedPortabilityRequestModel.success()

                val onboarding = TestModelFactory.buildPersonOnboarding(
                    personId = person.id,
                    currentPhase = OnboardingPhase.PORTABILITY_REVIEW
                )
                coEvery {
                    onboardingService.changePhaseTo(
                        person.id,
                        OnboardingPhase.PORTABILITY
                    )
                } returns onboarding.success()

                authenticatedAs(token, person) {
                    val insurancePortabilityService =
                        testEngine.application.getKoin().get<InsurancePortabilityService>()
                    val result = insurancePortabilityService.answer(person.id, answer)
                    val actual = slot.captured

                    Assertions.assertThat(actual).isEqualTo(expectedPortabilityRequestModel)
                    assertThat(result).isSuccessWithData(expectedPortabilityRequest)

                    coVerify(exactly = 1) { data.update(expectedPortabilityRequestModel) }
                    coVerify(exactly = 1) { onboardingService.changePhaseTo(person.id, OnboardingPhase.PORTABILITY) }
                }
            }
        }

    @Test
    fun `#answerV2 should deny portability if min grace period wasn't reached`() = runBlocking {
        val portabilityRequest = TestModelFactory.buildInsurancePortabilityRequest(personId = person.id)
        val portabilityRequestModel = portabilityRequest.toModel()
        val declineReason = InsurancePortabilityRequestDeclineReason.MIN_GRACE_PERIOD_NOT_ACHIEVED
        val answer = TestModelFactory.buildInsurancePortabilityRequestAnswerV2(
            questionType = InsurancePortabilityRequestQuestionTypeV2.MIN_GRACE_PERIOD,
            answer = InsurancePortabilityRequestMinGracePeriodAnswer.LESS_THAN_ONE_YEAR.name
        )
        val expectedPortabilityRequest =
            portabilityRequest
                .answer(answer)
                .decline(declineReason)
        val expectedPortabilityRequestModel = expectedPortabilityRequest.toModel()
        val onboarding =
            TestModelFactory.buildPersonOnboarding(personId = person.id, currentPhase = OnboardingPhase.REGISTRATION)
        val slot = slot<InsurancePortabilityRequestModel>()

        coEvery { data.findByPerson(person.id) } returns portabilityRequestModel.success()
        coEvery { onboardingService.findByPerson(person.id) } returns onboarding.copy(currentPhase = OnboardingPhase.PORTABILITY)
            .success()
        coEvery { data.update(capture(slot)) } returns expectedPortabilityRequestModel.success()
        coEvery {
            onboardingService.changePhaseTo(
                person.id,
                OnboardingPhase.REGISTRATION
            )
        } returns onboarding.success()

        authenticatedAs(token, person) {
            val insurancePortabilityService = testEngine.application.getKoin().get<InsurancePortabilityService>()
            insurancePortabilityService.answerV2(person.id, answer)
            val savedPortabilityRequest = slot.captured

            Assertions.assertThat(savedPortabilityRequest).isEqualTo(expectedPortabilityRequestModel)

            coVerify(exactly = 1) { data.update(expectedPortabilityRequestModel) }
            coVerify(exactly = 1) { onboardingService.changePhaseTo(person.id, OnboardingPhase.REGISTRATION) }
        }
    }

    @Test
    fun `#answerV2 with valid answer and with no next question, should update PortabilityRequest and change its phase to PORTABILITY_REVIEW`() =
        runBlocking {
            val portabilityRequest = TestModelFactory.buildInsurancePortabilityRequest(personId = person.id)
            val portabilityRequestModel = portabilityRequest.toModel()
            coEvery { data.findByPerson(person.id) } returns portabilityRequestModel.success()

            val answer = TestModelFactory.buildInsurancePortabilityRequestAnswerV2(
                questionType = InsurancePortabilityRequestQuestionTypeV2.ACKNOWLEDGE_CHECK,
                answer = "true"
            )
            val expectedPortabilityRequest =
                portabilityRequest
                    .answer(answer)
                    .submitToReview()
            val expectedPortabilityRequestModel = expectedPortabilityRequest.toModel()

            val slot = slot<InsurancePortabilityRequestModel>()
            coEvery { data.update(capture(slot)) } returns expectedPortabilityRequestModel.success()

            val onboarding = TestModelFactory.buildPersonOnboarding(
                personId = person.id,
                currentPhase = OnboardingPhase.PORTABILITY_REVIEW
            )
            coEvery {
                onboardingService.changePhaseTo(
                    person.id,
                    OnboardingPhase.PORTABILITY_REVIEW
                )
            } returns onboarding.success()

            authenticatedAs(token, person) {
                val insurancePortabilityService = testEngine.application.getKoin().get<InsurancePortabilityService>()
                insurancePortabilityService.answerV2(person.id, answer)
                val savedPortabilityRequest = slot.captured

                Assertions.assertThat(savedPortabilityRequest).isEqualTo(expectedPortabilityRequestModel)

                coVerify(exactly = 1) { data.update(any()) }
                coVerify(exactly = 1) { onboardingService.changePhaseTo(person.id, OnboardingPhase.PORTABILITY_REVIEW) }
            }
        }

    @Test
    fun `#answerV2 with valid answer and with next question, should update PortabilityRequest and change its phase to PORTABILITY`() =
        runBlocking {
            val portabilityRequest = TestModelFactory.buildInsurancePortabilityRequest(personId = person.id)
            val portabilityRequestModel = portabilityRequest.toModel()
            val slot = slot<InsurancePortabilityRequestModel>()
            val answer = TestModelFactory.buildInsurancePortabilityRequestAnswerV2(
                questionType = InsurancePortabilityRequestQuestionTypeV2.MIN_GRACE_PERIOD,
                answer = InsurancePortabilityRequestMinGracePeriodAnswer.MORE_THAN_TWO_YEARS.name
            )
            val expectedPortabilityRequest = portabilityRequest.answer(answer)
            val expectedPortabilityRequestModel = expectedPortabilityRequest.toModel()
            val onboarding = TestModelFactory.buildPersonOnboarding(
                personId = person.id,
                currentPhase = OnboardingPhase.PORTABILITY_REVIEW
            )


            coEvery { data.update(capture(slot)) } returns expectedPortabilityRequestModel.success()
            coEvery { data.findByPerson(person.id) } returns portabilityRequestModel.success()
            coEvery {
                onboardingService.changePhaseTo(
                    person.id,
                    OnboardingPhase.PORTABILITY
                )
            } returns onboarding.success()

            authenticatedAs(token, person) {
                val insurancePortabilityService = testEngine.application.getKoin().get<InsurancePortabilityService>()
                insurancePortabilityService.answerV2(person.id, answer)
                val savedPortabilityRequest = slot.captured

                Assertions.assertThat(savedPortabilityRequest).isEqualTo(expectedPortabilityRequestModel)

                coVerify(exactly = 1) { data.update(expectedPortabilityRequestModel) }
                coVerify(exactly = 1) { onboardingService.changePhaseTo(person.id, OnboardingPhase.PORTABILITY) }
            }
        }

    @Test
    fun `#answerV2 for an already DECLINED PortabilityRequest, should update it IN_PROGRESS status and change its phase to PORTABILITY`() =
        runBlocking {
            val portabilityRequest = TestModelFactory.buildInsurancePortabilityRequest(
                personId = person.id,
                status = InsurancePortabilityRequestStatus.DECLINED
            )
            val portabilityRequestModel = portabilityRequest.toModel()
            val answer = TestModelFactory.buildInsurancePortabilityRequestAnswerV2(
                questionType = InsurancePortabilityRequestQuestionTypeV2.MIN_GRACE_PERIOD,
                answer = InsurancePortabilityRequestMinGracePeriodAnswer.BETWEEN_ONE_AND_TWO_YEARS.name
            )
            val expectedPortabilityRequest = portabilityRequest.answer(answer)
            val expectedPortabilityRequestModel = expectedPortabilityRequest.toModel()
            val slot = slot<InsurancePortabilityRequestModel>()
            val onboarding = TestModelFactory.buildPersonOnboarding(
                personId = person.id,
                currentPhase = OnboardingPhase.PORTABILITY_REVIEW
            )

            coEvery { data.update(capture(slot)) } returns expectedPortabilityRequestModel.success()
            coEvery { data.findByPerson(person.id) } returns portabilityRequestModel.success()
            coEvery {
                onboardingService.changePhaseTo(
                    person.id,
                    OnboardingPhase.PORTABILITY
                )
            } returns onboarding.success()


            authenticatedAs(token, person) {
                val insurancePortabilityService = testEngine.application.getKoin().get<InsurancePortabilityService>()
                insurancePortabilityService.answerV2(person.id, answer)
                val savedPortabilityRequest = slot.captured

                Assertions.assertThat(savedPortabilityRequest).isEqualTo(expectedPortabilityRequestModel)

                coVerify(exactly = 1) { data.update(expectedPortabilityRequestModel) }
                coVerify(exactly = 1) { onboardingService.changePhaseTo(person.id, OnboardingPhase.PORTABILITY) }
            }
        }

    @Test
    fun `#skipPortability should go to REGISTRATION if current phase is PORTABILITY`() = runBlocking {
        val expected = onboarding.copy(currentPhase = OnboardingPhase.REGISTRATION)

        coEvery { onboardingService.changePhaseTo(person.id, OnboardingPhase.REGISTRATION) } returns expected.success()

        authenticatedAs(token, person) {
            val insurancePortabilityService = testEngine.application.getKoin().get<InsurancePortabilityService>()
            val result = insurancePortabilityService.skip(person.id)
            assertThat(result).isSuccessWithData(expected)
        }
    }

    @Test
    fun `#archive should get and update portability as expected`() = runBlocking {
        val portabilityRequest = TestModelFactory.buildInsurancePortabilityRequest(personId = person.id)
        val portabilityRequestModel = portabilityRequest.toModel()
        Assertions.assertThat(portabilityRequest.archived).isEqualTo(false)

        val slot = slot<InsurancePortabilityRequestModel>()

        coEvery { data.update(capture(slot)) } returns portabilityRequestModel.success()
        coEvery { data.findByPerson(person.id) } returns portabilityRequestModel.success()

        authenticatedAs(token, person) {

            val insurancePortabilityService = testEngine.application.getKoin().get<InsurancePortabilityService>()
            insurancePortabilityService.archive(person.id)
            val savedPortabilityRequest = slot.captured

            Assertions.assertThat(savedPortabilityRequest.personId).isEqualTo(portabilityRequest.personId)
            Assertions.assertThat(savedPortabilityRequest.archived).isEqualTo(true)

            coVerify(exactly = 1) { data.update(savedPortabilityRequest) }
        }
    }

    @Test
    fun `#archive should throw an exception when portability doesnt exist`() = runBlocking {
        coEvery { data.findByPerson(person.id) } returns NotFoundException("not_found").failure()

        authenticatedAs(token, person) {

            val insurancePortabilityService = testEngine.application.getKoin().get<InsurancePortabilityService>()

            assertThat(insurancePortabilityService.archive(person.id)).isFailureOfType(
                InsurancePortabilityNotRequestedException::class
            )
            coVerify(exactly = 0) { data.update(any()) }
        }
    }

    @Test
    fun `#archiveAll should get and update all portabilities`() = runBlocking {
        val portabilitiesToFetch = listOf(
            TestModelFactory.buildInsurancePortabilityRequest(personId = person.id),
            TestModelFactory.buildInsurancePortabilityRequest(personId = person.id),
        )
        val portabilitiesToFetchModel = portabilitiesToFetch.map { it.toModel() }

        val portabilitiesArchived = portabilitiesToFetch.subList(0, 2).map {
            it.archive()
        }.then {
            val model = it.toModel()
            coEvery { data.update(model) } returns model.success()
        }

        coEvery { data.findAllByPerson(person.id) } returns portabilitiesToFetchModel.success()

        authenticatedAs(token, person) {
            val insurancePortabilityService = testEngine.application.getKoin().get<InsurancePortabilityService>()
            val response = insurancePortabilityService.archiveAll(person.id).get()

            Assertions.assertThat(response).usingRecursiveComparison().ignoringFields("createdAt", "updatedAt")
                .isEqualTo(portabilitiesArchived)

            coVerify(exactly = 2) { data.update(any()) }
        }
    }

    @Test
    fun `#cancel should throw an exception when portability status is approved`() = runBlocking {
        val insurancePortabilityRequest = TestModelFactory.buildInsurancePortabilityRequest(status = InsurancePortabilityRequestStatus.APPROVED)

        authenticatedAs(token, person) {
            val insurancePortabilityService = testEngine.application.getKoin().get<InsurancePortabilityService>()
            assertThat(insurancePortabilityService.cancel(insurancePortabilityRequest)).isFailureOfType(
                PortabilityCannotBeCancelledException::class
            )
            coVerify(exactly = 0) { data.update(any()) }
        }
    }

    @Test
    fun `#cancel should return the same request when it has already been cancelled`() = runBlocking {
        val insurancePortabilityRequest = TestModelFactory.buildInsurancePortabilityRequest(status = InsurancePortabilityRequestStatus.DECLINED)

        authenticatedAs(token, person) {
            val insurancePortabilityService = testEngine.application.getKoin().get<InsurancePortabilityService>()
            val result = insurancePortabilityService.cancel(insurancePortabilityRequest)
            assertThat(result).isSuccessWithData(insurancePortabilityRequest)
            coVerify(exactly = 0) { data.update(any()) }
        }
    }

    @Test
    fun `#cancel should throw an exception when portability status is created`() = runBlocking {
        val insurancePortabilityRequest = TestModelFactory.buildInsurancePortabilityRequest(status = InsurancePortabilityRequestStatus.CREATED)

        authenticatedAs(token, person) {
            val insurancePortabilityService = testEngine.application.getKoin().get<InsurancePortabilityService>()
            assertThat(insurancePortabilityService.cancel(insurancePortabilityRequest)).isFailureOfType(
                PortabilityCannotBeCancelledException::class
            )
            coVerify(exactly = 0) { data.update(any()) }
        }
    }

    @Test
    fun `#cancel should decline and archive portability status is pending`() = runBlocking {
        val insurancePortabilityRequest = TestModelFactory.buildInsurancePortabilityRequest(status = InsurancePortabilityRequestStatus.PENDING)
        val insurancePortabilityRequestModel = insurancePortabilityRequest.toModel()

        coEvery { data.update(match { it.status == InsurancePortabilityRequestStatus.DECLINED && it.archived }) } returns insurancePortabilityRequestModel.success()

        authenticatedAs(token, person) {
            val insurancePortabilityService = testEngine.application.getKoin().get<InsurancePortabilityService>()
            insurancePortabilityService.cancel(insurancePortabilityRequest)
            coVerify(exactly = 1) { data.update(match { it.status == InsurancePortabilityRequestStatus.DECLINED && it.archived }) }
        }
    }

    @Test
    fun `#cancel should decline and archive portability status is in progress`() = runBlocking {
        val insurancePortabilityRequest = TestModelFactory.buildInsurancePortabilityRequest(status = InsurancePortabilityRequestStatus.IN_PROGRESS)
        val insurancePortabilityRequestModel = insurancePortabilityRequest.toModel()

        coEvery { data.update(match { it.status == InsurancePortabilityRequestStatus.DECLINED && it.archived }) } returns insurancePortabilityRequestModel.success()

        authenticatedAs(token, person) {
            val insurancePortabilityService = testEngine.application.getKoin().get<InsurancePortabilityService>()
            insurancePortabilityService.cancel(insurancePortabilityRequest)
            coVerify(exactly = 1) { data.update(match { it.status == InsurancePortabilityRequestStatus.DECLINED && it.archived }) }
        }
    }

    @Test
    fun `#requestDocuments should decline and archive portability status is in progress`() = runBlocking {
        val  missingDocuments = listOf(
            InsurancePortabilityMissingDocumentFile(InsurancePortabilityRequestFileType.LETTER)
        )
        val insurancePortabilityRequest = TestModelFactory.buildInsurancePortabilityRequest(
            status = InsurancePortabilityRequestStatus.IN_PROGRESS,
            missingDocuments = missingDocuments
        )
        val insurancePortabilityRequestModel = insurancePortabilityRequest.toModel()

        coEvery { data.update(match {
            it.status == InsurancePortabilityRequestStatus.DECLINED &&
                    it.step == InsurancePortabilityRequestStep.FINISHED &&
                    it.declinedReasons!! == listOf(InsurancePortabilityRequestDeclineReason.MISSING_DOCUMENTS)
        }) } returns insurancePortabilityRequestModel.success()

        authenticatedAs(token, person) {
            val insurancePortabilityService = testEngine.application.getKoin().get<InsurancePortabilityService>()
            insurancePortabilityService.requestDocuments(insurancePortabilityRequest, missingDocuments)
            coVerify(exactly = 1) { data.update(match { it.status == InsurancePortabilityRequestStatus.DECLINED }) }
        }
    }

    @Test
    fun `#requestDocuments should throw an exception when portability status is declined`() = runBlocking {
        val  missingDocuments = listOf(
            InsurancePortabilityMissingDocumentFile(InsurancePortabilityRequestFileType.LETTER)
        )
        val insurancePortabilityRequest = TestModelFactory.buildInsurancePortabilityRequest(
            status = InsurancePortabilityRequestStatus.DECLINED,
            missingDocuments = missingDocuments
        )

        authenticatedAs(token, person) {
            val insurancePortabilityService = testEngine.application.getKoin().get<InsurancePortabilityService>()
            assertThat(
                insurancePortabilityService.requestDocuments(
                    insurancePortabilityRequest,
                    missingDocuments
                )
            ).isFailureOfType(PortabilityCannotBeCancelledException::class)
            coVerify(exactly = 0) { data.update(any()) }
        }
    }

    @Test
    fun `#requestDocuments should throw an exception when missing documents is empty`() = runBlocking {
        val missingDocuments: List<InsurancePortabilityMissingDocumentFile> = emptyList()
        val insurancePortabilityRequest = TestModelFactory.buildInsurancePortabilityRequest(
            status = InsurancePortabilityRequestStatus.PENDING,
            missingDocuments = missingDocuments
        )

        authenticatedAs(token, person) {
            val insurancePortabilityService = testEngine.application.getKoin().get<InsurancePortabilityService>()
            assertThat(
                insurancePortabilityService.requestDocuments(
                    insurancePortabilityRequest,
                    missingDocuments
                )
            ).isFailureOfType(
                PortabilityMissingDocumentsCannotBeEmptyException::class
            )
            coVerify(exactly = 0) { data.update(any()) }
        }
    }

    @Test
    fun `#updateRequirements should throw an exception when request is declined`() = runBlocking {
        val insurancePortabilityRequest = TestModelFactory.buildInsurancePortabilityRequest(
            status = InsurancePortabilityRequestStatus.DECLINED,
        )

        authenticatedAs(token, person) {
            val insurancePortabilityService = testEngine.application.getKoin().get<InsurancePortabilityService>()
            assertThat(insurancePortabilityService.updateRequirements(insurancePortabilityRequest)).isFailureOfType(
                PortabilityRequirementsCannotBeChangedException::class
            )
            coVerify(exactly = 0) { data.update(any()) }
        }
    }

    @Test
    fun `#updateRequirements should update requirements and suggestion`() = runBlocking {
        val insurancePortabilityRequest = TestModelFactory.buildInsurancePortabilityRequest(
            status = InsurancePortabilityRequestStatus.IN_PROGRESS,
            activePlanRequirement = true,
            minimumTermRequirement = true,
            priceCompatibilityRequirement = true,
            paymentFulfillmentRequirement = true,
            hasCpt = true,
            hasFulfilledCpt = true,
        )
        val insurancePortabilityRequestModel = insurancePortabilityRequest.toModel()

        coEvery {
            data.update(match { it.suggestedAction == InsurancePortabilitySuggestedAction.APPROVE })
        } returns insurancePortabilityRequestModel.success()

        authenticatedAs(token, person) {
            val insurancePortabilityService = testEngine.application.getKoin().get<InsurancePortabilityService>()
            insurancePortabilityService.updateRequirements(insurancePortabilityRequest)
            coVerify(exactly = 1) { data.update(match { it.suggestedAction == InsurancePortabilitySuggestedAction.APPROVE }) }
        }
    }

    @Test
    fun `#moveToUploadStep should throw an exception when portability status is approved`() = runBlocking {
        val insurancePortabilityRequest = TestModelFactory.buildInsurancePortabilityRequest(status = InsurancePortabilityRequestStatus.APPROVED)

        authenticatedAs(token, person) {
            val insurancePortabilityService = testEngine.application.getKoin().get<InsurancePortabilityService>()
            assertThat(insurancePortabilityService.moveToUploadStep(insurancePortabilityRequest)).isFailureOfType(
                PortabilityCannotBeCancelledException::class
            )
            coVerify(exactly = 0) { data.update(any()) }
        }
    }

    @Test
    fun `#moveToUploadStep should throw an exception when portability status is created`() = runBlocking {
        val insurancePortabilityRequest = TestModelFactory.buildInsurancePortabilityRequest(status = InsurancePortabilityRequestStatus.CREATED)

        authenticatedAs(token, person) {
            val insurancePortabilityService = testEngine.application.getKoin().get<InsurancePortabilityService>()
            assertThat(insurancePortabilityService.moveToUploadStep(insurancePortabilityRequest)).isFailureOfType(
                PortabilityCannotBeCancelledException::class
            )
            coVerify(exactly = 0) { data.update(any()) }
        }
    }

    @Test
    fun `#moveToUploadStep should cancel the current request and create a new copy in the DOCUMENT_UPLOAD_LETTER step`() = runBlocking {
        val insurancePortabilityRequest = TestModelFactory.buildInsurancePortabilityRequest(
            status = InsurancePortabilityRequestStatus.PENDING,
            adhesionContract = false
        )
        val insurancePortabilityRequestModel = insurancePortabilityRequest.toModel()
        val onboarding = TestModelFactory.buildPersonOnboarding()
        coEvery { onboardingService.changePhaseTo(insurancePortabilityRequest.personId, OnboardingPhase.PORTABILITY) } returns onboarding.success()
        coEvery { data.update(match { it.status == InsurancePortabilityRequestStatus.DECLINED && it.archived }) } returns insurancePortabilityRequestModel.success()
        coEvery {
            data.add(match {
                it.status == InsurancePortabilityRequestStatus.IN_PROGRESS
                        && !it.archived
                        && it.step == InsurancePortabilityRequestStep.DOCUMENT_UPLOAD_LETTER
                        && it.id != insurancePortabilityRequest.id
            })
        } returns insurancePortabilityRequestModel.success()

        authenticatedAs(token, person) {
            val insurancePortabilityService = testEngine.application.getKoin().get<InsurancePortabilityService>()

            assertThat(
                insurancePortabilityService.moveToUploadStep(insurancePortabilityRequest)
            ).isSuccessWithData(
                insurancePortabilityRequest.copy(
                    declinedReasons = emptyList(),
                    missingDocuments = emptyList(),
                    notes = null
                )
            )
            coVerify(exactly = 1) { data.update(match { it.status == InsurancePortabilityRequestStatus.DECLINED && it.archived }) }
            coVerify(exactly = 1) {
                data.add(match {
                    it.status == InsurancePortabilityRequestStatus.IN_PROGRESS
                            && !it.archived
                            && it.step == InsurancePortabilityRequestStep.DOCUMENT_UPLOAD_LETTER
                            && it.id != insurancePortabilityRequestModel.id
                })
            }
        }
    }

    @Test
    fun `#moveToUploadStep should cancel the current request and create a new copy in the DOCUMENT_UPLOAD_LETTER_PAYMENT_RECEIPT step`() = runBlocking {
        val insurancePortabilityRequest = TestModelFactory.buildInsurancePortabilityRequest(status = InsurancePortabilityRequestStatus.PENDING, adhesionContract = true)
        val insurancePortabilityRequestModel = insurancePortabilityRequest.toModel()
        val onboarding = TestModelFactory.buildPersonOnboarding()
        coEvery { onboardingService.changePhaseTo(insurancePortabilityRequest.personId, OnboardingPhase.PORTABILITY) } returns onboarding.success()

        coEvery { data.update(match { it.status == InsurancePortabilityRequestStatus.DECLINED && it.archived }) } returns insurancePortabilityRequestModel.success()
        coEvery {
            data.add(match {
                it.status == InsurancePortabilityRequestStatus.IN_PROGRESS
                        && !it.archived
                        && it.step == InsurancePortabilityRequestStep.DOCUMENT_UPLOAD_LETTER_PAYMENT_RECEIPT
                        && it.id != insurancePortabilityRequest.id
            })
        } returns insurancePortabilityRequestModel.success()

        authenticatedAs(token, person) {
            val insurancePortabilityService = testEngine.application.getKoin().get<InsurancePortabilityService>()

            assertThat(
                insurancePortabilityService.moveToUploadStep(insurancePortabilityRequest)
            ).isSuccessWithData(
                insurancePortabilityRequest.copy(
                    declinedReasons = emptyList(),
                    missingDocuments = emptyList(),
                    notes = null
                )
            )
            coVerify(exactly = 1) { data.update(match { it.status == InsurancePortabilityRequestStatus.DECLINED && it.archived }) }
            coVerify(exactly = 1) {
                data.add(match {
                    it.status == InsurancePortabilityRequestStatus.IN_PROGRESS
                            && !it.archived
                            && it.step == InsurancePortabilityRequestStep.DOCUMENT_UPLOAD_LETTER_PAYMENT_RECEIPT
                            && it.id != insurancePortabilityRequest.id
                })
            }
        }
    }
}
