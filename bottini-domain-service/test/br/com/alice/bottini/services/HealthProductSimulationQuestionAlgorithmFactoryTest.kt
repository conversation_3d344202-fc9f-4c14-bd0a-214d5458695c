package br.com.alice.bottini.services

import br.com.alice.bottini.services.questions.CompanyCNPJAlgorithm
import br.com.alice.bottini.services.questions.CompanyCityAlgorithm
import br.com.alice.bottini.services.questions.CompanyDataAlgorithm
import br.com.alice.bottini.services.questions.CompanyEmployeesNumberAlgorithm
import br.com.alice.bottini.services.questions.CompanyNameAlgorithm
import br.com.alice.bottini.services.questions.CompanyResponsiblePersonDataAlgorithm
import br.com.alice.bottini.services.questions.ContactOptinQuestionAlgorithm
import br.com.alice.bottini.services.questions.EmailQuestionAlgorithm
import br.com.alice.bottini.services.questions.FindOutAliceAlgorithm
import br.com.alice.bottini.services.questions.MEIAlgorithm
import br.com.alice.bottini.services.questions.MicroCompanyAgesAlgorithm
import br.com.alice.bottini.services.questions.MicroCompanyCityAlgorithm
import br.com.alice.bottini.services.questions.MicroCompanyDataAlgorithm
import br.com.alice.bottini.services.questions.MicroCompanyResponsiblePersonDataAlgorithm
import br.com.alice.bottini.services.questions.NameQuestionAlgorithm
import br.com.alice.bottini.services.questions.PhoneNumberQuestionAlgorithm
import br.com.alice.bottini.services.questions.PostalCodeQuestionAlgorithm
import br.com.alice.data.layer.models.HealthProductSimulationQuestionType
import org.assertj.core.api.Assertions.assertThat
import kotlin.test.Test

class HealthProductSimulationQuestionAlgorithmFactoryTest {

    @Test
    fun `#createQuestion given POSTAL_CODE question type, should return PostalCodeQuestionAlgorithm`() {
        val type = HealthProductSimulationQuestionType.POSTAL_CODE
        val question = HealthProductSimulationQuestionFactory.createQuestion(type)

        assertThat(question).isInstanceOf(PostalCodeQuestionAlgorithm::class.java)
    }

    @Test
    fun `#createQuestion given NAME question type, should return NameQuestionAlgorithm`() {
        val type = HealthProductSimulationQuestionType.NAME
        val question = HealthProductSimulationQuestionFactory.createQuestion(type)

        assertThat(question).isInstanceOf(NameQuestionAlgorithm::class.java)
    }

    @Test
    fun `#createQuestion given PHONE_NUMBER question type, should return PhoneNumberQuestionAlgorithm`() {
        val type = HealthProductSimulationQuestionType.PHONE_NUMBER
        val question = HealthProductSimulationQuestionFactory.createQuestion(type)

        assertThat(question).isInstanceOf(PhoneNumberQuestionAlgorithm::class.java)
    }

    @Test
    fun `#createQuestion given CONTACT_OPTIN question type, should return ContactOptinQuestionAlgorithm`() {
        val type = HealthProductSimulationQuestionType.CONTACT_OPTIN
        val question = HealthProductSimulationQuestionFactory.createQuestion(type)

        assertThat(question).isInstanceOf(ContactOptinQuestionAlgorithm::class.java)
    }

    @Test
    fun `#createQuestion given EMAIL question type, should return EmailQuestionAlgorithm`() {
        val type = HealthProductSimulationQuestionType.EMAIL
        val question = HealthProductSimulationQuestionFactory.createQuestion(type)

        assertThat(question).isInstanceOf(EmailQuestionAlgorithm::class.java)
    }

    @Test
    fun `#createQuestion given MICRO_COMPANY_AGES question type, should return MicroCompanyAgesAlgorithm`() {
        val type = HealthProductSimulationQuestionType.MICRO_COMPANY_AGES
        val question = HealthProductSimulationQuestionFactory.createQuestion(type)

        assertThat(question).isInstanceOf(MicroCompanyAgesAlgorithm::class.java)
    }

    @Test
    fun `#createQuestion given MICRO_COMPANY_CITY question type, should return MicroCompanyCityAlgorithm`() {
        val type = HealthProductSimulationQuestionType.MICRO_COMPANY_CITY
        val question = HealthProductSimulationQuestionFactory.createQuestion(type)

        assertThat(question).isInstanceOf(MicroCompanyCityAlgorithm::class.java)
    }

    @Test
    fun `#createQuestion given MICRO_COMPANY_RESPONSIBLE_PERSON_DATA question type, should return MicroCompanyResponsiblePersonDataAlgorith`() {
        val type = HealthProductSimulationQuestionType.MICRO_COMPANY_RESPONSIBLE_PERSON_DATA
        val question = HealthProductSimulationQuestionFactory.createQuestion(type)

        assertThat(question).isInstanceOf(MicroCompanyResponsiblePersonDataAlgorithm::class.java)
    }

    @Test
    fun `#createQuestion given COMPANY_DATA question type, should return CompanyDataAlgorithm`() {
        val type = HealthProductSimulationQuestionType.COMPANY_DATA
        val question = HealthProductSimulationQuestionFactory.createQuestion(type)

        assertThat(question).isInstanceOf(CompanyDataAlgorithm::class.java)
    }

    @Test
    fun `#createQuestion given COMPANY_RESPONSIBLE_PERSON_DATA question type, should return CompanyResponsiblePersonDataAlgorithm`() {
        val type = HealthProductSimulationQuestionType.COMPANY_RESPONSIBLE_PERSON_DATA
        val question = HealthProductSimulationQuestionFactory.createQuestion(type)

        assertThat(question).isInstanceOf(CompanyResponsiblePersonDataAlgorithm::class.java)
    }

    @Test
    fun `#createQuestion given COMPANY_CITY question type, should return CompanyPostalCodeAlgorithm`() {
        val type = HealthProductSimulationQuestionType.COMPANY_CITY
        val question = HealthProductSimulationQuestionFactory.createQuestion(type)

        assertThat(question).isInstanceOf(CompanyCityAlgorithm::class.java)
    }

    @Test
    fun `#createQuestion given COMPANY_EMPLOYEES_NUMBER question type, should return CompanyEmployeesNumberAlgorithm`() {
        val type = HealthProductSimulationQuestionType.COMPANY_EMPLOYEES_NUMBER
        val question = HealthProductSimulationQuestionFactory.createQuestion(type)

        assertThat(question).isInstanceOf(CompanyEmployeesNumberAlgorithm::class.java)
    }

    @Test
    fun `#createQuestion given COMPANY_NAME question type, should return CompanyNameAlgorithm`() {
        val type = HealthProductSimulationQuestionType.COMPANY_NAME
        val question = HealthProductSimulationQuestionFactory.createQuestion(type)

        assertThat(question).isInstanceOf(CompanyNameAlgorithm::class.java)
    }

    @Test
    fun `#createQuestion given COMPANY_CNPJ question type, should return CompanyCNPJAlgorithm`() {
        val type = HealthProductSimulationQuestionType.COMPANY_CNPJ
        val question = HealthProductSimulationQuestionFactory.createQuestion(type)

        assertThat(question).isInstanceOf(CompanyCNPJAlgorithm::class.java)
    }

    @Test
    fun `#createQuestion given MEI question type, should return MEIAlgorithm`() {
        val type = HealthProductSimulationQuestionType.MEI
        val question = HealthProductSimulationQuestionFactory.createQuestion(type)

        assertThat(question).isInstanceOf(MEIAlgorithm::class.java)
    }

    @Test
    fun `#createQuestion given FIND_OUT_ALICE question type, should return FindOutAlicelgorithm`() {
        val type = HealthProductSimulationQuestionType.FIND_OUT_ALICE
        val question = HealthProductSimulationQuestionFactory.createQuestion(type)

        assertThat(question).isInstanceOf(FindOutAliceAlgorithm::class.java)
    }

    @Test
    fun `#createQuestion given MICRO_COMPANY_DATA question type, should return MicroCompanyDataAlgorithm`() {
        val type = HealthProductSimulationQuestionType.MICRO_COMPANY_DATA
        val question = HealthProductSimulationQuestionFactory.createQuestion(type)

        assertThat(question).isInstanceOf(MicroCompanyDataAlgorithm::class.java)
    }
}
