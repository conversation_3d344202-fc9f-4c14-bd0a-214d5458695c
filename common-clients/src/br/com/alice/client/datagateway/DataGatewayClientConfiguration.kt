package br.com.alice.client.datagateway

import br.com.alice.common.core.RunningMode
import com.typesafe.config.ConfigFactory
import io.ktor.server.config.HoconApplicationConfig

internal object DataGatewayClientConfiguration {
    private val config = HoconApplicationConfig(ConfigFactory.load("data_gateway.conf"))

    fun environment(): RunningMode {
        val environmentAsString = config.property("systemEnv").getString()
        return RunningMode.valueOf(environmentAsString.uppercase())
    }

    fun apiUrl() = config.property("${environment().value.lowercase()}.apiUrl").getString()
    fun timeout() = config.property("${environment().value.lowercase()}.timeout").getString().toLong()
}
