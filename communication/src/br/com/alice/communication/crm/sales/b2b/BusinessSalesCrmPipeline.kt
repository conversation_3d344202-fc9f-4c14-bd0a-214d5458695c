package br.com.alice.communication.crm.sales.b2b

import br.com.alice.common.core.exceptions.BadRequestException
import br.com.alice.communication.crm.hubspot.b2b.client.HubspotDealSearchResponse

interface BusinessSalesCrmPipeline {
    suspend fun createNewContact(contact: BusinessContact): ContactResult
    suspend fun updateContact(contactId: String, contact: BusinessContact): ContactResult
    suspend fun createDeal(deal: BusinessDeal): DealResult
    suspend fun createCompanyDeal(deal: BusinessCompanyDeal): DealResult
    suspend fun updateCompanyDeal(dealId: String, deal: BusinessCompanyDeal): DealResult
    suspend fun updateDeal(dealId: String, deal: BusinessDeal): DealResult
    suspend fun associateContactToDeal(contactId: String, dealId: String): AssociationResult
    suspend fun searchDealByCnpj(cnpj: String): HubspotDealSearchResponse
}

class BusinessSalesCrmPipelineException(
    message: String,
    code: String = "crm_business_sales_pipeline",
    cause: Throwable? = null
): BadRequestException(message, code, cause)
