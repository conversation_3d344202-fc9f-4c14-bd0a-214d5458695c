package br.com.alice.wanda.consumers

import br.com.alice.common.RangeUUID
import br.com.alice.common.data.dsl.matchers.ResultAssert.Companion.assertThat
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.HealthPlanTaskType
import br.com.alice.data.layer.models.Prescription
import br.com.alice.data.layer.models.copy
import br.com.alice.healthplan.events.HealthPlanTaskUpsertedEvent
import br.com.alice.wanda.services.HealthPlanTaskPersonHealthEventService
import com.github.kittinunf.result.success
import io.mockk.called
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.mockk
import kotlinx.coroutines.runBlocking
import java.time.LocalDate
import java.time.LocalDateTime
import kotlin.test.Test

class HealthPlanTaskConsumerTest : ConsumerTest() {
    private val taskPersonHealthEventService: HealthPlanTaskPersonHealthEventService = mockk()
    private val consumer = HealthPlanTaskConsumer(taskPersonHealthEventService)

    private val personHealthEvent = TestModelFactory.buildPersonHealthEvent()

    @Test
    fun `#upsertPersonHealthEventFromHealthPlanTask - ignores testRequest Health plan task`() = runBlocking {
        val task = TestModelFactory.buildHealthPlanTask(type = HealthPlanTaskType.TEST_REQUEST)
        val event = HealthPlanTaskUpsertedEvent(task)

        val result = consumer.upsertPersonHealthEventFromHealthPlanTask(event)
        assertThat(result).isSuccessWithData(true)
    }

    @Test
    fun `#upsertPersonHealthEventFromHealthPlanTask - unknown Health plan task type`() = runBlocking {
        val task = TestModelFactory.buildHealthPlanTask(type = HealthPlanTaskType.EATING)
        val event = HealthPlanTaskUpsertedEvent(task)

        val result = consumer.upsertPersonHealthEventFromHealthPlanTask(event)
        assertThat(result).isSuccessWithData(true)

        coVerify { taskPersonHealthEventService wasNot called}
    }

    @Test
    fun `#upsertPersonHealthEventFromHealthPlanTask - prescription health plan task`() = runBlocking {
        val task = TestModelFactory.buildHealthPlanTask(type = HealthPlanTaskType.PRESCRIPTION)
        val prescription = Prescription(
            packing = 1,
            task = task.copy( releasedAt = LocalDateTime.now()),
            dose = null,
            action = null,
            routeOfAdministration = null,
            medicine = null
        )
        val event = HealthPlanTaskUpsertedEvent(prescription.generalize())

        coEvery {
            taskPersonHealthEventService.upsertPrescriptionPersonHealthEvent(event.payload.task.specialize())
        } returns personHealthEvent.success()

        val result = consumer.upsertPersonHealthEventFromHealthPlanTask(event)
        assertThat(result).isSuccessWithData(personHealthEvent)

        coVerifyOnce {
            taskPersonHealthEventService.upsertPrescriptionPersonHealthEvent(any())
        }
    }

    @Test
    fun `#upsertPersonHealthEventFromHealthPlanTask - surgery prescription health plan task`() = runBlocking {
        val surgeryPrescription = TestModelFactory.buildHealthPlanTask(
            type = HealthPlanTaskType.SURGERY_PRESCRIPTION,
            releasedAt = LocalDateTime.now(),
            content = mapOf(
                "expectedDate" to LocalDate.of(2024,1,31).toString(),
                "reason" to "Muitas pernas",
                "provider" to mapOf("name" to "Hospital da Criança", "id" to RangeUUID.generate().toString()),
                "procedures" to listOf(mapOf("description" to "Corte", "tussCode" to "tussCode12412"))
            )
        )

        val event = HealthPlanTaskUpsertedEvent(surgeryPrescription)

        coEvery {
            taskPersonHealthEventService.upsertSurgeryPrescriptionPersonHealthEvent(event.payload.task.specialize())
        } returns personHealthEvent.success()

        val result = consumer.upsertPersonHealthEventFromHealthPlanTask(event)
        assertThat(result).isSuccessWithData(personHealthEvent)

        coVerifyOnce {
            taskPersonHealthEventService.upsertSurgeryPrescriptionPersonHealthEvent(any())
        }
    }
}
