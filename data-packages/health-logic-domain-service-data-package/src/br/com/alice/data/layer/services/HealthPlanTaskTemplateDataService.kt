package br.com.alice.data.layer.services

import br.com.alice.common.rfc.RemoteService
import br.com.alice.common.rfc.Service
import br.com.alice.common.service.data.client.Adder
import br.com.alice.common.service.data.client.CountByValues
import br.com.alice.common.service.data.client.Counter
import br.com.alice.common.service.data.client.Finder
import br.com.alice.common.service.data.client.Getter
import br.com.alice.common.service.data.client.Updater
import br.com.alice.common.service.data.dsl.Field
import br.com.alice.common.service.data.dsl.LikePredicateUsage
import br.com.alice.common.service.data.dsl.Predicate
import br.com.alice.common.service.data.dsl.Predicate.Companion.JsonSearchPredicateUsage
import br.com.alice.common.service.data.dsl.Query
import br.com.alice.common.service.data.dsl.QueryBuilder
import br.com.alice.data.layer.models.HealthPlanTaskTemplate
import br.com.alice.data.layer.models.HealthPlanTaskType
import br.com.alice.data.layer.services.HealthPlanTaskTemplateDataService.FieldOptions
import br.com.alice.data.layer.services.HealthPlanTaskTemplateDataService.OrderingOptions
import com.github.kittinunf.result.Result
import java.util.UUID

@RemoteService
interface HealthPlanTaskTemplateDataService : Service,
    Finder<FieldOptions, OrderingOptions, HealthPlanTaskTemplate>,
    Counter<FieldOptions, OrderingOptions, HealthPlanTaskTemplate>,
    Adder<HealthPlanTaskTemplate>,
    Getter<HealthPlanTaskTemplate>,
    Updater<HealthPlanTaskTemplate> {

    override val namespace: String
        get() = "ehr"
    override val serviceName: String
        get() = "health_plan_task_template"

    class IdField : Field.UUIDField(HealthPlanTaskTemplate::id) {
        fun eq(value: UUID) = Predicate.eq(this, value)
        fun inList(value: List<UUID>) = Predicate.inList(this, value)
    }

    class TitleField : Field.TextField(HealthPlanTaskTemplate::title) {
        @OptIn(LikePredicateUsage::class)
        fun like(value: String) = Predicate.like(this, value)
    }

    class DescriptionField : Field.JsonbField(HealthPlanTaskTemplate::description) {
        @OptIn(LikePredicateUsage::class)
        fun like(value: String) = Predicate.like(this, value)
    }

    class TypeField : Field.TextField(HealthPlanTaskTemplate::type) {
        fun eq(value: HealthPlanTaskType) = Predicate.eq(this, value)
    }

    class SearchTokens : Field.TextField(HealthPlanTaskTemplate::searchTokens) {
        fun search(value: String) = Predicate.rankedSearch(this, value)
    }

    class ActiveField : Field.BooleanField(HealthPlanTaskTemplate::active) {
        fun inList(value: List<Boolean>) = Predicate.inList(this, value)
    }

    class TestCodeField : Field.TextField(HealthPlanTaskTemplate::content) {
        @OptIn(Predicate.Companion.JsonSearchPredicateUsage::class)
        fun eq(value: String) = Predicate.jsonSearch(this, "{\"code\":\"$value\"}")
    }

    class SpecialtyField : Field.JsonbField(HealthPlanTaskTemplate::content) {
        @OptIn(JsonSearchPredicateUsage::class)
        fun eqById(value: UUID) = Predicate.jsonSearch(this, "{\"specialty\":{\"id\":\"$value\"}}")
    }

    class SubSpecialtyField : Field.JsonbField(HealthPlanTaskTemplate::content) {
        @OptIn(JsonSearchPredicateUsage::class)
        fun eqById(value: UUID) = Predicate.jsonSearch(this, "{\"subSpecialty\":{\"id\":\"$value\"}}")
    }

    class CreatedAtField : Field.DateTimeField(HealthPlanTaskTemplate::createdAt)

    class FieldOptions {
        val id = IdField()
        val title = TitleField()
        val description = DescriptionField()
        val type = TypeField()
        val searchTokens = SearchTokens()
        val active = ActiveField()
        val testCode = TestCodeField()
        val specialty = SpecialtyField()
        val subSpecialty = SubSpecialtyField()
    }

    class OrderingOptions {
        val title = TitleField()
        val active = ActiveField()
        val createdAt = CreatedAtField()
    }

    override fun queryBuilder() = QueryBuilder(
        FieldOptions(),
        OrderingOptions()
    )

    override suspend fun get(id: UUID): Result<HealthPlanTaskTemplate, Throwable>
    override suspend fun add(model: HealthPlanTaskTemplate): Result<HealthPlanTaskTemplate, Throwable>
    override suspend fun update(model: HealthPlanTaskTemplate): Result<HealthPlanTaskTemplate, Throwable>
    override suspend fun findByQuery(query: Query): Result<List<HealthPlanTaskTemplate>, Throwable>
    override suspend fun countByQuery(query: Query): Result<Int, Throwable>
    override suspend fun countGroupedByQuery(query: Query): Result<List<CountByValues>, Throwable>
    override suspend fun existsByQuery(query: Query): Result<Boolean, Throwable>
}
