package br.com.alice.data.layer.services

import br.com.alice.common.rfc.RemoteService
import br.com.alice.common.rfc.Service
import br.com.alice.common.service.data.client.Adder
import br.com.alice.common.service.data.client.CountByValues
import br.com.alice.common.service.data.client.Counter
import br.com.alice.common.service.data.client.Finder
import br.com.alice.common.service.data.client.Getter
import br.com.alice.common.service.data.client.Updater
import br.com.alice.common.service.data.client.UpdaterList
import br.com.alice.common.service.data.dsl.Field
import br.com.alice.common.service.data.dsl.LikePredicateUsage
import br.com.alice.common.service.data.dsl.Predicate
import br.com.alice.common.service.data.dsl.Query
import br.com.alice.common.service.data.dsl.QueryBuilder
import br.com.alice.data.layer.models.AgeRatingType
import br.com.alice.data.layer.models.AppointmentScheduleOptionModel
import br.com.alice.data.layer.models.AppointmentScheduleType
import br.com.alice.data.layer.models.HealthcareModelType
import com.github.kittinunf.result.Result
import java.util.UUID

@RemoteService
interface AppointmentScheduleOptionModelDataService : Service,
    Finder<AppointmentScheduleOptionModelDataService.FieldOptions, AppointmentScheduleOptionModelDataService.OrderingOptions, AppointmentScheduleOptionModel>,
    Counter<AppointmentScheduleOptionModelDataService.FieldOptions, AppointmentScheduleOptionModelDataService.OrderingOptions, AppointmentScheduleOptionModel>,
    Adder<AppointmentScheduleOptionModel>,
    Getter<AppointmentScheduleOptionModel>,
    Updater<AppointmentScheduleOptionModel>,
    UpdaterList<AppointmentScheduleOptionModel> {

    override val namespace: String
        get() = "ehr"
    override val serviceName: String
        get() = "appointment_schedule_option"

    class Title : Field.TextField(AppointmentScheduleOptionModel::title) {
        fun eq(value: String) = Predicate.eq(this, value)
        @OptIn(LikePredicateUsage::class)
        fun like(value: String) = Predicate.like(this, value)
    }

    class Type : Field.TextField(AppointmentScheduleOptionModel::type) {
        fun eq(value: AppointmentScheduleType) = Predicate.eq(this, value)
        fun inList(value: List<AppointmentScheduleType>) = Predicate.inList(this, value)
    }

    class Active : Field.BooleanField(AppointmentScheduleOptionModel::active)

    class HealthCommunitySpecialistId: Field.UUIDField(AppointmentScheduleOptionModel::specialistId) {
        fun inList(value: List<UUID>) = Predicate.inList(this, value)
        fun eq(value: UUID) = Predicate.eq(this, value)
    }

    class StaffId : Field.UUIDField(AppointmentScheduleOptionModel::staffId) {
        fun eq(value: UUID) = Predicate.eq(this, value)
        fun inList(value: List<UUID>) = Predicate.inList(this, value)
    }

    class AppointmentScheduleEventTypeId : Field.UUIDField(AppointmentScheduleOptionModel::appointmentScheduleEventTypeId) {
        fun eq(value: UUID) = Predicate.eq(this, value)
        fun inList(value: List<UUID>) = Predicate.inList(this, value)
    }

    class SpecialtyId: Field.UUIDField(AppointmentScheduleOptionModel::specialtyId) {
        fun eq(value: UUID) = Predicate.eq(this, value)
        fun inList(value: List<UUID>) = Predicate.inList(this, value)
    }

    class SubSpecialtyIds: Field.JsonbField(AppointmentScheduleOptionModel::subSpecialtyIds) {
        @OptIn(Predicate.Companion.ContainsPredicateUsage::class)
        fun contains(value: UUID) = Predicate.contains(this, value)
        @OptIn(Predicate.Companion.ContainsAnyPredicateUsage::class)
        fun containsAny(value: List<UUID>) = Predicate.containsAny(this, value)
    }

    class ShowOnApp: Field.BooleanField(AppointmentScheduleOptionModel::showOnApp)
    class HealthcareModelTypeField : Field.TextField(AppointmentScheduleOptionModel::healthcareModelType) {
        fun eq(value: HealthcareModelType) = Predicate.eq(this, value)
    }

    class AgeRatingField : Field.TextField(AppointmentScheduleOptionModel::ageRating) {
        fun inList(value: List<AgeRatingType>) = Predicate.inList(this, value)
    }

    class CalendarProviderUnitsField : Field.JsonbField(AppointmentScheduleOptionModel::calendarProviderUnits) {
        fun isNotEmpty() = Predicate.isNotEmptyList(this)
    }

    class FieldOptions {
        val title = Title()
        val type = Type()
        val active = Active()
        val staffId = StaffId()
        val healthCommunitySpecialistId = HealthCommunitySpecialistId()
        val appointmentScheduleEventTypeId = AppointmentScheduleEventTypeId()
        val specialtyId = SpecialtyId()
        val subSpecialtyIds = SubSpecialtyIds()
        val showOnApp = ShowOnApp()
        val modelType = HealthcareModelTypeField()
        val calendarProviderUnits = CalendarProviderUnitsField()
        val ageRating = AgeRatingField()
    }

    class CreatedAt: Field.DateTimeField(AppointmentScheduleOptionModel::createdAt)

    class AvailabilityLevel: Field.IntegerField(AppointmentScheduleOptionModel::availabilityLevel)

    class OrderingOptions {
        val title = Title()
        val type = Type()
        val createdAt = CreatedAt()
        val availabilityLevel = AvailabilityLevel()
    }

    override fun queryBuilder() = QueryBuilder(
        FieldOptions(),
        OrderingOptions()
    )

    override suspend fun add(model: AppointmentScheduleOptionModel): Result<AppointmentScheduleOptionModel, Throwable>
    override suspend fun get(id: UUID): Result<AppointmentScheduleOptionModel, Throwable>
    override suspend fun findByQuery(query: Query): Result<List<AppointmentScheduleOptionModel>, Throwable>
    override suspend fun countByQuery(query: Query): Result<Int, Throwable>
    override suspend fun countGroupedByQuery(query: Query): Result<List<CountByValues>, Throwable>
    override suspend fun existsByQuery(query: Query): Result<Boolean, Throwable>
    override suspend fun update(model: AppointmentScheduleOptionModel): Result<AppointmentScheduleOptionModel, Throwable>
    override suspend fun updateList(
        models: List<AppointmentScheduleOptionModel>,
        returnOnFailure: Boolean
    ): Result<List<AppointmentScheduleOptionModel>, Throwable>
}
