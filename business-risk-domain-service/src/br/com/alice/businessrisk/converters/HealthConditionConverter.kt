package br.com.alice.businessrisk.converters

import br.com.alice.common.core.PersonId
import br.com.alice.data.layer.models.AppointmentContractualRisk
import br.com.alice.data.layer.models.HealthCondition
import br.com.alice.data.layer.models.PersonHealthConditionContractualRisk
import java.util.UUID

object HealthConditionConverter {

    fun HealthCondition.toHealthConditionContractualRisk(
        personId: PersonId,
        staffId: UUID
    ): PersonHealthConditionContractualRisk {
        return PersonHealthConditionContractualRisk(
            personId = personId,
            healthConditionId = this.id,
            healthConditionDescription = this.name,
            staffId = staffId,
            factor = 0,
            baseRiskRating = this.riskRating ?: 0,
            finalRiskRating = this.riskRating ?: 0,
            reason = "",
            suggestedMonthlyCost = this.suggestedMonthlyCost,
            code = this.code.orEmpty()
        )
    }

    fun AppointmentContractualRisk.toPersonHealthConditionContractualRisk(
        personId: PersonId,
        staffId: UUID,
        healthConditionDescription: String
    ): PersonHealthConditionContractualRisk {
        return PersonHealthConditionContractualRisk(
            personId = personId,
            healthConditionId = this.description.id,
            healthConditionDescription = healthConditionDescription,
            staffId = staffId,
            factor = this.factor,
            baseRiskRating = this.baseRiskRating,
            finalRiskRating = this.finalRiskRating,
            reason = this.reason,
            code = this.description.value
        )
    }
}
