package br.com.alice.api.healthcareops.controllers

import br.com.alice.api.healthcareops.api.RoutesTestHelper
import br.com.alice.api.healthcareops.models.*
import br.com.alice.common.RangeUUID
import br.com.alice.common.convertTo
import br.com.alice.common.core.exceptions.NotFoundException
import br.com.alice.common.data.dsl.matchers.ResponseAssert.Companion.assertThat
import br.com.alice.common.helpers.bodyAsJson
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.BillingAccountableParty
import br.com.alice.data.layer.models.BillingAccountablePartyType
import br.com.alice.moneyin.client.BillingAccountablePartyService
import br.com.alice.person.client.PersonService
import com.github.kittinunf.result.failure
import com.github.kittinunf.result.success
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.mockk
import io.mockk.slot
import org.assertj.core.api.Assertions.assertThat
import kotlin.test.BeforeTest
import kotlin.test.Test

class BillingAccountablePartyControllerTest: RoutesTestHelper() {
    private val billingAccountablePartyService: BillingAccountablePartyService = mockk()
    private val personService: PersonService = mockk()

    @BeforeTest
    override fun setup() {
        super.setup()
        module.single { BillingAccountablePartyController(billingAccountablePartyService, personService) }
    }


    @Test
    fun `#get should return a billing accountable party`() {
        val billingAccountableParty = TestModelFactory.buildBillingAccountableParty()

        coEvery { billingAccountablePartyService.get(billingAccountableParty.id) } returns billingAccountableParty.success()

        authenticatedAs(token, staffToBeAuthenticated) {
            get("/billing_accountable_parties/${billingAccountableParty.id}") { response ->
                val result = response.bodyAsJson<BillingAccountablePartyResponse>()
                val expectedResult = BillingAccountablePartyResponse(
                    id = billingAccountableParty.id,
                    firstName = billingAccountableParty.firstName,
                    lastName = billingAccountableParty.lastName,
                    type = billingAccountableParty.type,
                    nationalId = billingAccountableParty.nationalId,
                    email = billingAccountableParty.email,
                    address = billingAccountableParty.address,
                )

                assertThat(result).isEqualTo(expectedResult)
            }
        }

        coVerify { billingAccountablePartyService.get(any()) }
    }

    @Test
    fun `#add should create a new billing accountable party`() {
        val id = RangeUUID.generate()
        val request = BillingAccountablePartyRequest(
            firstName = "Papa",
            lastName = "Leguas",
            type = BillingAccountablePartyType.NATURAL_PERSON,
            nationalId = "***********",
            email = "<EMAIL>",
            address = TestModelFactory.buildAddress(),
        )

        coEvery { billingAccountablePartyService.add(any()) } returns BillingAccountableParty(
            id = id,
            firstName = request.firstName,
            lastName = request.lastName,
            type = request.type,
            nationalId = request.nationalId,
            email = request.email,
            address = request.address,
        ).success()

        authenticatedAs(token, staffToBeAuthenticated) {
            post("/billing_accountable_parties", request) { response ->
                val result = response.bodyAsJson<BillingAccountablePartyResponse>()
                val expectedResult = BillingAccountablePartyResponse(
                    id = id,
                    firstName = request.firstName,
                    lastName = request.lastName,
                    type = request.type,
                    nationalId = request.nationalId,
                    email = request.email,
                    address = request.address,
                )

                assertThat(result).isEqualTo(expectedResult)
            }
        }

        coVerify { billingAccountablePartyService.add(any()) }
    }

    @Test
    fun `#update should change the values of a billing accountable party to the ones sent on the request`() {
        val billingAccountableParty = TestModelFactory.buildBillingAccountableParty()
        val request = BillingAccountablePartyRequest(
            firstName = "Papa",
            lastName = "Leguas",
            type = BillingAccountablePartyType.NATURAL_PERSON,
            nationalId = "***********",
            email = "<EMAIL>",
            address = TestModelFactory.buildAddress()
        )

        coEvery { billingAccountablePartyService.get(billingAccountableParty.id) } returns billingAccountableParty.success()
        coEvery { billingAccountablePartyService.update(any()) } returns billingAccountableParty.copy(
            firstName = request.firstName,
            lastName = request.lastName,
            type = request.type,
            nationalId = request.nationalId,
            email = request.email,
            address = request.address,
        ).success()

        authenticatedAs(token, staffToBeAuthenticated) {
            put("/billing_accountable_parties/${billingAccountableParty.id}", request) { response ->
                val result = response.bodyAsJson<BillingAccountablePartyResponse>()
                val expectedResult = BillingAccountablePartyResponse(
                    id = billingAccountableParty.id,
                    firstName = request.firstName,
                    lastName = request.lastName,
                    type = request.type,
                    nationalId = request.nationalId,
                    email = request.email,
                    address = request.address,
                )

                assertThat(result).isEqualTo(expectedResult)
            }
        }

        coVerify { billingAccountablePartyService.get(any()) }
        coVerify { billingAccountablePartyService.update(any()) }
    }

    @Test
    fun `#listBillingAccountableParties should list all billing accountable parties when no query is passed`() {
        val billingAccountableParty = TestModelFactory.buildBillingAccountableParty()

        coEvery { billingAccountablePartyService.getByRange(IntRange(0, 9)) } returns listOf(billingAccountableParty).success()
        coEvery { billingAccountablePartyService.count() } returns 1.success()

        val expectedResponse = BillingAccountablePartyPaginatedResponse(
            data = listOf(
                BillingAccountablePartyListResponse(
                    id = billingAccountableParty.id,
                    firstName = billingAccountableParty.firstName,
                    lastName = billingAccountableParty.lastName,
                    type = billingAccountableParty.type,
                    nationalId = billingAccountableParty.nationalId,
                    email = billingAccountableParty.email,
                )
            ),
            totalItems = 1,
            totalPages = 1,
            currentPage = 1,
        )


        authenticatedAs(token, staffToBeAuthenticated) {
            get("/billing_accountable_parties/list?start=0&end=4&itemsPerPage=10&currentPage=1") { response ->
                val result = response.bodyAsJson<BillingAccountablePartyPaginatedResponse>()
                assertThat(result).isEqualTo(expectedResponse)
            }
        }

        coVerifyOnce {
            billingAccountablePartyService.getByRange(any())
            billingAccountablePartyService.count()
        }
    }


    @Test
    fun `#listBillingAccountableParties should list a billing accountable parties when a query is passed`() {

        val query = "maria"
        val billingAccountableParty = TestModelFactory.buildBillingAccountableParty()

        coEvery { billingAccountablePartyService.getByRangeAndQuery(IntRange(0, 9), query) } returns listOf(billingAccountableParty).success()
        coEvery { billingAccountablePartyService.countByQuery(query) } returns 1.success()

        val expectedResponse = BillingAccountablePartyPaginatedResponse(
            data = listOf(
                BillingAccountablePartyListResponse(
                    id = billingAccountableParty.id,
                    firstName = billingAccountableParty.firstName,
                    lastName = billingAccountableParty.lastName,
                    type = billingAccountableParty.type,
                    nationalId = billingAccountableParty.nationalId,
                    email = billingAccountableParty.email,
                )
            ),
            totalItems = 1,
            totalPages = 1,
            currentPage = 1,
        )


        authenticatedAs(token, staffToBeAuthenticated) {
            get("/billing_accountable_parties/list?start=0&end=4&itemsPerPage=10&currentPage=1&query=$query") { response ->
                val result = response.bodyAsJson<BillingAccountablePartyPaginatedResponse>()
                assertThat(result).isEqualTo(expectedResponse)
            }
        }

        coVerifyOnce {
            billingAccountablePartyService.getByRangeAndQuery(any(), any())
            billingAccountablePartyService.countByQuery(any())
        }
    }


    @Test
    fun `#find should look for billing accountable parties using national id filter`() {
        val billingAccountableParty = TestModelFactory.buildBillingAccountableParty()

        coEvery { billingAccountablePartyService.findNationalIdEq("455") } returns billingAccountableParty.success()

        authenticatedAs(token, staffToBeAuthenticated) {
            get("/billing_accountable_parties?filter={\"national_id\": \"455\"}") { response ->
                val result = response.bodyAsJson<List<BillingAccountablePartyResponse>>()
                val expectedResult = BillingAccountablePartyResponse(
                    id = billingAccountableParty.id,
                    firstName = billingAccountableParty.firstName,
                    lastName = billingAccountableParty.lastName,
                    type = billingAccountableParty.type,
                    nationalId = billingAccountableParty.nationalId,
                    email = billingAccountableParty.email,
                    address = billingAccountableParty.address,
                )

                assertThat(result).containsExactly(expectedResult)
            }
        }
    }

    @Test
    fun `#findByPerson should look for billing accountable assignments by person`() {
        val person = TestModelFactory.buildPerson()
        val billingAccountableParty = TestModelFactory.buildBillingAccountableParty(nationalId = person.nationalId)
        val personBillingAccountableParty = TestModelFactory.buildPersonBillingAccountableParty(billingAccountablePartyId = billingAccountableParty.id)

        coEvery { personService.get(person.id) } returns person.success()
        coEvery { billingAccountablePartyService.findAssignmentsByPerson(person.id) } returns listOf(personBillingAccountableParty).success()
        coEvery { billingAccountablePartyService.get(billingAccountableParty.id) } returns billingAccountableParty.success()

        authenticatedAs(token, staffToBeAuthenticated) {
            get("/people/${person.id}/billing_accountable_parties") { response ->
                val result = response.bodyAsJson<PersonBillingAccountablePartiesResponse>()
                val expectedResult = PersonBillingAccountablePartyResponse(
                    billingAccountableParty = billingAccountableParty.convertTo(BillingAccountablePartyResponse::class),
                    startDate = personBillingAccountableParty.startDate,
                    endDate = personBillingAccountableParty.endDate,
                    selfAccountable = true,
                )

                assertThat(result.personBillingAccountableParties).containsExactly(expectedResult)
            }
        }
    }

    @Test
    fun `#assign should create an accountable party containing the person information and assign itself as the accountable`() {
        val person = TestModelFactory.buildPerson()
        val billingAccountableParty = TestModelFactory.buildBillingAccountableParty()
        val personBillingAccountableParty = TestModelFactory.buildPersonBillingAccountableParty()
        val request = BillingAccountablePartyAssignRequest(selfAccountable = true)
        val slot = slot<BillingAccountableParty>()

        coEvery { personService.get(person.id) } returns person.success()
        coEvery { billingAccountablePartyService.findNationalIdEq(person.nationalId) } returns NotFoundException().failure()
        coEvery { billingAccountablePartyService.add(capture(slot)) } returns billingAccountableParty.success()
        coEvery { billingAccountablePartyService.assign(person.id, billingAccountableParty) } returns personBillingAccountableParty.success()
        coEvery { billingAccountablePartyService.get(personBillingAccountableParty.billingAccountablePartyId) } returns billingAccountableParty.success()

        authenticatedAs(token, staffToBeAuthenticated) {
            post("/people/${person.id}/billing_accountable_parties", request) { response ->
                val result = response.bodyAsJson<BillingAccountablePartyResponse>()
                val captured = slot.captured
                val expectedResult = BillingAccountablePartyResponse(
                    id = billingAccountableParty.id,
                    firstName = billingAccountableParty.firstName,
                    lastName = billingAccountableParty.lastName,
                    type = billingAccountableParty.type,
                    nationalId = billingAccountableParty.nationalId,
                    email = billingAccountableParty.email,
                    address = billingAccountableParty.address,
                )
                assertThat(result).isEqualTo(expectedResult)

                val expectedBillingAccountableParty = BillingAccountableParty(
                    firstName = person.firstName,
                    lastName = person.lastName,
                    type = BillingAccountablePartyType.NATURAL_PERSON,
                    nationalId = person.nationalId,
                    email = person.email,
                    address = person.addresses.last(),
                )
                assertThat(captured).usingRecursiveComparison().ignoringFields("id", "createdAt", "updatedAt").isEqualTo(expectedBillingAccountableParty)
            }
        }
    }

    @Test
    fun `#assign should find the accountable party containing the person information and assign itself as the accountable`() {
        val person = TestModelFactory.buildPerson()
        val billingAccountableParty = TestModelFactory.buildBillingAccountableParty()
        val personBillingAccountableParty = TestModelFactory.buildPersonBillingAccountableParty()
        val request = BillingAccountablePartyAssignRequest(selfAccountable = true)

        coEvery { personService.get(person.id) } returns person.success()
        coEvery { billingAccountablePartyService.findNationalIdEq(person.nationalId) } returns billingAccountableParty.success()
        coEvery { billingAccountablePartyService.assign(person.id, billingAccountableParty) } returns personBillingAccountableParty.success()
        coEvery { billingAccountablePartyService.get(personBillingAccountableParty.billingAccountablePartyId) } returns billingAccountableParty.success()

        authenticatedAs(token, staffToBeAuthenticated) {
            post("/people/${person.id}/billing_accountable_parties", request) { response ->
                val result = response.bodyAsJson<BillingAccountablePartyResponse>()
                val expectedResult = BillingAccountablePartyResponse(
                    id = billingAccountableParty.id,
                    firstName = billingAccountableParty.firstName,
                    lastName = billingAccountableParty.lastName,
                    type = billingAccountableParty.type,
                    nationalId = billingAccountableParty.nationalId,
                    email = billingAccountableParty.email,
                    address = billingAccountableParty.address,
                )
                assertThat(result).isEqualTo(expectedResult)

                coVerify(exactly = 0) { billingAccountablePartyService.add(any()) }
            }
        }
    }

    @Test
    fun `#assign should assign an existing billing accountable party`() {
        val person = TestModelFactory.buildPerson()
        val billingAccountableParty = TestModelFactory.buildBillingAccountableParty()
        val personBillingAccountableParty = TestModelFactory.buildPersonBillingAccountableParty()
        val request = BillingAccountablePartyAssignRequest(billingAccountablePartyId = billingAccountableParty.id)

        coEvery { billingAccountablePartyService.get(billingAccountableParty.id) } returns billingAccountableParty.success()
        coEvery { billingAccountablePartyService.get(personBillingAccountableParty.billingAccountablePartyId) } returns billingAccountableParty.success()
        coEvery { billingAccountablePartyService.assign(person.id, billingAccountableParty) } returns personBillingAccountableParty.success()

        authenticatedAs(token, staffToBeAuthenticated) {
            post("/people/${person.id}/billing_accountable_parties", request) { response ->
                val result = response.bodyAsJson<BillingAccountablePartyResponse>()
                val expectedResult = BillingAccountablePartyResponse(
                    id = billingAccountableParty.id,
                    firstName = billingAccountableParty.firstName,
                    lastName = billingAccountableParty.lastName,
                    type = billingAccountableParty.type,
                    nationalId = billingAccountableParty.nationalId,
                    email = billingAccountableParty.email,
                    address = billingAccountableParty.address,
                )
                assertThat(result).isEqualTo(expectedResult)

                coVerify(exactly = 0) { billingAccountablePartyService.add(any()) }
                coVerify(exactly = 0) { billingAccountablePartyService.findNationalIdEq(any()) }
            }
        }
    }

    @Test
    fun `#assign should create a billing accountable party and assign it`() {
        val person = TestModelFactory.buildPerson()
        val billingAccountableParty = TestModelFactory.buildBillingAccountableParty()
        val personBillingAccountableParty = TestModelFactory.buildPersonBillingAccountableParty()
        val request = BillingAccountablePartyAssignRequest(billingAccountableParty = BillingAccountablePartyRequest(
            firstName = billingAccountableParty.firstName,
            lastName = billingAccountableParty.lastName,
            type = billingAccountableParty.type,
            nationalId = billingAccountableParty.nationalId,
            email = billingAccountableParty.email,
            address = billingAccountableParty.address!!,
        ))
        val slot = slot<BillingAccountableParty>()

        coEvery { billingAccountablePartyService.add(capture(slot)) } returns billingAccountableParty.success()
        coEvery { billingAccountablePartyService.assign(person.id, billingAccountableParty) } returns personBillingAccountableParty.success()
        coEvery { billingAccountablePartyService.get(personBillingAccountableParty.billingAccountablePartyId) } returns billingAccountableParty.success()

        authenticatedAs(token, staffToBeAuthenticated) {
            post("/people/${person.id}/billing_accountable_parties", request) { response ->
                val result = response.bodyAsJson<BillingAccountablePartyResponse>()
                val expectedResult = BillingAccountablePartyResponse(
                    id = billingAccountableParty.id,
                    firstName = billingAccountableParty.firstName,
                    lastName = billingAccountableParty.lastName,
                    type = billingAccountableParty.type,
                    nationalId = billingAccountableParty.nationalId,
                    email = billingAccountableParty.email,
                    address = billingAccountableParty.address,
                )
                assertThat(result).isEqualTo(expectedResult)

                val captured = slot.captured
                val expectedBillingAccountableParty = request.billingAccountableParty!!.convertTo(BillingAccountableParty::class)
                assertThat(captured).usingRecursiveComparison().ignoringFields("id", "createdAt", "updatedAt").isEqualTo(expectedBillingAccountableParty)

                coVerify(exactly = 0) { billingAccountablePartyService.findNationalIdEq(any()) }
            }
        }
    }

    @Test
    fun `#assign should return error if any accountable assignment information is provided`() {
        val person = TestModelFactory.buildPerson()
        val request = BillingAccountablePartyAssignRequest()

        authenticatedAs(token, staffToBeAuthenticated) {
            post("/people/${person.id}/billing_accountable_parties", request) { response ->
                assertThat(response).isBadRequest()
            }
        }
    }
}
