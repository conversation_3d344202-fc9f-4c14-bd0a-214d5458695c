package br.com.alice.channel.core.services

import br.com.alice.appointment.client.AppointmentService
import br.com.alice.channel.client.ChannelDemand
import br.com.alice.channel.client.ChannelService
import br.com.alice.channel.core.FirestoreTransactionalTestHelper
import br.com.alice.channel.core.context.FirestoreContextUsage
import br.com.alice.channel.core.services.internal.firestore.ChannelFirestoreService
import br.com.alice.channel.event.NewChannelDemandEvent
import br.com.alice.channel.extensions.toTimestamp
import br.com.alice.channel.models.ChannelDocument
import br.com.alice.channel.models.ChannelStaffInfo
import br.com.alice.common.RangeUUID
import br.com.alice.common.core.PersonId
import br.com.alice.common.core.exceptions.NotFoundException
import br.com.alice.common.data.dsl.matchers.ResultAssert.Companion.assertThat
import br.com.alice.common.featureflag.withFeatureFlag
import br.com.alice.common.helpers.coVerifyNone
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.common.kafka.interfaces.KafkaProducerService
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.CaseRecordDetails
import br.com.alice.data.layer.models.CaseRecordReference
import br.com.alice.data.layer.models.CaseRecordReferenceModel
import br.com.alice.data.layer.models.CaseSeverity
import br.com.alice.data.layer.models.ChannelCategory
import br.com.alice.data.layer.models.ChannelId
import br.com.alice.data.layer.models.ChannelKind
import br.com.alice.data.layer.models.ChannelStatus
import br.com.alice.data.layer.models.ChannelSubCategory
import br.com.alice.data.layer.models.ChannelSubCategoryClassifier
import br.com.alice.data.layer.models.Demand
import br.com.alice.common.Disease
import br.com.alice.data.layer.models.DiseaseDetails
import br.com.alice.data.layer.models.FeatureNamespace
import br.com.alice.healthcondition.client.CaseRecordService
import br.com.alice.staff.client.StaffService
import com.github.kittinunf.result.failure
import com.github.kittinunf.result.success
import com.google.cloud.Timestamp
import com.google.cloud.firestore.CollectionReference
import com.google.cloud.firestore.Query
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.confirmVerified
import io.mockk.every
import io.mockk.mockk
import io.mockk.mockkStatic
import io.mockk.slot
import io.mockk.verify
import kotlinx.coroutines.runBlocking
import java.time.LocalDateTime
import kotlin.test.AfterTest
import kotlin.test.BeforeTest
import kotlin.test.Test

class ChannelDemandServiceImplTest : FirestoreTransactionalTestHelper() {

    private val channelFirestoreService: ChannelFirestoreService = mockk()
    private val appointmentService: AppointmentService = mockk()
    private val staffService: StaffService = mockk()
    private val channelService: ChannelService = mockk()
    private val caseRecordService: CaseRecordService = mockk()
    private val kafkaProducerService: KafkaProducerService = mockk()

    private val channelDemandService = ChannelDemandServiceImpl(
        channelFirestoreService,
        appointmentService,
        staffService,
        channelService,
        caseRecordService,
        kafkaProducerService
    )

    private val channelName = "channelName"
    private val personId = PersonId()
    private val caseId = RangeUUID.generate()
    private val healthConditionId = RangeUUID.generate()
    private val appointment = TestModelFactory.buildAppointment(personId = personId).copy(
        caseRecordDetails = listOf(
            CaseRecordDetails(
                caseId = caseId,
                description = DiseaseDetails(
                    id = healthConditionId.toString(),
                    type = Disease.Type.CID_10,
                    value = "A10",
                    description = "CID A10"
                ),
                severity = CaseSeverity.COMPENSATED,
                follow = null,
                observation = "",
                channel = ChannelId(name = channelName)
            )
        )
    )
    private val staff = TestModelFactory.buildStaff()
    private val caseRecord = TestModelFactory.buildCaseRecord(
        caseId = caseId,
        personId = personId,
        healthConditionId = healthConditionId,
        description = Disease(id = healthConditionId, type = Disease.Type.CID_10, value = "A99"),
        addedByStaffId = staff.id,
        responsibleStaffId = staff.id,
        referencedLinks = listOf(
            CaseRecordReference(
                id = appointment.id.toString(),
                model = CaseRecordReferenceModel.APPOINTMENT
            )
        )
    )
    private val caseRecordWithoutLinks = caseRecord.copy(referencedLinks = emptyList())

    private val demand = Demand(
        caseId = caseRecord.caseId,
        startedAt = caseRecord.startedAt,
        description = caseRecord.description,
        severity = caseRecord.severity,
    )

    private val now = Timestamp.now()

    private val channelDocument = ChannelDocument(
        id = channelId,
        personId = caseRecord.personId.toString(),
        channelPersonId = "",
        name = channelName,
        staff = mutableMapOf(staff.id.toString() to ChannelStaffInfo.from(staff).copy(owner = true, lastSync = now)),
        kind = ChannelKind.CHAT,
        category = ChannelCategory.ASSISTANCE,
        subCategory = ChannelSubCategory.ACUTE,
        createdAt = now,
        updatedAt = now,
        lastSync = now
    )

    private val channelDocumentForAssociation = channelDocument.copy(
        kind = ChannelKind.CHANNEL,
        category = ChannelCategory.ASSISTANCE,
        subCategory = ChannelSubCategory.MULTI,
        subCategoryClassifier = ChannelSubCategoryClassifier.PSYCHOLOGY
    )

    private val channelDemand = ChannelDemand(
        id = channelId,
        name = channelName,
        kind = ChannelKind.CHANNEL,
        category = ChannelCategory.ASSISTANCE,
        subCategory = ChannelSubCategory.MULTI,
        subCategoryClassifier = ChannelSubCategoryClassifier.PSYCHOLOGY
    )

    private val channelDemandWithoutId = channelDemand.copy(id = null)

    @BeforeTest
    fun setup() {
        mockkStatic(Timestamp::class)
        every { Timestamp.now() } returns now
    }

    @AfterTest
    fun confirmMocks() = confirmVerified(
        channelFirestoreService,
        appointmentService,
        staffService,
        channelService,
        caseRecordService,
        kafkaProducerService
    )

    @OptIn(FirestoreContextUsage::class)
    @Test
    fun `#addDemandOnChannelByCase returns channel id and add new demand on channel`() = runBlocking {
        coEvery { channelFirestoreService.getChannel(channelId) } returns channelDocument.success()
        coEvery {
            channelFirestoreService.updateFields(
                channelId, mapOf(
                    ChannelDocument::demands to listOf(demand),
                    ChannelDocument::appointmentIds to listOf(appointment.id)
                )
            )
        } returns channelId.success()

        val result = mockFirestoreContextCommit { channelDemandService.addDemandOnChannelByCase(channelId, caseRecord) }
        assertThat(result).isSuccessWithData(channelId)

        coVerifyOnce { channelFirestoreService.getChannel(any()) }
        coVerifyOnce { channelFirestoreService.updateFields(any(), any()) }
    }

    @OptIn(FirestoreContextUsage::class)
    @Test
    fun `#addDemandOnChannelByCase returns channel id and add new demand on channel and force disease id when it have no id in case record`() =
        runBlocking {
            val caseRecord = caseRecord.copy(
                description = caseRecord.description.copy(id = null)
            )

            coEvery { channelFirestoreService.getChannel(channelId) } returns channelDocument.success()
            coEvery {
                channelFirestoreService.updateFields(
                    channelId, mapOf(
                        ChannelDocument::demands to listOf(demand),
                        ChannelDocument::appointmentIds to listOf(appointment.id)
                    )
                )
            } returns channelId.success()

            val result = mockFirestoreContextCommit { channelDemandService.addDemandOnChannelByCase(channelId, caseRecord) }
            assertThat(result).isSuccessWithData(channelId)

            coVerifyOnce { channelFirestoreService.getChannel(any()) }
            coVerifyOnce { channelFirestoreService.updateFields(any(), any()) }
        }

    @OptIn(FirestoreContextUsage::class)
    @Test
    fun `#addDemandOnChannelByCase returns channel id and add new demand on channel and add staff when is a channel`() = runBlocking {
        val channelDocument = channelDocument.copy(kind = ChannelKind.CHANNEL)

        coEvery { channelFirestoreService.getChannel(channelId) } returns channelDocument.success()
        coEvery {
            channelFirestoreService.updateFields(
                channelId, mapOf(
                    ChannelDocument::demands to listOf(demand),
                    ChannelDocument::appointmentIds to listOf(appointment.id)
                )
            )
        } returns channelId.success()
        coEvery {
            channelService.addStaff(channelId, staff.id.toString(), staff.id.toString(), force = true)
        } returns channelId.success()

        val result = mockFirestoreContextCommit { channelDemandService.addDemandOnChannelByCase(channelId, caseRecord) }
        assertThat(result).isSuccessWithData(channelId)

        coVerifyOnce { channelFirestoreService.getChannel(any()) }
        coVerifyOnce { channelFirestoreService.updateFields(any(), any()) }
        coVerifyOnce { channelService.addStaff(any(), any(), any(), any(), any()) }
    }

    @OptIn(FirestoreContextUsage::class)
    @Test
    fun `#addDemandOnChannelByCase returns channel id and add new demand on channel with other demand`() =
        runBlocking {
            val demand0 = demand.copy(caseId = RangeUUID.generate())
            val channelDocument = channelDocument.copy(demands = listOf(demand0))

            coEvery { channelFirestoreService.getChannel(channelId) } returns channelDocument.success()
            coEvery {
                channelFirestoreService.updateFields(
                    channelId, mapOf(
                        ChannelDocument::demands to listOf(demand0, demand),
                        ChannelDocument::appointmentIds to listOf(appointment.id)
                    )
                )
            } returns channelId.success()

            val result = mockFirestoreContextCommit { channelDemandService.addDemandOnChannelByCase(channelId, caseRecord) }
            assertThat(result).isSuccessWithData(channelId)

            coVerifyOnce { channelFirestoreService.getChannel(any()) }
            coVerifyOnce { channelFirestoreService.updateFields(any(), any()) }
        }

    @Test
    fun `#addDemandOnChannelByCase returns channel id and create channel and add the new demand`() =
        runBlocking {
            val channelDocument = channelDocument.copy(
                kind = ChannelKind.CHANNEL,
                subCategory = ChannelSubCategory.LONGITUDINAL
            ).plusDemand(demand)
                .plusAppointmentId(appointment.id)

            val functionSlot = slot<suspend (CollectionReference) -> Query>()

            every { collectionReference.whereEqualTo("name", channelName) } returns collectionReference
            every { collectionReference.whereEqualTo("personId", personId.toString()) } returns collectionReference
            every { collectionReference.whereEqualTo("status", "ACTIVE") } returns collectionReference

            coEvery { appointmentService.get(appointment.id) } returns appointment.success()
            coEvery { channelFirestoreService.find(capture(functionSlot)) } returns NotFoundException().failure()
            coEvery { staffService.get(caseRecord.addedByStaffId!!) } returns staff.success()
            coEvery { channelService.add(channelDocument.copy(id = null)) } returns channelDocument.success()
            coEvery {
                channelService.addStaff(channelId, staff.id.toString(), staff.id.toString(), force = true)
            } returns channelId.success()
            coEvery { kafkaProducerService.produce(NewChannelDemandEvent(channelId, caseId)) } returns mockk()

            val result = mockFirestoreContextCommit { channelDemandService.addDemandOnChannelByCase(null, caseRecord) }
            assertThat(result).isSuccessWithData(channelId)

            functionSlot.captured.invoke(collectionReference)

            verify(exactly = 3) { collectionReference.whereEqualTo(any<String>(), any()) }
            coVerifyOnce { appointmentService.get(any()) }
            coVerifyOnce { channelFirestoreService.find(any()) }
            coVerifyOnce { staffService.get(any()) }
            coVerifyOnce { channelService.add(any()) }
            coVerifyOnce { channelService.addStaff(any(), any(), any(), any(), any()) }
            coVerifyOnce { kafkaProducerService.produce(any()) }
        }

    @Test
    fun `#addDemandOnChannelByCase returns not found exception when channel is not found and feature flag is disabled`() =
        runBlocking {
            val functionSlot = slot<suspend (CollectionReference) -> Query>()

            every { collectionReference.whereEqualTo("name", channelName) } returns collectionReference
            every { collectionReference.whereEqualTo("personId", personId.toString()) } returns collectionReference
            every { collectionReference.whereEqualTo("status", "ACTIVE") } returns collectionReference

            coEvery { appointmentService.get(appointment.id) } returns appointment.success()
            coEvery { channelFirestoreService.find(capture(functionSlot)) } returns NotFoundException().failure()

            withFeatureFlag(
                namespace = FeatureNamespace.CHANNELS,
                key = "should_create_channel_when_not_exists_by_demand",
                value = false
            ) {
                val result = mockFirestoreContextCommit {
                    channelDemandService.addDemandOnChannelByCase(null, caseRecord)
                }
                assertThat(result).isSuccessWithData("invalid")
            }

            functionSlot.captured.invoke(collectionReference)

            verify(exactly = 3) { collectionReference.whereEqualTo(any<String>(), any()) }
            coVerifyOnce { appointmentService.get(any()) }
            coVerifyOnce { channelFirestoreService.find(any()) }
            coVerifyNone { staffService.get(any()) }
            coVerifyNone { channelService.add(any()) }
            coVerifyNone { channelService.addStaff(any(), any(), any(), any(), any()) }
            coVerifyNone { kafkaProducerService.produce(any()) }
        }

    @OptIn(FirestoreContextUsage::class)
    @Test
    fun `#addDemandOnChannelByCase returns true when channel not found`() = runBlocking {
        coEvery { channelFirestoreService.getChannel(channelId) } returns NotFoundException().failure()

        val result = mockFirestoreContextCommit { channelDemandService.addDemandOnChannelByCase(channelId, caseRecord) }
        assertThat(result).isFailureOfType(NotFoundException::class)

        coVerifyOnce { channelFirestoreService.getChannel(any()) }
        coVerifyNone { channelFirestoreService.updateFields(any(), any()) }
    }

    @OptIn(FirestoreContextUsage::class)
    @Test
    fun `#addDemandOnChannelByCase returns error when an error occurs to find channel`() = runBlocking {
        coEvery { channelFirestoreService.getChannel(channelId) } returns Exception().failure()

        val result = mockFirestoreContextCommit { channelDemandService.addDemandOnChannelByCase(channelId, caseRecord) }
        assertThat(result).isFailureOfType(Exception::class)

        coVerifyOnce { channelFirestoreService.getChannel(any()) }
        coVerifyNone { channelFirestoreService.updateFields(any(), any()) }
    }

    @Test
    fun `#addDemandOnChannelByCase returns true when appointment not found`() = runBlocking {
        coEvery { appointmentService.get(appointment.id) } returns NotFoundException().failure()

        val result = mockFirestoreContextCommit { channelDemandService.addDemandOnChannelByCase(null, caseRecord) }
        assertThat(result).isFailureOfType(NotFoundException::class)

        coVerifyOnce { appointmentService.get(any()) }
    }

    @Test
    fun `#addDemandOnChannelByCase returns true when staff not found`() = runBlocking {
        val functionSlot = slot<suspend (CollectionReference) -> Query>()

        every { collectionReference.whereEqualTo("name", channelName) } returns collectionReference
        every { collectionReference.whereEqualTo("personId", personId.toString()) } returns collectionReference
        every { collectionReference.whereEqualTo("status", "ACTIVE") } returns collectionReference

        coEvery { appointmentService.get(appointment.id) } returns appointment.success()
        coEvery { channelFirestoreService.find(capture(functionSlot)) } returns NotFoundException().failure()
        coEvery { staffService.get(caseRecord.addedByStaffId!!) } returns NotFoundException().failure()

        val result = mockFirestoreContextCommit { channelDemandService.addDemandOnChannelByCase(null, caseRecord) }
        assertThat(result).isFailureOfType(NotFoundException::class)

        functionSlot.captured.invoke(collectionReference)

        verify(exactly = 3) { collectionReference.whereEqualTo(any<String>(), any()) }
        coVerifyOnce { appointmentService.get(any()) }
        coVerifyOnce { channelFirestoreService.find(any()) }
        coVerifyOnce { staffService.get(any()) }
    }

    @Test
    fun `#addDemandOnChannelByCase returns error when an error occurs to find appointment`() =
        runBlocking {
            coEvery { appointmentService.get(appointment.id) } returns Exception().failure()

            val result = mockFirestoreContextCommit { channelDemandService.addDemandOnChannelByCase(null, caseRecord) }
            assertThat(result).isFailureOfType(Exception::class)

            coVerifyOnce { appointmentService.get(any()) }
        }

    @Test
    fun `#addDemandOnChannelByCase returns error when an error occurs to find staff`() = runBlocking {
        val functionSlot = slot<suspend (CollectionReference) -> Query>()

        every { collectionReference.whereEqualTo("name", channelName) } returns collectionReference
        every { collectionReference.whereEqualTo("personId", personId.toString()) } returns collectionReference
        every { collectionReference.whereEqualTo("status", "ACTIVE") } returns collectionReference

        coEvery { appointmentService.get(appointment.id) } returns appointment.success()
        coEvery { channelFirestoreService.find(capture(functionSlot)) } returns NotFoundException().failure()
        coEvery { staffService.get(caseRecord.addedByStaffId!!) } returns Exception().failure()

        val result = mockFirestoreContextCommit { channelDemandService.addDemandOnChannelByCase(null, caseRecord) }
        assertThat(result).isFailureOfType(Exception::class)

        functionSlot.captured.invoke(collectionReference)

        verify(exactly = 3) { collectionReference.whereEqualTo(any<String>(), any()) }
        coVerifyOnce { appointmentService.get(any()) }
        coVerifyOnce { channelFirestoreService.find(any()) }
        coVerifyOnce { staffService.get(any()) }
    }

    @Test
    fun `#addDemandOnChannelByCase returns error when an error occurs to create channel`() =
        runBlocking {
            val channelDocument = channelDocument.copy(
                kind = ChannelKind.CHANNEL,
                subCategory = ChannelSubCategory.LONGITUDINAL
            ).plusDemand(demand)
                .plusAppointmentId(appointment.id)

            val functionSlot = slot<suspend (CollectionReference) -> Query>()

            every { collectionReference.whereEqualTo("name", channelName) } returns collectionReference
            every { collectionReference.whereEqualTo("personId", personId.toString()) } returns collectionReference
            every { collectionReference.whereEqualTo("status", "ACTIVE") } returns collectionReference

            coEvery { appointmentService.get(appointment.id) } returns appointment.success()
            coEvery { channelFirestoreService.find(capture(functionSlot)) } returns NotFoundException().failure()
            coEvery { staffService.get(caseRecord.addedByStaffId!!) } returns staff.success()
            coEvery { channelService.add(channelDocument.copy(id = null)) } returns Exception().failure()

            val result = mockFirestoreContextCommit { channelDemandService.addDemandOnChannelByCase(null, caseRecord) }
            assertThat(result).isFailureOfType(Exception::class)

            functionSlot.captured.invoke(collectionReference)

            verify(exactly = 3) { collectionReference.whereEqualTo(any<String>(), any()) }
            coVerifyOnce { appointmentService.get(any()) }
            coVerifyOnce { channelFirestoreService.find(any()) }
            coVerifyOnce { staffService.get(any()) }
            coVerifyOnce { channelService.add(any()) }
        }

    @OptIn(FirestoreContextUsage::class)
    @Test
    fun `#addDemandOnChannelByCase returns error when an error occurs to update channel for existing channel case`() =
        runBlocking {
            coEvery { channelFirestoreService.getChannel(channelId) } returns channelDocument.success()
            coEvery {
                channelFirestoreService.updateFields(
                    channelId, mapOf(
                        ChannelDocument::demands to listOf(demand),
                        ChannelDocument::appointmentIds to listOf(appointment.id)
                    )
                )
            } returns Exception().failure()

            val result = mockFirestoreContextCommit { channelDemandService.addDemandOnChannelByCase(channelId, caseRecord) }
            assertThat(result).isFailureOfType(Exception::class)

            coVerifyOnce { channelFirestoreService.getChannel(any()) }
            coVerifyOnce { channelFirestoreService.updateFields(any(), any()) }
        }

    @Test
    fun `#addDemandOnChannelByCase returns true when appointment not have caseRecordDetails`() =
        runBlocking {
            coEvery {
                appointmentService.get(appointment.id)
            } returns appointment.copy(caseRecordDetails = null).success()

            val result = mockFirestoreContextCommit { channelDemandService.addDemandOnChannelByCase(null, caseRecord) }
            assertThat(result).isFailureOfType(NotFoundException::class)

            coVerifyOnce { appointmentService.get(any()) }
        }

    @Test
    fun `#addDemandOnChannelByCase returns true when case record not have appointment reference`() =
        runBlocking {
            val caseRecord = caseRecord.copy(referencedLinks = emptyList())

            val result = mockFirestoreContextCommit { channelDemandService.addDemandOnChannelByCase(null, caseRecord) }
            assertThat(result).isFailureOfType(NotFoundException::class)
        }

    @OptIn(FirestoreContextUsage::class)
    @Test
    fun `#addDemandOnChannelByCase returns channel id and add the new demand when case not have channel id but appointment have channel Id`() =
        runBlocking {
            val appointment = appointment.copy(
                caseRecordDetails = listOf(
                    CaseRecordDetails(
                        caseId = caseId,
                        description = DiseaseDetails(
                            id = healthcareTeamId,
                            type = Disease.Type.CID_10,
                            value = "A10",
                            description = "CID A10"
                        ),
                        severity = CaseSeverity.COMPENSATED,
                        follow = null,
                        observation = "",
                        channel = ChannelId(
                            id = channelId,
                            name = channelName
                        )
                    )
                )
            )

            coEvery { appointmentService.get(appointment.id) } returns appointment.success()
            coEvery { staffService.get(caseRecord.addedByStaffId!!) } returns staff.success()
            coEvery { channelFirestoreService.getChannel(channelId) } returns channelDocument.success()
            coEvery {
                channelFirestoreService.updateFields(
                    channelId, mapOf(
                        ChannelDocument::demands to listOf(demand),
                        ChannelDocument::appointmentIds to listOf(appointment.id)
                    )
                )
            } returns channelId.success()
            coEvery { kafkaProducerService.produce(NewChannelDemandEvent(channelId, caseId)) } returns mockk()

            val result = mockFirestoreContextCommit { channelDemandService.addDemandOnChannelByCase(null, caseRecord) }
            assertThat(result).isSuccessWithData(channelId)

            coVerifyOnce { channelFirestoreService.getChannel(any()) }
            coVerifyOnce { appointmentService.get(any()) }
            coVerifyOnce { channelFirestoreService.updateFields(any(), any()) }
            coVerifyOnce { kafkaProducerService.produce(any()) }
        }

    @OptIn(FirestoreContextUsage::class)
    @Test
    fun `#addDemandOnChannelByCase returns channel id and add the new demand when case not have channel id but exists a channel with the same name for member`() =
        runBlocking {
            val functionSlot = slot<suspend (CollectionReference) -> Query>()

            every { collectionReference.whereEqualTo("name", channelName) } returns collectionReference
            every { collectionReference.whereEqualTo("personId", personId.toString()) } returns collectionReference
            every { collectionReference.whereEqualTo("status", "ACTIVE") } returns collectionReference

            coEvery { appointmentService.get(appointment.id) } returns appointment.success()
            coEvery { channelFirestoreService.find(capture(functionSlot)) } returns listOf(channelDocument).success()
            coEvery {
                channelFirestoreService.updateFields(
                    channelId, mapOf(
                        ChannelDocument::demands to listOf(demand),
                        ChannelDocument::appointmentIds to listOf(appointment.id)
                    )
                )
            } returns channelId.success()
            coEvery { kafkaProducerService.produce(NewChannelDemandEvent(channelId, caseId)) } returns mockk()

            val result = mockFirestoreContextCommit { channelDemandService.addDemandOnChannelByCase(null, caseRecord) }
            assertThat(result).isSuccessWithData(channelId)

            functionSlot.captured.invoke(collectionReference)

            verify(exactly = 3) { collectionReference.whereEqualTo(any<String>(), any()) }
            coVerifyOnce { channelFirestoreService.find(any()) }
            coVerifyNone { channelFirestoreService.getChannel(any()) }
            coVerifyOnce { appointmentService.get(any()) }
            coVerifyOnce { channelFirestoreService.updateFields(any(), any()) }
            coVerifyOnce { kafkaProducerService.produce(any()) }
        }

    @OptIn(FirestoreContextUsage::class)
    @Test
    fun `#addDemandOnChannelByCase returns channel id and add the new demand when the person case has the same channel id than the case record`() =
        runBlocking {
            val caseRecord = caseRecord.copy(
                referencedLinks = caseRecord.referencedLinks.plus(
                    CaseRecordReference(
                        id = channelId, model = CaseRecordReferenceModel.CHANNEL
                    )
                )
            )

            coEvery { channelFirestoreService.getChannel(channelId) } returns channelDocument.success()
            coEvery {
                channelFirestoreService.updateFields(
                    channelId, mapOf(
                        ChannelDocument::demands to listOf(demand),
                        ChannelDocument::appointmentIds to listOf(appointment.id)
                    )
                )
            } returns channelId.success()

            val result = mockFirestoreContextCommit { channelDemandService.addDemandOnChannelByCase(channelId, caseRecord) }
            assertThat(result).isSuccessWithData(channelId)

            coVerifyOnce { channelFirestoreService.getChannel(any()) }
            coVerifyOnce { channelFirestoreService.updateFields(any(), any()) }
        }

    @OptIn(FirestoreContextUsage::class)
    @Test
    fun `#addDemandOnChannelByCase returns channel id and add the new demand when the person case has channel id but the case record not`() =
        runBlocking {
            coEvery { channelFirestoreService.getChannel(channelId) } returns channelDocument.success()
            coEvery {
                channelFirestoreService.updateFields(
                    channelId, mapOf(
                        ChannelDocument::demands to listOf(demand),
                        ChannelDocument::appointmentIds to listOf(appointment.id)
                    )
                )
            } returns channelId.success()

            val result = mockFirestoreContextCommit { channelDemandService.addDemandOnChannelByCase(channelId, caseRecord) }
            assertThat(result).isSuccessWithData(channelId)

            coVerifyOnce { channelFirestoreService.getChannel(any()) }
            coVerifyOnce { channelFirestoreService.updateFields(any(), any()) }
        }

    @OptIn(FirestoreContextUsage::class)
    @Test
    fun `#addDemandOnChannelByCase returns channel id and update the demand when the person case has channel id but the case record was created by system`() =
        runBlocking {
            val caseRecord = caseRecord.copy(addedByStaffId = null)

            coEvery { channelFirestoreService.getChannel(channelId) } returns channelDocument.success()
            coEvery {
                channelFirestoreService.updateFields(
                    channelId, mapOf(
                        ChannelDocument::demands to listOf(demand),
                        ChannelDocument::appointmentIds to listOf(appointment.id)
                    )
                )
            } returns channelId.success()

            val result = mockFirestoreContextCommit { channelDemandService.addDemandOnChannelByCase(channelId, caseRecord) }
            assertThat(result).isSuccessWithData(channelId)

            coVerifyOnce { channelFirestoreService.getChannel(any()) }
            coVerifyOnce { channelFirestoreService.updateFields(any(), any()) }
        }

    @Test
    fun `#addDemandOnChannelByCase returns created channel id and add the new demand when channel is not active`() =
        runBlocking {
            val newChannelId = "newChannelId"
            val channelDocument = channelDocument.plusDemand(demand)
                .copy(
                    id = newChannelId,
                    kind = ChannelKind.CHANNEL,
                    subCategory = ChannelSubCategory.LONGITUDINAL
                )
                .plusAppointmentId(appointment.id)
            val archivedChannel = channelDocument.copy(
                status = ChannelStatus.ARCHIVED,
                archivedAt = LocalDateTime.now().minusMinutes(20).toTimestamp()
            )

            val functionSlot = slot<suspend (CollectionReference) -> Query>()

            every { collectionReference.whereEqualTo("name", channelName) } returns collectionReference
            every { collectionReference.whereEqualTo("personId", personId.toString()) } returns collectionReference
            every { collectionReference.whereEqualTo("status", "ACTIVE") } returns collectionReference

            coEvery { channelFirestoreService.getChannel(channelId) } returns archivedChannel.success()
            coEvery { appointmentService.get(appointment.id) } returns appointment.success()
            coEvery { channelFirestoreService.find(capture(functionSlot)) } returns emptyList<ChannelDocument>().success()
            coEvery { staffService.get(caseRecord.addedByStaffId!!) } returns staff.success()
            coEvery { channelService.add(channelDocument.copy(id = null)) } returns channelDocument.success()
            coEvery {
                channelService.addStaff(newChannelId, staff.id.toString(), staff.id.toString(), force = true)
            } returns newChannelId.success()
            coEvery { kafkaProducerService.produce(NewChannelDemandEvent(newChannelId, caseId)) } returns mockk()

            val result = mockFirestoreContextCommit { channelDemandService.addDemandOnChannelByCase(channelId, caseRecord) }
            assertThat(result).isSuccessWithData(newChannelId)

            coVerifyOnce { channelFirestoreService.getChannel(any()) }
            coVerifyOnce { channelFirestoreService.find(any()) }
            coVerifyOnce { appointmentService.get(any()) }
            coVerifyOnce { staffService.get(any()) }
            coVerifyOnce { channelService.add(any()) }
            coVerifyOnce { channelService.addStaff(any(), any(), any(), any(), any()) }
            coVerifyOnce { kafkaProducerService.produce(any()) }
        }

    @Test
    fun `#addDemandOnChannelByCase returns same channel id when channel is not active and is recently archived`() =
        runBlocking {
            val newChannelId = "newChannelId"
            val channelDocument = channelDocument.plusDemand(demand)
                .copy(
                    id = newChannelId,
                    kind = ChannelKind.CHANNEL,
                    subCategory = ChannelSubCategory.LONGITUDINAL
                )
                .plusAppointmentId(appointment.id)
            val archivedChannel = channelDocument.copy(
                status = ChannelStatus.ARCHIVED,
                archivedAt = Timestamp.now()
            )

            coEvery { channelFirestoreService.getChannel(channelId) } returns archivedChannel.success()

            val result = mockFirestoreContextCommit { channelDemandService.addDemandOnChannelByCase(channelId, caseRecord) }
            assertThat(result).isSuccessWithData(channelId)

            coVerifyOnce { channelFirestoreService.getChannel(any()) }
        }

    /**
     * This scenario does not exist, this test is just to increase coverage :)
     */
    @OptIn(FirestoreContextUsage::class)
    @Test
    fun `#addDemandOnChannelByCase returns error when channel returns without id`() = runBlocking {
        coEvery { channelFirestoreService.getChannel(channelId) } returns channelDocument.copy(id = null).success()

        val result = mockFirestoreContextCommit { channelDemandService.addDemandOnChannelByCase(channelId, caseRecord) }
        assertThat(result).isFailureOfType(NullPointerException::class)

        coVerifyOnce { channelFirestoreService.getChannel(any()) }
        coVerifyNone { channelFirestoreService.updateFields(any(), any()) }
    }

    @OptIn(FirestoreContextUsage::class)
    @Test
    fun `#addDemandOnChannel returns channel id and add new demand on channel`() = runBlocking {
        coEvery { caseRecordService.getCurrentByCaseId(personId, caseId) } returns caseRecordWithoutLinks.success()
        coEvery { channelFirestoreService.getChannel(channelId) } returns channelDocumentForAssociation.success()
        coEvery {
            channelService.addStaff(channelId, staff.id.toString(), staff.id.toString(), force = true)
        } returns channelId.success()
        coEvery {
            channelFirestoreService.updateFields(
                channelId, mapOf(
                    ChannelDocument::demands to listOf(demand),
                    ChannelDocument::appointmentIds to emptyList<String>()
                )
            )
        } returns channelId.success()
        coEvery { kafkaProducerService.produce(NewChannelDemandEvent(channelId, caseId)) } returns mockk()

        val result = mockFirestoreContextCommit { channelDemandService.addDemandOnChannel(channelDemand, caseId, personId, staff.id) }
        assertThat(result).isSuccessWithData(channelId)

        coVerifyOnce { caseRecordService.getCurrentByCaseId(any(), any()) }
        coVerifyOnce { channelFirestoreService.getChannel(any()) }
        coVerifyOnce { channelService.addStaff(any(), any(), any(), any(), any()) }
        coVerifyOnce { channelFirestoreService.updateFields(any(), any()) }
        coVerifyOnce { kafkaProducerService.produce(any()) }
    }

    @OptIn(FirestoreContextUsage::class)
    @Test
    fun `#addDemandOnChannel returns channel id and add new demand on channel with other demand`() = runBlocking {
        val demand0 = demand.copy(caseId = RangeUUID.generate())
        val channelDocumentForAssociation = channelDocumentForAssociation.copy(demands = listOf(demand0))

        coEvery { caseRecordService.getCurrentByCaseId(personId, caseId) } returns caseRecordWithoutLinks.success()
        coEvery { channelFirestoreService.getChannel(channelId) } returns channelDocumentForAssociation.success()
        coEvery {
            channelService.addStaff(channelId, staff.id.toString(), staff.id.toString(), force = true)
        } returns channelId.success()
        coEvery {
            channelFirestoreService.updateFields(
                channelId, mapOf(
                    ChannelDocument::demands to listOf(demand0, demand),
                    ChannelDocument::appointmentIds to emptyList<String>()
                )
            )
        } returns channelId.success()
        coEvery { kafkaProducerService.produce(NewChannelDemandEvent(channelId, caseId)) } returns mockk()

        val result = mockFirestoreContextCommit { channelDemandService.addDemandOnChannel(channelDemand, caseId, personId, staff.id) }
        assertThat(result).isSuccessWithData(channelId)

        coVerifyOnce { caseRecordService.getCurrentByCaseId(any(), any()) }
        coVerifyOnce { channelFirestoreService.getChannel(any()) }
        coVerifyOnce { channelService.addStaff(any(), any(), any(), any(), any()) }
        coVerifyOnce { channelFirestoreService.updateFields(any(), any()) }
        coVerifyOnce { kafkaProducerService.produce(any()) }
    }

    @Test
    fun `#addDemandOnChannel returns channel id and create channel and add the new demand`() = runBlocking {
        val channelDocumentForAssociation = channelDocumentForAssociation.plusDemand(demand)
        val functionSlot = slot<suspend (CollectionReference) -> Query>()

        coEvery { caseRecordService.getCurrentByCaseId(personId, caseId) } returns caseRecordWithoutLinks.success()

        every { collectionReference.whereEqualTo("name", channelName) } returns collectionReference
        every { collectionReference.whereEqualTo("personId", personId.toString()) } returns collectionReference
        every { collectionReference.whereEqualTo("status", "ACTIVE") } returns collectionReference

        coEvery { channelFirestoreService.find(capture(functionSlot)) } returns NotFoundException().failure()
        coEvery { staffService.get(caseRecord.addedByStaffId!!) } returns staff.success()
        coEvery { channelService.add(channelDocumentForAssociation.copy(id = null)) } returns channelDocumentForAssociation.success()
        coEvery {
            channelService.addStaff(channelId, staff.id.toString(), staff.id.toString(), force = true)
        } returns channelId.success()
        coEvery { kafkaProducerService.produce(NewChannelDemandEvent(channelId, caseId)) } returns mockk()

        val result = mockFirestoreContextCommit { channelDemandService.addDemandOnChannel(channelDemandWithoutId, caseId, personId, staff.id) }
        assertThat(result).isSuccessWithData(channelId)

        functionSlot.captured.invoke(collectionReference)

        verify(exactly = 3) { collectionReference.whereEqualTo(any<String>(), any()) }
        coVerifyOnce { channelFirestoreService.find(any()) }
        coVerifyOnce { caseRecordService.getCurrentByCaseId(any(), any()) }
        coVerifyOnce { staffService.get(any()) }
        coVerifyOnce { channelService.add(any()) }
        coVerifyOnce { channelService.addStaff(any(), any(), any(), any(), any()) }
        coVerifyOnce { kafkaProducerService.produce(any()) }
    }

    @OptIn(FirestoreContextUsage::class)
    @Test
    fun `#addDemandOnChannel returns true when channel not found`() = runBlocking {
        coEvery { caseRecordService.getCurrentByCaseId(personId, caseId) } returns caseRecordWithoutLinks.success()
        coEvery { channelFirestoreService.getChannel(channelId) } returns NotFoundException().failure()

        val result = mockFirestoreContextCommit { channelDemandService.addDemandOnChannel(channelDemand, caseId, personId, staff.id) }
        assertThat(result).isFailureOfType(NotFoundException::class)

        coVerifyOnce { caseRecordService.getCurrentByCaseId(any(), any()) }
        coVerifyOnce { channelFirestoreService.getChannel(any()) }
        coVerifyNone { channelFirestoreService.updateFields(any(), any()) }
    }

    @OptIn(FirestoreContextUsage::class)
    @Test
    fun `#addDemandOnChannel returns error when an error occurs to find channel`() = runBlocking {
        coEvery { caseRecordService.getCurrentByCaseId(personId, caseId) } returns caseRecordWithoutLinks.success()
        coEvery { channelFirestoreService.getChannel(channelId) } returns Exception().failure()

        val result = mockFirestoreContextCommit { channelDemandService.addDemandOnChannel(channelDemand, caseId, personId, staff.id) }
        assertThat(result).isFailureOfType(Exception::class)

        coVerifyOnce { caseRecordService.getCurrentByCaseId(any(), any()) }
        coVerifyOnce { channelFirestoreService.getChannel(any()) }
        coVerifyNone { channelFirestoreService.updateFields(any(), any()) }
    }

    @Test
    fun `#addDemandOnChannel returns true when staff not found`() = runBlocking {
        coEvery { caseRecordService.getCurrentByCaseId(personId, caseId) } returns caseRecordWithoutLinks.success()
        coEvery { channelFirestoreService.find(any()) } returns NotFoundException().failure()
        coEvery { staffService.get(caseRecord.addedByStaffId!!) } returns NotFoundException().failure()

        val result = mockFirestoreContextCommit { channelDemandService.addDemandOnChannel(channelDemandWithoutId, caseId, personId, staff.id) }
        assertThat(result).isFailureOfType(NotFoundException::class)

        coVerifyOnce { caseRecordService.getCurrentByCaseId(any(), any()) }
        coVerifyOnce { channelFirestoreService.find(any()) }
        coVerifyOnce { staffService.get(any()) }
    }

    @Test
    fun `#addDemandOnChannel returns error when an error occurs to find staff`() = runBlocking {
        val functionSlot = slot<suspend (CollectionReference) -> Query>()

        coEvery { caseRecordService.getCurrentByCaseId(personId, caseId) } returns caseRecordWithoutLinks.success()

        every { collectionReference.whereEqualTo("name", channelName) } returns collectionReference
        every { collectionReference.whereEqualTo("personId", personId.toString()) } returns collectionReference
        every { collectionReference.whereEqualTo("status", "ACTIVE") } returns collectionReference

        coEvery { channelFirestoreService.find(capture(functionSlot)) } returns NotFoundException().failure()
        coEvery { staffService.get(caseRecord.addedByStaffId!!) } returns Exception().failure()

        val result = mockFirestoreContextCommit { channelDemandService.addDemandOnChannel(channelDemandWithoutId, caseId, personId, staff.id) }
        assertThat(result).isFailureOfType(Exception::class)

        functionSlot.captured.invoke(collectionReference)

        verify(exactly = 3) { collectionReference.whereEqualTo(any<String>(), any()) }
        coVerifyOnce { channelFirestoreService.find(any()) }
        coVerifyOnce { caseRecordService.getCurrentByCaseId(any(), any()) }
        coVerifyOnce { channelFirestoreService.find(any()) }
        coVerifyOnce { staffService.get(any()) }
    }

    @Test
    fun `#addDemandOnChannel returns error when an error occurs to create channel`() = runBlocking {
        val channelDocumentForAssociation = channelDocumentForAssociation.plusDemand(demand)

        val functionSlot = slot<suspend (CollectionReference) -> Query>()

        coEvery { caseRecordService.getCurrentByCaseId(personId, caseId) } returns caseRecordWithoutLinks.success()

        every { collectionReference.whereEqualTo("name", channelName) } returns collectionReference
        every { collectionReference.whereEqualTo("personId", personId.toString()) } returns collectionReference
        every { collectionReference.whereEqualTo("status", "ACTIVE") } returns collectionReference

        coEvery { channelFirestoreService.find(capture(functionSlot)) } returns NotFoundException().failure()
        coEvery { staffService.get(caseRecord.addedByStaffId!!) } returns staff.success()
        coEvery { channelService.add(channelDocumentForAssociation.copy(id = null)) } returns Exception().failure()

        val result = mockFirestoreContextCommit { channelDemandService.addDemandOnChannel(channelDemandWithoutId, caseId, personId, staff.id) }
        assertThat(result).isFailureOfType(Exception::class)

        functionSlot.captured.invoke(collectionReference)

        verify(exactly = 3) { collectionReference.whereEqualTo(any<String>(), any()) }
        coVerifyOnce { channelFirestoreService.find(any()) }
        coVerifyOnce { caseRecordService.getCurrentByCaseId(any(), any()) }
        coVerifyOnce { channelFirestoreService.find(any()) }
        coVerifyOnce { staffService.get(any()) }
        coVerifyOnce { channelService.add(any()) }
    }

    @OptIn(FirestoreContextUsage::class)
    @Test
    fun `#addDemandOnChannel returns error when an error occurs to update channel for existing channel case`() =
        runBlocking {
            coEvery { caseRecordService.getCurrentByCaseId(personId, caseId) } returns caseRecordWithoutLinks.success()
            coEvery { channelFirestoreService.getChannel(channelId) } returns channelDocumentForAssociation.success()
            coEvery {
                channelFirestoreService.updateFields(
                    channelId, mapOf(
                        ChannelDocument::demands to listOf(demand),
                        ChannelDocument::appointmentIds to emptyList<String>()
                    )
                )
            } returns Exception().failure()

            val result = mockFirestoreContextCommit { channelDemandService.addDemandOnChannel(channelDemand, caseId, personId, staff.id) }
            assertThat(result).isFailureOfType(Exception::class)

            coVerifyOnce { caseRecordService.getCurrentByCaseId(any(), any()) }
            coVerifyOnce { channelFirestoreService.getChannel(any()) }
            coVerifyOnce { channelFirestoreService.updateFields(any(), any()) }
        }

    @OptIn(FirestoreContextUsage::class)
    @Test
    fun `#addDemandOnChannel returns channel id and add the new demand when case not have channel id but exists a channel with the same name for member`() =
        runBlocking {
            val functionSlot = slot<suspend (CollectionReference) -> Query>()

            coEvery { caseRecordService.getCurrentByCaseId(personId, caseId) } returns caseRecordWithoutLinks.success()

            coEvery { channelFirestoreService.find(capture(functionSlot)) } returns listOf(channelDocumentForAssociation).success()

            every { collectionReference.whereEqualTo("name", channelName) } returns collectionReference
            every { collectionReference.whereEqualTo("personId", personId.toString()) } returns collectionReference
            every { collectionReference.whereEqualTo("status", "ACTIVE") } returns collectionReference
            coEvery {
                channelFirestoreService.updateFields(
                    channelId, mapOf(
                        ChannelDocument::demands to listOf(demand),
                        ChannelDocument::appointmentIds to emptyList<String>()
                    )
                )
            } returns channelId.success()
            coEvery {
                channelService.addStaff(channelId, staff.id.toString(), staff.id.toString(), force = true)
            } returns channelId.success()
            coEvery { kafkaProducerService.produce(NewChannelDemandEvent(channelId, caseId)) } returns mockk()

            val result = mockFirestoreContextCommit { channelDemandService.addDemandOnChannel(channelDemandWithoutId, caseId, personId, staff.id) }
            assertThat(result).isSuccessWithData(channelId)

            functionSlot.captured.invoke(collectionReference)

            verify(exactly = 3) { collectionReference.whereEqualTo(any<String>(), any()) }
            coVerifyOnce { caseRecordService.getCurrentByCaseId(any(), any()) }
            coVerifyOnce { channelFirestoreService.find(any()) }
            coVerifyNone { channelFirestoreService.getChannel(any()) }
            coVerifyOnce { channelFirestoreService.updateFields(any(), any()) }
            coVerifyOnce { channelService.addStaff(any(), any(), any(), any(), any()) }
            coVerifyOnce { kafkaProducerService.produce(any()) }
        }

    @OptIn(FirestoreContextUsage::class)
    @Test
    fun `#addDemandOnChannel returns channel id and add the new demand when the person case has the same channel id than the case record`() =
        runBlocking {
            coEvery { caseRecordService.getCurrentByCaseId(personId, caseId) } returns caseRecordWithoutLinks.success()
            coEvery { channelFirestoreService.getChannel(channelId) } returns channelDocumentForAssociation.success()
            coEvery {
                channelService.addStaff(channelId, staff.id.toString(), staff.id.toString(), force = true)
            } returns channelId.success()
            coEvery {
                channelFirestoreService.updateFields(
                    channelId, mapOf(
                        ChannelDocument::demands to listOf(demand),
                        ChannelDocument::appointmentIds to emptyList<String>()
                    )
                )
            } returns channelId.success()
            coEvery { kafkaProducerService.produce(NewChannelDemandEvent(channelId, caseId)) } returns mockk()

            val result = mockFirestoreContextCommit { channelDemandService.addDemandOnChannel(channelDemand, caseId, personId, staff.id) }
            assertThat(result).isSuccessWithData(channelId)

            coVerifyOnce { caseRecordService.getCurrentByCaseId(any(), any()) }
            coVerifyOnce { channelFirestoreService.getChannel(any()) }
            coVerifyOnce { channelService.addStaff(any(), any(), any(), any(), any()) }
            coVerifyOnce { channelFirestoreService.updateFields(any(), any()) }
            coVerifyOnce { kafkaProducerService.produce(any()) }
        }

    @OptIn(FirestoreContextUsage::class)
    @Test
    fun `#addDemandOnChannel returns channel id and add the new demand when the person case has channel id but the case record not`() =
        runBlocking {
            coEvery { caseRecordService.getCurrentByCaseId(personId, caseId) } returns caseRecordWithoutLinks.success()
            coEvery { channelFirestoreService.getChannel(channelId) } returns channelDocumentForAssociation.success()

            coEvery {
                channelService.addStaff(channelId, staff.id.toString(), staff.id.toString(), force = true)
            } returns channelId.success()
            coEvery {
                channelFirestoreService.updateFields(
                    channelId, mapOf(
                        ChannelDocument::demands to listOf(demand),
                        ChannelDocument::appointmentIds to emptyList<String>()
                    )
                )
            } returns channelId.success()
            coEvery { kafkaProducerService.produce(NewChannelDemandEvent(channelId, caseId)) } returns mockk()

            val result = mockFirestoreContextCommit { channelDemandService.addDemandOnChannel(channelDemand, caseId, personId, staff.id) }
            assertThat(result).isSuccessWithData(channelId)

            coVerifyOnce { caseRecordService.getCurrentByCaseId(any(), any()) }
            coVerifyOnce { channelFirestoreService.getChannel(any()) }
            coVerifyOnce { channelService.addStaff(any(), any(), any(), any(), any()) }
            coVerifyOnce { channelFirestoreService.updateFields(any(), any()) }
            coVerifyOnce { kafkaProducerService.produce(any()) }
        }

    @Test
    fun `#addDemandOnChannel returns created channel id and add the new demand but not add staff when channel is not active`() =
        runBlocking {
            val newChannelId = "newChannelId"
            val channelDocument = channelDocumentForAssociation.copy(
                id = newChannelId,
                name = channelName,
                staff = mutableMapOf(staff.id.toString() to ChannelStaffInfo.from(staff).copy(owner = true, lastSync = now)),
            ).plusDemand(demand)
            val archivedChannel = channelDocumentForAssociation.copy(
                status = ChannelStatus.ARCHIVED,
                archivedAt = LocalDateTime.now().minusMinutes(20).toTimestamp()
            )

            val functionSlot = slot<suspend (CollectionReference) -> Query>()

            every { collectionReference.whereEqualTo("name", channelName) } returns collectionReference
            every { collectionReference.whereEqualTo("personId", personId.toString()) } returns collectionReference
            every { collectionReference.whereEqualTo("status", "ACTIVE") } returns collectionReference

            coEvery { caseRecordService.getCurrentByCaseId(personId, caseId) } returns caseRecordWithoutLinks.success()
            coEvery { channelFirestoreService.getChannel(channelId) } returns archivedChannel.success()
            coEvery { channelFirestoreService.find(capture(functionSlot)) } returns NotFoundException().failure()
            coEvery { staffService.get(caseRecord.addedByStaffId!!) } returns staff.success()
            coEvery { channelService.add(channelDocument.copy(id = null)) } returns channelDocument.success()
            coEvery {
                channelService.addStaff(newChannelId, staff.id.toString(), staff.id.toString(), force = true)
            } returns newChannelId.success()
            coEvery { kafkaProducerService.produce(NewChannelDemandEvent(newChannelId, caseId)) } returns mockk()

            val result = mockFirestoreContextCommit { channelDemandService.addDemandOnChannel(channelDemand, caseId, personId, staff.id) }
            assertThat(result).isSuccessWithData(newChannelId)

            coVerifyOnce { caseRecordService.getCurrentByCaseId(any(), any()) }
            coVerify(exactly = 2) { channelFirestoreService.getChannel(any()) }
            coVerifyOnce { channelFirestoreService.find(any()) }
            coVerifyOnce { staffService.get(any()) }
            coVerifyOnce { channelService.add(any()) }
            coVerifyOnce { channelService.addStaff(any(), any(), any(), any(), any()) }
            coVerifyOnce { kafkaProducerService.produce(any()) }
        }
}
