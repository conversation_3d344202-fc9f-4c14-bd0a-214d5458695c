package br.com.alice.healthlogic.services.bud

import br.com.alice.common.core.PersonId
import br.com.alice.common.core.exceptions.InvalidArgumentException
import br.com.alice.common.service.data.dsl.and
import br.com.alice.data.layer.models.BudNode
import br.com.alice.data.layer.models.BudNode.BudNodeType
import br.com.alice.data.layer.models.ServiceScriptNavigationGroup
import br.com.alice.data.layer.models.ServiceScriptRelationship
import br.com.alice.data.layer.models.ServiceScriptStatus
import br.com.alice.data.layer.models.ServiceScriptStatus.ACTIVE
import br.com.alice.data.layer.models.ServiceScriptStatus.INACTIVE
import br.com.alice.data.layer.services.BudNodeDataService
import br.com.alice.data.layer.services.ServiceScriptNavigationGroupDataService
import br.com.alice.data.layer.services.ServiceScriptRelationshipDataService
import br.com.alice.healthlogic.client.BudAdministrationService
import br.com.alice.healthlogic.logics.builders.BudNodeQueryBuilder
import br.com.alice.healthlogic.logics.builders.ServiceScriptRelationshipQueryBuilder
import br.com.alice.healthlogic.models.bud.OrderBy
import com.github.kittinunf.result.Result
import java.time.LocalDateTime
import java.util.UUID

class BudAdministrationServiceImpl(
    private val budNodeDataService: BudNodeDataService,
    private val serviceScriptRelationshipDataService: ServiceScriptRelationshipDataService,
    private val serviceScriptNavigationGroupDataService: ServiceScriptNavigationGroupDataService,
): BudAdministrationService {

    companion object {
        val HIERARCHY = mapOf(
            BudNodeType.CATEGORY to listOf(BudNodeType.SUB_CATEGORY),
            BudNodeType.SUB_CATEGORY to listOf(BudNodeType.SCRIPT),
            BudNodeType.SCRIPT to listOf(BudNodeType.QUESTION, BudNodeType.QUESTION_WITH_OPTIONS),
            BudNodeType.QUESTION to listOf(BudNodeType.QUESTION, BudNodeType.ACTION),
            BudNodeType.QUESTION_WITH_OPTIONS to listOf(BudNodeType.QUESTION, BudNodeType.QUESTION_WITH_OPTIONS, BudNodeType.EXTERNAL_VALIDATION, BudNodeType.ACTION, BudNodeType.EXTERNAL_OPTIONS),
            BudNodeType.EXTERNAL_OPTIONS to listOf(BudNodeType.QUESTION, BudNodeType.QUESTION_WITH_OPTIONS, BudNodeType.ACTION),
            BudNodeType.EXTERNAL_VALIDATION to listOf(BudNodeType.QUESTION, BudNodeType.EXTERNAL_OPTIONS, BudNodeType.QUESTION_WITH_OPTIONS, BudNodeType.ACTION),
        )
    }
    override suspend fun getNode(id: UUID): Result<BudNode, Throwable> {
        return budNodeDataService.get(id)
    }

    override suspend fun getNodes(
        ids: List<UUID>,
        name: String?,
        type: BudNodeType?,
        orderBy: OrderBy,
        range: IntRange,
    ): Result<Pair<List<BudNode>, Int>, Throwable> = Result.of {
        val query = BudNodeQueryBuilder(
            budNodeDataService.queryBuilder()
        ).build(
            type = type?.let { arrayOf(type) } ?: emptyArray(),
            ids = ids,
            name = name,
            status = listOf(ACTIVE, INACTIVE)
        )

        val nodes = budNodeDataService.find {
            query
                .orderBy { orderBy.fieldNode() }
                .sortOrder { orderBy.sort() }
                .offset { range.first }
                .limit { range.count() }
        }.get()
        val totalCount = budNodeDataService.count { query }.get()

        Pair(nodes, totalCount)
    }

    override suspend fun createNode(node: BudNode): Result<BudNode, Throwable> {
        return budNodeDataService.add(node)
    }

    override suspend fun updateNode(node: BudNode): Result<BudNode, Throwable> {
        return budNodeDataService.update(node)
    }

    override suspend fun getRelationship(id: UUID): Result<ServiceScriptRelationship, Throwable> {
        return serviceScriptRelationshipDataService.get(id)
    }

    override suspend fun getRelationships(
        name: String?,
        status: ServiceScriptStatus?,
        orderBy: OrderBy,
        range: IntRange,
    ): Result<Pair<List<ServiceScriptRelationship>, Int>, Throwable> = Result.of {
        val query = ServiceScriptRelationshipQueryBuilder(
            serviceScriptRelationshipDataService.queryBuilder()
        ).build(
            name = name,
            status = status?.let { listOf(status) } ?: emptyList()
        )

        val nodes = serviceScriptRelationshipDataService.find {
            query
                .orderBy { orderBy.fieldNode() }
                .sortOrder { orderBy.sort() }
                .offset { range.first }
                .limit { range.count() }
        }.get()
        val totalCount = serviceScriptRelationshipDataService.count { query }.get()

        Pair(nodes, totalCount)
    }

    override suspend fun createRelationship(
        relationship: ServiceScriptRelationship
    ): Result<ServiceScriptRelationship, Throwable> = Result.of {
        validateRelationshipHierarchy(relationship)

        serviceScriptRelationshipDataService.add(relationship).get()
    }

    override suspend fun updateRelationship(
        relationship: ServiceScriptRelationship
    ): Result<ServiceScriptRelationship, Throwable> = Result.of {
        validateRelationshipHierarchy(relationship)

        return serviceScriptRelationshipDataService.update(relationship)
    }

    override suspend fun findByPersonIdAndScriptNodesAndFinishedAt(
        personId: PersonId,
        scriptNodeIds: List<UUID>,
        finishedAt: LocalDateTime
    ): Result<ServiceScriptNavigationGroup, Throwable> =
        serviceScriptNavigationGroupDataService.findOne {
            where {
                this.personId.eq(personId) and
                        this.scriptNodeId.inList(scriptNodeIds) and
                        this.finishedAt.greaterEq(finishedAt)
            }
                .orderBy { this.startedAt }
                .sortOrder { desc }
        }

    private suspend fun validateRelationshipHierarchy(relationship: ServiceScriptRelationship) {
        if (relationship.nodeChildId == relationship.nodeParentId)
            throw InvalidArgumentException(
                code = "relationship_between_node_and_itself",
                message = "Não é possível relacionar um nó consigo mesmo."
            )

        val parentNode = getNode(relationship.nodeParentId!!).get()
        val childNode = getNode(relationship.nodeChildId!!).get()

        if (HIERARCHY[parentNode.type] == null || !HIERARCHY[parentNode.type]!!.contains(childNode.type)) {
            throw InvalidArgumentException(
                code = "relationship_node_type_mismatch",
                message = "Não é possível criar uma relação entre esses tipos de nós."
            )
        }
    }
}
