package br.com.alice.exec.indicator.api.controller

import br.com.alice.common.DefaultErrorResponse
import br.com.alice.common.MvUtil
import br.com.alice.common.NotFoundResponse
import br.com.alice.common.Response
import br.com.alice.common.controllers.Controller
import br.com.alice.common.core.exceptions.NotFoundException
import br.com.alice.common.core.extensions.isNotNullOrEmpty
import br.com.alice.common.core.extensions.toPersonId
import br.com.alice.common.foldResponse
import br.com.alice.common.logging.logger
import br.com.alice.common.observability.setAttribute
import br.com.alice.data.layer.models.HealthProfessional
import br.com.alice.exec.indicator.api.controller.exception.UserEmailRequiredException
import br.com.alice.exec.indicator.api.models.AttendanceTypeCode
import br.com.alice.exec.indicator.api.models.EligibilityResponse
import br.com.alice.exec.indicator.models.EitaUserType
import br.com.alice.provider.client.ProviderUnitService
import br.com.alice.secondary.attention.client.EligibilityService
import br.com.alice.secondary.attention.models.EligibilityCheckedExternallyResponse
import br.com.alice.staff.client.HealthProfessionalService
import com.github.kittinunf.result.flatMapError
import com.github.kittinunf.result.map
import com.github.kittinunf.result.success
import io.ktor.http.HttpStatusCode
import io.opentelemetry.api.trace.Span
import io.opentelemetry.api.trace.StatusCode
import java.util.UUID

class EligibilityController(
    private val eligibilityService: EligibilityService,
    private val healthProfessionalService: HealthProfessionalService,
    private val providerUnitService: ProviderUnitService,
) : Controller() {

    private val attendanceTypes = mapOf(
        MvUtil.TISS.PS to AttendanceTypeCode(
            code = "10101039",
            tableType = "22"
        ),
        MvUtil.TISS.EXAM to AttendanceTypeCode(
            code = "98001148",
            tableType = "98"
        ),
    )

    suspend fun checkEligibilityExternally(personId: UUID, providerUnitId: UUID): Response {
        val userEmail = currentUserEmail() ?: throw UserEmailRequiredException()
        return eligibilityService.checkEligibilityExternally(personId.toPersonId(), providerUnitId, userEmail)
            .flatMapError { error ->
                logger.error("EligibilityController::checkEligibilityExternally", error)
                EligibilityCheckedExternallyResponse(eligible = true, message = null).success()
            }.foldResponse()
    }

    suspend fun checkEligibilityByAttendanceTypeExternally(personId: UUID, providerUnitId: UUID): Response {
        logger.info(
            "EligibilityController::checkEligibilityByAttendanceTypeExternally",
            "personId" to personId,
            "providerUnitId" to providerUnitId
        )
        val userEmail = currentUserEmail() ?: throw UserEmailRequiredException()
        return eligibilityService.checkEligibilityExternally(personId.toPersonId(), providerUnitId, userEmail).map {
            EligibilityResponse(
                eligibilityByAttendanceType = attendanceTypes.keys.associateWith { true },
                isEligibleAtProviderUnit = it.eligible,
                message = it.message,
            )
        }.flatMapError { error ->
            logger.error("EligibilityController::checkEligibilityExternally", error)
            EligibilityResponse(
                eligibilityByAttendanceType = attendanceTypes.keys.associateWith { true },
                isEligibleAtProviderUnit = true,
                message = null,
            ).success()
        }.foldResponse()
    }

    suspend fun getPersonEligibilityBySpecialist(personId: UUID, providerUnitId: UUID): Response =
        span("getPersonEligibilityBySpecialist") { span ->
            val roles = getUserRoles()
            val userEmail = currentUserEmail() ?: "<EMAIL>"
            span.setAttribute("person_id", personId)
            span.setAttribute("provider_unit_id", providerUnitId)
            span.setAttribute("roles_size", roles.size)

            if (roles.isEmpty()) {
                span.setStatus(StatusCode.ERROR, "User does not have the correct roles for this request")

                return@span NotFoundResponse(
                    httpStatusCode = HttpStatusCode.NotFound,
                    msg = "User does not have the correct roles for this request"
                )
            }

            getSpecialistsResponse(span, roles, providerUnitId).let { (specialists, response) ->
                if (response != null) return@span response

                eligibilityService
                    .getPersonEligibilityBySpecialist(
                        personId.toPersonId(),
                        specialists,
                        userEmail,
                        providerUnitId
                    )
                    .foldResponse(HttpStatusCode.OK)
            }
        }

    private suspend fun getSpecialistsResponse(
        span: Span,
        roles: List<String>,
        providerUnitId: UUID
    ): Pair<List<HealthProfessional>, Response?> {
        val isNotHealthSpecialist = !roles.contains(EitaUserType.HEALTH_SPECIALIST.name)
        val emptyResponse = listOf<HealthProfessional>()
        val specialistEmail = currentUserEmail() ?: "<EMAIL>"

        return if (isNotHealthSpecialist) {
            providerUnitService.get(providerUnitId).fold(
                { providerUnit ->
                    val clinicalStaffIds = providerUnit.clinicalStaffIds
                    if (clinicalStaffIds.isNotNullOrEmpty()) {
                        healthProfessionalService.getByStaffIds(clinicalStaffIds!!).fold(
                            { specialists -> specialists to null },
                            { emptyResponse to getHealthCommunitySpecialistError(specialistEmail, it, span) }
                        )
                    } else emptyResponse to null
                },
                { emptyResponse to getProviderUnitError(providerUnitId, it, span) }
            )
        } else {
            healthProfessionalService.findByEmail(specialistEmail).fold(
                { specialists -> listOf(specialists) to null },
                { emptyResponse to getHealthCommunitySpecialistError(specialistEmail, it, span) }
            )
        }
    }

    private fun getProviderUnitError(providerUnitId: UUID, throwable: Throwable, span: Span): Response {
        span.setStatus(
            StatusCode.ERROR,
            "Error getting provider unit for id: $providerUnitId, error: ${throwable.message}"
        )
        return when (throwable) {
            is NotFoundException -> NotFoundResponse(
                httpStatusCode = HttpStatusCode.NotFound,
                msg = "Provider with id equal to $providerUnitId not found in our database"
            )

            else -> DefaultErrorResponse(
                code = "error_getting_provider_unit",
                message = "Error getting provider unit"
            )
        }
    }

    private fun getHealthCommunitySpecialistError(email: String, throwable: Throwable, span: Span): Response {
        val emailPrefix = email.split("@").first()
        val emailDomain = email.split("@").last()
        span.setStatus(
            StatusCode.ERROR,
            "Error getting health community specialist for email(prefix:$emailPrefix, domain:$emailDomain), error: ${throwable.message}"
        )
        return when (throwable) {
            is NotFoundException -> NotFoundResponse(
                httpStatusCode = HttpStatusCode.NotFound,
                msg = "Health community specialist with email equal to $email not found in our database"
            )

            else -> DefaultErrorResponse(
                code = "error_getting_health_community_specialist",
                message = "Error getting health community specialist"
            )
        }
    }

    private suspend fun getUserRoles(): List<String> {
        val roles = currentClaims()
        val userRoles = mutableListOf<String>()
        if (roles != null) {
            when (roles["roles"]) {
                is String -> userRoles.add(roles["roles"] as String)
                is List<*> -> userRoles.addAll((roles["roles"] as List<*>).map { it.toString() })
            }
        }

        return userRoles.toList()
    }

}
