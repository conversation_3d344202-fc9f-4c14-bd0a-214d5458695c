package br.com.alice.duquesa.services.internal

import br.com.alice.common.data.dsl.matchers.ResultAssert
import br.com.alice.common.featureflag.withFeatureFlags
import br.com.alice.common.helpers.coVerifyNone
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.common.kafka.interfaces.KafkaProducerService
import br.com.alice.common.kafka.interfaces.ProducerResult
import br.com.alice.common.models.Sex
import br.com.alice.common.redis.GenericCache
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.AdditionalInformation
import br.com.alice.data.layer.models.FeatureNamespace
import br.com.alice.data.layer.models.ScheduleAppointmentType.DOCTOR_FAMILY
import br.com.alice.data.layer.models.ScheduleAppointmentType.NURSE_FAMILY
import br.com.alice.data.layer.models.ScheduleType.PRESENTIAL
import br.com.alice.data.layer.models.ScheduleType.REMOTE
import br.com.alice.duquesa.EinsteinTestModelFactory
import br.com.alice.duquesa.EinsteinTestModelFactory.toEinsteinType
import br.com.alice.duquesa.client.CancelSlotEinstein
import br.com.alice.duquesa.client.ConfirmSlotEinstein
import br.com.alice.duquesa.client.EstablishmentRequest
import br.com.alice.duquesa.client.SlotsRequest
import br.com.alice.duquesa.clients.einstein.scheduling.EinsteinSchedulingAppointmentClient
import br.com.alice.duquesa.clients.einstein.scheduling.EinsteinSchedulingAuthClient
import br.com.alice.duquesa.clients.einstein.scheduling.EinsteinSchedulingProductsClient
import br.com.alice.duquesa.clients.einstein.scheduling.EinsteinSchedulingSlotsClient
import br.com.alice.duquesa.clients.einstein.scheduling.converters.toEinsteinAppointmentPatient
import br.com.alice.duquesa.clients.einstein.scheduling.converters.toEinsteinDayOfWeek
import br.com.alice.duquesa.clients.einstein.scheduling.converters.toEinsteinSlotDataParam
import br.com.alice.duquesa.clients.einstein.scheduling.converters.toModelClient
import br.com.alice.duquesa.clients.einstein.scheduling.model.CancelResponse
import br.com.alice.duquesa.clients.einstein.scheduling.model.EinsteinSlot
import br.com.alice.duquesa.clients.einstein.scheduling.model.ProductEligibilityRequest
import br.com.alice.duquesa.clients.einstein.scheduling.model.ProductEligibilityResponse
import br.com.alice.duquesa.clients.einstein.scheduling.model.ScheduleAppointment
import br.com.alice.duquesa.clients.einstein.scheduling.model.ScheduleAppointmentPayment
import br.com.alice.duquesa.clients.einstein.scheduling.model.ScheduleAppointmentRequest
import br.com.alice.duquesa.clients.einstein.scheduling.model.ScheduleAppointmentResponse
import br.com.alice.duquesa.clients.einstein.scheduling.model.SlotHoursAvailableResponse
import br.com.alice.duquesa.clients.einstein.scheduling.model.SlotsDatesAvailableResponse
import br.com.alice.duquesa.clients.einstein.scheduling.model.ValidateProductRulesResponse
import br.com.alice.duquesa.clients.einstein.scheduling.model.ValidateProductRulesResponseData
import br.com.alice.duquesa.event.EinsteinScheduleCancelEvent
import br.com.alice.duquesa.event.EinsteinScheduleRegisterEvent
import br.com.alice.duquesa.services.PersonHelperService
import com.github.kittinunf.result.success
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.mockk
import kotlinx.coroutines.runBlocking
import org.assertj.core.api.Assertions.assertThat
import java.time.LocalDate
import java.time.LocalDateTime
import kotlin.test.BeforeTest
import kotlin.test.Test

class EinsteinSchedulingServiceTest {
    private val authClient: EinsteinSchedulingAuthClient = mockk()
    private val productsClient: EinsteinSchedulingProductsClient = mockk()
    private val slotsClient: EinsteinSchedulingSlotsClient = mockk()
    private val appointmentClient: EinsteinSchedulingAppointmentClient = mockk()
    private val cache: GenericCache = mockk()
    private val kafka: KafkaProducerService = mockk()
    private val helperService: PersonHelperService = mockk()

    private val service = EinsteinSchedulingService(
        authClient,
        productsClient,
        slotsClient,
        appointmentClient,
        cache,
        kafka,
        helperService,
    )

    private val producerResult = ProducerResult(LocalDateTime.now(), "topic", 100)

    val token = EinsteinTestModelFactory.getAuthToken()

    private val unitRemote = EinsteinTestModelFactory.getUnit()
    private val unitPresential = EinsteinTestModelFactory.getUnit()

    private val coverageDocRemote = EinsteinTestModelFactory.getCoverage(itemAlias = "Doc. Teleconsulta", careType = REMOTE.toEinsteinType(), units = listOf(unitRemote))
    private val coverageDocPresential = EinsteinTestModelFactory.getCoverage(itemAlias = "Doc. presencial", careType = PRESENTIAL.toEinsteinType(), units = listOf(unitPresential))
    private val productDoc = EinsteinTestModelFactory.getProduct(id = "68597dce-265a-4782-a89f-31290bb07252", name = "Médico da familia", coverages = listOf(coverageDocRemote, coverageDocPresential))

    private val coverageNurseRemote = EinsteinTestModelFactory.getCoverage(itemAlias = "Enf. Teleconsulta", careType = REMOTE.toEinsteinType(), units = listOf(unitRemote))
    private val productNurse = EinsteinTestModelFactory.getProduct(id = "14d61412-d1ad-4e88-bb5e-f6f6d7e94d45", name = "Enf. da familia", coverages = listOf(coverageNurseRemote))

    private val listProductResponse = EinsteinTestModelFactory.getListProductsResponse(
        listOf(productDoc, productNurse)
    )

    val person = TestModelFactory.buildPerson(
        sex = Sex.MALE,
        firstName = "Darth Vadder",
        lastName = "",
    )

    @BeforeTest
    fun setup() {
        coEvery { cache.get(any<String>(), PersonCoverage::class, any(), any(), any()) } coAnswers {
            arg<suspend () -> PersonCoverage>(4).invoke()
        }

        coEvery { cache.invalidateKeys(any()) } returns 1
    }

    @Test
    fun `#getEstablishments should return doctor and presential establishments`() = runBlocking {
        val validateResponse = ValidateProductRulesResponse(ValidateProductRulesResponseData(productDoc))
        val expected = listOf(unitPresential.toModelClient())

        coEvery { authClient.auth() } returns token.success()
        coEvery { helperService.verifyShouldReplacePerson(person.id) } returns person
        coEvery { productsClient.listProducts(token) } returns listProductResponse.success()
        coEvery { productsClient.validateRules(match { it.product == productDoc }, token) } returns validateResponse.success()

        withFeatureFlags(
            FeatureNamespace.DUQUESA,
            mapOf(
                "einstein_id_product_nurse" to productNurse.id,
                "einstein_id_product_doctor" to productDoc.id,
                "einstein_id_plan_code" to "123"
            )
        ) {
            val result = service.getEstablishments(person.id, EstablishmentRequest(DOCTOR_FAMILY, PRESENTIAL))
            ResultAssert.assertThat(result).isSuccessWithData(expected)
        }

        coVerifyOnce {
            authClient.auth()
            productsClient.listProducts(any())
            productsClient.validateRules(any(), any())
        }
    }

    @Test
    fun `#getEstablishments should return doctor and remote establishments`() = runBlocking {
        val validateResponse = ValidateProductRulesResponse(ValidateProductRulesResponseData(productDoc))
        val expected = listOf(unitRemote.toModelClient())

        coEvery { authClient.auth() } returns token.success()
        coEvery { helperService.verifyShouldReplacePerson(person.id) } returns person
        coEvery { productsClient.listProducts(token) } returns listProductResponse.success()
        coEvery { productsClient.validateRules(match { it.product == productDoc }, token) } returns validateResponse.success()

        withFeatureFlags(
            FeatureNamespace.DUQUESA,
            mapOf(
                "einstein_id_product_nurse" to productNurse.id,
                "einstein_id_product_doctor" to productDoc.id,
                "einstein_id_plan_code" to "123"
            )
        ) {
            val result = service.getEstablishments(person.id, EstablishmentRequest(DOCTOR_FAMILY, REMOTE))
            ResultAssert.assertThat(result).isSuccessWithData(expected)
        }

        coVerifyOnce {
            authClient.auth()
            productsClient.listProducts(any())
            productsClient.validateRules(any(), any())
        }
    }

    @Test
    fun `#getEstablishments should return nurse and remote establishments`() = runBlocking {
        val validateResponse = ValidateProductRulesResponse(ValidateProductRulesResponseData(productDoc))
        val expected = listOf(unitRemote.toModelClient())

        coEvery { authClient.auth() } returns token.success()
        coEvery { helperService.verifyShouldReplacePerson(person.id) } returns person
        coEvery { productsClient.listProducts(token) } returns listProductResponse.success()
        coEvery { productsClient.validateRules(match { it.product == productNurse }, token) } returns validateResponse.success()

        withFeatureFlags(
            FeatureNamespace.DUQUESA,
            mapOf(
                "einstein_id_product_nurse" to productNurse.id,
                "einstein_id_product_doctor" to productDoc.id,
                "einstein_id_plan_code" to "123"
            )
        ) {
            val result = service.getEstablishments(person.id, EstablishmentRequest(NURSE_FAMILY, REMOTE))
            ResultAssert.assertThat(result).isSuccessWithData(expected)
        }

        coVerifyOnce {
            authClient.auth()
            productsClient.listProducts(any())
            productsClient.validateRules(any(), any())
        }
    }

    @Test
    fun `#getSlots should call client and return slots`() = runBlocking {
        val validateResponse = ValidateProductRulesResponse(ValidateProductRulesResponseData(productDoc))

        val request = SlotsRequest(
            startDate = LocalDateTime.now() ,
            endDate = LocalDateTime.now().plusDays(1),
            productType = REMOTE,
            appointmentType = DOCTOR_FAMILY,
        )

        val datesParams = request.toEinsteinSlotDataParam(coverageDocRemote, null)

        val datesResponse = SlotsDatesAvailableResponse(listOf(LocalDate.now(), LocalDate.now().plusDays(1)))
        val slotsResponse1 = SlotHoursAvailableResponse(
            listOf(EinsteinTestModelFactory.getSlot(datesResponse.slotDates[0]), EinsteinTestModelFactory.getSlot(datesResponse.slotDates[0]))
        )
        val slotsResponse2 = SlotHoursAvailableResponse(
            listOf(EinsteinTestModelFactory.getSlot(datesResponse.slotDates[1]), EinsteinTestModelFactory.getSlot(datesResponse.slotDates[1]))
        )

        val expected = (slotsResponse1.availableSchedules!! + slotsResponse2.availableSchedules!!).map { it.toSlot(request.productType, request.appointmentType, null) }

        coEvery { authClient.auth() } returns token.success()
        coEvery { helperService.verifyShouldReplacePerson(person.id) } returns person
        coEvery { productsClient.listProducts(token) } returns listProductResponse.success()
        coEvery { productsClient.validateRules(match { it.product == productDoc }, token) } returns validateResponse.success()
        coEvery { slotsClient.datesAvailable(datesParams, token) } returns datesResponse.success()
        coEvery { slotsClient.hoursAvailable(datesParams.toHoursParam(datesResponse.slotDates[0], listOf(unitRemote.idSGH)), token) } returns slotsResponse1.success()
        coEvery { slotsClient.hoursAvailable(datesParams.toHoursParam(datesResponse.slotDates[1], listOf(unitRemote.idSGH)), token) } returns slotsResponse2.success()

        withFeatureFlags(
            FeatureNamespace.DUQUESA,
            mapOf(
                "einstein_id_product_nurse" to productNurse.id,
                "einstein_id_product_doctor" to productDoc.id,
                "einstein_id_plan_code" to "123"
            )
        ) {
            val result = service.getSlotsAvailable(person.id, request)
            ResultAssert.assertThat(result).isSuccessWithData(expected)
        }

        coVerifyOnce {
            authClient.auth()
            productsClient.listProducts(any())
            productsClient.validateRules(any(), any())
            slotsClient.datesAvailable(any(), any())
        }
        coVerify(exactly = datesResponse.slotDates.size ) { slotsClient.hoursAvailable(any(), any()) }
    }

    @Test
    fun `#getSlots should verify other resourceId when there is no slot available`() = runBlocking {
        val validateResponse = ValidateProductRulesResponse(ValidateProductRulesResponseData(productDoc))

        val request = SlotsRequest(
            startDate = LocalDateTime.now() ,
            endDate = LocalDateTime.now().plusDays(1),
            productType = REMOTE,
            appointmentType = DOCTOR_FAMILY,
        )

        val datesParams = request.toEinsteinSlotDataParam(coverageDocRemote, 1L)

        val datesResponse = SlotsDatesAvailableResponse(listOf(LocalDate.now(), LocalDate.now().plusDays(1)))
        val datesResponseEmpty = SlotsDatesAvailableResponse(emptyList())
        val slotsResponse1 = SlotHoursAvailableResponse(
            listOf(EinsteinTestModelFactory.getSlot(datesResponse.slotDates[0]), EinsteinTestModelFactory.getSlot(datesResponse.slotDates[0]))
        )
        val slotsResponse2 = SlotHoursAvailableResponse(
            listOf(EinsteinTestModelFactory.getSlot(datesResponse.slotDates[1]), EinsteinTestModelFactory.getSlot(datesResponse.slotDates[1]))
        )

        val expected = (slotsResponse1.availableSchedules!! + slotsResponse2.availableSchedules!!).map { it.toSlot(request.productType, request.appointmentType, null) }

        coEvery { authClient.auth() } returns token.success()
        coEvery { helperService.verifyShouldReplacePerson(person.id) } returns person
        coEvery { productsClient.listProducts(token) } returns listProductResponse.success()
        coEvery { productsClient.validateRules(match { it.product == productDoc }, token) } returns validateResponse.success()
        coEvery { slotsClient.datesAvailable(datesParams, token) } returns datesResponseEmpty.success()
        coEvery { slotsClient.datesAvailable(datesParams.copy(resourceId = ""), token) } returns datesResponse.success()
        coEvery { slotsClient.hoursAvailable(datesParams.copy(resourceId = "").toHoursParam(datesResponse.slotDates[0], listOf(unitRemote.idSGH)), token) } returns slotsResponse1.success()
        coEvery { slotsClient.hoursAvailable(datesParams.copy(resourceId = "").toHoursParam(datesResponse.slotDates[1], listOf(unitRemote.idSGH)), token) } returns slotsResponse2.success()

        withFeatureFlags(
            FeatureNamespace.DUQUESA,
            mapOf(
                "einstein_id_product_nurse" to productNurse.id,
                "einstein_id_product_doctor" to productDoc.id,
                "einstein_id_plan_code" to "123"
            )
        ) {
            val result = service.getSlotsAvailable(person.id, request)
            ResultAssert.assertThat(result).isSuccessWithData(expected)
        }

        coVerifyOnce {
            authClient.auth()
            productsClient.listProducts(any())
            productsClient.validateRules(any(), any())
            slotsClient.datesAvailable(any(), any())
        }
        coVerify(exactly = datesResponse.slotDates.size ) { slotsClient.hoursAvailable(any(), any()) }
    }

    @Test
    fun `#schedule should schedule appointment`() = runBlocking {
        val payload = ConfirmSlotEinstein(
            serviceId = 1,
            unitId = unitRemote.idSGH,
            date = LocalDateTime.now(),
            id = "234234||234234",
            localId = 1232,
            scheduleType = REMOTE,
            appointmentType = DOCTOR_FAMILY,
            professionalCouncil = "123123CRMRS",
            professionalId = 12312,
            professionalName = "Joe Doe",
        )

        val elegibilityRequest = ProductEligibilityRequest(
            document = person.nationalId,
            itemId = productDoc.id
        )

        val eligibilityResponse = ProductEligibilityResponse(
            insuranceId = 1,
            insuranceCode = "123",
            insurance = "Alice Saude",
            customerId = 2,
            planCode = "123",
            plan = "123123",
            officeCode = "1111",
            office = "222",
            name = "Produto",
        )
        val patient = person.toEinsteinAppointmentPatient(AdditionalInformation(false))
        val validateResponse = ValidateProductRulesResponse(ValidateProductRulesResponseData(productDoc))
        val scheduleRequest = ScheduleAppointmentRequest(
            patient = patient,
            schedules = listOf(
                ScheduleAppointment(
                    careType = coverageDocRemote.careType,
                    catalogItemId = coverageDocRemote.id,
                    itemId = coverageDocRemote.itemSGHId,
                    productId = productDoc.id,
                    productName = productDoc.name,
                    scheduleType = coverageDocRemote.category,
                    payment = ScheduleAppointmentPayment(
                        healthInsuranceId = eligibilityResponse.insuranceId.toString(),
                        planCode = eligibilityResponse.planCode,
                        name = eligibilityResponse.name
                    ),
                    selectedSlotOptions = listOf(
                        EinsteinSlot(
                            scheduleId = payload.id,
                            date = payload.date.toLocalDate(),
                            hour = payload.date.toLocalTime(),
                            weekDay = payload.date.toEinsteinDayOfWeek(),
                            employeeId = payload.professionalId,
                            resourceId = payload.professionalCouncil,
                            resource = payload.professionalName,
                            localId = payload.localId,
                            serviceId = payload.serviceId,
                        )
                    ),
                    unit = unitRemote.toScheduleRequest()
                )
            )
        )

        coEvery { authClient.auth() } returns token.success()
        coEvery { helperService.verifyShouldReplacePerson(person.id) } returns person
        coEvery { productsClient.listProducts(token) } returns listProductResponse.success()
        coEvery { productsClient.validateRules(match { it.product == productDoc }, token) } returns validateResponse.success()
        coEvery { productsClient.verifyEligibility(elegibilityRequest, token) } returns arrayOf(eligibilityResponse).success()
        coEvery { appointmentClient.schedule(scheduleRequest, token) } returns ScheduleAppointmentResponse("123||123||1","ZXC-321").success()
        coEvery { kafka.produce(any<EinsteinScheduleRegisterEvent>()) } returns producerResult
        assertThat(patient.firstName).isEqualTo("Darth")
        assertThat(patient.lastName).isEqualTo("Vadder")

        withFeatureFlags(
            FeatureNamespace.DUQUESA,
            mapOf(
                "einstein_id_product_nurse" to productNurse.id,
                "einstein_id_product_doctor" to productDoc.id,
                "einstein_id_plan_code" to "123"
            )
        ) {
            val result = service.schedule(person.id, payload)
            ResultAssert.assertThat(result).isSuccessWithData("ZXC-321")
        }

        coVerifyOnce {
            authClient.auth()
            productsClient.listProducts(any())
            productsClient.validateRules(any(), any())
            kafka.produce(any<EinsteinScheduleRegisterEvent>())
        }
    }

    @Test
    fun `#cancel should call client and cancel slot with national id`() = runBlocking {
        val data = CancelSlotEinstein("", "")

        coEvery { authClient.auth() } returns token.success()
        coEvery { helperService.verifyShouldReplacePerson(person.id) } returns person
        coEvery { appointmentClient.cancelWithNationalId(person.nationalId, data.id, token) } returns CancelResponse("cancelado").success()
        coEvery { kafka.produce(any<EinsteinScheduleCancelEvent>()) } returns producerResult

        val result = service.cancelSchedule(person.id, data)
        ResultAssert.assertThat(result).isSuccessWithData(true)

        coVerifyOnce {
            authClient.auth()
            appointmentClient.cancelWithNationalId(any(), any(), any())
            kafka.produce(any<EinsteinScheduleCancelEvent>())
        }

        coVerifyNone {
            appointmentClient.cancelWithVerificationCode(any(), any(), any())
        }
    }

    @Test
    fun `#cancel should call client and cancel slot with verification CODE`() = runBlocking {
        val data = CancelSlotEinstein("123", "ABC-123")

        coEvery { authClient.auth() } returns token.success()
        coEvery { helperService.verifyShouldReplacePerson(person.id) } returns person
        coEvery { appointmentClient.cancelWithVerificationCode(data.verificationCode!!, data.id, token) } returns CancelResponse("cancelado").success()
        coEvery { kafka.produce(any<EinsteinScheduleCancelEvent>()) } returns producerResult

        val result = service.cancelSchedule(person.id, data)
        ResultAssert.assertThat(result).isSuccessWithData(true)

        coVerifyOnce {
            authClient.auth()
            appointmentClient.cancelWithVerificationCode(any(), any(), any())
            kafka.produce(any<EinsteinScheduleCancelEvent>())
        }

        coVerifyNone {
            appointmentClient.cancelWithNationalId(any(), any(), any())
        }
    }

    @Test
    fun `#getEstablishments should return doctor and presential establishments when isPregnant is true`() = runBlocking {
        val validateResponse = ValidateProductRulesResponse(ValidateProductRulesResponseData(productDoc))
        val expected = listOf(unitPresential.toModelClient())

        coEvery { authClient.auth() } returns token.success()
        coEvery { helperService.verifyShouldReplacePerson(person.id) } returns person
        coEvery { productsClient.listProducts(token) } returns listProductResponse.success()
        coEvery { productsClient.validateRules(match { it.product == productDoc }, token) } returns validateResponse.success()

        withFeatureFlags(
            FeatureNamespace.DUQUESA,
            mapOf(
                "einstein_id_product_nurse" to productNurse.id,
                "einstein_id_product_doctor" to productDoc.id,
                "einstein_id_plan_code" to "123"
            )
        ) {
            val result = service.getEstablishments(person.id, EstablishmentRequest(DOCTOR_FAMILY, PRESENTIAL))
            ResultAssert.assertThat(result).isSuccessWithData(expected)
        }

        coVerifyOnce {
            authClient.auth()
            productsClient.listProducts(any())
            productsClient.validateRules(any(), any())
        }
    }

    @Test
    fun `#getEstablishments should return doctor and remote establishments when isPregnant is true`() = runBlocking {
        val validateResponse = ValidateProductRulesResponse(ValidateProductRulesResponseData(productDoc))
        val expected = listOf(unitRemote.toModelClient())

        coEvery { authClient.auth() } returns token.success()
        coEvery { helperService.verifyShouldReplacePerson(person.id) } returns person
        coEvery { productsClient.listProducts(token) } returns listProductResponse.success()
        coEvery { productsClient.validateRules(match { it.product == productDoc }, token) } returns validateResponse.success()

        withFeatureFlags(
            FeatureNamespace.DUQUESA,
            mapOf(
                "einstein_id_product_nurse" to productNurse.id,
                "einstein_id_product_doctor" to productDoc.id,
                "einstein_id_plan_code" to "123"
            )
        ) {
            val result = service.getEstablishments(person.id, EstablishmentRequest(DOCTOR_FAMILY, REMOTE))
            ResultAssert.assertThat(result).isSuccessWithData(expected)
        }

        coVerifyOnce {
            authClient.auth()
            productsClient.listProducts(any())
            productsClient.validateRules(any(), any())
        }
    }

    @Test
    fun `#getEstablishments should return nurse and remote establishments when isPregnant is true`() = runBlocking {
        val validateResponse = ValidateProductRulesResponse(ValidateProductRulesResponseData(productDoc))
        val expected = listOf(unitRemote.toModelClient())

        coEvery { authClient.auth() } returns token.success()
        coEvery { helperService.verifyShouldReplacePerson(person.id) } returns person
        coEvery { productsClient.listProducts(token) } returns listProductResponse.success()
        coEvery { productsClient.validateRules(match { it.product == productNurse }, token) } returns validateResponse.success()

        withFeatureFlags(
            FeatureNamespace.DUQUESA,
            mapOf(
                "einstein_id_product_nurse" to productNurse.id,
                "einstein_id_product_doctor" to productDoc.id,
                "einstein_id_plan_code" to "123"
            )
        ) {
            val result = service.getEstablishments(person.id, EstablishmentRequest(NURSE_FAMILY, REMOTE))
            ResultAssert.assertThat(result).isSuccessWithData(expected)
        }

        coVerifyOnce {
            authClient.auth()
            productsClient.listProducts(any())
            productsClient.validateRules(any(), any())
        }
    }
}
