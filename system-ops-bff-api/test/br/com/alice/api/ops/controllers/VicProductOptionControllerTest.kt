package br.com.alice.api.ops.controllers

import br.com.alice.api.ops.ControllerTestHelper
import br.com.alice.api.ops.converters.VicProductOptionResponseConverter
import br.com.alice.api.ops.models.MainProviderRequest
import br.com.alice.api.ops.models.VicProductOptionRequest
import br.com.alice.api.ops.models.VicProductOptionResponse
import br.com.alice.atlas.services.SiteAccreditedNetworkService
import br.com.alice.bottini.client.VicProductOptionService
import br.com.alice.common.RangeUUID
import br.com.alice.common.data.dsl.matchers.ResponseAssert.Companion.assertThat
import br.com.alice.common.helpers.bodyAsJson
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.product.client.ProductService
import com.github.kittinunf.result.success
import io.mockk.clearAllMocks
import io.mockk.coEvery
import io.mockk.mockk
import org.assertj.core.api.Assertions.assertThat
import kotlin.test.BeforeTest
import kotlin.test.Test

class VicProductOptionControllerTest : ControllerTestHelper() {
    private val vicProductOptionService: VicProductOptionService = mockk()
    private val siteAccreditedNetworkService: SiteAccreditedNetworkService = mockk()
    private val productService: ProductService = mockk()

    private val controller = VicProductOptionController(
        vicProductOptionService,
        siteAccreditedNetworkService,
        productService,
    )

    private val product = TestModelFactory.buildProduct(
        displayName = "Conforto +",
    )
    private val vicProductOption = TestModelFactory.buildVicProductOption(
        productName = "conforto",
        productId = product.id,
        siteAccreditedNetworkId = RangeUUID.generate()
    )
    private val expectedResponse = VicProductOptionResponseConverter.convert(
        vicProductOption,
    )
    private val siteAccreditedNetwork = TestModelFactory.buildSiteAccreditedNetwork(
        id = vicProductOption.siteAccreditedNetworkId,
    )

    @BeforeTest
    override fun setup() {
        clearAllMocks()
        super.setup()

        module.single { controller }
    }

    @Test
    fun `#index should list VicProductOption by productName`() {
        coEvery {
            vicProductOptionService.findProductsPaginated(
                IntRange(0, 49),
                vicProductOption.productName,
            )
        } returns listOf(vicProductOption).success()

        coEvery {
            vicProductOptionService.countAll()
        } returns 1.success()

        coEvery {
            siteAccreditedNetworkService.get(vicProductOption.siteAccreditedNetworkId)
        } returns siteAccreditedNetwork.success()

        authenticatedAs(idToken, staff) {
            get("/vic_product_option?filter={\"productName\":\"${vicProductOption.productName}\"}&range=[0,49]") { response ->
                assertThat(response).isSuccessfulJson()

                val content: List<VicProductOptionResponse> = response.bodyAsJson()
                assertThat(content.size).isEqualTo(1)
                val responseIds = content.map { it.id }
                assertThat(responseIds).contains(vicProductOption.id)
            }
        }
    }

    @Test
    fun `#index should list VicProductOption by productCategory`() {
        coEvery {
            vicProductOptionService.findProductsPaginated(
                range = IntRange(0, 49),
                category = vicProductOption.productCategory,
            )
        } returns listOf(vicProductOption).success()

        coEvery {
            vicProductOptionService.countAll()
        } returns 1.success()

        coEvery {
            siteAccreditedNetworkService.get(vicProductOption.siteAccreditedNetworkId)
        } returns siteAccreditedNetwork.success()

        authenticatedAs(idToken, staff) {
            get("/vic_product_option?filter={\"productCategory\":\"${vicProductOption.productCategory}\"}&range=[0,49]") { response ->
                assertThat(response).isSuccessfulJson()

                val content: List<VicProductOptionResponse> = response.bodyAsJson()
                assertThat(content.size).isEqualTo(1)
                val responseIds = content.map { it.id }
                assertThat(responseIds).contains(vicProductOption.id)
            }
        }
    }

    @Test
    fun `#index should list VicProductOption by range`() {
        coEvery {
            vicProductOptionService.findProductsPaginated(IntRange(0, 49))
        } returns listOf(vicProductOption).success()

        coEvery {
            vicProductOptionService.countAll()
        } returns 1.success()

        coEvery {
            siteAccreditedNetworkService.get(vicProductOption.siteAccreditedNetworkId)
        } returns siteAccreditedNetwork.success()

        authenticatedAs(idToken, staff) {
            get("/vic_product_option?filter={}&range=[0,49]") { response ->
                assertThat(response).isSuccessfulJson()

                val content: List<VicProductOptionResponse> = response.bodyAsJson()
                assertThat(content.size).isEqualTo(1)
                val responseIds = content.map { it.id }
                assertThat(responseIds).contains(vicProductOption.id)
            }
        }
    }

    @Test
    fun `#getById should get VicProductOption`() {
        coEvery {
            vicProductOptionService.get(vicProductOption.id)
        } returns vicProductOption.success()

        coEvery {
            siteAccreditedNetworkService.get(vicProductOption.siteAccreditedNetworkId)
        } returns siteAccreditedNetwork.success()

        authenticatedAs(idToken, staff) {
            get("/vic_product_option/${vicProductOption.id}") { response ->
                assertThat(response).isSuccessfulJson()

                val content: VicProductOptionResponse = response.bodyAsJson()
                assertThat(content).isEqualTo(expectedResponse)
            }
        }
    }

    @Test
    fun `#create should create VicProductOption`() {
        val request = VicProductOptionRequest(
            productId = product.id,
            productCategory = vicProductOption.productCategory.name,
            accommodation = vicProductOption.accommodation.name,
            coverage = vicProductOption.coverage.name,
            siteAccreditedNetworkId = vicProductOption.siteAccreditedNetworkId,
            coPayment = vicProductOption.coPayment,
            hospitals = vicProductOption.mainHospitals.map {
                MainProviderRequest(
                    id = it.id,
                    name = it.name,
                    icon = it.icon
                )
            },
            type = vicProductOption.type.toString(),
            laboratories = vicProductOption.mainLaboratories.map {
                MainProviderRequest(
                    id = it.id,
                    name = it.name,
                    icon = it.icon
                )
            }
        )

        coEvery {
            productService.getActive(product.id)
        } returns product.success()

        coEvery {
            vicProductOptionService.create(match {
                it.productId == product.id &&
                        it.productName == product.salesProductName &&
                        it.productCategory.name == vicProductOption.productCategory.name &&
                        it.coverage.name == vicProductOption.coverage.name &&
                        it.type!!.name == vicProductOption.type!!.name &&
                        it.coPayment == vicProductOption.coPayment &&
                        it.accommodation.name == vicProductOption.accommodation.name &&
                        it.siteAccreditedNetworkId == vicProductOption.siteAccreditedNetworkId &&
                        it.mainHospitals.map { it.id } == vicProductOption.mainHospitals.map { it.id } &&
                        it.mainLaboratories.map { it.id } == vicProductOption.mainLaboratories.map { it.id }
            })
        } returns vicProductOption.success()

        coEvery {
            siteAccreditedNetworkService.get(vicProductOption.siteAccreditedNetworkId)
        } returns siteAccreditedNetwork.success()

        authenticatedAs(idToken, staff) {
            post(to = "/vic_product_option", body = request) { response ->
                assertThat(response).isSuccessfulJson()

                val content: VicProductOptionResponse = response.bodyAsJson()
                assertThat(content).isEqualTo(expectedResponse)
            }
        }
    }

    @Test
    fun `#update should update VicProductOption`() {
        val request = VicProductOptionRequest(
            productId = product.id,
            productCategory = vicProductOption.productCategory.name,
            accommodation = vicProductOption.accommodation.name,
            coverage = vicProductOption.coverage.name,
            siteAccreditedNetworkId = vicProductOption.siteAccreditedNetworkId,
            coPayment = vicProductOption.coPayment,
            hospitals = vicProductOption.mainHospitals.map {
                MainProviderRequest(
                    id = it.id,
                    name = it.name,
                    icon = it.icon
                )
            },
            type = vicProductOption.type.toString(),
            laboratories = vicProductOption.mainLaboratories.map {
                MainProviderRequest(
                    id = it.id,
                    name = it.name,
                    icon = it.icon
                )
            }
        )

        coEvery {
            productService.getActive(vicProductOption.productId)
        } returns product.success()

        coEvery {
            vicProductOptionService.get(vicProductOption.id)
        } returns vicProductOption.success()

        coEvery {
            vicProductOptionService.update(match {
                it.id == vicProductOption.id &&
                it.productId == product.id &&
                it.productName == product.salesProductName &&
                it.productCategory.name == vicProductOption.productCategory.name &&
                it.coverage.name == vicProductOption.coverage.name &&
                it.type!!.name == vicProductOption.type!!.name &&
                it.coPayment == vicProductOption.coPayment &&
                it.accommodation.name == vicProductOption.accommodation.name &&
                it.siteAccreditedNetworkId == vicProductOption.siteAccreditedNetworkId &&
                it.mainHospitals.map { it.id } == vicProductOption.mainHospitals.map { it.id } &&
                it.mainLaboratories.map { it.id } == vicProductOption.mainLaboratories.map { it.id }

            })
        } returns vicProductOption.success()

        coEvery {
            siteAccreditedNetworkService.get(vicProductOption.siteAccreditedNetworkId)
        } returns siteAccreditedNetwork.success()

        authenticatedAs(idToken, staff) {
            put(to = "/vic_product_option/${vicProductOption.id}", body = request) { response ->
                assertThat(response).isSuccessfulJson()

                val content: VicProductOptionResponse = response.bodyAsJson()
                assertThat(content).isEqualTo(expectedResponse)
            }
        }
    }
}
