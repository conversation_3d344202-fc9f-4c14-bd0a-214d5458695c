package br.com.alice.hr.core.services.useCases

import br.com.alice.common.core.extensions.capitalizeEachWord
import br.com.alice.common.core.extensions.isAfterEq
import br.com.alice.common.core.extensions.isEmail
import br.com.alice.common.core.extensions.isValidBrazilianNationalId
import br.com.alice.common.core.extensions.isValidCnpj
import br.com.alice.common.core.extensions.notContains
import br.com.alice.common.core.extensions.onlyNumbers
import br.com.alice.common.core.extensions.toLocalDate
import br.com.alice.common.observability.Spannable
import br.com.alice.data.layer.models.ParentBeneficiaryRelationType
import br.com.alice.hr.core.model.BeneficiaryBatchItemTransport
import br.com.alice.hr.core.model.BeneficiaryBatchTransport
import br.com.alice.hr.core.model.BeneficiaryBatchValidation
import br.com.alice.hr.core.model.BeneficiaryBatchValidationError
import br.com.alice.hr.core.model.BeneficiaryBatchValidationErrorItem
import br.com.alice.hr.core.util.RuleAndMessage
import br.com.alice.hr.core.util.ValidationCollector
import com.github.kittinunf.result.Result
import com.github.kittinunf.result.success
import java.text.Normalizer
import java.time.LocalDate
import java.time.format.DateTimeFormatter
import java.time.format.DateTimeParseException
import java.util.Locale
import java.util.regex.Pattern

class StaticValidateBeneficiariesUseCase: Spannable {
    companion object {
        private val KEEP_ONLY_LETTER_REGEX = "[^a-zA-Z áéíóúàèìòùâêîôûãõçÁÉÍÓÚÀÈÌÒÙÂÊÎÔÛÃÕÇ]".toRegex()
        private const val DEPENDENT = "Dependente"
        private const val HOLDER = "Titular"
        private val YES_OR_NO = listOf("Sim", "Nao")
        private val BIOLOGICAL_SEX_OPTIONS = listOf("F", "M")
        private const val CLT = "CLT"
        private const val PJ = "PJ"
        private val BENEFICIARIES_CONTRACT_TYPES = listOf(CLT, PJ)
        private val PARENT_RELATIONSHIP_TYPE = listOf(
            ParentBeneficiaryRelationType.CHILD.description,
            ParentBeneficiaryRelationType.FOSTER_CHILD.description,
            ParentBeneficiaryRelationType.SPOUSE.description,
            ParentBeneficiaryRelationType.PARTNER.description,
            ParentBeneficiaryRelationType.STEPCHILD.description,
        )
        private const val FULL_NAME_IS_PRESENT = "FULL_NAME_IS_PRESENT"
        private const val FULL_NAME_IS_VALID = "FULL_NAME_IS_VALID"
        private const val FULL_NAME_IS_REALLY_FULL = "FULL_NAME_IS_REALLY_FULL"
        private const val NATIONAL_ID_IS_VALID = "NATIONAL_ID_IS_VALID"
        private const val NATIONAL_ID_IS_PRESENT = "NATIONAL_ID_IS_PRESENT"
        private const val HOLDER_NATIONAL_ID_IS_VALID = "HOLDER_NATIONAL_ID_IS_VALID"
        private const val HOLDER_NATIONAL_ID_IS_PRESENT = "HOLDER_NATIONAL_ID_IS_PRESENT"
        private const val SEX_IS_PRESENT = "SEX_IS_PRESENT"
        private const val SEX_IS_VALID = "SEX_IS_VALID"
        private const val MOTHERS_NAME_IS_PRESENT = "MOTHERS_NAME_IS_PRESENT"
        private const val MOTHERS_NAME_HAS_AT_LEAST_5_LETTERS = "MOTHERS_NAME_HAS_AT_LEAST_5_LETTERS"
        private const val MOTHERS_NAME_IS_FULL_NAME = "MOTHERS_NAME_IS_FULL_NAME"
        private const val PHONE_NUMBER_IS_PRESENT = "PHONE_NUMBER_IS_PRESENT"
        private const val PHONE_NUMBER_HAS_CORRECT_SIZE = "PHONE_NUMBER_HAS_CORRECT_SIZE"
        private const val ADDRESS_POSTAL_CODE_IS_PRESENT = "ADDRESS_POSTAL_CODE_IS_PRESENT"
        private const val ADDRESS_POSTAL_CODE_HAS_CORRECT_SIZE = "ADDRESS_POSTAL_CODE_HAS_CORRECT_SIZE"
        private const val ADDRESS_NUMBER_IS_PRESENT = "ADDRESS_NUMBER_IS_PRESENT"
        private const val PRODUCT_TITLE_IS_PRESENT = "ADDRESS_NUMBER_IS_VALID"
        private const val BIRTH_DATE_IS_PRESENT = "BIRTH_DATE_IS_PRESENT"
        private const val OWNERSHIP_IS_PRESENT = "OWNERSHIP_IS_PRESENT"
        private const val OWNERSHIP_IS_VALID = "OWNERSHIP_IS_VALID"
        private const val EMAIL_IS_PRESENT = "EMAIL_IS_PRESENT"
        private const val EMAIL_IS_VALID = "EMAIL_IS_VALID"
        private const val ACTIVATED_AT_IS_PRESENT = "ACTIVATED_AT_IS_PRESENT"
        private val reusableRules: Map<String, RuleAndMessage<String?>> = mapOf(
            FULL_NAME_IS_PRESENT to RuleAndMessage({ it == null }, "Nome completo obrigatório."),
            FULL_NAME_IS_VALID to RuleAndMessage({ it!!.length < 5 }, "Nome completo deve ter ao menos 5 caracteres."),
            FULL_NAME_IS_REALLY_FULL to RuleAndMessage({ it!!.split(" ").size < 2 }, "Insira o nome e sobrenome do beneficiário."),
            NATIONAL_ID_IS_PRESENT to RuleAndMessage({ it == null }, "CPF obrigatório."),
            NATIONAL_ID_IS_VALID to RuleAndMessage({ !it!!.isValidBrazilianNationalId() }, "CPF inválido."),
            HOLDER_NATIONAL_ID_IS_PRESENT to RuleAndMessage({ it == null }, "CPF do titular obrigatório."),
            HOLDER_NATIONAL_ID_IS_VALID to RuleAndMessage({ !it!!.isValidBrazilianNationalId() }, "CPF do titular inválido."),
            SEX_IS_PRESENT to RuleAndMessage({ it == null }, "Sexo biológico obrigatório."),
            SEX_IS_VALID to RuleAndMessage({ BIOLOGICAL_SEX_OPTIONS.notContains(it) }, "Sexo biológico inválido, selecione uma das opções da lista."),
            MOTHERS_NAME_IS_PRESENT to RuleAndMessage({ it == null }, "Nome completo da mãe obrigatório."),
            MOTHERS_NAME_HAS_AT_LEAST_5_LETTERS to RuleAndMessage({ it!!.length < 5 }, "Insira o nome e sobrenome da mãe."),
            MOTHERS_NAME_IS_FULL_NAME to RuleAndMessage({ it!!.split(" ").size < 2 }, "Insira o nome e sobrenome da mãe."),
            PHONE_NUMBER_IS_PRESENT to RuleAndMessage({ it == null }, "Telefone obrigatório."),
            PHONE_NUMBER_HAS_CORRECT_SIZE to RuleAndMessage({ listOf(10, 11).notContains(it!!.length) }, "Telefone inválido."),
            ADDRESS_POSTAL_CODE_IS_PRESENT to RuleAndMessage({ it == null }, "CEP obrigatório."),
            ADDRESS_POSTAL_CODE_HAS_CORRECT_SIZE to RuleAndMessage({ it!!.length != 8 }, "CEP inválido."),
            ADDRESS_NUMBER_IS_PRESENT to RuleAndMessage({ it.isNullOrBlank() }, "Número do logradouro inválido."),
            PRODUCT_TITLE_IS_PRESENT to RuleAndMessage({ it == null }, "Produto obrigatório."),
            BIRTH_DATE_IS_PRESENT to RuleAndMessage({ it.isNullOrBlank() }, "Data de nascimento obrigatória."),
            OWNERSHIP_IS_PRESENT to RuleAndMessage({ it.isNullOrBlank() }, "Titulariedade obrigatória."),
            OWNERSHIP_IS_VALID to RuleAndMessage({ it !in listOf(HOLDER, DEPENDENT) }, "Titulariedade inválida, escreva somente Titular ou Dependente."),
            EMAIL_IS_PRESENT to RuleAndMessage({ it == null }, "E-mail obrigatório."),
            EMAIL_IS_VALID to RuleAndMessage({ !it!!.isEmail() }, "E-mail inválido."),
            ACTIVATED_AT_IS_PRESENT to RuleAndMessage({ it == null }, "Data de ativação obrigatória."),
        )
    }

    suspend fun run(beneficiaryBatch: BeneficiaryBatchTransport): Result<BeneficiaryBatchValidation, Throwable> = span("ValidateBeneficiaries") {
        if (beneficiaryBatch.items.isEmpty()) {
            return@span BeneficiaryBatchValidation().success()
        }

        val errors = mutableListOf<BeneficiaryBatchValidationError>()
        val success = mutableListOf<Int>()

        val duplicated = validateHasDuplicatedBeneficiary(beneficiaryBatch.items)
        beneficiaryBatch.items
            .filter { it.index !in (duplicated?.map { it.index } ?: emptyList()) }
            .forEach { b ->
                val basicValidation = basicValidation(b)
                if (basicValidation != null) {
                    errors.add(basicValidation)
                    return@forEach
                }

                val ownership = b.ownership?.capitalizeEachWord()?.removeAccents()
                val validationHolder = if (ownership == HOLDER) {
                    validateHolder(b)
                } else null

                val validationDependent = if (ownership == DEPENDENT) {
                    validateDependent(b)
                } else null

                if (validationDependent != null) {
                    errors.add(validationDependent)
                } else if (validationHolder != null) {
                    errors.add(validationHolder)
                } else {
                    success.add(b.index)
                }
            }

        return@span BeneficiaryBatchValidation(
            success = success,
            errors = errors + (duplicated ?: emptyList()),
        ).success()
    }

    private fun validateHasDuplicatedBeneficiary(items: List<BeneficiaryBatchItemTransport>): List<BeneficiaryBatchValidationError>? =
        items.groupBy { it.nationalId }
            .filter { it.value.size > 1 }
            .map { it.value.map { item -> item.index } }
            .flatten()
            .let { l ->
                l.map {
                    BeneficiaryBatchValidationError(
                        index = it,
                        error = listOf(
                            BeneficiaryBatchValidationErrorItem(
                                field = "nationalId",
                                message = "CPF duplicado na planilha."
                            )
                        )
                    )
                }
            }

    private fun basicValidation(beneficiary: BeneficiaryBatchItemTransport): BeneficiaryBatchValidationError? {
        val validator = ValidationCollector(beneficiary.index)
        val minimumDateOfBirth = LocalDate.of(1905, 1, 1)
        val now = LocalDate.now()

        val ownership = beneficiary.ownership?.capitalizeEachWord()?.removeAccents()
        validator.check(
            ownership,
            "ownership",
            listOf(
                reusableRules.getValue(OWNERSHIP_IS_PRESENT),
                reusableRules.getValue(OWNERSHIP_IS_VALID),
            )
        )

        val fullName = beneficiary.fullName?.keepOnlyLetters()?.capitalizeEachWord()
        validator.check(
            fullName,
            "fullName",
            listOf(
                reusableRules.getValue(FULL_NAME_IS_PRESENT),
                reusableRules.getValue(FULL_NAME_IS_VALID),
                reusableRules.getValue(FULL_NAME_IS_REALLY_FULL),
                RuleAndMessage({ it!!.hasRepeatedWords() }, "Insira o nome e sobrenome do beneficiário."),
            )
        )

        val nationalId = beneficiary.nationalId?.onlyNumbers()
        validator.check(
            nationalId,
            "nationalId",
            listOf(
                reusableRules.getValue(NATIONAL_ID_IS_PRESENT),
                reusableRules.getValue(NATIONAL_ID_IS_VALID),
            )
        )

        val sex = beneficiary.sex?.keepOnlyLetters()
        validator.check(
            sex,
            "sex",
            listOf(
                reusableRules.getValue(SEX_IS_PRESENT),
                reusableRules.getValue(SEX_IS_VALID),
            )
        )

        val mothersName = beneficiary.mothersName?.keepOnlyLetters()
        validator.check(
            mothersName,
            "mothersName",
            listOf(
                reusableRules.getValue(MOTHERS_NAME_IS_PRESENT),
                reusableRules.getValue(MOTHERS_NAME_HAS_AT_LEAST_5_LETTERS),
                reusableRules.getValue(MOTHERS_NAME_IS_FULL_NAME),
            )
        )

        val phoneNumber = beneficiary.phoneNumber?.onlyNumbers()
        validator.check(
            phoneNumber,
            "phoneNumber",
            listOf(
                reusableRules.getValue(PHONE_NUMBER_IS_PRESENT),
                reusableRules.getValue(PHONE_NUMBER_HAS_CORRECT_SIZE),
                RuleAndMessage({ it!!.hasAllSameDigits() }, "Telefone inválido."),
            )
        )

        val addressPostalCode = beneficiary.addressPostalCode?.onlyNumbers()
        validator.check(
            addressPostalCode,
            "addressPostalCode",
            listOf(
                reusableRules.getValue(ADDRESS_POSTAL_CODE_IS_PRESENT),
                reusableRules.getValue(ADDRESS_POSTAL_CODE_HAS_CORRECT_SIZE),
                RuleAndMessage({ it!!.hasAllSameDigits() }, "CEP inválido."),
            )
        )

        val addressNumber = beneficiary.addressNumber?.onlyNumbers()
        validator.check(
            addressNumber,
            "addressNumber",
            listOf(
                reusableRules.getValue(ADDRESS_NUMBER_IS_PRESENT),
            )
        )

        val productTitle = beneficiary.productTitle?.keepOnlyLetters()
        validator.check(
            productTitle,
            "productTitle",
            listOf(
                reusableRules.getValue(PRODUCT_TITLE_IS_PRESENT),
            )
        )

        val dateOfBirthNormalized = beneficiary.dateOfBirth?.toNormalizedDate()
        val dateOfBirthParsed = dateOfBirthNormalized?.toLocalDate()
        validator.check(
            dateOfBirthNormalized,
            "dateOfBirth",
            listOf(
                reusableRules.getValue(BIRTH_DATE_IS_PRESENT),
                RuleAndMessage({ dateOfBirthParsed!!.isAfterEq(now) }, "Data de nascimento inválida."),
                RuleAndMessage({ dateOfBirthParsed!!.isBefore(minimumDateOfBirth) }, "Data de nascimento inválida."),
            )
        )

        val email = beneficiary.email?.replace("!", "")
        validator.check(
            email,
            "email",
            listOf(
                reusableRules.getValue(EMAIL_IS_PRESENT),
                reusableRules.getValue(EMAIL_IS_VALID),
            )
        )

        val activatedAtNormalized = beneficiary.activatedAt?.toNormalizedDate()
        val activatedAtParsed = activatedAtNormalized?.toLocalDate()
        validator.check(
            activatedAtNormalized,
            "activatedAt",
            listOf(
                reusableRules.getValue(ACTIVATED_AT_IS_PRESENT),
                RuleAndMessage({ !activatedAtParsed!!.isAfterEq(now) }, "Data de ativação deve ser maior que a data atual."),
            )
        )

        return validator.getErrors()
    }

    private fun validateDependent(beneficiary: BeneficiaryBatchItemTransport): BeneficiaryBatchValidationError? {
        val validator = ValidationCollector(beneficiary.index)

        if (beneficiary.ownership == HOLDER) {
            return validator.getErrors()
        }

        val relationExceeds30Days = beneficiary.relationExceeds30Days?.capitalizeEachWord()?.removeAccents()
        val parentBeneficiaryRelationType = beneficiary.parentBeneficiaryRelationType?.keepOnlyLetters()

        validator.check(
            relationExceeds30Days,
            "relationExceeds30Days",
            listOf(
                RuleAndMessage({ it == null }, "Vínculo com titular a mais de 30 dias obrigatório."),
                RuleAndMessage({ YES_OR_NO.notContains(it) }, "Vínculo com titular a mais de 30 dias inválido, selecione uma das opções da lista.")
            )
        )

        validator.check(
            parentBeneficiaryRelationType,
            "parentBeneficiaryRelationType",
            listOf(
                RuleAndMessage({ it == null }, "Relação com titular obrigatória."),
                RuleAndMessage({ PARENT_RELATIONSHIP_TYPE.notContains(it) }, "Relação com titular inválida, selecione uma das opções da lista.")
            )
        )

        val parentNationalId = beneficiary.parentNationalId?.onlyNumbers()
        validator.check(
            parentNationalId,
            "parentNationalId",
            listOf(
                reusableRules.getValue(HOLDER_NATIONAL_ID_IS_PRESENT),
                reusableRules.getValue(HOLDER_NATIONAL_ID_IS_VALID),
            )
        )

        return validator.getErrors()
    }

    private fun validateHolder(beneficiary: BeneficiaryBatchItemTransport): BeneficiaryBatchValidationError? {
        val validator = ValidationCollector(beneficiary.index)

        if (beneficiary.ownership == DEPENDENT) {
            return validator.getErrors()
        }

        val insuranceCnpj = beneficiary.insuranceCnpj?.onlyNumbers()
        validator.check(
            insuranceCnpj,
            "insuranceCnpj",
            listOf(
                RuleAndMessage({ it == null }, "CNPJ para faturamento obrigatório."),
                RuleAndMessage({ !it!!.isValidCnpj() }, "CNPJ para faturamento inválido."),
            )
        )

        val beneficiaryContractType = beneficiary.beneficiaryContractType?.keepOnlyLetters()
        validator.check(
            beneficiaryContractType,
            "beneficiaryContractType",
            listOf(
                RuleAndMessage({ it == null }, "Regime de contratação obrigatório."),
                RuleAndMessage({ BENEFICIARIES_CONTRACT_TYPES.notContains(it) }, "Regime de contratação inválido, escolha CLT ou PJ."),
            )
        )

        val cnpj = beneficiary.cnpj?.onlyNumbers()
        validator.check(
            cnpj,
            "cnpj",
            listOf(
                RuleAndMessage({ it == null && beneficiaryContractType == PJ }, "CNPJ do titular obrigatório."),
                RuleAndMessage({ !it!!.isValidCnpj() }, "CNPJ do titular inválido."),
            )
        )

        validator.check(
            beneficiary.hiredAt,
            "hiredAt",
            listOf(
                RuleAndMessage({ it == null }, "Data de admissão do titular obrigatória."),
                RuleAndMessage({ beneficiary.hiredAt!!.toNormalizedDate() == null }, "Data de admissão do titular com formato inválido, use DD/MM/AAAA."),
            )
        )

        return validator.getErrors()
    }

    private fun String.removeAccents(): String {
        val normalized = Normalizer.normalize(this, Normalizer.Form.NFD)
        val pattern = Pattern.compile("\\p{InCombiningDiacriticalMarks}+")
        return pattern.matcher(normalized).replaceAll("")
    }

    private fun String.keepOnlyLetters(): String = this.replace(KEEP_ONLY_LETTER_REGEX, "")

    private fun String.hasAllSameDigits(): Boolean =
        this.all { it == this[0] }

    private fun String.toNormalizedDate(): String? {
        val cleaned = this.replace("/", "-").trim()

        val formats = listOf(
            DateTimeFormatter.ofPattern("dd-MM-yyyy", Locale.getDefault()),
            DateTimeFormatter.ISO_LOCAL_DATE
        )

        for (format in formats) {
            try {
                val date = LocalDate.parse(cleaned, format)
                return date.format(DateTimeFormatter.ISO_LOCAL_DATE)
            } catch (e: DateTimeParseException) {
                continue
            }
        }

        return null
    }

    private fun String.hasRepeatedWords(): Boolean {
        val words = this
            .split("\\s+".toRegex())
            .filter { it.isNotBlank() }

        return words.size != words.toSet().size
    }
}
