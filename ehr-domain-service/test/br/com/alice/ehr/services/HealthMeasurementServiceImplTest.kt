package br.com.alice.ehr.services

import br.com.alice.appointment.client.AppointmentService
import br.com.alice.common.RangeUUID
import br.com.alice.common.core.exceptions.InvalidArgumentException
import br.com.alice.common.core.exceptions.NotFoundException
import br.com.alice.common.data.dsl.matchers.ResultAssert.Companion.assertThat
import br.com.alice.common.data.dsl.matchers.queryEq
import br.com.alice.common.kafka.interfaces.KafkaProducerService
import br.com.alice.common.service.data.dsl.and
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.HealthMeasurementInternalType
import br.com.alice.data.layer.models.HealthMeasurementInternalType.HEART_RATE
import br.com.alice.data.layer.models.HealthMeasurementInternalType.HEIGHT
import br.com.alice.data.layer.models.HealthMeasurementInternalType.WEIGHT
import br.com.alice.data.layer.services.DraftCommandModelDataService
import br.com.alice.data.layer.services.HealthMeasurementCategoryModelDataService
import br.com.alice.data.layer.services.HealthMeasurementModelDataService
import br.com.alice.data.layer.services.HealthMeasurementTypeModelDataService
import br.com.alice.ehr.converters.toModel
import br.com.alice.ehr.event.HealthMeasurementCreatedEvent
import br.com.alice.ehr.event.HealthMeasurementCreatedEventPayload
import com.github.kittinunf.result.failure
import com.github.kittinunf.result.success
import io.mockk.called
import io.mockk.clearMocks
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.mockk
import kotlinx.coroutines.runBlocking
import org.assertj.core.api.Assertions.assertThat
import java.math.BigDecimal
import java.time.LocalDateTime
import kotlin.test.BeforeTest
import kotlin.test.Test

class HealthMeasurementServiceImplTest {
    private val healthMeasurementDataService: HealthMeasurementModelDataService = mockk()
    private val healthMeasurementTypeDataService: HealthMeasurementTypeModelDataService = mockk()
    private val healthMeasurementCategoryDataService: HealthMeasurementCategoryModelDataService = mockk()
    private val appointmentService: AppointmentService = mockk()
    private val draftCommandDataService: DraftCommandModelDataService = mockk()
    private val kafkaProducerService: KafkaProducerService = mockk()

    private val healthMeasurementService = HealthMeasurementServiceImpl(
        healthMeasurementDataService,
        healthMeasurementTypeDataService,
        healthMeasurementCategoryDataService,
        appointmentService,
        draftCommandDataService,
        kafkaProducerService,
    )

    private val staff = TestModelFactory.buildHealthcareTeamPhysician()
    private val appointment = TestModelFactory.buildAppointment(staffId = staff.id)
    private val testPersonId = appointment.personId

    @BeforeTest
    fun setup() {
        clearMocks(healthMeasurementDataService)
    }

    @Test
    fun `#addToHistory should invalidate last valid and create new HealthMeasurement when there is an old one`() = runBlocking {
        val currentHealthMeasurement = TestModelFactory.buildHealthMeasurement(
            personId = testPersonId,
            type = HealthMeasurementInternalType.SKELETAL_MUSCLE_MASS,
            value = BigDecimal.valueOf(65),
            updatedByStaffId = staff.id,
            appointmentId = appointment.id
        ).toModel()

        val newHealthMeasurement = TestModelFactory.buildHealthMeasurement(
            personId = testPersonId,
            type = HealthMeasurementInternalType.SKELETAL_MUSCLE_MASS,
            value = BigDecimal.valueOf(64),
            updatedByStaffId = staff.id,
            appointmentId = appointment.id
        )

        coEvery {
            healthMeasurementDataService.findOne(any())
        } returns currentHealthMeasurement.success()

        coEvery {
            healthMeasurementDataService.update(any())
        } returns currentHealthMeasurement.copy(version = 1, active = false).success()

        coEvery {
            healthMeasurementDataService.add(any())
        } returns newHealthMeasurement.toModel().success()

        coEvery {
            healthMeasurementTypeDataService.findOneOrNull(any())
        } returns null

        coEvery { kafkaProducerService.produce(any()) } returns mockk()

        val result = healthMeasurementService.addToHistory(newHealthMeasurement)
        assertThat(result).isSuccessWithData(newHealthMeasurement)

        coVerify(exactly = 1) {
            healthMeasurementDataService.findOne(
                queryEq {
                    where {
                        this.active.eq(true)
                            .and(this.personId.eq(testPersonId))
                            .and(this.type.eq(currentHealthMeasurement.type!!))
                    }
                }
            )
        }

        coVerify(exactly = 1) {
            healthMeasurementDataService.update(
                match {
                    !it.active &&
                        it.personId == testPersonId &&
                        it.updatedByStaffId == staff.id &&
                        it.type == currentHealthMeasurement.type &&
                        it.value == currentHealthMeasurement.value &&
                        it.appointmentId == appointment.id
                }
            )
        }

        coVerify(exactly = 1) {
            healthMeasurementDataService.add(
                match {
                    it.active &&
                        it.personId == testPersonId &&
                        it.updatedByStaffId == staff.id &&
                        it.type == newHealthMeasurement.type &&
                        it.value == newHealthMeasurement.value &&
                        it.appointmentId == appointment.id
                }
            )
        }
        coVerify {
            kafkaProducerService.produce(
                match { it: HealthMeasurementCreatedEvent ->
                    it.payload == HealthMeasurementCreatedEventPayload(newHealthMeasurement)
                }
            )
        }
    }

    @Test
    fun `#addToHistory invalidates last valid and create new one for dynamic types`() = runBlocking {
        val bicepsCircumference = TestModelFactory.buildHealthMeasurementType(
            key = "BICEPS_CIRCUMFERENCE",
        )
        val currentHealthMeasurement = TestModelFactory.buildHealthMeasurement(
            personId = testPersonId,
            type = null,
            typeId = bicepsCircumference.id,
            value = BigDecimal.valueOf(38),
            updatedByStaffId = staff.id,
            appointmentId = appointment.id
        ).toModel()
        val newHealthMeasurement = TestModelFactory.buildHealthMeasurement(
            personId = testPersonId,
            type = null,
            typeId = bicepsCircumference.id,
            value = BigDecimal.valueOf(40),
            updatedByStaffId = staff.id,
            appointmentId = appointment.id
        )

        coEvery {
            healthMeasurementDataService.findOne(
                queryEq {
                    where {
                        this.active.eq(true) and
                            this.personId.eq(testPersonId) and
                            this.typeId.eq(bicepsCircumference.id)
                    }
                }
            )
        } returns currentHealthMeasurement.success()

        coEvery {
            healthMeasurementDataService.update(
                match {
                    !it.active &&
                        it.personId == testPersonId &&
                        it.updatedByStaffId == staff.id &&
                        it.typeId == currentHealthMeasurement.typeId &&
                        it.value == currentHealthMeasurement.value &&
                        it.appointmentId == appointment.id
                }
            )
        } returns currentHealthMeasurement.copy(version = 1, active = false).success()

        coEvery {
            healthMeasurementDataService.add(
                match {
                    it.active &&
                        it.personId == testPersonId &&
                        it.updatedByStaffId == staff.id &&
                        it.typeId == newHealthMeasurement.typeId &&
                        it.value == newHealthMeasurement.value &&
                        it.appointmentId == appointment.id
                }
            )
        } returns newHealthMeasurement.toModel().success()

        coEvery { kafkaProducerService.produce(any()) } returns mockk()

        val result = healthMeasurementService.addToHistory(newHealthMeasurement)

        assertThat(result).isSuccessWithData(newHealthMeasurement)

        coVerify { healthMeasurementTypeDataService wasNot called }
        coVerify(exactly = 1) { healthMeasurementDataService.findOne(any()) }
        coVerify(exactly = 1) { healthMeasurementDataService.update(any()) }
        coVerify(exactly = 1) { healthMeasurementDataService.add(any()) }
        coVerify {
            kafkaProducerService.produce(
                match { it: HealthMeasurementCreatedEvent ->
                    it.payload == HealthMeasurementCreatedEventPayload(newHealthMeasurement)
                }
            )
        }
    }

    @Test
    fun `#addToHistory should create new HealthMeasurement when there is not an old one`() = runBlocking {
        val healthMeasurement = TestModelFactory.buildHealthMeasurement(
            personId = testPersonId,
            type = HealthMeasurementInternalType.DIASTOLIC_PRESSURE,
            value = BigDecimal.valueOf(60),
            updatedByStaffId = staff.id,
            appointmentId = appointment.id
        )

        coEvery { healthMeasurementDataService.findOne(any()) } returns NotFoundException().failure()

        coEvery { healthMeasurementDataService.add(any()) } returns healthMeasurement.toModel().success()

        coEvery { healthMeasurementTypeDataService.findOneOrNull(any()) } returns null
        coEvery { kafkaProducerService.produce(any()) } returns mockk()

        val result = healthMeasurementService.addToHistory(healthMeasurement)
        assertThat(result).isSuccessWithData(healthMeasurement)

        coVerify(exactly = 1) {
            healthMeasurementDataService.findOne(
                queryEq {
                    where {
                        this.active.eq(true)
                            .and(this.personId.eq(testPersonId))
                            .and(this.type.eq(HealthMeasurementInternalType.DIASTOLIC_PRESSURE))
                    }
                }
            )
        }

        coVerify(exactly = 0) { healthMeasurementDataService.update(any()) }

        coVerify(exactly = 1) {
            healthMeasurementDataService.add(
                match {
                    it.active &&
                        it.personId == testPersonId &&
                        it.updatedByStaffId == staff.id &&
                        it.type == healthMeasurement.type &&
                        it.value == healthMeasurement.value &&
                        it.appointmentId == appointment.id
                }
            )
        }

        coVerify {
            kafkaProducerService.produce(
                match { it: HealthMeasurementCreatedEvent ->
                    it.payload == HealthMeasurementCreatedEventPayload(healthMeasurement)
                }
            )
        }
    }

    @Test
    fun `#addToHistory sets typeId of a known type`() = runBlocking {
        val heightType = TestModelFactory.buildHealthMeasurementType(
            key = HEIGHT.toString(),
        ).toModel()
        val measurement = TestModelFactory.buildHealthMeasurement(
            personId = testPersonId,
            type = HEIGHT,
            value = BigDecimal.valueOf(180),
            updatedByStaffId = staff.id,
            appointmentId = appointment.id,
        )

        val newMeasurement = measurement.copy(typeId = heightType.id)

        coEvery {
            healthMeasurementTypeDataService.findOneOrNull(
                queryEq { where { key.eq(HEIGHT.toString()) } }
            )
        } returns heightType

        coEvery {
            healthMeasurementDataService.findOne(any())
        } returns NotFoundException().failure()

        coEvery {
            healthMeasurementDataService.add(
                match {
                    it.active &&
                        it.personId == testPersonId &&
                        it.updatedByStaffId == staff.id &&
                        it.type == HealthMeasurementInternalType.HEIGHT &&
                        it.typeId == heightType.id &&
                        it.value == measurement.value &&
                        it.appointmentId == appointment.id
                }
            )
        } returns newMeasurement.toModel().success()

        coEvery { kafkaProducerService.produce(any()) } returns mockk()

        val result = healthMeasurementService.addToHistory(measurement)

        assertThat(result).isSuccessWithData(
            measurement.copy(typeId = heightType.id)
        )

        coVerify(exactly = 1) { healthMeasurementDataService.findOne(any()) }
        coVerify(exactly = 0) { healthMeasurementDataService.update(any()) }
        coVerify(exactly = 1) { healthMeasurementDataService.add(any()) }
        coVerify {
            kafkaProducerService.produce(
                match { it: HealthMeasurementCreatedEvent ->
                    it.payload == HealthMeasurementCreatedEventPayload(newMeasurement)
                }
            )
        }
    }

    @Test
    fun `#addToHistory returns an error if measurement does not have a type or typeId`() = runBlocking {
        val measurement = TestModelFactory.buildHealthMeasurement(
            type = null,
            typeId = null,
        )

        val result = healthMeasurementService.addToHistory(measurement)

        assertThat(result).isFailureOfType(InvalidArgumentException::class)

        coVerify { healthMeasurementDataService wasNot called }
        coVerify { healthMeasurementTypeDataService wasNot called }
    }

    @Test
    fun `#getActiveByPersonId should return all HealthMeasurement from personId`() = runBlocking {
        val activeWeight = TestModelFactory.buildHealthMeasurement().copy(
            type = WEIGHT,
            personId = testPersonId,
            updatedByStaffId = staff.id,
            appointmentId = appointment.id,
            active = true,
            createdAt = LocalDateTime.of(2021, 10, 20, 14, 10),
        )
        val inactiveWeight = TestModelFactory.buildHealthMeasurement().copy(
            type = WEIGHT,
            personId = testPersonId,
            updatedByStaffId = staff.id,
            appointmentId = appointment.id,
            active = false,
            createdAt = LocalDateTime.of(2021, 2, 20, 14, 10),
        )
        val activeHeight = TestModelFactory.buildHealthMeasurement().copy(
            type = HEIGHT,
            personId = testPersonId,
            updatedByStaffId = staff.id,
            appointmentId = appointment.id,
            active = true,
            createdAt = LocalDateTime.of(2021, 8, 20, 14, 10),
        )

        coEvery {
            healthMeasurementDataService.find(
                queryEq {
                    where { this.personId.eq(testPersonId) }
                }
            )
        } returns listOf(activeWeight.toModel(), activeHeight.toModel(), inactiveWeight.toModel()).success()

        val result = healthMeasurementService.getActivesByPersonId(testPersonId)

        assertThat(result).isSuccessWithData(
            listOf(activeHeight, activeWeight)
        )

        coVerify(exactly = 1) { healthMeasurementDataService.find(any()) }
    }

    @Test
    fun `#getActivesAtDate returns active HealthMeasurements on a given date`() = runBlocking {
        val bicepsCircumferenceType = TestModelFactory.buildHealthMeasurementType(
            key = "BICEPS_CIRCUMFERENCE",
            name = "Circunferência do Biceps",
        )
        val heightType = TestModelFactory.buildHealthMeasurementType(
            key = "HEIGHT",
            name = "Altura",
        )

        val someDate = LocalDateTime.of(2021, 3, 18, 13, 0)

        val mostRecentHeartMeasurementUntilSomeDate = TestModelFactory.buildHealthMeasurement(
            type = HEART_RATE,
            typeId = null,
            value = BigDecimal.valueOf(100),
            addedAt = LocalDateTime.of(2021, 3, 15, 14, 0),
        )
        val anotherHeartRateMeasurement = TestModelFactory.buildHealthMeasurement(
            type = HEART_RATE,
            typeId = null,
            value = BigDecimal.valueOf(90),
            addedAt = LocalDateTime.of(2021, 3, 10, 8, 0),
        )
        val mostRecentHeightMeasurement = TestModelFactory.buildHealthMeasurement(
            type = HEIGHT,
            typeId = null,
            value = BigDecimal.valueOf(180),
            addedAt = LocalDateTime.of(2021, 3, 15, 14, 0),
        )
        val anotherHeightMeasurement = TestModelFactory.buildHealthMeasurement(
            type = null,
            typeId = heightType.id,
            value = BigDecimal.valueOf(179),
            addedAt = LocalDateTime.of(2021, 3, 10, 8, 0),
        )
        val bicepsMeasurement = TestModelFactory.buildHealthMeasurement(
            type = null,
            typeId = bicepsCircumferenceType.id,
            addedAt = LocalDateTime.of(2021, 2, 10, 8, 0),
        )

        coEvery {
            healthMeasurementTypeDataService.find(queryEq { all() })
        } returns listOf(bicepsCircumferenceType.toModel(), heightType.toModel()).success()

        coEvery {
            healthMeasurementDataService.find(
                queryEq {
                    where {
                        this.personId.eq(testPersonId) and
                            this.addedAt.beforeOrAt(someDate) and
                            this.type.inList(HealthMeasurementInternalType.values().toList())
                    }
                }
            )
        } returns listOf(
            mostRecentHeartMeasurementUntilSomeDate.toModel(),
            anotherHeartRateMeasurement.toModel(),
            mostRecentHeightMeasurement.toModel(),
        ).success()

        coEvery {
            healthMeasurementDataService.find(
                queryEq {
                    where {
                        this.personId.eq(testPersonId) and
                            this.addedAt.beforeOrAt(someDate) and
                            this.typeId.inList(
                                listOf(bicepsCircumferenceType.id, heightType.id)
                            )
                    }
                }
            )
        } returns listOf(anotherHeightMeasurement.toModel(), bicepsMeasurement.toModel()).success()

        val result = healthMeasurementService.getActivesAtDate(testPersonId, someDate)

        assertThat(result).isSuccess()

        assertThat(result.get()).isEqualTo(
            listOf(
                mostRecentHeartMeasurementUntilSomeDate,
                mostRecentHeightMeasurement,
                bicepsMeasurement,
            )
        )

        coVerify(exactly = 2) { healthMeasurementTypeDataService.find(any()) }
        coVerify(exactly = 2) { healthMeasurementDataService.find(any()) }
    }

    @Test
    fun `#getByPersonId returns all measurements of a person`() = runBlocking {
        val healthMeasurement = TestModelFactory.buildHealthMeasurement(
            personId = testPersonId,
            updatedByStaffId = staff.id,
            appointmentId = appointment.id
        )

        coEvery {
            appointmentService.currentDraft(any(), any())
        } returns NotFoundException().failure()

        coEvery {
            healthMeasurementDataService.find(
                queryEq {
                    where { this.personId.eq(testPersonId) }
                }
            )
        } returns listOf(healthMeasurement.toModel()).success()

        val result = healthMeasurementService.getByPersonId(
            testPersonId, RangeUUID.generate()
        )

        assertThat(result).isSuccessWithData(listOf(healthMeasurement))

        coVerify(exactly = 1) { healthMeasurementDataService.find(any()) }
    }

    @Test
    fun `#createCategory calls data service add`() = runBlocking {
        val category = TestModelFactory.buildHealthMeasurementCategory()

        coEvery {
            healthMeasurementCategoryDataService.add(category.toModel())
        } returns category.toModel().success()

        val result = healthMeasurementService.createCategory(category)

        assertThat(result).isSuccessWithData(category)

        coVerify(exactly = 1) { healthMeasurementCategoryDataService.add(any()) }
    }

    @Test
    fun `#getAllCategories returns all categories`() = runBlocking {
        val activeCategory = TestModelFactory.buildHealthMeasurementCategory(
            active = true,
        )
        val inactiveCategory = TestModelFactory.buildHealthMeasurementCategory(
            active = false,
        )

        coEvery {
            healthMeasurementCategoryDataService.find(queryEq { all() })
        } returns listOf(activeCategory.toModel(), inactiveCategory.toModel()).success()

        val result = healthMeasurementService.getAllCategories()

        assertThat(result).isSuccessWithData(listOf(activeCategory, inactiveCategory))

        coVerify(exactly = 1) { healthMeasurementCategoryDataService.find(any()) }
    }

    @Test
    fun `#getActiveCategories returns active categories`() = runBlocking {
        val categoryA = TestModelFactory.buildHealthMeasurementCategory()
        val categoryB = TestModelFactory.buildHealthMeasurementCategory()

        coEvery {
            healthMeasurementCategoryDataService.find(
                queryEq {
                    where { active.eq(true) }.orderBy { updatedAt }.sortOrder { desc }
                }
            )
        } returns listOf(categoryA.toModel(), categoryB.toModel()).success()

        val result = healthMeasurementService.getActiveCategories()

        assertThat(result).isSuccessWithData(listOf(categoryA, categoryB))

        coVerify(exactly = 1) { healthMeasurementCategoryDataService.find(any()) }
    }

    @Test
    fun `#getCategory finds a single category by its id`() = runBlocking {
        val categoryId = RangeUUID.generate()
        val category = TestModelFactory.buildHealthMeasurementCategory().copy(id = categoryId)

        coEvery {
            healthMeasurementCategoryDataService.get(categoryId)
        } returns category.toModel().success()

        val result = healthMeasurementService.getCategory(categoryId)

        assertThat(result).isSuccessWithData(category)

        coVerify(exactly = 1) { healthMeasurementCategoryDataService.get(any()) }
    }

    @Test
    fun `#updateCategory calls data service update`() = runBlocking {
        val category = TestModelFactory.buildHealthMeasurementCategory()

        coEvery {
            healthMeasurementCategoryDataService.update(category.toModel())
        } returns category.toModel().success()

        val result = healthMeasurementService.updateCategory(category)

        assertThat(result).isSuccessWithData(category)

        coVerify(exactly = 1) { healthMeasurementCategoryDataService.update(any()) }
    }

    @Test
    fun `#createType calls data service add`() = runBlocking {
        val type = TestModelFactory.buildHealthMeasurementType()

        coEvery {
            healthMeasurementTypeDataService.add(type.toModel())
        } returns type.toModel().success()

        val result = healthMeasurementService.createType(type)

        assertThat(result).isSuccessWithData(type)

        coVerify(exactly = 1) { healthMeasurementTypeDataService.add(any()) }
    }

    @Test
    fun `#getAllTypes returns all types`() = runBlocking {
        val activeType = TestModelFactory.buildHealthMeasurementType()
        val inactiveType = TestModelFactory.buildHealthMeasurementType(
            active = false
        )

        coEvery {
            healthMeasurementTypeDataService.find(queryEq { all() })
        } returns listOf(activeType.toModel(), inactiveType.toModel()).success()

        val result = healthMeasurementService.getAllTypes()

        assertThat(result).isSuccessWithData(listOf(activeType, inactiveType))

        coVerify(exactly = 1) { healthMeasurementTypeDataService.find(any()) }
    }

    @Test
    fun `#getActiveTypes returns all active types`() = runBlocking {
        val typeA = TestModelFactory.buildHealthMeasurementType()
        val typeB = TestModelFactory.buildHealthMeasurementType()

        coEvery {
            healthMeasurementTypeDataService.find(
                queryEq {
                    where { active.eq(true) }.orderBy { updatedAt }.sortOrder { desc }
                }
            )
        } returns listOf(typeA.toModel(), typeB.toModel()).success()

        val result = healthMeasurementService.getActiveTypes()

        assertThat(result).isSuccessWithData(listOf(typeA, typeB))

        coVerify(exactly = 1) { healthMeasurementTypeDataService.find(any()) }
    }

    @Test
    fun `#getType finds a single type by its id`() = runBlocking {
        val typeId = RangeUUID.generate()
        val type = TestModelFactory.buildHealthMeasurementType().copy(id = typeId)

        coEvery {
            healthMeasurementTypeDataService.get(typeId)
        } returns type.toModel().success()

        val result = healthMeasurementService.getType(typeId)

        assertThat(result).isSuccessWithData(type)

        coVerify(exactly = 1) { healthMeasurementTypeDataService.get(any()) }
    }

    @Test
    fun `#getTypeByKey finds a single type by its id`() = runBlocking {
        val type = TestModelFactory.buildHealthMeasurementType()

        coEvery {
            healthMeasurementTypeDataService.findOne(
                queryEq {
                    where { this.key.eq(type.key) }
                }
            )
        } returns type.toModel().success()

        val result = healthMeasurementService.getTypeByKey(type.key)

        assertThat(result).isSuccessWithData(type)

        coVerify(exactly = 1) { healthMeasurementTypeDataService.findOne(any()) }
    }

    @Test
    fun `#updateType calls data service update`() = runBlocking {
        val type = TestModelFactory.buildHealthMeasurementType()

        coEvery {
            healthMeasurementTypeDataService.update(type.toModel())
        } returns type.toModel().success()

        val result = healthMeasurementService.updateType(type)

        assertThat(result).isSuccessWithData(type)

        coVerify(exactly = 1) { healthMeasurementTypeDataService.update(any()) }
    }
}
