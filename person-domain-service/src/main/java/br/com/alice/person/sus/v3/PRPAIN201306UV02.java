//
// Este arquivo foi gerado pela Arquitetura JavaTM para Implementação de Referência (JAXB) de Bind XML, v2.2.8-b130911.1802 
// Consulte <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// Todas as modificações neste arquivo serão perdidas após a recompilação do esquema de origem. 
// Gerado em: 2023.02.02 às 05:58:06 PM BRT 
//


package br.com.alice.person.sus.v3;

import jakarta.xml.bind.annotation.*;
import java.math.BigInteger;
import java.util.ArrayList;
import java.util.List;


/**
 * <p>Classe Java de anonymous complex type.
 * 
 * <p>O seguinte fragmento do esquema especifica o conteúdo esperado contido dentro desta classe.
 * 
 * <pre>
 * &lt;complexType>
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="id">
 *           &lt;complexType>
 *             &lt;complexContent>
 *               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *                 &lt;attribute name="root" use="required" type="{http://www.w3.org/2001/XMLSchema}string" />
 *               &lt;/restriction>
 *             &lt;/complexContent>
 *           &lt;/complexType>
 *         &lt;/element>
 *         &lt;element name="creationTime">
 *           &lt;complexType>
 *             &lt;complexContent>
 *               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *                 &lt;attribute name="value" use="required" type="{http://www.w3.org/2001/XMLSchema}unsignedLong" />
 *               &lt;/restriction>
 *             &lt;/complexContent>
 *           &lt;/complexType>
 *         &lt;/element>
 *         &lt;element name="interactionId">
 *           &lt;complexType>
 *             &lt;complexContent>
 *               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *                 &lt;attribute name="root" use="required" type="{http://www.w3.org/2001/XMLSchema}string" />
 *                 &lt;attribute name="extension" use="required" type="{http://www.w3.org/2001/XMLSchema}string" />
 *               &lt;/restriction>
 *             &lt;/complexContent>
 *           &lt;/complexType>
 *         &lt;/element>
 *         &lt;element name="processingCode">
 *           &lt;complexType>
 *             &lt;complexContent>
 *               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *                 &lt;attribute name="code" use="required" type="{http://www.w3.org/2001/XMLSchema}string" />
 *               &lt;/restriction>
 *             &lt;/complexContent>
 *           &lt;/complexType>
 *         &lt;/element>
 *         &lt;element name="processingModeCode">
 *           &lt;complexType>
 *             &lt;complexContent>
 *               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *                 &lt;attribute name="code" use="required" type="{http://www.w3.org/2001/XMLSchema}string" />
 *               &lt;/restriction>
 *             &lt;/complexContent>
 *           &lt;/complexType>
 *         &lt;/element>
 *         &lt;element name="acceptAckCode">
 *           &lt;complexType>
 *             &lt;complexContent>
 *               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *                 &lt;attribute name="code" use="required" type="{http://www.w3.org/2001/XMLSchema}string" />
 *               &lt;/restriction>
 *             &lt;/complexContent>
 *           &lt;/complexType>
 *         &lt;/element>
 *         &lt;element name="receiver">
 *           &lt;complexType>
 *             &lt;complexContent>
 *               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *                 &lt;sequence>
 *                   &lt;element name="device">
 *                     &lt;complexType>
 *                       &lt;complexContent>
 *                         &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *                           &lt;sequence>
 *                             &lt;element name="id">
 *                               &lt;complexType>
 *                                 &lt;complexContent>
 *                                   &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *                                     &lt;attribute name="root" use="required" type="{http://www.w3.org/2001/XMLSchema}string" />
 *                                   &lt;/restriction>
 *                                 &lt;/complexContent>
 *                               &lt;/complexType>
 *                             &lt;/element>
 *                             &lt;element name="name" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *                           &lt;/sequence>
 *                           &lt;attribute name="classCode" use="required" type="{http://www.w3.org/2001/XMLSchema}string" />
 *                           &lt;attribute name="determinerCode" use="required" type="{http://www.w3.org/2001/XMLSchema}string" />
 *                         &lt;/restriction>
 *                       &lt;/complexContent>
 *                     &lt;/complexType>
 *                   &lt;/element>
 *                 &lt;/sequence>
 *                 &lt;attribute name="typeCode" use="required" type="{http://www.w3.org/2001/XMLSchema}string" />
 *               &lt;/restriction>
 *             &lt;/complexContent>
 *           &lt;/complexType>
 *         &lt;/element>
 *         &lt;element name="sender">
 *           &lt;complexType>
 *             &lt;complexContent>
 *               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *                 &lt;sequence>
 *                   &lt;element name="device">
 *                     &lt;complexType>
 *                       &lt;complexContent>
 *                         &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *                           &lt;sequence>
 *                             &lt;element name="id">
 *                               &lt;complexType>
 *                                 &lt;complexContent>
 *                                   &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *                                     &lt;attribute name="root" use="required" type="{http://www.w3.org/2001/XMLSchema}string" />
 *                                   &lt;/restriction>
 *                                 &lt;/complexContent>
 *                               &lt;/complexType>
 *                             &lt;/element>
 *                           &lt;/sequence>
 *                           &lt;attribute name="classCode" use="required" type="{http://www.w3.org/2001/XMLSchema}string" />
 *                           &lt;attribute name="determinerCode" use="required" type="{http://www.w3.org/2001/XMLSchema}string" />
 *                         &lt;/restriction>
 *                       &lt;/complexContent>
 *                     &lt;/complexType>
 *                   &lt;/element>
 *                 &lt;/sequence>
 *                 &lt;attribute name="typeCode" use="required" type="{http://www.w3.org/2001/XMLSchema}string" />
 *               &lt;/restriction>
 *             &lt;/complexContent>
 *           &lt;/complexType>
 *         &lt;/element>
 *         &lt;element name="acknowledgement">
 *           &lt;complexType>
 *             &lt;complexContent>
 *               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *                 &lt;sequence>
 *                   &lt;element name="typeCode">
 *                     &lt;complexType>
 *                       &lt;complexContent>
 *                         &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *                           &lt;attribute name="code" use="required" type="{http://www.w3.org/2001/XMLSchema}string" />
 *                         &lt;/restriction>
 *                       &lt;/complexContent>
 *                     &lt;/complexType>
 *                   &lt;/element>
 *                   &lt;element name="targetMessage">
 *                     &lt;complexType>
 *                       &lt;complexContent>
 *                         &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *                           &lt;sequence>
 *                             &lt;element name="id">
 *                               &lt;complexType>
 *                                 &lt;complexContent>
 *                                   &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *                                     &lt;attribute name="root" use="required" type="{http://www.w3.org/2001/XMLSchema}string" />
 *                                     &lt;attribute name="extension" use="required" type="{http://www.w3.org/2001/XMLSchema}unsignedInt" />
 *                                   &lt;/restriction>
 *                                 &lt;/complexContent>
 *                               &lt;/complexType>
 *                             &lt;/element>
 *                           &lt;/sequence>
 *                         &lt;/restriction>
 *                       &lt;/complexContent>
 *                     &lt;/complexType>
 *                   &lt;/element>
 *                 &lt;/sequence>
 *               &lt;/restriction>
 *             &lt;/complexContent>
 *           &lt;/complexType>
 *         &lt;/element>
 *         &lt;element name="controlActProcess">
 *           &lt;complexType>
 *             &lt;complexContent>
 *               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *                 &lt;sequence>
 *                   &lt;element name="code">
 *                     &lt;complexType>
 *                       &lt;complexContent>
 *                         &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *                           &lt;attribute name="code" use="required" type="{http://www.w3.org/2001/XMLSchema}string" />
 *                         &lt;/restriction>
 *                       &lt;/complexContent>
 *                     &lt;/complexType>
 *                   &lt;/element>
 *                   &lt;element name="subject">
 *                     &lt;complexType>
 *                       &lt;complexContent>
 *                         &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *                           &lt;sequence>
 *                             &lt;element name="registrationEvent">
 *                               &lt;complexType>
 *                                 &lt;complexContent>
 *                                   &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *                                     &lt;sequence>
 *                                       &lt;element name="statusCode">
 *                                         &lt;complexType>
 *                                           &lt;complexContent>
 *                                             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *                                               &lt;attribute name="code" use="required" type="{http://www.w3.org/2001/XMLSchema}string" />
 *                                             &lt;/restriction>
 *                                           &lt;/complexContent>
 *                                         &lt;/complexType>
 *                                       &lt;/element>
 *                                       &lt;element name="subject1">
 *                                         &lt;complexType>
 *                                           &lt;complexContent>
 *                                             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *                                               &lt;sequence>
 *                                                 &lt;element name="realmCode">
 *                                                   &lt;complexType>
 *                                                     &lt;complexContent>
 *                                                       &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *                                                         &lt;attribute name="code" use="required" type="{http://www.w3.org/2001/XMLSchema}string" />
 *                                                       &lt;/restriction>
 *                                                     &lt;/complexContent>
 *                                                   &lt;/complexType>
 *                                                 &lt;/element>
 *                                                 &lt;element name="patient">
 *                                                   &lt;complexType>
 *                                                     &lt;complexContent>
 *                                                       &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *                                                         &lt;sequence>
 *                                                           &lt;element name="id" maxOccurs="unbounded">
 *                                                             &lt;complexType>
 *                                                               &lt;complexContent>
 *                                                                 &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *                                                                   &lt;attribute name="root" use="required" type="{http://www.w3.org/2001/XMLSchema}string" />
 *                                                                   &lt;attribute name="extension" use="required" type="{http://www.w3.org/2001/XMLSchema}unsignedLong" />
 *                                                                   &lt;attribute name="assigningAuthorityName" use="required" type="{http://www.w3.org/2001/XMLSchema}string" />
 *                                                                 &lt;/restriction>
 *                                                               &lt;/complexContent>
 *                                                             &lt;/complexType>
 *                                                           &lt;/element>
 *                                                           &lt;element name="statusCode">
 *                                                             &lt;complexType>
 *                                                               &lt;complexContent>
 *                                                                 &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *                                                                   &lt;attribute name="code" use="required" type="{http://www.w3.org/2001/XMLSchema}string" />
 *                                                                 &lt;/restriction>
 *                                                               &lt;/complexContent>
 *                                                             &lt;/complexType>
 *                                                           &lt;/element>
 *                                                           &lt;element name="patientPerson">
 *                                                             &lt;complexType>
 *                                                               &lt;complexContent>
 *                                                                 &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *                                                                   &lt;sequence>
 *                                                                     &lt;element name="name">
 *                                                                       &lt;complexType>
 *                                                                         &lt;complexContent>
 *                                                                           &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *                                                                             &lt;sequence>
 *                                                                               &lt;element name="given" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *                                                                             &lt;/sequence>
 *                                                                             &lt;attribute name="use" use="required" type="{http://www.w3.org/2001/XMLSchema}string" />
 *                                                                           &lt;/restriction>
 *                                                                         &lt;/complexContent>
 *                                                                       &lt;/complexType>
 *                                                                     &lt;/element>
 *                                                                     &lt;element name="administrativeGenderCode">
 *                                                                       &lt;complexType>
 *                                                                         &lt;complexContent>
 *                                                                           &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *                                                                             &lt;attribute name="code" use="required" type="{http://www.w3.org/2001/XMLSchema}string" />
 *                                                                             &lt;attribute name="codeSystem" use="required" type="{http://www.w3.org/2001/XMLSchema}string" />
 *                                                                           &lt;/restriction>
 *                                                                         &lt;/complexContent>
 *                                                                       &lt;/complexType>
 *                                                                     &lt;/element>
 *                                                                     &lt;element name="birthTime">
 *                                                                       &lt;complexType>
 *                                                                         &lt;complexContent>
 *                                                                           &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *                                                                             &lt;attribute name="value" use="required" type="{http://www.w3.org/2001/XMLSchema}unsignedLong" />
 *                                                                           &lt;/restriction>
 *                                                                         &lt;/complexContent>
 *                                                                       &lt;/complexType>
 *                                                                     &lt;/element>
 *                                                                     &lt;element name="addr">
 *                                                                       &lt;complexType>
 *                                                                         &lt;complexContent>
 *                                                                           &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *                                                                             &lt;sequence>
 *                                                                               &lt;element name="city" type="{http://www.w3.org/2001/XMLSchema}unsignedInt"/>
 *                                                                               &lt;element name="state" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *                                                                               &lt;element name="postalCode" type="{http://www.w3.org/2001/XMLSchema}unsignedInt"/>
 *                                                                               &lt;element name="country" type="{http://www.w3.org/2001/XMLSchema}unsignedByte"/>
 *                                                                               &lt;element name="houseNumber" type="{http://www.w3.org/2001/XMLSchema}unsignedShort"/>
 *                                                                               &lt;element name="streetNameType" type="{http://www.w3.org/2001/XMLSchema}unsignedByte"/>
 *                                                                               &lt;element name="additionalLocator" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *                                                                             &lt;/sequence>
 *                                                                             &lt;attribute name="use" use="required" type="{http://www.w3.org/2001/XMLSchema}string" />
 *                                                                           &lt;/restriction>
 *                                                                         &lt;/complexContent>
 *                                                                       &lt;/complexType>
 *                                                                     &lt;/element>
 *                                                                     &lt;element name="maritalStatusCode">
 *                                                                       &lt;complexType>
 *                                                                         &lt;complexContent>
 *                                                                           &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *                                                                             &lt;attribute name="code" use="required" type="{http://www.w3.org/2001/XMLSchema}unsignedByte" />
 *                                                                           &lt;/restriction>
 *                                                                         &lt;/complexContent>
 *                                                                       &lt;/complexType>
 *                                                                     &lt;/element>
 *                                                                     &lt;element name="raceCode">
 *                                                                       &lt;complexType>
 *                                                                         &lt;complexContent>
 *                                                                           &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *                                                                             &lt;attribute name="code" use="required" type="{http://www.w3.org/2001/XMLSchema}unsignedByte" />
 *                                                                           &lt;/restriction>
 *                                                                         &lt;/complexContent>
 *                                                                       &lt;/complexType>
 *                                                                     &lt;/element>
 *                                                                     &lt;element name="asOtherIDs" maxOccurs="unbounded">
 *                                                                       &lt;complexType>
 *                                                                         &lt;complexContent>
 *                                                                           &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *                                                                             &lt;sequence>
 *                                                                               &lt;element name="id" maxOccurs="unbounded">
 *                                                                                 &lt;complexType>
 *                                                                                   &lt;complexContent>
 *                                                                                     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *                                                                                       &lt;attribute name="root" use="required" type="{http://www.w3.org/2001/XMLSchema}string" />
 *                                                                                       &lt;attribute name="extension" use="required" type="{http://www.w3.org/2001/XMLSchema}string" />
 *                                                                                     &lt;/restriction>
 *                                                                                   &lt;/complexContent>
 *                                                                                 &lt;/complexType>
 *                                                                               &lt;/element>
 *                                                                               &lt;element name="scopingOrganization">
 *                                                                                 &lt;complexType>
 *                                                                                   &lt;complexContent>
 *                                                                                     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *                                                                                       &lt;sequence>
 *                                                                                         &lt;element name="id" maxOccurs="unbounded">
 *                                                                                           &lt;complexType>
 *                                                                                             &lt;complexContent>
 *                                                                                               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *                                                                                                 &lt;attribute name="root" use="required" type="{http://www.w3.org/2001/XMLSchema}string" />
 *                                                                                               &lt;/restriction>
 *                                                                                             &lt;/complexContent>
 *                                                                                           &lt;/complexType>
 *                                                                                         &lt;/element>
 *                                                                                       &lt;/sequence>
 *                                                                                       &lt;attribute name="classCode" use="required" type="{http://www.w3.org/2001/XMLSchema}string" />
 *                                                                                       &lt;attribute name="determinerCode" use="required" type="{http://www.w3.org/2001/XMLSchema}string" />
 *                                                                                     &lt;/restriction>
 *                                                                                   &lt;/complexContent>
 *                                                                                 &lt;/complexType>
 *                                                                               &lt;/element>
 *                                                                             &lt;/sequence>
 *                                                                             &lt;attribute name="classCode" use="required" type="{http://www.w3.org/2001/XMLSchema}string" />
 *                                                                           &lt;/restriction>
 *                                                                         &lt;/complexContent>
 *                                                                       &lt;/complexType>
 *                                                                     &lt;/element>
 *                                                                     &lt;element name="personalRelationship" maxOccurs="unbounded">
 *                                                                       &lt;complexType>
 *                                                                         &lt;complexContent>
 *                                                                           &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *                                                                             &lt;sequence>
 *                                                                               &lt;element name="code">
 *                                                                                 &lt;complexType>
 *                                                                                   &lt;complexContent>
 *                                                                                     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *                                                                                       &lt;attribute name="code" use="required" type="{http://www.w3.org/2001/XMLSchema}string" />
 *                                                                                       &lt;attribute name="codeSystem" use="required" type="{http://www.w3.org/2001/XMLSchema}string" />
 *                                                                                     &lt;/restriction>
 *                                                                                   &lt;/complexContent>
 *                                                                                 &lt;/complexType>
 *                                                                               &lt;/element>
 *                                                                               &lt;element name="relationshipHolder1">
 *                                                                                 &lt;complexType>
 *                                                                                   &lt;complexContent>
 *                                                                                     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *                                                                                       &lt;sequence>
 *                                                                                         &lt;element name="name">
 *                                                                                           &lt;complexType>
 *                                                                                             &lt;complexContent>
 *                                                                                               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *                                                                                                 &lt;sequence>
 *                                                                                                   &lt;element name="given" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *                                                                                                 &lt;/sequence>
 *                                                                                                 &lt;attribute name="use" use="required" type="{http://www.w3.org/2001/XMLSchema}string" />
 *                                                                                               &lt;/restriction>
 *                                                                                             &lt;/complexContent>
 *                                                                                           &lt;/complexType>
 *                                                                                         &lt;/element>
 *                                                                                       &lt;/sequence>
 *                                                                                       &lt;attribute name="classCode" use="required" type="{http://www.w3.org/2001/XMLSchema}string" />
 *                                                                                       &lt;attribute name="determinerCode" use="required" type="{http://www.w3.org/2001/XMLSchema}string" />
 *                                                                                     &lt;/restriction>
 *                                                                                   &lt;/complexContent>
 *                                                                                 &lt;/complexType>
 *                                                                               &lt;/element>
 *                                                                             &lt;/sequence>
 *                                                                             &lt;attribute name="classCode" use="required" type="{http://www.w3.org/2001/XMLSchema}string" />
 *                                                                           &lt;/restriction>
 *                                                                         &lt;/complexContent>
 *                                                                       &lt;/complexType>
 *                                                                     &lt;/element>
 *                                                                     &lt;element name="birthPlace">
 *                                                                       &lt;complexType>
 *                                                                         &lt;complexContent>
 *                                                                           &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *                                                                             &lt;sequence>
 *                                                                               &lt;element name="addr">
 *                                                                                 &lt;complexType>
 *                                                                                   &lt;complexContent>
 *                                                                                     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *                                                                                       &lt;sequence>
 *                                                                                         &lt;element name="city" type="{http://www.w3.org/2001/XMLSchema}unsignedInt"/>
 *                                                                                         &lt;element name="country" type="{http://www.w3.org/2001/XMLSchema}unsignedByte"/>
 *                                                                                       &lt;/sequence>
 *                                                                                     &lt;/restriction>
 *                                                                                   &lt;/complexContent>
 *                                                                                 &lt;/complexType>
 *                                                                               &lt;/element>
 *                                                                             &lt;/sequence>
 *                                                                             &lt;attribute name="classCode" use="required" type="{http://www.w3.org/2001/XMLSchema}string" />
 *                                                                           &lt;/restriction>
 *                                                                         &lt;/complexContent>
 *                                                                       &lt;/complexType>
 *                                                                     &lt;/element>
 *                                                                   &lt;/sequence>
 *                                                                   &lt;attribute name="classCode" use="required" type="{http://www.w3.org/2001/XMLSchema}string" />
 *                                                                   &lt;attribute name="determinerCode" use="required" type="{http://www.w3.org/2001/XMLSchema}string" />
 *                                                                 &lt;/restriction>
 *                                                               &lt;/complexContent>
 *                                                             &lt;/complexType>
 *                                                           &lt;/element>
 *                                                           &lt;element name="providerOrganization">
 *                                                             &lt;complexType>
 *                                                               &lt;complexContent>
 *                                                                 &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *                                                                   &lt;sequence>
 *                                                                     &lt;element name="id" maxOccurs="unbounded">
 *                                                                       &lt;complexType>
 *                                                                         &lt;complexContent>
 *                                                                           &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *                                                                             &lt;attribute name="root" use="required" type="{http://www.w3.org/2001/XMLSchema}string" />
 *                                                                           &lt;/restriction>
 *                                                                         &lt;/complexContent>
 *                                                                       &lt;/complexType>
 *                                                                     &lt;/element>
 *                                                                     &lt;element name="contactParty">
 *                                                                       &lt;complexType>
 *                                                                         &lt;complexContent>
 *                                                                           &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *                                                                             &lt;attribute name="classCode" use="required" type="{http://www.w3.org/2001/XMLSchema}string" />
 *                                                                           &lt;/restriction>
 *                                                                         &lt;/complexContent>
 *                                                                       &lt;/complexType>
 *                                                                     &lt;/element>
 *                                                                   &lt;/sequence>
 *                                                                   &lt;attribute name="classCode" use="required" type="{http://www.w3.org/2001/XMLSchema}string" />
 *                                                                   &lt;attribute name="determinerCode" use="required" type="{http://www.w3.org/2001/XMLSchema}string" />
 *                                                                 &lt;/restriction>
 *                                                               &lt;/complexContent>
 *                                                             &lt;/complexType>
 *                                                           &lt;/element>
 *                                                           &lt;element name="subjectOf1">
 *                                                             &lt;complexType>
 *                                                               &lt;complexContent>
 *                                                                 &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *                                                                   &lt;sequence>
 *                                                                     &lt;element name="queryMatchObservation">
 *                                                                       &lt;complexType>
 *                                                                         &lt;complexContent>
 *                                                                           &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *                                                                             &lt;sequence>
 *                                                                               &lt;element name="code">
 *                                                                                 &lt;complexType>
 *                                                                                   &lt;complexContent>
 *                                                                                     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *                                                                                       &lt;attribute name="code" use="required" type="{http://www.w3.org/2001/XMLSchema}string" />
 *                                                                                     &lt;/restriction>
 *                                                                                   &lt;/complexContent>
 *                                                                                 &lt;/complexType>
 *                                                                               &lt;/element>
 *                                                                               &lt;element name="value">
 *                                                                                 &lt;complexType>
 *                                                                                   &lt;complexContent>
 *                                                                                     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *                                                                                       &lt;attribute name="value" use="required" type="{http://www.w3.org/2001/XMLSchema}unsignedByte" />
 *                                                                                     &lt;/restriction>
 *                                                                                   &lt;/complexContent>
 *                                                                                 &lt;/complexType>
 *                                                                               &lt;/element>
 *                                                                             &lt;/sequence>
 *                                                                             &lt;attribute name="classCode" use="required" type="{http://www.w3.org/2001/XMLSchema}string" />
 *                                                                             &lt;attribute name="moodCode" use="required" type="{http://www.w3.org/2001/XMLSchema}string" />
 *                                                                           &lt;/restriction>
 *                                                                         &lt;/complexContent>
 *                                                                       &lt;/complexType>
 *                                                                     &lt;/element>
 *                                                                   &lt;/sequence>
 *                                                                 &lt;/restriction>
 *                                                               &lt;/complexContent>
 *                                                             &lt;/complexType>
 *                                                           &lt;/element>
 *                                                         &lt;/sequence>
 *                                                         &lt;attribute name="classCode" use="required" type="{http://www.w3.org/2001/XMLSchema}string" />
 *                                                       &lt;/restriction>
 *                                                     &lt;/complexContent>
 *                                                   &lt;/complexType>
 *                                                 &lt;/element>
 *                                               &lt;/sequence>
 *                                               &lt;attribute name="typeCode" use="required" type="{http://www.w3.org/2001/XMLSchema}string" />
 *                                             &lt;/restriction>
 *                                           &lt;/complexContent>
 *                                         &lt;/complexType>
 *                                       &lt;/element>
 *                                       &lt;element name="custodian">
 *                                         &lt;complexType>
 *                                           &lt;complexContent>
 *                                             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *                                               &lt;sequence>
 *                                                 &lt;element name="assignedEntity">
 *                                                   &lt;complexType>
 *                                                     &lt;complexContent>
 *                                                       &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *                                                         &lt;sequence>
 *                                                           &lt;element name="id">
 *                                                             &lt;complexType>
 *                                                               &lt;complexContent>
 *                                                                 &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *                                                                   &lt;attribute name="root" use="required" type="{http://www.w3.org/2001/XMLSchema}string" />
 *                                                                 &lt;/restriction>
 *                                                               &lt;/complexContent>
 *                                                             &lt;/complexType>
 *                                                           &lt;/element>
 *                                                         &lt;/sequence>
 *                                                         &lt;attribute name="classCode" use="required" type="{http://www.w3.org/2001/XMLSchema}string" />
 *                                                       &lt;/restriction>
 *                                                     &lt;/complexContent>
 *                                                   &lt;/complexType>
 *                                                 &lt;/element>
 *                                               &lt;/sequence>
 *                                               &lt;attribute name="typeCode" use="required" type="{http://www.w3.org/2001/XMLSchema}string" />
 *                                             &lt;/restriction>
 *                                           &lt;/complexContent>
 *                                         &lt;/complexType>
 *                                       &lt;/element>
 *                                     &lt;/sequence>
 *                                     &lt;attribute name="classCode" use="required" type="{http://www.w3.org/2001/XMLSchema}string" />
 *                                     &lt;attribute name="moodCode" use="required" type="{http://www.w3.org/2001/XMLSchema}string" />
 *                                   &lt;/restriction>
 *                                 &lt;/complexContent>
 *                               &lt;/complexType>
 *                             &lt;/element>
 *                           &lt;/sequence>
 *                           &lt;attribute name="typeCode" use="required" type="{http://www.w3.org/2001/XMLSchema}string" />
 *                           &lt;attribute name="contextConductionInd" use="required" type="{http://www.w3.org/2001/XMLSchema}boolean" />
 *                         &lt;/restriction>
 *                       &lt;/complexContent>
 *                     &lt;/complexType>
 *                   &lt;/element>
 *                   &lt;element name="queryAck">
 *                     &lt;complexType>
 *                       &lt;complexContent>
 *                         &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *                           &lt;sequence>
 *                             &lt;element name="queryId">
 *                               &lt;complexType>
 *                                 &lt;complexContent>
 *                                   &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *                                     &lt;attribute name="root" use="required" type="{http://www.w3.org/2001/XMLSchema}string" />
 *                                     &lt;attribute name="extension" use="required" type="{http://www.w3.org/2001/XMLSchema}unsignedLong" />
 *                                   &lt;/restriction>
 *                                 &lt;/complexContent>
 *                               &lt;/complexType>
 *                             &lt;/element>
 *                             &lt;element name="queryResponseCode">
 *                               &lt;complexType>
 *                                 &lt;complexContent>
 *                                   &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *                                     &lt;attribute name="code" use="required" type="{http://www.w3.org/2001/XMLSchema}string" />
 *                                   &lt;/restriction>
 *                                 &lt;/complexContent>
 *                               &lt;/complexType>
 *                             &lt;/element>
 *                             &lt;element name="resultTotalQuantity">
 *                               &lt;complexType>
 *                                 &lt;complexContent>
 *                                   &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *                                     &lt;attribute name="value" use="required" type="{http://www.w3.org/2001/XMLSchema}unsignedByte" />
 *                                   &lt;/restriction>
 *                                 &lt;/complexContent>
 *                               &lt;/complexType>
 *                             &lt;/element>
 *                             &lt;element name="resultCurrentQuantity">
 *                               &lt;complexType>
 *                                 &lt;complexContent>
 *                                   &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *                                     &lt;attribute name="value" use="required" type="{http://www.w3.org/2001/XMLSchema}unsignedByte" />
 *                                   &lt;/restriction>
 *                                 &lt;/complexContent>
 *                               &lt;/complexType>
 *                             &lt;/element>
 *                             &lt;element name="resultRemainingQuantity">
 *                               &lt;complexType>
 *                                 &lt;complexContent>
 *                                   &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *                                     &lt;attribute name="value" use="required" type="{http://www.w3.org/2001/XMLSchema}unsignedByte" />
 *                                   &lt;/restriction>
 *                                 &lt;/complexContent>
 *                               &lt;/complexType>
 *                             &lt;/element>
 *                           &lt;/sequence>
 *                         &lt;/restriction>
 *                       &lt;/complexContent>
 *                     &lt;/complexType>
 *                   &lt;/element>
 *                   &lt;element name="queryByParameter">
 *                     &lt;complexType>
 *                       &lt;complexContent>
 *                         &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *                           &lt;sequence>
 *                             &lt;element name="queryId">
 *                               &lt;complexType>
 *                                 &lt;complexContent>
 *                                   &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *                                     &lt;attribute name="root" use="required" type="{http://www.w3.org/2001/XMLSchema}string" />
 *                                     &lt;attribute name="extension" use="required" type="{http://www.w3.org/2001/XMLSchema}unsignedLong" />
 *                                   &lt;/restriction>
 *                                 &lt;/complexContent>
 *                               &lt;/complexType>
 *                             &lt;/element>
 *                             &lt;element name="statusCode">
 *                               &lt;complexType>
 *                                 &lt;complexContent>
 *                                   &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *                                     &lt;attribute name="code" use="required" type="{http://www.w3.org/2001/XMLSchema}string" />
 *                                   &lt;/restriction>
 *                                 &lt;/complexContent>
 *                               &lt;/complexType>
 *                             &lt;/element>
 *                             &lt;element name="responseModalityCode">
 *                               &lt;complexType>
 *                                 &lt;complexContent>
 *                                   &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *                                     &lt;attribute name="code" use="required" type="{http://www.w3.org/2001/XMLSchema}string" />
 *                                   &lt;/restriction>
 *                                 &lt;/complexContent>
 *                               &lt;/complexType>
 *                             &lt;/element>
 *                             &lt;element name="responsePriorityCode">
 *                               &lt;complexType>
 *                                 &lt;complexContent>
 *                                   &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *                                     &lt;attribute name="code" use="required" type="{http://www.w3.org/2001/XMLSchema}string" />
 *                                   &lt;/restriction>
 *                                 &lt;/complexContent>
 *                               &lt;/complexType>
 *                             &lt;/element>
 *                             &lt;element name="parameterList">
 *                               &lt;complexType>
 *                                 &lt;complexContent>
 *                                   &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *                                     &lt;sequence>
 *                                       &lt;element name="livingSubjectId">
 *                                         &lt;complexType>
 *                                           &lt;complexContent>
 *                                             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *                                               &lt;sequence>
 *                                                 &lt;element name="value">
 *                                                   &lt;complexType>
 *                                                     &lt;complexContent>
 *                                                       &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *                                                         &lt;attribute name="root" use="required" type="{http://www.w3.org/2001/XMLSchema}string" />
 *                                                         &lt;attribute name="extension" use="required" type="{http://www.w3.org/2001/XMLSchema}unsignedLong" />
 *                                                       &lt;/restriction>
 *                                                     &lt;/complexContent>
 *                                                   &lt;/complexType>
 *                                                 &lt;/element>
 *                                                 &lt;element name="semanticsText" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *                                               &lt;/sequence>
 *                                             &lt;/restriction>
 *                                           &lt;/complexContent>
 *                                         &lt;/complexType>
 *                                       &lt;/element>
 *                                     &lt;/sequence>
 *                                   &lt;/restriction>
 *                                 &lt;/complexContent>
 *                               &lt;/complexType>
 *                             &lt;/element>
 *                           &lt;/sequence>
 *                         &lt;/restriction>
 *                       &lt;/complexContent>
 *                     &lt;/complexType>
 *                   &lt;/element>
 *                 &lt;/sequence>
 *                 &lt;attribute name="classCode" use="required" type="{http://www.w3.org/2001/XMLSchema}string" />
 *                 &lt;attribute name="moodCode" use="required" type="{http://www.w3.org/2001/XMLSchema}string" />
 *               &lt;/restriction>
 *             &lt;/complexContent>
 *           &lt;/complexType>
 *         &lt;/element>
 *       &lt;/sequence>
 *       &lt;attribute name="ITSVersion" use="required" type="{http://www.w3.org/2001/XMLSchema}string" />
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "", propOrder = {
    "id",
    "creationTime",
    "interactionId",
    "processingCode",
    "processingModeCode",
    "acceptAckCode",
    "receiver",
    "sender",
    "acknowledgement",
    "controlActProcess"
})
@XmlRootElement(name = "PRPA_IN201306UV02")
public class PRPAIN201306UV02 {

    @XmlElement(required = true)
    protected PRPAIN201306UV02 .Id id;
    @XmlElement(required = true)
    protected PRPAIN201306UV02 .CreationTime creationTime;
    @XmlElement(required = true)
    protected PRPAIN201306UV02 .InteractionId interactionId;
    @XmlElement(required = true)
    protected PRPAIN201306UV02 .ProcessingCode processingCode;
    @XmlElement(required = true)
    protected PRPAIN201306UV02 .ProcessingModeCode processingModeCode;
    @XmlElement(required = true)
    protected PRPAIN201306UV02 .AcceptAckCode acceptAckCode;
    @XmlElement(required = true)
    protected PRPAIN201306UV02 .Receiver receiver;
    @XmlElement(required = true)
    protected PRPAIN201306UV02 .Sender sender;
    @XmlElement(required = true)
    protected PRPAIN201306UV02 .Acknowledgement acknowledgement;
    @XmlElement(required = true)
    protected PRPAIN201306UV02 .ControlActProcess controlActProcess;
    @XmlAttribute(name = "ITSVersion", required = true)
    protected String itsVersion;

    /**
     * Obtém o valor da propriedade id.
     * 
     * @return
     *     possible object is
     *     {@link PRPAIN201306UV02 .Id }
     *     
     */
    public PRPAIN201306UV02 .Id getId() {
        return id;
    }

    /**
     * Define o valor da propriedade id.
     * 
     * @param value
     *     allowed object is
     *     {@link PRPAIN201306UV02 .Id }
     *     
     */
    public void setId(PRPAIN201306UV02 .Id value) {
        this.id = value;
    }

    /**
     * Obtém o valor da propriedade creationTime.
     * 
     * @return
     *     possible object is
     *     {@link PRPAIN201306UV02 .CreationTime }
     *     
     */
    public PRPAIN201306UV02 .CreationTime getCreationTime() {
        return creationTime;
    }

    /**
     * Define o valor da propriedade creationTime.
     * 
     * @param value
     *     allowed object is
     *     {@link PRPAIN201306UV02 .CreationTime }
     *     
     */
    public void setCreationTime(PRPAIN201306UV02 .CreationTime value) {
        this.creationTime = value;
    }

    /**
     * Obtém o valor da propriedade interactionId.
     * 
     * @return
     *     possible object is
     *     {@link PRPAIN201306UV02 .InteractionId }
     *     
     */
    public PRPAIN201306UV02 .InteractionId getInteractionId() {
        return interactionId;
    }

    /**
     * Define o valor da propriedade interactionId.
     * 
     * @param value
     *     allowed object is
     *     {@link PRPAIN201306UV02 .InteractionId }
     *     
     */
    public void setInteractionId(PRPAIN201306UV02 .InteractionId value) {
        this.interactionId = value;
    }

    /**
     * Obtém o valor da propriedade processingCode.
     * 
     * @return
     *     possible object is
     *     {@link PRPAIN201306UV02 .ProcessingCode }
     *     
     */
    public PRPAIN201306UV02 .ProcessingCode getProcessingCode() {
        return processingCode;
    }

    /**
     * Define o valor da propriedade processingCode.
     * 
     * @param value
     *     allowed object is
     *     {@link PRPAIN201306UV02 .ProcessingCode }
     *     
     */
    public void setProcessingCode(PRPAIN201306UV02 .ProcessingCode value) {
        this.processingCode = value;
    }

    /**
     * Obtém o valor da propriedade processingModeCode.
     * 
     * @return
     *     possible object is
     *     {@link PRPAIN201306UV02 .ProcessingModeCode }
     *     
     */
    public PRPAIN201306UV02 .ProcessingModeCode getProcessingModeCode() {
        return processingModeCode;
    }

    /**
     * Define o valor da propriedade processingModeCode.
     * 
     * @param value
     *     allowed object is
     *     {@link PRPAIN201306UV02 .ProcessingModeCode }
     *     
     */
    public void setProcessingModeCode(PRPAIN201306UV02 .ProcessingModeCode value) {
        this.processingModeCode = value;
    }

    /**
     * Obtém o valor da propriedade acceptAckCode.
     * 
     * @return
     *     possible object is
     *     {@link PRPAIN201306UV02 .AcceptAckCode }
     *     
     */
    public PRPAIN201306UV02 .AcceptAckCode getAcceptAckCode() {
        return acceptAckCode;
    }

    /**
     * Define o valor da propriedade acceptAckCode.
     * 
     * @param value
     *     allowed object is
     *     {@link PRPAIN201306UV02 .AcceptAckCode }
     *     
     */
    public void setAcceptAckCode(PRPAIN201306UV02 .AcceptAckCode value) {
        this.acceptAckCode = value;
    }

    /**
     * Obtém o valor da propriedade receiver.
     * 
     * @return
     *     possible object is
     *     {@link PRPAIN201306UV02 .Receiver }
     *     
     */
    public PRPAIN201306UV02 .Receiver getReceiver() {
        return receiver;
    }

    /**
     * Define o valor da propriedade receiver.
     * 
     * @param value
     *     allowed object is
     *     {@link PRPAIN201306UV02 .Receiver }
     *     
     */
    public void setReceiver(PRPAIN201306UV02 .Receiver value) {
        this.receiver = value;
    }

    /**
     * Obtém o valor da propriedade sender.
     * 
     * @return
     *     possible object is
     *     {@link PRPAIN201306UV02 .Sender }
     *     
     */
    public PRPAIN201306UV02 .Sender getSender() {
        return sender;
    }

    /**
     * Define o valor da propriedade sender.
     * 
     * @param value
     *     allowed object is
     *     {@link PRPAIN201306UV02 .Sender }
     *     
     */
    public void setSender(PRPAIN201306UV02 .Sender value) {
        this.sender = value;
    }

    /**
     * Obtém o valor da propriedade acknowledgement.
     * 
     * @return
     *     possible object is
     *     {@link PRPAIN201306UV02 .Acknowledgement }
     *     
     */
    public PRPAIN201306UV02 .Acknowledgement getAcknowledgement() {
        return acknowledgement;
    }

    /**
     * Define o valor da propriedade acknowledgement.
     * 
     * @param value
     *     allowed object is
     *     {@link PRPAIN201306UV02 .Acknowledgement }
     *     
     */
    public void setAcknowledgement(PRPAIN201306UV02 .Acknowledgement value) {
        this.acknowledgement = value;
    }

    /**
     * Obtém o valor da propriedade controlActProcess.
     * 
     * @return
     *     possible object is
     *     {@link PRPAIN201306UV02 .ControlActProcess }
     *     
     */
    public PRPAIN201306UV02 .ControlActProcess getControlActProcess() {
        return controlActProcess;
    }

    /**
     * Define o valor da propriedade controlActProcess.
     * 
     * @param value
     *     allowed object is
     *     {@link PRPAIN201306UV02 .ControlActProcess }
     *     
     */
    public void setControlActProcess(PRPAIN201306UV02 .ControlActProcess value) {
        this.controlActProcess = value;
    }

    /**
     * Obtém o valor da propriedade itsVersion.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getITSVersion() {
        return itsVersion;
    }

    /**
     * Define o valor da propriedade itsVersion.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setITSVersion(String value) {
        this.itsVersion = value;
    }


    /**
     * <p>Classe Java de anonymous complex type.
     * 
     * <p>O seguinte fragmento do esquema especifica o conteúdo esperado contido dentro desta classe.
     * 
     * <pre>
     * &lt;complexType>
     *   &lt;complexContent>
     *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
     *       &lt;attribute name="code" use="required" type="{http://www.w3.org/2001/XMLSchema}string" />
     *     &lt;/restriction>
     *   &lt;/complexContent>
     * &lt;/complexType>
     * </pre>
     * 
     * 
     */
    @XmlAccessorType(XmlAccessType.FIELD)
    @XmlType(name = "")
    public static class AcceptAckCode {

        @XmlAttribute(name = "code", required = true)
        protected String code;

        /**
         * Obtém o valor da propriedade code.
         * 
         * @return
         *     possible object is
         *     {@link String }
         *     
         */
        public String getCode() {
            return code;
        }

        /**
         * Define o valor da propriedade code.
         * 
         * @param value
         *     allowed object is
         *     {@link String }
         *     
         */
        public void setCode(String value) {
            this.code = value;
        }

    }


    /**
     * <p>Classe Java de anonymous complex type.
     * 
     * <p>O seguinte fragmento do esquema especifica o conteúdo esperado contido dentro desta classe.
     * 
     * <pre>
     * &lt;complexType>
     *   &lt;complexContent>
     *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
     *       &lt;sequence>
     *         &lt;element name="typeCode">
     *           &lt;complexType>
     *             &lt;complexContent>
     *               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
     *                 &lt;attribute name="code" use="required" type="{http://www.w3.org/2001/XMLSchema}string" />
     *               &lt;/restriction>
     *             &lt;/complexContent>
     *           &lt;/complexType>
     *         &lt;/element>
     *         &lt;element name="targetMessage">
     *           &lt;complexType>
     *             &lt;complexContent>
     *               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
     *                 &lt;sequence>
     *                   &lt;element name="id">
     *                     &lt;complexType>
     *                       &lt;complexContent>
     *                         &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
     *                           &lt;attribute name="root" use="required" type="{http://www.w3.org/2001/XMLSchema}string" />
     *                           &lt;attribute name="extension" use="required" type="{http://www.w3.org/2001/XMLSchema}unsignedInt" />
     *                         &lt;/restriction>
     *                       &lt;/complexContent>
     *                     &lt;/complexType>
     *                   &lt;/element>
     *                 &lt;/sequence>
     *               &lt;/restriction>
     *             &lt;/complexContent>
     *           &lt;/complexType>
     *         &lt;/element>
     *       &lt;/sequence>
     *     &lt;/restriction>
     *   &lt;/complexContent>
     * &lt;/complexType>
     * </pre>
     * 
     * 
     */
    @XmlAccessorType(XmlAccessType.FIELD)
    @XmlType(name = "", propOrder = {
        "typeCode",
        "targetMessage"
    })
    public static class Acknowledgement {

        @XmlElement(required = true)
        protected PRPAIN201306UV02 .Acknowledgement.TypeCode typeCode;
        @XmlElement(required = true)
        protected PRPAIN201306UV02 .Acknowledgement.TargetMessage targetMessage;

        /**
         * Obtém o valor da propriedade typeCode.
         * 
         * @return
         *     possible object is
         *     {@link PRPAIN201306UV02 .Acknowledgement.TypeCode }
         *     
         */
        public PRPAIN201306UV02 .Acknowledgement.TypeCode getTypeCode() {
            return typeCode;
        }

        /**
         * Define o valor da propriedade typeCode.
         * 
         * @param value
         *     allowed object is
         *     {@link PRPAIN201306UV02 .Acknowledgement.TypeCode }
         *     
         */
        public void setTypeCode(PRPAIN201306UV02 .Acknowledgement.TypeCode value) {
            this.typeCode = value;
        }

        /**
         * Obtém o valor da propriedade targetMessage.
         * 
         * @return
         *     possible object is
         *     {@link PRPAIN201306UV02 .Acknowledgement.TargetMessage }
         *     
         */
        public PRPAIN201306UV02 .Acknowledgement.TargetMessage getTargetMessage() {
            return targetMessage;
        }

        /**
         * Define o valor da propriedade targetMessage.
         * 
         * @param value
         *     allowed object is
         *     {@link PRPAIN201306UV02 .Acknowledgement.TargetMessage }
         *     
         */
        public void setTargetMessage(PRPAIN201306UV02 .Acknowledgement.TargetMessage value) {
            this.targetMessage = value;
        }


        /**
         * <p>Classe Java de anonymous complex type.
         * 
         * <p>O seguinte fragmento do esquema especifica o conteúdo esperado contido dentro desta classe.
         * 
         * <pre>
         * &lt;complexType>
         *   &lt;complexContent>
         *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
         *       &lt;sequence>
         *         &lt;element name="id">
         *           &lt;complexType>
         *             &lt;complexContent>
         *               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
         *                 &lt;attribute name="root" use="required" type="{http://www.w3.org/2001/XMLSchema}string" />
         *                 &lt;attribute name="extension" use="required" type="{http://www.w3.org/2001/XMLSchema}unsignedInt" />
         *               &lt;/restriction>
         *             &lt;/complexContent>
         *           &lt;/complexType>
         *         &lt;/element>
         *       &lt;/sequence>
         *     &lt;/restriction>
         *   &lt;/complexContent>
         * &lt;/complexType>
         * </pre>
         * 
         * 
         */
        @XmlAccessorType(XmlAccessType.FIELD)
        @XmlType(name = "", propOrder = {
            "id"
        })
        public static class TargetMessage {

            @XmlElement(required = true)
            protected PRPAIN201306UV02 .Acknowledgement.TargetMessage.Id id;

            /**
             * Obtém o valor da propriedade id.
             * 
             * @return
             *     possible object is
             *     {@link PRPAIN201306UV02 .Acknowledgement.TargetMessage.Id }
             *     
             */
            public PRPAIN201306UV02 .Acknowledgement.TargetMessage.Id getId() {
                return id;
            }

            /**
             * Define o valor da propriedade id.
             * 
             * @param value
             *     allowed object is
             *     {@link PRPAIN201306UV02 .Acknowledgement.TargetMessage.Id }
             *     
             */
            public void setId(PRPAIN201306UV02 .Acknowledgement.TargetMessage.Id value) {
                this.id = value;
            }


            /**
             * <p>Classe Java de anonymous complex type.
             * 
             * <p>O seguinte fragmento do esquema especifica o conteúdo esperado contido dentro desta classe.
             * 
             * <pre>
             * &lt;complexType>
             *   &lt;complexContent>
             *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
             *       &lt;attribute name="root" use="required" type="{http://www.w3.org/2001/XMLSchema}string" />
             *       &lt;attribute name="extension" use="required" type="{http://www.w3.org/2001/XMLSchema}unsignedInt" />
             *     &lt;/restriction>
             *   &lt;/complexContent>
             * &lt;/complexType>
             * </pre>
             * 
             * 
             */
            @XmlAccessorType(XmlAccessType.FIELD)
            @XmlType(name = "")
            public static class Id {

                @XmlAttribute(name = "root", required = true)
                protected String root;
                @XmlAttribute(name = "extension", required = true)
                @XmlSchemaType(name = "unsignedInt")
                protected long extension;

                /**
                 * Obtém o valor da propriedade root.
                 * 
                 * @return
                 *     possible object is
                 *     {@link String }
                 *     
                 */
                public String getRoot() {
                    return root;
                }

                /**
                 * Define o valor da propriedade root.
                 * 
                 * @param value
                 *     allowed object is
                 *     {@link String }
                 *     
                 */
                public void setRoot(String value) {
                    this.root = value;
                }

                /**
                 * Obtém o valor da propriedade extension.
                 * 
                 */
                public long getExtension() {
                    return extension;
                }

                /**
                 * Define o valor da propriedade extension.
                 * 
                 */
                public void setExtension(long value) {
                    this.extension = value;
                }

            }

        }


        /**
         * <p>Classe Java de anonymous complex type.
         * 
         * <p>O seguinte fragmento do esquema especifica o conteúdo esperado contido dentro desta classe.
         * 
         * <pre>
         * &lt;complexType>
         *   &lt;complexContent>
         *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
         *       &lt;attribute name="code" use="required" type="{http://www.w3.org/2001/XMLSchema}string" />
         *     &lt;/restriction>
         *   &lt;/complexContent>
         * &lt;/complexType>
         * </pre>
         * 
         * 
         */
        @XmlAccessorType(XmlAccessType.FIELD)
        @XmlType(name = "")
        public static class TypeCode {

            @XmlAttribute(name = "code", required = true)
            protected String code;

            /**
             * Obtém o valor da propriedade code.
             * 
             * @return
             *     possible object is
             *     {@link String }
             *     
             */
            public String getCode() {
                return code;
            }

            /**
             * Define o valor da propriedade code.
             * 
             * @param value
             *     allowed object is
             *     {@link String }
             *     
             */
            public void setCode(String value) {
                this.code = value;
            }

        }

    }


    /**
     * <p>Classe Java de anonymous complex type.
     * 
     * <p>O seguinte fragmento do esquema especifica o conteúdo esperado contido dentro desta classe.
     * 
     * <pre>
     * &lt;complexType>
     *   &lt;complexContent>
     *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
     *       &lt;sequence>
     *         &lt;element name="code">
     *           &lt;complexType>
     *             &lt;complexContent>
     *               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
     *                 &lt;attribute name="code" use="required" type="{http://www.w3.org/2001/XMLSchema}string" />
     *               &lt;/restriction>
     *             &lt;/complexContent>
     *           &lt;/complexType>
     *         &lt;/element>
     *         &lt;element name="subject">
     *           &lt;complexType>
     *             &lt;complexContent>
     *               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
     *                 &lt;sequence>
     *                   &lt;element name="registrationEvent">
     *                     &lt;complexType>
     *                       &lt;complexContent>
     *                         &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
     *                           &lt;sequence>
     *                             &lt;element name="statusCode">
     *                               &lt;complexType>
     *                                 &lt;complexContent>
     *                                   &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
     *                                     &lt;attribute name="code" use="required" type="{http://www.w3.org/2001/XMLSchema}string" />
     *                                   &lt;/restriction>
     *                                 &lt;/complexContent>
     *                               &lt;/complexType>
     *                             &lt;/element>
     *                             &lt;element name="subject1">
     *                               &lt;complexType>
     *                                 &lt;complexContent>
     *                                   &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
     *                                     &lt;sequence>
     *                                       &lt;element name="realmCode">
     *                                         &lt;complexType>
     *                                           &lt;complexContent>
     *                                             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
     *                                               &lt;attribute name="code" use="required" type="{http://www.w3.org/2001/XMLSchema}string" />
     *                                             &lt;/restriction>
     *                                           &lt;/complexContent>
     *                                         &lt;/complexType>
     *                                       &lt;/element>
     *                                       &lt;element name="patient">
     *                                         &lt;complexType>
     *                                           &lt;complexContent>
     *                                             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
     *                                               &lt;sequence>
     *                                                 &lt;element name="id" maxOccurs="unbounded">
     *                                                   &lt;complexType>
     *                                                     &lt;complexContent>
     *                                                       &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
     *                                                         &lt;attribute name="root" use="required" type="{http://www.w3.org/2001/XMLSchema}string" />
     *                                                         &lt;attribute name="extension" use="required" type="{http://www.w3.org/2001/XMLSchema}unsignedLong" />
     *                                                         &lt;attribute name="assigningAuthorityName" use="required" type="{http://www.w3.org/2001/XMLSchema}string" />
     *                                                       &lt;/restriction>
     *                                                     &lt;/complexContent>
     *                                                   &lt;/complexType>
     *                                                 &lt;/element>
     *                                                 &lt;element name="statusCode">
     *                                                   &lt;complexType>
     *                                                     &lt;complexContent>
     *                                                       &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
     *                                                         &lt;attribute name="code" use="required" type="{http://www.w3.org/2001/XMLSchema}string" />
     *                                                       &lt;/restriction>
     *                                                     &lt;/complexContent>
     *                                                   &lt;/complexType>
     *                                                 &lt;/element>
     *                                                 &lt;element name="patientPerson">
     *                                                   &lt;complexType>
     *                                                     &lt;complexContent>
     *                                                       &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
     *                                                         &lt;sequence>
     *                                                           &lt;element name="name">
     *                                                             &lt;complexType>
     *                                                               &lt;complexContent>
     *                                                                 &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
     *                                                                   &lt;sequence>
     *                                                                     &lt;element name="given" type="{http://www.w3.org/2001/XMLSchema}string"/>
     *                                                                   &lt;/sequence>
     *                                                                   &lt;attribute name="use" use="required" type="{http://www.w3.org/2001/XMLSchema}string" />
     *                                                                 &lt;/restriction>
     *                                                               &lt;/complexContent>
     *                                                             &lt;/complexType>
     *                                                           &lt;/element>
     *                                                           &lt;element name="administrativeGenderCode">
     *                                                             &lt;complexType>
     *                                                               &lt;complexContent>
     *                                                                 &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
     *                                                                   &lt;attribute name="code" use="required" type="{http://www.w3.org/2001/XMLSchema}string" />
     *                                                                   &lt;attribute name="codeSystem" use="required" type="{http://www.w3.org/2001/XMLSchema}string" />
     *                                                                 &lt;/restriction>
     *                                                               &lt;/complexContent>
     *                                                             &lt;/complexType>
     *                                                           &lt;/element>
     *                                                           &lt;element name="birthTime">
     *                                                             &lt;complexType>
     *                                                               &lt;complexContent>
     *                                                                 &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
     *                                                                   &lt;attribute name="value" use="required" type="{http://www.w3.org/2001/XMLSchema}unsignedLong" />
     *                                                                 &lt;/restriction>
     *                                                               &lt;/complexContent>
     *                                                             &lt;/complexType>
     *                                                           &lt;/element>
     *                                                           &lt;element name="addr">
     *                                                             &lt;complexType>
     *                                                               &lt;complexContent>
     *                                                                 &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
     *                                                                   &lt;sequence>
     *                                                                     &lt;element name="city" type="{http://www.w3.org/2001/XMLSchema}unsignedInt"/>
     *                                                                     &lt;element name="state" type="{http://www.w3.org/2001/XMLSchema}string"/>
     *                                                                     &lt;element name="postalCode" type="{http://www.w3.org/2001/XMLSchema}unsignedInt"/>
     *                                                                     &lt;element name="country" type="{http://www.w3.org/2001/XMLSchema}unsignedByte"/>
     *                                                                     &lt;element name="houseNumber" type="{http://www.w3.org/2001/XMLSchema}unsignedShort"/>
     *                                                                     &lt;element name="streetNameType" type="{http://www.w3.org/2001/XMLSchema}unsignedByte"/>
     *                                                                     &lt;element name="additionalLocator" type="{http://www.w3.org/2001/XMLSchema}string"/>
     *                                                                   &lt;/sequence>
     *                                                                   &lt;attribute name="use" use="required" type="{http://www.w3.org/2001/XMLSchema}string" />
     *                                                                 &lt;/restriction>
     *                                                               &lt;/complexContent>
     *                                                             &lt;/complexType>
     *                                                           &lt;/element>
     *                                                           &lt;element name="maritalStatusCode">
     *                                                             &lt;complexType>
     *                                                               &lt;complexContent>
     *                                                                 &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
     *                                                                   &lt;attribute name="code" use="required" type="{http://www.w3.org/2001/XMLSchema}unsignedByte" />
     *                                                                 &lt;/restriction>
     *                                                               &lt;/complexContent>
     *                                                             &lt;/complexType>
     *                                                           &lt;/element>
     *                                                           &lt;element name="raceCode">
     *                                                             &lt;complexType>
     *                                                               &lt;complexContent>
     *                                                                 &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
     *                                                                   &lt;attribute name="code" use="required" type="{http://www.w3.org/2001/XMLSchema}unsignedByte" />
     *                                                                 &lt;/restriction>
     *                                                               &lt;/complexContent>
     *                                                             &lt;/complexType>
     *                                                           &lt;/element>
     *                                                           &lt;element name="asOtherIDs" maxOccurs="unbounded">
     *                                                             &lt;complexType>
     *                                                               &lt;complexContent>
     *                                                                 &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
     *                                                                   &lt;sequence>
     *                                                                     &lt;element name="id" maxOccurs="unbounded">
     *                                                                       &lt;complexType>
     *                                                                         &lt;complexContent>
     *                                                                           &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
     *                                                                             &lt;attribute name="root" use="required" type="{http://www.w3.org/2001/XMLSchema}string" />
     *                                                                             &lt;attribute name="extension" use="required" type="{http://www.w3.org/2001/XMLSchema}string" />
     *                                                                           &lt;/restriction>
     *                                                                         &lt;/complexContent>
     *                                                                       &lt;/complexType>
     *                                                                     &lt;/element>
     *                                                                     &lt;element name="scopingOrganization">
     *                                                                       &lt;complexType>
     *                                                                         &lt;complexContent>
     *                                                                           &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
     *                                                                             &lt;sequence>
     *                                                                               &lt;element name="id" maxOccurs="unbounded">
     *                                                                                 &lt;complexType>
     *                                                                                   &lt;complexContent>
     *                                                                                     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
     *                                                                                       &lt;attribute name="root" use="required" type="{http://www.w3.org/2001/XMLSchema}string" />
     *                                                                                     &lt;/restriction>
     *                                                                                   &lt;/complexContent>
     *                                                                                 &lt;/complexType>
     *                                                                               &lt;/element>
     *                                                                             &lt;/sequence>
     *                                                                             &lt;attribute name="classCode" use="required" type="{http://www.w3.org/2001/XMLSchema}string" />
     *                                                                             &lt;attribute name="determinerCode" use="required" type="{http://www.w3.org/2001/XMLSchema}string" />
     *                                                                           &lt;/restriction>
     *                                                                         &lt;/complexContent>
     *                                                                       &lt;/complexType>
     *                                                                     &lt;/element>
     *                                                                   &lt;/sequence>
     *                                                                   &lt;attribute name="classCode" use="required" type="{http://www.w3.org/2001/XMLSchema}string" />
     *                                                                 &lt;/restriction>
     *                                                               &lt;/complexContent>
     *                                                             &lt;/complexType>
     *                                                           &lt;/element>
     *                                                           &lt;element name="personalRelationship" maxOccurs="unbounded">
     *                                                             &lt;complexType>
     *                                                               &lt;complexContent>
     *                                                                 &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
     *                                                                   &lt;sequence>
     *                                                                     &lt;element name="code">
     *                                                                       &lt;complexType>
     *                                                                         &lt;complexContent>
     *                                                                           &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
     *                                                                             &lt;attribute name="code" use="required" type="{http://www.w3.org/2001/XMLSchema}string" />
     *                                                                             &lt;attribute name="codeSystem" use="required" type="{http://www.w3.org/2001/XMLSchema}string" />
     *                                                                           &lt;/restriction>
     *                                                                         &lt;/complexContent>
     *                                                                       &lt;/complexType>
     *                                                                     &lt;/element>
     *                                                                     &lt;element name="relationshipHolder1">
     *                                                                       &lt;complexType>
     *                                                                         &lt;complexContent>
     *                                                                           &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
     *                                                                             &lt;sequence>
     *                                                                               &lt;element name="name">
     *                                                                                 &lt;complexType>
     *                                                                                   &lt;complexContent>
     *                                                                                     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
     *                                                                                       &lt;sequence>
     *                                                                                         &lt;element name="given" type="{http://www.w3.org/2001/XMLSchema}string"/>
     *                                                                                       &lt;/sequence>
     *                                                                                       &lt;attribute name="use" use="required" type="{http://www.w3.org/2001/XMLSchema}string" />
     *                                                                                     &lt;/restriction>
     *                                                                                   &lt;/complexContent>
     *                                                                                 &lt;/complexType>
     *                                                                               &lt;/element>
     *                                                                             &lt;/sequence>
     *                                                                             &lt;attribute name="classCode" use="required" type="{http://www.w3.org/2001/XMLSchema}string" />
     *                                                                             &lt;attribute name="determinerCode" use="required" type="{http://www.w3.org/2001/XMLSchema}string" />
     *                                                                           &lt;/restriction>
     *                                                                         &lt;/complexContent>
     *                                                                       &lt;/complexType>
     *                                                                     &lt;/element>
     *                                                                   &lt;/sequence>
     *                                                                   &lt;attribute name="classCode" use="required" type="{http://www.w3.org/2001/XMLSchema}string" />
     *                                                                 &lt;/restriction>
     *                                                               &lt;/complexContent>
     *                                                             &lt;/complexType>
     *                                                           &lt;/element>
     *                                                           &lt;element name="birthPlace">
     *                                                             &lt;complexType>
     *                                                               &lt;complexContent>
     *                                                                 &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
     *                                                                   &lt;sequence>
     *                                                                     &lt;element name="addr">
     *                                                                       &lt;complexType>
     *                                                                         &lt;complexContent>
     *                                                                           &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
     *                                                                             &lt;sequence>
     *                                                                               &lt;element name="city" type="{http://www.w3.org/2001/XMLSchema}unsignedInt"/>
     *                                                                               &lt;element name="country" type="{http://www.w3.org/2001/XMLSchema}unsignedByte"/>
     *                                                                             &lt;/sequence>
     *                                                                           &lt;/restriction>
     *                                                                         &lt;/complexContent>
     *                                                                       &lt;/complexType>
     *                                                                     &lt;/element>
     *                                                                   &lt;/sequence>
     *                                                                   &lt;attribute name="classCode" use="required" type="{http://www.w3.org/2001/XMLSchema}string" />
     *                                                                 &lt;/restriction>
     *                                                               &lt;/complexContent>
     *                                                             &lt;/complexType>
     *                                                           &lt;/element>
     *                                                         &lt;/sequence>
     *                                                         &lt;attribute name="classCode" use="required" type="{http://www.w3.org/2001/XMLSchema}string" />
     *                                                         &lt;attribute name="determinerCode" use="required" type="{http://www.w3.org/2001/XMLSchema}string" />
     *                                                       &lt;/restriction>
     *                                                     &lt;/complexContent>
     *                                                   &lt;/complexType>
     *                                                 &lt;/element>
     *                                                 &lt;element name="providerOrganization">
     *                                                   &lt;complexType>
     *                                                     &lt;complexContent>
     *                                                       &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
     *                                                         &lt;sequence>
     *                                                           &lt;element name="id" maxOccurs="unbounded">
     *                                                             &lt;complexType>
     *                                                               &lt;complexContent>
     *                                                                 &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
     *                                                                   &lt;attribute name="root" use="required" type="{http://www.w3.org/2001/XMLSchema}string" />
     *                                                                 &lt;/restriction>
     *                                                               &lt;/complexContent>
     *                                                             &lt;/complexType>
     *                                                           &lt;/element>
     *                                                           &lt;element name="contactParty">
     *                                                             &lt;complexType>
     *                                                               &lt;complexContent>
     *                                                                 &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
     *                                                                   &lt;attribute name="classCode" use="required" type="{http://www.w3.org/2001/XMLSchema}string" />
     *                                                                 &lt;/restriction>
     *                                                               &lt;/complexContent>
     *                                                             &lt;/complexType>
     *                                                           &lt;/element>
     *                                                         &lt;/sequence>
     *                                                         &lt;attribute name="classCode" use="required" type="{http://www.w3.org/2001/XMLSchema}string" />
     *                                                         &lt;attribute name="determinerCode" use="required" type="{http://www.w3.org/2001/XMLSchema}string" />
     *                                                       &lt;/restriction>
     *                                                     &lt;/complexContent>
     *                                                   &lt;/complexType>
     *                                                 &lt;/element>
     *                                                 &lt;element name="subjectOf1">
     *                                                   &lt;complexType>
     *                                                     &lt;complexContent>
     *                                                       &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
     *                                                         &lt;sequence>
     *                                                           &lt;element name="queryMatchObservation">
     *                                                             &lt;complexType>
     *                                                               &lt;complexContent>
     *                                                                 &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
     *                                                                   &lt;sequence>
     *                                                                     &lt;element name="code">
     *                                                                       &lt;complexType>
     *                                                                         &lt;complexContent>
     *                                                                           &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
     *                                                                             &lt;attribute name="code" use="required" type="{http://www.w3.org/2001/XMLSchema}string" />
     *                                                                           &lt;/restriction>
     *                                                                         &lt;/complexContent>
     *                                                                       &lt;/complexType>
     *                                                                     &lt;/element>
     *                                                                     &lt;element name="value">
     *                                                                       &lt;complexType>
     *                                                                         &lt;complexContent>
     *                                                                           &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
     *                                                                             &lt;attribute name="value" use="required" type="{http://www.w3.org/2001/XMLSchema}unsignedByte" />
     *                                                                           &lt;/restriction>
     *                                                                         &lt;/complexContent>
     *                                                                       &lt;/complexType>
     *                                                                     &lt;/element>
     *                                                                   &lt;/sequence>
     *                                                                   &lt;attribute name="classCode" use="required" type="{http://www.w3.org/2001/XMLSchema}string" />
     *                                                                   &lt;attribute name="moodCode" use="required" type="{http://www.w3.org/2001/XMLSchema}string" />
     *                                                                 &lt;/restriction>
     *                                                               &lt;/complexContent>
     *                                                             &lt;/complexType>
     *                                                           &lt;/element>
     *                                                         &lt;/sequence>
     *                                                       &lt;/restriction>
     *                                                     &lt;/complexContent>
     *                                                   &lt;/complexType>
     *                                                 &lt;/element>
     *                                               &lt;/sequence>
     *                                               &lt;attribute name="classCode" use="required" type="{http://www.w3.org/2001/XMLSchema}string" />
     *                                             &lt;/restriction>
     *                                           &lt;/complexContent>
     *                                         &lt;/complexType>
     *                                       &lt;/element>
     *                                     &lt;/sequence>
     *                                     &lt;attribute name="typeCode" use="required" type="{http://www.w3.org/2001/XMLSchema}string" />
     *                                   &lt;/restriction>
     *                                 &lt;/complexContent>
     *                               &lt;/complexType>
     *                             &lt;/element>
     *                             &lt;element name="custodian">
     *                               &lt;complexType>
     *                                 &lt;complexContent>
     *                                   &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
     *                                     &lt;sequence>
     *                                       &lt;element name="assignedEntity">
     *                                         &lt;complexType>
     *                                           &lt;complexContent>
     *                                             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
     *                                               &lt;sequence>
     *                                                 &lt;element name="id">
     *                                                   &lt;complexType>
     *                                                     &lt;complexContent>
     *                                                       &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
     *                                                         &lt;attribute name="root" use="required" type="{http://www.w3.org/2001/XMLSchema}string" />
     *                                                       &lt;/restriction>
     *                                                     &lt;/complexContent>
     *                                                   &lt;/complexType>
     *                                                 &lt;/element>
     *                                               &lt;/sequence>
     *                                               &lt;attribute name="classCode" use="required" type="{http://www.w3.org/2001/XMLSchema}string" />
     *                                             &lt;/restriction>
     *                                           &lt;/complexContent>
     *                                         &lt;/complexType>
     *                                       &lt;/element>
     *                                     &lt;/sequence>
     *                                     &lt;attribute name="typeCode" use="required" type="{http://www.w3.org/2001/XMLSchema}string" />
     *                                   &lt;/restriction>
     *                                 &lt;/complexContent>
     *                               &lt;/complexType>
     *                             &lt;/element>
     *                           &lt;/sequence>
     *                           &lt;attribute name="classCode" use="required" type="{http://www.w3.org/2001/XMLSchema}string" />
     *                           &lt;attribute name="moodCode" use="required" type="{http://www.w3.org/2001/XMLSchema}string" />
     *                         &lt;/restriction>
     *                       &lt;/complexContent>
     *                     &lt;/complexType>
     *                   &lt;/element>
     *                 &lt;/sequence>
     *                 &lt;attribute name="typeCode" use="required" type="{http://www.w3.org/2001/XMLSchema}string" />
     *                 &lt;attribute name="contextConductionInd" use="required" type="{http://www.w3.org/2001/XMLSchema}boolean" />
     *               &lt;/restriction>
     *             &lt;/complexContent>
     *           &lt;/complexType>
     *         &lt;/element>
     *         &lt;element name="queryAck">
     *           &lt;complexType>
     *             &lt;complexContent>
     *               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
     *                 &lt;sequence>
     *                   &lt;element name="queryId">
     *                     &lt;complexType>
     *                       &lt;complexContent>
     *                         &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
     *                           &lt;attribute name="root" use="required" type="{http://www.w3.org/2001/XMLSchema}string" />
     *                           &lt;attribute name="extension" use="required" type="{http://www.w3.org/2001/XMLSchema}unsignedLong" />
     *                         &lt;/restriction>
     *                       &lt;/complexContent>
     *                     &lt;/complexType>
     *                   &lt;/element>
     *                   &lt;element name="queryResponseCode">
     *                     &lt;complexType>
     *                       &lt;complexContent>
     *                         &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
     *                           &lt;attribute name="code" use="required" type="{http://www.w3.org/2001/XMLSchema}string" />
     *                         &lt;/restriction>
     *                       &lt;/complexContent>
     *                     &lt;/complexType>
     *                   &lt;/element>
     *                   &lt;element name="resultTotalQuantity">
     *                     &lt;complexType>
     *                       &lt;complexContent>
     *                         &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
     *                           &lt;attribute name="value" use="required" type="{http://www.w3.org/2001/XMLSchema}unsignedByte" />
     *                         &lt;/restriction>
     *                       &lt;/complexContent>
     *                     &lt;/complexType>
     *                   &lt;/element>
     *                   &lt;element name="resultCurrentQuantity">
     *                     &lt;complexType>
     *                       &lt;complexContent>
     *                         &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
     *                           &lt;attribute name="value" use="required" type="{http://www.w3.org/2001/XMLSchema}unsignedByte" />
     *                         &lt;/restriction>
     *                       &lt;/complexContent>
     *                     &lt;/complexType>
     *                   &lt;/element>
     *                   &lt;element name="resultRemainingQuantity">
     *                     &lt;complexType>
     *                       &lt;complexContent>
     *                         &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
     *                           &lt;attribute name="value" use="required" type="{http://www.w3.org/2001/XMLSchema}unsignedByte" />
     *                         &lt;/restriction>
     *                       &lt;/complexContent>
     *                     &lt;/complexType>
     *                   &lt;/element>
     *                 &lt;/sequence>
     *               &lt;/restriction>
     *             &lt;/complexContent>
     *           &lt;/complexType>
     *         &lt;/element>
     *         &lt;element name="queryByParameter">
     *           &lt;complexType>
     *             &lt;complexContent>
     *               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
     *                 &lt;sequence>
     *                   &lt;element name="queryId">
     *                     &lt;complexType>
     *                       &lt;complexContent>
     *                         &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
     *                           &lt;attribute name="root" use="required" type="{http://www.w3.org/2001/XMLSchema}string" />
     *                           &lt;attribute name="extension" use="required" type="{http://www.w3.org/2001/XMLSchema}unsignedLong" />
     *                         &lt;/restriction>
     *                       &lt;/complexContent>
     *                     &lt;/complexType>
     *                   &lt;/element>
     *                   &lt;element name="statusCode">
     *                     &lt;complexType>
     *                       &lt;complexContent>
     *                         &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
     *                           &lt;attribute name="code" use="required" type="{http://www.w3.org/2001/XMLSchema}string" />
     *                         &lt;/restriction>
     *                       &lt;/complexContent>
     *                     &lt;/complexType>
     *                   &lt;/element>
     *                   &lt;element name="responseModalityCode">
     *                     &lt;complexType>
     *                       &lt;complexContent>
     *                         &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
     *                           &lt;attribute name="code" use="required" type="{http://www.w3.org/2001/XMLSchema}string" />
     *                         &lt;/restriction>
     *                       &lt;/complexContent>
     *                     &lt;/complexType>
     *                   &lt;/element>
     *                   &lt;element name="responsePriorityCode">
     *                     &lt;complexType>
     *                       &lt;complexContent>
     *                         &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
     *                           &lt;attribute name="code" use="required" type="{http://www.w3.org/2001/XMLSchema}string" />
     *                         &lt;/restriction>
     *                       &lt;/complexContent>
     *                     &lt;/complexType>
     *                   &lt;/element>
     *                   &lt;element name="parameterList">
     *                     &lt;complexType>
     *                       &lt;complexContent>
     *                         &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
     *                           &lt;sequence>
     *                             &lt;element name="livingSubjectId">
     *                               &lt;complexType>
     *                                 &lt;complexContent>
     *                                   &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
     *                                     &lt;sequence>
     *                                       &lt;element name="value">
     *                                         &lt;complexType>
     *                                           &lt;complexContent>
     *                                             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
     *                                               &lt;attribute name="root" use="required" type="{http://www.w3.org/2001/XMLSchema}string" />
     *                                               &lt;attribute name="extension" use="required" type="{http://www.w3.org/2001/XMLSchema}unsignedLong" />
     *                                             &lt;/restriction>
     *                                           &lt;/complexContent>
     *                                         &lt;/complexType>
     *                                       &lt;/element>
     *                                       &lt;element name="semanticsText" type="{http://www.w3.org/2001/XMLSchema}string"/>
     *                                     &lt;/sequence>
     *                                   &lt;/restriction>
     *                                 &lt;/complexContent>
     *                               &lt;/complexType>
     *                             &lt;/element>
     *                           &lt;/sequence>
     *                         &lt;/restriction>
     *                       &lt;/complexContent>
     *                     &lt;/complexType>
     *                   &lt;/element>
     *                 &lt;/sequence>
     *               &lt;/restriction>
     *             &lt;/complexContent>
     *           &lt;/complexType>
     *         &lt;/element>
     *       &lt;/sequence>
     *       &lt;attribute name="classCode" use="required" type="{http://www.w3.org/2001/XMLSchema}string" />
     *       &lt;attribute name="moodCode" use="required" type="{http://www.w3.org/2001/XMLSchema}string" />
     *     &lt;/restriction>
     *   &lt;/complexContent>
     * &lt;/complexType>
     * </pre>
     * 
     * 
     */
    @XmlAccessorType(XmlAccessType.FIELD)
    @XmlType(name = "", propOrder = {
        "code",
        "subject",
        "queryAck",
        "queryByParameter"
    })
    public static class ControlActProcess {

        @XmlElement(required = true)
        protected PRPAIN201306UV02 .ControlActProcess.Code code;
        @XmlElement(required = true)
        protected PRPAIN201306UV02 .ControlActProcess.Subject subject;
        @XmlElement(required = true)
        protected PRPAIN201306UV02 .ControlActProcess.QueryAck queryAck;
        @XmlElement(required = true)
        protected PRPAIN201306UV02 .ControlActProcess.QueryByParameter queryByParameter;
        @XmlAttribute(name = "classCode", required = true)
        protected String classCode;
        @XmlAttribute(name = "moodCode", required = true)
        protected String moodCode;

        /**
         * Obtém o valor da propriedade code.
         * 
         * @return
         *     possible object is
         *     {@link PRPAIN201306UV02 .ControlActProcess.Code }
         *     
         */
        public PRPAIN201306UV02 .ControlActProcess.Code getCode() {
            return code;
        }

        /**
         * Define o valor da propriedade code.
         * 
         * @param value
         *     allowed object is
         *     {@link PRPAIN201306UV02 .ControlActProcess.Code }
         *     
         */
        public void setCode(PRPAIN201306UV02 .ControlActProcess.Code value) {
            this.code = value;
        }

        /**
         * Obtém o valor da propriedade subject.
         * 
         * @return
         *     possible object is
         *     {@link PRPAIN201306UV02 .ControlActProcess.Subject }
         *     
         */
        public PRPAIN201306UV02 .ControlActProcess.Subject getSubject() {
            return subject;
        }

        /**
         * Define o valor da propriedade subject.
         * 
         * @param value
         *     allowed object is
         *     {@link PRPAIN201306UV02 .ControlActProcess.Subject }
         *     
         */
        public void setSubject(PRPAIN201306UV02 .ControlActProcess.Subject value) {
            this.subject = value;
        }

        /**
         * Obtém o valor da propriedade queryAck.
         * 
         * @return
         *     possible object is
         *     {@link PRPAIN201306UV02 .ControlActProcess.QueryAck }
         *     
         */
        public PRPAIN201306UV02 .ControlActProcess.QueryAck getQueryAck() {
            return queryAck;
        }

        /**
         * Define o valor da propriedade queryAck.
         * 
         * @param value
         *     allowed object is
         *     {@link PRPAIN201306UV02 .ControlActProcess.QueryAck }
         *     
         */
        public void setQueryAck(PRPAIN201306UV02 .ControlActProcess.QueryAck value) {
            this.queryAck = value;
        }

        /**
         * Obtém o valor da propriedade queryByParameter.
         * 
         * @return
         *     possible object is
         *     {@link PRPAIN201306UV02 .ControlActProcess.QueryByParameter }
         *     
         */
        public PRPAIN201306UV02 .ControlActProcess.QueryByParameter getQueryByParameter() {
            return queryByParameter;
        }

        /**
         * Define o valor da propriedade queryByParameter.
         * 
         * @param value
         *     allowed object is
         *     {@link PRPAIN201306UV02 .ControlActProcess.QueryByParameter }
         *     
         */
        public void setQueryByParameter(PRPAIN201306UV02 .ControlActProcess.QueryByParameter value) {
            this.queryByParameter = value;
        }

        /**
         * Obtém o valor da propriedade classCode.
         * 
         * @return
         *     possible object is
         *     {@link String }
         *     
         */
        public String getClassCode() {
            return classCode;
        }

        /**
         * Define o valor da propriedade classCode.
         * 
         * @param value
         *     allowed object is
         *     {@link String }
         *     
         */
        public void setClassCode(String value) {
            this.classCode = value;
        }

        /**
         * Obtém o valor da propriedade moodCode.
         * 
         * @return
         *     possible object is
         *     {@link String }
         *     
         */
        public String getMoodCode() {
            return moodCode;
        }

        /**
         * Define o valor da propriedade moodCode.
         * 
         * @param value
         *     allowed object is
         *     {@link String }
         *     
         */
        public void setMoodCode(String value) {
            this.moodCode = value;
        }


        /**
         * <p>Classe Java de anonymous complex type.
         * 
         * <p>O seguinte fragmento do esquema especifica o conteúdo esperado contido dentro desta classe.
         * 
         * <pre>
         * &lt;complexType>
         *   &lt;complexContent>
         *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
         *       &lt;attribute name="code" use="required" type="{http://www.w3.org/2001/XMLSchema}string" />
         *     &lt;/restriction>
         *   &lt;/complexContent>
         * &lt;/complexType>
         * </pre>
         * 
         * 
         */
        @XmlAccessorType(XmlAccessType.FIELD)
        @XmlType(name = "")
        public static class Code {

            @XmlAttribute(name = "code", required = true)
            protected String code;

            /**
             * Obtém o valor da propriedade code.
             * 
             * @return
             *     possible object is
             *     {@link String }
             *     
             */
            public String getCode() {
                return code;
            }

            /**
             * Define o valor da propriedade code.
             * 
             * @param value
             *     allowed object is
             *     {@link String }
             *     
             */
            public void setCode(String value) {
                this.code = value;
            }

        }


        /**
         * <p>Classe Java de anonymous complex type.
         * 
         * <p>O seguinte fragmento do esquema especifica o conteúdo esperado contido dentro desta classe.
         * 
         * <pre>
         * &lt;complexType>
         *   &lt;complexContent>
         *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
         *       &lt;sequence>
         *         &lt;element name="queryId">
         *           &lt;complexType>
         *             &lt;complexContent>
         *               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
         *                 &lt;attribute name="root" use="required" type="{http://www.w3.org/2001/XMLSchema}string" />
         *                 &lt;attribute name="extension" use="required" type="{http://www.w3.org/2001/XMLSchema}unsignedLong" />
         *               &lt;/restriction>
         *             &lt;/complexContent>
         *           &lt;/complexType>
         *         &lt;/element>
         *         &lt;element name="queryResponseCode">
         *           &lt;complexType>
         *             &lt;complexContent>
         *               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
         *                 &lt;attribute name="code" use="required" type="{http://www.w3.org/2001/XMLSchema}string" />
         *               &lt;/restriction>
         *             &lt;/complexContent>
         *           &lt;/complexType>
         *         &lt;/element>
         *         &lt;element name="resultTotalQuantity">
         *           &lt;complexType>
         *             &lt;complexContent>
         *               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
         *                 &lt;attribute name="value" use="required" type="{http://www.w3.org/2001/XMLSchema}unsignedByte" />
         *               &lt;/restriction>
         *             &lt;/complexContent>
         *           &lt;/complexType>
         *         &lt;/element>
         *         &lt;element name="resultCurrentQuantity">
         *           &lt;complexType>
         *             &lt;complexContent>
         *               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
         *                 &lt;attribute name="value" use="required" type="{http://www.w3.org/2001/XMLSchema}unsignedByte" />
         *               &lt;/restriction>
         *             &lt;/complexContent>
         *           &lt;/complexType>
         *         &lt;/element>
         *         &lt;element name="resultRemainingQuantity">
         *           &lt;complexType>
         *             &lt;complexContent>
         *               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
         *                 &lt;attribute name="value" use="required" type="{http://www.w3.org/2001/XMLSchema}unsignedByte" />
         *               &lt;/restriction>
         *             &lt;/complexContent>
         *           &lt;/complexType>
         *         &lt;/element>
         *       &lt;/sequence>
         *     &lt;/restriction>
         *   &lt;/complexContent>
         * &lt;/complexType>
         * </pre>
         * 
         * 
         */
        @XmlAccessorType(XmlAccessType.FIELD)
        @XmlType(name = "", propOrder = {
            "queryId",
            "queryResponseCode",
            "resultTotalQuantity",
            "resultCurrentQuantity",
            "resultRemainingQuantity"
        })
        public static class QueryAck {

            @XmlElement(required = true)
            protected PRPAIN201306UV02 .ControlActProcess.QueryAck.QueryId queryId;
            @XmlElement(required = true)
            protected PRPAIN201306UV02 .ControlActProcess.QueryAck.QueryResponseCode queryResponseCode;
            @XmlElement(required = true)
            protected PRPAIN201306UV02 .ControlActProcess.QueryAck.ResultTotalQuantity resultTotalQuantity;
            @XmlElement(required = true)
            protected PRPAIN201306UV02 .ControlActProcess.QueryAck.ResultCurrentQuantity resultCurrentQuantity;
            @XmlElement(required = true)
            protected PRPAIN201306UV02 .ControlActProcess.QueryAck.ResultRemainingQuantity resultRemainingQuantity;

            /**
             * Obtém o valor da propriedade queryId.
             * 
             * @return
             *     possible object is
             *     {@link PRPAIN201306UV02 .ControlActProcess.QueryAck.QueryId }
             *     
             */
            public PRPAIN201306UV02 .ControlActProcess.QueryAck.QueryId getQueryId() {
                return queryId;
            }

            /**
             * Define o valor da propriedade queryId.
             * 
             * @param value
             *     allowed object is
             *     {@link PRPAIN201306UV02 .ControlActProcess.QueryAck.QueryId }
             *     
             */
            public void setQueryId(PRPAIN201306UV02 .ControlActProcess.QueryAck.QueryId value) {
                this.queryId = value;
            }

            /**
             * Obtém o valor da propriedade queryResponseCode.
             * 
             * @return
             *     possible object is
             *     {@link PRPAIN201306UV02 .ControlActProcess.QueryAck.QueryResponseCode }
             *     
             */
            public PRPAIN201306UV02 .ControlActProcess.QueryAck.QueryResponseCode getQueryResponseCode() {
                return queryResponseCode;
            }

            /**
             * Define o valor da propriedade queryResponseCode.
             * 
             * @param value
             *     allowed object is
             *     {@link PRPAIN201306UV02 .ControlActProcess.QueryAck.QueryResponseCode }
             *     
             */
            public void setQueryResponseCode(PRPAIN201306UV02 .ControlActProcess.QueryAck.QueryResponseCode value) {
                this.queryResponseCode = value;
            }

            /**
             * Obtém o valor da propriedade resultTotalQuantity.
             * 
             * @return
             *     possible object is
             *     {@link PRPAIN201306UV02 .ControlActProcess.QueryAck.ResultTotalQuantity }
             *     
             */
            public PRPAIN201306UV02 .ControlActProcess.QueryAck.ResultTotalQuantity getResultTotalQuantity() {
                return resultTotalQuantity;
            }

            /**
             * Define o valor da propriedade resultTotalQuantity.
             * 
             * @param value
             *     allowed object is
             *     {@link PRPAIN201306UV02 .ControlActProcess.QueryAck.ResultTotalQuantity }
             *     
             */
            public void setResultTotalQuantity(PRPAIN201306UV02 .ControlActProcess.QueryAck.ResultTotalQuantity value) {
                this.resultTotalQuantity = value;
            }

            /**
             * Obtém o valor da propriedade resultCurrentQuantity.
             * 
             * @return
             *     possible object is
             *     {@link PRPAIN201306UV02 .ControlActProcess.QueryAck.ResultCurrentQuantity }
             *     
             */
            public PRPAIN201306UV02 .ControlActProcess.QueryAck.ResultCurrentQuantity getResultCurrentQuantity() {
                return resultCurrentQuantity;
            }

            /**
             * Define o valor da propriedade resultCurrentQuantity.
             * 
             * @param value
             *     allowed object is
             *     {@link PRPAIN201306UV02 .ControlActProcess.QueryAck.ResultCurrentQuantity }
             *     
             */
            public void setResultCurrentQuantity(PRPAIN201306UV02 .ControlActProcess.QueryAck.ResultCurrentQuantity value) {
                this.resultCurrentQuantity = value;
            }

            /**
             * Obtém o valor da propriedade resultRemainingQuantity.
             * 
             * @return
             *     possible object is
             *     {@link PRPAIN201306UV02 .ControlActProcess.QueryAck.ResultRemainingQuantity }
             *     
             */
            public PRPAIN201306UV02 .ControlActProcess.QueryAck.ResultRemainingQuantity getResultRemainingQuantity() {
                return resultRemainingQuantity;
            }

            /**
             * Define o valor da propriedade resultRemainingQuantity.
             * 
             * @param value
             *     allowed object is
             *     {@link PRPAIN201306UV02 .ControlActProcess.QueryAck.ResultRemainingQuantity }
             *     
             */
            public void setResultRemainingQuantity(PRPAIN201306UV02 .ControlActProcess.QueryAck.ResultRemainingQuantity value) {
                this.resultRemainingQuantity = value;
            }


            /**
             * <p>Classe Java de anonymous complex type.
             * 
             * <p>O seguinte fragmento do esquema especifica o conteúdo esperado contido dentro desta classe.
             * 
             * <pre>
             * &lt;complexType>
             *   &lt;complexContent>
             *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
             *       &lt;attribute name="root" use="required" type="{http://www.w3.org/2001/XMLSchema}string" />
             *       &lt;attribute name="extension" use="required" type="{http://www.w3.org/2001/XMLSchema}unsignedLong" />
             *     &lt;/restriction>
             *   &lt;/complexContent>
             * &lt;/complexType>
             * </pre>
             * 
             * 
             */
            @XmlAccessorType(XmlAccessType.FIELD)
            @XmlType(name = "")
            public static class QueryId {

                @XmlAttribute(name = "root", required = true)
                protected String root;
                @XmlAttribute(name = "extension", required = true)
                @XmlSchemaType(name = "unsignedLong")
                protected BigInteger extension;

                /**
                 * Obtém o valor da propriedade root.
                 * 
                 * @return
                 *     possible object is
                 *     {@link String }
                 *     
                 */
                public String getRoot() {
                    return root;
                }

                /**
                 * Define o valor da propriedade root.
                 * 
                 * @param value
                 *     allowed object is
                 *     {@link String }
                 *     
                 */
                public void setRoot(String value) {
                    this.root = value;
                }

                /**
                 * Obtém o valor da propriedade extension.
                 * 
                 * @return
                 *     possible object is
                 *     {@link BigInteger }
                 *     
                 */
                public BigInteger getExtension() {
                    return extension;
                }

                /**
                 * Define o valor da propriedade extension.
                 * 
                 * @param value
                 *     allowed object is
                 *     {@link BigInteger }
                 *     
                 */
                public void setExtension(BigInteger value) {
                    this.extension = value;
                }

            }


            /**
             * <p>Classe Java de anonymous complex type.
             * 
             * <p>O seguinte fragmento do esquema especifica o conteúdo esperado contido dentro desta classe.
             * 
             * <pre>
             * &lt;complexType>
             *   &lt;complexContent>
             *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
             *       &lt;attribute name="code" use="required" type="{http://www.w3.org/2001/XMLSchema}string" />
             *     &lt;/restriction>
             *   &lt;/complexContent>
             * &lt;/complexType>
             * </pre>
             * 
             * 
             */
            @XmlAccessorType(XmlAccessType.FIELD)
            @XmlType(name = "")
            public static class QueryResponseCode {

                @XmlAttribute(name = "code", required = true)
                protected String code;

                /**
                 * Obtém o valor da propriedade code.
                 * 
                 * @return
                 *     possible object is
                 *     {@link String }
                 *     
                 */
                public String getCode() {
                    return code;
                }

                /**
                 * Define o valor da propriedade code.
                 * 
                 * @param value
                 *     allowed object is
                 *     {@link String }
                 *     
                 */
                public void setCode(String value) {
                    this.code = value;
                }

            }


            /**
             * <p>Classe Java de anonymous complex type.
             * 
             * <p>O seguinte fragmento do esquema especifica o conteúdo esperado contido dentro desta classe.
             * 
             * <pre>
             * &lt;complexType>
             *   &lt;complexContent>
             *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
             *       &lt;attribute name="value" use="required" type="{http://www.w3.org/2001/XMLSchema}unsignedByte" />
             *     &lt;/restriction>
             *   &lt;/complexContent>
             * &lt;/complexType>
             * </pre>
             * 
             * 
             */
            @XmlAccessorType(XmlAccessType.FIELD)
            @XmlType(name = "")
            public static class ResultCurrentQuantity {

                @XmlAttribute(name = "value", required = true)
                @XmlSchemaType(name = "unsignedByte")
                protected short value;

                /**
                 * Obtém o valor da propriedade value.
                 * 
                 */
                public short getValue() {
                    return value;
                }

                /**
                 * Define o valor da propriedade value.
                 * 
                 */
                public void setValue(short value) {
                    this.value = value;
                }

            }


            /**
             * <p>Classe Java de anonymous complex type.
             * 
             * <p>O seguinte fragmento do esquema especifica o conteúdo esperado contido dentro desta classe.
             * 
             * <pre>
             * &lt;complexType>
             *   &lt;complexContent>
             *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
             *       &lt;attribute name="value" use="required" type="{http://www.w3.org/2001/XMLSchema}unsignedByte" />
             *     &lt;/restriction>
             *   &lt;/complexContent>
             * &lt;/complexType>
             * </pre>
             * 
             * 
             */
            @XmlAccessorType(XmlAccessType.FIELD)
            @XmlType(name = "")
            public static class ResultRemainingQuantity {

                @XmlAttribute(name = "value", required = true)
                @XmlSchemaType(name = "unsignedByte")
                protected short value;

                /**
                 * Obtém o valor da propriedade value.
                 * 
                 */
                public short getValue() {
                    return value;
                }

                /**
                 * Define o valor da propriedade value.
                 * 
                 */
                public void setValue(short value) {
                    this.value = value;
                }

            }


            /**
             * <p>Classe Java de anonymous complex type.
             * 
             * <p>O seguinte fragmento do esquema especifica o conteúdo esperado contido dentro desta classe.
             * 
             * <pre>
             * &lt;complexType>
             *   &lt;complexContent>
             *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
             *       &lt;attribute name="value" use="required" type="{http://www.w3.org/2001/XMLSchema}unsignedByte" />
             *     &lt;/restriction>
             *   &lt;/complexContent>
             * &lt;/complexType>
             * </pre>
             * 
             * 
             */
            @XmlAccessorType(XmlAccessType.FIELD)
            @XmlType(name = "")
            public static class ResultTotalQuantity {

                @XmlAttribute(name = "value", required = true)
                @XmlSchemaType(name = "unsignedByte")
                protected short value;

                /**
                 * Obtém o valor da propriedade value.
                 * 
                 */
                public short getValue() {
                    return value;
                }

                /**
                 * Define o valor da propriedade value.
                 * 
                 */
                public void setValue(short value) {
                    this.value = value;
                }

            }

        }


        /**
         * <p>Classe Java de anonymous complex type.
         * 
         * <p>O seguinte fragmento do esquema especifica o conteúdo esperado contido dentro desta classe.
         * 
         * <pre>
         * &lt;complexType>
         *   &lt;complexContent>
         *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
         *       &lt;sequence>
         *         &lt;element name="queryId">
         *           &lt;complexType>
         *             &lt;complexContent>
         *               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
         *                 &lt;attribute name="root" use="required" type="{http://www.w3.org/2001/XMLSchema}string" />
         *                 &lt;attribute name="extension" use="required" type="{http://www.w3.org/2001/XMLSchema}unsignedLong" />
         *               &lt;/restriction>
         *             &lt;/complexContent>
         *           &lt;/complexType>
         *         &lt;/element>
         *         &lt;element name="statusCode">
         *           &lt;complexType>
         *             &lt;complexContent>
         *               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
         *                 &lt;attribute name="code" use="required" type="{http://www.w3.org/2001/XMLSchema}string" />
         *               &lt;/restriction>
         *             &lt;/complexContent>
         *           &lt;/complexType>
         *         &lt;/element>
         *         &lt;element name="responseModalityCode">
         *           &lt;complexType>
         *             &lt;complexContent>
         *               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
         *                 &lt;attribute name="code" use="required" type="{http://www.w3.org/2001/XMLSchema}string" />
         *               &lt;/restriction>
         *             &lt;/complexContent>
         *           &lt;/complexType>
         *         &lt;/element>
         *         &lt;element name="responsePriorityCode">
         *           &lt;complexType>
         *             &lt;complexContent>
         *               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
         *                 &lt;attribute name="code" use="required" type="{http://www.w3.org/2001/XMLSchema}string" />
         *               &lt;/restriction>
         *             &lt;/complexContent>
         *           &lt;/complexType>
         *         &lt;/element>
         *         &lt;element name="parameterList">
         *           &lt;complexType>
         *             &lt;complexContent>
         *               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
         *                 &lt;sequence>
         *                   &lt;element name="livingSubjectId">
         *                     &lt;complexType>
         *                       &lt;complexContent>
         *                         &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
         *                           &lt;sequence>
         *                             &lt;element name="value">
         *                               &lt;complexType>
         *                                 &lt;complexContent>
         *                                   &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
         *                                     &lt;attribute name="root" use="required" type="{http://www.w3.org/2001/XMLSchema}string" />
         *                                     &lt;attribute name="extension" use="required" type="{http://www.w3.org/2001/XMLSchema}unsignedLong" />
         *                                   &lt;/restriction>
         *                                 &lt;/complexContent>
         *                               &lt;/complexType>
         *                             &lt;/element>
         *                             &lt;element name="semanticsText" type="{http://www.w3.org/2001/XMLSchema}string"/>
         *                           &lt;/sequence>
         *                         &lt;/restriction>
         *                       &lt;/complexContent>
         *                     &lt;/complexType>
         *                   &lt;/element>
         *                 &lt;/sequence>
         *               &lt;/restriction>
         *             &lt;/complexContent>
         *           &lt;/complexType>
         *         &lt;/element>
         *       &lt;/sequence>
         *     &lt;/restriction>
         *   &lt;/complexContent>
         * &lt;/complexType>
         * </pre>
         * 
         * 
         */
        @XmlAccessorType(XmlAccessType.FIELD)
        @XmlType(name = "", propOrder = {
            "queryId",
            "statusCode",
            "responseModalityCode",
            "responsePriorityCode",
            "parameterList"
        })
        public static class QueryByParameter {

            @XmlElement(required = true)
            protected PRPAIN201306UV02 .ControlActProcess.QueryByParameter.QueryId queryId;
            @XmlElement(required = true)
            protected PRPAIN201306UV02 .ControlActProcess.QueryByParameter.StatusCode statusCode;
            @XmlElement(required = true)
            protected PRPAIN201306UV02 .ControlActProcess.QueryByParameter.ResponseModalityCode responseModalityCode;
            @XmlElement(required = true)
            protected PRPAIN201306UV02 .ControlActProcess.QueryByParameter.ResponsePriorityCode responsePriorityCode;
            @XmlElement(required = true)
            protected PRPAIN201306UV02 .ControlActProcess.QueryByParameter.ParameterList parameterList;

            /**
             * Obtém o valor da propriedade queryId.
             * 
             * @return
             *     possible object is
             *     {@link PRPAIN201306UV02 .ControlActProcess.QueryByParameter.QueryId }
             *     
             */
            public PRPAIN201306UV02 .ControlActProcess.QueryByParameter.QueryId getQueryId() {
                return queryId;
            }

            /**
             * Define o valor da propriedade queryId.
             * 
             * @param value
             *     allowed object is
             *     {@link PRPAIN201306UV02 .ControlActProcess.QueryByParameter.QueryId }
             *     
             */
            public void setQueryId(PRPAIN201306UV02 .ControlActProcess.QueryByParameter.QueryId value) {
                this.queryId = value;
            }

            /**
             * Obtém o valor da propriedade statusCode.
             * 
             * @return
             *     possible object is
             *     {@link PRPAIN201306UV02 .ControlActProcess.QueryByParameter.StatusCode }
             *     
             */
            public PRPAIN201306UV02 .ControlActProcess.QueryByParameter.StatusCode getStatusCode() {
                return statusCode;
            }

            /**
             * Define o valor da propriedade statusCode.
             * 
             * @param value
             *     allowed object is
             *     {@link PRPAIN201306UV02 .ControlActProcess.QueryByParameter.StatusCode }
             *     
             */
            public void setStatusCode(PRPAIN201306UV02 .ControlActProcess.QueryByParameter.StatusCode value) {
                this.statusCode = value;
            }

            /**
             * Obtém o valor da propriedade responseModalityCode.
             * 
             * @return
             *     possible object is
             *     {@link PRPAIN201306UV02 .ControlActProcess.QueryByParameter.ResponseModalityCode }
             *     
             */
            public PRPAIN201306UV02 .ControlActProcess.QueryByParameter.ResponseModalityCode getResponseModalityCode() {
                return responseModalityCode;
            }

            /**
             * Define o valor da propriedade responseModalityCode.
             * 
             * @param value
             *     allowed object is
             *     {@link PRPAIN201306UV02 .ControlActProcess.QueryByParameter.ResponseModalityCode }
             *     
             */
            public void setResponseModalityCode(PRPAIN201306UV02 .ControlActProcess.QueryByParameter.ResponseModalityCode value) {
                this.responseModalityCode = value;
            }

            /**
             * Obtém o valor da propriedade responsePriorityCode.
             * 
             * @return
             *     possible object is
             *     {@link PRPAIN201306UV02 .ControlActProcess.QueryByParameter.ResponsePriorityCode }
             *     
             */
            public PRPAIN201306UV02 .ControlActProcess.QueryByParameter.ResponsePriorityCode getResponsePriorityCode() {
                return responsePriorityCode;
            }

            /**
             * Define o valor da propriedade responsePriorityCode.
             * 
             * @param value
             *     allowed object is
             *     {@link PRPAIN201306UV02 .ControlActProcess.QueryByParameter.ResponsePriorityCode }
             *     
             */
            public void setResponsePriorityCode(PRPAIN201306UV02 .ControlActProcess.QueryByParameter.ResponsePriorityCode value) {
                this.responsePriorityCode = value;
            }

            /**
             * Obtém o valor da propriedade parameterList.
             * 
             * @return
             *     possible object is
             *     {@link PRPAIN201306UV02 .ControlActProcess.QueryByParameter.ParameterList }
             *     
             */
            public PRPAIN201306UV02 .ControlActProcess.QueryByParameter.ParameterList getParameterList() {
                return parameterList;
            }

            /**
             * Define o valor da propriedade parameterList.
             * 
             * @param value
             *     allowed object is
             *     {@link PRPAIN201306UV02 .ControlActProcess.QueryByParameter.ParameterList }
             *     
             */
            public void setParameterList(PRPAIN201306UV02 .ControlActProcess.QueryByParameter.ParameterList value) {
                this.parameterList = value;
            }


            /**
             * <p>Classe Java de anonymous complex type.
             * 
             * <p>O seguinte fragmento do esquema especifica o conteúdo esperado contido dentro desta classe.
             * 
             * <pre>
             * &lt;complexType>
             *   &lt;complexContent>
             *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
             *       &lt;sequence>
             *         &lt;element name="livingSubjectId">
             *           &lt;complexType>
             *             &lt;complexContent>
             *               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
             *                 &lt;sequence>
             *                   &lt;element name="value">
             *                     &lt;complexType>
             *                       &lt;complexContent>
             *                         &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
             *                           &lt;attribute name="root" use="required" type="{http://www.w3.org/2001/XMLSchema}string" />
             *                           &lt;attribute name="extension" use="required" type="{http://www.w3.org/2001/XMLSchema}unsignedLong" />
             *                         &lt;/restriction>
             *                       &lt;/complexContent>
             *                     &lt;/complexType>
             *                   &lt;/element>
             *                   &lt;element name="semanticsText" type="{http://www.w3.org/2001/XMLSchema}string"/>
             *                 &lt;/sequence>
             *               &lt;/restriction>
             *             &lt;/complexContent>
             *           &lt;/complexType>
             *         &lt;/element>
             *       &lt;/sequence>
             *     &lt;/restriction>
             *   &lt;/complexContent>
             * &lt;/complexType>
             * </pre>
             * 
             * 
             */
            @XmlAccessorType(XmlAccessType.FIELD)
            @XmlType(name = "", propOrder = {
                "livingSubjectId"
            })
            public static class ParameterList {

                @XmlElement(required = true)
                protected PRPAIN201306UV02 .ControlActProcess.QueryByParameter.ParameterList.LivingSubjectId livingSubjectId;

                /**
                 * Obtém o valor da propriedade livingSubjectId.
                 * 
                 * @return
                 *     possible object is
                 *     {@link PRPAIN201306UV02 .ControlActProcess.QueryByParameter.ParameterList.LivingSubjectId }
                 *     
                 */
                public PRPAIN201306UV02 .ControlActProcess.QueryByParameter.ParameterList.LivingSubjectId getLivingSubjectId() {
                    return livingSubjectId;
                }

                /**
                 * Define o valor da propriedade livingSubjectId.
                 * 
                 * @param value
                 *     allowed object is
                 *     {@link PRPAIN201306UV02 .ControlActProcess.QueryByParameter.ParameterList.LivingSubjectId }
                 *     
                 */
                public void setLivingSubjectId(PRPAIN201306UV02 .ControlActProcess.QueryByParameter.ParameterList.LivingSubjectId value) {
                    this.livingSubjectId = value;
                }


                /**
                 * <p>Classe Java de anonymous complex type.
                 * 
                 * <p>O seguinte fragmento do esquema especifica o conteúdo esperado contido dentro desta classe.
                 * 
                 * <pre>
                 * &lt;complexType>
                 *   &lt;complexContent>
                 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
                 *       &lt;sequence>
                 *         &lt;element name="value">
                 *           &lt;complexType>
                 *             &lt;complexContent>
                 *               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
                 *                 &lt;attribute name="root" use="required" type="{http://www.w3.org/2001/XMLSchema}string" />
                 *                 &lt;attribute name="extension" use="required" type="{http://www.w3.org/2001/XMLSchema}unsignedLong" />
                 *               &lt;/restriction>
                 *             &lt;/complexContent>
                 *           &lt;/complexType>
                 *         &lt;/element>
                 *         &lt;element name="semanticsText" type="{http://www.w3.org/2001/XMLSchema}string"/>
                 *       &lt;/sequence>
                 *     &lt;/restriction>
                 *   &lt;/complexContent>
                 * &lt;/complexType>
                 * </pre>
                 * 
                 * 
                 */
                @XmlAccessorType(XmlAccessType.FIELD)
                @XmlType(name = "", propOrder = {
                    "value",
                    "semanticsText"
                })
                public static class LivingSubjectId {

                    @XmlElement(required = true)
                    protected PRPAIN201306UV02 .ControlActProcess.QueryByParameter.ParameterList.LivingSubjectId.Value value;
                    @XmlElement(required = true)
                    protected String semanticsText;

                    /**
                     * Obtém o valor da propriedade value.
                     * 
                     * @return
                     *     possible object is
                     *     {@link PRPAIN201306UV02 .ControlActProcess.QueryByParameter.ParameterList.LivingSubjectId.Value }
                     *     
                     */
                    public PRPAIN201306UV02 .ControlActProcess.QueryByParameter.ParameterList.LivingSubjectId.Value getValue() {
                        return value;
                    }

                    /**
                     * Define o valor da propriedade value.
                     * 
                     * @param value
                     *     allowed object is
                     *     {@link PRPAIN201306UV02 .ControlActProcess.QueryByParameter.ParameterList.LivingSubjectId.Value }
                     *     
                     */
                    public void setValue(PRPAIN201306UV02 .ControlActProcess.QueryByParameter.ParameterList.LivingSubjectId.Value value) {
                        this.value = value;
                    }

                    /**
                     * Obtém o valor da propriedade semanticsText.
                     * 
                     * @return
                     *     possible object is
                     *     {@link String }
                     *     
                     */
                    public String getSemanticsText() {
                        return semanticsText;
                    }

                    /**
                     * Define o valor da propriedade semanticsText.
                     * 
                     * @param value
                     *     allowed object is
                     *     {@link String }
                     *     
                     */
                    public void setSemanticsText(String value) {
                        this.semanticsText = value;
                    }


                    /**
                     * <p>Classe Java de anonymous complex type.
                     * 
                     * <p>O seguinte fragmento do esquema especifica o conteúdo esperado contido dentro desta classe.
                     * 
                     * <pre>
                     * &lt;complexType>
                     *   &lt;complexContent>
                     *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
                     *       &lt;attribute name="root" use="required" type="{http://www.w3.org/2001/XMLSchema}string" />
                     *       &lt;attribute name="extension" use="required" type="{http://www.w3.org/2001/XMLSchema}unsignedLong" />
                     *     &lt;/restriction>
                     *   &lt;/complexContent>
                     * &lt;/complexType>
                     * </pre>
                     * 
                     * 
                     */
                    @XmlAccessorType(XmlAccessType.FIELD)
                    @XmlType(name = "")
                    public static class Value {

                        @XmlAttribute(name = "root", required = true)
                        protected String root;
                        @XmlAttribute(name = "extension", required = true)
                        @XmlSchemaType(name = "unsignedLong")
                        protected BigInteger extension;

                        /**
                         * Obtém o valor da propriedade root.
                         * 
                         * @return
                         *     possible object is
                         *     {@link String }
                         *     
                         */
                        public String getRoot() {
                            return root;
                        }

                        /**
                         * Define o valor da propriedade root.
                         * 
                         * @param value
                         *     allowed object is
                         *     {@link String }
                         *     
                         */
                        public void setRoot(String value) {
                            this.root = value;
                        }

                        /**
                         * Obtém o valor da propriedade extension.
                         * 
                         * @return
                         *     possible object is
                         *     {@link BigInteger }
                         *     
                         */
                        public BigInteger getExtension() {
                            return extension;
                        }

                        /**
                         * Define o valor da propriedade extension.
                         * 
                         * @param value
                         *     allowed object is
                         *     {@link BigInteger }
                         *     
                         */
                        public void setExtension(BigInteger value) {
                            this.extension = value;
                        }

                    }

                }

            }


            /**
             * <p>Classe Java de anonymous complex type.
             * 
             * <p>O seguinte fragmento do esquema especifica o conteúdo esperado contido dentro desta classe.
             * 
             * <pre>
             * &lt;complexType>
             *   &lt;complexContent>
             *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
             *       &lt;attribute name="root" use="required" type="{http://www.w3.org/2001/XMLSchema}string" />
             *       &lt;attribute name="extension" use="required" type="{http://www.w3.org/2001/XMLSchema}unsignedLong" />
             *     &lt;/restriction>
             *   &lt;/complexContent>
             * &lt;/complexType>
             * </pre>
             * 
             * 
             */
            @XmlAccessorType(XmlAccessType.FIELD)
            @XmlType(name = "")
            public static class QueryId {

                @XmlAttribute(name = "root", required = true)
                protected String root;
                @XmlAttribute(name = "extension", required = true)
                @XmlSchemaType(name = "unsignedLong")
                protected BigInteger extension;

                /**
                 * Obtém o valor da propriedade root.
                 * 
                 * @return
                 *     possible object is
                 *     {@link String }
                 *     
                 */
                public String getRoot() {
                    return root;
                }

                /**
                 * Define o valor da propriedade root.
                 * 
                 * @param value
                 *     allowed object is
                 *     {@link String }
                 *     
                 */
                public void setRoot(String value) {
                    this.root = value;
                }

                /**
                 * Obtém o valor da propriedade extension.
                 * 
                 * @return
                 *     possible object is
                 *     {@link BigInteger }
                 *     
                 */
                public BigInteger getExtension() {
                    return extension;
                }

                /**
                 * Define o valor da propriedade extension.
                 * 
                 * @param value
                 *     allowed object is
                 *     {@link BigInteger }
                 *     
                 */
                public void setExtension(BigInteger value) {
                    this.extension = value;
                }

            }


            /**
             * <p>Classe Java de anonymous complex type.
             * 
             * <p>O seguinte fragmento do esquema especifica o conteúdo esperado contido dentro desta classe.
             * 
             * <pre>
             * &lt;complexType>
             *   &lt;complexContent>
             *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
             *       &lt;attribute name="code" use="required" type="{http://www.w3.org/2001/XMLSchema}string" />
             *     &lt;/restriction>
             *   &lt;/complexContent>
             * &lt;/complexType>
             * </pre>
             * 
             * 
             */
            @XmlAccessorType(XmlAccessType.FIELD)
            @XmlType(name = "")
            public static class ResponseModalityCode {

                @XmlAttribute(name = "code", required = true)
                protected String code;

                /**
                 * Obtém o valor da propriedade code.
                 * 
                 * @return
                 *     possible object is
                 *     {@link String }
                 *     
                 */
                public String getCode() {
                    return code;
                }

                /**
                 * Define o valor da propriedade code.
                 * 
                 * @param value
                 *     allowed object is
                 *     {@link String }
                 *     
                 */
                public void setCode(String value) {
                    this.code = value;
                }

            }


            /**
             * <p>Classe Java de anonymous complex type.
             * 
             * <p>O seguinte fragmento do esquema especifica o conteúdo esperado contido dentro desta classe.
             * 
             * <pre>
             * &lt;complexType>
             *   &lt;complexContent>
             *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
             *       &lt;attribute name="code" use="required" type="{http://www.w3.org/2001/XMLSchema}string" />
             *     &lt;/restriction>
             *   &lt;/complexContent>
             * &lt;/complexType>
             * </pre>
             * 
             * 
             */
            @XmlAccessorType(XmlAccessType.FIELD)
            @XmlType(name = "")
            public static class ResponsePriorityCode {

                @XmlAttribute(name = "code", required = true)
                protected String code;

                /**
                 * Obtém o valor da propriedade code.
                 * 
                 * @return
                 *     possible object is
                 *     {@link String }
                 *     
                 */
                public String getCode() {
                    return code;
                }

                /**
                 * Define o valor da propriedade code.
                 * 
                 * @param value
                 *     allowed object is
                 *     {@link String }
                 *     
                 */
                public void setCode(String value) {
                    this.code = value;
                }

            }


            /**
             * <p>Classe Java de anonymous complex type.
             * 
             * <p>O seguinte fragmento do esquema especifica o conteúdo esperado contido dentro desta classe.
             * 
             * <pre>
             * &lt;complexType>
             *   &lt;complexContent>
             *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
             *       &lt;attribute name="code" use="required" type="{http://www.w3.org/2001/XMLSchema}string" />
             *     &lt;/restriction>
             *   &lt;/complexContent>
             * &lt;/complexType>
             * </pre>
             * 
             * 
             */
            @XmlAccessorType(XmlAccessType.FIELD)
            @XmlType(name = "")
            public static class StatusCode {

                @XmlAttribute(name = "code", required = true)
                protected String code;

                /**
                 * Obtém o valor da propriedade code.
                 * 
                 * @return
                 *     possible object is
                 *     {@link String }
                 *     
                 */
                public String getCode() {
                    return code;
                }

                /**
                 * Define o valor da propriedade code.
                 * 
                 * @param value
                 *     allowed object is
                 *     {@link String }
                 *     
                 */
                public void setCode(String value) {
                    this.code = value;
                }

            }

        }


        /**
         * <p>Classe Java de anonymous complex type.
         * 
         * <p>O seguinte fragmento do esquema especifica o conteúdo esperado contido dentro desta classe.
         * 
         * <pre>
         * &lt;complexType>
         *   &lt;complexContent>
         *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
         *       &lt;sequence>
         *         &lt;element name="registrationEvent">
         *           &lt;complexType>
         *             &lt;complexContent>
         *               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
         *                 &lt;sequence>
         *                   &lt;element name="statusCode">
         *                     &lt;complexType>
         *                       &lt;complexContent>
         *                         &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
         *                           &lt;attribute name="code" use="required" type="{http://www.w3.org/2001/XMLSchema}string" />
         *                         &lt;/restriction>
         *                       &lt;/complexContent>
         *                     &lt;/complexType>
         *                   &lt;/element>
         *                   &lt;element name="subject1">
         *                     &lt;complexType>
         *                       &lt;complexContent>
         *                         &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
         *                           &lt;sequence>
         *                             &lt;element name="realmCode">
         *                               &lt;complexType>
         *                                 &lt;complexContent>
         *                                   &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
         *                                     &lt;attribute name="code" use="required" type="{http://www.w3.org/2001/XMLSchema}string" />
         *                                   &lt;/restriction>
         *                                 &lt;/complexContent>
         *                               &lt;/complexType>
         *                             &lt;/element>
         *                             &lt;element name="patient">
         *                               &lt;complexType>
         *                                 &lt;complexContent>
         *                                   &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
         *                                     &lt;sequence>
         *                                       &lt;element name="id" maxOccurs="unbounded">
         *                                         &lt;complexType>
         *                                           &lt;complexContent>
         *                                             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
         *                                               &lt;attribute name="root" use="required" type="{http://www.w3.org/2001/XMLSchema}string" />
         *                                               &lt;attribute name="extension" use="required" type="{http://www.w3.org/2001/XMLSchema}unsignedLong" />
         *                                               &lt;attribute name="assigningAuthorityName" use="required" type="{http://www.w3.org/2001/XMLSchema}string" />
         *                                             &lt;/restriction>
         *                                           &lt;/complexContent>
         *                                         &lt;/complexType>
         *                                       &lt;/element>
         *                                       &lt;element name="statusCode">
         *                                         &lt;complexType>
         *                                           &lt;complexContent>
         *                                             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
         *                                               &lt;attribute name="code" use="required" type="{http://www.w3.org/2001/XMLSchema}string" />
         *                                             &lt;/restriction>
         *                                           &lt;/complexContent>
         *                                         &lt;/complexType>
         *                                       &lt;/element>
         *                                       &lt;element name="patientPerson">
         *                                         &lt;complexType>
         *                                           &lt;complexContent>
         *                                             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
         *                                               &lt;sequence>
         *                                                 &lt;element name="name">
         *                                                   &lt;complexType>
         *                                                     &lt;complexContent>
         *                                                       &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
         *                                                         &lt;sequence>
         *                                                           &lt;element name="given" type="{http://www.w3.org/2001/XMLSchema}string"/>
         *                                                         &lt;/sequence>
         *                                                         &lt;attribute name="use" use="required" type="{http://www.w3.org/2001/XMLSchema}string" />
         *                                                       &lt;/restriction>
         *                                                     &lt;/complexContent>
         *                                                   &lt;/complexType>
         *                                                 &lt;/element>
         *                                                 &lt;element name="administrativeGenderCode">
         *                                                   &lt;complexType>
         *                                                     &lt;complexContent>
         *                                                       &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
         *                                                         &lt;attribute name="code" use="required" type="{http://www.w3.org/2001/XMLSchema}string" />
         *                                                         &lt;attribute name="codeSystem" use="required" type="{http://www.w3.org/2001/XMLSchema}string" />
         *                                                       &lt;/restriction>
         *                                                     &lt;/complexContent>
         *                                                   &lt;/complexType>
         *                                                 &lt;/element>
         *                                                 &lt;element name="birthTime">
         *                                                   &lt;complexType>
         *                                                     &lt;complexContent>
         *                                                       &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
         *                                                         &lt;attribute name="value" use="required" type="{http://www.w3.org/2001/XMLSchema}unsignedLong" />
         *                                                       &lt;/restriction>
         *                                                     &lt;/complexContent>
         *                                                   &lt;/complexType>
         *                                                 &lt;/element>
         *                                                 &lt;element name="addr">
         *                                                   &lt;complexType>
         *                                                     &lt;complexContent>
         *                                                       &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
         *                                                         &lt;sequence>
         *                                                           &lt;element name="city" type="{http://www.w3.org/2001/XMLSchema}unsignedInt"/>
         *                                                           &lt;element name="state" type="{http://www.w3.org/2001/XMLSchema}string"/>
         *                                                           &lt;element name="postalCode" type="{http://www.w3.org/2001/XMLSchema}unsignedInt"/>
         *                                                           &lt;element name="country" type="{http://www.w3.org/2001/XMLSchema}unsignedByte"/>
         *                                                           &lt;element name="houseNumber" type="{http://www.w3.org/2001/XMLSchema}unsignedShort"/>
         *                                                           &lt;element name="streetNameType" type="{http://www.w3.org/2001/XMLSchema}unsignedByte"/>
         *                                                           &lt;element name="additionalLocator" type="{http://www.w3.org/2001/XMLSchema}string"/>
         *                                                         &lt;/sequence>
         *                                                         &lt;attribute name="use" use="required" type="{http://www.w3.org/2001/XMLSchema}string" />
         *                                                       &lt;/restriction>
         *                                                     &lt;/complexContent>
         *                                                   &lt;/complexType>
         *                                                 &lt;/element>
         *                                                 &lt;element name="maritalStatusCode">
         *                                                   &lt;complexType>
         *                                                     &lt;complexContent>
         *                                                       &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
         *                                                         &lt;attribute name="code" use="required" type="{http://www.w3.org/2001/XMLSchema}unsignedByte" />
         *                                                       &lt;/restriction>
         *                                                     &lt;/complexContent>
         *                                                   &lt;/complexType>
         *                                                 &lt;/element>
         *                                                 &lt;element name="raceCode">
         *                                                   &lt;complexType>
         *                                                     &lt;complexContent>
         *                                                       &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
         *                                                         &lt;attribute name="code" use="required" type="{http://www.w3.org/2001/XMLSchema}unsignedByte" />
         *                                                       &lt;/restriction>
         *                                                     &lt;/complexContent>
         *                                                   &lt;/complexType>
         *                                                 &lt;/element>
         *                                                 &lt;element name="asOtherIDs" maxOccurs="unbounded">
         *                                                   &lt;complexType>
         *                                                     &lt;complexContent>
         *                                                       &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
         *                                                         &lt;sequence>
         *                                                           &lt;element name="id" maxOccurs="unbounded">
         *                                                             &lt;complexType>
         *                                                               &lt;complexContent>
         *                                                                 &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
         *                                                                   &lt;attribute name="root" use="required" type="{http://www.w3.org/2001/XMLSchema}string" />
         *                                                                   &lt;attribute name="extension" use="required" type="{http://www.w3.org/2001/XMLSchema}string" />
         *                                                                 &lt;/restriction>
         *                                                               &lt;/complexContent>
         *                                                             &lt;/complexType>
         *                                                           &lt;/element>
         *                                                           &lt;element name="scopingOrganization">
         *                                                             &lt;complexType>
         *                                                               &lt;complexContent>
         *                                                                 &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
         *                                                                   &lt;sequence>
         *                                                                     &lt;element name="id" maxOccurs="unbounded">
         *                                                                       &lt;complexType>
         *                                                                         &lt;complexContent>
         *                                                                           &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
         *                                                                             &lt;attribute name="root" use="required" type="{http://www.w3.org/2001/XMLSchema}string" />
         *                                                                           &lt;/restriction>
         *                                                                         &lt;/complexContent>
         *                                                                       &lt;/complexType>
         *                                                                     &lt;/element>
         *                                                                   &lt;/sequence>
         *                                                                   &lt;attribute name="classCode" use="required" type="{http://www.w3.org/2001/XMLSchema}string" />
         *                                                                   &lt;attribute name="determinerCode" use="required" type="{http://www.w3.org/2001/XMLSchema}string" />
         *                                                                 &lt;/restriction>
         *                                                               &lt;/complexContent>
         *                                                             &lt;/complexType>
         *                                                           &lt;/element>
         *                                                         &lt;/sequence>
         *                                                         &lt;attribute name="classCode" use="required" type="{http://www.w3.org/2001/XMLSchema}string" />
         *                                                       &lt;/restriction>
         *                                                     &lt;/complexContent>
         *                                                   &lt;/complexType>
         *                                                 &lt;/element>
         *                                                 &lt;element name="personalRelationship" maxOccurs="unbounded">
         *                                                   &lt;complexType>
         *                                                     &lt;complexContent>
         *                                                       &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
         *                                                         &lt;sequence>
         *                                                           &lt;element name="code">
         *                                                             &lt;complexType>
         *                                                               &lt;complexContent>
         *                                                                 &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
         *                                                                   &lt;attribute name="code" use="required" type="{http://www.w3.org/2001/XMLSchema}string" />
         *                                                                   &lt;attribute name="codeSystem" use="required" type="{http://www.w3.org/2001/XMLSchema}string" />
         *                                                                 &lt;/restriction>
         *                                                               &lt;/complexContent>
         *                                                             &lt;/complexType>
         *                                                           &lt;/element>
         *                                                           &lt;element name="relationshipHolder1">
         *                                                             &lt;complexType>
         *                                                               &lt;complexContent>
         *                                                                 &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
         *                                                                   &lt;sequence>
         *                                                                     &lt;element name="name">
         *                                                                       &lt;complexType>
         *                                                                         &lt;complexContent>
         *                                                                           &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
         *                                                                             &lt;sequence>
         *                                                                               &lt;element name="given" type="{http://www.w3.org/2001/XMLSchema}string"/>
         *                                                                             &lt;/sequence>
         *                                                                             &lt;attribute name="use" use="required" type="{http://www.w3.org/2001/XMLSchema}string" />
         *                                                                           &lt;/restriction>
         *                                                                         &lt;/complexContent>
         *                                                                       &lt;/complexType>
         *                                                                     &lt;/element>
         *                                                                   &lt;/sequence>
         *                                                                   &lt;attribute name="classCode" use="required" type="{http://www.w3.org/2001/XMLSchema}string" />
         *                                                                   &lt;attribute name="determinerCode" use="required" type="{http://www.w3.org/2001/XMLSchema}string" />
         *                                                                 &lt;/restriction>
         *                                                               &lt;/complexContent>
         *                                                             &lt;/complexType>
         *                                                           &lt;/element>
         *                                                         &lt;/sequence>
         *                                                         &lt;attribute name="classCode" use="required" type="{http://www.w3.org/2001/XMLSchema}string" />
         *                                                       &lt;/restriction>
         *                                                     &lt;/complexContent>
         *                                                   &lt;/complexType>
         *                                                 &lt;/element>
         *                                                 &lt;element name="birthPlace">
         *                                                   &lt;complexType>
         *                                                     &lt;complexContent>
         *                                                       &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
         *                                                         &lt;sequence>
         *                                                           &lt;element name="addr">
         *                                                             &lt;complexType>
         *                                                               &lt;complexContent>
         *                                                                 &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
         *                                                                   &lt;sequence>
         *                                                                     &lt;element name="city" type="{http://www.w3.org/2001/XMLSchema}unsignedInt"/>
         *                                                                     &lt;element name="country" type="{http://www.w3.org/2001/XMLSchema}unsignedByte"/>
         *                                                                   &lt;/sequence>
         *                                                                 &lt;/restriction>
         *                                                               &lt;/complexContent>
         *                                                             &lt;/complexType>
         *                                                           &lt;/element>
         *                                                         &lt;/sequence>
         *                                                         &lt;attribute name="classCode" use="required" type="{http://www.w3.org/2001/XMLSchema}string" />
         *                                                       &lt;/restriction>
         *                                                     &lt;/complexContent>
         *                                                   &lt;/complexType>
         *                                                 &lt;/element>
         *                                               &lt;/sequence>
         *                                               &lt;attribute name="classCode" use="required" type="{http://www.w3.org/2001/XMLSchema}string" />
         *                                               &lt;attribute name="determinerCode" use="required" type="{http://www.w3.org/2001/XMLSchema}string" />
         *                                             &lt;/restriction>
         *                                           &lt;/complexContent>
         *                                         &lt;/complexType>
         *                                       &lt;/element>
         *                                       &lt;element name="providerOrganization">
         *                                         &lt;complexType>
         *                                           &lt;complexContent>
         *                                             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
         *                                               &lt;sequence>
         *                                                 &lt;element name="id" maxOccurs="unbounded">
         *                                                   &lt;complexType>
         *                                                     &lt;complexContent>
         *                                                       &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
         *                                                         &lt;attribute name="root" use="required" type="{http://www.w3.org/2001/XMLSchema}string" />
         *                                                       &lt;/restriction>
         *                                                     &lt;/complexContent>
         *                                                   &lt;/complexType>
         *                                                 &lt;/element>
         *                                                 &lt;element name="contactParty">
         *                                                   &lt;complexType>
         *                                                     &lt;complexContent>
         *                                                       &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
         *                                                         &lt;attribute name="classCode" use="required" type="{http://www.w3.org/2001/XMLSchema}string" />
         *                                                       &lt;/restriction>
         *                                                     &lt;/complexContent>
         *                                                   &lt;/complexType>
         *                                                 &lt;/element>
         *                                               &lt;/sequence>
         *                                               &lt;attribute name="classCode" use="required" type="{http://www.w3.org/2001/XMLSchema}string" />
         *                                               &lt;attribute name="determinerCode" use="required" type="{http://www.w3.org/2001/XMLSchema}string" />
         *                                             &lt;/restriction>
         *                                           &lt;/complexContent>
         *                                         &lt;/complexType>
         *                                       &lt;/element>
         *                                       &lt;element name="subjectOf1">
         *                                         &lt;complexType>
         *                                           &lt;complexContent>
         *                                             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
         *                                               &lt;sequence>
         *                                                 &lt;element name="queryMatchObservation">
         *                                                   &lt;complexType>
         *                                                     &lt;complexContent>
         *                                                       &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
         *                                                         &lt;sequence>
         *                                                           &lt;element name="code">
         *                                                             &lt;complexType>
         *                                                               &lt;complexContent>
         *                                                                 &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
         *                                                                   &lt;attribute name="code" use="required" type="{http://www.w3.org/2001/XMLSchema}string" />
         *                                                                 &lt;/restriction>
         *                                                               &lt;/complexContent>
         *                                                             &lt;/complexType>
         *                                                           &lt;/element>
         *                                                           &lt;element name="value">
         *                                                             &lt;complexType>
         *                                                               &lt;complexContent>
         *                                                                 &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
         *                                                                   &lt;attribute name="value" use="required" type="{http://www.w3.org/2001/XMLSchema}unsignedByte" />
         *                                                                 &lt;/restriction>
         *                                                               &lt;/complexContent>
         *                                                             &lt;/complexType>
         *                                                           &lt;/element>
         *                                                         &lt;/sequence>
         *                                                         &lt;attribute name="classCode" use="required" type="{http://www.w3.org/2001/XMLSchema}string" />
         *                                                         &lt;attribute name="moodCode" use="required" type="{http://www.w3.org/2001/XMLSchema}string" />
         *                                                       &lt;/restriction>
         *                                                     &lt;/complexContent>
         *                                                   &lt;/complexType>
         *                                                 &lt;/element>
         *                                               &lt;/sequence>
         *                                             &lt;/restriction>
         *                                           &lt;/complexContent>
         *                                         &lt;/complexType>
         *                                       &lt;/element>
         *                                     &lt;/sequence>
         *                                     &lt;attribute name="classCode" use="required" type="{http://www.w3.org/2001/XMLSchema}string" />
         *                                   &lt;/restriction>
         *                                 &lt;/complexContent>
         *                               &lt;/complexType>
         *                             &lt;/element>
         *                           &lt;/sequence>
         *                           &lt;attribute name="typeCode" use="required" type="{http://www.w3.org/2001/XMLSchema}string" />
         *                         &lt;/restriction>
         *                       &lt;/complexContent>
         *                     &lt;/complexType>
         *                   &lt;/element>
         *                   &lt;element name="custodian">
         *                     &lt;complexType>
         *                       &lt;complexContent>
         *                         &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
         *                           &lt;sequence>
         *                             &lt;element name="assignedEntity">
         *                               &lt;complexType>
         *                                 &lt;complexContent>
         *                                   &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
         *                                     &lt;sequence>
         *                                       &lt;element name="id">
         *                                         &lt;complexType>
         *                                           &lt;complexContent>
         *                                             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
         *                                               &lt;attribute name="root" use="required" type="{http://www.w3.org/2001/XMLSchema}string" />
         *                                             &lt;/restriction>
         *                                           &lt;/complexContent>
         *                                         &lt;/complexType>
         *                                       &lt;/element>
         *                                     &lt;/sequence>
         *                                     &lt;attribute name="classCode" use="required" type="{http://www.w3.org/2001/XMLSchema}string" />
         *                                   &lt;/restriction>
         *                                 &lt;/complexContent>
         *                               &lt;/complexType>
         *                             &lt;/element>
         *                           &lt;/sequence>
         *                           &lt;attribute name="typeCode" use="required" type="{http://www.w3.org/2001/XMLSchema}string" />
         *                         &lt;/restriction>
         *                       &lt;/complexContent>
         *                     &lt;/complexType>
         *                   &lt;/element>
         *                 &lt;/sequence>
         *                 &lt;attribute name="classCode" use="required" type="{http://www.w3.org/2001/XMLSchema}string" />
         *                 &lt;attribute name="moodCode" use="required" type="{http://www.w3.org/2001/XMLSchema}string" />
         *               &lt;/restriction>
         *             &lt;/complexContent>
         *           &lt;/complexType>
         *         &lt;/element>
         *       &lt;/sequence>
         *       &lt;attribute name="typeCode" use="required" type="{http://www.w3.org/2001/XMLSchema}string" />
         *       &lt;attribute name="contextConductionInd" use="required" type="{http://www.w3.org/2001/XMLSchema}boolean" />
         *     &lt;/restriction>
         *   &lt;/complexContent>
         * &lt;/complexType>
         * </pre>
         * 
         * 
         */
        @XmlAccessorType(XmlAccessType.FIELD)
        @XmlType(name = "", propOrder = {
            "registrationEvent"
        })
        public static class Subject {

            @XmlElement(required = true)
            protected PRPAIN201306UV02 .ControlActProcess.Subject.RegistrationEvent registrationEvent;
            @XmlAttribute(name = "typeCode", required = true)
            protected String typeCode;
            @XmlAttribute(name = "contextConductionInd", required = true)
            protected boolean contextConductionInd;

            /**
             * Obtém o valor da propriedade registrationEvent.
             * 
             * @return
             *     possible object is
             *     {@link PRPAIN201306UV02 .ControlActProcess.Subject.RegistrationEvent }
             *     
             */
            public PRPAIN201306UV02 .ControlActProcess.Subject.RegistrationEvent getRegistrationEvent() {
                return registrationEvent;
            }

            /**
             * Define o valor da propriedade registrationEvent.
             * 
             * @param value
             *     allowed object is
             *     {@link PRPAIN201306UV02 .ControlActProcess.Subject.RegistrationEvent }
             *     
             */
            public void setRegistrationEvent(PRPAIN201306UV02 .ControlActProcess.Subject.RegistrationEvent value) {
                this.registrationEvent = value;
            }

            /**
             * Obtém o valor da propriedade typeCode.
             * 
             * @return
             *     possible object is
             *     {@link String }
             *     
             */
            public String getTypeCode() {
                return typeCode;
            }

            /**
             * Define o valor da propriedade typeCode.
             * 
             * @param value
             *     allowed object is
             *     {@link String }
             *     
             */
            public void setTypeCode(String value) {
                this.typeCode = value;
            }

            /**
             * Obtém o valor da propriedade contextConductionInd.
             * 
             */
            public boolean isContextConductionInd() {
                return contextConductionInd;
            }

            /**
             * Define o valor da propriedade contextConductionInd.
             * 
             */
            public void setContextConductionInd(boolean value) {
                this.contextConductionInd = value;
            }


            /**
             * <p>Classe Java de anonymous complex type.
             * 
             * <p>O seguinte fragmento do esquema especifica o conteúdo esperado contido dentro desta classe.
             * 
             * <pre>
             * &lt;complexType>
             *   &lt;complexContent>
             *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
             *       &lt;sequence>
             *         &lt;element name="statusCode">
             *           &lt;complexType>
             *             &lt;complexContent>
             *               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
             *                 &lt;attribute name="code" use="required" type="{http://www.w3.org/2001/XMLSchema}string" />
             *               &lt;/restriction>
             *             &lt;/complexContent>
             *           &lt;/complexType>
             *         &lt;/element>
             *         &lt;element name="subject1">
             *           &lt;complexType>
             *             &lt;complexContent>
             *               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
             *                 &lt;sequence>
             *                   &lt;element name="realmCode">
             *                     &lt;complexType>
             *                       &lt;complexContent>
             *                         &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
             *                           &lt;attribute name="code" use="required" type="{http://www.w3.org/2001/XMLSchema}string" />
             *                         &lt;/restriction>
             *                       &lt;/complexContent>
             *                     &lt;/complexType>
             *                   &lt;/element>
             *                   &lt;element name="patient">
             *                     &lt;complexType>
             *                       &lt;complexContent>
             *                         &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
             *                           &lt;sequence>
             *                             &lt;element name="id" maxOccurs="unbounded">
             *                               &lt;complexType>
             *                                 &lt;complexContent>
             *                                   &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
             *                                     &lt;attribute name="root" use="required" type="{http://www.w3.org/2001/XMLSchema}string" />
             *                                     &lt;attribute name="extension" use="required" type="{http://www.w3.org/2001/XMLSchema}unsignedLong" />
             *                                     &lt;attribute name="assigningAuthorityName" use="required" type="{http://www.w3.org/2001/XMLSchema}string" />
             *                                   &lt;/restriction>
             *                                 &lt;/complexContent>
             *                               &lt;/complexType>
             *                             &lt;/element>
             *                             &lt;element name="statusCode">
             *                               &lt;complexType>
             *                                 &lt;complexContent>
             *                                   &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
             *                                     &lt;attribute name="code" use="required" type="{http://www.w3.org/2001/XMLSchema}string" />
             *                                   &lt;/restriction>
             *                                 &lt;/complexContent>
             *                               &lt;/complexType>
             *                             &lt;/element>
             *                             &lt;element name="patientPerson">
             *                               &lt;complexType>
             *                                 &lt;complexContent>
             *                                   &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
             *                                     &lt;sequence>
             *                                       &lt;element name="name">
             *                                         &lt;complexType>
             *                                           &lt;complexContent>
             *                                             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
             *                                               &lt;sequence>
             *                                                 &lt;element name="given" type="{http://www.w3.org/2001/XMLSchema}string"/>
             *                                               &lt;/sequence>
             *                                               &lt;attribute name="use" use="required" type="{http://www.w3.org/2001/XMLSchema}string" />
             *                                             &lt;/restriction>
             *                                           &lt;/complexContent>
             *                                         &lt;/complexType>
             *                                       &lt;/element>
             *                                       &lt;element name="administrativeGenderCode">
             *                                         &lt;complexType>
             *                                           &lt;complexContent>
             *                                             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
             *                                               &lt;attribute name="code" use="required" type="{http://www.w3.org/2001/XMLSchema}string" />
             *                                               &lt;attribute name="codeSystem" use="required" type="{http://www.w3.org/2001/XMLSchema}string" />
             *                                             &lt;/restriction>
             *                                           &lt;/complexContent>
             *                                         &lt;/complexType>
             *                                       &lt;/element>
             *                                       &lt;element name="birthTime">
             *                                         &lt;complexType>
             *                                           &lt;complexContent>
             *                                             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
             *                                               &lt;attribute name="value" use="required" type="{http://www.w3.org/2001/XMLSchema}unsignedLong" />
             *                                             &lt;/restriction>
             *                                           &lt;/complexContent>
             *                                         &lt;/complexType>
             *                                       &lt;/element>
             *                                       &lt;element name="addr">
             *                                         &lt;complexType>
             *                                           &lt;complexContent>
             *                                             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
             *                                               &lt;sequence>
             *                                                 &lt;element name="city" type="{http://www.w3.org/2001/XMLSchema}unsignedInt"/>
             *                                                 &lt;element name="state" type="{http://www.w3.org/2001/XMLSchema}string"/>
             *                                                 &lt;element name="postalCode" type="{http://www.w3.org/2001/XMLSchema}unsignedInt"/>
             *                                                 &lt;element name="country" type="{http://www.w3.org/2001/XMLSchema}unsignedByte"/>
             *                                                 &lt;element name="houseNumber" type="{http://www.w3.org/2001/XMLSchema}unsignedShort"/>
             *                                                 &lt;element name="streetNameType" type="{http://www.w3.org/2001/XMLSchema}unsignedByte"/>
             *                                                 &lt;element name="additionalLocator" type="{http://www.w3.org/2001/XMLSchema}string"/>
             *                                               &lt;/sequence>
             *                                               &lt;attribute name="use" use="required" type="{http://www.w3.org/2001/XMLSchema}string" />
             *                                             &lt;/restriction>
             *                                           &lt;/complexContent>
             *                                         &lt;/complexType>
             *                                       &lt;/element>
             *                                       &lt;element name="maritalStatusCode">
             *                                         &lt;complexType>
             *                                           &lt;complexContent>
             *                                             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
             *                                               &lt;attribute name="code" use="required" type="{http://www.w3.org/2001/XMLSchema}unsignedByte" />
             *                                             &lt;/restriction>
             *                                           &lt;/complexContent>
             *                                         &lt;/complexType>
             *                                       &lt;/element>
             *                                       &lt;element name="raceCode">
             *                                         &lt;complexType>
             *                                           &lt;complexContent>
             *                                             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
             *                                               &lt;attribute name="code" use="required" type="{http://www.w3.org/2001/XMLSchema}unsignedByte" />
             *                                             &lt;/restriction>
             *                                           &lt;/complexContent>
             *                                         &lt;/complexType>
             *                                       &lt;/element>
             *                                       &lt;element name="asOtherIDs" maxOccurs="unbounded">
             *                                         &lt;complexType>
             *                                           &lt;complexContent>
             *                                             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
             *                                               &lt;sequence>
             *                                                 &lt;element name="id" maxOccurs="unbounded">
             *                                                   &lt;complexType>
             *                                                     &lt;complexContent>
             *                                                       &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
             *                                                         &lt;attribute name="root" use="required" type="{http://www.w3.org/2001/XMLSchema}string" />
             *                                                         &lt;attribute name="extension" use="required" type="{http://www.w3.org/2001/XMLSchema}string" />
             *                                                       &lt;/restriction>
             *                                                     &lt;/complexContent>
             *                                                   &lt;/complexType>
             *                                                 &lt;/element>
             *                                                 &lt;element name="scopingOrganization">
             *                                                   &lt;complexType>
             *                                                     &lt;complexContent>
             *                                                       &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
             *                                                         &lt;sequence>
             *                                                           &lt;element name="id" maxOccurs="unbounded">
             *                                                             &lt;complexType>
             *                                                               &lt;complexContent>
             *                                                                 &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
             *                                                                   &lt;attribute name="root" use="required" type="{http://www.w3.org/2001/XMLSchema}string" />
             *                                                                 &lt;/restriction>
             *                                                               &lt;/complexContent>
             *                                                             &lt;/complexType>
             *                                                           &lt;/element>
             *                                                         &lt;/sequence>
             *                                                         &lt;attribute name="classCode" use="required" type="{http://www.w3.org/2001/XMLSchema}string" />
             *                                                         &lt;attribute name="determinerCode" use="required" type="{http://www.w3.org/2001/XMLSchema}string" />
             *                                                       &lt;/restriction>
             *                                                     &lt;/complexContent>
             *                                                   &lt;/complexType>
             *                                                 &lt;/element>
             *                                               &lt;/sequence>
             *                                               &lt;attribute name="classCode" use="required" type="{http://www.w3.org/2001/XMLSchema}string" />
             *                                             &lt;/restriction>
             *                                           &lt;/complexContent>
             *                                         &lt;/complexType>
             *                                       &lt;/element>
             *                                       &lt;element name="personalRelationship" maxOccurs="unbounded">
             *                                         &lt;complexType>
             *                                           &lt;complexContent>
             *                                             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
             *                                               &lt;sequence>
             *                                                 &lt;element name="code">
             *                                                   &lt;complexType>
             *                                                     &lt;complexContent>
             *                                                       &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
             *                                                         &lt;attribute name="code" use="required" type="{http://www.w3.org/2001/XMLSchema}string" />
             *                                                         &lt;attribute name="codeSystem" use="required" type="{http://www.w3.org/2001/XMLSchema}string" />
             *                                                       &lt;/restriction>
             *                                                     &lt;/complexContent>
             *                                                   &lt;/complexType>
             *                                                 &lt;/element>
             *                                                 &lt;element name="relationshipHolder1">
             *                                                   &lt;complexType>
             *                                                     &lt;complexContent>
             *                                                       &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
             *                                                         &lt;sequence>
             *                                                           &lt;element name="name">
             *                                                             &lt;complexType>
             *                                                               &lt;complexContent>
             *                                                                 &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
             *                                                                   &lt;sequence>
             *                                                                     &lt;element name="given" type="{http://www.w3.org/2001/XMLSchema}string"/>
             *                                                                   &lt;/sequence>
             *                                                                   &lt;attribute name="use" use="required" type="{http://www.w3.org/2001/XMLSchema}string" />
             *                                                                 &lt;/restriction>
             *                                                               &lt;/complexContent>
             *                                                             &lt;/complexType>
             *                                                           &lt;/element>
             *                                                         &lt;/sequence>
             *                                                         &lt;attribute name="classCode" use="required" type="{http://www.w3.org/2001/XMLSchema}string" />
             *                                                         &lt;attribute name="determinerCode" use="required" type="{http://www.w3.org/2001/XMLSchema}string" />
             *                                                       &lt;/restriction>
             *                                                     &lt;/complexContent>
             *                                                   &lt;/complexType>
             *                                                 &lt;/element>
             *                                               &lt;/sequence>
             *                                               &lt;attribute name="classCode" use="required" type="{http://www.w3.org/2001/XMLSchema}string" />
             *                                             &lt;/restriction>
             *                                           &lt;/complexContent>
             *                                         &lt;/complexType>
             *                                       &lt;/element>
             *                                       &lt;element name="birthPlace">
             *                                         &lt;complexType>
             *                                           &lt;complexContent>
             *                                             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
             *                                               &lt;sequence>
             *                                                 &lt;element name="addr">
             *                                                   &lt;complexType>
             *                                                     &lt;complexContent>
             *                                                       &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
             *                                                         &lt;sequence>
             *                                                           &lt;element name="city" type="{http://www.w3.org/2001/XMLSchema}unsignedInt"/>
             *                                                           &lt;element name="country" type="{http://www.w3.org/2001/XMLSchema}unsignedByte"/>
             *                                                         &lt;/sequence>
             *                                                       &lt;/restriction>
             *                                                     &lt;/complexContent>
             *                                                   &lt;/complexType>
             *                                                 &lt;/element>
             *                                               &lt;/sequence>
             *                                               &lt;attribute name="classCode" use="required" type="{http://www.w3.org/2001/XMLSchema}string" />
             *                                             &lt;/restriction>
             *                                           &lt;/complexContent>
             *                                         &lt;/complexType>
             *                                       &lt;/element>
             *                                     &lt;/sequence>
             *                                     &lt;attribute name="classCode" use="required" type="{http://www.w3.org/2001/XMLSchema}string" />
             *                                     &lt;attribute name="determinerCode" use="required" type="{http://www.w3.org/2001/XMLSchema}string" />
             *                                   &lt;/restriction>
             *                                 &lt;/complexContent>
             *                               &lt;/complexType>
             *                             &lt;/element>
             *                             &lt;element name="providerOrganization">
             *                               &lt;complexType>
             *                                 &lt;complexContent>
             *                                   &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
             *                                     &lt;sequence>
             *                                       &lt;element name="id" maxOccurs="unbounded">
             *                                         &lt;complexType>
             *                                           &lt;complexContent>
             *                                             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
             *                                               &lt;attribute name="root" use="required" type="{http://www.w3.org/2001/XMLSchema}string" />
             *                                             &lt;/restriction>
             *                                           &lt;/complexContent>
             *                                         &lt;/complexType>
             *                                       &lt;/element>
             *                                       &lt;element name="contactParty">
             *                                         &lt;complexType>
             *                                           &lt;complexContent>
             *                                             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
             *                                               &lt;attribute name="classCode" use="required" type="{http://www.w3.org/2001/XMLSchema}string" />
             *                                             &lt;/restriction>
             *                                           &lt;/complexContent>
             *                                         &lt;/complexType>
             *                                       &lt;/element>
             *                                     &lt;/sequence>
             *                                     &lt;attribute name="classCode" use="required" type="{http://www.w3.org/2001/XMLSchema}string" />
             *                                     &lt;attribute name="determinerCode" use="required" type="{http://www.w3.org/2001/XMLSchema}string" />
             *                                   &lt;/restriction>
             *                                 &lt;/complexContent>
             *                               &lt;/complexType>
             *                             &lt;/element>
             *                             &lt;element name="subjectOf1">
             *                               &lt;complexType>
             *                                 &lt;complexContent>
             *                                   &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
             *                                     &lt;sequence>
             *                                       &lt;element name="queryMatchObservation">
             *                                         &lt;complexType>
             *                                           &lt;complexContent>
             *                                             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
             *                                               &lt;sequence>
             *                                                 &lt;element name="code">
             *                                                   &lt;complexType>
             *                                                     &lt;complexContent>
             *                                                       &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
             *                                                         &lt;attribute name="code" use="required" type="{http://www.w3.org/2001/XMLSchema}string" />
             *                                                       &lt;/restriction>
             *                                                     &lt;/complexContent>
             *                                                   &lt;/complexType>
             *                                                 &lt;/element>
             *                                                 &lt;element name="value">
             *                                                   &lt;complexType>
             *                                                     &lt;complexContent>
             *                                                       &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
             *                                                         &lt;attribute name="value" use="required" type="{http://www.w3.org/2001/XMLSchema}unsignedByte" />
             *                                                       &lt;/restriction>
             *                                                     &lt;/complexContent>
             *                                                   &lt;/complexType>
             *                                                 &lt;/element>
             *                                               &lt;/sequence>
             *                                               &lt;attribute name="classCode" use="required" type="{http://www.w3.org/2001/XMLSchema}string" />
             *                                               &lt;attribute name="moodCode" use="required" type="{http://www.w3.org/2001/XMLSchema}string" />
             *                                             &lt;/restriction>
             *                                           &lt;/complexContent>
             *                                         &lt;/complexType>
             *                                       &lt;/element>
             *                                     &lt;/sequence>
             *                                   &lt;/restriction>
             *                                 &lt;/complexContent>
             *                               &lt;/complexType>
             *                             &lt;/element>
             *                           &lt;/sequence>
             *                           &lt;attribute name="classCode" use="required" type="{http://www.w3.org/2001/XMLSchema}string" />
             *                         &lt;/restriction>
             *                       &lt;/complexContent>
             *                     &lt;/complexType>
             *                   &lt;/element>
             *                 &lt;/sequence>
             *                 &lt;attribute name="typeCode" use="required" type="{http://www.w3.org/2001/XMLSchema}string" />
             *               &lt;/restriction>
             *             &lt;/complexContent>
             *           &lt;/complexType>
             *         &lt;/element>
             *         &lt;element name="custodian">
             *           &lt;complexType>
             *             &lt;complexContent>
             *               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
             *                 &lt;sequence>
             *                   &lt;element name="assignedEntity">
             *                     &lt;complexType>
             *                       &lt;complexContent>
             *                         &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
             *                           &lt;sequence>
             *                             &lt;element name="id">
             *                               &lt;complexType>
             *                                 &lt;complexContent>
             *                                   &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
             *                                     &lt;attribute name="root" use="required" type="{http://www.w3.org/2001/XMLSchema}string" />
             *                                   &lt;/restriction>
             *                                 &lt;/complexContent>
             *                               &lt;/complexType>
             *                             &lt;/element>
             *                           &lt;/sequence>
             *                           &lt;attribute name="classCode" use="required" type="{http://www.w3.org/2001/XMLSchema}string" />
             *                         &lt;/restriction>
             *                       &lt;/complexContent>
             *                     &lt;/complexType>
             *                   &lt;/element>
             *                 &lt;/sequence>
             *                 &lt;attribute name="typeCode" use="required" type="{http://www.w3.org/2001/XMLSchema}string" />
             *               &lt;/restriction>
             *             &lt;/complexContent>
             *           &lt;/complexType>
             *         &lt;/element>
             *       &lt;/sequence>
             *       &lt;attribute name="classCode" use="required" type="{http://www.w3.org/2001/XMLSchema}string" />
             *       &lt;attribute name="moodCode" use="required" type="{http://www.w3.org/2001/XMLSchema}string" />
             *     &lt;/restriction>
             *   &lt;/complexContent>
             * &lt;/complexType>
             * </pre>
             * 
             * 
             */
            @XmlAccessorType(XmlAccessType.FIELD)
            @XmlType(name = "", propOrder = {
                "statusCode",
                "subject1",
                "custodian"
            })
            public static class RegistrationEvent {

                @XmlElement(required = true)
                protected PRPAIN201306UV02 .ControlActProcess.Subject.RegistrationEvent.StatusCode statusCode;
                @XmlElement(required = true)
                protected PRPAIN201306UV02 .ControlActProcess.Subject.RegistrationEvent.Subject1 subject1;
                @XmlElement(required = true)
                protected PRPAIN201306UV02 .ControlActProcess.Subject.RegistrationEvent.Custodian custodian;
                @XmlAttribute(name = "classCode", required = true)
                protected String classCode;
                @XmlAttribute(name = "moodCode", required = true)
                protected String moodCode;

                /**
                 * Obtém o valor da propriedade statusCode.
                 * 
                 * @return
                 *     possible object is
                 *     {@link PRPAIN201306UV02 .ControlActProcess.Subject.RegistrationEvent.StatusCode }
                 *     
                 */
                public PRPAIN201306UV02 .ControlActProcess.Subject.RegistrationEvent.StatusCode getStatusCode() {
                    return statusCode;
                }

                /**
                 * Define o valor da propriedade statusCode.
                 * 
                 * @param value
                 *     allowed object is
                 *     {@link PRPAIN201306UV02 .ControlActProcess.Subject.RegistrationEvent.StatusCode }
                 *     
                 */
                public void setStatusCode(PRPAIN201306UV02 .ControlActProcess.Subject.RegistrationEvent.StatusCode value) {
                    this.statusCode = value;
                }

                /**
                 * Obtém o valor da propriedade subject1.
                 * 
                 * @return
                 *     possible object is
                 *     {@link PRPAIN201306UV02 .ControlActProcess.Subject.RegistrationEvent.Subject1 }
                 *     
                 */
                public PRPAIN201306UV02 .ControlActProcess.Subject.RegistrationEvent.Subject1 getSubject1() {
                    return subject1;
                }

                /**
                 * Define o valor da propriedade subject1.
                 * 
                 * @param value
                 *     allowed object is
                 *     {@link PRPAIN201306UV02 .ControlActProcess.Subject.RegistrationEvent.Subject1 }
                 *     
                 */
                public void setSubject1(PRPAIN201306UV02 .ControlActProcess.Subject.RegistrationEvent.Subject1 value) {
                    this.subject1 = value;
                }

                /**
                 * Obtém o valor da propriedade custodian.
                 * 
                 * @return
                 *     possible object is
                 *     {@link PRPAIN201306UV02 .ControlActProcess.Subject.RegistrationEvent.Custodian }
                 *     
                 */
                public PRPAIN201306UV02 .ControlActProcess.Subject.RegistrationEvent.Custodian getCustodian() {
                    return custodian;
                }

                /**
                 * Define o valor da propriedade custodian.
                 * 
                 * @param value
                 *     allowed object is
                 *     {@link PRPAIN201306UV02 .ControlActProcess.Subject.RegistrationEvent.Custodian }
                 *     
                 */
                public void setCustodian(PRPAIN201306UV02 .ControlActProcess.Subject.RegistrationEvent.Custodian value) {
                    this.custodian = value;
                }

                /**
                 * Obtém o valor da propriedade classCode.
                 * 
                 * @return
                 *     possible object is
                 *     {@link String }
                 *     
                 */
                public String getClassCode() {
                    return classCode;
                }

                /**
                 * Define o valor da propriedade classCode.
                 * 
                 * @param value
                 *     allowed object is
                 *     {@link String }
                 *     
                 */
                public void setClassCode(String value) {
                    this.classCode = value;
                }

                /**
                 * Obtém o valor da propriedade moodCode.
                 * 
                 * @return
                 *     possible object is
                 *     {@link String }
                 *     
                 */
                public String getMoodCode() {
                    return moodCode;
                }

                /**
                 * Define o valor da propriedade moodCode.
                 * 
                 * @param value
                 *     allowed object is
                 *     {@link String }
                 *     
                 */
                public void setMoodCode(String value) {
                    this.moodCode = value;
                }


                /**
                 * <p>Classe Java de anonymous complex type.
                 * 
                 * <p>O seguinte fragmento do esquema especifica o conteúdo esperado contido dentro desta classe.
                 * 
                 * <pre>
                 * &lt;complexType>
                 *   &lt;complexContent>
                 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
                 *       &lt;sequence>
                 *         &lt;element name="assignedEntity">
                 *           &lt;complexType>
                 *             &lt;complexContent>
                 *               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
                 *                 &lt;sequence>
                 *                   &lt;element name="id">
                 *                     &lt;complexType>
                 *                       &lt;complexContent>
                 *                         &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
                 *                           &lt;attribute name="root" use="required" type="{http://www.w3.org/2001/XMLSchema}string" />
                 *                         &lt;/restriction>
                 *                       &lt;/complexContent>
                 *                     &lt;/complexType>
                 *                   &lt;/element>
                 *                 &lt;/sequence>
                 *                 &lt;attribute name="classCode" use="required" type="{http://www.w3.org/2001/XMLSchema}string" />
                 *               &lt;/restriction>
                 *             &lt;/complexContent>
                 *           &lt;/complexType>
                 *         &lt;/element>
                 *       &lt;/sequence>
                 *       &lt;attribute name="typeCode" use="required" type="{http://www.w3.org/2001/XMLSchema}string" />
                 *     &lt;/restriction>
                 *   &lt;/complexContent>
                 * &lt;/complexType>
                 * </pre>
                 * 
                 * 
                 */
                @XmlAccessorType(XmlAccessType.FIELD)
                @XmlType(name = "", propOrder = {
                    "assignedEntity"
                })
                public static class Custodian {

                    @XmlElement(required = true)
                    protected PRPAIN201306UV02 .ControlActProcess.Subject.RegistrationEvent.Custodian.AssignedEntity assignedEntity;
                    @XmlAttribute(name = "typeCode", required = true)
                    protected String typeCode;

                    /**
                     * Obtém o valor da propriedade assignedEntity.
                     * 
                     * @return
                     *     possible object is
                     *     {@link PRPAIN201306UV02 .ControlActProcess.Subject.RegistrationEvent.Custodian.AssignedEntity }
                     *     
                     */
                    public PRPAIN201306UV02 .ControlActProcess.Subject.RegistrationEvent.Custodian.AssignedEntity getAssignedEntity() {
                        return assignedEntity;
                    }

                    /**
                     * Define o valor da propriedade assignedEntity.
                     * 
                     * @param value
                     *     allowed object is
                     *     {@link PRPAIN201306UV02 .ControlActProcess.Subject.RegistrationEvent.Custodian.AssignedEntity }
                     *     
                     */
                    public void setAssignedEntity(PRPAIN201306UV02 .ControlActProcess.Subject.RegistrationEvent.Custodian.AssignedEntity value) {
                        this.assignedEntity = value;
                    }

                    /**
                     * Obtém o valor da propriedade typeCode.
                     * 
                     * @return
                     *     possible object is
                     *     {@link String }
                     *     
                     */
                    public String getTypeCode() {
                        return typeCode;
                    }

                    /**
                     * Define o valor da propriedade typeCode.
                     * 
                     * @param value
                     *     allowed object is
                     *     {@link String }
                     *     
                     */
                    public void setTypeCode(String value) {
                        this.typeCode = value;
                    }


                    /**
                     * <p>Classe Java de anonymous complex type.
                     * 
                     * <p>O seguinte fragmento do esquema especifica o conteúdo esperado contido dentro desta classe.
                     * 
                     * <pre>
                     * &lt;complexType>
                     *   &lt;complexContent>
                     *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
                     *       &lt;sequence>
                     *         &lt;element name="id">
                     *           &lt;complexType>
                     *             &lt;complexContent>
                     *               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
                     *                 &lt;attribute name="root" use="required" type="{http://www.w3.org/2001/XMLSchema}string" />
                     *               &lt;/restriction>
                     *             &lt;/complexContent>
                     *           &lt;/complexType>
                     *         &lt;/element>
                     *       &lt;/sequence>
                     *       &lt;attribute name="classCode" use="required" type="{http://www.w3.org/2001/XMLSchema}string" />
                     *     &lt;/restriction>
                     *   &lt;/complexContent>
                     * &lt;/complexType>
                     * </pre>
                     * 
                     * 
                     */
                    @XmlAccessorType(XmlAccessType.FIELD)
                    @XmlType(name = "", propOrder = {
                        "id"
                    })
                    public static class AssignedEntity {

                        @XmlElement(required = true)
                        protected PRPAIN201306UV02 .ControlActProcess.Subject.RegistrationEvent.Custodian.AssignedEntity.Id id;
                        @XmlAttribute(name = "classCode", required = true)
                        protected String classCode;

                        /**
                         * Obtém o valor da propriedade id.
                         * 
                         * @return
                         *     possible object is
                         *     {@link PRPAIN201306UV02 .ControlActProcess.Subject.RegistrationEvent.Custodian.AssignedEntity.Id }
                         *     
                         */
                        public PRPAIN201306UV02 .ControlActProcess.Subject.RegistrationEvent.Custodian.AssignedEntity.Id getId() {
                            return id;
                        }

                        /**
                         * Define o valor da propriedade id.
                         * 
                         * @param value
                         *     allowed object is
                         *     {@link PRPAIN201306UV02 .ControlActProcess.Subject.RegistrationEvent.Custodian.AssignedEntity.Id }
                         *     
                         */
                        public void setId(PRPAIN201306UV02 .ControlActProcess.Subject.RegistrationEvent.Custodian.AssignedEntity.Id value) {
                            this.id = value;
                        }

                        /**
                         * Obtém o valor da propriedade classCode.
                         * 
                         * @return
                         *     possible object is
                         *     {@link String }
                         *     
                         */
                        public String getClassCode() {
                            return classCode;
                        }

                        /**
                         * Define o valor da propriedade classCode.
                         * 
                         * @param value
                         *     allowed object is
                         *     {@link String }
                         *     
                         */
                        public void setClassCode(String value) {
                            this.classCode = value;
                        }


                        /**
                         * <p>Classe Java de anonymous complex type.
                         * 
                         * <p>O seguinte fragmento do esquema especifica o conteúdo esperado contido dentro desta classe.
                         * 
                         * <pre>
                         * &lt;complexType>
                         *   &lt;complexContent>
                         *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
                         *       &lt;attribute name="root" use="required" type="{http://www.w3.org/2001/XMLSchema}string" />
                         *     &lt;/restriction>
                         *   &lt;/complexContent>
                         * &lt;/complexType>
                         * </pre>
                         * 
                         * 
                         */
                        @XmlAccessorType(XmlAccessType.FIELD)
                        @XmlType(name = "")
                        public static class Id {

                            @XmlAttribute(name = "root", required = true)
                            protected String root;

                            /**
                             * Obtém o valor da propriedade root.
                             * 
                             * @return
                             *     possible object is
                             *     {@link String }
                             *     
                             */
                            public String getRoot() {
                                return root;
                            }

                            /**
                             * Define o valor da propriedade root.
                             * 
                             * @param value
                             *     allowed object is
                             *     {@link String }
                             *     
                             */
                            public void setRoot(String value) {
                                this.root = value;
                            }

                        }

                    }

                }


                /**
                 * <p>Classe Java de anonymous complex type.
                 * 
                 * <p>O seguinte fragmento do esquema especifica o conteúdo esperado contido dentro desta classe.
                 * 
                 * <pre>
                 * &lt;complexType>
                 *   &lt;complexContent>
                 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
                 *       &lt;attribute name="code" use="required" type="{http://www.w3.org/2001/XMLSchema}string" />
                 *     &lt;/restriction>
                 *   &lt;/complexContent>
                 * &lt;/complexType>
                 * </pre>
                 * 
                 * 
                 */
                @XmlAccessorType(XmlAccessType.FIELD)
                @XmlType(name = "")
                public static class StatusCode {

                    @XmlAttribute(name = "code", required = true)
                    protected String code;

                    /**
                     * Obtém o valor da propriedade code.
                     * 
                     * @return
                     *     possible object is
                     *     {@link String }
                     *     
                     */
                    public String getCode() {
                        return code;
                    }

                    /**
                     * Define o valor da propriedade code.
                     * 
                     * @param value
                     *     allowed object is
                     *     {@link String }
                     *     
                     */
                    public void setCode(String value) {
                        this.code = value;
                    }

                }


                /**
                 * <p>Classe Java de anonymous complex type.
                 * 
                 * <p>O seguinte fragmento do esquema especifica o conteúdo esperado contido dentro desta classe.
                 * 
                 * <pre>
                 * &lt;complexType>
                 *   &lt;complexContent>
                 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
                 *       &lt;sequence>
                 *         &lt;element name="realmCode">
                 *           &lt;complexType>
                 *             &lt;complexContent>
                 *               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
                 *                 &lt;attribute name="code" use="required" type="{http://www.w3.org/2001/XMLSchema}string" />
                 *               &lt;/restriction>
                 *             &lt;/complexContent>
                 *           &lt;/complexType>
                 *         &lt;/element>
                 *         &lt;element name="patient">
                 *           &lt;complexType>
                 *             &lt;complexContent>
                 *               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
                 *                 &lt;sequence>
                 *                   &lt;element name="id" maxOccurs="unbounded">
                 *                     &lt;complexType>
                 *                       &lt;complexContent>
                 *                         &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
                 *                           &lt;attribute name="root" use="required" type="{http://www.w3.org/2001/XMLSchema}string" />
                 *                           &lt;attribute name="extension" use="required" type="{http://www.w3.org/2001/XMLSchema}unsignedLong" />
                 *                           &lt;attribute name="assigningAuthorityName" use="required" type="{http://www.w3.org/2001/XMLSchema}string" />
                 *                         &lt;/restriction>
                 *                       &lt;/complexContent>
                 *                     &lt;/complexType>
                 *                   &lt;/element>
                 *                   &lt;element name="statusCode">
                 *                     &lt;complexType>
                 *                       &lt;complexContent>
                 *                         &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
                 *                           &lt;attribute name="code" use="required" type="{http://www.w3.org/2001/XMLSchema}string" />
                 *                         &lt;/restriction>
                 *                       &lt;/complexContent>
                 *                     &lt;/complexType>
                 *                   &lt;/element>
                 *                   &lt;element name="patientPerson">
                 *                     &lt;complexType>
                 *                       &lt;complexContent>
                 *                         &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
                 *                           &lt;sequence>
                 *                             &lt;element name="name">
                 *                               &lt;complexType>
                 *                                 &lt;complexContent>
                 *                                   &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
                 *                                     &lt;sequence>
                 *                                       &lt;element name="given" type="{http://www.w3.org/2001/XMLSchema}string"/>
                 *                                     &lt;/sequence>
                 *                                     &lt;attribute name="use" use="required" type="{http://www.w3.org/2001/XMLSchema}string" />
                 *                                   &lt;/restriction>
                 *                                 &lt;/complexContent>
                 *                               &lt;/complexType>
                 *                             &lt;/element>
                 *                             &lt;element name="administrativeGenderCode">
                 *                               &lt;complexType>
                 *                                 &lt;complexContent>
                 *                                   &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
                 *                                     &lt;attribute name="code" use="required" type="{http://www.w3.org/2001/XMLSchema}string" />
                 *                                     &lt;attribute name="codeSystem" use="required" type="{http://www.w3.org/2001/XMLSchema}string" />
                 *                                   &lt;/restriction>
                 *                                 &lt;/complexContent>
                 *                               &lt;/complexType>
                 *                             &lt;/element>
                 *                             &lt;element name="birthTime">
                 *                               &lt;complexType>
                 *                                 &lt;complexContent>
                 *                                   &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
                 *                                     &lt;attribute name="value" use="required" type="{http://www.w3.org/2001/XMLSchema}unsignedLong" />
                 *                                   &lt;/restriction>
                 *                                 &lt;/complexContent>
                 *                               &lt;/complexType>
                 *                             &lt;/element>
                 *                             &lt;element name="addr">
                 *                               &lt;complexType>
                 *                                 &lt;complexContent>
                 *                                   &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
                 *                                     &lt;sequence>
                 *                                       &lt;element name="city" type="{http://www.w3.org/2001/XMLSchema}unsignedInt"/>
                 *                                       &lt;element name="state" type="{http://www.w3.org/2001/XMLSchema}string"/>
                 *                                       &lt;element name="postalCode" type="{http://www.w3.org/2001/XMLSchema}unsignedInt"/>
                 *                                       &lt;element name="country" type="{http://www.w3.org/2001/XMLSchema}unsignedByte"/>
                 *                                       &lt;element name="houseNumber" type="{http://www.w3.org/2001/XMLSchema}unsignedShort"/>
                 *                                       &lt;element name="streetNameType" type="{http://www.w3.org/2001/XMLSchema}unsignedByte"/>
                 *                                       &lt;element name="additionalLocator" type="{http://www.w3.org/2001/XMLSchema}string"/>
                 *                                     &lt;/sequence>
                 *                                     &lt;attribute name="use" use="required" type="{http://www.w3.org/2001/XMLSchema}string" />
                 *                                   &lt;/restriction>
                 *                                 &lt;/complexContent>
                 *                               &lt;/complexType>
                 *                             &lt;/element>
                 *                             &lt;element name="maritalStatusCode">
                 *                               &lt;complexType>
                 *                                 &lt;complexContent>
                 *                                   &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
                 *                                     &lt;attribute name="code" use="required" type="{http://www.w3.org/2001/XMLSchema}unsignedByte" />
                 *                                   &lt;/restriction>
                 *                                 &lt;/complexContent>
                 *                               &lt;/complexType>
                 *                             &lt;/element>
                 *                             &lt;element name="raceCode">
                 *                               &lt;complexType>
                 *                                 &lt;complexContent>
                 *                                   &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
                 *                                     &lt;attribute name="code" use="required" type="{http://www.w3.org/2001/XMLSchema}unsignedByte" />
                 *                                   &lt;/restriction>
                 *                                 &lt;/complexContent>
                 *                               &lt;/complexType>
                 *                             &lt;/element>
                 *                             &lt;element name="asOtherIDs" maxOccurs="unbounded">
                 *                               &lt;complexType>
                 *                                 &lt;complexContent>
                 *                                   &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
                 *                                     &lt;sequence>
                 *                                       &lt;element name="id" maxOccurs="unbounded">
                 *                                         &lt;complexType>
                 *                                           &lt;complexContent>
                 *                                             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
                 *                                               &lt;attribute name="root" use="required" type="{http://www.w3.org/2001/XMLSchema}string" />
                 *                                               &lt;attribute name="extension" use="required" type="{http://www.w3.org/2001/XMLSchema}string" />
                 *                                             &lt;/restriction>
                 *                                           &lt;/complexContent>
                 *                                         &lt;/complexType>
                 *                                       &lt;/element>
                 *                                       &lt;element name="scopingOrganization">
                 *                                         &lt;complexType>
                 *                                           &lt;complexContent>
                 *                                             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
                 *                                               &lt;sequence>
                 *                                                 &lt;element name="id" maxOccurs="unbounded">
                 *                                                   &lt;complexType>
                 *                                                     &lt;complexContent>
                 *                                                       &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
                 *                                                         &lt;attribute name="root" use="required" type="{http://www.w3.org/2001/XMLSchema}string" />
                 *                                                       &lt;/restriction>
                 *                                                     &lt;/complexContent>
                 *                                                   &lt;/complexType>
                 *                                                 &lt;/element>
                 *                                               &lt;/sequence>
                 *                                               &lt;attribute name="classCode" use="required" type="{http://www.w3.org/2001/XMLSchema}string" />
                 *                                               &lt;attribute name="determinerCode" use="required" type="{http://www.w3.org/2001/XMLSchema}string" />
                 *                                             &lt;/restriction>
                 *                                           &lt;/complexContent>
                 *                                         &lt;/complexType>
                 *                                       &lt;/element>
                 *                                     &lt;/sequence>
                 *                                     &lt;attribute name="classCode" use="required" type="{http://www.w3.org/2001/XMLSchema}string" />
                 *                                   &lt;/restriction>
                 *                                 &lt;/complexContent>
                 *                               &lt;/complexType>
                 *                             &lt;/element>
                 *                             &lt;element name="personalRelationship" maxOccurs="unbounded">
                 *                               &lt;complexType>
                 *                                 &lt;complexContent>
                 *                                   &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
                 *                                     &lt;sequence>
                 *                                       &lt;element name="code">
                 *                                         &lt;complexType>
                 *                                           &lt;complexContent>
                 *                                             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
                 *                                               &lt;attribute name="code" use="required" type="{http://www.w3.org/2001/XMLSchema}string" />
                 *                                               &lt;attribute name="codeSystem" use="required" type="{http://www.w3.org/2001/XMLSchema}string" />
                 *                                             &lt;/restriction>
                 *                                           &lt;/complexContent>
                 *                                         &lt;/complexType>
                 *                                       &lt;/element>
                 *                                       &lt;element name="relationshipHolder1">
                 *                                         &lt;complexType>
                 *                                           &lt;complexContent>
                 *                                             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
                 *                                               &lt;sequence>
                 *                                                 &lt;element name="name">
                 *                                                   &lt;complexType>
                 *                                                     &lt;complexContent>
                 *                                                       &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
                 *                                                         &lt;sequence>
                 *                                                           &lt;element name="given" type="{http://www.w3.org/2001/XMLSchema}string"/>
                 *                                                         &lt;/sequence>
                 *                                                         &lt;attribute name="use" use="required" type="{http://www.w3.org/2001/XMLSchema}string" />
                 *                                                       &lt;/restriction>
                 *                                                     &lt;/complexContent>
                 *                                                   &lt;/complexType>
                 *                                                 &lt;/element>
                 *                                               &lt;/sequence>
                 *                                               &lt;attribute name="classCode" use="required" type="{http://www.w3.org/2001/XMLSchema}string" />
                 *                                               &lt;attribute name="determinerCode" use="required" type="{http://www.w3.org/2001/XMLSchema}string" />
                 *                                             &lt;/restriction>
                 *                                           &lt;/complexContent>
                 *                                         &lt;/complexType>
                 *                                       &lt;/element>
                 *                                     &lt;/sequence>
                 *                                     &lt;attribute name="classCode" use="required" type="{http://www.w3.org/2001/XMLSchema}string" />
                 *                                   &lt;/restriction>
                 *                                 &lt;/complexContent>
                 *                               &lt;/complexType>
                 *                             &lt;/element>
                 *                             &lt;element name="birthPlace">
                 *                               &lt;complexType>
                 *                                 &lt;complexContent>
                 *                                   &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
                 *                                     &lt;sequence>
                 *                                       &lt;element name="addr">
                 *                                         &lt;complexType>
                 *                                           &lt;complexContent>
                 *                                             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
                 *                                               &lt;sequence>
                 *                                                 &lt;element name="city" type="{http://www.w3.org/2001/XMLSchema}unsignedInt"/>
                 *                                                 &lt;element name="country" type="{http://www.w3.org/2001/XMLSchema}unsignedByte"/>
                 *                                               &lt;/sequence>
                 *                                             &lt;/restriction>
                 *                                           &lt;/complexContent>
                 *                                         &lt;/complexType>
                 *                                       &lt;/element>
                 *                                     &lt;/sequence>
                 *                                     &lt;attribute name="classCode" use="required" type="{http://www.w3.org/2001/XMLSchema}string" />
                 *                                   &lt;/restriction>
                 *                                 &lt;/complexContent>
                 *                               &lt;/complexType>
                 *                             &lt;/element>
                 *                           &lt;/sequence>
                 *                           &lt;attribute name="classCode" use="required" type="{http://www.w3.org/2001/XMLSchema}string" />
                 *                           &lt;attribute name="determinerCode" use="required" type="{http://www.w3.org/2001/XMLSchema}string" />
                 *                         &lt;/restriction>
                 *                       &lt;/complexContent>
                 *                     &lt;/complexType>
                 *                   &lt;/element>
                 *                   &lt;element name="providerOrganization">
                 *                     &lt;complexType>
                 *                       &lt;complexContent>
                 *                         &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
                 *                           &lt;sequence>
                 *                             &lt;element name="id" maxOccurs="unbounded">
                 *                               &lt;complexType>
                 *                                 &lt;complexContent>
                 *                                   &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
                 *                                     &lt;attribute name="root" use="required" type="{http://www.w3.org/2001/XMLSchema}string" />
                 *                                   &lt;/restriction>
                 *                                 &lt;/complexContent>
                 *                               &lt;/complexType>
                 *                             &lt;/element>
                 *                             &lt;element name="contactParty">
                 *                               &lt;complexType>
                 *                                 &lt;complexContent>
                 *                                   &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
                 *                                     &lt;attribute name="classCode" use="required" type="{http://www.w3.org/2001/XMLSchema}string" />
                 *                                   &lt;/restriction>
                 *                                 &lt;/complexContent>
                 *                               &lt;/complexType>
                 *                             &lt;/element>
                 *                           &lt;/sequence>
                 *                           &lt;attribute name="classCode" use="required" type="{http://www.w3.org/2001/XMLSchema}string" />
                 *                           &lt;attribute name="determinerCode" use="required" type="{http://www.w3.org/2001/XMLSchema}string" />
                 *                         &lt;/restriction>
                 *                       &lt;/complexContent>
                 *                     &lt;/complexType>
                 *                   &lt;/element>
                 *                   &lt;element name="subjectOf1">
                 *                     &lt;complexType>
                 *                       &lt;complexContent>
                 *                         &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
                 *                           &lt;sequence>
                 *                             &lt;element name="queryMatchObservation">
                 *                               &lt;complexType>
                 *                                 &lt;complexContent>
                 *                                   &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
                 *                                     &lt;sequence>
                 *                                       &lt;element name="code">
                 *                                         &lt;complexType>
                 *                                           &lt;complexContent>
                 *                                             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
                 *                                               &lt;attribute name="code" use="required" type="{http://www.w3.org/2001/XMLSchema}string" />
                 *                                             &lt;/restriction>
                 *                                           &lt;/complexContent>
                 *                                         &lt;/complexType>
                 *                                       &lt;/element>
                 *                                       &lt;element name="value">
                 *                                         &lt;complexType>
                 *                                           &lt;complexContent>
                 *                                             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
                 *                                               &lt;attribute name="value" use="required" type="{http://www.w3.org/2001/XMLSchema}unsignedByte" />
                 *                                             &lt;/restriction>
                 *                                           &lt;/complexContent>
                 *                                         &lt;/complexType>
                 *                                       &lt;/element>
                 *                                     &lt;/sequence>
                 *                                     &lt;attribute name="classCode" use="required" type="{http://www.w3.org/2001/XMLSchema}string" />
                 *                                     &lt;attribute name="moodCode" use="required" type="{http://www.w3.org/2001/XMLSchema}string" />
                 *                                   &lt;/restriction>
                 *                                 &lt;/complexContent>
                 *                               &lt;/complexType>
                 *                             &lt;/element>
                 *                           &lt;/sequence>
                 *                         &lt;/restriction>
                 *                       &lt;/complexContent>
                 *                     &lt;/complexType>
                 *                   &lt;/element>
                 *                 &lt;/sequence>
                 *                 &lt;attribute name="classCode" use="required" type="{http://www.w3.org/2001/XMLSchema}string" />
                 *               &lt;/restriction>
                 *             &lt;/complexContent>
                 *           &lt;/complexType>
                 *         &lt;/element>
                 *       &lt;/sequence>
                 *       &lt;attribute name="typeCode" use="required" type="{http://www.w3.org/2001/XMLSchema}string" />
                 *     &lt;/restriction>
                 *   &lt;/complexContent>
                 * &lt;/complexType>
                 * </pre>
                 * 
                 * 
                 */
                @XmlAccessorType(XmlAccessType.FIELD)
                @XmlType(name = "", propOrder = {
                    "realmCode",
                    "patient"
                })
                public static class Subject1 {

                    @XmlElement(required = true)
                    protected PRPAIN201306UV02 .ControlActProcess.Subject.RegistrationEvent.Subject1 .RealmCode realmCode;
                    @XmlElement(required = true)
                    protected PRPAIN201306UV02 .ControlActProcess.Subject.RegistrationEvent.Subject1 .Patient patient;
                    @XmlAttribute(name = "typeCode", required = true)
                    protected String typeCode;

                    /**
                     * Obtém o valor da propriedade realmCode.
                     * 
                     * @return
                     *     possible object is
                     *     {@link PRPAIN201306UV02 .ControlActProcess.Subject.RegistrationEvent.Subject1 .RealmCode }
                     *     
                     */
                    public PRPAIN201306UV02 .ControlActProcess.Subject.RegistrationEvent.Subject1 .RealmCode getRealmCode() {
                        return realmCode;
                    }

                    /**
                     * Define o valor da propriedade realmCode.
                     * 
                     * @param value
                     *     allowed object is
                     *     {@link PRPAIN201306UV02 .ControlActProcess.Subject.RegistrationEvent.Subject1 .RealmCode }
                     *     
                     */
                    public void setRealmCode(PRPAIN201306UV02 .ControlActProcess.Subject.RegistrationEvent.Subject1 .RealmCode value) {
                        this.realmCode = value;
                    }

                    /**
                     * Obtém o valor da propriedade patient.
                     * 
                     * @return
                     *     possible object is
                     *     {@link PRPAIN201306UV02 .ControlActProcess.Subject.RegistrationEvent.Subject1 .Patient }
                     *     
                     */
                    public PRPAIN201306UV02 .ControlActProcess.Subject.RegistrationEvent.Subject1 .Patient getPatient() {
                        return patient;
                    }

                    /**
                     * Define o valor da propriedade patient.
                     * 
                     * @param value
                     *     allowed object is
                     *     {@link PRPAIN201306UV02 .ControlActProcess.Subject.RegistrationEvent.Subject1 .Patient }
                     *     
                     */
                    public void setPatient(PRPAIN201306UV02 .ControlActProcess.Subject.RegistrationEvent.Subject1 .Patient value) {
                        this.patient = value;
                    }

                    /**
                     * Obtém o valor da propriedade typeCode.
                     * 
                     * @return
                     *     possible object is
                     *     {@link String }
                     *     
                     */
                    public String getTypeCode() {
                        return typeCode;
                    }

                    /**
                     * Define o valor da propriedade typeCode.
                     * 
                     * @param value
                     *     allowed object is
                     *     {@link String }
                     *     
                     */
                    public void setTypeCode(String value) {
                        this.typeCode = value;
                    }


                    /**
                     * <p>Classe Java de anonymous complex type.
                     * 
                     * <p>O seguinte fragmento do esquema especifica o conteúdo esperado contido dentro desta classe.
                     * 
                     * <pre>
                     * &lt;complexType>
                     *   &lt;complexContent>
                     *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
                     *       &lt;sequence>
                     *         &lt;element name="id" maxOccurs="unbounded">
                     *           &lt;complexType>
                     *             &lt;complexContent>
                     *               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
                     *                 &lt;attribute name="root" use="required" type="{http://www.w3.org/2001/XMLSchema}string" />
                     *                 &lt;attribute name="extension" use="required" type="{http://www.w3.org/2001/XMLSchema}unsignedLong" />
                     *                 &lt;attribute name="assigningAuthorityName" use="required" type="{http://www.w3.org/2001/XMLSchema}string" />
                     *               &lt;/restriction>
                     *             &lt;/complexContent>
                     *           &lt;/complexType>
                     *         &lt;/element>
                     *         &lt;element name="statusCode">
                     *           &lt;complexType>
                     *             &lt;complexContent>
                     *               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
                     *                 &lt;attribute name="code" use="required" type="{http://www.w3.org/2001/XMLSchema}string" />
                     *               &lt;/restriction>
                     *             &lt;/complexContent>
                     *           &lt;/complexType>
                     *         &lt;/element>
                     *         &lt;element name="patientPerson">
                     *           &lt;complexType>
                     *             &lt;complexContent>
                     *               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
                     *                 &lt;sequence>
                     *                   &lt;element name="name">
                     *                     &lt;complexType>
                     *                       &lt;complexContent>
                     *                         &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
                     *                           &lt;sequence>
                     *                             &lt;element name="given" type="{http://www.w3.org/2001/XMLSchema}string"/>
                     *                           &lt;/sequence>
                     *                           &lt;attribute name="use" use="required" type="{http://www.w3.org/2001/XMLSchema}string" />
                     *                         &lt;/restriction>
                     *                       &lt;/complexContent>
                     *                     &lt;/complexType>
                     *                   &lt;/element>
                     *                   &lt;element name="administrativeGenderCode">
                     *                     &lt;complexType>
                     *                       &lt;complexContent>
                     *                         &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
                     *                           &lt;attribute name="code" use="required" type="{http://www.w3.org/2001/XMLSchema}string" />
                     *                           &lt;attribute name="codeSystem" use="required" type="{http://www.w3.org/2001/XMLSchema}string" />
                     *                         &lt;/restriction>
                     *                       &lt;/complexContent>
                     *                     &lt;/complexType>
                     *                   &lt;/element>
                     *                   &lt;element name="birthTime">
                     *                     &lt;complexType>
                     *                       &lt;complexContent>
                     *                         &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
                     *                           &lt;attribute name="value" use="required" type="{http://www.w3.org/2001/XMLSchema}unsignedLong" />
                     *                         &lt;/restriction>
                     *                       &lt;/complexContent>
                     *                     &lt;/complexType>
                     *                   &lt;/element>
                     *                   &lt;element name="addr">
                     *                     &lt;complexType>
                     *                       &lt;complexContent>
                     *                         &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
                     *                           &lt;sequence>
                     *                             &lt;element name="city" type="{http://www.w3.org/2001/XMLSchema}unsignedInt"/>
                     *                             &lt;element name="state" type="{http://www.w3.org/2001/XMLSchema}string"/>
                     *                             &lt;element name="postalCode" type="{http://www.w3.org/2001/XMLSchema}unsignedInt"/>
                     *                             &lt;element name="country" type="{http://www.w3.org/2001/XMLSchema}unsignedByte"/>
                     *                             &lt;element name="houseNumber" type="{http://www.w3.org/2001/XMLSchema}unsignedShort"/>
                     *                             &lt;element name="streetNameType" type="{http://www.w3.org/2001/XMLSchema}unsignedByte"/>
                     *                             &lt;element name="additionalLocator" type="{http://www.w3.org/2001/XMLSchema}string"/>
                     *                           &lt;/sequence>
                     *                           &lt;attribute name="use" use="required" type="{http://www.w3.org/2001/XMLSchema}string" />
                     *                         &lt;/restriction>
                     *                       &lt;/complexContent>
                     *                     &lt;/complexType>
                     *                   &lt;/element>
                     *                   &lt;element name="maritalStatusCode">
                     *                     &lt;complexType>
                     *                       &lt;complexContent>
                     *                         &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
                     *                           &lt;attribute name="code" use="required" type="{http://www.w3.org/2001/XMLSchema}unsignedByte" />
                     *                         &lt;/restriction>
                     *                       &lt;/complexContent>
                     *                     &lt;/complexType>
                     *                   &lt;/element>
                     *                   &lt;element name="raceCode">
                     *                     &lt;complexType>
                     *                       &lt;complexContent>
                     *                         &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
                     *                           &lt;attribute name="code" use="required" type="{http://www.w3.org/2001/XMLSchema}unsignedByte" />
                     *                         &lt;/restriction>
                     *                       &lt;/complexContent>
                     *                     &lt;/complexType>
                     *                   &lt;/element>
                     *                   &lt;element name="asOtherIDs" maxOccurs="unbounded">
                     *                     &lt;complexType>
                     *                       &lt;complexContent>
                     *                         &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
                     *                           &lt;sequence>
                     *                             &lt;element name="id" maxOccurs="unbounded">
                     *                               &lt;complexType>
                     *                                 &lt;complexContent>
                     *                                   &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
                     *                                     &lt;attribute name="root" use="required" type="{http://www.w3.org/2001/XMLSchema}string" />
                     *                                     &lt;attribute name="extension" use="required" type="{http://www.w3.org/2001/XMLSchema}string" />
                     *                                   &lt;/restriction>
                     *                                 &lt;/complexContent>
                     *                               &lt;/complexType>
                     *                             &lt;/element>
                     *                             &lt;element name="scopingOrganization">
                     *                               &lt;complexType>
                     *                                 &lt;complexContent>
                     *                                   &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
                     *                                     &lt;sequence>
                     *                                       &lt;element name="id" maxOccurs="unbounded">
                     *                                         &lt;complexType>
                     *                                           &lt;complexContent>
                     *                                             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
                     *                                               &lt;attribute name="root" use="required" type="{http://www.w3.org/2001/XMLSchema}string" />
                     *                                             &lt;/restriction>
                     *                                           &lt;/complexContent>
                     *                                         &lt;/complexType>
                     *                                       &lt;/element>
                     *                                     &lt;/sequence>
                     *                                     &lt;attribute name="classCode" use="required" type="{http://www.w3.org/2001/XMLSchema}string" />
                     *                                     &lt;attribute name="determinerCode" use="required" type="{http://www.w3.org/2001/XMLSchema}string" />
                     *                                   &lt;/restriction>
                     *                                 &lt;/complexContent>
                     *                               &lt;/complexType>
                     *                             &lt;/element>
                     *                           &lt;/sequence>
                     *                           &lt;attribute name="classCode" use="required" type="{http://www.w3.org/2001/XMLSchema}string" />
                     *                         &lt;/restriction>
                     *                       &lt;/complexContent>
                     *                     &lt;/complexType>
                     *                   &lt;/element>
                     *                   &lt;element name="personalRelationship" maxOccurs="unbounded">
                     *                     &lt;complexType>
                     *                       &lt;complexContent>
                     *                         &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
                     *                           &lt;sequence>
                     *                             &lt;element name="code">
                     *                               &lt;complexType>
                     *                                 &lt;complexContent>
                     *                                   &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
                     *                                     &lt;attribute name="code" use="required" type="{http://www.w3.org/2001/XMLSchema}string" />
                     *                                     &lt;attribute name="codeSystem" use="required" type="{http://www.w3.org/2001/XMLSchema}string" />
                     *                                   &lt;/restriction>
                     *                                 &lt;/complexContent>
                     *                               &lt;/complexType>
                     *                             &lt;/element>
                     *                             &lt;element name="relationshipHolder1">
                     *                               &lt;complexType>
                     *                                 &lt;complexContent>
                     *                                   &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
                     *                                     &lt;sequence>
                     *                                       &lt;element name="name">
                     *                                         &lt;complexType>
                     *                                           &lt;complexContent>
                     *                                             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
                     *                                               &lt;sequence>
                     *                                                 &lt;element name="given" type="{http://www.w3.org/2001/XMLSchema}string"/>
                     *                                               &lt;/sequence>
                     *                                               &lt;attribute name="use" use="required" type="{http://www.w3.org/2001/XMLSchema}string" />
                     *                                             &lt;/restriction>
                     *                                           &lt;/complexContent>
                     *                                         &lt;/complexType>
                     *                                       &lt;/element>
                     *                                     &lt;/sequence>
                     *                                     &lt;attribute name="classCode" use="required" type="{http://www.w3.org/2001/XMLSchema}string" />
                     *                                     &lt;attribute name="determinerCode" use="required" type="{http://www.w3.org/2001/XMLSchema}string" />
                     *                                   &lt;/restriction>
                     *                                 &lt;/complexContent>
                     *                               &lt;/complexType>
                     *                             &lt;/element>
                     *                           &lt;/sequence>
                     *                           &lt;attribute name="classCode" use="required" type="{http://www.w3.org/2001/XMLSchema}string" />
                     *                         &lt;/restriction>
                     *                       &lt;/complexContent>
                     *                     &lt;/complexType>
                     *                   &lt;/element>
                     *                   &lt;element name="birthPlace">
                     *                     &lt;complexType>
                     *                       &lt;complexContent>
                     *                         &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
                     *                           &lt;sequence>
                     *                             &lt;element name="addr">
                     *                               &lt;complexType>
                     *                                 &lt;complexContent>
                     *                                   &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
                     *                                     &lt;sequence>
                     *                                       &lt;element name="city" type="{http://www.w3.org/2001/XMLSchema}unsignedInt"/>
                     *                                       &lt;element name="country" type="{http://www.w3.org/2001/XMLSchema}unsignedByte"/>
                     *                                     &lt;/sequence>
                     *                                   &lt;/restriction>
                     *                                 &lt;/complexContent>
                     *                               &lt;/complexType>
                     *                             &lt;/element>
                     *                           &lt;/sequence>
                     *                           &lt;attribute name="classCode" use="required" type="{http://www.w3.org/2001/XMLSchema}string" />
                     *                         &lt;/restriction>
                     *                       &lt;/complexContent>
                     *                     &lt;/complexType>
                     *                   &lt;/element>
                     *                 &lt;/sequence>
                     *                 &lt;attribute name="classCode" use="required" type="{http://www.w3.org/2001/XMLSchema}string" />
                     *                 &lt;attribute name="determinerCode" use="required" type="{http://www.w3.org/2001/XMLSchema}string" />
                     *               &lt;/restriction>
                     *             &lt;/complexContent>
                     *           &lt;/complexType>
                     *         &lt;/element>
                     *         &lt;element name="providerOrganization">
                     *           &lt;complexType>
                     *             &lt;complexContent>
                     *               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
                     *                 &lt;sequence>
                     *                   &lt;element name="id" maxOccurs="unbounded">
                     *                     &lt;complexType>
                     *                       &lt;complexContent>
                     *                         &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
                     *                           &lt;attribute name="root" use="required" type="{http://www.w3.org/2001/XMLSchema}string" />
                     *                         &lt;/restriction>
                     *                       &lt;/complexContent>
                     *                     &lt;/complexType>
                     *                   &lt;/element>
                     *                   &lt;element name="contactParty">
                     *                     &lt;complexType>
                     *                       &lt;complexContent>
                     *                         &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
                     *                           &lt;attribute name="classCode" use="required" type="{http://www.w3.org/2001/XMLSchema}string" />
                     *                         &lt;/restriction>
                     *                       &lt;/complexContent>
                     *                     &lt;/complexType>
                     *                   &lt;/element>
                     *                 &lt;/sequence>
                     *                 &lt;attribute name="classCode" use="required" type="{http://www.w3.org/2001/XMLSchema}string" />
                     *                 &lt;attribute name="determinerCode" use="required" type="{http://www.w3.org/2001/XMLSchema}string" />
                     *               &lt;/restriction>
                     *             &lt;/complexContent>
                     *           &lt;/complexType>
                     *         &lt;/element>
                     *         &lt;element name="subjectOf1">
                     *           &lt;complexType>
                     *             &lt;complexContent>
                     *               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
                     *                 &lt;sequence>
                     *                   &lt;element name="queryMatchObservation">
                     *                     &lt;complexType>
                     *                       &lt;complexContent>
                     *                         &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
                     *                           &lt;sequence>
                     *                             &lt;element name="code">
                     *                               &lt;complexType>
                     *                                 &lt;complexContent>
                     *                                   &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
                     *                                     &lt;attribute name="code" use="required" type="{http://www.w3.org/2001/XMLSchema}string" />
                     *                                   &lt;/restriction>
                     *                                 &lt;/complexContent>
                     *                               &lt;/complexType>
                     *                             &lt;/element>
                     *                             &lt;element name="value">
                     *                               &lt;complexType>
                     *                                 &lt;complexContent>
                     *                                   &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
                     *                                     &lt;attribute name="value" use="required" type="{http://www.w3.org/2001/XMLSchema}unsignedByte" />
                     *                                   &lt;/restriction>
                     *                                 &lt;/complexContent>
                     *                               &lt;/complexType>
                     *                             &lt;/element>
                     *                           &lt;/sequence>
                     *                           &lt;attribute name="classCode" use="required" type="{http://www.w3.org/2001/XMLSchema}string" />
                     *                           &lt;attribute name="moodCode" use="required" type="{http://www.w3.org/2001/XMLSchema}string" />
                     *                         &lt;/restriction>
                     *                       &lt;/complexContent>
                     *                     &lt;/complexType>
                     *                   &lt;/element>
                     *                 &lt;/sequence>
                     *               &lt;/restriction>
                     *             &lt;/complexContent>
                     *           &lt;/complexType>
                     *         &lt;/element>
                     *       &lt;/sequence>
                     *       &lt;attribute name="classCode" use="required" type="{http://www.w3.org/2001/XMLSchema}string" />
                     *     &lt;/restriction>
                     *   &lt;/complexContent>
                     * &lt;/complexType>
                     * </pre>
                     * 
                     * 
                     */
                    @XmlAccessorType(XmlAccessType.FIELD)
                    @XmlType(name = "", propOrder = {
                        "id",
                        "statusCode",
                        "patientPerson",
                        "providerOrganization",
                        "subjectOf1"
                    })
                    public static class Patient {

                        @XmlElement(required = true)
                        protected List<PRPAIN201306UV02 .ControlActProcess.Subject.RegistrationEvent.Subject1 .Patient.Id> id;
                        @XmlElement(required = true)
                        protected PRPAIN201306UV02 .ControlActProcess.Subject.RegistrationEvent.Subject1 .Patient.StatusCode statusCode;
                        @XmlElement(required = true)
                        protected PRPAIN201306UV02 .ControlActProcess.Subject.RegistrationEvent.Subject1 .Patient.PatientPerson patientPerson;
                        @XmlElement(required = true)
                        protected PRPAIN201306UV02 .ControlActProcess.Subject.RegistrationEvent.Subject1 .Patient.ProviderOrganization providerOrganization;
                        @XmlElement(required = true)
                        protected PRPAIN201306UV02 .ControlActProcess.Subject.RegistrationEvent.Subject1 .Patient.SubjectOf1 subjectOf1;
                        @XmlAttribute(name = "classCode", required = true)
                        protected String classCode;

                        /**
                         * Gets the value of the id property.
                         * 
                         * <p>
                         * This accessor method returns a reference to the live list,
                         * not a snapshot. Therefore any modification you make to the
                         * returned list will be present inside the JAXB object.
                         * This is why there is not a <CODE>set</CODE> method for the id property.
                         * 
                         * <p>
                         * For example, to add a new item, do as follows:
                         * <pre>
                         *    getId().add(newItem);
                         * </pre>
                         * 
                         * 
                         * <p>
                         * Objects of the following type(s) are allowed in the list
                         * {@link PRPAIN201306UV02 .ControlActProcess.Subject.RegistrationEvent.Subject1 .Patient.Id }
                         * 
                         * 
                         */
                        public List<PRPAIN201306UV02 .ControlActProcess.Subject.RegistrationEvent.Subject1 .Patient.Id> getId() {
                            if (id == null) {
                                id = new ArrayList<PRPAIN201306UV02 .ControlActProcess.Subject.RegistrationEvent.Subject1 .Patient.Id>();
                            }
                            return this.id;
                        }

                        /**
                         * Obtém o valor da propriedade statusCode.
                         * 
                         * @return
                         *     possible object is
                         *     {@link PRPAIN201306UV02 .ControlActProcess.Subject.RegistrationEvent.Subject1 .Patient.StatusCode }
                         *     
                         */
                        public PRPAIN201306UV02 .ControlActProcess.Subject.RegistrationEvent.Subject1 .Patient.StatusCode getStatusCode() {
                            return statusCode;
                        }

                        /**
                         * Define o valor da propriedade statusCode.
                         * 
                         * @param value
                         *     allowed object is
                         *     {@link PRPAIN201306UV02 .ControlActProcess.Subject.RegistrationEvent.Subject1 .Patient.StatusCode }
                         *     
                         */
                        public void setStatusCode(PRPAIN201306UV02 .ControlActProcess.Subject.RegistrationEvent.Subject1 .Patient.StatusCode value) {
                            this.statusCode = value;
                        }

                        /**
                         * Obtém o valor da propriedade patientPerson.
                         * 
                         * @return
                         *     possible object is
                         *     {@link PRPAIN201306UV02 .ControlActProcess.Subject.RegistrationEvent.Subject1 .Patient.PatientPerson }
                         *     
                         */
                        public PRPAIN201306UV02 .ControlActProcess.Subject.RegistrationEvent.Subject1 .Patient.PatientPerson getPatientPerson() {
                            return patientPerson;
                        }

                        /**
                         * Define o valor da propriedade patientPerson.
                         * 
                         * @param value
                         *     allowed object is
                         *     {@link PRPAIN201306UV02 .ControlActProcess.Subject.RegistrationEvent.Subject1 .Patient.PatientPerson }
                         *     
                         */
                        public void setPatientPerson(PRPAIN201306UV02 .ControlActProcess.Subject.RegistrationEvent.Subject1 .Patient.PatientPerson value) {
                            this.patientPerson = value;
                        }

                        /**
                         * Obtém o valor da propriedade providerOrganization.
                         * 
                         * @return
                         *     possible object is
                         *     {@link PRPAIN201306UV02 .ControlActProcess.Subject.RegistrationEvent.Subject1 .Patient.ProviderOrganization }
                         *     
                         */
                        public PRPAIN201306UV02 .ControlActProcess.Subject.RegistrationEvent.Subject1 .Patient.ProviderOrganization getProviderOrganization() {
                            return providerOrganization;
                        }

                        /**
                         * Define o valor da propriedade providerOrganization.
                         * 
                         * @param value
                         *     allowed object is
                         *     {@link PRPAIN201306UV02 .ControlActProcess.Subject.RegistrationEvent.Subject1 .Patient.ProviderOrganization }
                         *     
                         */
                        public void setProviderOrganization(PRPAIN201306UV02 .ControlActProcess.Subject.RegistrationEvent.Subject1 .Patient.ProviderOrganization value) {
                            this.providerOrganization = value;
                        }

                        /**
                         * Obtém o valor da propriedade subjectOf1.
                         * 
                         * @return
                         *     possible object is
                         *     {@link PRPAIN201306UV02 .ControlActProcess.Subject.RegistrationEvent.Subject1 .Patient.SubjectOf1 }
                         *     
                         */
                        public PRPAIN201306UV02 .ControlActProcess.Subject.RegistrationEvent.Subject1 .Patient.SubjectOf1 getSubjectOf1() {
                            return subjectOf1;
                        }

                        /**
                         * Define o valor da propriedade subjectOf1.
                         * 
                         * @param value
                         *     allowed object is
                         *     {@link PRPAIN201306UV02 .ControlActProcess.Subject.RegistrationEvent.Subject1 .Patient.SubjectOf1 }
                         *     
                         */
                        public void setSubjectOf1(PRPAIN201306UV02 .ControlActProcess.Subject.RegistrationEvent.Subject1 .Patient.SubjectOf1 value) {
                            this.subjectOf1 = value;
                        }

                        /**
                         * Obtém o valor da propriedade classCode.
                         * 
                         * @return
                         *     possible object is
                         *     {@link String }
                         *     
                         */
                        public String getClassCode() {
                            return classCode;
                        }

                        /**
                         * Define o valor da propriedade classCode.
                         * 
                         * @param value
                         *     allowed object is
                         *     {@link String }
                         *     
                         */
                        public void setClassCode(String value) {
                            this.classCode = value;
                        }


                        /**
                         * <p>Classe Java de anonymous complex type.
                         * 
                         * <p>O seguinte fragmento do esquema especifica o conteúdo esperado contido dentro desta classe.
                         * 
                         * <pre>
                         * &lt;complexType>
                         *   &lt;complexContent>
                         *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
                         *       &lt;attribute name="root" use="required" type="{http://www.w3.org/2001/XMLSchema}string" />
                         *       &lt;attribute name="extension" use="required" type="{http://www.w3.org/2001/XMLSchema}unsignedLong" />
                         *       &lt;attribute name="assigningAuthorityName" use="required" type="{http://www.w3.org/2001/XMLSchema}string" />
                         *     &lt;/restriction>
                         *   &lt;/complexContent>
                         * &lt;/complexType>
                         * </pre>
                         * 
                         * 
                         */
                        @XmlAccessorType(XmlAccessType.FIELD)
                        @XmlType(name = "")
                        public static class Id {

                            @XmlAttribute(name = "root", required = true)
                            protected String root;
                            @XmlAttribute(name = "extension", required = true)
                            @XmlSchemaType(name = "unsignedLong")
                            protected String extension;
                            @XmlAttribute(name = "assigningAuthorityName", required = true)
                            protected String assigningAuthorityName;

                            /**
                             * Obtém o valor da propriedade root.
                             * 
                             * @return
                             *     possible object is
                             *     {@link String }
                             *     
                             */
                            public String getRoot() {
                                return root;
                            }

                            /**
                             * Define o valor da propriedade root.
                             * 
                             * @param value
                             *     allowed object is
                             *     {@link String }
                             *     
                             */
                            public void setRoot(String value) {
                                this.root = value;
                            }

                            /**
                             * Obtém o valor da propriedade extension.
                             * 
                             * @return
                             *     possible object is
                             *     {@link BigInteger }
                             *     
                             */
                            public String getExtension() {
                                return extension;
                            }

                            /**
                             * Define o valor da propriedade extension.
                             * 
                             * @param value
                             *     allowed object is
                             *     {@link String }
                             *     
                             */
                            public void setExtension(String value) {
                                this.extension = value;
                            }

                            /**
                             * Obtém o valor da propriedade assigningAuthorityName.
                             * 
                             * @return
                             *     possible object is
                             *     {@link String }
                             *     
                             */
                            public String getAssigningAuthorityName() {
                                return assigningAuthorityName;
                            }

                            /**
                             * Define o valor da propriedade assigningAuthorityName.
                             * 
                             * @param value
                             *     allowed object is
                             *     {@link String }
                             *     
                             */
                            public void setAssigningAuthorityName(String value) {
                                this.assigningAuthorityName = value;
                            }

                        }


                        /**
                         * <p>Classe Java de anonymous complex type.
                         * 
                         * <p>O seguinte fragmento do esquema especifica o conteúdo esperado contido dentro desta classe.
                         * 
                         * <pre>
                         * &lt;complexType>
                         *   &lt;complexContent>
                         *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
                         *       &lt;sequence>
                         *         &lt;element name="name">
                         *           &lt;complexType>
                         *             &lt;complexContent>
                         *               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
                         *                 &lt;sequence>
                         *                   &lt;element name="given" type="{http://www.w3.org/2001/XMLSchema}string"/>
                         *                 &lt;/sequence>
                         *                 &lt;attribute name="use" use="required" type="{http://www.w3.org/2001/XMLSchema}string" />
                         *               &lt;/restriction>
                         *             &lt;/complexContent>
                         *           &lt;/complexType>
                         *         &lt;/element>
                         *         &lt;element name="administrativeGenderCode">
                         *           &lt;complexType>
                         *             &lt;complexContent>
                         *               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
                         *                 &lt;attribute name="code" use="required" type="{http://www.w3.org/2001/XMLSchema}string" />
                         *                 &lt;attribute name="codeSystem" use="required" type="{http://www.w3.org/2001/XMLSchema}string" />
                         *               &lt;/restriction>
                         *             &lt;/complexContent>
                         *           &lt;/complexType>
                         *         &lt;/element>
                         *         &lt;element name="birthTime">
                         *           &lt;complexType>
                         *             &lt;complexContent>
                         *               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
                         *                 &lt;attribute name="value" use="required" type="{http://www.w3.org/2001/XMLSchema}unsignedLong" />
                         *               &lt;/restriction>
                         *             &lt;/complexContent>
                         *           &lt;/complexType>
                         *         &lt;/element>
                         *         &lt;element name="addr">
                         *           &lt;complexType>
                         *             &lt;complexContent>
                         *               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
                         *                 &lt;sequence>
                         *                   &lt;element name="city" type="{http://www.w3.org/2001/XMLSchema}unsignedInt"/>
                         *                   &lt;element name="state" type="{http://www.w3.org/2001/XMLSchema}string"/>
                         *                   &lt;element name="postalCode" type="{http://www.w3.org/2001/XMLSchema}unsignedInt"/>
                         *                   &lt;element name="country" type="{http://www.w3.org/2001/XMLSchema}unsignedByte"/>
                         *                   &lt;element name="houseNumber" type="{http://www.w3.org/2001/XMLSchema}unsignedShort"/>
                         *                   &lt;element name="streetNameType" type="{http://www.w3.org/2001/XMLSchema}unsignedByte"/>
                         *                   &lt;element name="additionalLocator" type="{http://www.w3.org/2001/XMLSchema}string"/>
                         *                 &lt;/sequence>
                         *                 &lt;attribute name="use" use="required" type="{http://www.w3.org/2001/XMLSchema}string" />
                         *               &lt;/restriction>
                         *             &lt;/complexContent>
                         *           &lt;/complexType>
                         *         &lt;/element>
                         *         &lt;element name="maritalStatusCode">
                         *           &lt;complexType>
                         *             &lt;complexContent>
                         *               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
                         *                 &lt;attribute name="code" use="required" type="{http://www.w3.org/2001/XMLSchema}unsignedByte" />
                         *               &lt;/restriction>
                         *             &lt;/complexContent>
                         *           &lt;/complexType>
                         *         &lt;/element>
                         *         &lt;element name="raceCode">
                         *           &lt;complexType>
                         *             &lt;complexContent>
                         *               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
                         *                 &lt;attribute name="code" use="required" type="{http://www.w3.org/2001/XMLSchema}unsignedByte" />
                         *               &lt;/restriction>
                         *             &lt;/complexContent>
                         *           &lt;/complexType>
                         *         &lt;/element>
                         *         &lt;element name="asOtherIDs" maxOccurs="unbounded">
                         *           &lt;complexType>
                         *             &lt;complexContent>
                         *               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
                         *                 &lt;sequence>
                         *                   &lt;element name="id" maxOccurs="unbounded">
                         *                     &lt;complexType>
                         *                       &lt;complexContent>
                         *                         &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
                         *                           &lt;attribute name="root" use="required" type="{http://www.w3.org/2001/XMLSchema}string" />
                         *                           &lt;attribute name="extension" use="required" type="{http://www.w3.org/2001/XMLSchema}string" />
                         *                         &lt;/restriction>
                         *                       &lt;/complexContent>
                         *                     &lt;/complexType>
                         *                   &lt;/element>
                         *                   &lt;element name="scopingOrganization">
                         *                     &lt;complexType>
                         *                       &lt;complexContent>
                         *                         &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
                         *                           &lt;sequence>
                         *                             &lt;element name="id" maxOccurs="unbounded">
                         *                               &lt;complexType>
                         *                                 &lt;complexContent>
                         *                                   &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
                         *                                     &lt;attribute name="root" use="required" type="{http://www.w3.org/2001/XMLSchema}string" />
                         *                                   &lt;/restriction>
                         *                                 &lt;/complexContent>
                         *                               &lt;/complexType>
                         *                             &lt;/element>
                         *                           &lt;/sequence>
                         *                           &lt;attribute name="classCode" use="required" type="{http://www.w3.org/2001/XMLSchema}string" />
                         *                           &lt;attribute name="determinerCode" use="required" type="{http://www.w3.org/2001/XMLSchema}string" />
                         *                         &lt;/restriction>
                         *                       &lt;/complexContent>
                         *                     &lt;/complexType>
                         *                   &lt;/element>
                         *                 &lt;/sequence>
                         *                 &lt;attribute name="classCode" use="required" type="{http://www.w3.org/2001/XMLSchema}string" />
                         *               &lt;/restriction>
                         *             &lt;/complexContent>
                         *           &lt;/complexType>
                         *         &lt;/element>
                         *         &lt;element name="personalRelationship" maxOccurs="unbounded">
                         *           &lt;complexType>
                         *             &lt;complexContent>
                         *               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
                         *                 &lt;sequence>
                         *                   &lt;element name="code">
                         *                     &lt;complexType>
                         *                       &lt;complexContent>
                         *                         &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
                         *                           &lt;attribute name="code" use="required" type="{http://www.w3.org/2001/XMLSchema}string" />
                         *                           &lt;attribute name="codeSystem" use="required" type="{http://www.w3.org/2001/XMLSchema}string" />
                         *                         &lt;/restriction>
                         *                       &lt;/complexContent>
                         *                     &lt;/complexType>
                         *                   &lt;/element>
                         *                   &lt;element name="relationshipHolder1">
                         *                     &lt;complexType>
                         *                       &lt;complexContent>
                         *                         &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
                         *                           &lt;sequence>
                         *                             &lt;element name="name">
                         *                               &lt;complexType>
                         *                                 &lt;complexContent>
                         *                                   &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
                         *                                     &lt;sequence>
                         *                                       &lt;element name="given" type="{http://www.w3.org/2001/XMLSchema}string"/>
                         *                                     &lt;/sequence>
                         *                                     &lt;attribute name="use" use="required" type="{http://www.w3.org/2001/XMLSchema}string" />
                         *                                   &lt;/restriction>
                         *                                 &lt;/complexContent>
                         *                               &lt;/complexType>
                         *                             &lt;/element>
                         *                           &lt;/sequence>
                         *                           &lt;attribute name="classCode" use="required" type="{http://www.w3.org/2001/XMLSchema}string" />
                         *                           &lt;attribute name="determinerCode" use="required" type="{http://www.w3.org/2001/XMLSchema}string" />
                         *                         &lt;/restriction>
                         *                       &lt;/complexContent>
                         *                     &lt;/complexType>
                         *                   &lt;/element>
                         *                 &lt;/sequence>
                         *                 &lt;attribute name="classCode" use="required" type="{http://www.w3.org/2001/XMLSchema}string" />
                         *               &lt;/restriction>
                         *             &lt;/complexContent>
                         *           &lt;/complexType>
                         *         &lt;/element>
                         *         &lt;element name="birthPlace">
                         *           &lt;complexType>
                         *             &lt;complexContent>
                         *               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
                         *                 &lt;sequence>
                         *                   &lt;element name="addr">
                         *                     &lt;complexType>
                         *                       &lt;complexContent>
                         *                         &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
                         *                           &lt;sequence>
                         *                             &lt;element name="city" type="{http://www.w3.org/2001/XMLSchema}unsignedInt"/>
                         *                             &lt;element name="country" type="{http://www.w3.org/2001/XMLSchema}unsignedByte"/>
                         *                           &lt;/sequence>
                         *                         &lt;/restriction>
                         *                       &lt;/complexContent>
                         *                     &lt;/complexType>
                         *                   &lt;/element>
                         *                 &lt;/sequence>
                         *                 &lt;attribute name="classCode" use="required" type="{http://www.w3.org/2001/XMLSchema}string" />
                         *               &lt;/restriction>
                         *             &lt;/complexContent>
                         *           &lt;/complexType>
                         *         &lt;/element>
                         *       &lt;/sequence>
                         *       &lt;attribute name="classCode" use="required" type="{http://www.w3.org/2001/XMLSchema}string" />
                         *       &lt;attribute name="determinerCode" use="required" type="{http://www.w3.org/2001/XMLSchema}string" />
                         *     &lt;/restriction>
                         *   &lt;/complexContent>
                         * &lt;/complexType>
                         * </pre>
                         * 
                         * 
                         */
                        @XmlAccessorType(XmlAccessType.FIELD)
                        @XmlType(name = "", propOrder = {
                            "name",
                            "administrativeGenderCode",
                            "birthTime",
                            "addr",
                            "maritalStatusCode",
                            "raceCode",
                            "asOtherIDs",
                            "personalRelationship",
                            "birthPlace"
                        })
                        public static class PatientPerson {

                            @XmlElement(required = true)
                            protected List<PRPAIN201306UV02 .ControlActProcess.Subject.RegistrationEvent.Subject1 .Patient.PatientPerson.Name> name;
                            @XmlElement(required = true)
                            protected PRPAIN201306UV02 .ControlActProcess.Subject.RegistrationEvent.Subject1 .Patient.PatientPerson.AdministrativeGenderCode administrativeGenderCode;
                            @XmlElement(required = true)
                            protected PRPAIN201306UV02 .ControlActProcess.Subject.RegistrationEvent.Subject1 .Patient.PatientPerson.BirthTime birthTime;
                            @XmlElement(required = true)
                            protected PRPAIN201306UV02 .ControlActProcess.Subject.RegistrationEvent.Subject1 .Patient.PatientPerson.Addr addr;
                            @XmlElement(required = true)
                            protected PRPAIN201306UV02 .ControlActProcess.Subject.RegistrationEvent.Subject1 .Patient.PatientPerson.MaritalStatusCode maritalStatusCode;
                            @XmlElement(required = true)
                            protected PRPAIN201306UV02 .ControlActProcess.Subject.RegistrationEvent.Subject1 .Patient.PatientPerson.RaceCode raceCode;
                            @XmlElement(required = true)
                            protected List<PRPAIN201306UV02 .ControlActProcess.Subject.RegistrationEvent.Subject1 .Patient.PatientPerson.AsOtherIDs> asOtherIDs;
                            @XmlElement(required = true)
                            protected List<PRPAIN201306UV02 .ControlActProcess.Subject.RegistrationEvent.Subject1 .Patient.PatientPerson.PersonalRelationship> personalRelationship;
                            @XmlElement(required = true)
                            protected PRPAIN201306UV02 .ControlActProcess.Subject.RegistrationEvent.Subject1 .Patient.PatientPerson.BirthPlace birthPlace;
                            @XmlAttribute(name = "classCode", required = true)
                            protected String classCode;
                            @XmlAttribute(name = "determinerCode", required = true)
                            protected String determinerCode;

                            /**
                             * Obtém o valor da propriedade name.
                             * 
                             * @return
                             *     possible object is
                             *     {@link PRPAIN201306UV02 .ControlActProcess.Subject.RegistrationEvent.Subject1 .Patient.PatientPerson.Name }
                             *     
                             */

                            public List<PRPAIN201306UV02 .ControlActProcess.Subject.RegistrationEvent.Subject1 .Patient.PatientPerson.Name> getName() {
                                if (name == null) {
                                    name = new ArrayList<PRPAIN201306UV02 .ControlActProcess.Subject.RegistrationEvent.Subject1 .Patient.PatientPerson.Name>();
                                }
                                return this.name;
                            }

                            public void setName(List<PRPAIN201306UV02 .ControlActProcess.Subject.RegistrationEvent.Subject1 .Patient.PatientPerson.Name> name) {
                                this.name = name;
                            }

                            /**
                             * Obtém o valor da propriedade administrativeGenderCode.
                             * 
                             * @return
                             *     possible object is
                             *     {@link PRPAIN201306UV02 .ControlActProcess.Subject.RegistrationEvent.Subject1 .Patient.PatientPerson.AdministrativeGenderCode }
                             *     
                             */
                            public PRPAIN201306UV02 .ControlActProcess.Subject.RegistrationEvent.Subject1 .Patient.PatientPerson.AdministrativeGenderCode getAdministrativeGenderCode() {
                                return administrativeGenderCode;
                            }

                            /**
                             * Define o valor da propriedade administrativeGenderCode.
                             * 
                             * @param value
                             *     allowed object is
                             *     {@link PRPAIN201306UV02 .ControlActProcess.Subject.RegistrationEvent.Subject1 .Patient.PatientPerson.AdministrativeGenderCode }
                             *     
                             */
                            public void setAdministrativeGenderCode(PRPAIN201306UV02 .ControlActProcess.Subject.RegistrationEvent.Subject1 .Patient.PatientPerson.AdministrativeGenderCode value) {
                                this.administrativeGenderCode = value;
                            }

                            /**
                             * Obtém o valor da propriedade birthTime.
                             * 
                             * @return
                             *     possible object is
                             *     {@link PRPAIN201306UV02 .ControlActProcess.Subject.RegistrationEvent.Subject1 .Patient.PatientPerson.BirthTime }
                             *     
                             */
                            public PRPAIN201306UV02 .ControlActProcess.Subject.RegistrationEvent.Subject1 .Patient.PatientPerson.BirthTime getBirthTime() {
                                return birthTime;
                            }

                            /**
                             * Define o valor da propriedade birthTime.
                             * 
                             * @param value
                             *     allowed object is
                             *     {@link PRPAIN201306UV02 .ControlActProcess.Subject.RegistrationEvent.Subject1 .Patient.PatientPerson.BirthTime }
                             *     
                             */
                            public void setBirthTime(PRPAIN201306UV02 .ControlActProcess.Subject.RegistrationEvent.Subject1 .Patient.PatientPerson.BirthTime value) {
                                this.birthTime = value;
                            }

                            /**
                             * Obtém o valor da propriedade addr.
                             * 
                             * @return
                             *     possible object is
                             *     {@link PRPAIN201306UV02 .ControlActProcess.Subject.RegistrationEvent.Subject1 .Patient.PatientPerson.Addr }
                             *     
                             */
                            public PRPAIN201306UV02 .ControlActProcess.Subject.RegistrationEvent.Subject1 .Patient.PatientPerson.Addr getAddr() {
                                return addr;
                            }

                            /**
                             * Define o valor da propriedade addr.
                             * 
                             * @param value
                             *     allowed object is
                             *     {@link PRPAIN201306UV02 .ControlActProcess.Subject.RegistrationEvent.Subject1 .Patient.PatientPerson.Addr }
                             *     
                             */
                            public void setAddr(PRPAIN201306UV02 .ControlActProcess.Subject.RegistrationEvent.Subject1 .Patient.PatientPerson.Addr value) {
                                this.addr = value;
                            }

                            /**
                             * Obtém o valor da propriedade maritalStatusCode.
                             * 
                             * @return
                             *     possible object is
                             *     {@link PRPAIN201306UV02 .ControlActProcess.Subject.RegistrationEvent.Subject1 .Patient.PatientPerson.MaritalStatusCode }
                             *     
                             */
                            public PRPAIN201306UV02 .ControlActProcess.Subject.RegistrationEvent.Subject1 .Patient.PatientPerson.MaritalStatusCode getMaritalStatusCode() {
                                return maritalStatusCode;
                            }

                            /**
                             * Define o valor da propriedade maritalStatusCode.
                             * 
                             * @param value
                             *     allowed object is
                             *     {@link PRPAIN201306UV02 .ControlActProcess.Subject.RegistrationEvent.Subject1 .Patient.PatientPerson.MaritalStatusCode }
                             *     
                             */
                            public void setMaritalStatusCode(PRPAIN201306UV02 .ControlActProcess.Subject.RegistrationEvent.Subject1 .Patient.PatientPerson.MaritalStatusCode value) {
                                this.maritalStatusCode = value;
                            }

                            /**
                             * Obtém o valor da propriedade raceCode.
                             * 
                             * @return
                             *     possible object is
                             *     {@link PRPAIN201306UV02 .ControlActProcess.Subject.RegistrationEvent.Subject1 .Patient.PatientPerson.RaceCode }
                             *     
                             */
                            public PRPAIN201306UV02 .ControlActProcess.Subject.RegistrationEvent.Subject1 .Patient.PatientPerson.RaceCode getRaceCode() {
                                return raceCode;
                            }

                            /**
                             * Define o valor da propriedade raceCode.
                             * 
                             * @param value
                             *     allowed object is
                             *     {@link PRPAIN201306UV02 .ControlActProcess.Subject.RegistrationEvent.Subject1 .Patient.PatientPerson.RaceCode }
                             *     
                             */
                            public void setRaceCode(PRPAIN201306UV02 .ControlActProcess.Subject.RegistrationEvent.Subject1 .Patient.PatientPerson.RaceCode value) {
                                this.raceCode = value;
                            }

                            /**
                             * Gets the value of the asOtherIDs property.
                             * 
                             * <p>
                             * This accessor method returns a reference to the live list,
                             * not a snapshot. Therefore any modification you make to the
                             * returned list will be present inside the JAXB object.
                             * This is why there is not a <CODE>set</CODE> method for the asOtherIDs property.
                             * 
                             * <p>
                             * For example, to add a new item, do as follows:
                             * <pre>
                             *    getAsOtherIDs().add(newItem);
                             * </pre>
                             * 
                             * 
                             * <p>
                             * Objects of the following type(s) are allowed in the list
                             * {@link PRPAIN201306UV02 .ControlActProcess.Subject.RegistrationEvent.Subject1 .Patient.PatientPerson.AsOtherIDs }
                             * 
                             * 
                             */
                            public List<PRPAIN201306UV02 .ControlActProcess.Subject.RegistrationEvent.Subject1 .Patient.PatientPerson.AsOtherIDs> getAsOtherIDs() {
                                if (asOtherIDs == null) {
                                    asOtherIDs = new ArrayList<PRPAIN201306UV02 .ControlActProcess.Subject.RegistrationEvent.Subject1 .Patient.PatientPerson.AsOtherIDs>();
                                }
                                return this.asOtherIDs;
                            }

                            /**
                             * Gets the value of the personalRelationship property.
                             * 
                             * <p>
                             * This accessor method returns a reference to the live list,
                             * not a snapshot. Therefore any modification you make to the
                             * returned list will be present inside the JAXB object.
                             * This is why there is not a <CODE>set</CODE> method for the personalRelationship property.
                             * 
                             * <p>
                             * For example, to add a new item, do as follows:
                             * <pre>
                             *    getPersonalRelationship().add(newItem);
                             * </pre>
                             * 
                             * 
                             * <p>
                             * Objects of the following type(s) are allowed in the list
                             * {@link PRPAIN201306UV02 .ControlActProcess.Subject.RegistrationEvent.Subject1 .Patient.PatientPerson.PersonalRelationship }
                             * 
                             * 
                             */
                            public List<PRPAIN201306UV02 .ControlActProcess.Subject.RegistrationEvent.Subject1 .Patient.PatientPerson.PersonalRelationship> getPersonalRelationship() {
                                if (personalRelationship == null) {
                                    personalRelationship = new ArrayList<PRPAIN201306UV02 .ControlActProcess.Subject.RegistrationEvent.Subject1 .Patient.PatientPerson.PersonalRelationship>();
                                }
                                return this.personalRelationship;
                            }

                            /**
                             * Obtém o valor da propriedade birthPlace.
                             * 
                             * @return
                             *     possible object is
                             *     {@link PRPAIN201306UV02 .ControlActProcess.Subject.RegistrationEvent.Subject1 .Patient.PatientPerson.BirthPlace }
                             *     
                             */
                            public PRPAIN201306UV02 .ControlActProcess.Subject.RegistrationEvent.Subject1 .Patient.PatientPerson.BirthPlace getBirthPlace() {
                                return birthPlace;
                            }

                            /**
                             * Define o valor da propriedade birthPlace.
                             * 
                             * @param value
                             *     allowed object is
                             *     {@link PRPAIN201306UV02 .ControlActProcess.Subject.RegistrationEvent.Subject1 .Patient.PatientPerson.BirthPlace }
                             *     
                             */
                            public void setBirthPlace(PRPAIN201306UV02 .ControlActProcess.Subject.RegistrationEvent.Subject1 .Patient.PatientPerson.BirthPlace value) {
                                this.birthPlace = value;
                            }

                            /**
                             * Obtém o valor da propriedade classCode.
                             * 
                             * @return
                             *     possible object is
                             *     {@link String }
                             *     
                             */
                            public String getClassCode() {
                                return classCode;
                            }

                            /**
                             * Define o valor da propriedade classCode.
                             * 
                             * @param value
                             *     allowed object is
                             *     {@link String }
                             *     
                             */
                            public void setClassCode(String value) {
                                this.classCode = value;
                            }

                            /**
                             * Obtém o valor da propriedade determinerCode.
                             * 
                             * @return
                             *     possible object is
                             *     {@link String }
                             *     
                             */
                            public String getDeterminerCode() {
                                return determinerCode;
                            }

                            /**
                             * Define o valor da propriedade determinerCode.
                             * 
                             * @param value
                             *     allowed object is
                             *     {@link String }
                             *     
                             */
                            public void setDeterminerCode(String value) {
                                this.determinerCode = value;
                            }


                            /**
                             * <p>Classe Java de anonymous complex type.
                             * 
                             * <p>O seguinte fragmento do esquema especifica o conteúdo esperado contido dentro desta classe.
                             * 
                             * <pre>
                             * &lt;complexType>
                             *   &lt;complexContent>
                             *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
                             *       &lt;sequence>
                             *         &lt;element name="city" type="{http://www.w3.org/2001/XMLSchema}unsignedInt"/>
                             *         &lt;element name="state" type="{http://www.w3.org/2001/XMLSchema}string"/>
                             *         &lt;element name="postalCode" type="{http://www.w3.org/2001/XMLSchema}unsignedInt"/>
                             *         &lt;element name="country" type="{http://www.w3.org/2001/XMLSchema}unsignedByte"/>
                             *         &lt;element name="houseNumber" type="{http://www.w3.org/2001/XMLSchema}unsignedShort"/>
                             *         &lt;element name="streetNameType" type="{http://www.w3.org/2001/XMLSchema}unsignedByte"/>
                             *         &lt;element name="additionalLocator" type="{http://www.w3.org/2001/XMLSchema}string"/>
                             *       &lt;/sequence>
                             *       &lt;attribute name="use" use="required" type="{http://www.w3.org/2001/XMLSchema}string" />
                             *     &lt;/restriction>
                             *   &lt;/complexContent>
                             * &lt;/complexType>
                             * </pre>
                             * 
                             * 
                             */
                            @XmlAccessorType(XmlAccessType.FIELD)
                            @XmlType(name = "", propOrder = {
                                "city",
                                "state",
                                "postalCode",
                                "country",
                                "houseNumber",
                                "streetNameType",
                                "additionalLocator"
                            })
                            public static class Addr {

                                @XmlSchemaType(name = "unsignedInt")
                                protected long city;
                                @XmlElement(required = true)
                                protected String state;
                                @XmlSchemaType(name = "unsignedInt")
                                protected long postalCode;
                                @XmlSchemaType(name = "unsignedByte")
                                protected short country;
                                @XmlSchemaType(name = "unsignedShort")
                                protected int houseNumber;
                                @XmlSchemaType(name = "unsignedByte")
                                protected short streetNameType;
                                @XmlElement(required = true)
                                protected String additionalLocator;
                                @XmlAttribute(name = "use", required = true)
                                protected String use;

                                /**
                                 * Obtém o valor da propriedade city.
                                 * 
                                 */
                                public long getCity() {
                                    return city;
                                }

                                /**
                                 * Define o valor da propriedade city.
                                 * 
                                 */
                                public void setCity(long value) {
                                    this.city = value;
                                }

                                /**
                                 * Obtém o valor da propriedade state.
                                 * 
                                 * @return
                                 *     possible object is
                                 *     {@link String }
                                 *     
                                 */
                                public String getState() {
                                    return state;
                                }

                                /**
                                 * Define o valor da propriedade state.
                                 * 
                                 * @param value
                                 *     allowed object is
                                 *     {@link String }
                                 *     
                                 */
                                public void setState(String value) {
                                    this.state = value;
                                }

                                /**
                                 * Obtém o valor da propriedade postalCode.
                                 * 
                                 */
                                public long getPostalCode() {
                                    return postalCode;
                                }

                                /**
                                 * Define o valor da propriedade postalCode.
                                 * 
                                 */
                                public void setPostalCode(long value) {
                                    this.postalCode = value;
                                }

                                /**
                                 * Obtém o valor da propriedade country.
                                 * 
                                 */
                                public short getCountry() {
                                    return country;
                                }

                                /**
                                 * Define o valor da propriedade country.
                                 * 
                                 */
                                public void setCountry(short value) {
                                    this.country = value;
                                }

                                /**
                                 * Obtém o valor da propriedade houseNumber.
                                 * 
                                 */
                                public int getHouseNumber() {
                                    return houseNumber;
                                }

                                /**
                                 * Define o valor da propriedade houseNumber.
                                 * 
                                 */
                                public void setHouseNumber(int value) {
                                    this.houseNumber = value;
                                }

                                /**
                                 * Obtém o valor da propriedade streetNameType.
                                 * 
                                 */
                                public short getStreetNameType() {
                                    return streetNameType;
                                }

                                /**
                                 * Define o valor da propriedade streetNameType.
                                 * 
                                 */
                                public void setStreetNameType(short value) {
                                    this.streetNameType = value;
                                }

                                /**
                                 * Obtém o valor da propriedade additionalLocator.
                                 * 
                                 * @return
                                 *     possible object is
                                 *     {@link String }
                                 *     
                                 */
                                public String getAdditionalLocator() {
                                    return additionalLocator;
                                }

                                /**
                                 * Define o valor da propriedade additionalLocator.
                                 * 
                                 * @param value
                                 *     allowed object is
                                 *     {@link String }
                                 *     
                                 */
                                public void setAdditionalLocator(String value) {
                                    this.additionalLocator = value;
                                }

                                /**
                                 * Obtém o valor da propriedade use.
                                 * 
                                 * @return
                                 *     possible object is
                                 *     {@link String }
                                 *     
                                 */
                                public String getUse() {
                                    return use;
                                }

                                /**
                                 * Define o valor da propriedade use.
                                 * 
                                 * @param value
                                 *     allowed object is
                                 *     {@link String }
                                 *     
                                 */
                                public void setUse(String value) {
                                    this.use = value;
                                }

                            }


                            /**
                             * <p>Classe Java de anonymous complex type.
                             * 
                             * <p>O seguinte fragmento do esquema especifica o conteúdo esperado contido dentro desta classe.
                             * 
                             * <pre>
                             * &lt;complexType>
                             *   &lt;complexContent>
                             *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
                             *       &lt;attribute name="code" use="required" type="{http://www.w3.org/2001/XMLSchema}string" />
                             *       &lt;attribute name="codeSystem" use="required" type="{http://www.w3.org/2001/XMLSchema}string" />
                             *     &lt;/restriction>
                             *   &lt;/complexContent>
                             * &lt;/complexType>
                             * </pre>
                             * 
                             * 
                             */
                            @XmlAccessorType(XmlAccessType.FIELD)
                            @XmlType(name = "")
                            public static class AdministrativeGenderCode {

                                @XmlAttribute(name = "code", required = true)
                                protected String code;
                                @XmlAttribute(name = "codeSystem", required = true)
                                protected String codeSystem;

                                /**
                                 * Obtém o valor da propriedade code.
                                 * 
                                 * @return
                                 *     possible object is
                                 *     {@link String }
                                 *     
                                 */
                                public String getCode() {
                                    return code;
                                }

                                /**
                                 * Define o valor da propriedade code.
                                 * 
                                 * @param value
                                 *     allowed object is
                                 *     {@link String }
                                 *     
                                 */
                                public void setCode(String value) {
                                    this.code = value;
                                }

                                /**
                                 * Obtém o valor da propriedade codeSystem.
                                 * 
                                 * @return
                                 *     possible object is
                                 *     {@link String }
                                 *     
                                 */
                                public String getCodeSystem() {
                                    return codeSystem;
                                }

                                /**
                                 * Define o valor da propriedade codeSystem.
                                 * 
                                 * @param value
                                 *     allowed object is
                                 *     {@link String }
                                 *     
                                 */
                                public void setCodeSystem(String value) {
                                    this.codeSystem = value;
                                }

                            }


                            /**
                             * <p>Classe Java de anonymous complex type.
                             * 
                             * <p>O seguinte fragmento do esquema especifica o conteúdo esperado contido dentro desta classe.
                             * 
                             * <pre>
                             * &lt;complexType>
                             *   &lt;complexContent>
                             *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
                             *       &lt;sequence>
                             *         &lt;element name="id" maxOccurs="unbounded">
                             *           &lt;complexType>
                             *             &lt;complexContent>
                             *               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
                             *                 &lt;attribute name="root" use="required" type="{http://www.w3.org/2001/XMLSchema}string" />
                             *                 &lt;attribute name="extension" use="required" type="{http://www.w3.org/2001/XMLSchema}string" />
                             *               &lt;/restriction>
                             *             &lt;/complexContent>
                             *           &lt;/complexType>
                             *         &lt;/element>
                             *         &lt;element name="scopingOrganization">
                             *           &lt;complexType>
                             *             &lt;complexContent>
                             *               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
                             *                 &lt;sequence>
                             *                   &lt;element name="id" maxOccurs="unbounded">
                             *                     &lt;complexType>
                             *                       &lt;complexContent>
                             *                         &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
                             *                           &lt;attribute name="root" use="required" type="{http://www.w3.org/2001/XMLSchema}string" />
                             *                         &lt;/restriction>
                             *                       &lt;/complexContent>
                             *                     &lt;/complexType>
                             *                   &lt;/element>
                             *                 &lt;/sequence>
                             *                 &lt;attribute name="classCode" use="required" type="{http://www.w3.org/2001/XMLSchema}string" />
                             *                 &lt;attribute name="determinerCode" use="required" type="{http://www.w3.org/2001/XMLSchema}string" />
                             *               &lt;/restriction>
                             *             &lt;/complexContent>
                             *           &lt;/complexType>
                             *         &lt;/element>
                             *       &lt;/sequence>
                             *       &lt;attribute name="classCode" use="required" type="{http://www.w3.org/2001/XMLSchema}string" />
                             *     &lt;/restriction>
                             *   &lt;/complexContent>
                             * &lt;/complexType>
                             * </pre>
                             * 
                             * 
                             */
                            @XmlAccessorType(XmlAccessType.FIELD)
                            @XmlType(name = "", propOrder = {
                                "id",
                                "scopingOrganization"
                            })
                            public static class AsOtherIDs {

                                @XmlElement(required = true)
                                protected List<PRPAIN201306UV02 .ControlActProcess.Subject.RegistrationEvent.Subject1 .Patient.PatientPerson.AsOtherIDs.Id> id;
                                @XmlElement(required = true)
                                protected PRPAIN201306UV02 .ControlActProcess.Subject.RegistrationEvent.Subject1 .Patient.PatientPerson.AsOtherIDs.ScopingOrganization scopingOrganization;
                                @XmlAttribute(name = "classCode", required = true)
                                protected String classCode;

                                /**
                                 * Gets the value of the id property.
                                 * 
                                 * <p>
                                 * This accessor method returns a reference to the live list,
                                 * not a snapshot. Therefore any modification you make to the
                                 * returned list will be present inside the JAXB object.
                                 * This is why there is not a <CODE>set</CODE> method for the id property.
                                 * 
                                 * <p>
                                 * For example, to add a new item, do as follows:
                                 * <pre>
                                 *    getId().add(newItem);
                                 * </pre>
                                 * 
                                 * 
                                 * <p>
                                 * Objects of the following type(s) are allowed in the list
                                 * {@link PRPAIN201306UV02 .ControlActProcess.Subject.RegistrationEvent.Subject1 .Patient.PatientPerson.AsOtherIDs.Id }
                                 * 
                                 * 
                                 */
                                public List<PRPAIN201306UV02 .ControlActProcess.Subject.RegistrationEvent.Subject1 .Patient.PatientPerson.AsOtherIDs.Id> getId() {
                                    if (id == null) {
                                        id = new ArrayList<PRPAIN201306UV02 .ControlActProcess.Subject.RegistrationEvent.Subject1 .Patient.PatientPerson.AsOtherIDs.Id>();
                                    }
                                    return this.id;
                                }

                                /**
                                 * Obtém o valor da propriedade scopingOrganization.
                                 * 
                                 * @return
                                 *     possible object is
                                 *     {@link PRPAIN201306UV02 .ControlActProcess.Subject.RegistrationEvent.Subject1 .Patient.PatientPerson.AsOtherIDs.ScopingOrganization }
                                 *     
                                 */
                                public PRPAIN201306UV02 .ControlActProcess.Subject.RegistrationEvent.Subject1 .Patient.PatientPerson.AsOtherIDs.ScopingOrganization getScopingOrganization() {
                                    return scopingOrganization;
                                }

                                /**
                                 * Define o valor da propriedade scopingOrganization.
                                 * 
                                 * @param value
                                 *     allowed object is
                                 *     {@link PRPAIN201306UV02 .ControlActProcess.Subject.RegistrationEvent.Subject1 .Patient.PatientPerson.AsOtherIDs.ScopingOrganization }
                                 *     
                                 */
                                public void setScopingOrganization(PRPAIN201306UV02 .ControlActProcess.Subject.RegistrationEvent.Subject1 .Patient.PatientPerson.AsOtherIDs.ScopingOrganization value) {
                                    this.scopingOrganization = value;
                                }

                                /**
                                 * Obtém o valor da propriedade classCode.
                                 * 
                                 * @return
                                 *     possible object is
                                 *     {@link String }
                                 *     
                                 */
                                public String getClassCode() {
                                    return classCode;
                                }

                                /**
                                 * Define o valor da propriedade classCode.
                                 * 
                                 * @param value
                                 *     allowed object is
                                 *     {@link String }
                                 *     
                                 */
                                public void setClassCode(String value) {
                                    this.classCode = value;
                                }


                                /**
                                 * <p>Classe Java de anonymous complex type.
                                 * 
                                 * <p>O seguinte fragmento do esquema especifica o conteúdo esperado contido dentro desta classe.
                                 * 
                                 * <pre>
                                 * &lt;complexType>
                                 *   &lt;complexContent>
                                 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
                                 *       &lt;attribute name="root" use="required" type="{http://www.w3.org/2001/XMLSchema}string" />
                                 *       &lt;attribute name="extension" use="required" type="{http://www.w3.org/2001/XMLSchema}string" />
                                 *     &lt;/restriction>
                                 *   &lt;/complexContent>
                                 * &lt;/complexType>
                                 * </pre>
                                 * 
                                 * 
                                 */
                                @XmlAccessorType(XmlAccessType.FIELD)
                                @XmlType(name = "")
                                public static class Id {

                                    @XmlAttribute(name = "root", required = true)
                                    protected String root;
                                    @XmlAttribute(name = "extension", required = true)
                                    protected String extension;

                                    /**
                                     * Obtém o valor da propriedade root.
                                     * 
                                     * @return
                                     *     possible object is
                                     *     {@link String }
                                     *     
                                     */
                                    public String getRoot() {
                                        return root;
                                    }

                                    /**
                                     * Define o valor da propriedade root.
                                     * 
                                     * @param value
                                     *     allowed object is
                                     *     {@link String }
                                     *     
                                     */
                                    public void setRoot(String value) {
                                        this.root = value;
                                    }

                                    /**
                                     * Obtém o valor da propriedade extension.
                                     * 
                                     * @return
                                     *     possible object is
                                     *     {@link String }
                                     *     
                                     */
                                    public String getExtension() {
                                        return extension;
                                    }

                                    /**
                                     * Define o valor da propriedade extension.
                                     * 
                                     * @param value
                                     *     allowed object is
                                     *     {@link String }
                                     *     
                                     */
                                    public void setExtension(String value) {
                                        this.extension = value;
                                    }

                                }


                                /**
                                 * <p>Classe Java de anonymous complex type.
                                 * 
                                 * <p>O seguinte fragmento do esquema especifica o conteúdo esperado contido dentro desta classe.
                                 * 
                                 * <pre>
                                 * &lt;complexType>
                                 *   &lt;complexContent>
                                 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
                                 *       &lt;sequence>
                                 *         &lt;element name="id" maxOccurs="unbounded">
                                 *           &lt;complexType>
                                 *             &lt;complexContent>
                                 *               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
                                 *                 &lt;attribute name="root" use="required" type="{http://www.w3.org/2001/XMLSchema}string" />
                                 *               &lt;/restriction>
                                 *             &lt;/complexContent>
                                 *           &lt;/complexType>
                                 *         &lt;/element>
                                 *       &lt;/sequence>
                                 *       &lt;attribute name="classCode" use="required" type="{http://www.w3.org/2001/XMLSchema}string" />
                                 *       &lt;attribute name="determinerCode" use="required" type="{http://www.w3.org/2001/XMLSchema}string" />
                                 *     &lt;/restriction>
                                 *   &lt;/complexContent>
                                 * &lt;/complexType>
                                 * </pre>
                                 * 
                                 * 
                                 */
                                @XmlAccessorType(XmlAccessType.FIELD)
                                @XmlType(name = "", propOrder = {
                                    "id"
                                })
                                public static class ScopingOrganization {

                                    @XmlElement(required = true)
                                    protected List<PRPAIN201306UV02 .ControlActProcess.Subject.RegistrationEvent.Subject1 .Patient.PatientPerson.AsOtherIDs.ScopingOrganization.Id> id;
                                    @XmlAttribute(name = "classCode", required = true)
                                    protected String classCode;
                                    @XmlAttribute(name = "determinerCode", required = true)
                                    protected String determinerCode;

                                    /**
                                     * Gets the value of the id property.
                                     * 
                                     * <p>
                                     * This accessor method returns a reference to the live list,
                                     * not a snapshot. Therefore any modification you make to the
                                     * returned list will be present inside the JAXB object.
                                     * This is why there is not a <CODE>set</CODE> method for the id property.
                                     * 
                                     * <p>
                                     * For example, to add a new item, do as follows:
                                     * <pre>
                                     *    getId().add(newItem);
                                     * </pre>
                                     * 
                                     * 
                                     * <p>
                                     * Objects of the following type(s) are allowed in the list
                                     * {@link PRPAIN201306UV02 .ControlActProcess.Subject.RegistrationEvent.Subject1 .Patient.PatientPerson.AsOtherIDs.ScopingOrganization.Id }
                                     * 
                                     * 
                                     */
                                    public List<PRPAIN201306UV02 .ControlActProcess.Subject.RegistrationEvent.Subject1 .Patient.PatientPerson.AsOtherIDs.ScopingOrganization.Id> getId() {
                                        if (id == null) {
                                            id = new ArrayList<PRPAIN201306UV02 .ControlActProcess.Subject.RegistrationEvent.Subject1 .Patient.PatientPerson.AsOtherIDs.ScopingOrganization.Id>();
                                        }
                                        return this.id;
                                    }

                                    /**
                                     * Obtém o valor da propriedade classCode.
                                     * 
                                     * @return
                                     *     possible object is
                                     *     {@link String }
                                     *     
                                     */
                                    public String getClassCode() {
                                        return classCode;
                                    }

                                    /**
                                     * Define o valor da propriedade classCode.
                                     * 
                                     * @param value
                                     *     allowed object is
                                     *     {@link String }
                                     *     
                                     */
                                    public void setClassCode(String value) {
                                        this.classCode = value;
                                    }

                                    /**
                                     * Obtém o valor da propriedade determinerCode.
                                     * 
                                     * @return
                                     *     possible object is
                                     *     {@link String }
                                     *     
                                     */
                                    public String getDeterminerCode() {
                                        return determinerCode;
                                    }

                                    /**
                                     * Define o valor da propriedade determinerCode.
                                     * 
                                     * @param value
                                     *     allowed object is
                                     *     {@link String }
                                     *     
                                     */
                                    public void setDeterminerCode(String value) {
                                        this.determinerCode = value;
                                    }


                                    /**
                                     * <p>Classe Java de anonymous complex type.
                                     * 
                                     * <p>O seguinte fragmento do esquema especifica o conteúdo esperado contido dentro desta classe.
                                     * 
                                     * <pre>
                                     * &lt;complexType>
                                     *   &lt;complexContent>
                                     *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
                                     *       &lt;attribute name="root" use="required" type="{http://www.w3.org/2001/XMLSchema}string" />
                                     *     &lt;/restriction>
                                     *   &lt;/complexContent>
                                     * &lt;/complexType>
                                     * </pre>
                                     * 
                                     * 
                                     */
                                    @XmlAccessorType(XmlAccessType.FIELD)
                                    @XmlType(name = "")
                                    public static class Id {

                                        @XmlAttribute(name = "root", required = true)
                                        protected String root;

                                        /**
                                         * Obtém o valor da propriedade root.
                                         * 
                                         * @return
                                         *     possible object is
                                         *     {@link String }
                                         *     
                                         */
                                        public String getRoot() {
                                            return root;
                                        }

                                        /**
                                         * Define o valor da propriedade root.
                                         * 
                                         * @param value
                                         *     allowed object is
                                         *     {@link String }
                                         *     
                                         */
                                        public void setRoot(String value) {
                                            this.root = value;
                                        }

                                    }

                                }

                            }


                            /**
                             * <p>Classe Java de anonymous complex type.
                             * 
                             * <p>O seguinte fragmento do esquema especifica o conteúdo esperado contido dentro desta classe.
                             * 
                             * <pre>
                             * &lt;complexType>
                             *   &lt;complexContent>
                             *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
                             *       &lt;sequence>
                             *         &lt;element name="addr">
                             *           &lt;complexType>
                             *             &lt;complexContent>
                             *               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
                             *                 &lt;sequence>
                             *                   &lt;element name="city" type="{http://www.w3.org/2001/XMLSchema}unsignedInt"/>
                             *                   &lt;element name="country" type="{http://www.w3.org/2001/XMLSchema}unsignedByte"/>
                             *                 &lt;/sequence>
                             *               &lt;/restriction>
                             *             &lt;/complexContent>
                             *           &lt;/complexType>
                             *         &lt;/element>
                             *       &lt;/sequence>
                             *       &lt;attribute name="classCode" use="required" type="{http://www.w3.org/2001/XMLSchema}string" />
                             *     &lt;/restriction>
                             *   &lt;/complexContent>
                             * &lt;/complexType>
                             * </pre>
                             * 
                             * 
                             */
                            @XmlAccessorType(XmlAccessType.FIELD)
                            @XmlType(name = "", propOrder = {
                                "addr"
                            })
                            public static class BirthPlace {

                                @XmlElement(required = true)
                                protected PRPAIN201306UV02 .ControlActProcess.Subject.RegistrationEvent.Subject1 .Patient.PatientPerson.BirthPlace.Addr addr;
                                @XmlAttribute(name = "classCode", required = true)
                                protected String classCode;

                                /**
                                 * Obtém o valor da propriedade addr.
                                 * 
                                 * @return
                                 *     possible object is
                                 *     {@link PRPAIN201306UV02 .ControlActProcess.Subject.RegistrationEvent.Subject1 .Patient.PatientPerson.BirthPlace.Addr }
                                 *     
                                 */
                                public PRPAIN201306UV02 .ControlActProcess.Subject.RegistrationEvent.Subject1 .Patient.PatientPerson.BirthPlace.Addr getAddr() {
                                    return addr;
                                }

                                /**
                                 * Define o valor da propriedade addr.
                                 * 
                                 * @param value
                                 *     allowed object is
                                 *     {@link PRPAIN201306UV02 .ControlActProcess.Subject.RegistrationEvent.Subject1 .Patient.PatientPerson.BirthPlace.Addr }
                                 *     
                                 */
                                public void setAddr(PRPAIN201306UV02 .ControlActProcess.Subject.RegistrationEvent.Subject1 .Patient.PatientPerson.BirthPlace.Addr value) {
                                    this.addr = value;
                                }

                                /**
                                 * Obtém o valor da propriedade classCode.
                                 * 
                                 * @return
                                 *     possible object is
                                 *     {@link String }
                                 *     
                                 */
                                public String getClassCode() {
                                    return classCode;
                                }

                                /**
                                 * Define o valor da propriedade classCode.
                                 * 
                                 * @param value
                                 *     allowed object is
                                 *     {@link String }
                                 *     
                                 */
                                public void setClassCode(String value) {
                                    this.classCode = value;
                                }


                                /**
                                 * <p>Classe Java de anonymous complex type.
                                 * 
                                 * <p>O seguinte fragmento do esquema especifica o conteúdo esperado contido dentro desta classe.
                                 * 
                                 * <pre>
                                 * &lt;complexType>
                                 *   &lt;complexContent>
                                 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
                                 *       &lt;sequence>
                                 *         &lt;element name="city" type="{http://www.w3.org/2001/XMLSchema}unsignedInt"/>
                                 *         &lt;element name="country" type="{http://www.w3.org/2001/XMLSchema}unsignedByte"/>
                                 *       &lt;/sequence>
                                 *     &lt;/restriction>
                                 *   &lt;/complexContent>
                                 * &lt;/complexType>
                                 * </pre>
                                 * 
                                 * 
                                 */
                                @XmlAccessorType(XmlAccessType.FIELD)
                                @XmlType(name = "", propOrder = {
                                    "city",
                                    "country"
                                })
                                public static class Addr {

                                    @XmlSchemaType(name = "unsignedInt")
                                    protected long city;
                                    @XmlSchemaType(name = "unsignedByte")
                                    protected short country;

                                    /**
                                     * Obtém o valor da propriedade city.
                                     * 
                                     */
                                    public long getCity() {
                                        return city;
                                    }

                                    /**
                                     * Define o valor da propriedade city.
                                     * 
                                     */
                                    public void setCity(long value) {
                                        this.city = value;
                                    }

                                    /**
                                     * Obtém o valor da propriedade country.
                                     * 
                                     */
                                    public short getCountry() {
                                        return country;
                                    }

                                    /**
                                     * Define o valor da propriedade country.
                                     * 
                                     */
                                    public void setCountry(short value) {
                                        this.country = value;
                                    }

                                }

                            }


                            /**
                             * <p>Classe Java de anonymous complex type.
                             * 
                             * <p>O seguinte fragmento do esquema especifica o conteúdo esperado contido dentro desta classe.
                             * 
                             * <pre>
                             * &lt;complexType>
                             *   &lt;complexContent>
                             *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
                             *       &lt;attribute name="value" use="required" type="{http://www.w3.org/2001/XMLSchema}unsignedLong" />
                             *     &lt;/restriction>
                             *   &lt;/complexContent>
                             * &lt;/complexType>
                             * </pre>
                             * 
                             * 
                             */
                            @XmlAccessorType(XmlAccessType.FIELD)
                            @XmlType(name = "")
                            public static class BirthTime {

                                @XmlAttribute(name = "value", required = true)
                                @XmlSchemaType(name = "unsignedLong")
                                protected BigInteger value;

                                /**
                                 * Obtém o valor da propriedade value.
                                 * 
                                 * @return
                                 *     possible object is
                                 *     {@link BigInteger }
                                 *     
                                 */
                                public BigInteger getValue() {
                                    return value;
                                }

                                /**
                                 * Define o valor da propriedade value.
                                 * 
                                 * @param value
                                 *     allowed object is
                                 *     {@link BigInteger }
                                 *     
                                 */
                                public void setValue(BigInteger value) {
                                    this.value = value;
                                }

                            }


                            /**
                             * <p>Classe Java de anonymous complex type.
                             * 
                             * <p>O seguinte fragmento do esquema especifica o conteúdo esperado contido dentro desta classe.
                             * 
                             * <pre>
                             * &lt;complexType>
                             *   &lt;complexContent>
                             *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
                             *       &lt;attribute name="code" use="required" type="{http://www.w3.org/2001/XMLSchema}unsignedByte" />
                             *     &lt;/restriction>
                             *   &lt;/complexContent>
                             * &lt;/complexType>
                             * </pre>
                             * 
                             * 
                             */
                            @XmlAccessorType(XmlAccessType.FIELD)
                            @XmlType(name = "")
                            public static class MaritalStatusCode {

                                @XmlAttribute(name = "code", required = true)
                                @XmlSchemaType(name = "unsignedByte")
                                protected short code;

                                /**
                                 * Obtém o valor da propriedade code.
                                 * 
                                 */
                                public short getCode() {
                                    return code;
                                }

                                /**
                                 * Define o valor da propriedade code.
                                 * 
                                 */
                                public void setCode(short value) {
                                    this.code = value;
                                }

                            }




                            /**
                             * <p>Classe Java de anonymous complex type.
                             * 
                             * <p>O seguinte fragmento do esquema especifica o conteúdo esperado contido dentro desta classe.
                             * 
                             * <pre>
                             * &lt;complexType>
                             *   &lt;complexContent>
                             *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
                             *       &lt;sequence>
                             *         &lt;element name="given" type="{http://www.w3.org/2001/XMLSchema}string"/>
                             *       &lt;/sequence>
                             *       &lt;attribute name="use" use="required" type="{http://www.w3.org/2001/XMLSchema}string" />
                             *     &lt;/restriction>
                             *   &lt;/complexContent>
                             * &lt;/complexType>
                             * </pre>
                             * 
                             * 
                             */
                            @XmlAccessorType(XmlAccessType.FIELD)
                            @XmlType(name = "", propOrder = {
                                "given"
                            })
                            public static class Name {

                                @XmlElement(required = true)
                                protected String given;
                                @XmlAttribute(name = "use", required = true)
                                protected String use;

                                /**
                                 * Obtém o valor da propriedade given.
                                 *
                                 * @return
                                 *     possible object is
                                 *     {@link String }
                                 *
                                 */
                                public String getGiven() {
                                    return given;
                                }

                                /**
                                 * Define o valor da propriedade given.
                                 *
                                 * @param value
                                 *     allowed object is
                                 *     {@link String }
                                 *
                                 */
                                public void setGiven(String value) {
                                    this.given = value;
                                }

                                /**
                                 * Obtém o valor da propriedade use.
                                 *
                                 * @return
                                 *     possible object is
                                 *     {@link String }
                                 *
                                 */
                                public String getUse() {
                                    return use;
                                }

                                /**
                                 * Define o valor da propriedade use.
                                 *
                                 * @param value
                                 *     allowed object is
                                 *     {@link String }
                                 *
                                 */
                                public void setUse(String value) {
                                    this.use = value;
                                }

                            }


                            /**
                             * <p>Classe Java de anonymous complex type.
                             * 
                             * <p>O seguinte fragmento do esquema especifica o conteúdo esperado contido dentro desta classe.
                             * 
                             * <pre>
                             * &lt;complexType>
                             *   &lt;complexContent>
                             *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
                             *       &lt;sequence>
                             *         &lt;element name="code">
                             *           &lt;complexType>
                             *             &lt;complexContent>
                             *               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
                             *                 &lt;attribute name="code" use="required" type="{http://www.w3.org/2001/XMLSchema}string" />
                             *                 &lt;attribute name="codeSystem" use="required" type="{http://www.w3.org/2001/XMLSchema}string" />
                             *               &lt;/restriction>
                             *             &lt;/complexContent>
                             *           &lt;/complexType>
                             *         &lt;/element>
                             *         &lt;element name="relationshipHolder1">
                             *           &lt;complexType>
                             *             &lt;complexContent>
                             *               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
                             *                 &lt;sequence>
                             *                   &lt;element name="name">
                             *                     &lt;complexType>
                             *                       &lt;complexContent>
                             *                         &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
                             *                           &lt;sequence>
                             *                             &lt;element name="given" type="{http://www.w3.org/2001/XMLSchema}string"/>
                             *                           &lt;/sequence>
                             *                           &lt;attribute name="use" use="required" type="{http://www.w3.org/2001/XMLSchema}string" />
                             *                         &lt;/restriction>
                             *                       &lt;/complexContent>
                             *                     &lt;/complexType>
                             *                   &lt;/element>
                             *                 &lt;/sequence>
                             *                 &lt;attribute name="classCode" use="required" type="{http://www.w3.org/2001/XMLSchema}string" />
                             *                 &lt;attribute name="determinerCode" use="required" type="{http://www.w3.org/2001/XMLSchema}string" />
                             *               &lt;/restriction>
                             *             &lt;/complexContent>
                             *           &lt;/complexType>
                             *         &lt;/element>
                             *       &lt;/sequence>
                             *       &lt;attribute name="classCode" use="required" type="{http://www.w3.org/2001/XMLSchema}string" />
                             *     &lt;/restriction>
                             *   &lt;/complexContent>
                             * &lt;/complexType>
                             * </pre>
                             * 
                             * 
                             */
                            @XmlAccessorType(XmlAccessType.FIELD)
                            @XmlType(name = "", propOrder = {
                                "code",
                                "relationshipHolder1"
                            })
                            public static class PersonalRelationship {

                                @XmlElement(required = true)
                                protected PRPAIN201306UV02 .ControlActProcess.Subject.RegistrationEvent.Subject1 .Patient.PatientPerson.PersonalRelationship.Code code;
                                @XmlElement(required = true)
                                protected PRPAIN201306UV02 .ControlActProcess.Subject.RegistrationEvent.Subject1 .Patient.PatientPerson.PersonalRelationship.RelationshipHolder1 relationshipHolder1;
                                @XmlAttribute(name = "classCode", required = true)
                                protected String classCode;

                                /**
                                 * Obtém o valor da propriedade code.
                                 * 
                                 * @return
                                 *     possible object is
                                 *     {@link PRPAIN201306UV02 .ControlActProcess.Subject.RegistrationEvent.Subject1 .Patient.PatientPerson.PersonalRelationship.Code }
                                 *     
                                 */
                                public PRPAIN201306UV02 .ControlActProcess.Subject.RegistrationEvent.Subject1 .Patient.PatientPerson.PersonalRelationship.Code getCode() {
                                    return code;
                                }

                                /**
                                 * Define o valor da propriedade code.
                                 * 
                                 * @param value
                                 *     allowed object is
                                 *     {@link PRPAIN201306UV02 .ControlActProcess.Subject.RegistrationEvent.Subject1 .Patient.PatientPerson.PersonalRelationship.Code }
                                 *     
                                 */
                                public void setCode(PRPAIN201306UV02 .ControlActProcess.Subject.RegistrationEvent.Subject1 .Patient.PatientPerson.PersonalRelationship.Code value) {
                                    this.code = value;
                                }

                                /**
                                 * Obtém o valor da propriedade relationshipHolder1.
                                 * 
                                 * @return
                                 *     possible object is
                                 *     {@link PRPAIN201306UV02 .ControlActProcess.Subject.RegistrationEvent.Subject1 .Patient.PatientPerson.PersonalRelationship.RelationshipHolder1 }
                                 *     
                                 */
                                public PRPAIN201306UV02 .ControlActProcess.Subject.RegistrationEvent.Subject1 .Patient.PatientPerson.PersonalRelationship.RelationshipHolder1 getRelationshipHolder1() {
                                    return relationshipHolder1;
                                }

                                /**
                                 * Define o valor da propriedade relationshipHolder1.
                                 * 
                                 * @param value
                                 *     allowed object is
                                 *     {@link PRPAIN201306UV02 .ControlActProcess.Subject.RegistrationEvent.Subject1 .Patient.PatientPerson.PersonalRelationship.RelationshipHolder1 }
                                 *     
                                 */
                                public void setRelationshipHolder1(PRPAIN201306UV02 .ControlActProcess.Subject.RegistrationEvent.Subject1 .Patient.PatientPerson.PersonalRelationship.RelationshipHolder1 value) {
                                    this.relationshipHolder1 = value;
                                }

                                /**
                                 * Obtém o valor da propriedade classCode.
                                 * 
                                 * @return
                                 *     possible object is
                                 *     {@link String }
                                 *     
                                 */
                                public String getClassCode() {
                                    return classCode;
                                }

                                /**
                                 * Define o valor da propriedade classCode.
                                 * 
                                 * @param value
                                 *     allowed object is
                                 *     {@link String }
                                 *     
                                 */
                                public void setClassCode(String value) {
                                    this.classCode = value;
                                }


                                /**
                                 * <p>Classe Java de anonymous complex type.
                                 * 
                                 * <p>O seguinte fragmento do esquema especifica o conteúdo esperado contido dentro desta classe.
                                 * 
                                 * <pre>
                                 * &lt;complexType>
                                 *   &lt;complexContent>
                                 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
                                 *       &lt;attribute name="code" use="required" type="{http://www.w3.org/2001/XMLSchema}string" />
                                 *       &lt;attribute name="codeSystem" use="required" type="{http://www.w3.org/2001/XMLSchema}string" />
                                 *     &lt;/restriction>
                                 *   &lt;/complexContent>
                                 * &lt;/complexType>
                                 * </pre>
                                 * 
                                 * 
                                 */
                                @XmlAccessorType(XmlAccessType.FIELD)
                                @XmlType(name = "")
                                public static class Code {

                                    @XmlAttribute(name = "code", required = true)
                                    protected String code;
                                    @XmlAttribute(name = "codeSystem", required = true)
                                    protected String codeSystem;

                                    /**
                                     * Obtém o valor da propriedade code.
                                     * 
                                     * @return
                                     *     possible object is
                                     *     {@link String }
                                     *     
                                     */
                                    public String getCode() {
                                        return code;
                                    }

                                    /**
                                     * Define o valor da propriedade code.
                                     * 
                                     * @param value
                                     *     allowed object is
                                     *     {@link String }
                                     *     
                                     */
                                    public void setCode(String value) {
                                        this.code = value;
                                    }

                                    /**
                                     * Obtém o valor da propriedade codeSystem.
                                     * 
                                     * @return
                                     *     possible object is
                                     *     {@link String }
                                     *     
                                     */
                                    public String getCodeSystem() {
                                        return codeSystem;
                                    }

                                    /**
                                     * Define o valor da propriedade codeSystem.
                                     * 
                                     * @param value
                                     *     allowed object is
                                     *     {@link String }
                                     *     
                                     */
                                    public void setCodeSystem(String value) {
                                        this.codeSystem = value;
                                    }

                                }


                                /**
                                 * <p>Classe Java de anonymous complex type.
                                 * 
                                 * <p>O seguinte fragmento do esquema especifica o conteúdo esperado contido dentro desta classe.
                                 * 
                                 * <pre>
                                 * &lt;complexType>
                                 *   &lt;complexContent>
                                 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
                                 *       &lt;sequence>
                                 *         &lt;element name="name">
                                 *           &lt;complexType>
                                 *             &lt;complexContent>
                                 *               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
                                 *                 &lt;sequence>
                                 *                   &lt;element name="given" type="{http://www.w3.org/2001/XMLSchema}string"/>
                                 *                 &lt;/sequence>
                                 *                 &lt;attribute name="use" use="required" type="{http://www.w3.org/2001/XMLSchema}string" />
                                 *               &lt;/restriction>
                                 *             &lt;/complexContent>
                                 *           &lt;/complexType>
                                 *         &lt;/element>
                                 *       &lt;/sequence>
                                 *       &lt;attribute name="classCode" use="required" type="{http://www.w3.org/2001/XMLSchema}string" />
                                 *       &lt;attribute name="determinerCode" use="required" type="{http://www.w3.org/2001/XMLSchema}string" />
                                 *     &lt;/restriction>
                                 *   &lt;/complexContent>
                                 * &lt;/complexType>
                                 * </pre>
                                 * 
                                 * 
                                 */
                                @XmlAccessorType(XmlAccessType.FIELD)
                                @XmlType(name = "", propOrder = {
                                    "name"
                                })
                                public static class RelationshipHolder1 {

                                    @XmlElement(required = true)
                                    protected PRPAIN201306UV02 .ControlActProcess.Subject.RegistrationEvent.Subject1 .Patient.PatientPerson.PersonalRelationship.RelationshipHolder1 .Name name;
                                    @XmlAttribute(name = "classCode", required = true)
                                    protected String classCode;
                                    @XmlAttribute(name = "determinerCode", required = true)
                                    protected String determinerCode;

                                    /**
                                     * Obtém o valor da propriedade name.
                                     * 
                                     * @return
                                     *     possible object is
                                     *     {@link PRPAIN201306UV02 .ControlActProcess.Subject.RegistrationEvent.Subject1 .Patient.PatientPerson.PersonalRelationship.RelationshipHolder1 .Name }
                                     *     
                                     */
                                    public PRPAIN201306UV02 .ControlActProcess.Subject.RegistrationEvent.Subject1 .Patient.PatientPerson.PersonalRelationship.RelationshipHolder1 .Name getName() {
                                        return name;
                                    }

                                    /**
                                     * Define o valor da propriedade name.
                                     * 
                                     * @param value
                                     *     allowed object is
                                     *     {@link PRPAIN201306UV02 .ControlActProcess.Subject.RegistrationEvent.Subject1 .Patient.PatientPerson.PersonalRelationship.RelationshipHolder1 .Name }
                                     *     
                                     */
                                    public void setName(PRPAIN201306UV02 .ControlActProcess.Subject.RegistrationEvent.Subject1 .Patient.PatientPerson.PersonalRelationship.RelationshipHolder1 .Name value) {
                                        this.name = value;
                                    }

                                    /**
                                     * Obtém o valor da propriedade classCode.
                                     * 
                                     * @return
                                     *     possible object is
                                     *     {@link String }
                                     *     
                                     */
                                    public String getClassCode() {
                                        return classCode;
                                    }

                                    /**
                                     * Define o valor da propriedade classCode.
                                     * 
                                     * @param value
                                     *     allowed object is
                                     *     {@link String }
                                     *     
                                     */
                                    public void setClassCode(String value) {
                                        this.classCode = value;
                                    }

                                    /**
                                     * Obtém o valor da propriedade determinerCode.
                                     * 
                                     * @return
                                     *     possible object is
                                     *     {@link String }
                                     *     
                                     */
                                    public String getDeterminerCode() {
                                        return determinerCode;
                                    }

                                    /**
                                     * Define o valor da propriedade determinerCode.
                                     * 
                                     * @param value
                                     *     allowed object is
                                     *     {@link String }
                                     *     
                                     */
                                    public void setDeterminerCode(String value) {
                                        this.determinerCode = value;
                                    }


                                    /**
                                     * <p>Classe Java de anonymous complex type.
                                     * 
                                     * <p>O seguinte fragmento do esquema especifica o conteúdo esperado contido dentro desta classe.
                                     * 
                                     * <pre>
                                     * &lt;complexType>
                                     *   &lt;complexContent>
                                     *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
                                     *       &lt;sequence>
                                     *         &lt;element name="given" type="{http://www.w3.org/2001/XMLSchema}string"/>
                                     *       &lt;/sequence>
                                     *       &lt;attribute name="use" use="required" type="{http://www.w3.org/2001/XMLSchema}string" />
                                     *     &lt;/restriction>
                                     *   &lt;/complexContent>
                                     * &lt;/complexType>
                                     * </pre>
                                     * 
                                     * 
                                     */
                                    @XmlAccessorType(XmlAccessType.FIELD)
                                    @XmlType(name = "", propOrder = {
                                        "given"
                                    })
                                    public static class Name {

                                        @XmlElement(required = true)
                                        protected String given;
                                        @XmlAttribute(name = "use", required = true)
                                        protected String use;

                                        /**
                                         * Obtém o valor da propriedade given.
                                         * 
                                         * @return
                                         *     possible object is
                                         *     {@link String }
                                         *     
                                         */
                                        public String getGiven() {
                                            return given;
                                        }

                                        /**
                                         * Define o valor da propriedade given.
                                         * 
                                         * @param value
                                         *     allowed object is
                                         *     {@link String }
                                         *     
                                         */
                                        public void setGiven(String value) {
                                            this.given = value;
                                        }

                                        /**
                                         * Obtém o valor da propriedade use.
                                         * 
                                         * @return
                                         *     possible object is
                                         *     {@link String }
                                         *     
                                         */
                                        public String getUse() {
                                            return use;
                                        }

                                        /**
                                         * Define o valor da propriedade use.
                                         * 
                                         * @param value
                                         *     allowed object is
                                         *     {@link String }
                                         *     
                                         */
                                        public void setUse(String value) {
                                            this.use = value;
                                        }

                                    }

                                }

                            }


                            /**
                             * <p>Classe Java de anonymous complex type.
                             * 
                             * <p>O seguinte fragmento do esquema especifica o conteúdo esperado contido dentro desta classe.
                             * 
                             * <pre>
                             * &lt;complexType>
                             *   &lt;complexContent>
                             *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
                             *       &lt;attribute name="code" use="required" type="{http://www.w3.org/2001/XMLSchema}unsignedByte" />
                             *     &lt;/restriction>
                             *   &lt;/complexContent>
                             * &lt;/complexType>
                             * </pre>
                             * 
                             * 
                             */
                            @XmlAccessorType(XmlAccessType.FIELD)
                            @XmlType(name = "")
                            public static class RaceCode {

                                @XmlAttribute(name = "code", required = true)
                                @XmlSchemaType(name = "unsignedByte")
                                protected short code;

                                /**
                                 * Obtém o valor da propriedade code.
                                 * 
                                 */
                                public short getCode() {
                                    return code;
                                }

                                /**
                                 * Define o valor da propriedade code.
                                 * 
                                 */
                                public void setCode(short value) {
                                    this.code = value;
                                }

                            }

                        }


                        /**
                         * <p>Classe Java de anonymous complex type.
                         * 
                         * <p>O seguinte fragmento do esquema especifica o conteúdo esperado contido dentro desta classe.
                         * 
                         * <pre>
                         * &lt;complexType>
                         *   &lt;complexContent>
                         *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
                         *       &lt;sequence>
                         *         &lt;element name="id" maxOccurs="unbounded">
                         *           &lt;complexType>
                         *             &lt;complexContent>
                         *               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
                         *                 &lt;attribute name="root" use="required" type="{http://www.w3.org/2001/XMLSchema}string" />
                         *               &lt;/restriction>
                         *             &lt;/complexContent>
                         *           &lt;/complexType>
                         *         &lt;/element>
                         *         &lt;element name="contactParty">
                         *           &lt;complexType>
                         *             &lt;complexContent>
                         *               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
                         *                 &lt;attribute name="classCode" use="required" type="{http://www.w3.org/2001/XMLSchema}string" />
                         *               &lt;/restriction>
                         *             &lt;/complexContent>
                         *           &lt;/complexType>
                         *         &lt;/element>
                         *       &lt;/sequence>
                         *       &lt;attribute name="classCode" use="required" type="{http://www.w3.org/2001/XMLSchema}string" />
                         *       &lt;attribute name="determinerCode" use="required" type="{http://www.w3.org/2001/XMLSchema}string" />
                         *     &lt;/restriction>
                         *   &lt;/complexContent>
                         * &lt;/complexType>
                         * </pre>
                         * 
                         * 
                         */
                        @XmlAccessorType(XmlAccessType.FIELD)
                        @XmlType(name = "", propOrder = {
                            "id",
                            "contactParty"
                        })
                        public static class ProviderOrganization {

                            @XmlElement(required = true)
                            protected List<PRPAIN201306UV02 .ControlActProcess.Subject.RegistrationEvent.Subject1 .Patient.ProviderOrganization.Id> id;
                            @XmlElement(required = true)
                            protected PRPAIN201306UV02 .ControlActProcess.Subject.RegistrationEvent.Subject1 .Patient.ProviderOrganization.ContactParty contactParty;
                            @XmlAttribute(name = "classCode", required = true)
                            protected String classCode;
                            @XmlAttribute(name = "determinerCode", required = true)
                            protected String determinerCode;

                            /**
                             * Gets the value of the id property.
                             * 
                             * <p>
                             * This accessor method returns a reference to the live list,
                             * not a snapshot. Therefore any modification you make to the
                             * returned list will be present inside the JAXB object.
                             * This is why there is not a <CODE>set</CODE> method for the id property.
                             * 
                             * <p>
                             * For example, to add a new item, do as follows:
                             * <pre>
                             *    getId().add(newItem);
                             * </pre>
                             * 
                             * 
                             * <p>
                             * Objects of the following type(s) are allowed in the list
                             * {@link PRPAIN201306UV02 .ControlActProcess.Subject.RegistrationEvent.Subject1 .Patient.ProviderOrganization.Id }
                             * 
                             * 
                             */
                            public List<PRPAIN201306UV02 .ControlActProcess.Subject.RegistrationEvent.Subject1 .Patient.ProviderOrganization.Id> getId() {
                                if (id == null) {
                                    id = new ArrayList<PRPAIN201306UV02 .ControlActProcess.Subject.RegistrationEvent.Subject1 .Patient.ProviderOrganization.Id>();
                                }
                                return this.id;
                            }

                            /**
                             * Obtém o valor da propriedade contactParty.
                             * 
                             * @return
                             *     possible object is
                             *     {@link PRPAIN201306UV02 .ControlActProcess.Subject.RegistrationEvent.Subject1 .Patient.ProviderOrganization.ContactParty }
                             *     
                             */
                            public PRPAIN201306UV02 .ControlActProcess.Subject.RegistrationEvent.Subject1 .Patient.ProviderOrganization.ContactParty getContactParty() {
                                return contactParty;
                            }

                            /**
                             * Define o valor da propriedade contactParty.
                             * 
                             * @param value
                             *     allowed object is
                             *     {@link PRPAIN201306UV02 .ControlActProcess.Subject.RegistrationEvent.Subject1 .Patient.ProviderOrganization.ContactParty }
                             *     
                             */
                            public void setContactParty(PRPAIN201306UV02 .ControlActProcess.Subject.RegistrationEvent.Subject1 .Patient.ProviderOrganization.ContactParty value) {
                                this.contactParty = value;
                            }

                            /**
                             * Obtém o valor da propriedade classCode.
                             * 
                             * @return
                             *     possible object is
                             *     {@link String }
                             *     
                             */
                            public String getClassCode() {
                                return classCode;
                            }

                            /**
                             * Define o valor da propriedade classCode.
                             * 
                             * @param value
                             *     allowed object is
                             *     {@link String }
                             *     
                             */
                            public void setClassCode(String value) {
                                this.classCode = value;
                            }

                            /**
                             * Obtém o valor da propriedade determinerCode.
                             * 
                             * @return
                             *     possible object is
                             *     {@link String }
                             *     
                             */
                            public String getDeterminerCode() {
                                return determinerCode;
                            }

                            /**
                             * Define o valor da propriedade determinerCode.
                             * 
                             * @param value
                             *     allowed object is
                             *     {@link String }
                             *     
                             */
                            public void setDeterminerCode(String value) {
                                this.determinerCode = value;
                            }


                            /**
                             * <p>Classe Java de anonymous complex type.
                             * 
                             * <p>O seguinte fragmento do esquema especifica o conteúdo esperado contido dentro desta classe.
                             * 
                             * <pre>
                             * &lt;complexType>
                             *   &lt;complexContent>
                             *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
                             *       &lt;attribute name="classCode" use="required" type="{http://www.w3.org/2001/XMLSchema}string" />
                             *     &lt;/restriction>
                             *   &lt;/complexContent>
                             * &lt;/complexType>
                             * </pre>
                             * 
                             * 
                             */
                            @XmlAccessorType(XmlAccessType.FIELD)
                            @XmlType(name = "")
                            public static class ContactParty {

                                @XmlAttribute(name = "classCode", required = true)
                                protected String classCode;

                                /**
                                 * Obtém o valor da propriedade classCode.
                                 * 
                                 * @return
                                 *     possible object is
                                 *     {@link String }
                                 *     
                                 */
                                public String getClassCode() {
                                    return classCode;
                                }

                                /**
                                 * Define o valor da propriedade classCode.
                                 * 
                                 * @param value
                                 *     allowed object is
                                 *     {@link String }
                                 *     
                                 */
                                public void setClassCode(String value) {
                                    this.classCode = value;
                                }

                            }


                            /**
                             * <p>Classe Java de anonymous complex type.
                             * 
                             * <p>O seguinte fragmento do esquema especifica o conteúdo esperado contido dentro desta classe.
                             * 
                             * <pre>
                             * &lt;complexType>
                             *   &lt;complexContent>
                             *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
                             *       &lt;attribute name="root" use="required" type="{http://www.w3.org/2001/XMLSchema}string" />
                             *     &lt;/restriction>
                             *   &lt;/complexContent>
                             * &lt;/complexType>
                             * </pre>
                             * 
                             * 
                             */
                            @XmlAccessorType(XmlAccessType.FIELD)
                            @XmlType(name = "")
                            public static class Id {

                                @XmlAttribute(name = "root", required = true)
                                protected String root;

                                /**
                                 * Obtém o valor da propriedade root.
                                 * 
                                 * @return
                                 *     possible object is
                                 *     {@link String }
                                 *     
                                 */
                                public String getRoot() {
                                    return root;
                                }

                                /**
                                 * Define o valor da propriedade root.
                                 * 
                                 * @param value
                                 *     allowed object is
                                 *     {@link String }
                                 *     
                                 */
                                public void setRoot(String value) {
                                    this.root = value;
                                }

                            }

                        }


                        /**
                         * <p>Classe Java de anonymous complex type.
                         * 
                         * <p>O seguinte fragmento do esquema especifica o conteúdo esperado contido dentro desta classe.
                         * 
                         * <pre>
                         * &lt;complexType>
                         *   &lt;complexContent>
                         *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
                         *       &lt;attribute name="code" use="required" type="{http://www.w3.org/2001/XMLSchema}string" />
                         *     &lt;/restriction>
                         *   &lt;/complexContent>
                         * &lt;/complexType>
                         * </pre>
                         * 
                         * 
                         */
                        @XmlAccessorType(XmlAccessType.FIELD)
                        @XmlType(name = "")
                        public static class StatusCode {

                            @XmlAttribute(name = "code", required = true)
                            protected String code;

                            /**
                             * Obtém o valor da propriedade code.
                             * 
                             * @return
                             *     possible object is
                             *     {@link String }
                             *     
                             */
                            public String getCode() {
                                return code;
                            }

                            /**
                             * Define o valor da propriedade code.
                             * 
                             * @param value
                             *     allowed object is
                             *     {@link String }
                             *     
                             */
                            public void setCode(String value) {
                                this.code = value;
                            }

                        }


                        /**
                         * <p>Classe Java de anonymous complex type.
                         * 
                         * <p>O seguinte fragmento do esquema especifica o conteúdo esperado contido dentro desta classe.
                         * 
                         * <pre>
                         * &lt;complexType>
                         *   &lt;complexContent>
                         *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
                         *       &lt;sequence>
                         *         &lt;element name="queryMatchObservation">
                         *           &lt;complexType>
                         *             &lt;complexContent>
                         *               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
                         *                 &lt;sequence>
                         *                   &lt;element name="code">
                         *                     &lt;complexType>
                         *                       &lt;complexContent>
                         *                         &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
                         *                           &lt;attribute name="code" use="required" type="{http://www.w3.org/2001/XMLSchema}string" />
                         *                         &lt;/restriction>
                         *                       &lt;/complexContent>
                         *                     &lt;/complexType>
                         *                   &lt;/element>
                         *                   &lt;element name="value">
                         *                     &lt;complexType>
                         *                       &lt;complexContent>
                         *                         &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
                         *                           &lt;attribute name="value" use="required" type="{http://www.w3.org/2001/XMLSchema}unsignedByte" />
                         *                         &lt;/restriction>
                         *                       &lt;/complexContent>
                         *                     &lt;/complexType>
                         *                   &lt;/element>
                         *                 &lt;/sequence>
                         *                 &lt;attribute name="classCode" use="required" type="{http://www.w3.org/2001/XMLSchema}string" />
                         *                 &lt;attribute name="moodCode" use="required" type="{http://www.w3.org/2001/XMLSchema}string" />
                         *               &lt;/restriction>
                         *             &lt;/complexContent>
                         *           &lt;/complexType>
                         *         &lt;/element>
                         *       &lt;/sequence>
                         *     &lt;/restriction>
                         *   &lt;/complexContent>
                         * &lt;/complexType>
                         * </pre>
                         * 
                         * 
                         */
                        @XmlAccessorType(XmlAccessType.FIELD)
                        @XmlType(name = "", propOrder = {
                            "queryMatchObservation"
                        })
                        public static class SubjectOf1 {

                            @XmlElement(required = true)
                            protected PRPAIN201306UV02 .ControlActProcess.Subject.RegistrationEvent.Subject1 .Patient.SubjectOf1 .QueryMatchObservation queryMatchObservation;

                            /**
                             * Obtém o valor da propriedade queryMatchObservation.
                             * 
                             * @return
                             *     possible object is
                             *     {@link PRPAIN201306UV02 .ControlActProcess.Subject.RegistrationEvent.Subject1 .Patient.SubjectOf1 .QueryMatchObservation }
                             *     
                             */
                            public PRPAIN201306UV02 .ControlActProcess.Subject.RegistrationEvent.Subject1 .Patient.SubjectOf1 .QueryMatchObservation getQueryMatchObservation() {
                                return queryMatchObservation;
                            }

                            /**
                             * Define o valor da propriedade queryMatchObservation.
                             * 
                             * @param value
                             *     allowed object is
                             *     {@link PRPAIN201306UV02 .ControlActProcess.Subject.RegistrationEvent.Subject1 .Patient.SubjectOf1 .QueryMatchObservation }
                             *     
                             */
                            public void setQueryMatchObservation(PRPAIN201306UV02 .ControlActProcess.Subject.RegistrationEvent.Subject1 .Patient.SubjectOf1 .QueryMatchObservation value) {
                                this.queryMatchObservation = value;
                            }


                            /**
                             * <p>Classe Java de anonymous complex type.
                             * 
                             * <p>O seguinte fragmento do esquema especifica o conteúdo esperado contido dentro desta classe.
                             * 
                             * <pre>
                             * &lt;complexType>
                             *   &lt;complexContent>
                             *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
                             *       &lt;sequence>
                             *         &lt;element name="code">
                             *           &lt;complexType>
                             *             &lt;complexContent>
                             *               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
                             *                 &lt;attribute name="code" use="required" type="{http://www.w3.org/2001/XMLSchema}string" />
                             *               &lt;/restriction>
                             *             &lt;/complexContent>
                             *           &lt;/complexType>
                             *         &lt;/element>
                             *         &lt;element name="value">
                             *           &lt;complexType>
                             *             &lt;complexContent>
                             *               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
                             *                 &lt;attribute name="value" use="required" type="{http://www.w3.org/2001/XMLSchema}unsignedByte" />
                             *               &lt;/restriction>
                             *             &lt;/complexContent>
                             *           &lt;/complexType>
                             *         &lt;/element>
                             *       &lt;/sequence>
                             *       &lt;attribute name="classCode" use="required" type="{http://www.w3.org/2001/XMLSchema}string" />
                             *       &lt;attribute name="moodCode" use="required" type="{http://www.w3.org/2001/XMLSchema}string" />
                             *     &lt;/restriction>
                             *   &lt;/complexContent>
                             * &lt;/complexType>
                             * </pre>
                             * 
                             * 
                             */
                            @XmlAccessorType(XmlAccessType.FIELD)
                            @XmlType(name = "", propOrder = {
                                "code",
                                "value"
                            })
                            public static class QueryMatchObservation {

                                @XmlElement(required = true)
                                protected PRPAIN201306UV02 .ControlActProcess.Subject.RegistrationEvent.Subject1 .Patient.SubjectOf1 .QueryMatchObservation.Code code;
                                @XmlElement(required = true)
                                protected PRPAIN201306UV02 .ControlActProcess.Subject.RegistrationEvent.Subject1 .Patient.SubjectOf1 .QueryMatchObservation.Value value;
                                @XmlAttribute(name = "classCode", required = true)
                                protected String classCode;
                                @XmlAttribute(name = "moodCode", required = true)
                                protected String moodCode;

                                /**
                                 * Obtém o valor da propriedade code.
                                 * 
                                 * @return
                                 *     possible object is
                                 *     {@link PRPAIN201306UV02 .ControlActProcess.Subject.RegistrationEvent.Subject1 .Patient.SubjectOf1 .QueryMatchObservation.Code }
                                 *     
                                 */
                                public PRPAIN201306UV02 .ControlActProcess.Subject.RegistrationEvent.Subject1 .Patient.SubjectOf1 .QueryMatchObservation.Code getCode() {
                                    return code;
                                }

                                /**
                                 * Define o valor da propriedade code.
                                 * 
                                 * @param value
                                 *     allowed object is
                                 *     {@link PRPAIN201306UV02 .ControlActProcess.Subject.RegistrationEvent.Subject1 .Patient.SubjectOf1 .QueryMatchObservation.Code }
                                 *     
                                 */
                                public void setCode(PRPAIN201306UV02 .ControlActProcess.Subject.RegistrationEvent.Subject1 .Patient.SubjectOf1 .QueryMatchObservation.Code value) {
                                    this.code = value;
                                }

                                /**
                                 * Obtém o valor da propriedade value.
                                 * 
                                 * @return
                                 *     possible object is
                                 *     {@link PRPAIN201306UV02 .ControlActProcess.Subject.RegistrationEvent.Subject1 .Patient.SubjectOf1 .QueryMatchObservation.Value }
                                 *     
                                 */
                                public PRPAIN201306UV02 .ControlActProcess.Subject.RegistrationEvent.Subject1 .Patient.SubjectOf1 .QueryMatchObservation.Value getValue() {
                                    return value;
                                }

                                /**
                                 * Define o valor da propriedade value.
                                 * 
                                 * @param value
                                 *     allowed object is
                                 *     {@link PRPAIN201306UV02 .ControlActProcess.Subject.RegistrationEvent.Subject1 .Patient.SubjectOf1 .QueryMatchObservation.Value }
                                 *     
                                 */
                                public void setValue(PRPAIN201306UV02 .ControlActProcess.Subject.RegistrationEvent.Subject1 .Patient.SubjectOf1 .QueryMatchObservation.Value value) {
                                    this.value = value;
                                }

                                /**
                                 * Obtém o valor da propriedade classCode.
                                 * 
                                 * @return
                                 *     possible object is
                                 *     {@link String }
                                 *     
                                 */
                                public String getClassCode() {
                                    return classCode;
                                }

                                /**
                                 * Define o valor da propriedade classCode.
                                 * 
                                 * @param value
                                 *     allowed object is
                                 *     {@link String }
                                 *     
                                 */
                                public void setClassCode(String value) {
                                    this.classCode = value;
                                }

                                /**
                                 * Obtém o valor da propriedade moodCode.
                                 * 
                                 * @return
                                 *     possible object is
                                 *     {@link String }
                                 *     
                                 */
                                public String getMoodCode() {
                                    return moodCode;
                                }

                                /**
                                 * Define o valor da propriedade moodCode.
                                 * 
                                 * @param value
                                 *     allowed object is
                                 *     {@link String }
                                 *     
                                 */
                                public void setMoodCode(String value) {
                                    this.moodCode = value;
                                }


                                /**
                                 * <p>Classe Java de anonymous complex type.
                                 * 
                                 * <p>O seguinte fragmento do esquema especifica o conteúdo esperado contido dentro desta classe.
                                 * 
                                 * <pre>
                                 * &lt;complexType>
                                 *   &lt;complexContent>
                                 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
                                 *       &lt;attribute name="code" use="required" type="{http://www.w3.org/2001/XMLSchema}string" />
                                 *     &lt;/restriction>
                                 *   &lt;/complexContent>
                                 * &lt;/complexType>
                                 * </pre>
                                 * 
                                 * 
                                 */
                                @XmlAccessorType(XmlAccessType.FIELD)
                                @XmlType(name = "")
                                public static class Code {

                                    @XmlAttribute(name = "code", required = true)
                                    protected String code;

                                    /**
                                     * Obtém o valor da propriedade code.
                                     * 
                                     * @return
                                     *     possible object is
                                     *     {@link String }
                                     *     
                                     */
                                    public String getCode() {
                                        return code;
                                    }

                                    /**
                                     * Define o valor da propriedade code.
                                     * 
                                     * @param value
                                     *     allowed object is
                                     *     {@link String }
                                     *     
                                     */
                                    public void setCode(String value) {
                                        this.code = value;
                                    }

                                }


                                /**
                                 * <p>Classe Java de anonymous complex type.
                                 * 
                                 * <p>O seguinte fragmento do esquema especifica o conteúdo esperado contido dentro desta classe.
                                 * 
                                 * <pre>
                                 * &lt;complexType>
                                 *   &lt;complexContent>
                                 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
                                 *       &lt;attribute name="value" use="required" type="{http://www.w3.org/2001/XMLSchema}unsignedByte" />
                                 *     &lt;/restriction>
                                 *   &lt;/complexContent>
                                 * &lt;/complexType>
                                 * </pre>
                                 * 
                                 * 
                                 */
                                @XmlAccessorType(XmlAccessType.FIELD)
                                @XmlType(name = "")
                                public static class Value {

                                    @XmlAttribute(name = "value", required = true)
                                    @XmlSchemaType(name = "unsignedByte")
                                    protected short value;

                                    /**
                                     * Obtém o valor da propriedade value.
                                     * 
                                     */
                                    public short getValue() {
                                        return value;
                                    }

                                    /**
                                     * Define o valor da propriedade value.
                                     * 
                                     */
                                    public void setValue(short value) {
                                        this.value = value;
                                    }

                                }

                            }

                        }

                    }


                    /**
                     * <p>Classe Java de anonymous complex type.
                     * 
                     * <p>O seguinte fragmento do esquema especifica o conteúdo esperado contido dentro desta classe.
                     * 
                     * <pre>
                     * &lt;complexType>
                     *   &lt;complexContent>
                     *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
                     *       &lt;attribute name="code" use="required" type="{http://www.w3.org/2001/XMLSchema}string" />
                     *     &lt;/restriction>
                     *   &lt;/complexContent>
                     * &lt;/complexType>
                     * </pre>
                     * 
                     * 
                     */
                    @XmlAccessorType(XmlAccessType.FIELD)
                    @XmlType(name = "")
                    public static class RealmCode {

                        @XmlAttribute(name = "code", required = true)
                        protected String code;

                        /**
                         * Obtém o valor da propriedade code.
                         * 
                         * @return
                         *     possible object is
                         *     {@link String }
                         *     
                         */
                        public String getCode() {
                            return code;
                        }

                        /**
                         * Define o valor da propriedade code.
                         * 
                         * @param value
                         *     allowed object is
                         *     {@link String }
                         *     
                         */
                        public void setCode(String value) {
                            this.code = value;
                        }

                    }

                }

            }

        }

    }


    /**
     * <p>Classe Java de anonymous complex type.
     * 
     * <p>O seguinte fragmento do esquema especifica o conteúdo esperado contido dentro desta classe.
     * 
     * <pre>
     * &lt;complexType>
     *   &lt;complexContent>
     *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
     *       &lt;attribute name="value" use="required" type="{http://www.w3.org/2001/XMLSchema}unsignedLong" />
     *     &lt;/restriction>
     *   &lt;/complexContent>
     * &lt;/complexType>
     * </pre>
     * 
     * 
     */
    @XmlAccessorType(XmlAccessType.FIELD)
    @XmlType(name = "")
    public static class CreationTime {

        @XmlAttribute(name = "value", required = true)
        @XmlSchemaType(name = "unsignedLong")
        protected BigInteger value;

        /**
         * Obtém o valor da propriedade value.
         * 
         * @return
         *     possible object is
         *     {@link BigInteger }
         *     
         */
        public BigInteger getValue() {
            return value;
        }

        /**
         * Define o valor da propriedade value.
         * 
         * @param value
         *     allowed object is
         *     {@link BigInteger }
         *     
         */
        public void setValue(BigInteger value) {
            this.value = value;
        }

    }


    /**
     * <p>Classe Java de anonymous complex type.
     * 
     * <p>O seguinte fragmento do esquema especifica o conteúdo esperado contido dentro desta classe.
     * 
     * <pre>
     * &lt;complexType>
     *   &lt;complexContent>
     *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
     *       &lt;attribute name="root" use="required" type="{http://www.w3.org/2001/XMLSchema}string" />
     *     &lt;/restriction>
     *   &lt;/complexContent>
     * &lt;/complexType>
     * </pre>
     * 
     * 
     */
    @XmlAccessorType(XmlAccessType.FIELD)
    @XmlType(name = "")
    public static class Id {

        @XmlAttribute(name = "root", required = true)
        protected String root;

        /**
         * Obtém o valor da propriedade root.
         * 
         * @return
         *     possible object is
         *     {@link String }
         *     
         */
        public String getRoot() {
            return root;
        }

        /**
         * Define o valor da propriedade root.
         * 
         * @param value
         *     allowed object is
         *     {@link String }
         *     
         */
        public void setRoot(String value) {
            this.root = value;
        }

    }


    /**
     * <p>Classe Java de anonymous complex type.
     * 
     * <p>O seguinte fragmento do esquema especifica o conteúdo esperado contido dentro desta classe.
     * 
     * <pre>
     * &lt;complexType>
     *   &lt;complexContent>
     *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
     *       &lt;attribute name="root" use="required" type="{http://www.w3.org/2001/XMLSchema}string" />
     *       &lt;attribute name="extension" use="required" type="{http://www.w3.org/2001/XMLSchema}string" />
     *     &lt;/restriction>
     *   &lt;/complexContent>
     * &lt;/complexType>
     * </pre>
     * 
     * 
     */
    @XmlAccessorType(XmlAccessType.FIELD)
    @XmlType(name = "")
    public static class InteractionId {

        @XmlAttribute(name = "root", required = true)
        protected String root;
        @XmlAttribute(name = "extension", required = true)
        protected String extension;

        /**
         * Obtém o valor da propriedade root.
         * 
         * @return
         *     possible object is
         *     {@link String }
         *     
         */
        public String getRoot() {
            return root;
        }

        /**
         * Define o valor da propriedade root.
         * 
         * @param value
         *     allowed object is
         *     {@link String }
         *     
         */
        public void setRoot(String value) {
            this.root = value;
        }

        /**
         * Obtém o valor da propriedade extension.
         * 
         * @return
         *     possible object is
         *     {@link String }
         *     
         */
        public String getExtension() {
            return extension;
        }

        /**
         * Define o valor da propriedade extension.
         * 
         * @param value
         *     allowed object is
         *     {@link String }
         *     
         */
        public void setExtension(String value) {
            this.extension = value;
        }

    }


    /**
     * <p>Classe Java de anonymous complex type.
     * 
     * <p>O seguinte fragmento do esquema especifica o conteúdo esperado contido dentro desta classe.
     * 
     * <pre>
     * &lt;complexType>
     *   &lt;complexContent>
     *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
     *       &lt;attribute name="code" use="required" type="{http://www.w3.org/2001/XMLSchema}string" />
     *     &lt;/restriction>
     *   &lt;/complexContent>
     * &lt;/complexType>
     * </pre>
     * 
     * 
     */
    @XmlAccessorType(XmlAccessType.FIELD)
    @XmlType(name = "")
    public static class ProcessingCode {

        @XmlAttribute(name = "code", required = true)
        protected String code;

        /**
         * Obtém o valor da propriedade code.
         * 
         * @return
         *     possible object is
         *     {@link String }
         *     
         */
        public String getCode() {
            return code;
        }

        /**
         * Define o valor da propriedade code.
         * 
         * @param value
         *     allowed object is
         *     {@link String }
         *     
         */
        public void setCode(String value) {
            this.code = value;
        }

    }


    /**
     * <p>Classe Java de anonymous complex type.
     * 
     * <p>O seguinte fragmento do esquema especifica o conteúdo esperado contido dentro desta classe.
     * 
     * <pre>
     * &lt;complexType>
     *   &lt;complexContent>
     *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
     *       &lt;attribute name="code" use="required" type="{http://www.w3.org/2001/XMLSchema}string" />
     *     &lt;/restriction>
     *   &lt;/complexContent>
     * &lt;/complexType>
     * </pre>
     * 
     * 
     */
    @XmlAccessorType(XmlAccessType.FIELD)
    @XmlType(name = "")
    public static class ProcessingModeCode {

        @XmlAttribute(name = "code", required = true)
        protected String code;

        /**
         * Obtém o valor da propriedade code.
         * 
         * @return
         *     possible object is
         *     {@link String }
         *     
         */
        public String getCode() {
            return code;
        }

        /**
         * Define o valor da propriedade code.
         * 
         * @param value
         *     allowed object is
         *     {@link String }
         *     
         */
        public void setCode(String value) {
            this.code = value;
        }

    }


    /**
     * <p>Classe Java de anonymous complex type.
     * 
     * <p>O seguinte fragmento do esquema especifica o conteúdo esperado contido dentro desta classe.
     * 
     * <pre>
     * &lt;complexType>
     *   &lt;complexContent>
     *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
     *       &lt;sequence>
     *         &lt;element name="device">
     *           &lt;complexType>
     *             &lt;complexContent>
     *               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
     *                 &lt;sequence>
     *                   &lt;element name="id">
     *                     &lt;complexType>
     *                       &lt;complexContent>
     *                         &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
     *                           &lt;attribute name="root" use="required" type="{http://www.w3.org/2001/XMLSchema}string" />
     *                         &lt;/restriction>
     *                       &lt;/complexContent>
     *                     &lt;/complexType>
     *                   &lt;/element>
     *                   &lt;element name="name" type="{http://www.w3.org/2001/XMLSchema}string"/>
     *                 &lt;/sequence>
     *                 &lt;attribute name="classCode" use="required" type="{http://www.w3.org/2001/XMLSchema}string" />
     *                 &lt;attribute name="determinerCode" use="required" type="{http://www.w3.org/2001/XMLSchema}string" />
     *               &lt;/restriction>
     *             &lt;/complexContent>
     *           &lt;/complexType>
     *         &lt;/element>
     *       &lt;/sequence>
     *       &lt;attribute name="typeCode" use="required" type="{http://www.w3.org/2001/XMLSchema}string" />
     *     &lt;/restriction>
     *   &lt;/complexContent>
     * &lt;/complexType>
     * </pre>
     * 
     * 
     */
    @XmlAccessorType(XmlAccessType.FIELD)
    @XmlType(name = "", propOrder = {
        "device"
    })
    public static class Receiver {

        @XmlElement(required = true)
        protected PRPAIN201306UV02 .Receiver.Device device;
        @XmlAttribute(name = "typeCode", required = true)
        protected String typeCode;

        /**
         * Obtém o valor da propriedade device.
         * 
         * @return
         *     possible object is
         *     {@link PRPAIN201306UV02 .Receiver.Device }
         *     
         */
        public PRPAIN201306UV02 .Receiver.Device getDevice() {
            return device;
        }

        /**
         * Define o valor da propriedade device.
         * 
         * @param value
         *     allowed object is
         *     {@link PRPAIN201306UV02 .Receiver.Device }
         *     
         */
        public void setDevice(PRPAIN201306UV02 .Receiver.Device value) {
            this.device = value;
        }

        /**
         * Obtém o valor da propriedade typeCode.
         * 
         * @return
         *     possible object is
         *     {@link String }
         *     
         */
        public String getTypeCode() {
            return typeCode;
        }

        /**
         * Define o valor da propriedade typeCode.
         * 
         * @param value
         *     allowed object is
         *     {@link String }
         *     
         */
        public void setTypeCode(String value) {
            this.typeCode = value;
        }


        /**
         * <p>Classe Java de anonymous complex type.
         * 
         * <p>O seguinte fragmento do esquema especifica o conteúdo esperado contido dentro desta classe.
         * 
         * <pre>
         * &lt;complexType>
         *   &lt;complexContent>
         *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
         *       &lt;sequence>
         *         &lt;element name="id">
         *           &lt;complexType>
         *             &lt;complexContent>
         *               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
         *                 &lt;attribute name="root" use="required" type="{http://www.w3.org/2001/XMLSchema}string" />
         *               &lt;/restriction>
         *             &lt;/complexContent>
         *           &lt;/complexType>
         *         &lt;/element>
         *         &lt;element name="name" type="{http://www.w3.org/2001/XMLSchema}string"/>
         *       &lt;/sequence>
         *       &lt;attribute name="classCode" use="required" type="{http://www.w3.org/2001/XMLSchema}string" />
         *       &lt;attribute name="determinerCode" use="required" type="{http://www.w3.org/2001/XMLSchema}string" />
         *     &lt;/restriction>
         *   &lt;/complexContent>
         * &lt;/complexType>
         * </pre>
         * 
         * 
         */
        @XmlAccessorType(XmlAccessType.FIELD)
        @XmlType(name = "", propOrder = {
            "id",
            "name"
        })
        public static class Device {

            @XmlElement(required = true)
            protected PRPAIN201306UV02 .Receiver.Device.Id id;
            @XmlElement(required = true)
            protected String name;
            @XmlAttribute(name = "classCode", required = true)
            protected String classCode;
            @XmlAttribute(name = "determinerCode", required = true)
            protected String determinerCode;

            /**
             * Obtém o valor da propriedade id.
             * 
             * @return
             *     possible object is
             *     {@link PRPAIN201306UV02 .Receiver.Device.Id }
             *     
             */
            public PRPAIN201306UV02 .Receiver.Device.Id getId() {
                return id;
            }

            /**
             * Define o valor da propriedade id.
             * 
             * @param value
             *     allowed object is
             *     {@link PRPAIN201306UV02 .Receiver.Device.Id }
             *     
             */
            public void setId(PRPAIN201306UV02 .Receiver.Device.Id value) {
                this.id = value;
            }

            /**
             * Obtém o valor da propriedade name.
             * 
             * @return
             *     possible object is
             *     {@link String }
             *     
             */
            public String getName() {
                return name;
            }

            /**
             * Define o valor da propriedade name.
             * 
             * @param value
             *     allowed object is
             *     {@link String }
             *     
             */
            public void setName(String value) {
                this.name = value;
            }

            /**
             * Obtém o valor da propriedade classCode.
             * 
             * @return
             *     possible object is
             *     {@link String }
             *     
             */
            public String getClassCode() {
                return classCode;
            }

            /**
             * Define o valor da propriedade classCode.
             * 
             * @param value
             *     allowed object is
             *     {@link String }
             *     
             */
            public void setClassCode(String value) {
                this.classCode = value;
            }

            /**
             * Obtém o valor da propriedade determinerCode.
             * 
             * @return
             *     possible object is
             *     {@link String }
             *     
             */
            public String getDeterminerCode() {
                return determinerCode;
            }

            /**
             * Define o valor da propriedade determinerCode.
             * 
             * @param value
             *     allowed object is
             *     {@link String }
             *     
             */
            public void setDeterminerCode(String value) {
                this.determinerCode = value;
            }


            /**
             * <p>Classe Java de anonymous complex type.
             * 
             * <p>O seguinte fragmento do esquema especifica o conteúdo esperado contido dentro desta classe.
             * 
             * <pre>
             * &lt;complexType>
             *   &lt;complexContent>
             *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
             *       &lt;attribute name="root" use="required" type="{http://www.w3.org/2001/XMLSchema}string" />
             *     &lt;/restriction>
             *   &lt;/complexContent>
             * &lt;/complexType>
             * </pre>
             * 
             * 
             */
            @XmlAccessorType(XmlAccessType.FIELD)
            @XmlType(name = "")
            public static class Id {

                @XmlAttribute(name = "root", required = true)
                protected String root;

                /**
                 * Obtém o valor da propriedade root.
                 * 
                 * @return
                 *     possible object is
                 *     {@link String }
                 *     
                 */
                public String getRoot() {
                    return root;
                }

                /**
                 * Define o valor da propriedade root.
                 * 
                 * @param value
                 *     allowed object is
                 *     {@link String }
                 *     
                 */
                public void setRoot(String value) {
                    this.root = value;
                }

            }

        }

    }


    /**
     * <p>Classe Java de anonymous complex type.
     * 
     * <p>O seguinte fragmento do esquema especifica o conteúdo esperado contido dentro desta classe.
     * 
     * <pre>
     * &lt;complexType>
     *   &lt;complexContent>
     *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
     *       &lt;sequence>
     *         &lt;element name="device">
     *           &lt;complexType>
     *             &lt;complexContent>
     *               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
     *                 &lt;sequence>
     *                   &lt;element name="id">
     *                     &lt;complexType>
     *                       &lt;complexContent>
     *                         &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
     *                           &lt;attribute name="root" use="required" type="{http://www.w3.org/2001/XMLSchema}string" />
     *                         &lt;/restriction>
     *                       &lt;/complexContent>
     *                     &lt;/complexType>
     *                   &lt;/element>
     *                 &lt;/sequence>
     *                 &lt;attribute name="classCode" use="required" type="{http://www.w3.org/2001/XMLSchema}string" />
     *                 &lt;attribute name="determinerCode" use="required" type="{http://www.w3.org/2001/XMLSchema}string" />
     *               &lt;/restriction>
     *             &lt;/complexContent>
     *           &lt;/complexType>
     *         &lt;/element>
     *       &lt;/sequence>
     *       &lt;attribute name="typeCode" use="required" type="{http://www.w3.org/2001/XMLSchema}string" />
     *     &lt;/restriction>
     *   &lt;/complexContent>
     * &lt;/complexType>
     * </pre>
     * 
     * 
     */
    @XmlAccessorType(XmlAccessType.FIELD)
    @XmlType(name = "", propOrder = {
        "device"
    })
    public static class Sender {

        @XmlElement(required = true)
        protected PRPAIN201306UV02 .Sender.Device device;
        @XmlAttribute(name = "typeCode", required = true)
        protected String typeCode;

        /**
         * Obtém o valor da propriedade device.
         * 
         * @return
         *     possible object is
         *     {@link PRPAIN201306UV02 .Sender.Device }
         *     
         */
        public PRPAIN201306UV02 .Sender.Device getDevice() {
            return device;
        }

        /**
         * Define o valor da propriedade device.
         * 
         * @param value
         *     allowed object is
         *     {@link PRPAIN201306UV02 .Sender.Device }
         *     
         */
        public void setDevice(PRPAIN201306UV02 .Sender.Device value) {
            this.device = value;
        }

        /**
         * Obtém o valor da propriedade typeCode.
         * 
         * @return
         *     possible object is
         *     {@link String }
         *     
         */
        public String getTypeCode() {
            return typeCode;
        }

        /**
         * Define o valor da propriedade typeCode.
         * 
         * @param value
         *     allowed object is
         *     {@link String }
         *     
         */
        public void setTypeCode(String value) {
            this.typeCode = value;
        }


        /**
         * <p>Classe Java de anonymous complex type.
         * 
         * <p>O seguinte fragmento do esquema especifica o conteúdo esperado contido dentro desta classe.
         * 
         * <pre>
         * &lt;complexType>
         *   &lt;complexContent>
         *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
         *       &lt;sequence>
         *         &lt;element name="id">
         *           &lt;complexType>
         *             &lt;complexContent>
         *               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
         *                 &lt;attribute name="root" use="required" type="{http://www.w3.org/2001/XMLSchema}string" />
         *               &lt;/restriction>
         *             &lt;/complexContent>
         *           &lt;/complexType>
         *         &lt;/element>
         *       &lt;/sequence>
         *       &lt;attribute name="classCode" use="required" type="{http://www.w3.org/2001/XMLSchema}string" />
         *       &lt;attribute name="determinerCode" use="required" type="{http://www.w3.org/2001/XMLSchema}string" />
         *     &lt;/restriction>
         *   &lt;/complexContent>
         * &lt;/complexType>
         * </pre>
         * 
         * 
         */
        @XmlAccessorType(XmlAccessType.FIELD)
        @XmlType(name = "", propOrder = {
            "id"
        })
        public static class Device {

            @XmlElement(required = true)
            protected PRPAIN201306UV02 .Sender.Device.Id id;
            @XmlAttribute(name = "classCode", required = true)
            protected String classCode;
            @XmlAttribute(name = "determinerCode", required = true)
            protected String determinerCode;

            /**
             * Obtém o valor da propriedade id.
             * 
             * @return
             *     possible object is
             *     {@link PRPAIN201306UV02 .Sender.Device.Id }
             *     
             */
            public PRPAIN201306UV02 .Sender.Device.Id getId() {
                return id;
            }

            /**
             * Define o valor da propriedade id.
             * 
             * @param value
             *     allowed object is
             *     {@link PRPAIN201306UV02 .Sender.Device.Id }
             *     
             */
            public void setId(PRPAIN201306UV02 .Sender.Device.Id value) {
                this.id = value;
            }

            /**
             * Obtém o valor da propriedade classCode.
             * 
             * @return
             *     possible object is
             *     {@link String }
             *     
             */
            public String getClassCode() {
                return classCode;
            }

            /**
             * Define o valor da propriedade classCode.
             * 
             * @param value
             *     allowed object is
             *     {@link String }
             *     
             */
            public void setClassCode(String value) {
                this.classCode = value;
            }

            /**
             * Obtém o valor da propriedade determinerCode.
             * 
             * @return
             *     possible object is
             *     {@link String }
             *     
             */
            public String getDeterminerCode() {
                return determinerCode;
            }

            /**
             * Define o valor da propriedade determinerCode.
             * 
             * @param value
             *     allowed object is
             *     {@link String }
             *     
             */
            public void setDeterminerCode(String value) {
                this.determinerCode = value;
            }


            /**
             * <p>Classe Java de anonymous complex type.
             * 
             * <p>O seguinte fragmento do esquema especifica o conteúdo esperado contido dentro desta classe.
             * 
             * <pre>
             * &lt;complexType>
             *   &lt;complexContent>
             *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
             *       &lt;attribute name="root" use="required" type="{http://www.w3.org/2001/XMLSchema}string" />
             *     &lt;/restriction>
             *   &lt;/complexContent>
             * &lt;/complexType>
             * </pre>
             * 
             * 
             */
            @XmlAccessorType(XmlAccessType.FIELD)
            @XmlType(name = "")
            public static class Id {

                @XmlAttribute(name = "root", required = true)
                protected String root;

                /**
                 * Obtém o valor da propriedade root.
                 * 
                 * @return
                 *     possible object is
                 *     {@link String }
                 *     
                 */
                public String getRoot() {
                    return root;
                }

                /**
                 * Define o valor da propriedade root.
                 * 
                 * @param value
                 *     allowed object is
                 *     {@link String }
                 *     
                 */
                public void setRoot(String value) {
                    this.root = value;
                }

            }

        }

    }

}
