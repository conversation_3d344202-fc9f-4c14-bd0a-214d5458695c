package br.com.alice.nullvs.consumers.company

import br.com.alice.business.events.CompanyContractCreatedEvent
import br.com.alice.business.events.CompanyContractUpdatedEvent
import br.com.alice.common.RangeUUID
import br.com.alice.common.data.dsl.matchers.ResultAssert
import br.com.alice.common.featureflag.withFeatureFlag
import br.com.alice.common.helpers.coVerifyNone
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.common.helpers.returns
import br.com.alice.common.kafka.exceptions.AutoRetryableException
import br.com.alice.common.kafka.internals.LocalProducer
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.ExternalModelType
import br.com.alice.data.layer.models.FeatureNamespace
import br.com.alice.data.layer.models.InternalModelType
import br.com.alice.data.layer.models.NullvsIntegrationLog
import br.com.alice.nullvs.common.NullvsActionType
import br.com.alice.nullvs.consumers.ConsumerTest
import br.com.alice.nullvs.events.NullvsCompanyContractActivatedEvent
import br.com.alice.nullvs.events.NullvsCompanyContractBatchRequestEvent
import br.com.alice.nullvs.events.NullvsSyncCompanyContractRequestEvent
import br.com.alice.nullvs.exceptions.ContractDoesNotHaveStartAtDate
import br.com.alice.nullvs.exceptions.DependsOnModelException
import br.com.alice.nullvs.exceptions.ExternalClientNotFound
import br.com.alice.nullvs.models.Meta
import br.com.alice.nullvs.models.company.CompanyContractData
import br.com.alice.nullvs.models.company.ContractType
import br.com.alice.nullvs.models.company.NullvsCompanyContractBatchRequest
import br.com.alice.nullvs.services.internals.TotvsCompanyIntegrationService
import br.com.alice.nullvs.services.internals.dependency.NullvsDependencyService
import com.github.kittinunf.result.failure
import io.mockk.coEvery
import io.mockk.mockk
import kotlinx.coroutines.runBlocking
import org.assertj.core.api.Assertions
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test
import java.time.LocalDate
import kotlin.test.BeforeTest

class CompanyContractConsumerTest : ConsumerTest() {
    private val totvsCompanyIntegrationService: TotvsCompanyIntegrationService = mockk()
    private val nullvsDependencyService: NullvsDependencyService = mockk()

    private val consumer =
        CompanyContractConsumer(totvsCompanyIntegrationService, LocalProducer, nullvsDependencyService)

    @BeforeTest
    fun setup() {
        super.before()
        LocalProducer.clearMessages()
    }

    @Nested
    inner class CompanyContractCreated {

        val contract = TestModelFactory.buildCompanyContract(startedAt = LocalDate.now())
        val event = CompanyContractCreatedEvent(contract)
        private val meta = Meta(
            eventId = event.messageId,
            eventName = event.name,
            internalModelName = InternalModelType.CONTRACT,
            internalId = contract.id,
            externalModelName = ExternalModelType.CONTRACT,
            integratedAt = event.eventDate,
            originalTopic = CompanyContractCreatedEvent.name,
            integrationEventName = NullvsCompanyContractBatchRequestEvent.name,
        )

        val request = NullvsCompanyContractBatchRequest(
            meta = meta,
            action = NullvsActionType.CREATE,
            contractData = CompanyContractData(
                number = null,
                isBillingLevel = contract.isBillingLevel,
                codeClient = "0001",
                dueDate = contract.dueDate,
                id = contract.id,
                groupCompany = "0001",
                contractType = ContractType.BUSINESS,
                isProRata = true,
                nature = "",
                startedAt = contract.startedAt!!,
            )
        )

        @Test
        fun `#should produce NullvsCompanyContractBatchRequestEvent`() = runBlocking {
            coEvery { totvsCompanyIntegrationService.createContract(meta, contract) } returns request

            val result = consumer.syncContractCreated(event)

            ResultAssert.assertThat(result).isSuccessWithData(true)

            Assertions.assertThat(LocalProducer.hasEvent(NullvsCompanyContractBatchRequestEvent.name)).isTrue

            coVerifyOnce { totvsCompanyIntegrationService.createContract(meta, contract) }
        }

        @Test
        fun `should return success when ExternalClientNotFound exception is thrown`() = runBlocking {
            coEvery {
                totvsCompanyIntegrationService.createContract(
                    meta,
                    contract
                )
            } returns ExternalClientNotFound("").failure()

            val result = consumer.syncContractCreated(event)

            ResultAssert.assertThat(result).isSuccess()

            Assertions.assertThat(LocalProducer.hasEvent(NullvsCompanyContractBatchRequestEvent.name)).isFalse

            coVerifyOnce { totvsCompanyIntegrationService.createContract(meta, contract) }
        }

        @Test
        fun `should return success when DependsOnModelException exception is thrown`() = runBlocking {
            coEvery {
                totvsCompanyIntegrationService.createContract(
                    meta,
                    contract
                )
            } returns DependsOnModelException(contract.id, InternalModelType.CONTRACT).failure()

            val result = consumer.syncContractCreated(event)

            ResultAssert.assertThat(result).isSuccess()

            Assertions.assertThat(LocalProducer.hasEvent(NullvsCompanyContractBatchRequestEvent.name)).isFalse

            coVerifyOnce { totvsCompanyIntegrationService.createContract(meta, contract) }
        }
    }

    @Nested
    inner class CompanyContractUpdated {

        val contract = TestModelFactory.buildCompanyContract(startedAt = LocalDate.now())
        val event = CompanyContractUpdatedEvent(contract)
        private val meta = Meta(
            eventId = event.messageId,
            eventName = event.name,
            internalModelName = InternalModelType.CONTRACT,
            internalId = contract.id,
            externalModelName = ExternalModelType.CONTRACT,
            integratedAt = event.eventDate,
            originalTopic = CompanyContractUpdatedEvent.name,
            integrationEventName = NullvsCompanyContractBatchRequestEvent.name,
        )

        val request = NullvsCompanyContractBatchRequest(
            meta = meta,
            action = NullvsActionType.CREATE,
            contractData = CompanyContractData(
                number = null,
                isBillingLevel = contract.isBillingLevel,
                codeClient = "0001",
                dueDate = contract.dueDate,
                id = contract.id,
                groupCompany = "0001",
                contractType = ContractType.BUSINESS,
                isProRata = true,
                nature = "",
                startedAt = contract.startedAt!!,
            )
        )

        @Test
        fun `#should produce NullvsCompanyContractBatchRequestEvent`() = runBlocking {
            coEvery { totvsCompanyIntegrationService.updateContract(meta, contract) } returns request

            val result = consumer.syncContractUpdated(event)

            ResultAssert.assertThat(result).isSuccessWithData(true)

            Assertions.assertThat(LocalProducer.hasEvent(NullvsCompanyContractBatchRequestEvent.name)).isTrue

            coVerifyOnce { totvsCompanyIntegrationService.updateContract(meta, contract) }
        }

        @Test
        fun `should return success when ExternalClientNotFound exception is thrown`() = runBlocking {
            coEvery {
                totvsCompanyIntegrationService.updateContract(
                    meta,
                    contract
                )
            } returns ExternalClientNotFound("").failure()

            val result = consumer.syncContractUpdated(event)

            ResultAssert.assertThat(result).isSuccess()

            Assertions.assertThat(LocalProducer.hasEvent(NullvsCompanyContractBatchRequestEvent.name)).isFalse

            coVerifyOnce { totvsCompanyIntegrationService.updateContract(meta, contract) }
        }

        @Test
        fun `should return success when DependsOnModelException exception is thrown`() = runBlocking {
            coEvery {
                totvsCompanyIntegrationService.updateContract(
                    meta,
                    contract
                )
            } returns DependsOnModelException(contract.id, InternalModelType.CONTRACT).failure()

            val result = consumer.syncContractUpdated(event)

            ResultAssert.assertThat(result).isSuccess()

            Assertions.assertThat(LocalProducer.hasEvent(NullvsCompanyContractBatchRequestEvent.name)).isFalse

            coVerifyOnce { totvsCompanyIntegrationService.updateContract(meta, contract) }
        }
    }

    @Nested
    inner class SyncContract {
        private val contract = TestModelFactory.buildCompanyContract(startedAt = LocalDate.now())
        private val event = NullvsSyncCompanyContractRequestEvent(contract, NullvsActionType.CREATE)
        private val meta = Meta(
            eventId = event.messageId,
            eventName = event.name,
            internalModelName = InternalModelType.CONTRACT,
            internalId = contract.id,
            externalModelName = ExternalModelType.CONTRACT,
            integratedAt = event.eventDate,
            originalTopic = "backfill-sync",
            integrationEventName = NullvsCompanyContractBatchRequestEvent.name,
        )

        val request = NullvsCompanyContractBatchRequest(
            meta = meta,
            action = NullvsActionType.CREATE,
            contractData = CompanyContractData(
                number = null,
                isBillingLevel = contract.isBillingLevel,
                codeClient = "0001",
                dueDate = contract.dueDate,
                id = contract.id,
                groupCompany = "0001",
                contractType = ContractType.BUSINESS,
                isProRata = true,
                nature = "",
                startedAt = contract.startedAt!!,
            )
        )

        @Test
        fun `#should return an exception when something is wrong`() = runBlocking {
            coEvery {
                totvsCompanyIntegrationService.createContract(
                    meta,
                    contract,
                )
            } returns Exception("")

            val result = consumer.syncContract(event)

            ResultAssert.assertThat(result).isFailureOfType(Exception::class)

            coVerifyOnce { totvsCompanyIntegrationService.createContract(meta, contract) }
        }

        @Test
        fun `#should return success when the exception is a bad request`() = runBlocking {
            coEvery {
                totvsCompanyIntegrationService.createContract(
                    meta,
                    contract,
                )
            } returns ContractDoesNotHaveStartAtDate("")

            val result = consumer.syncContract(event)

            ResultAssert.assertThat(result).isSuccess()

            coVerifyOnce { totvsCompanyIntegrationService.createContract(meta, contract) }
        }

        @Test
        fun `#should return success when the DependsOnModelException is thrown`() = runBlocking {
            coEvery {
                totvsCompanyIntegrationService.createContract(
                    meta,
                    contract,
                )
            } returns DependsOnModelException(contract.id, InternalModelType.CONTRACT)

            val result = consumer.syncContract(event)

            ResultAssert.assertThat(result).isSuccess()

            coVerifyOnce { totvsCompanyIntegrationService.createContract(meta, contract) }
        }

        @Test
        fun `#should return AutoRetryableException when DependsOnModelException is thrown and isFromDependencySystem is true`() =
            runBlocking {
                val event = NullvsSyncCompanyContractRequestEvent(
                    contract,
                    NullvsActionType.CREATE,
                    isFromDependencySystem = true
                )

                val meta = Meta(
                    eventId = event.messageId,
                    eventName = event.name,
                    internalModelName = InternalModelType.CONTRACT,
                    internalId = contract.id,
                    externalModelName = ExternalModelType.CONTRACT,
                    integratedAt = event.eventDate,
                    originalTopic = "backfill-sync",
                    integrationEventName = NullvsCompanyContractBatchRequestEvent.name,
                )

                coEvery {
                    totvsCompanyIntegrationService.createContract(
                        meta,
                        contract,
                    )
                } returns DependsOnModelException(contract.id, InternalModelType.CONTRACT)

                val result = consumer.syncContract(event)

                ResultAssert.assertThat(result).isFailureOfType(AutoRetryableException::class)

                coVerifyOnce { totvsCompanyIntegrationService.createContract(meta, contract) }
            }

        @Test
        fun `#should return an exception when action is invalid`() = runBlocking {
            val invalidEvent =
                NullvsSyncCompanyContractRequestEvent(contract, action = NullvsActionType.UPDATE_PAYMENT)
            val result = consumer.syncContract(invalidEvent)

            ResultAssert.assertThat(result).isFailureOfType(Exception::class)
        }

        @Test
        fun `#should produce NullvsSyncCompanyContractRequestEvent for create`() =
            runBlocking {
                coEvery {
                    totvsCompanyIntegrationService.createContract(
                        meta,
                        contract,
                    )
                } returns request

                val result = consumer.syncContract(event)

                ResultAssert.assertThat(result).isSuccessWithData(true)

                Assertions.assertThat(LocalProducer.hasEvent(NullvsCompanyContractBatchRequestEvent.name)).isTrue

                coVerifyOnce {
                    totvsCompanyIntegrationService.createContract(
                        meta,
                        contract,
                    )
                }

            }

        @Test
        fun `#should produce NullvsSyncCompanyContractRequestEvent for update`() =
            runBlocking {
                val updateEvent = NullvsSyncCompanyContractRequestEvent(contract, NullvsActionType.UPDATE)
                val updateMeta = meta.copy(eventId = updateEvent.messageId, integratedAt = updateEvent.eventDate)
                val requestUpdate =
                    request.copy(meta = updateMeta, action = NullvsActionType.UPDATE)

                coEvery {
                    totvsCompanyIntegrationService.updateContract(
                        updateMeta, contract
                    )
                } returns requestUpdate

                val result = consumer.syncContract(updateEvent)

                ResultAssert.assertThat(result).isSuccessWithData(true)

                Assertions.assertThat(LocalProducer.hasEvent(NullvsCompanyContractBatchRequestEvent.name)).isTrue

                coVerifyOnce {
                    totvsCompanyIntegrationService.updateContract(
                        updateMeta, contract
                    )
                }
            }
    }

    @Nested
    inner class ReprocessContractDependencies {
        @Test
        fun `#should reprocess contract dependencies`() = runBlocking {
            val event = NullvsCompanyContractActivatedEvent(RangeUUID.generate(), "0000001", "0022")

            coEvery {
                nullvsDependencyService.reprocessItsDependents(
                    event.payload.companyContractId,
                    InternalModelType.CONTRACT
                )
            } returns emptyList()

            val result = consumer.reprocessContractDependencies(event)

            ResultAssert.assertThat(result).isSuccessWithData(emptyList<NullvsIntegrationLog>())

            coVerifyOnce {
                nullvsDependencyService.reprocessItsDependents(
                    event.payload.companyContractId,
                    InternalModelType.CONTRACT
                )
            }
        }

        @Test
        fun `#should not reprocess contract dependencies when FF is disabled`() = runBlocking {
            val event = NullvsCompanyContractActivatedEvent(RangeUUID.generate(), "0000001", "0022")

            withFeatureFlag(FeatureNamespace.NULLVS, "use_dependency_service", false) {
                val result = consumer.reprocessContractDependencies(event)

                ResultAssert.assertThat(result).isSuccessWithData(true)

                coVerifyNone {
                    nullvsDependencyService.reprocessItsDependents(
                        any(),
                        any(),
                    )
                }
            }
        }
    }
}
