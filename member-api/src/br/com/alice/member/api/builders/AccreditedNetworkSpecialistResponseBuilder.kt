package br.com.alice.member.api.builders

import br.com.alice.app.content.model.BackgroundColor
import br.com.alice.app.content.model.RemoteActionMethod
import br.com.alice.app.content.model.ScreenType
import br.com.alice.app.content.model.Tag
import br.com.alice.app.content.model.TagColorScheme
import br.com.alice.app.content.model.section.CalloutSection
import br.com.alice.common.DistanceUtils
import br.com.alice.common.core.extensions.onlyNumbers
import br.com.alice.common.core.extensions.toPhoneNumberMaskWithDDD
import br.com.alice.common.mobile.SemanticVersion
import br.com.alice.coverage.br.com.alice.coverage.converter.HealthProfessionalConsolidatedLightConverter.toConsolidatedType
import br.com.alice.data.layer.models.AccreditedNetworkFavorite
import br.com.alice.data.layer.models.ConsolidatedRating
import br.com.alice.data.layer.models.FeatureNamespace
import br.com.alice.data.layer.models.HealthProfessional
import br.com.alice.data.layer.models.HealthcareTeam
import br.com.alice.data.layer.models.MedicalSpecialty
import br.com.alice.data.layer.models.Member
import br.com.alice.data.layer.models.ModalityType
import br.com.alice.data.layer.models.Person
import br.com.alice.data.layer.models.PhoneNumber
import br.com.alice.data.layer.models.PhoneType
import br.com.alice.data.layer.models.Product
import br.com.alice.featureconfig.core.FeatureService
import br.com.alice.member.api.MobileApp
import br.com.alice.member.api.ServiceConfig
import br.com.alice.member.api.models.MobileRouting
import br.com.alice.member.api.models.NavigationResponse
import br.com.alice.member.api.models.accreditedNetwork.AccreditedNetworkDetailsSection
import br.com.alice.member.api.models.accreditedNetwork.ServiceLocation
import br.com.alice.member.api.models.accreditedNetwork.SpecialistDetailsTransport
import br.com.alice.member.api.models.accreditedNetwork.SpecialistSchedulingInformationTransport
import br.com.alice.member.api.models.appContent.RemoteAction
import br.com.alice.member.api.utils.TitleGenerator
import io.ktor.util.toLowerCasePreservingASCIIRules
import java.util.UUID

object AccreditedNetworkSpecialistResponseBuilder {

    private const val PROFESSIONAL_PLACEHOLDER_IMAGE =
        "https://alice-member-app-assets.s3.amazonaws.com/placeholder/providers/Professional.svg"

    fun buildSpecialistResponse(
        person: Person,
        member: Member,
        product: Product,
        specialist: HealthProfessional,
        favorite: AccreditedNetworkFavorite?,
        consolidatedRating: ConsolidatedRating? = null,
        specialty: MedicalSpecialty? = null,
        subSpecialties: List<MedicalSpecialty> = emptyList(),
        memberLat: Double? = null,
        memberLng: Double? = null,
        schedulingInfo: SpecialistSchedulingInformationTransport?,
        serviceDays: List<String>,
        callout: CalloutSection? = null,
        appVersion: SemanticVersion,
    ): SpecialistDetailsTransport {
        return SpecialistDetailsTransport(
            id = specialist.id,
            providerType = specialist.type.toConsolidatedType(),
            title = specialty?.name ?: specialist.type.toConsolidatedType().title,
            name = specialist.name,
            imageUrl = specialist.imageUrl ?: PROFESSIONAL_PLACEHOLDER_IMAGE,
            crm = specialist.council.toString(),
            emptyRating = consolidatedRating?.let { buildEmptyRating(it) },
            rating = consolidatedRating?.let { buildRating(it) },
            subspecialties = subSpecialties.map { it.name },
            services = buildServiceLocations(
                specialist = specialist,
                schedulingInfo = schedulingInfo,
                serviceDays = serviceDays,
                memberLat = memberLat,
                memberLng = memberLng,
            ),
            membershipCard = MembershipCardBuilder.buildAliceMembershipCard(person, product),
            sections = buildSections(specialist, appVersion = appVersion),
            callout = callout,
            showMembershipCardShareButton = showMembershipCardShareButton(),
            favoriteInfo = FavoriteInfoTransportBuilder.build(
                referenceId = specialist.id,
                specialtyIds = specialist.specialtyId?.let { listOf(it) } ?: emptyList(),
                referenceType = specialist.type.toConsolidatedType(),
                favoriteId = favorite?.id,
                isFavorite = favorite != null,
                appVersion = appVersion,
            ),
        )
    }

    fun buildHealthCareTeamSpecialistResponse(
        specialist: HealthProfessional,
        memberHealthcareTeam: HealthcareTeam,
        person: Person?,
        schedulingInfo: SpecialistSchedulingInformationTransport?,
        serviceDays: List<String>,
        rootNodeId: UUID? = null,
        scheduleUrl: String? = null,
        memberLat: Double? = null,
        memberLng: Double? = null,
        specialty: MedicalSpecialty? = null,
        appVersion: SemanticVersion,
    ): SpecialistDetailsTransport {
        return SpecialistDetailsTransport(
            id = specialist.id,
            providerType = specialist.type.toConsolidatedType(),
            title = specialty?.name ?: specialist.type.toConsolidatedType().title,
            name = specialist.name,
            imageUrl = specialist.imageUrl ?: PROFESSIONAL_PLACEHOLDER_IMAGE,
            crm = specialist.council.toString(),
            tag = buildHealthCareTeamTag(specialist, person, memberHealthcareTeam),
            services = buildServiceLocations(
                specialist = specialist,
                rootNodeId = rootNodeId,
                scheduleUrl = scheduleUrl,
                schedulingInfo = schedulingInfo,
                serviceDays = serviceDays,
                memberLat = memberLat,
                memberLng = memberLng,
            ),
            sections = buildSections(specialist = specialist, appVersion = appVersion),
            action = buildAction(specialist, memberHealthcareTeam, person, memberLat, memberLng),
            showMembershipCardShareButton = showMembershipCardShareButton(),
            favoriteInfo = null,
        )
    }

    private fun buildEmptyRating(consolidatedRating: ConsolidatedRating): SpecialistDetailsTransport.EmptySpecialistRating? =
        if (consolidatedRating.ratingDetail.evaluationsCount < 10) {
            val remaining = 10 - consolidatedRating.ratingDetail.evaluationsCount
            SpecialistDetailsTransport.EmptySpecialistRating(
                value = if (remaining == 1)
                    "Falta **1 avaliação** dos membros para disponibilizar os dados"
                else
                    "Faltam **$remaining avaliações** dos membros para disponibilizar os dados",

                backgroundColor = BackgroundColor.BACKGROUND_DEFAULT
            )
        } else null

    private fun buildRating(consolidatedRating: ConsolidatedRating): SpecialistDetailsTransport.SpecialistRating? =
        if (consolidatedRating.ratingDetail.evaluationsCount >= 10)
            SpecialistDetailsTransport.SpecialistRating(
                backgroundColor = BackgroundColor.BACKGROUND_DEFAULT,
                aiGeneratedComment = consolidatedRating.aiGeneratedComment,
                appointmentCount = consolidatedRating.ratingDetail.appointmentCount,
                evaluationsCount = consolidatedRating.ratingDetail.evaluationsCount,
                trustScore = consolidatedRating.ratingDetail.trustScore,
                recommendationScore = consolidatedRating.ratingDetail.recommendationScore
            )
        else null

    private fun buildAction(
        specialist: HealthProfessional,
        memberHealthcareTeam: HealthcareTeam,
        person: Person?,
        latitude: Double?,
        longitude: Double?
    ): SpecialistDetailsTransport.Action? =
        if (memberHealthcareTeam.physicianStaffId != specialist.staffId) {
            val queryParams = if (latitude != null && longitude != null) "?lat=$latitude&lng=$longitude" else ""
            SpecialistDetailsTransport.Action(
                label = "Ver ${
                    TitleGenerator.getAccreditedNetworkPhysicianDescriptionBySegment(
                        specialist.gender,
                        memberHealthcareTeam.segment,
                        person
                    ).toLowerCasePreservingASCIIRules()
                }",
                navigation = NavigationResponse(
                    mobileRoute = MobileRouting.SPECIALIST_DETAILS,
                    properties = mapOf(
                        "method" to RemoteActionMethod.GET,
                        "endpoint" to "/accredited_network/healthcare_team/physician/${memberHealthcareTeam.physicianStaffId}$queryParams"
                    ),
                )
            )
        } else null

    private fun buildSections(
        specialist: HealthProfessional,
        appVersion: SemanticVersion
    ): List<AccreditedNetworkDetailsSection> {
        return listOfNotNull(
            specialist.profileBio?.let {
                AccreditedNetworkDetailsSection(
                    title = "Biografia",
                    icon = "user",
                    content = listOf(
                        AccreditedNetworkDetailsSection.Content(
                            text = it
                        )
                    )
                )
            },

            specialist.education.takeIf { it.isNotEmpty() }?.let { education ->
                val icon = "education".takeIf { appVersion >= SemanticVersion("4.30.0") } ?: "paper"

                AccreditedNetworkDetailsSection(
                    title = "Educação",
                    icon = icon,
                    content = listOf(
                        AccreditedNetworkDetailsSection.Content(
                            text = education.joinToString(separator = "\n")
                        )
                    )
                )
            },

            specialist.qualifications.takeIf { it.isNotEmpty() }?.let { qualifications ->
                AccreditedNetworkDetailsSection(
                    title = "Certificações e qualificações",
                    icon = "script",
                    content = listOf(
                        AccreditedNetworkDetailsSection.Content(
                            text = qualifications.joinToString { it.description }
                        )
                    ),
                    isExpandable = false,
                )
            }
        )
    }

    private fun buildHealthCareTeamTag(
        specialist: HealthProfessional,
        person: Person?,
        healthcareTeam: HealthcareTeam
    ): Tag? = if (healthcareTeam.physicianStaffId == specialist.staffId)
        Tag(
            text = TitleGenerator.getAccreditedNetworkPhysicianDescriptionBySegment(
                specialist.gender,
                healthcareTeam.segment,
                person
            ),
            colorScheme = TagColorScheme.MAGENTA
        )
    else null

    private fun buildServiceLocations(
        specialist: HealthProfessional,
        schedulingInfo: SpecialistSchedulingInformationTransport?,
        serviceDays: List<String>,
        memberLat: Double?,
        memberLng: Double?,
        rootNodeId: UUID? = null,
        scheduleUrl: String? = null,
    ): List<ServiceLocation> {
        val scheduleService = scheduleUrl?.let { buildSaraCalendarServiceLocations(it, schedulingInfo) }
            ?: rootNodeId?.let { buildPreTriageServiceLocations(it, schedulingInfo) } ?: emptyList()

        val directScheduleAllowed = (rootNodeId == null && scheduleUrl == null)

        val remoteServices = specialist.contacts?.filter { it.modality == ModalityType.REMOTE }
            ?.map { remoteContact ->
                ServiceLocation(
                    title = "Por vídeo chamada",
                    details = "Mais rápido",
                    tag = remoteContact.scheduleAvailabilityDays?.takeIf { schedulingInfo == null && it > 0 }?.let {
                        buildServiceLocationTag(it)
                    },
                    serviceDays = serviceDays.ifEmpty { remoteContact.availableDays.map { it.weekDay.surname } },
                    contacts = buildServiceLocationContacts(remoteContact.website, remoteContact.phones),
                    nextDateAvailable = schedulingInfo?.nextAvailableDate?.toString().takeIf { directScheduleAllowed },
                    scheduleNavigation = schedulingInfo?.schedulingUrl?.let { url ->
                        ServiceLocation.ScheduleNavigation(
                            mobileRoute = MobileRouting.WEBVIEW,
                            properties = mapOf(
                                "link" to url,
                                "pop_on_complete" to true,
                                "feedback_message" to "Agendamento efetuado com sucesso!",
                                "token" to "true",
                            )
                        )
                    }.takeIf { directScheduleAllowed }
                )
            } ?: emptyList()

        val presentServices =
            specialist.contacts?.filter { it.modality == ModalityType.PRESENTIAL }
                ?.map { contact ->
                    ServiceLocation(
                        title = "Presencial",
                        details = contact.address?.distanceInMetersTo(memberLat, memberLng)?.let { distance ->
                            "${DistanceUtils.formatDistance(distance)} de você"
                        }.orEmpty(),
                        tag = contact.scheduleAvailabilityDays?.takeIf { it > 0 }?.let { buildServiceLocationTag(it) },
                        address = contact.address?.formattedAddress(),
                        serviceDays = contact.availableDays.map { it.weekDay.surname },
                        contacts = buildServiceLocationContacts(contact.website, contact.phones)
                    )
                } ?: emptyList()

        return scheduleService + remoteServices + presentServices
    }

    private fun buildServiceLocationTag(scheduleAvailabilityDays: Int) = Tag(
        icon = "calendar",
        text = when (scheduleAvailabilityDays) {
            1 -> "Previsão de agenda: 1 dia"
            else -> "Previsão de agenda: $scheduleAvailabilityDays dias"
        },
        colorScheme = serviceLocationTagColor()
    )

    private fun buildServiceLocationContacts(
        website: String?,
        phones: List<PhoneNumber>
    ): List<ServiceLocation.ServiceContact> {
        val websiteContact = website?.let {
            ServiceLocation.ServiceContact(
                type = ServiceLocation.ServiceContactType.WEBSITE,
                text = it,
                url = it
            )
        }

        val phoneContacts = phones.map {
            val phoneNumber = it.phone.onlyNumbers()
            ServiceLocation.ServiceContact(
                type = it.type?.toServiceContactType()
                    ?: ServiceLocation.ServiceContactType.PHONE,
                text = startWithZero(phoneNumber).toPhoneNumberMaskWithDDD(),
                url = if (it.type == PhoneType.WHATSAPP) {
                    buildWhatsappPhoneUrl(phoneNumber)
                } else {
                    buildPhoneUrl(phoneNumber)
                }
            )
        }

        return phoneContacts + listOfNotNull(websiteContact)
    }

    private fun PhoneType.toServiceContactType() = when (this) {
        PhoneType.WHATSAPP -> ServiceLocation.ServiceContactType.WHATSAPP
        PhoneType.MOBILE -> ServiceLocation.ServiceContactType.MOBILE
        else -> ServiceLocation.ServiceContactType.PHONE
    }

    private fun startWithZero(onlyPhoneNumber: String): String =
        when {
            onlyPhoneNumber.startsWith("0") -> onlyPhoneNumber
            else -> "0$onlyPhoneNumber"
        }

    private fun buildWhatsappPhoneUrl(preformattedPhone: String): String {
        val formattedPhone = when {
            preformattedPhone.length > 11 -> preformattedPhone
            else -> "55$preformattedPhone"
        }
        return "${MobileApp.Links.WHATSAPP_API_URL}$formattedPhone"
    }

    private fun buildPhoneUrl(preformattedPhone: String): String {
        val formattedPhone = when {
            preformattedPhone.startsWith("0") -> preformattedPhone
            else -> "0$preformattedPhone"
        }
        return "tel:$formattedPhone"
    }

    private fun buildPreTriageServiceLocations(
        rootNodeId: UUID,
        schedulingInfo: SpecialistSchedulingInformationTransport?
    ) = buildScheduleService(
        schedulingInfo,
        MobileRouting.CHESHIRE_SCREEN,
        mapOf(
            "action" to RemoteAction(
                method = RemoteActionMethod.POST,
                endpoint = ServiceConfig.url("/bud/start/$rootNodeId"),
                params = mapOf("screen_id" to ScreenType.SCREENING_SCHEDULE_REDIRECT)
            )
        )
    )

    private fun buildSaraCalendarServiceLocations(
        scheduleUrl: String,
        schedulingInfo: SpecialistSchedulingInformationTransport?
    ) = buildScheduleService(
        schedulingInfo,
        MobileRouting.WEBVIEW,
        mapOf(
            "link" to scheduleUrl,
            "token" to "true",
            "pop_on_complete" to true,
            "feedback_message" to "Agendamento efetuado com sucesso!",
        )
    )

    private fun buildScheduleService(
        schedulingInfo: SpecialistSchedulingInformationTransport?,
        mobileRoute: MobileRouting,
        properties: Map<String, Any>
    ) = listOf(
        ServiceLocation(
            title = "Agendar",
            details = "",
            nextDateAvailable = schedulingInfo?.nextAvailableDate?.toString(),
            scheduleNavigation = ServiceLocation.ScheduleNavigation(
                mobileRoute = mobileRoute,
                properties = properties
            )
        )
    )

    private fun serviceLocationTagColor() =
        FeatureService.get(
            namespace = FeatureNamespace.ALICE_APP,
            key = "specialist_details_service_location_tag_color",
            defaultValue = "gray"
        ).let {
            runCatching { TagColorScheme.valueOf(it.uppercase()) }.getOrDefault(TagColorScheme.GRAY)
        }

    private fun showMembershipCardShareButton() =
        FeatureService.get(
            namespace = FeatureNamespace.ALICE_APP,
            key = "membership_card_share_button",
            defaultValue = false
        )
}
