package br.com.alice.member.api.webhooks

import br.com.alice.common.Response
import br.com.alice.common.foldResponse
import br.com.alice.common.logging.logger
import br.com.alice.common.toResponse
import br.com.alice.common.withRootServicePolicy
import br.com.alice.common.withUnauthenticatedTokenWithKey
import br.com.alice.data.layer.MEMBERSHIP_WEBHOOKS_ROOT_SERVICE_NAME
import br.com.alice.data.layer.models.PersonIdentityValidationStatus
import br.com.alice.member.api.models.webhooks.clearsale.BiometricScoreRequest
import br.com.alice.member.api.models.webhooks.clearsale.FacematchRequest
import br.com.alice.member.api.services.AppState.IDENTITY_VALIDATION_UPDATED
import br.com.alice.member.api.services.AppStateNotifier
import br.com.alice.person.client.PersonIdentityValidationService
import com.github.kittinunf.result.map
import java.time.LocalDateTime

class ClearSaleWebhookReceiver(
    private val personIdentityValidationService: PersonIdentityValidationService,
) : Webhook() {
    suspend fun notifyBiometricScore(request: BiometricScoreRequest) = unauthenticatedToken {
        personIdentityValidationService
            .handleBiometricScoreNotification(request.code, LocalDateTime.now())
            .map {
                if (it.status != PersonIdentityValidationStatus.PROCESSING) {
                    AppStateNotifier.updateAppState(it.personId, IDENTITY_VALIDATION_UPDATED)
                }

                true
            }.foldResponse()
    }

    suspend fun notifyFacematch(request: FacematchRequest) = unauthenticatedToken {
        logger.info("ClearSaleWebhookReceiver::notifyFacematch called", "body" to request)

        if (request.status == "Processing") {
            logger.info("ClearSaleWebhookReceiver::notifyFacematch skipping when the status is processing")
            return@unauthenticatedToken true.toResponse()
        }

        val isMatch = if (request.status == "Error") false else request.isMatch!!

        personIdentityValidationService.handleFacematchNotification(
            request.transactionId,
            request.confiability!!,
            isMatch,
        ).map {
            if (it.status === PersonIdentityValidationStatus.VALID ||
                it.status === PersonIdentityValidationStatus.INVALID) {
                AppStateNotifier.updateAppState(it.personId, IDENTITY_VALIDATION_UPDATED)
            }

            true
        }.foldResponse()
    }

    private suspend fun unauthenticatedToken(func: suspend () -> Response): Response =
        withRootServicePolicy(MEMBERSHIP_WEBHOOKS_ROOT_SERVICE_NAME) {
            withUnauthenticatedTokenWithKey(MEMBERSHIP_WEBHOOKS_ROOT_SERVICE_NAME) {
                func.invoke()
            }
        }
}
