package br.com.alice.member.api.controllers.beneficiary

import br.com.alice.app.content.model.ScreenLayout
import br.com.alice.app.content.model.ScreensTransport
import br.com.alice.common.data.dsl.matchers.ResponseAssert
import br.com.alice.common.helpers.mockLocalDateTime
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.BeneficiaryOnboardingFlowType
import br.com.alice.data.layer.models.BeneficiaryOnboardingPhase
import br.com.alice.data.layer.models.BeneficiaryOnboardingPhaseType
import br.com.alice.data.layer.models.Person
import br.com.alice.member.api.RoutesTestHelper
import br.com.alice.member.api.models.BeneficiaryCompanyInfoResponse
import br.com.alice.member.api.models.BeneficiaryPersonInfoResponse
import br.com.alice.member.api.models.BeneficiaryResponse
import br.com.alice.member.api.models.activation.ActivationItemTransport
import br.com.alice.member.api.models.activation.ActivationScreenTransport
import br.com.alice.member.api.services.beneficiary.BeneficiaryActivationHomeScreenService
import br.com.alice.member.api.usecases.GetBeneficiaryUseCase
import br.com.alice.membership.client.onboarding.HealthDeclarationAppointmentScheduler
import br.com.alice.schedule.client.AppointmentScheduleService
import br.com.alice.schedule.client.PersonCalendlyService
import io.mockk.coEvery
import io.mockk.mockk
import java.time.LocalDateTime
import kotlin.test.BeforeTest
import kotlin.test.Test

class BeneficiaryActivationHomeScreenControllerTest : RoutesTestHelper() {
    private val getBeneficiaryUseCase: GetBeneficiaryUseCase = mockk()
    private val beneficiaryActivationHomeScreenService: BeneficiaryActivationHomeScreenService = mockk()
    private val healthDeclarationAppointmentScheduler: HealthDeclarationAppointmentScheduler  = mockk()
    private val appointmentScheduleService: AppointmentScheduleService  = mockk()
    private val personCalendlyService: PersonCalendlyService  = mockk()

    private val company = TestModelFactory.buildCompany()
    private val member = TestModelFactory.buildMember()
    private val person: Person = TestModelFactory.buildPerson()
    private val token = person.id.toString()
    private val screensTransport = ScreensTransport(
        "activation",
        ScreenLayout(
            "layout",
            body = emptyList()
        ),
    )

    private val beneficiaryOnboarding = TestModelFactory.buildBeneficiaryOnboarding(
        flowType = BeneficiaryOnboardingFlowType.FULL_RISK_FLOW
    )

    private val beneficiary = TestModelFactory.buildBeneficiary(
        personId = person.id,
        companyId = company.id,
        onboarding = beneficiaryOnboarding,
    )

    @BeforeTest
    override fun setup() {
        super.setup()
        module.single {
            BeneficiaryActivationHomeScreenController(
                getBeneficiaryUseCase,
                beneficiaryActivationHomeScreenService,
                healthDeclarationAppointmentScheduler,
                appointmentScheduleService,
                personCalendlyService
            )
        }

        coEvery { getBeneficiaryUseCase.getBeneficiaryWithDependentsByPersonId(person.id) } returns
                GetBeneficiaryUseCase.BeneficiaryInfo(
                    person,
                    beneficiary,
                    company,
                    dependentsResponse = emptyList(),
                    cptsCount = 0,
                    isCurrentRespondent = true,
                    earliestOnboardingPhase = BeneficiaryOnboardingPhaseType.HEALTH_DECLARATION,
                    isHighRisk = false,
                    currentPhase = BeneficiaryOnboardingPhaseType.HEALTH_DECLARATION,
                    initialProduct = null,
                    isMinor = false,
                    member = member,
                    cptsAction = null,
                )
    }

    @Test
    fun `#getActivationHomeScreen should return Health Declaration phase as expected`() {
        coEvery {
            beneficiaryActivationHomeScreenService.buildActivationHomeCards(
                ActivationScreenTransport(
                    listOf(
                        ActivationItemTransport(
                            personId = person.id.toString(),
                            personFirstName = person.firstName,
                            isMinor = false,
                            personIsBeneficiary = true,
                            isPending = false,
                            beneficiaryNationalId = person.nationalId,
                            currentPhaseIndex = BeneficiaryOnboardingPhaseType.HEALTH_DECLARATION.order,
                            cptsCount = 0,
                        )
                    )
                )
            )
        } returns screensTransport

        authenticatedAs(token, toTestPerson(person)) {
            get("/beneficiaries/screen/activation_home") { response ->
                ResponseAssert.assertThat(response).isOKWithData(screensTransport)
            }
        }
    }

    @Test
    fun `#getActivationHomeScreen should call service with delayed added dependents`() = mockLocalDateTime { localDateTime ->
        val company = TestModelFactory.buildCompany()

        val companyInfo = BeneficiaryCompanyInfoResponse(
            id = company.id,
            name = company.name,
            legalName = company.legalName,
            cnpj = company.cnpj
        )

        val personDependent = TestModelFactory.buildPerson(acceptedTermsAt = LocalDateTime.now().minusDays(1))

        val personDependentInfo = BeneficiaryPersonInfoResponse(
            id = personDependent.id.id,
            firstName = personDependent.firstName,
            lastName = personDependent.lastName,
            nationalId = personDependent.nationalId,
            email = personDependent.email,
            nickName = personDependent.nickName,
            genderIdentity = personDependent.genderIdentity,
            acceptedTermsAt = personDependent.acceptedTermsAt,
            phoneNumber = person.phoneNumber,
        )

        val dependent = BeneficiaryResponse(
            id = beneficiary.id,
            parentBeneficiary = null,
            type = beneficiary.type,
            contractType = null,
            parentBeneficiaryRelationType = null,
            activatedAt = beneficiary.activatedAt,
            currentPhase = BeneficiaryOnboardingPhaseType.HEALTH_DECLARATION,
            companyInfo = companyInfo,
            personInfo = personDependentInfo,
            dependents = emptyList(),
            isMemberActive = false,
            flowType = null,
            productName = null,
            isMinor = false,
            showCptsConfirmationCard = false,
            cptsCount = 0,
            firstPhaseTransactedAt = localDateTime.plusDays(1)
        )

        val beneficiary = beneficiary.copy(onboarding = beneficiaryOnboarding.copy(
            phases = listOf(
                TestModelFactory.buildBeneficiaryOnboardingPhase(
                    phase = BeneficiaryOnboardingPhaseType.HEALTH_DECLARATION,
                    transactedAt = localDateTime
                )
            )
        ))

        coEvery { getBeneficiaryUseCase.getBeneficiaryWithDependentsByPersonId(person.id) } returns
                GetBeneficiaryUseCase.BeneficiaryInfo(
                    person,
                    beneficiary,
                    company,
                    dependentsResponse = listOf(dependent),
                    cptsCount = 0,
                    isCurrentRespondent = true,
                    earliestOnboardingPhase = BeneficiaryOnboardingPhaseType.HEALTH_DECLARATION,
                    isHighRisk = false,
                    currentPhase = BeneficiaryOnboardingPhaseType.HEALTH_DECLARATION,
                    initialProduct = null,
                    isMinor = false,
                    member = member,
                    cptsAction = null,
                )

        coEvery {
            beneficiaryActivationHomeScreenService.buildActivationHomeCards(
                ActivationScreenTransport(
                    listOf(
                        ActivationItemTransport(
                            personId = person.id.toString(),
                            personFirstName = person.firstName,
                            isMinor = false,
                            personIsBeneficiary = true,
                            isPending = false,
                            beneficiaryNationalId = person.nationalId,
                            currentPhaseIndex = BeneficiaryOnboardingPhaseType.HEALTH_DECLARATION.order,
                            cptsCount = 0,
                            isDelayedActivation = false,
                        ),
                        ActivationItemTransport(
                            personId = personDependent.id.toString(),
                            personFirstName = person.firstName,
                            isMinor = false,
                            personIsBeneficiary = false,
                            isPending = false,
                            beneficiaryNationalId = person.nationalId,
                            currentPhaseIndex = BeneficiaryOnboardingPhaseType.HEALTH_DECLARATION.order,
                            cptsCount = 0,
                            isDelayedActivation = true
                        )
                    ),
                )
            )
        } returns screensTransport

        authenticatedAs(token, toTestPerson(person)) {
            get("/beneficiaries/screen/activation_home") { response ->
                ResponseAssert.assertThat(response).isOKWithData(screensTransport)
            }
        }
    }

    @Test
    fun `#getHealthDeclarationsScreen should return Health Declaration List Screen`() {
        coEvery {
            beneficiaryActivationHomeScreenService.buildHealthDeclarationListScreen(
                ActivationScreenTransport(
                    listOf(
                        ActivationItemTransport(
                            personId = person.id.toString(),
                            personFirstName = person.firstName,
                            isMinor = false,
                            personIsBeneficiary = true,
                            isPending = true,
                            beneficiaryNationalId = person.nationalId,
                            currentPhaseIndex = BeneficiaryOnboardingPhaseType.HEALTH_DECLARATION.order,
                            cptsCount = 0,
                        )
                    )
                )
            )
        } returns screensTransport

        authenticatedAs(token, toTestPerson(person)) {
            get("/beneficiaries/screen/health_declarations") { response ->
                ResponseAssert.assertThat(response).isOKWithData(screensTransport)
            }
        }
    }

    @Test
    fun `#getReviewTermsScreen should return Review terms list Screen`() {
        coEvery {
            beneficiaryActivationHomeScreenService.buildReviewTermsListScreen(
                ActivationScreenTransport(
                    listOf(
                        ActivationItemTransport(
                            personId = person.id.toString(),
                            personFirstName = person.firstName,
                            isMinor = false,
                            personIsBeneficiary = true,
                            isPending = true,
                            beneficiaryNationalId = person.nationalId,
                            currentPhaseIndex = BeneficiaryOnboardingPhaseType.HEALTH_DECLARATION.order,
                            cptsCount = 0,
                        )
                    )
                )
            )
        } returns screensTransport

        authenticatedAs(token, toTestPerson(person)) {
            get("/beneficiaries/screen/review_terms") { response ->
                ResponseAssert.assertThat(response).isOKWithData(screensTransport)
            }
        }
    }

    @Test
    fun `#getVideoCallOrientationsScreen should return videocall orientations screen`() {
        coEvery {
            beneficiaryActivationHomeScreenService.buildVideoCallInstructionsScreen(
                ActivationScreenTransport(
                    listOf(
                        ActivationItemTransport(
                            personId = person.id.toString(),
                            personFirstName = person.firstName,
                            isMinor = false,
                            personIsBeneficiary = true,
                            isPending = false,
                            beneficiaryNationalId = person.nationalId,
                            currentPhaseIndex = BeneficiaryOnboardingPhaseType.HEALTH_DECLARATION.order,
                            cptsCount = 0,
                        )
                    )
                )
            )
        } returns screensTransport

        authenticatedAs(token, toTestPerson(person)) {
            get("/beneficiaries/screen/videocall_orientations") { response ->
                ResponseAssert.assertThat(response).isOKWithData(screensTransport)
            }
        }
    }

    @Test
    fun `#getBeneficiaryVideoCallOrientationsScreen should return videocall orientations screen`() {
        coEvery {
            beneficiaryActivationHomeScreenService.buildBeneficiaryVideocallInstructionsScreen()
        } returns screensTransport

        authenticatedAs(token, toTestPerson(person)) {
            get("/beneficiaries/screen/videocall_orientations/beneficiary") { response ->
                ResponseAssert.assertThat(response).isOKWithData(screensTransport)
            }
        }
    }

    @Test
    fun `#getDependentsVideoCallOrientationsScreen should return videocall orientations screen`() {
        coEvery {
            beneficiaryActivationHomeScreenService.buildDependentsVideocallInstructionsScreen()
        } returns screensTransport

        authenticatedAs(token, toTestPerson(person)) {
            get("/beneficiaries/screen/videocall_orientations/dependents") { response ->
                ResponseAssert.assertThat(response).isOKWithData(screensTransport)
            }
        }
    }
}
