package br.com.alice.business.services

import br.com.alice.business.client.BeneficiaryService
import br.com.alice.business.client.CompanyService
import br.com.alice.business.model.BeneficiaryTransport
import br.com.alice.business.model.ChangePlanType
import br.com.alice.common.core.extensions.toBrazilianDateFormat
import br.com.alice.common.data.dsl.matchers.ResultAssert.Companion.assertThat
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.common.models.Sex
import br.com.alice.communication.email.EmailSender
import br.com.alice.communication.email.model.EmailAddress
import br.com.alice.communication.email.model.EmailReceipt
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.common.BeneficiaryType
import br.com.alice.common.Brand
import br.com.alice.person.client.MemberService
import br.com.alice.person.client.PersonService
import br.com.alice.product.client.ProductService
import com.github.kittinunf.result.success
import io.mockk.coEvery
import io.mockk.mockk
import kotlinx.coroutines.runBlocking
import java.time.LocalDateTime
import kotlin.test.Test

class MailerServiceImplTest {
    private val sender: EmailSender = mockk()
    private val personService: PersonService = mockk()
    private val memberService: MemberService = mockk()
    private val productService: ProductService = mockk()
    private val companyService: CompanyService = mockk()
    private val beneficiaryService: BeneficiaryService = mockk()
    private val mailerService = MailerServiceImpl(sender, personService, memberService, productService, companyService, beneficiaryService)

    private val company = TestModelFactory.buildCompany()
    private val companyStaff = TestModelFactory.buildCompanyStaff(companyId = company.id)
    private val person = TestModelFactory.buildPerson()
    private val sendTo = EmailAddress(name = companyStaff.fullName(), email = companyStaff.email)

    private val newProduct = TestModelFactory.buildProduct(title = "New Product")
    private val oldProduct = TestModelFactory.buildProduct(title = "Old Product")
    private val newMemberProduct = TestModelFactory.buildMemberProduct(id = newProduct.id)
    private val oldMemberProduct = TestModelFactory.buildMemberProduct(id = oldProduct.id)

    private val newMember = TestModelFactory.buildMember(personId = person.id, selectedProduct = newMemberProduct)
    private val oldMember = TestModelFactory.buildMember(personId = person.id, selectedProduct = oldMemberProduct)
    private val schedule = TestModelFactory.buildMemberProductChangeSchedule(
        personId = person.id,
        memberId = newMember.id,
        productId = newProduct.id,
    )

    private val beneficiary = TestModelFactory.buildBeneficiary(
        memberId = newMember.id,
        personId = person.id
    )

    private val beneficiaryTransport = BeneficiaryTransport(
        companyId = company.id,
        type = BeneficiaryType.EMPLOYEE,
        firstName = "Alice",
        lastName = "Test",
        nationalId = "12345678901",
        email = "<EMAIL>",
        activatedAt = LocalDateTime.now(),
        address = TestModelFactory.buildAddress(),
        initialProductId = oldProduct.id,
        brand = Brand.ALICE,
        flowType = null,
        birthDate = LocalDateTime.of(1990, 1, 1, 0, 0, 0),
        sex = Sex.FEMALE,
        phoneNumber = "11999999999",
        cnpj = company.cnpj
    )

    @Test
    fun `#sendChangePlanEmailByConsumer should send change plan email by consumer successfully`(): Unit = runBlocking {

        coEvery { personService.get(schedule.personId) } returns person.success()
        coEvery { memberService.findByPerson(schedule.personId) } returns listOf(oldMember, newMember).success()
        coEvery { productService.findByIds(any()) } returns listOf(newProduct, oldProduct).success()
        coEvery { sender.send(any()) } returns EmailReceipt(id = "Test")

        val result = mailerService.sendChangePlanEmailByConsumer(sendTo, schedule)

        assertThat(result).isSuccess()

        coVerifyOnce {
            sender.send(match {
                it.to == listOf(EmailAddress(companyStaff.fullName(), companyStaff.email))
                        && it.templateName.contains("cs_beneficiary_plan_change")
                        && it.replaceVariables["plan_change_title"] == "A troca de plano foi concluída."
                        && it.replaceVariables["show_requested"] == "none"
                        && it.replaceVariables["show_canceled"] == "none"
                        && it.replaceVariables["beneficiary_new_plan"] == newProduct.salesProductName
                        && it.replaceVariables["beneficiary_current_plan"] == oldProduct.salesProductName
            })
        }
    }

    @Test
    fun `#sendRequestedFileEmail change plan email by consumer`(): Unit = runBlocking {

        val companyName = "Company Test"
        val fileVaultLink = "sample_link.com.br"
        val fileName = "sample_file.xlsx"
        val requestDate = LocalDateTime.now().toBrazilianDateFormat()

        coEvery { sender.send(any()) } returns EmailReceipt(id = "Test")

        val result = mailerService.sendRequestedFileEmail(
            sendTo,
            companyName = companyName,
            fileLink = fileVaultLink,
            fileName = "sample_file.xlsx",
            linkExpirationInDays = 1
        )

        assertThat(result).isSuccess()

        coVerifyOnce {
            sender.send(match {
                it.to == listOf(EmailAddress(companyStaff.fullName(), companyStaff.email))
                        && it.templateName.contains("cs_beneficiary_export_data")
                        && it.replaceVariables["company_name"] == companyName
                        && it.replaceVariables["file_name"] == fileName
                        && it.replaceVariables["file_link"] == fileVaultLink
                        && it.replaceVariables["date_of_request"] == requestDate
                        && it.replaceVariables["days_to_expiration"] == "1 dia"
            })
        }
    }

    @Test
    fun `send inclusion email successfully when member is holder`(): Unit = runBlocking {
        coEvery { companyService.get(any()) } returns company.success()
        coEvery { productService.getProduct(any()) } returns oldProduct.success()
        coEvery { sender.send(any()) } returns EmailReceipt("id")

        mailerService.sendInclusionEmail(sendTo, beneficiaryTransport)

        coVerifyOnce {
            sender.send(match {
                it.to == listOf(EmailAddress(companyStaff.fullName(), companyStaff.email))
                        && it.templateName.contains("cs_beneficiary_inclusion")
            })
        }
    }

    @Test
    fun `send inclusion email successfully when member is dependent`(): Unit = runBlocking {
        coEvery { companyService.get(any()) } returns company.success()
        coEvery { productService.getProduct(any()) } returns oldProduct.success()
        coEvery { sender.send(any()) } returns EmailReceipt("id")

        mailerService.sendInclusionEmail(sendTo, beneficiaryTransport)

        coVerifyOnce {
            sender.send(match {
                it.to == listOf(EmailAddress(companyStaff.fullName(), companyStaff.email))
                        && it.templateName.contains("cs_beneficiary_inclusion")
            })
        }
    }

    @Test
    fun `#sendChangePlanEmail should send email successfully when type is PLAN_CHANGE_REQUESTED`(): Unit = runBlocking {
        coEvery { personService.get(any()) } returns person.success()
        coEvery { memberService.get(any()) } returns oldMember.success()
        coEvery { productService.findByIds(any()) } returns listOf(oldProduct, newProduct).success()
        coEvery { sender.send(any()) } returns EmailReceipt("id")

        mailerService.sendChangePlanEmail(
            sendTo,
            schedule,
            oldMember.id,
            ChangePlanType.PLAN_CHANGE_REQUESTED
        )

        coVerifyOnce {
            sender.send(match {
                it.to == listOf(EmailAddress(companyStaff.fullName(), companyStaff.email))
                        && it.templateName.contains("cs_beneficiary_plan_change")
                        && it.replaceVariables["plan_change_title"] == "A troca de plano foi agendada com sucesso."
                        && it.replaceVariables["show_concluded"] == "none"
                        && it.replaceVariables["show_canceled"] == "none"
            })
        }
    }

    @Test
    fun `#sendChangePlanEmail should send email successfully when type is PLAN_CHANGE_CONCLUDED`(): Unit = runBlocking {
        coEvery { personService.get(any()) } returns person.success()
        coEvery { memberService.get(any()) } returns oldMember.success()
        coEvery { productService.findByIds(any()) } returns listOf(oldProduct, newProduct).success()
        coEvery { sender.send(any()) } returns EmailReceipt("id")

        mailerService.sendChangePlanEmail(
            sendTo,
            schedule,
            beneficiary.memberId,
            ChangePlanType.PLAN_CHANGE_CONCLUDED
        )

        coVerifyOnce {
            sender.send(match {
                it.to == listOf(EmailAddress(companyStaff.fullName(), companyStaff.email))
                        && it.templateName.contains("cs_beneficiary_plan_change")
                        && it.replaceVariables["plan_change_title"] == "A troca de plano foi concluída."
                        && it.replaceVariables["show_requested"] == "none"
                        && it.replaceVariables["show_canceled"] == "none"
            })
        }
    }

    @Test
    fun `#sendChangePlanEmail should send email successfully when type is PLAN_CHANGE_CANCELED`(): Unit = runBlocking {
        coEvery { personService.get(any()) } returns person.success()
        coEvery { memberService.get(any()) } returns oldMember.success()
        coEvery { productService.findByIds(any()) } returns listOf(oldProduct, newProduct).success()
        coEvery { sender.send(any()) } returns EmailReceipt("id")

        mailerService.sendChangePlanEmail(
            sendTo,
            schedule,
            beneficiary.memberId,
            ChangePlanType.PLAN_CHANGE_CANCELED
        )

        coVerifyOnce {
            sender.send(match {
                it.to == listOf(EmailAddress(companyStaff.fullName(), companyStaff.email))
                        && it.templateName.contains("cs_beneficiary_plan_change")
                        && it.replaceVariables["plan_change_title"] == "A troca de plano foi cancelada."
                        && it.replaceVariables["show_requested"] == "none"
                        && it.replaceVariables["show_concluded"] == "none"
            })
        }
    }

    @Test
    fun `#sendRequestedFileFailedEmail change plan email by consumer`(): Unit = runBlocking {

        val companyName = "Company Test"

        coEvery { sender.send(any()) } returns EmailReceipt(id = "Test")

        val result = mailerService.sendRequestedFileFailedEmail(
            sendTo,
            companyName = companyName,
        )

        assertThat(result).isSuccess()

        coVerifyOnce {
            sender.send(match {
                it.to == listOf(EmailAddress(companyStaff.fullName(), companyStaff.email))
                        && it.templateName.contains("cs_beneficiary_export_data_error")
                        && it.replaceVariables["company_name"] == companyName
            })
        }
    }
}
