package br.com.alice.healthplan.services

import br.com.alice.common.Disease
import br.com.alice.common.RangeUUID
import br.com.alice.common.UpdatedBy
import br.com.alice.common.core.PersonId
import br.com.alice.common.core.exceptions.ConflictException
import br.com.alice.common.core.exceptions.InvalidArgumentException
import br.com.alice.common.core.exceptions.NotFoundException
import br.com.alice.common.core.extensions.atEndOfTheDay
import br.com.alice.common.data.dsl.matchers.ResultAssert.Companion.assertThat
import br.com.alice.common.data.dsl.matchers.queryEq
import br.com.alice.common.featureflag.withFeatureFlag
import br.com.alice.common.featureflag.withFeatureFlags
import br.com.alice.common.helpers.coVerifyNone
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.common.helpers.mockLocalDateTime
import br.com.alice.common.helpers.mockRangeUUID
import br.com.alice.common.kafka.interfaces.KafkaProducerService
import br.com.alice.common.service.data.client.CountByValues
import br.com.alice.common.service.data.dsl.and
import br.com.alice.coverage.client.City
import br.com.alice.coverage.client.LocationService
import br.com.alice.coverage.client.State
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.helpers.TestModelFactory.buildHealthPlanTaskReferral
import br.com.alice.data.layer.models.*
import br.com.alice.data.layer.models.HealthPlanTaskStatus.ACTIVE
import br.com.alice.data.layer.models.HealthPlanTaskStatus.CREATE
import br.com.alice.data.layer.models.HealthPlanTaskStatus.DELETED
import br.com.alice.data.layer.models.HealthPlanTaskStatus.DELETED_BY_MEMBER
import br.com.alice.data.layer.models.HealthPlanTaskStatus.DELETED_BY_STAFF
import br.com.alice.data.layer.models.HealthPlanTaskStatus.DONE
import br.com.alice.data.layer.models.HealthPlanTaskStatus.DRAFT
import br.com.alice.data.layer.models.HealthPlanTaskType.*
import br.com.alice.data.layer.models.ProfessionalType.NUTRITIONIST
import br.com.alice.data.layer.models.TaskSourceType.COUNTER_REFERRAL
import br.com.alice.data.layer.models.TaskSourceType.MEMBER
import br.com.alice.data.layer.models.TaskSourceType.STAFF
import br.com.alice.data.layer.models.TaskSourceType.SYSTEM
import br.com.alice.data.layer.services.HealthPlanTaskDataService
import br.com.alice.data.layer.services.testPersonId
import br.com.alice.documentsigner.services.DocumentPrinterService
import br.com.alice.ehr.client.AdvancedAccessService
import br.com.alice.healthlogic.client.AdherenceValidationService
import br.com.alice.healthlogic.models.adherence.AdherenceByTaskId
import br.com.alice.healthlogic.models.adherence.AdherenceResponse
import br.com.alice.healthplan.client.ActiveReferralsByPersonNotFound
import br.com.alice.healthplan.client.HealthPlanTaskCounters
import br.com.alice.healthplan.client.HealthPlanTaskFilters
import br.com.alice.healthplan.client.HealthPlanTaskGroupService
import br.com.alice.healthplan.client.PublishGroupedDemandRequest
import br.com.alice.healthplan.client.ReferralWithoutSpecialistNotFound
import br.com.alice.healthplan.converters.HealthPlanTaskTransportConverter
import br.com.alice.healthplan.converters.ReferralSuggestionResponseConverter
import br.com.alice.healthplan.events.HealthPlanTaskGroupPublishedEvent
import br.com.alice.healthplan.events.HealthPlanTaskUpsertedEvent
import br.com.alice.healthplan.extensions.internal.fillDateUsingNewDeadlineInterval
import br.com.alice.healthplan.helpers.TestModelDomainFactory
import br.com.alice.healthplan.models.HLAdherenceValidationResponse
import br.com.alice.healthplan.models.HealthPlanTaskGroupTransport
import br.com.alice.healthplan.models.HealthPlanTasksTransport
import br.com.alice.healthplan.models.PrescriptionTransport
import br.com.alice.healthplan.models.ReferralTransport
import br.com.alice.healthplan.models.TaskRequesterRequest
import br.com.alice.healthplan.models.ValidatedDemand
import br.com.alice.provider.client.MedicalSpecialtyService
import br.com.alice.staff.client.StaffService
import com.github.kittinunf.result.failure
import com.github.kittinunf.result.success
import io.mockk.MockKMatcherScope
import io.mockk.called
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.confirmVerified
import io.mockk.mockk
import io.mockk.mockkStatic
import java.time.LocalDate
import java.time.LocalDateTime
import kotlinx.coroutines.runBlocking
import org.assertj.core.api.Assertions.assertThat
import kotlin.test.AfterTest
import kotlin.test.Test

class HealthPlanTaskServiceImplTest {
    private val healthPlanTaskDataService: HealthPlanTaskDataService = mockk()
    private val kafkaProducer: KafkaProducerService = mockk()
    private val documentPrinterService: DocumentPrinterService = mockk()
    private val healthPlanTaskGroupService: HealthPlanTaskGroupService = mockk()
    private val staffService: StaffService = mockk()
    private val locationService: LocationService = mockk()
    private val adherenceValidationService: AdherenceValidationService = mockk()
    private val medicalSpecialtyService: MedicalSpecialtyService = mockk()
    private val advancedAccessService: AdvancedAccessService = mockk()

    private val healthPlanTaskService = HealthPlanTaskServiceImpl(
        healthPlanTaskDataService,
        kafkaProducer,
        documentPrinterService,
        healthPlanTaskGroupService,
        staffService,
        locationService,
        adherenceValidationService,
        medicalSpecialtyService,
        advancedAccessService,
    )

    private val personId = PersonId()
    private val healthPlanId = RangeUUID.generate()
    private val staff = TestModelFactory.buildStaff()
    private val taskCaseId = RangeUUID.generate()
    private val appointmentId = RangeUUID.generate()

    private val group = HealthPlanTaskGroupTransport(
        id = RangeUUID.generate(),
        healthPlanId = healthPlanId,
        name = "Name",
        personId = personId
    )

    private val prescriptionModel = TestModelFactory.buildHealthPlanTaskPrescription(
        personId = personId,
        healthPlanId = healthPlanId,
        staffId = staff.id,
        groupId = group.id
    )

    private val prescriptionModelWithAppointment = prescriptionModel.copy(appointmentId = appointmentId)

    private val adherenceResponse = AdherenceResponse(
        result = HLAdherence.AdherenceResultType.ADHERENT
    )

    private val expectedAdherenceValidationResponseNode = HLAdherenceValidationResponse(
        result = adherenceResponse.result,
        model = adherenceResponse.adherenceRecommendedActions?.first()?.model,
        validatedDemand = adherenceResponse.adherenceRecommendedActions?.map { adherenceRecommendedAction ->
            ValidatedDemand(
                id = adherenceRecommendedAction.id,
                healthLogicId = adherenceRecommendedAction.healthLogicId,
                healthConditionId = prescriptionModel.caseRecordDetails.first().description.id!!,
                healthConditionCode = prescriptionModel.caseRecordDetails.first().description.value
            )
        },
        healthCondition = prescriptionModel.caseRecordDetails.firstOrNull()?.description
    )

    private val prescriptionTransport =
        TestModelDomainFactory.convertHealthPlanTaskTransport<Prescription, PrescriptionTransport>(
            listOf(prescriptionModel),
            mapOf(staff.id to staff),
            prescriptionModel.type,
            mapOf(group.id!! to group)
        ).first()

    private val dateTimeFromOffset = LocalDateTime.now().minusHours(12.toLong())
    private val dateTimeToOffset = LocalDateTime.now().minusHours(2.toLong())

    private val tasksTransport = HealthPlanTasksTransport(
        prescription = listOf(prescriptionTransport)
    )
    private val referral = TestModelFactory.buildHealthPlanTask(
        id = RangeUUID.generate(),
        healthPlanId = RangeUUID.generate(),
        personId = personId,
        type = REFERRAL,
        status = ACTIVE
    ).specialize<Referral>()

    private val testRequest = TestModelFactory.buildHealthPlanTask(
        id = RangeUUID.generate(),
        healthPlanId = RangeUUID.generate(),
        personId = personId,
        type = TEST_REQUEST,
        status = ACTIVE
    )

    private val anotherReferral = TestModelFactory.buildHealthPlanTask(
        id = RangeUUID.generate(),
        healthPlanId = healthPlanId,
        personId = personId,
        groupId = group.id,
        type = REFERRAL,
        content = mapOf(
            "suggestedSpecialist" to mapOf(
                "name" to "João", "id" to RangeUUID.generate(),
                "type" to "STAFF"
            )
        ),
        status = ACTIVE
    ).specialize<Referral>()

    private val actionPlanTaskBackfillStatusList = listOf(ACTIVE, DONE, DELETED, DELETED_BY_MEMBER, DELETED_BY_STAFF)
    private val validHealthPlanTypes =
        listOf(
            PRESCRIPTION,
            EATING,
            PHYSICAL_ACTIVITY,
            SLEEP,
            MOOD,
            OTHERS,
            TEST_REQUEST,
            REFERRAL,
            EMERGENCY,
            FOLLOW_UP_REQUEST,
            SURGERY_PRESCRIPTION
        )

    private val event = HealthPlanTaskUpsertedEvent(
        task = prescriptionModel.fillDate(),
        staffId = staff.id,
        previousStatus = CREATE
    )

    @AfterTest
    fun confirmMocks() =
        confirmVerified(
            healthPlanTaskDataService,
            kafkaProducer,
            documentPrinterService,
            healthPlanTaskGroupService,
            staffService,
            locationService,
            adherenceValidationService
        )

    @Test
    fun `#create should create a new task`() = runBlocking {
        val transport = prescriptionTransport.copy(id = prescriptionModel.id)

        coEvery { healthPlanTaskDataService.add(prescriptionModel.fillDate()) } returns prescriptionModel.success()
        coEvery { healthPlanTaskGroupService.get(prescriptionModel.groupId!!) } returns group.success()
        coEvery { staffService.findByList(prescriptionModel.requestersStaffIds.toList()) } returns listOf(staff).success()

        val result = healthPlanTaskService.create(prescriptionModel, staff.id)
        assertThat(result).isSuccessWithData(transport)

        coVerifyOnce { healthPlanTaskDataService.add(any()) }
        coVerifyOnce { healthPlanTaskGroupService.get(any()) }
        coVerifyOnce { staffService.findByList(any()) }
        coVerify { locationService wasNot called }
        coVerify { kafkaProducer wasNot called }
        coVerify { adherenceValidationService wasNot called }
        coVerify { documentPrinterService wasNot called }
    }

    @Test
    fun `#create should create a new task with adherence check for task without demand`() = runBlocking {
        val transport = prescriptionTransport.copy(id = prescriptionModel.id)

        coEvery { healthPlanTaskDataService.add(prescriptionModel.fillDate()) } returns prescriptionModel.success()
        coEvery { healthPlanTaskGroupService.get(prescriptionModel.groupId!!) } returns group.success()
        coEvery { staffService.findByList(prescriptionModel.requestersStaffIds.toList()) } returns listOf(staff).success()

        val result = healthPlanTaskService.create(prescriptionModel, staff.id, true)
        assertThat(result).isSuccessWithData(transport)

        coVerifyOnce { healthPlanTaskDataService.add(any()) }
        coVerifyOnce { healthPlanTaskGroupService.get(any()) }
        coVerifyOnce { staffService.findByList(any()) }
        coVerify { locationService wasNot called }
        coVerify { kafkaProducer wasNot called }
        coVerify { documentPrinterService wasNot called }
        coVerify { adherenceValidationService wasNot called }
    }

    @Test
    fun `#create should create a new task with adherence check`() = runBlocking {
        val healthConditionId = RangeUUID.generate()
        val caseRecordDetails = listOf(
            CaseRecordDetails(
                description = DiseaseDetails(
                    type = Disease.Type.CID_10,
                    value = "10",
                    id = healthConditionId.toString()
                ),
                severity = CaseSeverity.ONGOING
            )
        )
        val prescriptionModel = prescriptionModel.copy(caseRecordDetails = caseRecordDetails)
        val expectedModel = prescriptionModel.fillDate()
        val transport = prescriptionTransport.copy(
            id = prescriptionModel.id,
            caseRecordDetails = caseRecordDetails,
            deadline = expectedModel.deadline,
            start = expectedModel.start,
            hlAdherenceValidation = expectedAdherenceValidationResponseNode.copy(
                healthCondition = caseRecordDetails.first().description
            ),
            demand = caseRecordDetails.first()
        )

        coEvery { healthPlanTaskDataService.add(expectedModel) } returns expectedModel.success()
        coEvery {
            adherenceValidationService.validateAdherence(
                healthConditionId = healthConditionId,
                healthPlanTask = expectedModel,
                forceValidation = false
            )
        } returns adherenceResponse.success()
        coEvery { healthPlanTaskGroupService.get(expectedModel.groupId!!) } returns group.success()
        coEvery { staffService.findByList(expectedModel.requestersStaffIds.toList()) } returns listOf(staff).success()

        val result = healthPlanTaskService.create(prescriptionModel, staff.id, true)
        assertThat(result).isSuccessWithData(transport)

        coVerifyOnce { healthPlanTaskDataService.add(any()) }
        coVerifyOnce { healthPlanTaskGroupService.get(any()) }
        coVerifyOnce { staffService.findByList(any()) }
        coVerifyOnce { adherenceValidationService.validateAdherence(any(), any(), any()) }
        coVerify { documentPrinterService wasNot called }
        coVerify { locationService wasNot called }
        coVerify { kafkaProducer wasNot called }
    }

    @Test
    fun `#create should not change deadline informed date`() = runBlocking {
        val start = Start(
            date = LocalDateTime.of(2021, 9, 5, 10, 0)
        )
        val deadline = Deadline(
            quantity = 0,
            date = LocalDateTime.of(2021, 10, 5, 22, 0)
        )
        val model = prescriptionModel.copy(
            start = start,
            deadline = deadline
        )
        val transport = prescriptionTransport.copy(
            id = model.id,
            sentence = model.specialize<Prescription>().fullSentence(),
            createdAt = model.createdAt.toString(),
            start = start,
            deadline = deadline
        )

        coEvery { healthPlanTaskDataService.add(model.fillDate()) } returns model.success()
        coEvery { healthPlanTaskGroupService.get(prescriptionModel.groupId!!) } returns group.success()
        coEvery { staffService.findByList(prescriptionModel.requestersStaffIds.toList()) } returns listOf(staff).success()
        coEvery { kafkaProducer.produce(matchEvent(event)) } returns mockk()

        val result = healthPlanTaskService.create(model, staff.id)
        assertThat(result).isSuccessWithData(transport)

        coVerifyOnce { healthPlanTaskDataService.add(any()) }
        coVerifyOnce { healthPlanTaskGroupService.get(any()) }
        coVerifyOnce { staffService.findByList(any()) }
        coVerify { locationService wasNot called }
        coVerify { kafkaProducer wasNot called }
        coVerify { documentPrinterService wasNot called }
        coVerify { locationService wasNot called }
        coVerify { adherenceValidationService wasNot called }
    }

    @Test
    fun `#create should not change deadline informed date even when new deadline setup flow is enabled`() =
        runBlocking {
            withFeatureFlag(
                namespace = FeatureNamespace.HEALTH_PLAN,
                key = "should_use_new_health_plan_task_deadline_calculation_flow",
                value = true
            ) {
                val group = HealthPlanTaskGroupTransport(
                    id = RangeUUID.generate(),
                    healthPlanId = healthPlanId,
                    name = "Name",
                    personId = personId
                )

                val prescriptionModel = TestModelFactory.buildHealthPlanTaskPrescription(
                    personId = personId,
                    healthPlanId = healthPlanId,
                    staffId = staff.id,
                    groupId = group.id
                )

                val prescriptionTransport =
                    TestModelDomainFactory.convertHealthPlanTaskTransport<Prescription, PrescriptionTransport>(
                        listOf(prescriptionModel),
                        mapOf(staff.id to staff),
                        prescriptionModel.type,
                        mapOf(group.id!! to group)
                    ).first()
                val event = HealthPlanTaskUpsertedEvent(
                    task = prescriptionModel.fillDateUsingNewDeadlineInterval(),
                    staffId = staff.id,
                    previousStatus = CREATE
                )
                val start = Start(
                    date = LocalDateTime.of(2021, 9, 5, 10, 0)
                )
                val deadline = Deadline(
                    quantity = 0,
                    date = LocalDateTime.of(2021, 10, 5, 22, 0)
                )
                val model = prescriptionModel.copy(
                    start = start,
                    deadline = deadline
                )
                val transport = prescriptionTransport.copy(
                    id = model.id,
                    sentence = model.specialize<Prescription>().fullSentence(),
                    createdAt = model.createdAt.toString(),
                    start = start,
                    deadline = deadline
                )

                coEvery { healthPlanTaskDataService.add(model.fillDateUsingNewDeadlineInterval()) } returns model.success()
                coEvery { healthPlanTaskGroupService.get(prescriptionModel.groupId!!) } returns group.success()
                coEvery { staffService.findByList(prescriptionModel.requestersStaffIds.toList()) } returns listOf(staff).success()
                coEvery { kafkaProducer.produce(matchEvent(event)) } returns mockk()

                val result = healthPlanTaskService.create(model, staff.id)
                assertThat(result).isSuccessWithData(transport)

                coVerifyOnce { healthPlanTaskDataService.add(any()) }
                coVerifyOnce { healthPlanTaskGroupService.get(any()) }
                coVerifyOnce { staffService.findByList(any()) }
                coVerify { locationService wasNot called }
                coVerify { kafkaProducer wasNot called }
                coVerify { documentPrinterService wasNot called }
                coVerify { locationService wasNot called }
                coVerify { adherenceValidationService wasNot called }
            }
        }

    @Test
    fun `#update should check conflicts and update`() = mockLocalDateTime {
        val healthConditionId = RangeUUID.generate()
        val caseRecordDetails = listOf(
            CaseRecordDetails(
                description = DiseaseDetails(
                    type = Disease.Type.CID_10,
                    value = "10",
                    id = healthConditionId.toString()
                ),
                severity = CaseSeverity.ONGOING
            )
        )

        val model = prescriptionModel.copy(
            id = RangeUUID.generate(),
            caseRecordDetails = caseRecordDetails
        )

        val transport = prescriptionTransport.copy(
            id = model.id,
            lastRequester = prescriptionTransport.lastRequester?.copy(requestedAt = model.updatedAt.toString()),
            sentence = prescriptionModel.specialize<Prescription>().fullSentence(),
            createdAt = prescriptionModel.createdAt.toString(),
            caseRecordDetails = caseRecordDetails,
            hlAdherenceValidation = expectedAdherenceValidationResponseNode.copy(
                healthCondition = caseRecordDetails.first().description
            ),
            demand = caseRecordDetails.first()
        )

        coEvery { healthPlanTaskDataService.get(model.id) } returns model.copy(description = "new").success()
        coEvery { healthPlanTaskDataService.update(model.copy(deadline = Deadline(unit = PeriodUnit.DAY, quantity = 30)).fillDate()) } returns model.success()
        coEvery {
            adherenceValidationService.validateAdherence(
                healthConditionId = healthConditionId,
                healthPlanTask = model,
                forceValidation = false
            )
        } returns adherenceResponse.success()
        coEvery { healthPlanTaskGroupService.get(model.groupId!!) } returns group.success()
        coEvery { staffService.findByList(model.requestersStaffIds.toList()) } returns listOf(staff).success()

        val result = healthPlanTaskService.update(
            healthPlanTask = model,
            staffId = staff.id,
            shouldValidateAdherence = true
        )
        assertThat(result).isSuccessWithData(transport)

        coVerifyOnce { healthPlanTaskDataService.get(any()) }
        coVerifyOnce { healthPlanTaskDataService.update(any()) }
        coVerifyOnce { healthPlanTaskGroupService.get(any()) }
        coVerifyOnce { staffService.findByList(any()) }
        coVerifyOnce { adherenceValidationService.validateAdherence(any(), any(), any()) }
        coVerify { locationService wasNot called }
        coVerify { documentPrinterService wasNot called }
    }

    @Test
    fun `#update should check conflicts and update using new deadline calculation flow`() = mockLocalDateTime { now ->
        withFeatureFlag(
            FeatureNamespace.HEALTH_PLAN, "should_use_new_health_plan_task_deadline_calculation_flow", true
        ) {
            val prescriptionModel = TestModelFactory.buildHealthPlanTaskPrescription(
                personId = personId,
                healthPlanId = healthPlanId,
                staffId = staff.id,
                groupId = group.id
            ).let { it.copy(dueDate = it.deadline!!.fillDate(now).date!!.toLocalDate()) }

            val prescriptionTransport =
                TestModelDomainFactory.convertHealthPlanTaskTransport<Prescription, PrescriptionTransport>(
                    listOf(prescriptionModel),
                    mapOf(staff.id to staff),
                    prescriptionModel.type,
                    mapOf(group.id!! to group)
                ).first()

            val healthConditionId = RangeUUID.generate()
            val caseRecordDetails = listOf(
                CaseRecordDetails(
                    description = DiseaseDetails(
                        type = Disease.Type.CID_10,
                        value = "10",
                        id = healthConditionId.toString()
                    ),
                    severity = CaseSeverity.ONGOING
                )
            )

            val model = prescriptionModel.copy(
                id = RangeUUID.generate(),
                caseRecordDetails = caseRecordDetails
            )

            val transport = prescriptionTransport.copy(
                id = model.id,
                lastRequester = prescriptionTransport.lastRequester?.copy(requestedAt = model.updatedAt.toString()),
                sentence = prescriptionModel.specialize<Prescription>().fullSentence(),
                createdAt = prescriptionModel.createdAt.toString(),
                caseRecordDetails = caseRecordDetails,
                hlAdherenceValidation = expectedAdherenceValidationResponseNode.copy(
                    healthCondition = caseRecordDetails.first().description
                ),
                demand = caseRecordDetails.first()
            )
            val toUpdate = model.fillDateUsingNewDeadlineInterval()

            coEvery { healthPlanTaskDataService.get(model.id) } returns model.copy(description = "new").success()
            coEvery { healthPlanTaskDataService.update(toUpdate) } returns model.success()
            coEvery {
                adherenceValidationService.validateAdherence(
                    healthConditionId = healthConditionId,
                    healthPlanTask = model,
                    forceValidation = false
                )
            } returns adherenceResponse.success()
            coEvery { healthPlanTaskGroupService.get(model.groupId!!) } returns group.success()
            coEvery { staffService.findByList(model.requestersStaffIds.toList()) } returns listOf(staff).success()

            val result = healthPlanTaskService.update(
                healthPlanTask = model,
                staffId = staff.id,
                shouldValidateAdherence = true
            )
            assertThat(result).isSuccessWithData(transport)

            coVerifyOnce { healthPlanTaskDataService.get(any()) }
            coVerifyOnce { healthPlanTaskDataService.update(any()) }
            coVerifyOnce { healthPlanTaskGroupService.get(any()) }
            coVerifyOnce { staffService.findByList(any()) }
            coVerifyOnce { adherenceValidationService.validateAdherence(any(), any(), any()) }
            coVerify { locationService wasNot called }
            coVerify { documentPrinterService wasNot called }
        }
    }

    @Test
    fun `#update should check conflicts and update using new deadline calculation flow checking date`() =
        mockLocalDateTime { now ->
            withFeatureFlags(
                FeatureNamespace.HEALTH_PLAN, mapOf(
                    "should_use_new_health_plan_task_deadline_calculation_flow" to false,
                    "new_health_plan_task_deadline_calculation_flow_base_date" to now.toLocalDate().minusDays(2)
                        .toString(),
                )
            ) {
                val prescriptionModel = TestModelFactory.buildHealthPlanTaskPrescription(
                    personId = personId,
                    healthPlanId = healthPlanId,
                    staffId = staff.id,
                    groupId = group.id
                ).let { it.copy(dueDate = it.deadline!!.fillDate(now).date!!.toLocalDate()) }

                val prescriptionTransport =
                    TestModelDomainFactory.convertHealthPlanTaskTransport<Prescription, PrescriptionTransport>(
                        listOf(prescriptionModel),
                        mapOf(staff.id to staff),
                        prescriptionModel.type,
                        mapOf(group.id!! to group)
                    ).first()

                val healthConditionId = RangeUUID.generate()
                val caseRecordDetails = listOf(
                    CaseRecordDetails(
                        description = DiseaseDetails(
                            type = Disease.Type.CID_10,
                            value = "10",
                            id = healthConditionId.toString()
                        ),
                        severity = CaseSeverity.ONGOING
                    )
                )

                val model = prescriptionModel.copy(
                    id = RangeUUID.generate(),
                    caseRecordDetails = caseRecordDetails
                )

                val transport = prescriptionTransport.copy(
                    id = model.id,
                    lastRequester = prescriptionTransport.lastRequester?.copy(requestedAt = model.updatedAt.toString()),
                    sentence = prescriptionModel.specialize<Prescription>().fullSentence(),
                    createdAt = prescriptionModel.createdAt.toString(),
                    caseRecordDetails = caseRecordDetails,
                    hlAdherenceValidation = expectedAdherenceValidationResponseNode.copy(
                        healthCondition = caseRecordDetails.first().description
                    ),
                    demand = caseRecordDetails.first()
                )
                val toUpdate = model.fillDateUsingNewDeadlineInterval()

                coEvery { healthPlanTaskDataService.get(model.id) } returns model.copy(description = "new").success()
                coEvery { healthPlanTaskDataService.update(toUpdate) } returns model.success()
                coEvery {
                    adherenceValidationService.validateAdherence(
                        healthConditionId = healthConditionId,
                        healthPlanTask = model,
                        forceValidation = false
                    )
                } returns adherenceResponse.success()
                coEvery { healthPlanTaskGroupService.get(model.groupId!!) } returns group.success()
                coEvery { staffService.findByList(model.requestersStaffIds.toList()) } returns listOf(staff).success()

                val result = healthPlanTaskService.update(
                    healthPlanTask = model,
                    staffId = staff.id,
                    shouldValidateAdherence = true
                )
                assertThat(result).isSuccessWithData(transport)

                coVerifyOnce { healthPlanTaskDataService.get(any()) }
                coVerifyOnce { healthPlanTaskDataService.update(any()) }
                coVerifyOnce { healthPlanTaskGroupService.get(any()) }
                coVerifyOnce { staffService.findByList(any()) }
                coVerifyOnce { adherenceValidationService.validateAdherence(any(), any(), any()) }
                coVerify { locationService wasNot called }
                coVerify { documentPrinterService wasNot called }
            }
        }

    @Test
    fun `#update should throw conflict exception`() = runBlocking {
        val transport = prescriptionModel.copy(
            id = RangeUUID.generate(),
            version = 3
        )
        val model = prescriptionModel.copy(
            id = transport.id,
            version = 1,
            updatedBy = UpdatedBy("Staff", RangeUUID.generate().toString(), "ehr-api")
        )

        coEvery { healthPlanTaskDataService.get(transport.id) } returns model.success()

        val result = healthPlanTaskService.update(model.copy(version = 3), staff.id)
        assertThat(result).isFailureOfType(ConflictException::class)

        coVerifyOnce { healthPlanTaskDataService.get(any()) }
        coVerify { kafkaProducer wasNot called }
        coVerify { documentPrinterService wasNot called }
        coVerify { healthPlanTaskGroupService wasNot called }
        coVerify { staffService wasNot called }
        coVerify { locationService wasNot called }
        coVerify { adherenceValidationService wasNot called }
    }

    @Test
    fun `#update should update deadline date according deadline pattern`() = mockLocalDateTime {
        val start = Start(
            date = LocalDateTime.of(2021, 9, 5, 10, 0),
        )
        val deadline = Deadline(
            quantity = 0,
            date = LocalDateTime.of(2021, 10, 5, 22, 0),
        )
        val taskId = RangeUUID.generate()
        val model = prescriptionModel.copy(
            id = taskId,
            deadline = deadline,
            start = start
        )
        val transport = prescriptionTransport.copy(
            id = taskId,
            lastRequester = prescriptionTransport.lastRequester?.copy(requestedAt = model.updatedAt.toString()),
            sentence = model.specialize<Prescription>().fullSentence(),
            createdAt = model.createdAt.toString(),
            start = start,
            deadline = deadline
        )

        coEvery { healthPlanTaskDataService.get(taskId) } returns model.copy(description = "new").success()
        coEvery { healthPlanTaskDataService.update(model.copy(deadline = Deadline(unit = PeriodUnit.DAY, quantity = 30)).fillDate()) } returns model.success()
        coEvery { healthPlanTaskGroupService.get(model.groupId!!) } returns group.success()
        coEvery { staffService.findByList(model.requestersStaffIds.toList()) } returns listOf(staff).success()
        coEvery { kafkaProducer.produce(matchEvent(event)) } returns mockk()

        val result = healthPlanTaskService.update(model, staff.id, false)
        assertThat(result).isSuccessWithData(transport)

        coVerifyOnce { healthPlanTaskDataService.get(any()) }
        coVerifyOnce { healthPlanTaskDataService.update(any()) }
        coVerifyOnce { healthPlanTaskGroupService.get(any()) }
        coVerifyOnce { staffService.findByList(any()) }
        coVerify { kafkaProducer wasNot called }
        coVerify { documentPrinterService wasNot called }
        coVerify { locationService wasNot called }
        coVerify { adherenceValidationService wasNot called }
    }

    @Test
    fun `#updateAll should check conflicts and update`() = mockLocalDateTime { now ->
        val requester = TaskRequesterRequest(
            staffId = staff.id,
            name = staff.firstName,
            profileImageUrl = staff.profileImageUrl
        )

        val transport = prescriptionTransport.copy(
            id = RangeUUID.generate(),
            lastRequester = requester,
            requesters = setOf(requester),
            deadline = Deadline(
                unit = PeriodUnit.DAY,
                quantity = 30,
                date = now
            )
        )

        val model = prescriptionModel.copy(
            id = transport.id!!,
            deadline = Deadline(
                unit = PeriodUnit.DAY,
                quantity = 30,
                date = now
            )
        )

        coEvery {
            healthPlanTaskDataService.find(queryEq { where { id.inList(listOf(transport.id!!)) } })
        } returns listOf(model.copy(content = mapOf("professional" to NUTRITIONIST as Any))).success()

        coEvery { healthPlanTaskDataService.update(model.copy(deadline = Deadline(unit = PeriodUnit.DAY, quantity = 30)).fillDate()) } returns model.success()
        coEvery { kafkaProducer.produce(matchEvent(event)) } returns mockk()

        val result = healthPlanTaskService.updateAll(listOf(transport), staff)
        assertThat(result).isSuccessWithData(listOf(model))

        coVerifyOnce { healthPlanTaskDataService.find(any()) }
        coVerifyOnce { healthPlanTaskDataService.update(any()) }
        coVerify { kafkaProducer wasNot called }
        coVerify { documentPrinterService wasNot called }
        coVerify { healthPlanTaskGroupService wasNot called }
        coVerify { staffService wasNot called }
        coVerify { locationService wasNot called }
        coVerify { adherenceValidationService wasNot called }
    }

    @Test
    fun `#updateAll should check conflicts and not update if the new object is the same as the old one`() =
        runBlocking {
            val requester = TaskRequesterRequest(
                staffId = staff.id,
                name = staff.firstName,
                profileImageUrl = staff.profileImageUrl
            )

            val transportId = RangeUUID.generate()
            val transport = prescriptionTransport.copy(
                id = transportId,
                lastRequester = requester,
                requesters = setOf(requester)
            )

            val model = prescriptionModel.copy(id = transportId)

            coEvery {
                healthPlanTaskDataService.find(queryEq { where { id.inList(listOf(transportId)) } })
            } returns listOf(model).success()

            val result = healthPlanTaskService.updateAll(listOf(transport), staff)
            assertThat(result).isSuccessWithData(listOf(model))

            coVerifyOnce { healthPlanTaskDataService.find(any()) }
            coVerify { kafkaProducer wasNot called }
            coVerify { documentPrinterService wasNot called }
            coVerify { healthPlanTaskGroupService wasNot called }
            coVerify { staffService wasNot called }
            coVerify { locationService wasNot called }
            coVerify { adherenceValidationService wasNot called }
        }

    @Test
    fun `#updateAll should not update when has conflicts`() = runBlocking {
        val requester = TaskRequesterRequest(
            staffId = staff.id,
            name = staff.firstName,
            profileImageUrl = staff.profileImageUrl
        )

        val transport = prescriptionTransport.copy(
            id = RangeUUID.generate(),
            lastRequester = requester,
            requesters = setOf(requester),
            version = 3
        )

        val model = prescriptionModel.copy(
            id = transport.id!!,
            version = 1,
            updatedBy = UpdatedBy("Staff", RangeUUID.generate().toString(), "ehr-api")
        )

        coEvery {
            healthPlanTaskDataService.find(queryEq { where { id.inList(listOf(transport.id!!)) } })
        } returns listOf(model).success()

        val result = healthPlanTaskService.updateAll(listOf(transport), staff)
        assertThat(result).isFailureOfType(ConflictException::class)

        coVerifyOnce { healthPlanTaskDataService.find(any()) }
        coVerify { kafkaProducer wasNot called }
        coVerify { documentPrinterService wasNot called }
        coVerify { healthPlanTaskGroupService wasNot called }
        coVerify { staffService wasNot called }
        coVerify { locationService wasNot called }
        coVerify { adherenceValidationService wasNot called }
    }

    @Test
    fun `#updateAll should not update when task is empty`() = runBlocking {
        val result = healthPlanTaskService.updateAll(emptyList(), staff)
        assertThat(result).isSuccessWithData(emptyList())

        coVerify { healthPlanTaskDataService wasNot called }
        coVerify { kafkaProducer wasNot called }
        coVerify { documentPrinterService wasNot called }
        coVerify { healthPlanTaskGroupService wasNot called }
        coVerify { staffService wasNot called }
        coVerify { locationService wasNot called }
        coVerify { adherenceValidationService wasNot called }
    }

    @Test
    fun `#updateAll should update all and notify`() = runBlocking {
        val task = buildHealthPlanTaskReferral()
        val event = HealthPlanTaskUpsertedEvent(task)
        val tasks = listOf(task)

        coEvery { healthPlanTaskDataService.updateList(tasks) } returns tasks.success()
        coEvery { kafkaProducer.produce(event = matchEvent(event), partitionKey = task.id.toString()) } returns mockk()

        val result = healthPlanTaskService.updateAll(tasks, notify = true)

        assertThat(result).isSuccessWithData(tasks)

        coVerifyOnce { kafkaProducer.produce(any(), any()) }
        coVerifyOnce { healthPlanTaskDataService.updateList(any()) }
    }

    @Test
    fun `#getByHealthPlan should get HealthPlanTasks by HealthPlan id`() = runBlocking {
        val id = RangeUUID.generate()
        val task =
            TestModelFactory.buildHealthPlanTask(
                personId = personId,
                staffId = staff.id,
                healthPlanId = id,
                type = OTHERS
            )

        coEvery {
            healthPlanTaskDataService.find(
                queryEq {
                    where {
                        healthPlanId.eq(id).and(status.inList(listOf(DRAFT, ACTIVE, DONE)))
                    }.orderBy { createdAt }.sortOrder { asc }
                }
            )
        } returns listOf(task).success()

        val result = healthPlanTaskService.getByHealthPlan(id)
        assertThat(result).isSuccessWithData(listOf(task))

        coVerifyOnce { healthPlanTaskDataService.find(any()) }
        coVerify { kafkaProducer wasNot called }
        coVerify { documentPrinterService wasNot called }
        coVerify { healthPlanTaskGroupService wasNot called }
        coVerify { staffService wasNot called }
        coVerify { locationService wasNot called }
        coVerify { adherenceValidationService wasNot called }
    }

    @Test
    fun `#getByHealthPlan should return error`() = runBlocking {
        coEvery { healthPlanTaskDataService.find(any()) } returns NotFoundException("task_not_found").failure()

        val result = healthPlanTaskService.getByHealthPlan(healthPlanId)
        assertThat(result).isFailureOfType(NotFoundException::class)

        coVerifyOnce { healthPlanTaskDataService.find(any()) }
        coVerify { kafkaProducer wasNot called }
        coVerify { documentPrinterService wasNot called }
        coVerify { healthPlanTaskGroupService wasNot called }
        coVerify { staffService wasNot called }
        coVerify { locationService wasNot called }
        coVerify { adherenceValidationService wasNot called }
    }

    @Test
    fun `#getByPerson should get HealthPlanTasks by person id and return HealthPlanTasksTransport`() = runBlocking {
        val personId = personId

        coEvery {
            healthPlanTaskDataService.find(
                queryEq {
                    where {
                        this.personId.eq(personId) and
                                this.status.inList(listOf(DRAFT, ACTIVE, DELETED_BY_MEMBER, DONE))
                    }.orderBy { createdAt }.sortOrder { desc }
                }
            )
        } returns listOf(prescriptionModel).success()

        coEvery { healthPlanTaskGroupService.findByIds(listOf(group.id!!)) } returns listOf(group).success()
        coEvery { staffService.findByList(listOf(staff.id)) } returns listOf(staff).success()

        coEvery {
            adherenceValidationService.getByTaskIds(listOf(prescriptionModel))
        } returns emptyList<AdherenceByTaskId>().success()

        val result = healthPlanTaskService.getByPerson(personId)

        assertThat(result).isSuccessWithData(tasksTransport)

        coVerifyOnce { healthPlanTaskDataService.find(any()) }
        coVerifyOnce { healthPlanTaskGroupService.findByIds(any()) }
        coVerifyOnce { staffService.findByList(any()) }
        coVerify { kafkaProducer wasNot called }
        coVerify { documentPrinterService wasNot called }
        coVerify { locationService wasNot called }
        coVerify { adherenceValidationService wasNot called }
    }

    @Test
    fun `#getByPerson should get HealthPlanTasks by person id and return HealthPlanTasksTransport with adherence validation`() =
        runBlocking {
            val personId = personId

            coEvery {
                healthPlanTaskDataService.find(
                    queryEq {
                        where {
                            this.personId.eq(personId) and
                                    this.status.inList(listOf(DRAFT, ACTIVE, DELETED_BY_MEMBER, DONE))
                        }.orderBy { createdAt }.sortOrder { desc }
                    }
                )
            } returns listOf(prescriptionModel).success()

            coEvery {
                healthPlanTaskGroupService.findByIds(listOf(group.id!!))
            } returns listOf(group).success()

            coEvery {
                staffService.findByList(listOf(staff.id))
            } returns listOf(staff).success()

            coEvery {
                adherenceValidationService.getByTaskIds(listOf(prescriptionModel))
            } returns listOf(
                AdherenceByTaskId(
                    taskId = prescriptionModel.id,
                    adherenceValidation = adherenceResponse
                )
            ).success()

            val result = healthPlanTaskService.getByPerson(personId, checkAdherence = true)

            assertThat(result).isSuccessWithData(
                tasksTransport.copy(
                    prescription = listOf(
                        prescriptionTransport.copy(
                            hlAdherenceValidation = expectedAdherenceValidationResponseNode
                        )
                    )
                )
            )

            coVerifyOnce { healthPlanTaskDataService.find(any()) }
            coVerifyOnce { healthPlanTaskGroupService.findByIds(any()) }
            coVerifyOnce { staffService.findByList(any()) }
            coVerifyOnce { adherenceValidationService.getByTaskIds(any()) }
            coVerify { locationService wasNot called }
            coVerify { kafkaProducer wasNot called }
            coVerify { documentPrinterService wasNot called }
            confirmMocks()
        }

    @Test
    fun `#getByPerson should get HealthPlanTasks by person id and return HealthPlanTasksTransport ignoring adherence validation`() =
        runBlocking {
            val personId = personId

            coEvery {
                healthPlanTaskDataService.find(
                    queryEq {
                        where {
                            this.personId.eq(personId) and
                                    this.status.inList(listOf(DRAFT, ACTIVE, DELETED_BY_MEMBER, DONE))
                        }.orderBy { createdAt }.sortOrder { desc }
                    }
                )
            } returns listOf(prescriptionModel).success()

            coEvery {
                healthPlanTaskGroupService.findByIds(listOf(group.id!!))
            } returns listOf(group).success()

            coEvery {
                staffService.findByList(listOf(staff.id))
            } returns listOf(staff).success()

            val result = healthPlanTaskService.getByPerson(personId, checkAdherence = false)

            assertThat(result).isSuccessWithData(tasksTransport)

            coVerifyOnce { healthPlanTaskDataService.find(any()) }
            coVerifyOnce { healthPlanTaskGroupService.findByIds(any()) }
            coVerifyOnce { staffService.findByList(any()) }
            coVerify { locationService wasNot called }
            coVerify { adherenceValidationService wasNot called }
            coVerify { kafkaProducer wasNot called }
            coVerify { documentPrinterService wasNot called }
            confirmMocks()
        }

    @Test
    fun `#getByPerson filters HealthPlanTasks`() = runBlocking {
        val personId = personId

        coEvery {
            healthPlanTaskDataService.find(
                queryEq {
                    where {
                        this.personId.eq(personId) and
                                status.inList(listOf(DRAFT)) and
                                type.inList(listOf(PRESCRIPTION))
                    }.orderBy { createdAt }.sortOrder { desc }
                }
            )
        } returns listOf(prescriptionModel).success()

        coEvery { healthPlanTaskGroupService.findByIds(listOf(group.id!!)) } returns listOf(group).success()
        coEvery { staffService.findByList(listOf(staff.id)) } returns listOf(staff).success()
        coEvery {
            adherenceValidationService.getByTaskIds(listOf(prescriptionModel))
        } returns emptyList<AdherenceByTaskId>().success()

        val result = healthPlanTaskService.getByPerson(
            personId,
            mapOf(
                "status" to listOf("DRAFT"),
                "type" to listOf("prescription"),
            ),
        )

        assertThat(result).isSuccessWithData(tasksTransport)

        coVerifyOnce { healthPlanTaskDataService.find(any()) }
        coVerifyOnce { healthPlanTaskGroupService.findByIds(any()) }
        coVerifyOnce { staffService.findByList(any()) }
        coVerify { kafkaProducer wasNot called }
        coVerify { documentPrinterService wasNot called }
        coVerify { locationService wasNot called }
        coVerify { adherenceValidationService wasNot called }
    }

    @Test
    fun `#getByPerson returns HealthPlanTasks filtered by all filters`() = runBlocking {
        val personId = personId

        coEvery {
            healthPlanTaskDataService.find(
                queryEq {
                    where {
                        this.personId.eq(personId) and
                                status.inList(listOf(DRAFT)) and
                                type.inList(listOf(PRESCRIPTION)) and
                                groupId.inList(listOf(prescriptionModel.groupId!!)) and
                                releasedByStaffId.inList(listOf(staff.id))
                    }.orderBy { createdAt }.sortOrder { desc }
                }
            )
        } returns listOf(prescriptionModel).success()

        coEvery { healthPlanTaskGroupService.findByIds(listOf(group.id!!)) } returns listOf(group).success()
        coEvery { staffService.findByList(listOf(staff.id)) } returns listOf(staff).success()

        coEvery {
            adherenceValidationService.getByTaskIds(listOf(prescriptionModel))
        } returns emptyList<AdherenceByTaskId>().success()

        val result = healthPlanTaskService.getByPerson(
            personId,
            mapOf(
                "status" to listOf("DRAFT"),
                "type" to listOf("prescription"),
                "group" to listOf(prescriptionModel.groupId!!.toString()),
                "released_by" to listOf(staff.id.toString()),
            ),
        )

        assertThat(result).isSuccessWithData(tasksTransport)

        coVerifyOnce { healthPlanTaskDataService.find(any()) }
        coVerifyOnce { healthPlanTaskGroupService.findByIds(any()) }
        coVerifyOnce { staffService.findByList(any()) }
        coVerify { kafkaProducer wasNot called }
        coVerify { documentPrinterService wasNot called }
        coVerify { locationService wasNot called }
        coVerify { adherenceValidationService wasNot called }
    }

    @Test
    fun `#getByPerson returns HealthPlanTasks filtered by case_id, status and appointment_id`() = runBlocking {
        val personId = personId
        val appointmentId = appointmentId
        val taskTransportWithAppointmentId = tasksTransport.copy(
            prescription = listOf(
                prescriptionTransport.copy(
                    appointmentId = appointmentId
                )
            )
        )

        coEvery {
            healthPlanTaskDataService.find(
                queryEq {
                    where {
                        this.personId.eq(personId) and
                                status.inList(listOf(DRAFT)) and
                                caseId.inList(listOf(taskCaseId)) and
                                this.appointmentId.inList(listOf(appointmentId))
                    }.orderBy { createdAt }.sortOrder { desc }
                }
            )
        } returns listOf(prescriptionModelWithAppointment).success()

        coEvery { healthPlanTaskGroupService.findByIds(listOf(group.id!!)) } returns listOf(group).success()
        coEvery { staffService.findByList(listOf(staff.id)) } returns listOf(staff).success()

        coEvery {
            adherenceValidationService.getByTaskIds(listOf(prescriptionModelWithAppointment))
        } returns emptyList<AdherenceByTaskId>().success()

        val result = healthPlanTaskService.getByPerson(
            personId,
            mapOf(
                "status" to listOf("DRAFT"),
                "case_id" to listOf(taskCaseId.toString()),
                "appointment_id" to listOf(appointmentId.toString()),
            ),
        )

        assertThat(result).isSuccessWithData(taskTransportWithAppointmentId)

        coVerifyOnce { healthPlanTaskDataService.find(any()) }
        coVerifyOnce { healthPlanTaskGroupService.findByIds(any()) }
        coVerifyOnce { staffService.findByList(any()) }
        coVerify { locationService wasNot called }
    }

    @Test
    fun `#findByPersonAndFilters returns HealthPlanTasks filtered by all filters`() = runBlocking {
        val personId = personId
        val appointmentId = appointmentId
        val releasedAtGreater = LocalDateTime.now()
        val dueDateGreater = LocalDate.now()

        coEvery {
            healthPlanTaskDataService.find(
                queryEq {
                    where {
                        this.personId.eq(personId) and
                                status.inList(listOf(DRAFT)) and
                                type.inList(listOf(PRESCRIPTION)) and
                                groupId.inList(listOf(prescriptionModel.groupId!!)) and
                                releasedByStaffId.inList(listOf(staff.id)) and
                                caseId.inList(listOf(taskCaseId)) and
                                this.appointmentId.inList(listOf(appointmentId)) and
                                this.dueDate.greaterEq(dueDateGreater) and
                                this.releasedAt.greaterEq(releasedAtGreater)

                    }.orderBy { createdAt }.sortOrder { desc }
                }
            )
        } returns listOf(prescriptionModel).success()

        coEvery { healthPlanTaskGroupService.findByIds(listOf(group.id!!)) } returns listOf(group).success()
        coEvery { staffService.findByList(listOf(staff.id)) } returns listOf(staff).success()

        coEvery {
            adherenceValidationService.getByTaskIds(listOf(prescriptionModel))
        } returns emptyList<AdherenceByTaskId>().success()

        val result = healthPlanTaskService.findByPersonAndFilters(
            personId,
            HealthPlanTaskFilters(
                statuses = listOf(DRAFT),
                types = listOf(PRESCRIPTION),
                groups = listOf(prescriptionModel.groupId!!),
                releasedByStaffIds = listOf(staff.id),
                caseIds = listOf(taskCaseId),
                appointmentIds = listOf(appointmentId),
                dueDateGreater = dueDateGreater,
                releasedAtGreater = releasedAtGreater
            ),
        )
        assertThat(result).isSuccessWithData(listOf(prescriptionModel))

        coVerifyOnce { healthPlanTaskDataService.find(any()) }
        coVerify { healthPlanTaskGroupService wasNot called }
        coVerify { staffService wasNot called }
        coVerify { kafkaProducer wasNot called }
        coVerify { documentPrinterService wasNot called }
        coVerify { locationService wasNot called }
        coVerify { adherenceValidationService wasNot called }
    }

    @Test
    fun `#findByPersonAndCreatedByList should return a list of tasks`() = runBlocking {
        val createdBy = TaskUpdatedBy(id = RangeUUID.generate(), source = SYSTEM)
        val createdBy2 = TaskUpdatedBy(id = RangeUUID.generate(), source = SYSTEM)

        val createdByList = listOf(createdBy, createdBy2)

        val task1 = prescriptionModel.copy(createdBy = createdBy)
        val task2 = testRequest.copy(createdBy = createdBy2)

        val expected = listOf(task1, task2)

        coEvery {
            healthPlanTaskDataService.find(
                queryEq {
                    where {
                        this.personId.eq(testPersonId)
                            .and(this.createdBy.inList(createdByList))
                            .and(this.status.eq(ACTIVE))
                    }
                }
            )
        } returns expected.success()

        val result = healthPlanTaskService.findByPersonAndCreatedByList(testPersonId, createdByList)

        assertThat(result).isSuccessWithData(expected)

        coVerifyOnce { healthPlanTaskDataService.find(any()) }
    }

    @Test
    fun `#getByDateRangeForActionPlanTaskBackfill finds HealthPlanTasks`() = runBlocking {
        val mockNow = LocalDateTime.now().plusMonths(2)
        val mockYesterday = mockNow.minusDays(1)
        val offset = 10
        val limit = 20

        val doneMoodTask =
            TestModelFactory.buildHealthPlanTask(status = DONE, type = MOOD, date = mockYesterday.plusMinutes(10))
        val activeSleepTask =
            TestModelFactory.buildHealthPlanTask(status = ACTIVE, type = SLEEP, date = mockYesterday.plusHours(10))
        val expectedResponse = listOf(doneMoodTask, activeSleepTask)

        mockkStatic(LocalDateTime::class) {
            coEvery { LocalDateTime.now() } returns mockNow
            coEvery {
                healthPlanTaskDataService.find(
                    queryEq {
                        where {
                            this.type.inList(validHealthPlanTypes)
                                .and(this.status.inList(actionPlanTaskBackfillStatusList))
                                .and(this.createdAt.greaterEq(mockYesterday))
                                .and(this.createdAt.lessEq(mockNow))
                        }.orderBy { createdAt }
                            .sortOrder { asc }
                            .offset { offset }
                            .limit { limit }
                    }
                )
            } returns expectedResponse.success()

            val result =
                healthPlanTaskService.getByDateRangeForActionPlanTaskBackfill(mockYesterday, mockNow, offset, limit)

            assertThat(result).isSuccessWithData(expectedResponse)

            coVerifyOnce { healthPlanTaskDataService.find(any()) }
            coVerify { kafkaProducer wasNot called }
            coVerify { documentPrinterService wasNot called }
            coVerify { healthPlanTaskGroupService wasNot called }
            coVerify { staffService wasNot called }
            coVerify { locationService wasNot called }
            coVerify { adherenceValidationService wasNot called }
        }
    }

    @Test
    fun `#getByPersonForActionPlanTaskBackfill finds HealthPlanTasks`() = runBlocking {
        val personId = personId
        val offset = 10
        val limit = 20

        val doneMoodTask = TestModelFactory.buildHealthPlanTask(status = DONE, type = MOOD)
        val activeSleepTask = TestModelFactory.buildHealthPlanTask(status = ACTIVE, type = SLEEP)
        val expectedResponse = listOf(prescriptionModel, doneMoodTask, activeSleepTask)

        coEvery {
            healthPlanTaskDataService.find(
                queryEq {
                    where {
                        this.type.inList(validHealthPlanTypes)
                            .and(this.personId.eq(personId))
                            .and(this.status.inList(actionPlanTaskBackfillStatusList))
                    }.orderBy { createdAt }
                        .sortOrder { desc }
                        .offset { offset }
                        .limit { limit }
                }
            )
        } returns expectedResponse.success()

        val result = healthPlanTaskService.getByPersonForActionPlanTaskBackfill(personId, offset, limit)

        assertThat(result).isSuccessWithData(expectedResponse)

        coVerifyOnce { healthPlanTaskDataService.find(any()) }
        coVerify { kafkaProducer wasNot called }
        coVerify { documentPrinterService wasNot called }
        coVerify { healthPlanTaskGroupService wasNot called }
        coVerify { staffService wasNot called }
        coVerify { locationService wasNot called }
        coVerify { adherenceValidationService wasNot called }
    }

    @Test
    fun `#getByPerson should return error`() = runBlocking {
        coEvery { healthPlanTaskDataService.find(any()) } returns NotFoundException("task_not_found").failure()

        val result = healthPlanTaskService.getByPerson(personId)
        assertThat(result).isFailureOfType(NotFoundException::class)

        coVerifyOnce { healthPlanTaskDataService.find(any()) }
        coVerify { kafkaProducer wasNot called }
        coVerify { documentPrinterService wasNot called }
        coVerify { healthPlanTaskGroupService wasNot called }
        coVerify { staffService wasNot called }
        coVerify { locationService wasNot called }
        coVerify { adherenceValidationService wasNot called }
    }

    @Test
    fun `#update should check conflicts and create the new task by empty id`() = mockLocalDateTime { date ->
        mockRangeUUID { uuid ->
            val taskTransport = prescriptionTransport.copy(
                id = null,
                status = CREATE
            )

            val expectedModel = prescriptionModel.copy(
                id = uuid,
                updatedAt = date
            ).fillDate()

            coEvery {
                healthPlanTaskDataService.find(
                    queryEq {
                        where { this.id.inList(emptyList()) }
                    }
                )
            } returns emptyList<HealthPlanTask>().success()

            coEvery { healthPlanTaskDataService.add(expectedModel.copy(deadline = Deadline(unit = PeriodUnit.DAY, quantity = 30)).fillDate()) } returns prescriptionModel.success()
            coEvery { kafkaProducer.produce(matchEvent(event), prescriptionTransport.id.toString()) } returns mockk()

            val result = healthPlanTaskService.updateAll(listOf(taskTransport), staff)
            assertThat(result).isSuccessWithData(listOf(prescriptionModel))

            coVerifyOnce { healthPlanTaskDataService.add(any()) }
            coVerify(exactly = 0) { healthPlanTaskDataService.update(any()) }
            coVerify(exactly = 0) { healthPlanTaskDataService.find(any()) }
            coVerify { kafkaProducer wasNot called }
            coVerify { documentPrinterService wasNot called }
            coVerify { healthPlanTaskGroupService wasNot called }
            coVerify { staffService wasNot called }
            coVerify { locationService wasNot called }
            coVerify { adherenceValidationService wasNot called }
        }
    }

    @Test
    fun `#update should check conflicts and create the new task by status`() = mockLocalDateTime { date ->
        val model = prescriptionModel.copy(id = RangeUUID.generate(), updatedAt = date).fillDate()
        val transport = prescriptionTransport.copy(id = model.id, status = CREATE)

        coEvery {
            healthPlanTaskDataService.find(queryEq { where { id.inList(listOf(model.id)) } })
        } returns emptyList<HealthPlanTask>().success()

        coEvery { healthPlanTaskDataService.add(model.copy(deadline = Deadline(unit = PeriodUnit.DAY, quantity = 30)).fillDate()) } returns model.success()

        val result = healthPlanTaskService.updateAll(listOf(transport), staff)
        assertThat(result).isSuccessWithData(listOf(model))

        coVerifyOnce { healthPlanTaskDataService.find(any()) }
        coVerifyOnce { healthPlanTaskDataService.add(any()) }
        coVerify { kafkaProducer wasNot called }
        coVerify { documentPrinterService wasNot called }
        coVerify { healthPlanTaskGroupService wasNot called }
        coVerify { staffService wasNot called }
        coVerify { locationService wasNot called }
        coVerify { adherenceValidationService wasNot called }
    }

    @Test
    fun `#getByGroup should get HealthPlanTasks by group id`() = runBlocking {
        coEvery {
            healthPlanTaskDataService.find(
                queryEq {
                    where {
                        this.groupId.eq(prescriptionModel.groupId!!)
                            .and(this.status.inList(listOf(DRAFT, ACTIVE, DONE)))
                    }.orderBy { createdAt }.sortOrder { asc }
                }
            )
        } returns listOf(prescriptionModel).success()

        val result = healthPlanTaskService.getByGroup(prescriptionModel.groupId!!)
        assertThat(result).isSuccessWithData(listOf(prescriptionModel))

        coVerifyOnce { healthPlanTaskDataService.find(any()) }
        coVerify { kafkaProducer wasNot called }
        coVerify { documentPrinterService wasNot called }
        coVerify { healthPlanTaskGroupService wasNot called }
        coVerify { staffService wasNot called }
        coVerify { locationService wasNot called }
        coVerify { adherenceValidationService wasNot called }
    }

    @Test
    fun `#update should not update if has invalid status change`() = runBlocking {
        val model = prescriptionModel.copy(status = DONE)

        val transport = prescriptionTransport.copy(status = DELETED)

        coEvery { healthPlanTaskDataService.get(transport.id!!) } returns model.success()
        coEvery { healthPlanTaskGroupService.get(group.id!!) } returns group.success()
        coEvery { staffService.findByList(model.requestersStaffIds.toList()) } returns listOf(staff).success()
        coEvery { kafkaProducer.produce(event) } returns mockk()

        val result = healthPlanTaskService.update(model, staff.id)
        assertThat(result).isSuccessWithData(transport.copy(status = DONE, memberCanInit = false))

        coVerifyOnce { healthPlanTaskDataService.get(any()) }
        coVerifyOnce { healthPlanTaskGroupService.get(any()) }
        coVerifyOnce { staffService.findByList(any()) }
        coVerify { kafkaProducer wasNot called }
        coVerify { documentPrinterService wasNot called }
        coVerify { locationService wasNot called }
        coVerify { adherenceValidationService wasNot called }
    }

    @Test
    fun `#update should update if has valid status change`() = mockLocalDateTime { date ->
        val expectedModel = prescriptionModel.copy(status = ACTIVE).fillDate()

        val transport = prescriptionTransport.copy(
            status = ACTIVE,
            memberCanInit = true,
            releasedAt = date.toString(),
            deadline = expectedModel.deadline,
            start = expectedModel.start,
            releasedBy = prescriptionTransport.releasedBy?.copy(requestedAt = date.toString())
        )

        val event = event.copy(
            task = expectedModel,
            staffId = staff.id,
            previousStatus = DRAFT
        )

        coEvery { healthPlanTaskDataService.get(prescriptionModel.id) } returns prescriptionModel.success()
        coEvery { healthPlanTaskDataService.update(expectedModel.copy(deadline = Deadline(unit = PeriodUnit.DAY, quantity = 30)).fillDate()) } returns expectedModel.success()
        coEvery { healthPlanTaskGroupService.get(prescriptionModel.groupId!!) } returns group.success()
        coEvery { staffService.findByList(expectedModel.requestersStaffIds.toList()) } returns listOf(staff).success()
        coEvery { kafkaProducer.produce(matchEvent(event), expectedModel.id.toString()) } returns mockk()

        val result = healthPlanTaskService.update(expectedModel, staff.id)
        assertThat(result).isSuccessWithData(transport)

        coVerifyOnce { healthPlanTaskDataService.get(any()) }
        coVerifyOnce { healthPlanTaskDataService.update(any()) }
        coVerifyOnce { staffService.findByList(any()) }
        coVerifyOnce { healthPlanTaskGroupService.get(any()) }
        coVerifyOnce { kafkaProducer.produce(any(), any()) }
        coVerify { documentPrinterService wasNot called }
        coVerify { locationService wasNot called }
        coVerify { adherenceValidationService wasNot called }
    }

    @Test
    fun `#acknowledge should mark task as acknowledged by the member`() = runBlocking {
        val acknowledgedAt = LocalDateTime.of(2020, 12, 15, 0, 0)

        val taskId = prescriptionModel.id
        val prescriptionModel = prescriptionModel.copy(status = ACTIVE)
        val expectedTask = prescriptionModel.copy(acknowledgedAt = acknowledgedAt)
        val event = event.copy(
            task = expectedTask,
            staffId = null,
            previousStatus = null
        )

        coEvery { healthPlanTaskDataService.get(taskId) } returns prescriptionModel.success()
        coEvery { healthPlanTaskDataService.update(expectedTask) } returns expectedTask.success()
        coEvery { kafkaProducer.produce(matchEvent(event), taskId.toString()) } returns mockk()

        val result = healthPlanTaskService.acknowledge(taskId, acknowledgedAt)
        assertThat(result).isSuccessWithData(expectedTask)

        coVerifyOnce { healthPlanTaskDataService.get(any()) }
        coVerifyOnce { healthPlanTaskDataService.update(any()) }
        coVerifyOnce { kafkaProducer.produce(any(), any()) }
        coVerify { documentPrinterService wasNot called }
        coVerify { healthPlanTaskGroupService wasNot called }
        coVerify { staffService wasNot called }
        coVerify { locationService wasNot called }
        coVerify { adherenceValidationService wasNot called }
    }

    @Test
    fun `#acknowledgeTasks should mark task as acknowledged`() = runBlocking {
        val acknowledgedAt = LocalDateTime.of(2020, 12, 15, 0, 0)

        val taskId = prescriptionModel.id
        val prescriptionModel = prescriptionModel.copy(status = ACTIVE)
        val expectedTask = prescriptionModel.copy(acknowledgedAt = acknowledgedAt)

        coEvery {
            healthPlanTaskDataService.find(
                queryEq {
                    where { id.inList(listOf(taskId)) }
                }
            )
        } returns listOf(prescriptionModel).success()
        coEvery { healthPlanTaskDataService.updateList(listOf(expectedTask)) } returns listOf(expectedTask).success()

        val result = healthPlanTaskService.acknowledgeTasks(listOf(taskId), acknowledgedAt)
        assertThat(result).isSuccessWithData(listOf(expectedTask))

        coVerifyOnce { healthPlanTaskDataService.find(any()) }
        coVerifyOnce { healthPlanTaskDataService.updateList(any()) }
        coVerify { documentPrinterService wasNot called }
        coVerify { healthPlanTaskGroupService wasNot called }
        coVerify { staffService wasNot called }
        coVerify { locationService wasNot called }
        coVerify { adherenceValidationService wasNot called }
        coVerify { kafkaProducer wasNot called }
    }

    @Test
    fun `#init sets the time when member initiated the task and triggers upserted event`() = runBlocking {
        val initiatedAt = LocalDateTime.of(2021, 6, 6, 14, 0)
        val task = TestModelFactory.buildHealthPlanTask(
            status = ACTIVE,
            initiatedByMemberAt = null,
            type = MOOD,
            deadline = Deadline(unit = PeriodUnit.CONTINUOUS, quantity = 0),
            start = StartType.IMMEDIATE,
        )
        val initiatedTask = task.copy(initiatedByMemberAt = initiatedAt)

        val event = HealthPlanTaskUpsertedEvent(
            task = initiatedTask,
            staffId = null,
            previousStatus = null
        )

        coEvery { healthPlanTaskDataService.get(task.id) } returns task.success()
        coEvery { healthPlanTaskDataService.update(initiatedTask) } returns initiatedTask.success()
        coEvery { kafkaProducer.produce(matchEvent(event), initiatedTask.id.toString()) } returns mockk()

        val result = healthPlanTaskService.init(task.id, initiatedAt)
        assertThat(result).isSuccessWithData(initiatedTask)

        coVerifyOnce { healthPlanTaskDataService.get(any()) }
        coVerifyOnce { healthPlanTaskDataService.update(any()) }
        coVerifyOnce { kafkaProducer.produce(any(), any()) }
        coVerify { documentPrinterService wasNot called }
        coVerify { healthPlanTaskGroupService wasNot called }
        coVerify { staffService wasNot called }
        coVerify { locationService wasNot called }
        coVerify { adherenceValidationService wasNot called }
    }

    @Test
    fun `#init saves the expected date when the medicine will end for prescription tasks`() = runBlocking {
        val initiatedAt = LocalDateTime.of(2021, 6, 6, 14, 0)
        val task = TestModelFactory.buildHealthPlanTaskPrescription().copy(
            status = ACTIVE,
            initiatedByMemberAt = null,
            deadline = Deadline(quantity = 0, unit = PeriodUnit.CONTINUOUS),
            frequency = Frequency(
                type = FrequencyType.QUANTITY_IN_PERIOD,
                unit = PeriodUnit.HOUR,
                quantity = 6
            ),
            content = mapOf(
                "dose" to Dose(MedicineUnit.PILLS, 2f),
                "action" to ActionType.TAKE,
                "routeOfAdministration" to RouteOfAdministration.ORAL,
                "medicine" to PrescriptionMedicine(
                    name = "NOVALGINA",
                    drug = "DIPIRONA",
                    type = PrescriptionMedicineType.SPECIAL,
                    unit = MedicineUnit.PILLS,
                    quantity = "10.0"
                ),
                "packing" to 2
            )
        ).specialize<Prescription>().generalize()
        val initiatedTask = task.copy(initiatedByMemberAt = initiatedAt)
        val withMedicineEndingTask = initiatedTask.specialize<Prescription>()
            .copy(medicineEndAt = initiatedAt.plusDays(2).toLocalDate().atEndOfTheDay())
            .generalize()

        val event = HealthPlanTaskUpsertedEvent(
            task = initiatedTask,
            staffId = null,
            previousStatus = null
        )

        coEvery { healthPlanTaskDataService.get(task.id) } returns task.success()
        coEvery { healthPlanTaskDataService.update(initiatedTask) } returns initiatedTask.success()
        coEvery { healthPlanTaskDataService.update(withMedicineEndingTask) } returns withMedicineEndingTask.success()
        coEvery { kafkaProducer.produce(matchEvent(event), initiatedTask.id.toString()) } returns mockk()

        val result = healthPlanTaskService.init(task.id, initiatedAt)
        assertThat(result).isSuccessWithData(withMedicineEndingTask)

        coVerifyOnce { healthPlanTaskDataService.get(any()) }
        coVerify(exactly = 2) { healthPlanTaskDataService.update(any()) }
        coVerifyOnce { kafkaProducer.produce(any(), any()) }
        coVerify { documentPrinterService wasNot called }
        coVerify { healthPlanTaskGroupService wasNot called }
        coVerify { staffService wasNot called }
        coVerify { locationService wasNot called }
        coVerify { adherenceValidationService wasNot called }
    }

    @Test
    fun `#init returns an error if task cannot be initiated`() = runBlocking {
        val task = prescriptionModel.copy(status = ACTIVE, initiatedByMemberAt = LocalDateTime.now())

        coEvery { healthPlanTaskDataService.get(task.id) } returns task.success()

        val result = healthPlanTaskService.init(task.id, LocalDateTime.now())
        assertThat(result).isFailureOfType(InvalidArgumentException::class)

        coVerifyOnce { healthPlanTaskDataService.get(any()) }
        coVerify { kafkaProducer wasNot called }
        coVerify { documentPrinterService wasNot called }
        coVerify { healthPlanTaskGroupService wasNot called }
        coVerify { staffService wasNot called }
        coVerify { locationService wasNot called }
        coVerify { adherenceValidationService wasNot called }
    }

    @Test
    fun `#deleteByMember updates task as deleted by member and triggers upserted event`() = mockLocalDateTime { date ->
        val task = prescriptionModel.copy(
            status = ACTIVE,
            finishedAt = null,
            finishedBy = null
        )
        val deletedTask = task.copy(
            status = DELETED_BY_MEMBER,
            finishedAt = date,
            finishedBy = TaskUpdatedBy(source = MEMBER)
        )

        val event = HealthPlanTaskUpsertedEvent(
            task = deletedTask,
            staffId = null,
            previousStatus = ACTIVE
        )

        coEvery { healthPlanTaskDataService.get(task.id) } returns task.success()
        coEvery { healthPlanTaskDataService.update(deletedTask) } returns deletedTask.success()
        coEvery { kafkaProducer.produce(matchEvent(event), deletedTask.id.toString()) } returns mockk()

        val result = healthPlanTaskService.deleteByMember(task.id)
        assertThat(result).isSuccessWithData(deletedTask)

        coVerifyOnce { healthPlanTaskDataService.get(any()) }
        coVerifyOnce { healthPlanTaskDataService.update(any()) }
        coVerifyOnce { kafkaProducer.produce(any(), any()) }
        coVerify { documentPrinterService wasNot called }
        coVerify { healthPlanTaskGroupService wasNot called }
        coVerify { staffService wasNot called }
        coVerify { locationService wasNot called }
        coVerify { adherenceValidationService wasNot called }
    }

    @Test
    fun `#deleteByMember does nothing if task status is DONE state`() = runBlocking {
        val task = TestModelFactory.buildHealthPlanTask(status = DONE)

        coEvery { healthPlanTaskDataService.get(task.id) } returns task.success()

        val result = healthPlanTaskService.deleteByMember(task.id)
        assertThat(result).isFailureOfType(InvalidArgumentException::class)

        coVerifyOnce { healthPlanTaskDataService.get(any()) }
        coVerify { kafkaProducer wasNot called }
        coVerify { documentPrinterService wasNot called }
        coVerify { healthPlanTaskGroupService wasNot called }
        coVerify { staffService wasNot called }
        coVerify { locationService wasNot called }
        coVerify { adherenceValidationService wasNot called }
    }

    @Test
    fun `#autoCompleteInitiated update only tasks initiated and with past deadline`() = runBlocking {
        mockkStatic(LocalDateTime::class) {
            val elevenDaysAgo = LocalDateTime.of(2021, 6, 6, 10, 0)
            val tenDaysAgo = LocalDateTime.of(2021, 6, 7, 12, 0)
            val nineDaysAgo = LocalDateTime.of(2021, 6, 8, 18, 0)
            val now = LocalDateTime.of(2021, 6, 17, 16, 0)

            val tenDaysDeadline = Deadline(unit = PeriodUnit.DAY, quantity = 10)

            val taskDeadlineYesterday = TestModelFactory.buildHealthPlanTask().copy(
                id = RangeUUID.generate(),
                initiatedByMemberAt = elevenDaysAgo,
                deadline = tenDaysDeadline,
            )
            val taskDeadlineToday = TestModelFactory.buildHealthPlanTask().copy(
                id = RangeUUID.generate(),
                initiatedByMemberAt = tenDaysAgo,
                deadline = tenDaysDeadline,
            )
            val taskDeadlineTomorrow = TestModelFactory.buildHealthPlanTask().copy(
                id = RangeUUID.generate(),
                initiatedByMemberAt = nineDaysAgo,
                deadline = tenDaysDeadline
            )

            val updatedDeadlineYesterday = taskDeadlineYesterday.copy(
                status = DONE,
            )
            val event = HealthPlanTaskUpsertedEvent(
                task = updatedDeadlineYesterday,
                staffId = null,
                previousStatus = DRAFT
            )

            coEvery { LocalDateTime.now() } returns now

            coEvery {
                healthPlanTaskDataService.find(
                    queryEq {
                        where {
                            this.status.eq(ACTIVE).and(this.initiatedByMemberAt.isNotNull())
                        }.orderBy { status }
                            .sortOrder { asc }
                    }
                )
            } returns listOf(
                taskDeadlineYesterday, taskDeadlineToday, taskDeadlineTomorrow
            ).success()

            coEvery { healthPlanTaskDataService.update(updatedDeadlineYesterday) } returns updatedDeadlineYesterday.success()
            coEvery { kafkaProducer.produce(matchEvent(event), updatedDeadlineYesterday.id.toString()) } returns mockk()

            val result = healthPlanTaskService.completeInitiated()
            assertThat(result).isSuccessWithData(listOf(updatedDeadlineYesterday))

            coVerifyOnce { healthPlanTaskDataService.find(any()) }
            coVerifyOnce { healthPlanTaskDataService.update(any()) }
            coVerifyOnce { kafkaProducer.produce(any(), any()) }
            coVerify { documentPrinterService wasNot called }
            coVerify { healthPlanTaskGroupService wasNot called }
            coVerify { staffService wasNot called }
            coVerify { locationService wasNot called }
            coVerify { adherenceValidationService wasNot called }
        }
    }

    @Test
    fun `#completeTask throw error when get error producing event HealthPlanTaskUpsertedEvent`() =
        mockLocalDateTime { date ->
            val task = TestModelFactory.buildHealthPlanTask().copy(
                status = ACTIVE,
                finishedAt = null,
                finishedBy = null,
            )
            val completedTask = task.copy(
                status = DONE,
                finishedAt = date,
                finishedBy = TaskUpdatedBy(source = SYSTEM),
            )

            coEvery { healthPlanTaskDataService.update(completedTask) } returns completedTask.success()
            coEvery { kafkaProducer.produce(any(), any()) } throws Exception()

            val result = healthPlanTaskService.completeTask(task)
            assertThat(result).isFailureOfType(Exception::class)

            coVerifyOnce { healthPlanTaskDataService.update(any()) }
            coVerifyOnce { kafkaProducer.produce(any(), any()) }
            coVerify { documentPrinterService wasNot called }
            coVerify { healthPlanTaskGroupService wasNot called }
            coVerify { staffService wasNot called }
            coVerify { locationService wasNot called }
            coVerify { adherenceValidationService wasNot called }
        }

    @Test
    fun `#completeTask does not update an already completed task by System`() = runBlocking {
        val task = TestModelFactory.buildHealthPlanTask().copy(
            status = DONE,
            finishedBy = TaskUpdatedBy(source = SYSTEM),
        )

        val result = healthPlanTaskService.completeTask(task)
        assertThat(result).isSuccessWithData(task)

        coVerify { healthPlanTaskDataService wasNot called }
        coVerify { kafkaProducer wasNot called }
        coVerify { documentPrinterService wasNot called }
        coVerify { healthPlanTaskGroupService wasNot called }
        coVerify { staffService wasNot called }
        coVerify { locationService wasNot called }
        coVerify { adherenceValidationService wasNot called }
    }

    @Test
    fun `#completeTask updates a task already DONE when system override it`() = mockLocalDateTime { date ->
        val task = TestModelFactory.buildHealthPlanTask().copy(
            status = DONE,
            finishedAt = null,
            finishedBy = TaskUpdatedBy(source = MEMBER)
        )
        val completedTask = task.copy(
            status = DONE,
            finishedAt = date,
            finishedBy = TaskUpdatedBy(source = SYSTEM)
        )

        val event = HealthPlanTaskUpsertedEvent(
            task = completedTask,
            staffId = null,
            previousStatus = DONE
        )

        coEvery { healthPlanTaskDataService.update(completedTask) } returns completedTask.success()
        coEvery { kafkaProducer.produce(matchEvent(event), completedTask.id.toString()) } returns mockk()

        val result = healthPlanTaskService.completeTask(task)
        assertThat(result).isSuccessWithData(completedTask)

        coVerifyOnce { healthPlanTaskDataService.update(any()) }
        coVerifyOnce { kafkaProducer.produce(any(), any()) }
        coVerify { documentPrinterService wasNot called }
        coVerify { healthPlanTaskGroupService wasNot called }
        coVerify { staffService wasNot called }
        coVerify { locationService wasNot called }
        coVerify { adherenceValidationService wasNot called }
    }

    @Test
    fun `#activateTask updates a task to ACTIVE and not finished`() = runBlocking {
        val completedTask = TestModelFactory.buildHealthPlanTask().copy(
            status = DONE,
            finishedAt = LocalDateTime.of(2021, 6, 18, 15, 0),
            finishedBy = TaskUpdatedBy(source = SYSTEM),
        )
        val activeTask = completedTask.copy(
            status = ACTIVE,
            finishedAt = null,
            finishedBy = null,
        )

        val event = HealthPlanTaskUpsertedEvent(
            task = activeTask,
            staffId = null,
            previousStatus = DONE
        )

        coEvery { healthPlanTaskDataService.update(activeTask) } returns activeTask.success()
        coEvery { kafkaProducer.produce(matchEvent(event), activeTask.id.toString()) } returns mockk()

        val result = healthPlanTaskService.activateTask(completedTask)
        assertThat(result).isSuccessWithData(activeTask)

        coVerifyOnce { healthPlanTaskDataService.update(any()) }
        coVerifyOnce { kafkaProducer.produce(any(), any()) }
        coVerify { documentPrinterService wasNot called }
        coVerify { healthPlanTaskGroupService wasNot called }
        coVerify { staffService wasNot called }
        coVerify { locationService wasNot called }
        coVerify { adherenceValidationService wasNot called }
    }

    @Test
    fun `#activateTask does not update an active task`() = runBlocking {
        val task = TestModelFactory.buildHealthPlanTask().copy(status = ACTIVE)

        val result = healthPlanTaskService.activateTask(task)
        assertThat(result).isSuccessWithData(task)

        coVerify { healthPlanTaskDataService wasNot called }
        coVerify { healthPlanTaskDataService wasNot called }
        coVerify { kafkaProducer wasNot called }
        coVerify { documentPrinterService wasNot called }
        coVerify { healthPlanTaskGroupService wasNot called }
        coVerify { staffService wasNot called }
        coVerify { locationService wasNot called }
        coVerify { adherenceValidationService wasNot called }
    }

    @Test
    fun `#updateTask should update HealthPlanTask and publish event`() = runBlocking {
        val updatedTask = prescriptionModel.copy(
            title = "${prescriptionModel.title} updated",
            content = mapOf("professional" to "another_professional"),
            status = ACTIVE
        )

        val event = HealthPlanTaskUpsertedEvent(
            task = updatedTask,
            staffId = null,
            previousStatus = null
        )

        coEvery { healthPlanTaskDataService.update(updatedTask) } returns updatedTask.success()
        coEvery { kafkaProducer.produce(matchEvent(event), updatedTask.id.toString()) } returns mockk()

        val result = healthPlanTaskService.updateTask(updatedTask)
        assertThat(result).isSuccessWithData(updatedTask)

        coVerifyOnce { healthPlanTaskDataService.update(updatedTask) }
        coVerifyOnce { kafkaProducer.produce(any(), any()) }
        coVerify { documentPrinterService wasNot called }
        coVerify { healthPlanTaskGroupService wasNot called }
        coVerify { staffService wasNot called }
        coVerify { locationService wasNot called }
        coVerify { adherenceValidationService wasNot called }
    }

    @Test
    fun `#updateTask should update HealthPlanTask and not publish event`() = runBlocking {
        val updatedTask = prescriptionModel.copy(
            title = "${prescriptionModel.title} updated",
            content = mapOf("professional" to "another_professional"),
            status = ACTIVE
        )
        coEvery { healthPlanTaskDataService.update(updatedTask) } returns updatedTask.success()

        val result = healthPlanTaskService.updateTask(updatedTask, false)

        assertThat(result).isSuccessWithData(updatedTask)

        coVerifyOnce { healthPlanTaskDataService.update(updatedTask) }
        coVerify { kafkaProducer wasNot called }
        coVerify { documentPrinterService wasNot called }
        coVerify { healthPlanTaskGroupService wasNot called }
        coVerify { staffService wasNot called }
        coVerify { locationService wasNot called }
        coVerify { adherenceValidationService wasNot called }
    }

    @Test
    fun `#getAllActiveByPersonAndType should return a list of tasks`() = runBlocking {
        coEvery {
            healthPlanTaskDataService.find(
                queryEq {
                    where {
                        this.personId.eq(prescriptionModel.personId)
                            .and(this.type.eq(PRESCRIPTION))
                            .and(this.status.eq(ACTIVE))
                    }
                }
            )
        } returns listOf(prescriptionModel).success()

        val result = healthPlanTaskService.getAllActiveByPersonAndType(
            personId = personId,
            type = PRESCRIPTION,
        )

        assertThat(result).isSuccessWithData(listOf(prescriptionModel))

        coVerifyOnce { healthPlanTaskDataService.find(any()) }
        coVerify { kafkaProducer wasNot called }
        coVerify { documentPrinterService wasNot called }
        coVerify { healthPlanTaskGroupService wasNot called }
        coVerify { staffService wasNot called }
        coVerify { locationService wasNot called }
        coVerify { adherenceValidationService wasNot called }
    }

    @Test
    fun `#getAllActiveByPersonAndTypeAndDueDate should return a list of tasks`() = runBlocking {
        val referralModel = buildHealthPlanTaskReferral()
        val dueDate = LocalDate.now()
        coEvery {
            healthPlanTaskDataService.find(
                queryEq {
                    where {
                        this.personId.eq(prescriptionModel.personId)
                            .and(this.type.eq(REFERRAL))
                            .and(this.status.eq(ACTIVE))
                            .and(this.dueDate.greaterEq(dueDate))
                    }
                }
            )
        } returns listOf(referralModel).success()

        val result = healthPlanTaskService.getAllActiveByPersonAndTypeAndDueDate(
            personId = personId,
            type = REFERRAL,
            dueDate
        )

        assertThat(result).isSuccessWithData(listOf(referralModel))

        coVerifyOnce { healthPlanTaskDataService.find(any()) }
        coVerify { kafkaProducer wasNot called }
        coVerify { documentPrinterService wasNot called }
        coVerify { healthPlanTaskGroupService wasNot called }
        coVerify { staffService wasNot called }
        coVerify { locationService wasNot called }
        coVerify { adherenceValidationService wasNot called }
    }

    @Test
    fun `#getAllActiveTasksByTypeAndReleasedInterval should return a list of tasks`() = runBlocking {
        coEvery {
            healthPlanTaskDataService.find(
                queryEq {
                    where {
                        this.type.eq(PRESCRIPTION)
                            .and(this.status.eq(ACTIVE))
                            .and(this.releasedAt.greater(dateTimeFromOffset))
                            .and(this.releasedAt.lessEq(dateTimeToOffset))
                    }
                }
            )
        } returns listOf(prescriptionModel).success()

        val result = healthPlanTaskService.getAllActiveTasksByTypeAndReleasedInterval(
            type = PRESCRIPTION,
            dateTimeFromOffset = dateTimeFromOffset,
            dateTimeToOffset = dateTimeToOffset
        )

        assertThat(result).isSuccessWithData(listOf(prescriptionModel))

        coVerifyOnce { healthPlanTaskDataService.find(any()) }
        coVerify { kafkaProducer wasNot called }
        coVerify { documentPrinterService wasNot called }
        coVerify { healthPlanTaskGroupService wasNot called }
        coVerify { staffService wasNot called }
        coVerify { locationService wasNot called }
        coVerify { adherenceValidationService wasNot called }
    }

    @Test
    fun `#publishTasksByDemand should change task status to ACTIVE`() = mockLocalDateTime { date ->
        val taskGroupId = RangeUUID.generate()
        val taskCaseId = RangeUUID.generate()
        val moodTask = TestModelFactory.buildHealthPlanTask().copy(
            personId = personId,
            type = MOOD,
            status = DRAFT,
            groupId = taskGroupId,
            lastRequesterStaffId = staff.id,
            requestersStaffIds = setOf(staff.id),
            content = emptyMap(),
            caseId = taskCaseId
        ).copy(
            caseRecordDetails = listOf(
                CaseRecordDetails(
                    description = DiseaseDetails(
                        type = Disease.Type.CID_10,
                        value = "10"
                    ),
                    severity = CaseSeverity.ONGOING
                )
            )
        )
        val expectedModel = moodTask.copy(
            status = ACTIVE,
            releasedAt = date
        ).fillDate()

        val event = HealthPlanTaskUpsertedEvent(
            task = expectedModel,
            staffId = staff.id,
            previousStatus = DRAFT
        )
        val eventGroup = HealthPlanTaskGroupPublishedEvent(listOf(expectedModel))

        coEvery {
            healthPlanTaskDataService.find(
                queryEq {
                    where {
                        this.caseId.eq(taskCaseId).and(this.type.eq(moodTask.type))
                            .and(this.status.inList(listOf(DRAFT, ACTIVE)))
                    }
                        .orderBy { this.createdAt }
                        .sortOrder { desc }
                }
            )
        } returns listOf(moodTask).success()
        coEvery { healthPlanTaskDataService.update(expectedModel) } returns expectedModel.success()
        coEvery { kafkaProducer.produce(matchEvent(event), expectedModel.id.toString()) } returns mockk()
        coEvery { kafkaProducer.produce(eventGroup) } returns mockk()

        val result = healthPlanTaskService.publishTasksByDemand(
            PublishGroupedDemandRequest(taskCaseId, moodTask.type, staff.id)
        )
        assertThat(result).isSuccessWithData(listOf(expectedModel))

        coVerifyOnce { healthPlanTaskDataService.find(any()) }
        coVerifyOnce { healthPlanTaskDataService.update(any()) }
        coVerify(exactly = 2) { kafkaProducer.produce(any(), any()) }
        coVerify { documentPrinterService wasNot called }
        coVerify { healthPlanTaskGroupService wasNot called }
        coVerify { staffService wasNot called }
        coVerify { locationService wasNot called }
        coVerify { adherenceValidationService wasNot called }
        confirmMocks()
    }

    @Test
    fun `#publishTasksByDemand should change task status to ACTIVE and close query by person_id`() =
        mockLocalDateTime { date ->
            val taskGroupId = RangeUUID.generate()
            val taskCaseId = RangeUUID.generate()
            val moodTask = TestModelFactory.buildHealthPlanTask().copy(
                personId = personId,
                type = MOOD,
                status = DRAFT,
                groupId = taskGroupId,
                lastRequesterStaffId = staff.id,
                requestersStaffIds = setOf(staff.id),
                content = emptyMap(),
                caseId = taskCaseId
            ).copy(
                caseRecordDetails = listOf(
                    CaseRecordDetails(
                        description = DiseaseDetails(
                            type = Disease.Type.CID_10,
                            value = "10"
                        ),
                        severity = CaseSeverity.ONGOING
                    )
                )
            )
            val expectedModel = moodTask.copy(
                status = ACTIVE,
                releasedAt = date
            ).fillDate()

            val event = HealthPlanTaskUpsertedEvent(
                task = expectedModel,
                staffId = staff.id,
                previousStatus = DRAFT
            )
            val eventGroup = HealthPlanTaskGroupPublishedEvent(listOf(expectedModel))

            coEvery {
                healthPlanTaskDataService.find(
                    queryEq {
                        where {
                            this.caseId.eq(taskCaseId) and
                                    this.type.eq(moodTask.type) and
                                    this.status.inList(listOf(DRAFT, ACTIVE)) and
                                    this.personId.eq(moodTask.personId)
                        }
                            .orderBy { this.createdAt }
                            .sortOrder { desc }
                    }
                )
            } returns listOf(moodTask).success()
            coEvery { healthPlanTaskDataService.update(expectedModel) } returns expectedModel.success()
            coEvery { kafkaProducer.produce(matchEvent(event), expectedModel.id.toString()) } returns mockk()
            coEvery { kafkaProducer.produce(eventGroup) } returns mockk()

            val result = healthPlanTaskService.publishTasksByDemand(
                PublishGroupedDemandRequest(
                    caseId = taskCaseId,
                    type = moodTask.type,
                    staffId = staff.id,
                    personId = personId
                )
            )
            assertThat(result).isSuccessWithData(listOf(expectedModel))

            coVerifyOnce { healthPlanTaskDataService.find(any()) }
            coVerifyOnce { healthPlanTaskDataService.update(any()) }
            coVerify(exactly = 2) { kafkaProducer.produce(any(), any()) }
            coVerify { documentPrinterService wasNot called }
            coVerify { healthPlanTaskGroupService wasNot called }
            coVerify { staffService wasNot called }
            coVerify { locationService wasNot called }
            coVerify { adherenceValidationService wasNot called }
            confirmMocks()
        }

    @Test
    fun `#publishTasksByDemand should change task status to ACTIVE (only getting draft tasks)`() =
        mockLocalDateTime { date ->
            withFeatureFlag(FeatureNamespace.HEALTH_PLAN, "health_plan_task_get_by_demand_statuses", listOf("DRAFT")) {
                val taskGroupId = RangeUUID.generate()
                val taskCaseId = RangeUUID.generate()
                val moodTask = TestModelFactory.buildHealthPlanTask().copy(
                    personId = personId,
                    type = MOOD,
                    status = DRAFT,
                    groupId = taskGroupId,
                    lastRequesterStaffId = staff.id,
                    requestersStaffIds = setOf(staff.id),
                    content = emptyMap(),
                    caseId = taskCaseId
                ).copy(
                    caseRecordDetails = listOf(
                        CaseRecordDetails(
                            description = DiseaseDetails(
                                type = Disease.Type.CID_10,
                                value = "10"
                            ),
                            severity = CaseSeverity.ONGOING
                        )
                    )
                )
                val expectedModel = moodTask.copy(
                    status = ACTIVE,
                    releasedAt = date
                ).fillDate()

                val event = HealthPlanTaskUpsertedEvent(
                    task = expectedModel,
                    staffId = staff.id,
                    previousStatus = DRAFT
                )
                val eventGroup = HealthPlanTaskGroupPublishedEvent(listOf(expectedModel))

                coEvery {
                    healthPlanTaskDataService.find(
                        queryEq {
                            where {
                                this.caseId.eq(taskCaseId).and(this.type.eq(moodTask.type))
                                    .and(this.status.inList(listOf(DRAFT)))
                            }
                                .orderBy { this.createdAt }
                                .sortOrder { desc }
                        }
                    )
                } returns listOf(moodTask).success()
                coEvery { healthPlanTaskDataService.update(expectedModel) } returns expectedModel.success()
                coEvery { kafkaProducer.produce(matchEvent(event), expectedModel.id.toString()) } returns mockk()
                coEvery { kafkaProducer.produce(eventGroup) } returns mockk()

                val result = healthPlanTaskService.publishTasksByDemand(
                    PublishGroupedDemandRequest(taskCaseId, moodTask.type, staff.id)
                )
                assertThat(result).isSuccessWithData(listOf(expectedModel))

                coVerifyOnce { healthPlanTaskDataService.find(any()) }
                coVerifyOnce { healthPlanTaskDataService.update(any()) }
                coVerify(exactly = 2) { kafkaProducer.produce(any(), any()) }
                coVerify { documentPrinterService wasNot called }
                coVerify { healthPlanTaskGroupService wasNot called }
                coVerify { staffService wasNot called }
                coVerify { locationService wasNot called }
                coVerify { adherenceValidationService wasNot called }
                confirmMocks()
            }
        }

    @Test
    fun `#publishTasksByDemand returns an error when there is a task without case record and DRAFT`() =
        runBlocking {
            val taskCaseId = RangeUUID.generate()
            val task = TestModelFactory.buildHealthPlanTask().copy(
                caseRecordDetails = emptyList(),
                caseId = taskCaseId
            )

            coEvery {
                healthPlanTaskDataService.find(
                    queryEq {
                        where {
                            this.caseId.eq(taskCaseId).and(this.type.eq(task.type))
                                .and(this.status.inList(listOf(DRAFT, ACTIVE)))
                        }
                            .orderBy { this.createdAt }
                            .sortOrder { desc }
                    }
                )
            } returns listOf(task).success()

            val result = healthPlanTaskService.publishTasksByDemand(
                PublishGroupedDemandRequest(taskCaseId, task.type, staff.id)
            )
            assertThat(result).isFailureOfType(InvalidArgumentException::class)

            coVerifyOnce { healthPlanTaskDataService.find(any()) }
            coVerify { kafkaProducer wasNot called }
            coVerify { documentPrinterService wasNot called }
            coVerify { healthPlanTaskGroupService wasNot called }
            coVerify { staffService wasNot called }
            coVerify { locationService wasNot called }
            coVerify { adherenceValidationService wasNot called }
            confirmMocks()
        }

    @Test
    fun `#publishTasks returns published tasks with status ACTIVE`() = mockLocalDateTime { date ->
        val taskCaseId = RangeUUID.generate()
        val moodTask = TestModelFactory.buildHealthPlanTask().copy(
            personId = personId,
            type = MOOD,
            status = DRAFT,
            lastRequesterStaffId = staff.id,
            requestersStaffIds = setOf(staff.id),
            content = emptyMap(),
            caseId = taskCaseId
        ).copy(
            caseRecordDetails = listOf(
                CaseRecordDetails(
                    caseId = taskCaseId,
                    description = DiseaseDetails(
                        type = Disease.Type.CID_10,
                        value = "10"
                    ),
                    severity = CaseSeverity.ONGOING
                )
            )
        )
        val expectedModel = moodTask.copy(
            status = ACTIVE,
            releasedAt = date
        ).fillDate()

        val event = HealthPlanTaskUpsertedEvent(
            task = expectedModel,
            staffId = staff.id,
            previousStatus = DRAFT
        )
        val eventGroup = HealthPlanTaskGroupPublishedEvent(listOf(expectedModel))

        coEvery { healthPlanTaskDataService.update(expectedModel) } returns expectedModel.success()
        coEvery { kafkaProducer.produce(matchEvent(event), expectedModel.id.toString()) } returns mockk()
        coEvery { kafkaProducer.produce(eventGroup) } returns mockk()

        val result = healthPlanTaskService.publishTasks(
            listOf(moodTask),
            taskCaseId,
            MOOD,
            "token",
            staff.id
        )
        assertThat(result).isSuccessWithData(listOf(expectedModel))

        coVerifyOnce { healthPlanTaskDataService.update(any()) }
        coVerify(exactly = 2) { kafkaProducer.produce(any(), any()) }
        coVerify { documentPrinterService wasNot called }
        coVerify { healthPlanTaskGroupService wasNot called }
        coVerify { staffService wasNot called }
        coVerify { locationService wasNot called }
        coVerify { adherenceValidationService wasNot called }
    }

    @Test
    fun `#publishTasks returns published tasks with status ACTIVE and updated deadline`() = mockLocalDateTime { date ->
        withFeatureFlag(
            FeatureNamespace.HEALTH_PLAN, "should_use_new_health_plan_task_deadline_calculation_flow", true
        ) {
            val taskCaseId = RangeUUID.generate()
            val moodTask = TestModelFactory.buildHealthPlanTask().copy(
                personId = personId,
                type = MOOD,
                status = DRAFT,
                lastRequesterStaffId = staff.id,
                requestersStaffIds = setOf(staff.id),
                content = emptyMap(),
                caseId = taskCaseId
            ).copy(
                caseRecordDetails = listOf(
                    CaseRecordDetails(
                        caseId = taskCaseId,
                        description = DiseaseDetails(
                            type = Disease.Type.CID_10,
                            value = "10"
                        ),
                        severity = CaseSeverity.ONGOING
                    )
                )
            )
            val expectedModel = moodTask.copy(
                status = ACTIVE,
                releasedAt = date
            ).fillDateUsingNewDeadlineInterval()

            val event = HealthPlanTaskUpsertedEvent(
                task = expectedModel,
                staffId = staff.id,
                previousStatus = DRAFT
            )
            val eventGroup = HealthPlanTaskGroupPublishedEvent(listOf(expectedModel))

            coEvery { healthPlanTaskDataService.update(expectedModel) } returns expectedModel.success()
            coEvery { kafkaProducer.produce(matchEvent(event), expectedModel.id.toString()) } returns mockk()
            coEvery { kafkaProducer.produce(eventGroup) } returns mockk()

            val result = healthPlanTaskService.publishTasks(
                listOf(moodTask),
                taskCaseId,
                MOOD,
                "token",
                staff.id
            )
            assertThat(result).isSuccessWithData(listOf(expectedModel))

            coVerifyOnce { healthPlanTaskDataService.update(any()) }
            coVerify(exactly = 2) { kafkaProducer.produce(any(), any()) }
            coVerify { documentPrinterService wasNot called }
            coVerify { healthPlanTaskGroupService wasNot called }
            coVerify { staffService wasNot called }
            coVerify { locationService wasNot called }
            coVerify { adherenceValidationService wasNot called }
        }
    }

    @Test
    fun `#update should update if has valid status change DONE to ACTIVE`() = mockLocalDateTime { date ->
        val model = prescriptionModel.copy(status = DONE)
        val expectedModel = model.copy(status = ACTIVE).fillDate()

        val transport = prescriptionTransport.copy(
            status = ACTIVE,
            memberCanInit = true,
            releasedAt = date.toString(),
            deadline = expectedModel.deadline,
            start = expectedModel.start,
            releasedBy = prescriptionTransport.releasedBy?.copy(requestedAt = date.toString())
        )

        val event = HealthPlanTaskUpsertedEvent(
            task = expectedModel,
            staffId = staff.id,
            previousStatus = DONE
        )

        coEvery { healthPlanTaskDataService.get(model.id) } returns model.success()
        coEvery { healthPlanTaskDataService.update(expectedModel.copy(deadline = Deadline(unit = PeriodUnit.DAY, quantity = 30)).fillDate()) } returns expectedModel.success()
        coEvery { healthPlanTaskGroupService.get(model.groupId!!) } returns group.success()
        coEvery { staffService.findByList(expectedModel.requestersStaffIds.toList()) } returns listOf(staff).success()
        coEvery { kafkaProducer.produce(matchEvent(event), expectedModel.id.toString()) } returns mockk()

        coEvery { locationService.getCities(emptyList()) } returns emptyList<City>().success()

        val result = healthPlanTaskService.update(expectedModel, staff.id)
        assertThat(result).isSuccessWithData(transport)

        coVerifyOnce { healthPlanTaskDataService.get(any()) }
        coVerifyOnce { healthPlanTaskDataService.update(any()) }
        coVerifyOnce { healthPlanTaskGroupService.get(any()) }
        coVerifyOnce { staffService.findByList(any()) }
        coVerifyOnce { kafkaProducer.produce(any(), any()) }
        coVerify { documentPrinterService wasNot called }
        coVerify { locationService wasNot called }
        coVerify { adherenceValidationService wasNot called }
    }

    @Test
    fun `#update should update if has valid status change ACTIVE to DONE`() = mockLocalDateTime { date ->
        val model = prescriptionModel.copy(status = ACTIVE, releasedAt = date)
        val expectedModel = model.copy(status = DONE).fillDate()

        val transport = prescriptionTransport.copy(
            status = DONE,
            memberCanInit = false,
            releasedAt = date.toString(),
            finishedAt = date.toString(),
            deadline = expectedModel.deadline,
            start = expectedModel.start,
            releasedBy = prescriptionTransport.releasedBy?.copy(requestedAt = date.toString())
        )

        val event = HealthPlanTaskUpsertedEvent(
            task = expectedModel,
            staffId = staff.id,
            previousStatus = ACTIVE
        )

        coEvery { healthPlanTaskDataService.get(model.id) } returns model.success()
        coEvery { healthPlanTaskDataService.update(expectedModel.copy(deadline = Deadline(unit = PeriodUnit.DAY, quantity = 30)).fillDate()) } returns expectedModel.success()
        coEvery { healthPlanTaskGroupService.get(group.id!!) } returns group.success()
        coEvery { staffService.findByList(expectedModel.requestersStaffIds.toList()) } returns listOf(staff).success()
        coEvery { kafkaProducer.produce(matchEvent(event), expectedModel.id.toString()) } returns mockk()

        val result = healthPlanTaskService.update(expectedModel, staff.id)
        assertThat(result).isSuccessWithData(transport)

        coVerifyOnce { healthPlanTaskDataService.get(any()) }
        coVerifyOnce { healthPlanTaskDataService.update(any()) }
        coVerifyOnce { healthPlanTaskGroupService.get(any()) }
        coVerifyOnce { staffService.findByList(any()) }
        coVerifyOnce { kafkaProducer.produce(any(), any()) }
        coVerify { documentPrinterService wasNot called }
        coVerify { locationService wasNot called }
        coVerify { adherenceValidationService wasNot called }
    }

    @Test
    fun `#update sets cityId and returns location`() = mockLocalDateTime { date ->
        val saoPaulo = State(
            id = "35",
            name = "São Paulo",
            code = "SP",
        )
        val santos = City(
            id = "3548500",
            name = "Santos",
            state = saoPaulo,
        )

        val model = referral
            .copy(cityId = santos.id)
            .generalize()
            .copy(
                lastRequesterStaffId = staff.id,
                requestersStaffIds = setOf(staff.id),
                releasedByStaffId = staff.id
            )

        val expectedModel = model.copy(status = DONE, finishedAt = date).fillDate()

        val transport = HealthPlanTaskTransportConverter.convert<Referral, ReferralTransport>(
            task = expectedModel.specialize(),
            staffsById = mapOf(staff.id to staff),
            groups = mapOf(group.id!! to group),
            cities = mapOf(santos.id to santos),
        ).copy(status = DONE, memberCanInit = false)

        val event = HealthPlanTaskUpsertedEvent(
            task = expectedModel,
            staffId = staff.id,
            previousStatus = ACTIVE
        )

        coEvery { healthPlanTaskDataService.get(model.id) } returns model.success()
        coEvery { healthPlanTaskDataService.update(expectedModel) } returns expectedModel.success()
        coEvery { staffService.findByList(listOf(staff.id)) } returns listOf(staff).success()
        coEvery { locationService.getCity(santos.id) } returns santos.success()
        coEvery { kafkaProducer.produce(matchEvent(event), model.id.toString()) } returns mockk()

        val result = healthPlanTaskService.update(expectedModel, staff.id)
        assertThat(result).isSuccessWithData(transport)

        coVerifyOnce { healthPlanTaskDataService.get(any()) }
        coVerifyOnce { healthPlanTaskDataService.update(any()) }
        coVerifyOnce { staffService.findByList(any()) }
        coVerifyOnce { locationService.getCity(any()) }
        coVerifyOnce { kafkaProducer.produce(any(), any()) }
        coVerify { healthPlanTaskGroupService wasNot called }
        coVerify { documentPrinterService wasNot called }
        coVerify { adherenceValidationService wasNot called }
    }

    @Test
    fun `#update sets cityId and not returns location when flag is disabled`() = mockLocalDateTime { date ->
        withFeatureFlag(FeatureNamespace.HEALTH_PLAN, "can_load_city_in_ibge", false) {
            val saoPaulo = State(
                id = "35",
                name = "São Paulo",
                code = "SP",
            )
            val santos = City(
                id = "3548500",
                name = "Santos",
                state = saoPaulo,
            )

            val model = referral.copy(
                lastRequesterStaffId = staff.id,
                requestersStaffIds = setOf(staff.id),
                releasedByStaffId = staff.id
            )
                .specialize<Referral>()
                .copy(cityId = santos.id)
                .generalize()

            val expectedModel = model.copy(status = DONE, finishedAt = date).fillDate()

            val transport = HealthPlanTaskTransportConverter.convert<Referral, ReferralTransport>(
                task = expectedModel.specialize(),
                staffsById = mapOf(staff.id to staff),
                groups = mapOf(group.id!! to group),
                cities = emptyMap(),
            ).copy(status = DONE, memberCanInit = false)

            val event = HealthPlanTaskUpsertedEvent(
                task = expectedModel,
                staffId = staff.id,
                previousStatus = ACTIVE
            )

            coEvery { healthPlanTaskDataService.get(model.id) } returns model.success()
            coEvery { healthPlanTaskDataService.update(expectedModel) } returns expectedModel.success()
            coEvery { staffService.findByList(listOf(staff.id)) } returns listOf(staff).success()
            coEvery { kafkaProducer.produce(matchEvent(event), model.id.toString()) } returns mockk()

            val result = healthPlanTaskService.update(expectedModel, staff.id)
            assertThat(result).isSuccessWithData(transport)

            coVerifyOnce { healthPlanTaskDataService.get(any()) }
            coVerifyOnce { healthPlanTaskDataService.update(any()) }
            coVerifyOnce { staffService.findByList(any()) }
            coVerifyOnce { kafkaProducer.produce(any(), any()) }
            coVerify { locationService wasNot called }
            coVerify { healthPlanTaskGroupService wasNot called }
            coVerify { documentPrinterService wasNot called }
            coVerify { adherenceValidationService wasNot called }
        }
    }

    @Test
    fun `#markAsDoneUndone should update status from ACTIVE to DONE`() = mockLocalDateTime { date ->
        val activeModel = prescriptionModel.copy(status = ACTIVE)
        val finishedBy = TaskUpdatedBy(id = staff.id, source = STAFF)
        val doneModel = prescriptionModel.copy(
            status = DONE,
            finishedBy = finishedBy,
            finishedAt = date
        ).fillDate()

        val expected = prescriptionTransport.copy(
            status = DONE,
            finishedBy = TaskRequesterRequest(
                staffId = staff.id,
                name = staff.fullName,
                profileImageUrl = staff.profileImageUrl,
                requestedAt = date.toString()
            ),
            finishedAt = date.toString(),
            deadline = doneModel.deadline,
            start = doneModel.start
        )

        val event = HealthPlanTaskUpsertedEvent(
            task = doneModel,
            staffId = staff.id,
            previousStatus = ACTIVE
        )

        coEvery { healthPlanTaskDataService.get(prescriptionModel.id) } returns activeModel.success()
        coEvery { healthPlanTaskDataService.update(doneModel.copy(deadline = Deadline(unit = PeriodUnit.DAY, quantity = 30)).fillDate()) } returns doneModel.success()
        coEvery { healthPlanTaskGroupService.get(prescriptionModel.groupId!!) } returns group.success()
        coEvery { staffService.findByList(doneModel.requestersStaffIds.toList()) } returns listOf(staff).success()
        coEvery { kafkaProducer.produce(matchEvent(event), doneModel.id.toString()) } returns mockk()

        val result = healthPlanTaskService.markAsDoneUndone(prescriptionModel.id, staff.id)
        assertThat(result).isSuccessWithData(expected)

        coVerifyOnce { healthPlanTaskDataService.get(any()) }
        coVerifyOnce { healthPlanTaskDataService.update(any()) }
        coVerifyOnce { healthPlanTaskGroupService.get(any()) }
        coVerifyOnce { staffService.findByList(any()) }
        coVerifyOnce { kafkaProducer.produce(any(), any()) }
        coVerify { documentPrinterService wasNot called }
        coVerify { locationService wasNot called }
        coVerify { adherenceValidationService wasNot called }
    }

    @Test
    fun `#delete should update task status do DELETED`() = mockLocalDateTime {
        val expectedTask = prescriptionModel.copy(
            status = DELETED,
            lastRequesterStaffId = staff.id
        ).fillDate()

        val event = HealthPlanTaskUpsertedEvent(
            task = expectedTask,
            staffId = staff.id,
            previousStatus = DRAFT
        )

        coEvery { healthPlanTaskDataService.get(prescriptionModel.id) } returns prescriptionModel.success()
        coEvery { healthPlanTaskDataService.update(expectedTask.copy(deadline = Deadline(unit = PeriodUnit.DAY, quantity = 30)).fillDate()) } returns expectedTask.success()
        coEvery { kafkaProducer.produce(matchEvent(event), expectedTask.id.toString()) } returns mockk()

        val result = healthPlanTaskService.delete(prescriptionModel.id, staff.id)
        assertThat(result).isSuccessWithData(true)

        coVerifyOnce { healthPlanTaskDataService.get(any()) }
        coVerifyOnce { healthPlanTaskDataService.update(any()) }
        coVerifyOnce { kafkaProducer.produce(any(), any()) }
        coVerify { healthPlanTaskGroupService wasNot called }
        coVerify { documentPrinterService wasNot called }
        coVerify { staffService wasNot called }
        coVerify { locationService wasNot called }
        coVerify { adherenceValidationService wasNot called }
    }

    @Test
    fun `#delete should update task status do DELETED_BY_STAFF`() = mockLocalDateTime {
        val activeTask = prescriptionModel.copy(status = ACTIVE)
        val expectedTask = activeTask.copy(
            status = DELETED_BY_STAFF,
            lastRequesterStaffId = staff.id
        ).fillDate()

        val event = HealthPlanTaskUpsertedEvent(
            task = expectedTask,
            staffId = staff.id,
            previousStatus = ACTIVE
        )

        coEvery { healthPlanTaskDataService.get(activeTask.id) } returns activeTask.success()
        coEvery { healthPlanTaskDataService.update(expectedTask.copy(deadline = Deadline(unit = PeriodUnit.DAY, quantity = 30)).fillDate()) } returns expectedTask.success()
        coEvery { kafkaProducer.produce(matchEvent(event), expectedTask.id.toString()) } returns mockk()

        val result = healthPlanTaskService.delete(activeTask.id, staff.id)
        assertThat(result).isSuccessWithData(true)

        coVerifyOnce { healthPlanTaskDataService.get(any()) }
        coVerifyOnce { healthPlanTaskDataService.update(any()) }
        coVerifyOnce { kafkaProducer.produce(any(), any()) }
        coVerify { healthPlanTaskGroupService wasNot called }
        coVerify { documentPrinterService wasNot called }
        coVerify { staffService wasNot called }
        coVerify { locationService wasNot called }
        coVerify { adherenceValidationService wasNot called }
    }

    @Test
    fun `#sendOverdueTasks should send overdue tasks to NotificationService`() = runBlocking {
        val now = LocalDate.of(2020, 12, 12)

        coEvery {
            healthPlanTaskDataService.find(
                queryEq {
                    where {
                        type.inList(listOf(REFERRAL, PRESCRIPTION, TEST_REQUEST, EMERGENCY))
                            .and(status.inList(listOf(ACTIVE)))
                            .and(dueDate.less(now))
                            .and(dueDate.greater(now.minusDays(2)))
                    }
                }
            )
        } returns listOf(
            prescriptionModel.copy(dueDate = now.minusDays(2)),
            prescriptionModel.copy(dueDate = now.minusDays(1))
        ).success()

        coEvery { kafkaProducer.produce(matchEvent(event)) } returns mockk()

        val result = healthPlanTaskService.sendOverdueTasks(now)
        assertThat(result).isSuccessWithData(true)

        coVerifyOnce { healthPlanTaskDataService.find(any()) }
        coVerifyOnce { kafkaProducer.produce(any(), any()) }
        coVerify { documentPrinterService wasNot called }
        coVerify { healthPlanTaskGroupService wasNot called }
        coVerify { staffService wasNot called }
        coVerify { locationService wasNot called }
        coVerify { adherenceValidationService wasNot called }
    }

    @Test
    fun `#getLastActiveReferralWithoutSpecialist should return last active generic referral`() = runBlocking {
        val personId = personId
        val expected = referral.specialize<Referral>()

        coEvery {
            healthPlanTaskDataService.find(
                queryEq {
                    where {
                        this.personId.eq(personId) and
                                this.type.eq(REFERRAL) and
                                this.status.inList(listOf(ACTIVE))
                    }
                }
            )
        } returns listOf(referral).success()

        val result = healthPlanTaskService.getLastActiveReferralWithoutSpecialist(personId)
        assertThat(result).isSuccessWithData(expected)

        coVerifyOnce { healthPlanTaskDataService.find(any()) }
        coVerify { kafkaProducer wasNot called }
        coVerify { documentPrinterService wasNot called }
        coVerify { healthPlanTaskGroupService wasNot called }
        coVerify { staffService wasNot called }
        coVerify { locationService wasNot called }
        coVerify { adherenceValidationService wasNot called }
    }

    @Test
    fun `#getLastActiveReferralWithoutSpecialist should return error when not found referrals`() = runBlocking {
        val personId = personId

        coEvery {
            healthPlanTaskDataService.find(
                queryEq {
                    where {
                        this.personId.eq(personId) and
                                this.type.eq(REFERRAL) and
                                this.status.inList(listOf(ACTIVE))
                    }
                }
            )
        } returns NotFoundException("active_referrals_not_found").failure()

        val result = healthPlanTaskService.getLastActiveReferralWithoutSpecialist(personId)
        assertThat(result).isFailureOfType(ActiveReferralsByPersonNotFound::class)

        coVerifyOnce { healthPlanTaskDataService.find(any()) }
        coVerify { kafkaProducer wasNot called }
        coVerify { documentPrinterService wasNot called }
        coVerify { healthPlanTaskGroupService wasNot called }
        coVerify { staffService wasNot called }
        coVerify { locationService wasNot called }
        coVerify { adherenceValidationService wasNot called }
    }

    @Test
    fun `#getLastActiveReferralWithoutSpecialist should return error when not found referral without specialist`() =
        runBlocking {
            val personId = personId

            coEvery {
                healthPlanTaskDataService.find(
                    queryEq {
                        where {
                            this.personId.eq(personId) and
                                    this.type.eq(REFERRAL) and
                                    this.status.inList(listOf(ACTIVE))
                        }
                    }
                )
            } returns listOf(anotherReferral).success()

            val result = healthPlanTaskService.getLastActiveReferralWithoutSpecialist(personId)
            assertThat(result).isFailureOfType(ReferralWithoutSpecialistNotFound::class)

            coVerifyOnce { healthPlanTaskDataService.find(any()) }
            coVerify { kafkaProducer wasNot called }
            coVerify { documentPrinterService wasNot called }
            coVerify { healthPlanTaskGroupService wasNot called }
            coVerify { staffService wasNot called }
            coVerify { locationService wasNot called }
            coVerify { adherenceValidationService wasNot called }
        }

    @Test
    fun `#getTask return task found by shortId and token`() = runBlocking {
        val shortId = "Asvd30"
        val token = "0349"

        coEvery {
            healthPlanTaskDataService.findOne(
                queryEq {
                    where { this.shortId.eq(shortId) and this.token.eq(token) }
                }
            )
        } returns prescriptionModel.success()

        val result = healthPlanTaskService.getTask(shortId, token)
        assertThat(result).isSuccessWithData(prescriptionModel)

        coVerifyOnce { healthPlanTaskDataService.findOne(any()) }
        coVerify { kafkaProducer wasNot called }
        coVerify { documentPrinterService wasNot called }
        coVerify { healthPlanTaskGroupService wasNot called }
        coVerify { staffService wasNot called }
        coVerify { locationService wasNot called }
        coVerify { adherenceValidationService wasNot called }
    }

    @Test
    fun `#getByPersonAndCreateBy return task found by createdBy`() = runBlocking {
        val createdBy = TaskUpdatedBy(
            source = SYSTEM,
            id = RangeUUID.generate()
        )

        coEvery {
            healthPlanTaskDataService.find(
                queryEq {
                    where {
                        this.personId.eq(prescriptionModel.personId) and
                                this.createdBy.eq(createdBy) and
                                this.status.inList(listOf(DRAFT, ACTIVE))
                    }
                }
            )
        } returns listOf(prescriptionModel).success()

        val result = healthPlanTaskService.getByPersonAndCreateBy(
            prescriptionModel.personId, createdBy
        )
        assertThat(result).isSuccessWithData(listOf(prescriptionModel))

        coVerifyOnce { healthPlanTaskDataService.find(any()) }
        coVerify { kafkaProducer wasNot called }
        coVerify { documentPrinterService wasNot called }
        coVerify { healthPlanTaskGroupService wasNot called }
        coVerify { staffService wasNot called }
        coVerify { locationService wasNot called }
        coVerify { adherenceValidationService wasNot called }
    }

    @Test
    fun `#getByAppointmentId return task found by appointmentId`() = runBlocking {
        val appointmentId = appointmentId

        coEvery {
            healthPlanTaskDataService.find(
                queryEq {
                    where { this.appointmentId.eq(appointmentId) }
                }
            )
        } returns listOf(prescriptionModel).success()

        val result = healthPlanTaskService.getByAppointmentId(appointmentId)
        assertThat(result).isSuccessWithData(listOf(prescriptionModel))

        coVerifyOnce { healthPlanTaskDataService.find(any()) }
        coVerify { kafkaProducer wasNot called }
        coVerify { documentPrinterService wasNot called }
        coVerify { healthPlanTaskGroupService wasNot called }
        coVerify { staffService wasNot called }
        coVerify { locationService wasNot called }
        coVerify { adherenceValidationService wasNot called }
    }

    @Test
    fun `#getTaskReferralByPersonAndStaffId should return referral suggestion response`() = runBlocking {
        val personID = personId
        val generalize = anotherReferral.generalize().copy(
            lastRequesterStaffId = staff.id,
            requestersStaffIds = setOf(staff.id),
            releasedByStaffId = staff.id
        )

        val responseExpected = ReferralSuggestionResponseConverter.convert(generalize.specialize(), group)

        coEvery {
            healthPlanTaskDataService.findOne(
                queryEq {
                    where {
                        this.personId.eq(personID) and
                                this.status.eq(ACTIVE) and
                                this.type.eq(REFERRAL) and
                                this.healthSpecialistId.eq(staff.id.toString())
                    }.orderBy { this.dueDate }.sortOrder { desc }
                }
            )
        } returns generalize.success()

        coEvery { healthPlanTaskGroupService.get(generalize.groupId!!) } returns group.success()

        val result = healthPlanTaskService.getTaskReferralByPersonAndStaffId(personID, staff.id)
        assertThat(result).isSuccessWithData(responseExpected)

        coVerifyOnce { healthPlanTaskDataService.findOne(any()) }
        coVerifyOnce { healthPlanTaskGroupService.get(any()) }
        coVerify { kafkaProducer wasNot called }
        coVerify { documentPrinterService wasNot called }
        coVerify { staffService wasNot called }
        coVerify { locationService wasNot called }
        coVerify { adherenceValidationService wasNot called }
    }

    @Test
    fun `#getTaskReferralByPersonAndStaffId return error NotFound when call the method get in healthPlanTaskGroupService`() =
        runBlocking {
            val personID = personId
            val generalize = anotherReferral.generalize()

            coEvery {
                healthPlanTaskDataService.findOne(
                    queryEq {
                        where {
                            this.personId.eq(personID) and
                                    this.status.eq(ACTIVE) and
                                    this.type.eq(REFERRAL) and
                                    this.healthSpecialistId.eq(staff.id.toString())
                        }.orderBy { this.dueDate }
                            .sortOrder { desc }
                    }
                )
            } returns generalize.success()

            coEvery { healthPlanTaskGroupService.get(generalize.groupId!!) } returns NotFoundException().failure()

            val result = healthPlanTaskService.getTaskReferralByPersonAndStaffId(personID, staff.id)
            assertThat(result).isFailureOfType(NotFoundException::class)

            coVerifyOnce { healthPlanTaskDataService.findOne(any()) }
            coVerifyOnce { healthPlanTaskGroupService.get(any()) }
            coVerify { kafkaProducer wasNot called }
            coVerify { documentPrinterService wasNot called }
            coVerify { staffService wasNot called }
            coVerify { locationService wasNot called }
            coVerify { adherenceValidationService wasNot called }
        }

    @Test
    fun `#getTaskReferralByPersonAndStaffId return error NotFound when call the method find in healthPlanTaskDataService`() =
        runBlocking {
            val personID = personId

            coEvery {
                healthPlanTaskDataService.findOne(
                    queryEq {
                        where {
                            this.personId.eq(personID) and
                                    this.status.eq(ACTIVE) and
                                    this.type.eq(REFERRAL) and
                                    this.healthSpecialistId.eq(staff.id.toString())
                        }.orderBy { this.dueDate }
                            .sortOrder { desc }
                    }
                )
            } returns NotFoundException().failure()

            val result = healthPlanTaskService.getTaskReferralByPersonAndStaffId(personID, staff.id)
            assertThat(result).isFailureOfType(NotFoundException::class)

            coVerifyOnce { healthPlanTaskDataService.findOne(any()) }
            coVerify { kafkaProducer wasNot called }
            coVerify { documentPrinterService wasNot called }
            coVerify { healthPlanTaskGroupService wasNot called }
            coVerify { staffService wasNot called }
            coVerify { locationService wasNot called }
            coVerify { adherenceValidationService wasNot called }
        }

    @Test
    fun `#favorite return health plan task with favorite true`() = runBlocking {

        coEvery { healthPlanTaskDataService.get(prescriptionModel.id) } returns prescriptionModel.success()

        coEvery {
            healthPlanTaskDataService.update(prescriptionModel.copy(favorite = true))
        } returns prescriptionModel.success()

        val result = healthPlanTaskService.unfavorite(prescriptionModel.id)
        assertThat(result).isSuccessWithData(prescriptionModel)

        coVerifyOnce { healthPlanTaskDataService.get(any()) }
        coVerifyOnce { healthPlanTaskDataService.update(any()) }
        coVerify { kafkaProducer wasNot called }
        coVerify { documentPrinterService wasNot called }
        coVerify { healthPlanTaskGroupService wasNot called }
        coVerify { staffService wasNot called }
        coVerify { locationService wasNot called }
        coVerify { adherenceValidationService wasNot called }
    }

    @Test
    fun `#unfavorite return health plan task with favorite false`() = runBlocking {
        coEvery { healthPlanTaskDataService.get(prescriptionModel.id) } returns prescriptionModel.success()

        coEvery {
            healthPlanTaskDataService.update(prescriptionModel.copy(favorite = false))
        } returns prescriptionModel.success()

        val result = healthPlanTaskService.unfavorite(prescriptionModel.id)
        assertThat(result).isSuccessWithData(prescriptionModel)

        coVerifyOnce { healthPlanTaskDataService.get(any()) }
        coVerifyOnce { healthPlanTaskDataService.update(any()) }
        coVerify { kafkaProducer wasNot called }
        coVerify { documentPrinterService wasNot called }
        coVerify { healthPlanTaskGroupService wasNot called }
        coVerify { staffService wasNot called }
        coVerify { locationService wasNot called }
        coVerify { adherenceValidationService wasNot called }
    }

    @Test
    fun `#completeTaskByMember should update task status do DONE`() = mockLocalDateTime { date ->
        val expectedTask = prescriptionModel.copy(
            status = DONE,
            finishedBy = TaskUpdatedBy(source = MEMBER),
            finishedAt = date
        )

        val event = HealthPlanTaskUpsertedEvent(
            task = expectedTask,
            staffId = null,
            previousStatus = DRAFT
        )

        coEvery { healthPlanTaskDataService.get(prescriptionModel.id) } returns prescriptionModel.success()
        coEvery { healthPlanTaskDataService.update(expectedTask) } returns expectedTask.success()
        coEvery { kafkaProducer.produce(matchEvent(event), expectedTask.id.toString()) } returns mockk()

        val result = healthPlanTaskService.completeTaskByMember(prescriptionModel.id)
        assertThat(result).isSuccessWithData(expectedTask)

        coVerifyOnce { healthPlanTaskDataService.get(any()) }
        coVerifyOnce { healthPlanTaskDataService.update(any()) }
        coVerifyOnce { kafkaProducer.produce(any(), any()) }
        coVerify { documentPrinterService wasNot called }
        coVerify { healthPlanTaskGroupService wasNot called }
        coVerify { staffService wasNot called }
        coVerify { locationService wasNot called }
        coVerify { adherenceValidationService wasNot called }
    }

    @Test
    fun `#getAllActiveAndDoneByPersonAndType - should return tasks active and done by person and type`() = runBlocking {
        val testType = REFERRAL
        val expected = TestModelFactory.buildHealthPlanTask(testPersonId)
        val status = listOf(ACTIVE, DONE, DELETED_BY_MEMBER)
        coEvery {
            healthPlanTaskDataService.find(
                queryEq {
                    where {
                        this.personId.eq(testPersonId)
                            .and(this.type.eq(testType))
                            .and(this.status.inList(status))
                    }
                }
            )
        } returns listOf(expected).success()

        val result = healthPlanTaskService.getAllByTypeAndStatus(testPersonId, testType, status)
        assertThat(result).isSuccessWithData(listOf(expected))

        coVerifyOnce { healthPlanTaskDataService.find(any()) }
        coVerify { kafkaProducer wasNot called }
        coVerify { documentPrinterService wasNot called }
        coVerify { healthPlanTaskGroupService wasNot called }
        coVerify { staffService wasNot called }
        coVerify { locationService wasNot called }
        coVerify { adherenceValidationService wasNot called }
    }

    @Test
    fun `#getCountsByPersonId returns counters per type by person id`() = runBlocking {
        val personId = personId

        val expected = HealthPlanTaskCounters(
            prescription = 1,
            eating = 0,
            physicalActivity = 0,
            sleep = 0,
            mood = 0,
            others = 0,
            testRequest = 2,
            referral = 3,
            emergency = 0,
            followUpRequest = 0,
            surgeryPrescription = 0
        )

        coEvery {
            healthPlanTaskDataService.countGrouped(
                queryEq {
                    where {
                        this.personId.eq(personId) and
                                this.status.inList(listOf(DRAFT, ACTIVE, DELETED_BY_MEMBER, DONE))
                    }.groupBy {
                        listOf(this.type)
                    }
                }
            )
        } returns listOf(
            CountByValues(listOf("PRESCRIPTION"), 1),
            CountByValues(listOf("REFERRAL"), 3),
            CountByValues(listOf("TEST_REQUEST"), 2)
        ).success()

        val result = healthPlanTaskService.getCountsByPersonId(personId)
        assertThat(result).isSuccessWithData(expected)

        coVerifyOnce { healthPlanTaskDataService.countGrouped(any()) }
        coVerify { kafkaProducer wasNot called }
        coVerify { documentPrinterService wasNot called }
        coVerify { healthPlanTaskGroupService wasNot called }
        coVerify { staffService wasNot called }
        coVerify { locationService wasNot called }
        coVerify { adherenceValidationService wasNot called }
    }

    @Test
    fun `#getCountsByPersonId returns counters filtered by caseId and status`() = runBlocking {
        val personId = personId

        val expected = HealthPlanTaskCounters(
            prescription = 0,
            eating = 0,
            physicalActivity = 0,
            sleep = 0,
            mood = 0,
            others = 0,
            testRequest = 0,
            referral = 0,
            emergency = 0,
            followUpRequest = 0,
            surgeryPrescription = 0
        )

        coEvery {
            healthPlanTaskDataService.countGrouped(
                queryEq {
                    where {
                        this.personId.eq(personId) and
                                this.status.inList(listOf(ACTIVE)) and
                                this.caseId.inList(listOf(taskCaseId))
                    }.groupBy {
                        listOf(this.type)
                    }
                }
            )
        } returns emptyList<CountByValues>().success()

        val result = healthPlanTaskService.getCountsByPersonId(
            personId,
            mapOf(
                "status" to listOf("ACTIVE"),
                "case_id" to listOf(taskCaseId.toString())
            )
        )
        assertThat(result).isSuccessWithData(expected)

        coVerifyOnce { healthPlanTaskDataService.countGrouped(any()) }
        coVerify { kafkaProducer wasNot called }
        coVerify { documentPrinterService wasNot called }
        coVerify { healthPlanTaskGroupService wasNot called }
        coVerify { staffService wasNot called }
        coVerify { locationService wasNot called }
    }

    @Test
    fun `#getByOriginTaskIdAndType should get tasks`() = runBlocking {
        val originTaskId = RangeUUID.generate()
        coEvery {
            healthPlanTaskDataService.find(
                queryEq {
                    where {
                        this.originTaskId.eq(originTaskId)
                            .and(type.eq(REFERRAL))
                            .and(status.eq(ACTIVE))
                    }
                }
            )
        } returns listOf(
            referral.copy(originTaskId = originTaskId),
        ).success()

        val result = healthPlanTaskService.getByOriginTaskIdAndType(originTaskId, REFERRAL)
        assertThat(result).isSuccessWithData(
            listOf(
                referral.copy(originTaskId = originTaskId),
            )
        )

        coVerifyOnce { healthPlanTaskDataService.find(any()) }
        coVerify { kafkaProducer wasNot called }
        coVerify { documentPrinterService wasNot called }
        coVerify { healthPlanTaskGroupService wasNot called }
        coVerify { staffService wasNot called }
        coVerify { locationService wasNot called }
        coVerify { adherenceValidationService wasNot called }
    }

    @Test
    fun `#existsByPersonAndCreatedBy should return true when has tasks`() = runBlocking {
        val createdBy = TaskUpdatedBy(id = RangeUUID.generate(), source = COUNTER_REFERRAL)
        coEvery {
            healthPlanTaskDataService.exists(
                queryEq {
                    where {
                        this.personId.eq(testPersonId)
                            .and(this.createdBy.eq(createdBy))
                            .and(this.status.eq(ACTIVE))
                    }
                }
            )
        } returns true.success()

        val result = healthPlanTaskService.existsByPersonAndCreatedBy(testPersonId, createdBy)
        assertThat(result).isSuccessWithData(true)

        coVerifyOnce { healthPlanTaskDataService.exists(any()) }
        coVerify { kafkaProducer wasNot called }
        coVerify { documentPrinterService wasNot called }
        coVerify { healthPlanTaskGroupService wasNot called }
        coVerify { staffService wasNot called }
        coVerify { locationService wasNot called }
        coVerify { adherenceValidationService wasNot called }
    }

    @Test
    fun `#getByPersonAndCreatedByAndType should return tasks filtering by type`() = runBlocking {
        val createdBy = TaskUpdatedBy(id = RangeUUID.generate(), source = COUNTER_REFERRAL)
        val type = TEST_REQUEST
        val expectedTasks = listOf(testRequest)
        coEvery {
            healthPlanTaskDataService.find(
                queryEq {
                    where {
                        this.personId.eq(testPersonId)
                            .and(this.createdBy.eq(createdBy))
                            .and(this.status.eq(ACTIVE))
                            .and(this.type.eq(type))
                    }
                }
            )
        } returns expectedTasks.success()

        val result = healthPlanTaskService.getByPersonAndCreatedByAndType(
            testPersonId,
            createdBy,
            type
        )

        assertThat(result).isSuccessWithData(expectedTasks)

        coVerifyOnce { healthPlanTaskDataService.find(any()) }
        coVerify { kafkaProducer wasNot called }
        coVerify { documentPrinterService wasNot called }
        coVerify { healthPlanTaskGroupService wasNot called }
        coVerify { staffService wasNot called }
        coVerify { locationService wasNot called }
        coVerify { adherenceValidationService wasNot called }
    }

    @Test
    fun `#getByPersonAndCreatedByAndType should return tasks without type filter`() = runBlocking {
        val createdBy = TaskUpdatedBy(id = RangeUUID.generate(), source = COUNTER_REFERRAL)
        val expectedTasks = listOf(testRequest)
        coEvery {
            healthPlanTaskDataService.find(
                queryEq {
                    where {
                        this.personId.eq(testPersonId)
                            .and(this.createdBy.eq(createdBy))
                            .and(this.status.eq(ACTIVE))
                    }
                }
            )
        } returns expectedTasks.success()

        val result = healthPlanTaskService.getByPersonAndCreatedByAndType(
            testPersonId,
            createdBy,
        )

        assertThat(result).isSuccessWithData(expectedTasks)

        coVerifyOnce { healthPlanTaskDataService.find(any()) }
        coVerify { kafkaProducer wasNot called }
        coVerify { documentPrinterService wasNot called }
        coVerify { healthPlanTaskGroupService wasNot called }
        coVerify { staffService wasNot called }
        coVerify { locationService wasNot called }
        coVerify { adherenceValidationService wasNot called }
    }

    @Test
    fun `#create should create a new task without adherence check when adherence call throws error`() = runBlocking {
        val healthConditionId = RangeUUID.generate()
        val caseRecordDetails = listOf(
            CaseRecordDetails(
                description = DiseaseDetails(
                    type = Disease.Type.CID_10,
                    value = "10",
                    id = healthConditionId.toString()
                ),
                severity = CaseSeverity.ONGOING
            )
        )
        val prescriptionModel = prescriptionModel.copy(caseRecordDetails = caseRecordDetails)
        val expectedModel = prescriptionModel.fillDate()
        val transport = prescriptionTransport.copy(
            id = prescriptionModel.id,
            caseRecordDetails = caseRecordDetails,
            deadline = expectedModel.deadline,
            start = expectedModel.start,
            hlAdherenceValidation = null,
            demand = caseRecordDetails.first()
        )

        coEvery { healthPlanTaskDataService.add(expectedModel) } returns expectedModel.success()
        coEvery {
            adherenceValidationService.validateAdherence(
                healthConditionId = healthConditionId,
                healthPlanTask = expectedModel,
                forceValidation = false
            )
        } returns Exception("ex").failure()
        coEvery { healthPlanTaskGroupService.get(expectedModel.groupId!!) } returns group.success()
        coEvery { staffService.findByList(expectedModel.requestersStaffIds.toList()) } returns listOf(staff).success()

        val result = healthPlanTaskService.create(prescriptionModel, staff.id, true)
        assertThat(result).isSuccessWithData(transport)

        coVerifyOnce { healthPlanTaskDataService.add(any()) }
        coVerifyOnce { healthPlanTaskGroupService.get(any()) }
        coVerifyOnce { staffService.findByList(any()) }
        coVerifyOnce { adherenceValidationService.validateAdherence(any(), any(), any()) }
        coVerify { documentPrinterService wasNot called }
        coVerify { locationService wasNot called }
        coVerify { kafkaProducer wasNot called }
    }

    @Test
    fun `#getByFilters should filter`() = runBlocking {
        val expectedTasks = listOf(
            testRequest.copy(
                groupId = RangeUUID.generate(),
                appointmentId = RangeUUID.generate(),
                caseId = RangeUUID.generate(),
                releasedByStaffId = RangeUUID.generate(),
                createdAt = LocalDateTime.now()
            )
        )
        coEvery {
            healthPlanTaskDataService.find(
                queryEq {
                    where {
                        this.status.inList(listOf(DRAFT)) and
                                this.type.inList(listOf(EATING)) and
                                this.groupId.inList(listOf(expectedTasks.first().groupId!!)) and
                                this.releasedByStaffId.inList(listOf(expectedTasks.first().releasedByStaffId!!)) and
                                this.caseId.inList(listOf(expectedTasks.first().caseId!!)) and
                                this.appointmentId.inList(listOf(expectedTasks.first().appointmentId!!)) and
                                this.createdAt.lessEq(expectedTasks.first().createdAt)
                    }.offset {
                        0
                    }.limit {
                        2
                    }
                }
            )
        } returns expectedTasks.success()

        val filters = HealthPlanTaskFilters(
            statuses = listOf(DRAFT),
            types = listOf(EATING),
            groups = listOf(expectedTasks.first().groupId!!),
            appointmentIds = listOf(expectedTasks.first().appointmentId!!),
            caseIds = listOf(expectedTasks.first().caseId!!),
            releasedByStaffIds = listOf(expectedTasks.first().releasedByStaffId!!),
            createdAtLess = expectedTasks.first().createdAt,
            range = 0..1
        )

        val result = healthPlanTaskService.getByFilters(filters)

        assertThat(result).isSuccessWithData(expectedTasks)

        coVerifyOnce { healthPlanTaskDataService.find(any()) }
    }

    @Test
    fun `#getByFilters should throws exception with no filter`() = runBlocking {
        coEvery {
            healthPlanTaskDataService.find(any())
        } returns InvalidArgumentException(code = "empty_filters", message = "Filters cannot be empty").failure()

        val filters = HealthPlanTaskFilters()

        val result = healthPlanTaskService.getByFilters(filters)

        assertThat(result).isFailureOfType(InvalidArgumentException::class)

        coVerifyOnce { healthPlanTaskDataService.find(any()) }
    }

    @Test
    fun `#existsByFilters should filter`() = runBlocking {
        val task = listOf(
            testRequest.copy(
                groupId = RangeUUID.generate(),
                appointmentId = RangeUUID.generate(),
                caseId = RangeUUID.generate(),
                releasedByStaffId = RangeUUID.generate(),
                createdAt = LocalDateTime.now()
            )
        )
        coEvery {
            healthPlanTaskDataService.exists(
                queryEq {
                    where {
                        this.status.inList(listOf(DRAFT)) and
                                this.type.inList(listOf(EATING)) and
                                this.groupId.inList(listOf(task.first().groupId!!)) and
                                this.releasedByStaffId.inList(listOf(task.first().releasedByStaffId!!)) and
                                this.caseId.inList(listOf(task.first().caseId!!)) and
                                this.appointmentId.inList(listOf(task.first().appointmentId!!)) and
                                this.createdAt.lessEq(task.first().createdAt)
                    }
                }
            )
        } returns true.success()

        val filters = HealthPlanTaskFilters(
            statuses = listOf(DRAFT),
            types = listOf(EATING),
            groups = listOf(task.first().groupId!!),
            appointmentIds = listOf(task.first().appointmentId!!),
            caseIds = listOf(task.first().caseId!!),
            releasedByStaffIds = listOf(task.first().releasedByStaffId!!),
            createdAtLess = task.first().createdAt,
        )

        val result = healthPlanTaskService.existsByFilters(filters)

        assertThat(result).isSuccessWithData(true)

        coVerifyOnce { healthPlanTaskDataService.exists(any()) }
    }

    @Test
    fun `#existsByFilters should throws exception with no filter`() = runBlocking {
        val filters = HealthPlanTaskFilters()

        val result = healthPlanTaskService.existsByFilters(filters)

        assertThat(result).isFailureOfType(InvalidArgumentException::class)

        coVerify { healthPlanTaskDataService wasNot called }
    }

    @Test
    fun `#getTransportById should return health plan task response`() = mockLocalDateTime {
        val expected = prescriptionTransport


        coEvery { healthPlanTaskDataService.get(prescriptionModel.id) } returns prescriptionModel.success()
        coEvery { healthPlanTaskGroupService.get(prescriptionModel.groupId!!) } returns group.success()
        coEvery { staffService.findByList(prescriptionModel.requestersStaffIds.toList()) } returns listOf(staff).success()

        val result = healthPlanTaskService.getTransportById(prescriptionModel.id)
        assertThat(result).isSuccessWithData(expected)

        coVerifyOnce { healthPlanTaskDataService.get(any()) }
        coVerifyOnce { healthPlanTaskGroupService.get(any()) }
        coVerifyOnce { staffService.findByList(any()) }
        coVerify { documentPrinterService wasNot called }
        coVerify { locationService wasNot called }
        coVerify { adherenceValidationService wasNot called }
    }


    @Test
    fun `#create should create a new referral task`() = mockLocalDateTime {
        mockRangeUUID { uuid ->
            val referralModel = buildHealthPlanTaskReferral(
                personId = personId,
                healthPlanId = healthPlanId,
                groupId = group.id,
                releasedByStaffId = staff.id,
                lastRequesterStaffId = staff.id,
                description = "some description",
            )

            val expected = TestModelDomainFactory.convertHealthPlanTaskTransport<Referral, ReferralTransport>(
                listOf(referralModel),
                mapOf(staff.id to staff),
                referralModel.type,
                mapOf(group.id!! to group)
            ).first().copy(description = "some description")

            val referralEvent = HealthPlanTaskUpsertedEvent(
                task = referralModel,
                staffId = staff.id,
                previousStatus = null,
                taskStatusChangeHistoryId = uuid
            )

            coEvery { healthPlanTaskGroupService.get(referralModel.groupId!!) } returns group.success()
            coEvery { staffService.findByList(referralModel.requestersStaffIds.toList()) } returns listOf(staff).success()
            coEvery { kafkaProducer.produce(matchEvent(referralEvent), any()) } returns mockk()
            coEvery {
                healthPlanTaskDataService.add(
                    referralModel.fillDate()
                )
            } returns referralModel.success()

            val result = healthPlanTaskService.create(referralModel, staff.id)

            assertThat(result).isSuccessWithData(expected)

            coVerifyOnce { healthPlanTaskDataService.add(any()) }
            coVerifyOnce { healthPlanTaskGroupService.get(any()) }
            coVerifyOnce { staffService.findByList(any()) }
            coVerifyOnce { kafkaProducer.produce(any(), any()) }
            coVerify { locationService wasNot called }
            coVerify { adherenceValidationService wasNot called }
            coVerify { documentPrinterService wasNot called }
            coVerify { medicalSpecialtyService wasNot called }
            coVerify { advancedAccessService wasNot called }
        }
    }

    @Test
    fun `#create should create new referral task with not advanced access sub speciality`() = mockLocalDateTime {
        mockRangeUUID { uuid ->
            val subSpecialty = ReferralSpecialty(
                id = RangeUUID.generate(),
                name = "SubSpecialty"
            )
            val medicalSpecialtyAdvancedAccess = TestModelFactory.buildMedicalSpecialty().copy(
                name = subSpecialty.name,
                id = subSpecialty.id!!,
                isAdvancedAccess = false
            )
            val referralModel = buildHealthPlanTaskReferral(
                personId = personId,
                healthPlanId = healthPlanId,
                groupId = group.id,
                releasedByStaffId = staff.id,
                lastRequesterStaffId = staff.id,
                description = "some description",
                subSpecialty = subSpecialty,
            )

            val updatedReferralTask = buildHealthPlanTaskReferral(
                personId = personId,
                healthPlanId = healthPlanId,
                groupId = group.id,
                releasedByStaffId = staff.id,
                lastRequesterStaffId = staff.id,
                description = "some description",
                subSpecialty = subSpecialty,
                isAdvancedAccess = false
            ).fillDate()

            val expected = TestModelDomainFactory.convertHealthPlanTaskTransport<Referral, ReferralTransport>(
                listOf(updatedReferralTask),
                mapOf(staff.id to staff),
                updatedReferralTask.type,
                mapOf(group.id!! to group)
            ).first()

            val referralEvent = HealthPlanTaskUpsertedEvent(
                task = updatedReferralTask,
                staffId = staff.id,
                previousStatus = null,
                taskStatusChangeHistoryId = uuid
            )



            coEvery { healthPlanTaskGroupService.get(referralModel.groupId!!) } returns group.success()
            coEvery { staffService.findByList(referralModel.requestersStaffIds.toList()) } returns listOf(staff).success()
            coEvery { kafkaProducer.produce(matchEvent(referralEvent), any()) } returns mockk()
            coEvery { medicalSpecialtyService.getById(subSpecialty.id!!) } returns medicalSpecialtyAdvancedAccess.success()
            coEvery {
                healthPlanTaskDataService.add(
                    updatedReferralTask
                )
            } returns updatedReferralTask.success()

            val result = healthPlanTaskService.create(referralModel, staff.id)

            assertThat(result).isSuccessWithData(expected)

            coVerifyOnce { healthPlanTaskDataService.add(any()) }
            coVerifyOnce { healthPlanTaskGroupService.get(any()) }
            coVerifyOnce { staffService.findByList(any()) }
            coVerifyOnce { kafkaProducer.produce(any(), any()) }
            coVerifyOnce { medicalSpecialtyService.getById(any()) }
            coVerify { advancedAccessService wasNot called }
            coVerify { locationService wasNot called }
            coVerify { adherenceValidationService wasNot called }
            coVerify { documentPrinterService wasNot called }
        }
    }

    @Test
    fun `#create should create new referral task with error to get sub specialty`() = mockLocalDateTime {
        mockRangeUUID { uuid ->
            val subSpecialty = ReferralSpecialty(
                id = RangeUUID.generate(),
                name = "SubSpecialty"
            )
            val referralModel = buildHealthPlanTaskReferral(
                personId = personId,
                healthPlanId = healthPlanId,
                groupId = group.id,
                releasedByStaffId = staff.id,
                lastRequesterStaffId = staff.id,
                description = "some description",
                subSpecialty = subSpecialty,
            )

            val referralModelUpdated = buildHealthPlanTaskReferral(
                personId = personId,
                healthPlanId = healthPlanId,
                groupId = group.id,
                releasedByStaffId = staff.id,
                lastRequesterStaffId = staff.id,
                description = "some description",
                subSpecialty = subSpecialty,
                isAdvancedAccess = false
            ).fillDate()

            val expected = TestModelDomainFactory.convertHealthPlanTaskTransport<Referral, ReferralTransport>(
                listOf(referralModel),
                mapOf(staff.id to staff),
                referralModel.type,
                mapOf(group.id!! to group)
            ).first()

            val referralEvent = HealthPlanTaskUpsertedEvent(
                task = referralModel,
                staffId = staff.id,
                previousStatus = null,
                taskStatusChangeHistoryId = uuid
            )

            coEvery { healthPlanTaskGroupService.get(referralModel.groupId!!) } returns group.success()
            coEvery { staffService.findByList(referralModel.requestersStaffIds.toList()) } returns listOf(staff).success()
            coEvery { kafkaProducer.produce(matchEvent(referralEvent), any()) } returns mockk()
            coEvery { medicalSpecialtyService.getById(subSpecialty.id!!) } returns NotFoundException("medical_specialty_not_found").failure()
            coEvery {
                healthPlanTaskDataService.add(
                    referralModelUpdated
                )
            } returns referralModel.success()

            val result = healthPlanTaskService.create(referralModel, staff.id)

            assertThat(result).isSuccessWithData(expected)

            coVerifyOnce { healthPlanTaskDataService.add(any()) }
            coVerifyOnce { healthPlanTaskGroupService.get(any()) }
            coVerifyOnce { staffService.findByList(any()) }
            coVerifyOnce { kafkaProducer.produce(any(), any()) }
            coVerifyOnce { medicalSpecialtyService.getById(any()) }
            coVerify { advancedAccessService wasNot called }
            coVerify { locationService wasNot called }
            coVerify { adherenceValidationService wasNot called }
            coVerify { documentPrinterService wasNot called }
        }
    }

    @Test
    fun `#create should create a new advanced access referral task`() = mockLocalDateTime {
        mockRangeUUID { uuid ->
            val durationInDays = 2
            val subSpecialty = ReferralSpecialty(
                id = RangeUUID.generate(),
                name = "SubSpecialty"
            )
            val medicalSpecialtyAdvancedAccess = TestModelFactory.buildMedicalSpecialty().copy(
                name = subSpecialty.name,
                id = subSpecialty.id!!,
                isAdvancedAccess = true
            )
            val referralModel = buildHealthPlanTaskReferral(
                personId = personId,
                healthPlanId = healthPlanId,
                groupId = group.id,
                releasedByStaffId = staff.id,
                lastRequesterStaffId = staff.id,
                description = "some description",
                subSpecialty = subSpecialty,
            )

            val healthPlanTaskToAdd = buildHealthPlanTaskReferral(
                personId = personId,
                healthPlanId = healthPlanId,
                groupId = group.id,
                releasedByStaffId = staff.id,
                lastRequesterStaffId = staff.id,
                description = "some description",
                subSpecialty = subSpecialty,
                title = "Encaminhamento para ${medicalSpecialtyAdvancedAccess.name}",
                isAdvancedAccess = true,
                deadline =  Deadline(quantity = durationInDays, unit = PeriodUnit.DAY).fillDate(referralModel.releasedAt)
            )

            val referralEvent = HealthPlanTaskUpsertedEvent(
                task = healthPlanTaskToAdd,
                staffId = staff.id,
                previousStatus = null,
                taskStatusChangeHistoryId = uuid
            )

            val expected = TestModelDomainFactory.convertHealthPlanTaskTransport<Referral, ReferralTransport>(
                listOf(healthPlanTaskToAdd),
                mapOf(staff.id to staff),
                referralModel.type,
                mapOf(group.id!! to group)
            ).first()

            coEvery { healthPlanTaskGroupService.get(referralModel.groupId!!) } returns group.success()
            coEvery { staffService.findByList(referralModel.requestersStaffIds.toList()) } returns listOf(staff).success()
            coEvery { kafkaProducer.produce(matchEvent(referralEvent), any()) } returns mockk()
            coEvery { medicalSpecialtyService.getById(subSpecialty.id!!) } returns medicalSpecialtyAdvancedAccess.success()
            coEvery { advancedAccessService.getAdvancedAccessDurationByPersonId(referralModel.personId) } returns durationInDays.success()
            coEvery { healthPlanTaskDataService.add(healthPlanTaskToAdd) } returns healthPlanTaskToAdd.success()

            val result = healthPlanTaskService.create(referralModel, staff.id)

            assertThat(result).isSuccessWithData(expected)

            coVerifyOnce { healthPlanTaskDataService.add(any()) }
            coVerifyOnce { healthPlanTaskGroupService.get(any()) }
            coVerifyOnce { staffService.findByList(any()) }
            coVerifyOnce { kafkaProducer.produce(any(), any()) }
            coVerifyOnce { medicalSpecialtyService.getById(any()) }
            coVerifyOnce { advancedAccessService.getAdvancedAccessDurationByPersonId(any()) }
            coVerify { locationService wasNot called }
            coVerify { adherenceValidationService wasNot called }
            coVerify { documentPrinterService wasNot called }
        }
    }

    @Test
    fun `#update should update deadline if change to advanced access sub specialty`() = mockLocalDateTime {
        mockRangeUUID { uuid ->
            val durationInDays = 2
            val subSpecialty = ReferralSpecialty(
                id = RangeUUID.generate(),
                name = "SubSpecialty"
            )
            val medicalSpecialtyAdvancedAccess = TestModelFactory.buildMedicalSpecialty().copy(
                name = subSpecialty.name,
                id = subSpecialty.id!!,
                isAdvancedAccess = true
            )
            val releasedAt = LocalDateTime.now()
            val referralModel = buildHealthPlanTaskReferral(
                personId = personId,
                healthPlanId = healthPlanId,
                groupId = group.id,
                releasedByStaffId = staff.id,
                lastRequesterStaffId = staff.id,
                description = "some description",
                subSpecialty = subSpecialty,
            ).copy(
                status = ACTIVE,
                releasedAt = releasedAt,
            )

            val referralModelAfterUpdate = buildHealthPlanTaskReferral(
                personId = personId,
                healthPlanId = healthPlanId,
                groupId = group.id,
                releasedByStaffId = staff.id,
                lastRequesterStaffId = staff.id,
                description = "some description",
                subSpecialty = subSpecialty,
                isAdvancedAccess = true,
                title = "Encaminhamento para ${medicalSpecialtyAdvancedAccess.name}",
                deadline = Deadline(unit = PeriodUnit.DAY, quantity = durationInDays).fillDate(releasedAt),
            ).copy(
                status = ACTIVE,
                releasedAt = releasedAt,
                requestersStaffIds = setOf(staff.id)
            )

            val expected = TestModelDomainFactory.convertHealthPlanTaskTransport<Referral, ReferralTransport>(
                listOf(referralModelAfterUpdate),
                mapOf(staff.id to staff),
                referralModel.type,
                mapOf(group.id!! to group)
            ).first()

            val referralEvent = HealthPlanTaskUpsertedEvent(
                task = referralModelAfterUpdate,
                staffId = staff.id,
                previousStatus = DRAFT,
                taskStatusChangeHistoryId = uuid
            )

            coEvery { healthPlanTaskDataService.get(referralModel.id) } returns referralModel.copy(
                description = "new",
                status = DRAFT
            ).success()
            coEvery { staffService.findByList(referralModelAfterUpdate.requestersStaffIds.toList()) } returns listOf(staff).success()
            coEvery { healthPlanTaskDataService.update(referralModelAfterUpdate) } returns referralModelAfterUpdate.success()
            coEvery { healthPlanTaskGroupService.get(referralModel.groupId!!) } returns group.success()
            coEvery { medicalSpecialtyService.getById(subSpecialty.id!!) } returns medicalSpecialtyAdvancedAccess.success()
            coEvery { advancedAccessService.getAdvancedAccessDurationByPersonId(referralModel.personId) } returns durationInDays.success()
            coEvery { kafkaProducer.produce(matchEvent(referralEvent), any()) } returns mockk()

            val result = healthPlanTaskService.update(referralModel, staff.id, false)
            assertThat(result).isSuccessWithData(expected)

            coVerifyOnce { healthPlanTaskDataService.get(any()) }
            coVerifyOnce { healthPlanTaskDataService.update(any()) }
            coVerifyOnce { healthPlanTaskGroupService.get(any()) }
            coVerifyOnce { staffService.findByList(any()) }
            coVerifyOnce { medicalSpecialtyService.getById(any()) }
            coVerifyOnce { advancedAccessService.getAdvancedAccessDurationByPersonId(any()) }
            coVerifyOnce { kafkaProducer.produce(any(), any()) }
            coVerify { locationService wasNot called }
            coVerify { adherenceValidationService wasNot called }
        }
    }

    @Test
    fun `#update should update deadline if change from advanced access sub specialty for one that is not`() = mockLocalDateTime {
        mockRangeUUID { uuid ->
            withFeatureFlag(
                namespace = FeatureNamespace.HEALTH_PLAN,
                key = "should_use_new_health_plan_task_deadline_calculation_flow",
                value = true
            ) {
                val durationInDays = 210
                val subSpecialty = ReferralSpecialty(
                    id = RangeUUID.generate(),
                    name = "SubSpecialty"
                )
                val medicalSpecialtyAdvancedAccess = TestModelFactory.buildMedicalSpecialty().copy(
                    name = subSpecialty.name,
                    id = subSpecialty.id!!,
                    isAdvancedAccess = false
                )
                val releasedAt = LocalDateTime.now()
                val referralModel = buildHealthPlanTaskReferral(
                    personId = personId,
                    healthPlanId = healthPlanId,
                    groupId = group.id,
                    releasedByStaffId = staff.id,
                    lastRequesterStaffId = staff.id,
                    description = "some description",
                    subSpecialty = subSpecialty,
                    isAdvancedAccess = true
                ).copy(
                    status = ACTIVE,
                    releasedAt = releasedAt,
                    deadline = Deadline(unit = PeriodUnit.DAY, quantity = 3).fillDate(releasedAt),
                ).fillDateUsingNewDeadlineInterval()

                val referralModelAfterUpdate = buildHealthPlanTaskReferral(
                    personId = personId,
                    healthPlanId = healthPlanId,
                    groupId = group.id,
                    releasedByStaffId = staff.id,
                    lastRequesterStaffId = staff.id,
                    description = "some description",
                    subSpecialty = subSpecialty,
                    isAdvancedAccess = false
                ).copy(
                    status = ACTIVE,
                    releasedAt = releasedAt,
                    deadline = Deadline(unit = PeriodUnit.DAY, quantity = durationInDays).fillDate(releasedAt),
                    requestersStaffIds = setOf(staff.id)
                ).fillDateUsingNewDeadlineInterval()

                val expected = TestModelDomainFactory.convertHealthPlanTaskTransport<Referral, ReferralTransport>(
                    listOf(referralModelAfterUpdate),
                    mapOf(staff.id to staff),
                    referralModel.type,
                    mapOf(group.id!! to group)
                ).first()

                val referralEvent = HealthPlanTaskUpsertedEvent(
                    task = referralModelAfterUpdate,
                    staffId = staff.id,
                    previousStatus = DRAFT,
                    taskStatusChangeHistoryId = uuid
                )

                coEvery { healthPlanTaskDataService.get(referralModel.id) } returns referralModel.copy(
                    description = "new",
                    status = DRAFT
                ).success()
                coEvery { staffService.findByList(referralModelAfterUpdate.requestersStaffIds.toList()) } returns listOf(
                    staff
                ).success()
                coEvery { healthPlanTaskDataService.update(referralModelAfterUpdate) } returns referralModelAfterUpdate.success()
                coEvery { healthPlanTaskGroupService.get(referralModel.groupId!!) } returns group.success()
                coEvery { medicalSpecialtyService.getById(subSpecialty.id!!) } returns medicalSpecialtyAdvancedAccess.success()
                coEvery { kafkaProducer.produce(matchEvent(referralEvent), any()) } returns mockk()

                val result = healthPlanTaskService.update(referralModel, staff.id, false)
                assertThat(result).isSuccessWithData(expected)

                coVerifyOnce { healthPlanTaskDataService.get(any()) }
                coVerifyOnce { healthPlanTaskDataService.update(any()) }
                coVerifyOnce { healthPlanTaskGroupService.get(any()) }
                coVerifyOnce { staffService.findByList(any()) }
                coVerifyOnce { kafkaProducer.produce(any(), any()) }
                coVerifyOnce { medicalSpecialtyService.getById(any()) }
                coVerify { locationService wasNot called }
                coVerify { adherenceValidationService wasNot called }
                coVerifyNone { advancedAccessService.getAdvancedAccessDurationByPersonId(any()) }
            }
        }
    }


    private fun MockKMatcherScope.matchEvent(expected: HealthPlanTaskUpsertedEvent) =
        this.match<HealthPlanTaskUpsertedEvent> {

            // this assert will be throw exception when data is different
            assertThat(it.payload)
                .usingRecursiveComparison()
                .ignoringFields("taskStatusChangeHistoryId")
                .isEqualTo(expected.payload)

            true
        }
}
