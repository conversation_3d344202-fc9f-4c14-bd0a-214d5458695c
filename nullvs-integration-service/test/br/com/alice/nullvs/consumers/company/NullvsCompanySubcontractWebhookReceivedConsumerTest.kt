package br.com.alice.nullvs.consumers.company

import br.com.alice.business.client.CompanyService
import br.com.alice.business.client.CompanySubContractService
import br.com.alice.common.RangeUUID
import br.com.alice.common.core.exceptions.NotFoundException
import br.com.alice.common.data.dsl.matchers.ResultAssert
import br.com.alice.common.helpers.coVerifyNone
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.common.helpers.returns
import br.com.alice.common.kafka.interfaces.KafkaProducerService
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.BatchType
import br.com.alice.data.layer.models.CompanyStatus
import br.com.alice.data.layer.models.CompanySubContractIntegrationStatus
import br.com.alice.data.layer.models.CompanySubContractStatus
import br.com.alice.data.layer.models.ExternalModelType
import br.com.alice.data.layer.models.InternalModelType
import br.com.alice.data.layer.models.LogStatus
import br.com.alice.moneyin.client.InvoiceItemService
import br.com.alice.moneyin.event.InvoiceItemCreatedEvent
import br.com.alice.nullvs.consumers.ConsumerTest
import br.com.alice.nullvs.converters.NullvsCompanyConverter.toNullvsIntegrationRecord
import br.com.alice.nullvs.events.NullvsCompanySubContractActivatedEvent
import br.com.alice.nullvs.events.NullvsCompanySubcontractWebhookReceivedEvent
import br.com.alice.nullvs.metrics.TotvsIntegrationMetric
import br.com.alice.nullvs.metrics.TotvsIntegrationMetric.Method
import br.com.alice.nullvs.metrics.TotvsIntegrationMetric.Status
import br.com.alice.nullvs.models.TotvsGroupCompany
import br.com.alice.nullvs.models.TotvsStatus
import br.com.alice.nullvs.models.company.NullvsCompanySubcontractWebhookReceived
import br.com.alice.nullvs.services.internals.NullvsIntegrationLogService
import br.com.alice.nullvs.services.internals.NullvsIntegrationRecordService
import io.mockk.coEvery
import io.mockk.mockk
import io.mockk.mockkObject
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.Nested
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.MethodSource
import kotlin.test.BeforeTest
import kotlin.test.Test

class NullvsCompanySubcontractWebhookReceivedConsumerTest : ConsumerTest() {
    private val nullvsIntegrationLogService: NullvsIntegrationLogService = mockk()
    private val nullvsIntegrationRecordService: NullvsIntegrationRecordService = mockk()
    private val companySubcontractService: CompanySubContractService = mockk()
    private val invoiceItemService: InvoiceItemService = mockk()
    private val kafkaProducerService: KafkaProducerService = mockk()
    private val companyService: CompanyService = mockk()

    private val consumer =
        NullvsCompanySubcontractWebhookReceivedConsumer(
            nullvsIntegrationLogService,
            nullvsIntegrationRecordService,
            companySubcontractService,
            invoiceItemService,
            kafkaProducerService,
            companyService,
        )

    val subcontract = TestModelFactory.buildCompanySubContract()
    val externalId = "0001"

    val nullvsCompanySubcontractWebhookReceived = NullvsCompanySubcontractWebhookReceived(
        idSoc = "1",
        batchId = "00001",
        status = TotvsStatus.SUCCESS,
        contractNumber = "0000001",
        internalId = subcontract.id,
        externalId = externalId,
        groupCompany = TotvsGroupCompany.ALICE_MICRO_ENTERPRISE,
        type = BatchType.CREATE
    )

    @BeforeTest
    fun setup() {
        super.before()
        mockkObject(TotvsIntegrationMetric)
    }

    companion object {
        @JvmStatic
        fun updateNullvsIntegrationLogDueToTotvsStatus() = listOf(
            arrayOf(TotvsStatus.SUCCESS, LogStatus.FINISHED),
            arrayOf(TotvsStatus.FAILURE, LogStatus.FAILURE),
        )
    }

    @Nested
    inner class ProcessedAtTotvs {

        @Nested
        inner class CreateAction {

            @Test
            fun `#should update subcontract external id and add nullvsIntegrationRecord and update nullvsIntegrationLog as expected when totvsStatus is success`() =
                runBlocking {
                    val companyId = RangeUUID.generate()
                    val subcontract = subcontract.copy(companyId = companyId)
                    val subcontractUpdated =
                        subcontract.copy(
                            externalId = externalId,
                            integrationStatus = CompanySubContractIntegrationStatus.PROCESSED,
                            status = CompanySubContractStatus.ACTIVE,
                        )
                    val webhook = nullvsCompanySubcontractWebhookReceived.copy(status = TotvsStatus.SUCCESS)
                    val event = NullvsCompanySubcontractWebhookReceivedEvent(webhook)
                    val nullvsIntegrationLog = TestModelFactory.buildNullvsIntegrationLog(batchType = BatchType.CREATE)
                    val nullvsIntegrationRecord = webhook.toNullvsIntegrationRecord(nullvsIntegrationLog.internalId)
                    val updateNullvsIntegrationLog = nullvsIntegrationLog.copy(status = LogStatus.FINISHED)
                    val company = TestModelFactory.buildCompany(id = companyId)
                    val companyUpdated = company.copy(status = CompanyStatus.ACTIVE)
                    val invoiceItem = TestModelFactory.buildInvoiceItem(subcontractId = subcontract.id)
                    val invoiceItem2 = TestModelFactory.buildInvoiceItem(subcontractId = subcontract.id)

                    coEvery {
                        nullvsIntegrationLogService.findByBatchIdSocAndPayloadId(
                            any(),
                            any(),
                            any(),
                        )
                    } returns nullvsIntegrationLog
                    coEvery { companySubcontractService.get(any()) } returns subcontract
                    coEvery { companySubcontractService.update(any(), false) } returns subcontractUpdated
                    coEvery { nullvsIntegrationRecordService.add(any()) } returns nullvsIntegrationRecord
                    coEvery { nullvsIntegrationLogService.update(any()) } returns updateNullvsIntegrationLog
                    coEvery {
                        invoiceItemService.listInvoiceItemsByCompanyAndSubcontract(
                            subcontract.companyId,
                            subcontract.id
                        )
                    } returns listOf(invoiceItem, invoiceItem2)
                    coEvery { companyService.get(companyId) } returns company
                    coEvery { companyService.update(companyUpdated) } returns companyUpdated
                    coEvery { kafkaProducerService.produce(match<InvoiceItemCreatedEvent> { it.payload.invoiceItem == invoiceItem2 }) } returns mockk()
                    coEvery {
                        kafkaProducerService.produce(match<NullvsCompanySubContractActivatedEvent> {
                            it.payload.companySubContractId == nullvsIntegrationRecord.internalId
                                    && it.payload.externalId == webhook.externalId
                                    && it.payload.companyGroup == webhook.groupCompany!!.code
                                    && it.payload.companyContractExternalId == webhook.contractNumber
                        })
                    } returns mockk()

                    val result = consumer.processedAtTotvs(event)

                    ResultAssert.assertThat(result).isSuccessWithData(updateNullvsIntegrationLog)

                    coVerifyOnce {
                        nullvsIntegrationLogService.findByBatchIdSocAndPayloadId(
                            webhook.batchId,
                            webhook.idSoc,
                            1,
                        )
                    }
                    coVerifyOnce {
                        companySubcontractService.get(subcontract.id)
                        companySubcontractService.update(subcontractUpdated, false)
                        companyService.get(companyId)
                        companyService.update(companyUpdated)
                    }
                    coVerifyOnce {
                        nullvsIntegrationRecordService.add(match {
                            it.internalId == nullvsIntegrationRecord.internalId && it.internalModelName == nullvsIntegrationRecord.internalModelName
                        })
                    }
                    coVerifyOnce { nullvsIntegrationLogService.update(updateNullvsIntegrationLog) }
                    coVerifyOnce {
                        TotvsIntegrationMetric.countNullvsWebhookConsumer(
                            Method.SUBCONTRACT,
                            Status.SUCCESS
                        )
                    }
                    coVerifyOnce {
                        kafkaProducerService.produce(match<InvoiceItemCreatedEvent> { it.payload.invoiceItem == invoiceItem2 })
                        kafkaProducerService.produce(match<NullvsCompanySubContractActivatedEvent> {
                            it.payload.companySubContractId == nullvsIntegrationRecord.internalId
                                    && it.payload.externalId == webhook.externalId
                                    && it.payload.companyGroup == webhook.groupCompany!!.code
                                    && it.payload.companyContractExternalId == webhook.contractNumber
                        })
                    }
                }

            @Test
            fun `#should update subcontract external id and add nullvsIntegrationRecord and update nullvsIntegrationLog as expected even when the log is not found`() =
                runBlocking {
                    val companyId = RangeUUID.generate()
                    val subcontract = subcontract.copy(companyId = companyId)
                    val subcontractUpdated =
                        subcontract.copy(
                            externalId = externalId,
                            integrationStatus = CompanySubContractIntegrationStatus.PROCESSED,
                            status = CompanySubContractStatus.ACTIVE,
                        )
                    val webhook = nullvsCompanySubcontractWebhookReceived.copy(status = TotvsStatus.SUCCESS)
                    val event = NullvsCompanySubcontractWebhookReceivedEvent(webhook)
                    val nullvsIntegrationLog = TestModelFactory.buildNullvsIntegrationLog(batchType = BatchType.CREATE)
                    val nullvsIntegrationRecord = webhook.toNullvsIntegrationRecord(nullvsIntegrationLog.internalId)
                    val updateNullvsIntegrationLog = nullvsIntegrationLog.copy(status = LogStatus.FINISHED)
                    val company = TestModelFactory.buildCompany(id = companyId)
                    val companyUpdated = company.copy(status = CompanyStatus.ACTIVE)
                    val invoiceItem = TestModelFactory.buildInvoiceItem(subcontractId = subcontract.id)
                    val invoiceItem2 = TestModelFactory.buildInvoiceItem(subcontractId = subcontract.id)

                    coEvery {
                        nullvsIntegrationLogService.findByBatchIdSocAndPayloadId(
                            any(),
                            any(),
                            any(),
                        )
                    } returns NotFoundException()
                    coEvery {
                        nullvsIntegrationLogService.add(any())
                    } returns nullvsIntegrationLog
                    coEvery { companySubcontractService.get(any()) } returns subcontract
                    coEvery { companySubcontractService.update(any(), false) } returns subcontractUpdated
                    coEvery { nullvsIntegrationRecordService.add(any()) } returns nullvsIntegrationRecord
                    coEvery { nullvsIntegrationLogService.update(any()) } returns updateNullvsIntegrationLog
                    coEvery {
                        invoiceItemService.listInvoiceItemsByCompanyAndSubcontract(
                            subcontract.companyId,
                            subcontract.id
                        )
                    } returns listOf(invoiceItem, invoiceItem2)
                    coEvery { companyService.get(companyId) } returns company
                    coEvery { companyService.update(companyUpdated) } returns companyUpdated
                    coEvery { kafkaProducerService.produce(match<InvoiceItemCreatedEvent> { it.payload.invoiceItem == invoiceItem2 }) } returns mockk()
                    coEvery { kafkaProducerService.produce(match<NullvsCompanySubContractActivatedEvent> {
                        it.payload.companySubContractId == nullvsIntegrationRecord.internalId
                                && it.payload.externalId == webhook.externalId
                                && it.payload.companyGroup == webhook.groupCompany!!.code
                                && it.payload.companyContractExternalId == webhook.contractNumber
                    }) } returns mockk()

                    val result = consumer.processedAtTotvs(event)

                    ResultAssert.assertThat(result).isSuccessWithData(updateNullvsIntegrationLog)

                    coVerifyOnce {
                        nullvsIntegrationLogService.findByBatchIdSocAndPayloadId(
                            webhook.batchId,
                            webhook.idSoc,
                            1,
                        )

                        nullvsIntegrationLogService.add(match {
                            it.internalId == webhook.internalId!! &&
                                    it.eventId == event.messageId &&
                                    it.idSoc == webhook.idSoc &&
                                    it.batchId == webhook.batchId &&
                                    it.internalModelName == InternalModelType.SUBCONTRACT &&
                                    it.externalModelName == ExternalModelType.SUBCONTRACT
                        })
                    }
                    coVerifyOnce {
                        companySubcontractService.get(subcontract.id)
                        companySubcontractService.update(subcontractUpdated, false)
                        companyService.get(companyId)
                        companyService.update(companyUpdated)
                    }
                    coVerifyOnce {
                        nullvsIntegrationRecordService.add(match {
                            it.internalId == nullvsIntegrationRecord.internalId && it.internalModelName == nullvsIntegrationRecord.internalModelName
                        })
                    }
                    coVerifyOnce { nullvsIntegrationLogService.update(updateNullvsIntegrationLog) }
                    coVerifyOnce {
                        TotvsIntegrationMetric.countNullvsWebhookConsumer(
                            Method.SUBCONTRACT,
                            Status.SUCCESS
                        )
                    }
                    coVerifyOnce {
                        kafkaProducerService.produce(match<InvoiceItemCreatedEvent> { it.payload.invoiceItem == invoiceItem2 })
                        kafkaProducerService.produce(match<NullvsCompanySubContractActivatedEvent> {
                            it.payload.companySubContractId == nullvsIntegrationRecord.internalId
                                    && it.payload.externalId == webhook.externalId
                                    && it.payload.companyGroup == webhook.groupCompany!!.code
                                    && it.payload.companyContractExternalId == webhook.contractNumber
                        })
                    }
                }

            @Test
            fun `#should update nullvsIntegrationLog and subcontract as expected when totvsStatus is failure`() =
                runBlocking {
                    val webhook = nullvsCompanySubcontractWebhookReceived.copy(
                        status = TotvsStatus.FAILURE,
                        internalId = subcontract.id
                    )
                    val event = NullvsCompanySubcontractWebhookReceivedEvent(webhook)
                    val nullvsIntegrationLog = TestModelFactory.buildNullvsIntegrationLog(
                        batchType = BatchType.CREATE,
                        internalId = subcontract.id
                    )
                    val updateNullvsIntegrationLog =
                        nullvsIntegrationLog.copy(status = LogStatus.FAILURE, internalId = subcontract.id)
                    val subcontractFailed =
                        subcontract.copy(integrationStatus = CompanySubContractIntegrationStatus.FAILED)

                    coEvery {
                        nullvsIntegrationLogService.findByBatchIdSocAndPayloadId(
                            any(),
                            any(),
                            any(),
                        )
                    } returns nullvsIntegrationLog
                    coEvery { nullvsIntegrationLogService.update(any()) } returns updateNullvsIntegrationLog
                    coEvery { companySubcontractService.get(subcontract.id) } returns subcontract
                    coEvery { companySubcontractService.update(subcontractFailed, false) } returns subcontractFailed

                    val result = consumer.processedAtTotvs(event)

                    ResultAssert.assertThat(result).isSuccessWithData(updateNullvsIntegrationLog)

                    coVerifyOnce {
                        nullvsIntegrationLogService.findByBatchIdSocAndPayloadId(
                            webhook.batchId,
                            webhook.idSoc,
                            1,
                        )
                    }
                    coVerifyOnce { nullvsIntegrationLogService.update(updateNullvsIntegrationLog) }
                    coVerifyOnce { companySubcontractService.get(subcontract.id) }
                    coVerifyOnce { companySubcontractService.update(subcontractFailed, false) }
                    coVerifyNone { nullvsIntegrationRecordService.add(any()) }
                    coVerifyOnce {
                        TotvsIntegrationMetric.countNullvsWebhookConsumer(
                            Method.SUBCONTRACT,
                            Status.SUCCESS
                        )
                    }
                }

            @Test
            fun `#should update nullvsIntegrationLog and subcontract as expected when totvsStatus is failure (without internalId in response)`() =
                runBlocking {
                    val webhook =
                        nullvsCompanySubcontractWebhookReceived.copy(status = TotvsStatus.FAILURE, internalId = null)
                    val event = NullvsCompanySubcontractWebhookReceivedEvent(webhook)
                    val nullvsIntegrationLog = TestModelFactory.buildNullvsIntegrationLog(
                        batchType = BatchType.CREATE,
                        internalId = subcontract.id
                    )
                    val updateNullvsIntegrationLog =
                        nullvsIntegrationLog.copy(status = LogStatus.FAILURE, internalId = subcontract.id)
                    val subcontractFailed =
                        subcontract.copy(integrationStatus = CompanySubContractIntegrationStatus.FAILED)

                    coEvery {
                        nullvsIntegrationLogService.findByBatchIdSocAndPayloadId(
                            any(),
                            any(),
                            any(),
                        )
                    } returns nullvsIntegrationLog
                    coEvery { nullvsIntegrationLogService.update(any()) } returns updateNullvsIntegrationLog
                    coEvery { companySubcontractService.get(subcontract.id) } returns subcontract
                    coEvery { companySubcontractService.update(subcontractFailed, false) } returns subcontractFailed

                    val result = consumer.processedAtTotvs(event)

                    ResultAssert.assertThat(result).isSuccessWithData(updateNullvsIntegrationLog)

                    coVerifyOnce {
                        nullvsIntegrationLogService.findByBatchIdSocAndPayloadId(
                            webhook.batchId,
                            webhook.idSoc,
                            1,
                        )
                    }
                    coVerifyOnce { nullvsIntegrationLogService.update(updateNullvsIntegrationLog) }
                    coVerifyOnce { companySubcontractService.get(subcontract.id) }
                    coVerifyOnce { companySubcontractService.update(subcontractFailed, false) }
                    coVerifyNone { nullvsIntegrationRecordService.add(any()) }
                    coVerifyOnce {
                        TotvsIntegrationMetric.countNullvsWebhookConsumer(
                            Method.SUBCONTRACT,
                            Status.SUCCESS
                        )
                    }
                }

        }

        @Nested
        inner class UpdateAction {
            @ParameterizedTest(name = "should update nullvsIntegrationLog to {1} as expected when totvsStatus is {0}")
            @MethodSource("br.com.alice.nullvs.consumers.company.NullvsCompanySubcontractWebhookReceivedConsumerTest#updateNullvsIntegrationLogDueToTotvsStatus")
            fun `#should update nullvsIntegrationLog as expected when totvsStatus is success or failure`(
                totvsStatus: TotvsStatus,
                logStatus: LogStatus
            ) =
                runBlocking {
                    val webhook = nullvsCompanySubcontractWebhookReceived.copy(status = totvsStatus)
                    val event = NullvsCompanySubcontractWebhookReceivedEvent(webhook)
                    val nullvsIntegrationLog = TestModelFactory.buildNullvsIntegrationLog(batchType = BatchType.UPDATE)
                    val updateNullvsIntegrationLog = nullvsIntegrationLog.copy(status = logStatus)

                    coEvery {
                        nullvsIntegrationLogService.findByBatchIdSocAndPayloadId(
                            any(),
                            any(),
                            any(),
                        )
                    } returns nullvsIntegrationLog

                    coEvery { nullvsIntegrationLogService.update(any()) } returns updateNullvsIntegrationLog

                    val result = consumer.processedAtTotvs(event)

                    ResultAssert.assertThat(result).isSuccessWithData(updateNullvsIntegrationLog)

                    coVerifyOnce {
                        nullvsIntegrationLogService.findByBatchIdSocAndPayloadId(
                            webhook.batchId,
                            webhook.idSoc,
                            1,
                        )
                    }
                    coVerifyOnce { nullvsIntegrationLogService.update(updateNullvsIntegrationLog) }
                    coVerifyOnce {
                        TotvsIntegrationMetric.countNullvsWebhookConsumer(
                            Method.SUBCONTRACT,
                            Status.SUCCESS
                        )
                    }
                }

            @Test
            fun `#should update nullvsIntegrationLog as expected even when the log is not found`() =
                runBlocking {
                    val webhook = nullvsCompanySubcontractWebhookReceived.copy(status = TotvsStatus.FAILURE)
                    val event = NullvsCompanySubcontractWebhookReceivedEvent(webhook)
                    val nullvsIntegrationLog = TestModelFactory.buildNullvsIntegrationLog(batchType = BatchType.UPDATE)
                    val updateNullvsIntegrationLog = nullvsIntegrationLog.copy(status = LogStatus.FAILURE)

                    coEvery {
                        nullvsIntegrationLogService.findByBatchIdSocAndPayloadId(
                            any(),
                            any(),
                            any(),
                        )
                    } returns NotFoundException()
                    coEvery { nullvsIntegrationLogService.add(any()) } returns nullvsIntegrationLog

                    coEvery { nullvsIntegrationLogService.update(any()) } returns updateNullvsIntegrationLog

                    val result = consumer.processedAtTotvs(event)

                    ResultAssert.assertThat(result).isSuccessWithData(updateNullvsIntegrationLog)

                    coVerifyOnce {
                        nullvsIntegrationLogService.findByBatchIdSocAndPayloadId(
                            webhook.batchId,
                            webhook.idSoc,
                            1,
                        )
                        nullvsIntegrationLogService.add(match {
                            it.internalId == webhook.internalId!! &&
                                    it.eventId == event.messageId &&
                                    it.idSoc == webhook.idSoc &&
                                    it.batchId == webhook.batchId &&
                                    it.internalModelName == InternalModelType.SUBCONTRACT &&
                                    it.externalModelName == ExternalModelType.SUBCONTRACT
                        })
                    }
                    coVerifyOnce { nullvsIntegrationLogService.update(updateNullvsIntegrationLog) }
                    coVerifyOnce {
                        TotvsIntegrationMetric.countNullvsWebhookConsumer(
                            Method.SUBCONTRACT,
                            Status.SUCCESS
                        )
                    }
                }
        }

        @Nested
        inner class CancelAction {

            @Test
            fun `#should update the nullvsIntegrationRecord and update nullvsIntegrationLog as expected when totvsStatus is success`() =
                runBlocking {
                    val webhook = nullvsCompanySubcontractWebhookReceived.copy(status = TotvsStatus.SUCCESS)
                    val event = NullvsCompanySubcontractWebhookReceivedEvent(webhook)
                    val nullvsIntegrationLog = TestModelFactory.buildNullvsIntegrationLog(
                        batchType = BatchType.CANCEL,
                        internalId = subcontract.id,
                    )
                    val nullvsIntegrationRecord = webhook.toNullvsIntegrationRecord(nullvsIntegrationLog.internalId)
                    val updateNullvsIntegrationLog = nullvsIntegrationLog.copy(status = LogStatus.FINISHED)

                    coEvery {
                        nullvsIntegrationLogService.findByBatchIdSocAndPayloadId(
                            any(),
                            any(),
                            any(),
                        )
                    } returns nullvsIntegrationLog
                    coEvery {
                        nullvsIntegrationRecordService.findByInternalIdAndModel(
                            any(),
                            InternalModelType.SUBCONTRACT,
                        )
                    } returns nullvsIntegrationRecord
                    coEvery { nullvsIntegrationRecordService.update(any()) } returns nullvsIntegrationRecord
                    coEvery { nullvsIntegrationLogService.update(any()) } returns updateNullvsIntegrationLog

                    val result = consumer.processedAtTotvs(event)

                    ResultAssert.assertThat(result).isSuccessWithData(updateNullvsIntegrationLog)

                    coVerifyOnce {
                        nullvsIntegrationLogService.findByBatchIdSocAndPayloadId(
                            webhook.batchId,
                            webhook.idSoc,
                            1,
                        )
                    }
                    coVerifyOnce {
                        nullvsIntegrationRecordService.findByInternalIdAndModel(
                            subcontract.id,
                            InternalModelType.SUBCONTRACT,
                        )
                    }
                    coVerifyOnce {
                        nullvsIntegrationRecordService.update(match {
                            it.internalId == nullvsIntegrationRecord.internalId
                                    && it.internalModelName == nullvsIntegrationRecord.internalModelName
                                    && it.canceledAt != null
                        })
                    }
                    coVerifyOnce { nullvsIntegrationLogService.update(updateNullvsIntegrationLog) }
                    coVerifyOnce {
                        TotvsIntegrationMetric.countNullvsWebhookConsumer(
                            Method.SUBCONTRACT,
                            Status.SUCCESS
                        )
                    }
                }

            @Test
            fun `#should update the nullvsIntegrationRecord and update nullvsIntegrationLog as expected even when the log is not found`() =
                runBlocking {
                    val webhook = nullvsCompanySubcontractWebhookReceived.copy(status = TotvsStatus.SUCCESS)
                    val event = NullvsCompanySubcontractWebhookReceivedEvent(webhook)
                    val nullvsIntegrationLog = TestModelFactory.buildNullvsIntegrationLog(
                        batchType = BatchType.CANCEL,
                        internalId = subcontract.id,
                    )
                    val nullvsIntegrationRecord = webhook.toNullvsIntegrationRecord(nullvsIntegrationLog.internalId)
                    val updateNullvsIntegrationLog = nullvsIntegrationLog.copy(status = LogStatus.FINISHED)

                    coEvery {
                        nullvsIntegrationLogService.findByBatchIdSocAndPayloadId(
                            any(),
                            any(),
                            any(),
                        )
                    } returns NotFoundException()
                    coEvery { nullvsIntegrationLogService.add(any()) } returns nullvsIntegrationLog
                    coEvery {
                        nullvsIntegrationRecordService.findByInternalIdAndModel(
                            any(),
                            InternalModelType.SUBCONTRACT,
                        )
                    } returns nullvsIntegrationRecord
                    coEvery { nullvsIntegrationRecordService.update(any()) } returns nullvsIntegrationRecord
                    coEvery { nullvsIntegrationLogService.update(any()) } returns updateNullvsIntegrationLog

                    val result = consumer.processedAtTotvs(event)

                    ResultAssert.assertThat(result).isSuccessWithData(updateNullvsIntegrationLog)

                    coVerifyOnce {
                        nullvsIntegrationLogService.findByBatchIdSocAndPayloadId(
                            webhook.batchId,
                            webhook.idSoc,
                            1,
                        )
                        nullvsIntegrationLogService.add(match {
                            it.internalId == webhook.internalId!! &&
                                    it.eventId == event.messageId &&
                                    it.idSoc == webhook.idSoc &&
                                    it.batchId == webhook.batchId &&
                                    it.internalModelName == InternalModelType.SUBCONTRACT &&
                                    it.externalModelName == ExternalModelType.SUBCONTRACT
                        })
                    }
                    coVerifyOnce {
                        nullvsIntegrationRecordService.findByInternalIdAndModel(
                            subcontract.id,
                            InternalModelType.SUBCONTRACT,
                        )
                    }
                    coVerifyOnce {
                        nullvsIntegrationRecordService.update(match {
                            it.internalId == nullvsIntegrationRecord.internalId
                                    && it.internalModelName == nullvsIntegrationRecord.internalModelName
                                    && it.canceledAt != null
                        })
                    }
                    coVerifyOnce { nullvsIntegrationLogService.update(updateNullvsIntegrationLog) }
                    coVerifyOnce {
                        TotvsIntegrationMetric.countNullvsWebhookConsumer(
                            Method.SUBCONTRACT,
                            Status.SUCCESS
                        )
                    }
                }

            @Test
            fun `#should update nullvsIntegrationLog as expected when totvsStatus is failure`() =
                runBlocking {
                    val webhook = nullvsCompanySubcontractWebhookReceived.copy(status = TotvsStatus.FAILURE)
                    val event = NullvsCompanySubcontractWebhookReceivedEvent(webhook)
                    val nullvsIntegrationLog = TestModelFactory.buildNullvsIntegrationLog(
                        batchType = BatchType.CANCEL,
                        internalId = subcontract.id,
                    )
                    val updateNullvsIntegrationLog = nullvsIntegrationLog.copy(status = LogStatus.FAILURE)

                    coEvery {
                        nullvsIntegrationLogService.findByBatchIdSocAndPayloadId(
                            any(),
                            any(),
                            any(),
                        )
                    } returns nullvsIntegrationLog
                    coEvery { nullvsIntegrationLogService.update(any()) } returns updateNullvsIntegrationLog

                    val result = consumer.processedAtTotvs(event)

                    ResultAssert.assertThat(result).isSuccessWithData(updateNullvsIntegrationLog)

                    coVerifyOnce {
                        nullvsIntegrationLogService.findByBatchIdSocAndPayloadId(
                            webhook.batchId,
                            webhook.idSoc,
                            1,
                        )
                    }
                    coVerifyNone {
                        nullvsIntegrationRecordService.findByInternalIdAndModel(
                            any(),
                            InternalModelType.SUBCONTRACT,
                        )
                    }
                    coVerifyNone { nullvsIntegrationRecordService.update(any()) }
                    coVerifyOnce { nullvsIntegrationLogService.update(updateNullvsIntegrationLog) }
                    coVerifyOnce {
                        TotvsIntegrationMetric.countNullvsWebhookConsumer(
                            Method.SUBCONTRACT,
                            Status.SUCCESS
                        )
                    }
                }
        }
    }
}
