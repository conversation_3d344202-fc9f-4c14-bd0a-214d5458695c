package br.com.alice.api.memberwannabe.converters

import br.com.alice.api.memberwannabe.models.AccreditedItemResponse
import br.com.alice.atlas.model.Accredited
import br.com.alice.atlas.model.AccreditedCategory
import br.com.alice.atlas.model.AccreditedSubCategory
import br.com.alice.atlas.model.Address
import br.com.alice.common.RangeUUID
import kotlinx.coroutines.runBlocking
import kotlin.test.Test
import kotlin.test.assertEquals

class AccreditedResponseConverterTest {
    @Test
    fun `#fromAccredited should convert Accredited to AccreditedItemResponse correctly`() = runBlocking {
        val referencedId = RangeUUID.generate()
        val accredited = Accredited(
            id = RangeUUID.generate(),
            name = "Hospital",
            category = AccreditedCategory.HOSPITAL,
            referencedId = referencedId,
            address = Address(
                id = RangeUUID.generate(),
                street = "Rua",
                number = "123",
                neighborhood = "Bairro",
                city = "Cidade",
                state = "Estado",
                zipcode = "12345",
            ),
            subCategory = AccreditedSubCategory.HOSPITAL_CHILDREN.description,
        )

        val accreditedLightweight = AccreditedResponseConverter.fromAccredited(accredited)

        assertEquals(
            AccreditedItemResponse(
                name = accredited.name,
                referencedId = accredited.referencedId,
                icon = accredited.icon,
                category = accredited.category,
                address = accredited.address,
                specialities = accredited.specialities,
                subCategory = accredited.subCategory,
            ),
            accreditedLightweight
        )
    }
}
