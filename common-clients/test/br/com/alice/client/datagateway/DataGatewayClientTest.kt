package br.com.alice.client.datagateway

import br.com.alice.common.data.dsl.matchers.ResultAssert
import br.com.alice.common.service.serialization.gsonCompleteSerializer
import com.google.gson.JsonParseException
import io.ktor.client.HttpClient
import io.ktor.client.engine.mock.MockEngine
import io.ktor.client.engine.mock.respond
import io.ktor.client.engine.mock.respondError
import io.ktor.client.plugins.HttpRequestTimeoutException
import io.ktor.client.plugins.HttpTimeout
import io.ktor.client.plugins.ServerResponseException
import io.ktor.http.HttpMethod
import io.ktor.http.HttpStatusCode
import kotlinx.coroutines.delay
import kotlinx.coroutines.runBlocking
import org.assertj.core.api.Assertions.assertThat
import kotlin.test.Test

class DataGatewayClientTest {

    private val apiUrl = DataGatewayClientConfiguration.apiUrl()

    private val dataResponse = DataResponse(
        firstColumn = "first",
        secondColumn = 2.0
    )
    private val response = DataGatewayResponse(
        data = listOf(dataResponse),
        meta = DataGatewayResponseMetadata(
            page = 1,
            pageSize = 10,
            total = 100,
            firstPage = 1,
            lastPage = 10,
            previousPage = null,
            nextPage = "https://next-page",
            hasNextPage = true,
            hasPreviousPage = false
        )
    )

    @Test
    fun `#search should return success when no parameter is passed`() = runBlocking {
        val tableName = "table"
        val httpClient = HttpClient(MockEngine) {
            expectSuccess = true
            engine {
                addHandler { request ->
                    assertThat(request.url.toString()).isEqualTo("$apiUrl/$tableName")
                    assertThat(request.method).isEqualTo(HttpMethod.Get)

                    respond(gsonCompleteSerializer.toJson(response))
                }
            }
        }

        val client = DataGatewayClient(httpClient)

        val result = client.search(tableName, response = DataResponse::class)

        ResultAssert.assertThat(result).isSuccessWithData(response)
    }

    @Test
    fun `#search should return success when all parameters are passed`() = runBlocking {
        val tableName = "table"
        val columnName = "columnString"
        val valueString = "value"

        val filters = listOf(
            DataGatewayFilter(
                column = columnName,
                operator = DataGatewayFilterOperator.EQUALS,
                value = valueString
            )
        )
        val sort = listOf(
            DataGatewaySort(
                column = columnName,
                order = DataGatewaySortOrder.DESC
            )
        )
        val fields = listOf("firstColumn", "secondColumn")
        val pageSize = 10L
        val pageNumber = 1L

        val expectedUrl = listOf(
            "filter[$columnName]=$valueString",
            "sort=-$columnName",
            "fields=${fields.joinToString(",")}",
            "page[number]=$pageNumber",
            "page[size]=$pageSize"
        ).let { "$apiUrl/$tableName?${it.joinToString("&")}" }

        val httpClient = HttpClient(MockEngine) {
            expectSuccess = true
            engine {
                addHandler { request ->
                    assertThat(request.url.toString()).isEqualTo(expectedUrl)
                    assertThat(request.method).isEqualTo(HttpMethod.Get)

                    respond(gsonCompleteSerializer.toJson(response))
                }
            }
        }

        val client = DataGatewayClient(httpClient)


        val result = client.search(
            tableName = tableName,
            params = DataGatewayFilterParams(
                filters = filters,
                sort = sort,
                fields = fields,
                pageSize = pageSize,
                pageNumber = pageNumber
            ),
            response = DataResponse::class
        )

        ResultAssert.assertThat(result).isSuccessWithData(response)
    }

    @Test
    fun `#search should return error when request fails`() = runBlocking {
        val tableName = "table"
        val columnName = "columnString"
        val valueString = "value"

        val filters = listOf(
            DataGatewayFilter(
                column = columnName,
                operator = DataGatewayFilterOperator.EQUALS,
                value = valueString
            )
        )
        val sort = listOf(
            DataGatewaySort(
                column = columnName,
                order = DataGatewaySortOrder.DESC
            )
        )
        val fields = listOf("firstColumn", "secondColumn")
        val pageSize = 10L
        val pageNumber = 1L

        val expectedUrl = listOf(
            "filter[$columnName]=$valueString",
            "sort=-$columnName",
            "fields=${fields.joinToString(",")}",
            "page[number]=$pageNumber",
            "page[size]=$pageSize"
        ).let { "$apiUrl/$tableName?${it.joinToString("&")}" }

        val httpClient = HttpClient(MockEngine) {
            expectSuccess = true
            engine {
                addHandler { request ->
                    assertThat(request.url.toString()).isEqualTo(expectedUrl)
                    assertThat(request.method).isEqualTo(HttpMethod.Get)

                    respondError(status = HttpStatusCode.InternalServerError)
                }
            }
        }

        val client = DataGatewayClient(httpClient)


        val result = client.search(
            tableName = tableName,
            params = DataGatewayFilterParams(
                filters = filters,
                sort = sort,
                fields = fields,
                pageSize = pageSize,
                pageNumber = pageNumber
            ),
            response = DataResponse::class
        )

        ResultAssert.assertThat(result).isFailureOfType(ServerResponseException::class)
    }

    @Test
    fun `#search should return error when there is a timeout error`() = runBlocking {
        val tableName = "table"
        val columnName = "columnString"
        val valueString = "value"

        val filters = listOf(
            DataGatewayFilter(
                column = columnName,
                operator = DataGatewayFilterOperator.EQUALS,
                value = valueString
            )
        )
        val sort = listOf(
            DataGatewaySort(
                column = columnName,
                order = DataGatewaySortOrder.DESC
            )
        )
        val fields = listOf("firstColumn", "secondColumn")
        val pageSize = 10L
        val pageNumber = 1L

        val expectedUrl = listOf(
            "filter[$columnName]=$valueString",
            "sort=-$columnName",
            "fields=${fields.joinToString(",")}",
            "page[number]=$pageNumber",
            "page[size]=$pageSize"
        ).let { "$apiUrl/$tableName?${it.joinToString("&")}" }

        val httpClient = HttpClient(MockEngine) {
            expectSuccess = true
            install(HttpTimeout) {
                socketTimeoutMillis = 100
                requestTimeoutMillis = 100
                connectTimeoutMillis = 100
            }
            engine {
                addHandler { request ->
                    assertThat(request.url.toString()).isEqualTo(expectedUrl)
                    assertThat(request.method).isEqualTo(HttpMethod.Get)

                    delay(500)
                    respond(gsonCompleteSerializer.toJson(response))
                }
            }
        }

        val client = DataGatewayClient(httpClient)


        val result = client.search(
            tableName = tableName,
            params = DataGatewayFilterParams(
                filters = filters,
                sort = sort,
                fields = fields,
                pageSize = pageSize,
                pageNumber = pageNumber
            ),
            response = DataResponse::class
        )

        ResultAssert.assertThat(result).isFailureOfType(HttpRequestTimeoutException::class)
    }

    @Test
    fun `#search should return error when parse fails`() = runBlocking {
        val tableName = "table"
        val columnName = "columnString"
        val valueString = "value"

        val filters = listOf(
            DataGatewayFilter(
                column = columnName,
                operator = DataGatewayFilterOperator.EQUALS,
                value = valueString
            )
        )
        val sort = listOf(
            DataGatewaySort(
                column = columnName,
                order = DataGatewaySortOrder.DESC
            )
        )
        val fields = listOf("firstColumn", "secondColumn")
        val pageSize = 10L
        val pageNumber = 1L

        val expectedUrl = listOf(
            "filter[$columnName]=$valueString",
            "sort=-$columnName",
            "fields=${fields.joinToString(",")}",
            "page[number]=$pageNumber",
            "page[size]=$pageSize"
        ).let { "$apiUrl/$tableName?${it.joinToString("&")}" }

        val httpClient = HttpClient(MockEngine) {
            expectSuccess = true
            engine {
                addHandler { request ->
                    assertThat(request.url.toString()).isEqualTo(expectedUrl)
                    assertThat(request.method).isEqualTo(HttpMethod.Get)

                    respond(gsonCompleteSerializer.toJson(response))
                }
            }
        }

        val client = DataGatewayClient(httpClient)


        val result = client.search(
            tableName = tableName,
            params = DataGatewayFilterParams(
                filters = filters,
                sort = sort,
                fields = fields,
                pageSize = pageSize,
                pageNumber = pageNumber
            ),
            response = DummyDataResponse::class
        )

        ResultAssert.assertThat(result).isFailureOfType(JsonParseException::class)
    }


    data class DataResponse(
        val firstColumn: String,
        val secondColumn: Double,
    )

    data class DummyDataResponse(
        val id: String
    )
}
