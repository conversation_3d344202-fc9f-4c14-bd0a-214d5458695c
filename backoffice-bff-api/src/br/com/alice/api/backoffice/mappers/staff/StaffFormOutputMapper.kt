package br.com.alice.api.backoffice.mappers.staff

import br.com.alice.api.backoffice.transfers.staff.StaffFormResponse
import br.com.alice.data.layer.models.MedicalSpecialty
import br.com.alice.data.layer.models.ProviderUnit
import br.com.alice.api.backoffice.transfers.staff.StaffRolesResponse
import br.com.alice.api.backoffice.transfers.staff.StaffTiersResponse
import br.com.alice.api.backoffice.transfers.staff.StaffScoreResponse
import br.com.alice.api.backoffice.transfers.staff.CouncilTypeResponse

object StaffFormOutputMapper {
    fun toResponse(
        specialties: List<MedicalSpecialty>? = null,
        providerUnits: List<ProviderUnit>? = null,
        staffRoles: List<StaffRolesResponse>? = null,
        staffTiers: List<StaffTiersResponse>? = null,
        staffScore: List<StaffScoreResponse>? = null,
        councilTypes: List<CouncilTypeResponse>? = null
    ): StaffFormResponse = StaffFormResponse(
        specialties = specialties,
        providerUnits = providerUnits,
        staffRoles = staffRoles,
        staffTiers = staffTiers,
        staffScore = staffScore,
        councilTypes = councilTypes
    )
}
