package br.com.alice.tiss.consumers

import br.com.alice.common.data.dsl.matchers.ResultAssert
import br.com.alice.common.helpers.coVerifyNone
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.common.models.CouncilType
import br.com.alice.common.models.State
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.exec.indicator.client.TotvsGuiaService
import br.com.alice.exec.indicator.events.GuiaHospitalizationCreatedEvent
import br.com.alice.exec.indicator.events.GuiaHospitalizationCreatedPayload
import br.com.alice.exec.indicator.models.Beneficiary
import br.com.alice.exec.indicator.models.GuiaOrigin
import br.com.alice.exec.indicator.models.GuiaProcedure
import br.com.alice.exec.indicator.models.Professional
import br.com.alice.exec.indicator.models.RequesterData
import br.com.alice.tiss.consumers.converters.GuiaHospitalizationCreatedRequestBuilder
import br.com.alice.tiss.models.CreateGuiaHospitalizationRequest
import br.com.alice.tiss.services.internal.GuiaService
import com.github.kittinunf.result.success
import io.mockk.clearAllMocks
import io.mockk.coEvery
import io.mockk.mockk
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.TestInstance
import java.time.LocalDate
import kotlin.test.AfterTest
import kotlin.test.Test

@TestInstance(TestInstance.Lifecycle.PER_CLASS)
class GuiaHospitalizationCreatedConsumerTest : ConsumerTest() {

    @AfterTest
    fun setup() = clearAllMocks()

    private val guiaService: GuiaService = mockk()
    private val builder: GuiaHospitalizationCreatedRequestBuilder = mockk()
    private val totvsGuiaService: TotvsGuiaService = mockk()

    private val consumer = GuiaHospitalizationCreatedConsumer(guiaService, builder, totvsGuiaService)

    private val totvsGuia = TestModelFactory.buildTotvsGuia()

    private val payload = GuiaHospitalizationCreatedPayload(
        totvsGuia = totvsGuia,
        procedures = emptyList(),
        beneficiary = Beneficiary(nationalId = "123", newBornAttendance = false, name = "Teste"),
        origin = GuiaOrigin.EITA
    )

    private val createGuiaHospitalizationRequest = CreateGuiaHospitalizationRequest(
        procedures = listOf(
            GuiaProcedure(table = "98", code = "123", quantity = 1, description = ""),
            GuiaProcedure(table = "98", code = "545", quantity = 1, description = "")
        ),
        requester = RequesterData(name = "Alice Operadora", providerCode = "00000000000"),
        professional = Professional(
            name = "Robson",
            council = CouncilType.CRM,
            councilNumber = "123",
            councilState = State.CE,
            phoneNumber = "(11) 1111-1111",
        ),
        guiaNumber = "983732412324",
        requestDate = LocalDate.now(),
        beneficiary = Beneficiary(nationalId = "123", newBornAttendance = false, name = "Teste"),
        ansCode = "1234",
        authorizationType = "1",
        attendanceCharacter = "1",
        origin = GuiaOrigin.EHR,
        attendanceRegime = "1",
        accidentIndication = "1",
        recommendedAdmissionDate = LocalDate.now(),
        admissionType = "1",
        admissionDaysRequested = 1.toBigInteger(),
        opmeIndicator = false,
        quimioterapyIndicator = false,
        clinicalIndication = "1"
    )

    private val event = GuiaHospitalizationCreatedEvent(payload)

    @Test
    fun `#handle GuiaHospitalizationCreatedEvent should process event`() = runBlocking {
        coEvery {
            guiaService.createGuiaHospitalization(any())
        } returns true.success()

        coEvery {
            builder.build(any(), any(), any(), any())
        } returns createGuiaHospitalizationRequest.success()

        coEvery {
            totvsGuiaService.get(totvsGuia.id)
        } returns totvsGuia.success()

        val result = consumer.handle(event)

        ResultAssert.assertThat(result).isSuccessWithData(true)

        coVerifyOnce { guiaService.createGuiaHospitalization(any()) }
    }

    @Test
    fun `#handle GuiaHospitalizationCreatedEvent should not process event when guia already has external code`() = runBlocking {
        coEvery {
            guiaService.createGuiaHospitalization(any())
        } returns true.success()

        coEvery {
            builder.build(any(), any(), any(), any())
        } returns createGuiaHospitalizationRequest.success()

        coEvery {
            totvsGuiaService.get(totvsGuia.id)
        } returns totvsGuia.copy(externalCode = "haveExternalCode").success()

        val result = consumer.handle(event)

        ResultAssert.assertThat(result).isSuccessWithData(true)

        coVerifyNone { guiaService.createGuiaHospitalization(any()) }
    }
}
