package br.com.alice.member.onboarding.factory

import br.com.alice.common.core.extensions.toBrazilianDateFormat
import br.com.alice.data.layer.models.MemberOnboarding
import br.com.alice.data.layer.models.MemberOnboarding.MemberOnboardingStepType
import br.com.alice.data.layer.models.MemberOnboarding.MemberOnboardingStepType.ALICE_INFO
import br.com.alice.data.layer.models.MemberOnboarding.MemberOnboardingStepType.CONCLUSION
import br.com.alice.data.layer.models.MemberOnboarding.MemberOnboardingStepType.COVER
import br.com.alice.data.layer.models.MemberOnboarding.MemberOnboardingStepType.COVER_MFC
import br.com.alice.data.layer.models.MemberOnboarding.MemberOnboardingStepType.SCORE_MAGENTA
import br.com.alice.data.layer.models.MemberOnboarding.MemberOnboardingStepType.VIDEO
import br.com.alice.data.layer.models.MemberOnboardingFlowType
import br.com.alice.data.layer.models.MemberOnboardingFlowType.ADULT
import br.com.alice.data.layer.models.MemberOnboardingFlowType.CHILD
import br.com.alice.member.onboarding.model.BottomSheetContent
import br.com.alice.member.onboarding.model.OnboardingStepTypes
import br.com.alice.member.onboarding.model.OnboardingTemplate
import br.com.alice.member.onboarding.model.OnboardingVersion
import br.com.alice.member.onboarding.model.OnboardingVersion.V1
import br.com.alice.member.onboarding.model.OnboardingVersion.V2
import br.com.alice.member.onboarding.model.OnboardingVersion.V3
import br.com.alice.member.onboarding.model.TitleAndDescriptionData
import java.time.LocalDateTime

object OnboardingFactory {
    private const val V2_TIME_TO_COMPLETE = "4 min"

    fun getStepTypes(
        type: MemberOnboardingFlowType,
        version: OnboardingVersion = V1
    ): List<MemberOnboardingStepType> =
        templateTypes.first { it.version == version && it.flowType == type }.types

    fun getTitleAndDescription(memberOnboarding: MemberOnboarding, version: OnboardingVersion) =
        getTitleAndDescriptionData(memberOnboarding, version).let {
            it.title to it.description
        }
    
    operator fun get(
        type: MemberOnboardingStepType,
        flowType: MemberOnboardingFlowType,
        version: OnboardingVersion,
        memberActivationDate: LocalDateTime
    ): OnboardingTemplate =
        when (type) {
            VIDEO -> welcome
            COVER -> getHealthDeclaration(flowType, version)
            SCORE_MAGENTA -> getScoreMagenta(version, flowType)
            ALICE_INFO -> getAliceInfoTemplate()
            CONCLUSION -> conclusion
            COVER_MFC -> buildMFCCoverStep(memberActivationDate, flowType, version)
        }

    private fun getHealthDeclaration(flowType: MemberOnboardingFlowType, version: OnboardingVersion) =
        when(flowType to version) {
            ADULT to V1 -> healthDeclaration
            ADULT to V2 -> healthDeclaration.copy(timeToComplete = V2_TIME_TO_COMPLETE)
            CHILD to V1 -> healthDeclarationChild
            CHILD to V2 -> healthDeclarationChild.copy(timeToComplete = V2_TIME_TO_COMPLETE)
            CHILD to V3 -> healthDeclarationChild.copy(bottomSheet = healthDeclaration.bottomSheet?.copy(description = buildBottomSheetDescription(flowType, version)))
            ADULT to V3 -> healthDeclaration.copy(bottomSheet = healthDeclaration.bottomSheet?.copy(description = buildBottomSheetDescription(flowType, version)))
            else -> healthDeclaration
        }
    
    private fun getScoreMagenta(version: OnboardingVersion, flowType: MemberOnboardingFlowType) =
        when(version) {
            V1 -> scoreMagenta
            V2 -> scoreMagenta.copy(timeToComplete = V2_TIME_TO_COMPLETE)
            V3 -> scoreMagenta.copy(bottomSheet = scoreMagenta.bottomSheet?.copy(description = buildBottomSheetDescription(flowType, version)))
        }

    private fun buildMFCCoverStep(
        memberActivationDate: LocalDateTime,
        flowType: MemberOnboardingFlowType,
        version: OnboardingVersion
    ) = OnboardingTemplate(
        type = COVER_MFC,
        title = "Escolha o Médico que vai cuidar de você na Alice",
        path = "/mfc_cover",
        timeToComplete = "Complete até ${memberActivationDate.plusDays(60L).toBrazilianDateFormat()}",
        bottomSheet = BottomSheetContent(
            title = "Você já preencheu essa etapa",
            description = buildBottomSheetDescription(flowType, version)
        )
    )

    private fun buildBottomSheetDescription(flowType: MemberOnboardingFlowType, version: OnboardingVersion) =
        when(flowType to version) {
            ADULT to V3 -> "As etapas de escolha do seu médico(a), Declaração de Saúde e Score Magenta só podem ser preenchidas uma vez."
            CHILD to V3 -> "As etapas de escolha do médico(a) do seu dependente e Declaração de Saúde só podem ser preenchidas uma vez."
            else -> "As etapas de Declaração de Saúde e Score Magenta só podem ser preenchidas uma vez."
        }
    
    private val welcome = OnboardingTemplate(
        type = VIDEO,
        title = "Boas-vindas à revolução dos planos de saúde",
        url = "https://alice-member-app-assets.s3.amazonaws.com/member_onboarding/videos/v2/florence_ola.mp4",
        timeToComplete = "1 min",
        caption = "WEBVTT\n\n1\n00:00:00.000 --> 00:00:00.633\nTudo bom?\n\n2\n00:00:00.633 --> 00:00:04.337" +
                "\nMeu nome é André Florence e eu sou um dos fundadores e também CEO da Alice.\n\n3\n00:00:04.637" +
                " --> 00:00:07.207\nÉ muito bom ter você com a gente.\n\n4\n00:00:07.207 --> 00:00:10.377\nA " +
                "gente fez vários vídeos bem curtinhos explicando tudo sobre Alice,\n\n5\n00:00:10.543 --> " +
                "00:00:14.381\nmas nesse aqui eu quero te contar sobre qual é o nosso propósito:\n\n6\n" +
                "00:00:14.948 --> 00:00:16.750\ntornar o mundo mais saudável.\n\n7\n00:00:16.750 --> " +
                "00:00:18.018\nAntes de fundar a Alice,\n\n8\n00:00:18.018 --> 00:00:21.021\neu era usuário" +
                " comum do sistema de saúde e tinha um convênio tradicional.\n\n9\n00:00:21.688 --> 00:00:24.791\n" +
                "Até que eu percebi que estava rolando uma revolução\n\n10\n00:00:24.791 --> 00:00:28.028\nem" +
                " todos os serviços que eu conhecia, desde a forma que a gente pede comida,\n\n11\n00:00:28.661" +
                " --> 00:00:31.131\npede um táxi, ouve uma música\n\n12\n00:00:31.131 --> 00:00:34.134\ne até" +
                " mesmo a forma como a gente se relaciona.\n\n13\n00:00:34.667 --> 00:00:38.304\nPor outro lado," +
                " o plano de saúde continuava a mesma coisa há mais de 20 anos.\n\n14\n00:00:38.805 --> 00:00:40.807" +
                "\nEu até tinha uma carteirinha,\n\n15\n00:00:40.807 --> 00:00:44.377\nmas no fundo eu não tinha" +
                " nenhuma ajuda na hora que eu mais precisava.\n\n16\n00:00:44.377 --> 00:00:46.546\nE foi por " +
                "isso que a gente fundou a Alice.\n\n17\n00:00:46.546 --> 00:00:50.417\nPara você ter alguém para" +
                " contar em todos os momentos, de qualquer lugar,\n\n18\n00:00:50.817 --> 00:00:55.188\n24 horas" +
                " por dia e para genuinamente te ajudar a ficar mais saudável.\n\n19\n00:00:55.455 --> 00:00:58.158" +
                "\nMuito obrigado e pode contar com a gente!\n\n20\n00:00:58.158 --> 00:00:59.859\nAh, te vejo " +
                "nos próximos vídeos."
    )

    private val healthDeclaration = OnboardingTemplate(
        type = COVER,
        title = "Preencha sua declaração de saúde",
        path = "/health_declaration_cover",
        timeToComplete = "8 min",
        bottomSheet = BottomSheetContent(
            title = "Você já preencheu essa etapa",
            description = "As etapas de Declaração de Saúde e Score Magenta só podem ser preenchidas uma vez."
        )
    )

    private val healthDeclarationChild = healthDeclaration.copy(title = "Preencha a Declaração de Saúde")

    private val scoreMagenta = OnboardingTemplate(
        type = SCORE_MAGENTA,
        title = "Descubra como está sua saúde com o Score Magenta",
        timeToComplete = "8 min",
        bottomSheet = BottomSheetContent(
            title = "Você já preencheu essa etapa",
            description = "As etapas de Declaração de Saúde e Score Magenta só podem ser preenchidas uma vez."
        ),
        questionnaireType = "QUEST_IMMERSION_PROFILE"
    )

    private fun getAliceInfoTemplate() =
        OnboardingTemplate(
            type = ALICE_INFO,
            title = "Saiba como a Alice funciona na prática",
            timeToComplete = "1 min",
            data = OnboardingTemplate.Data(2)
        )

    private val conclusion = OnboardingTemplate(
        type = CONCLUSION,
        title = "Ver Plano de Ação",
        timeToComplete = "1 min",
        bottomSheet = BottomSheetContent(
            title = "O que é o Plano de Ação",
            description = "Plano de Ação é onde ficam centralizadas todas as suas tarefas de saúde, desde pedidos" +
                    " de exames, encaminhamentos e receitas de remédios.\n\nCom base em suas condições preexistentes" +
                    " ou no seu estado de saúde atual definiremos a tarefa do seu primeiro Plano de Ação."
        )
    )

    private fun getTitleAndDescriptionData(
        memberOnboarding: MemberOnboarding,
        version: OnboardingVersion = V1
    ) =
        getTitleAndDescriptionByVersionAndFlowType(version, memberOnboarding.flowType)
            .first {
                it.stepsCompleted.contains(memberOnboarding.finishedSteps)
            }

    private fun getTitleAndDescriptionByVersionAndFlowType(
        version: OnboardingVersion,
        flowType: MemberOnboardingFlowType
    ) =
        onboardingTitleAndDescription
            .filter { it.version == version && it.flowType == flowType }

    private val onboardingTitleAndDescription = listOf(
        TitleAndDescriptionData(
            title = "Complete as etapas",
            description = "Conclua as etapas para desbloquear seu primeiro Plano de Ação. " +
                    "As tarefas serão personalizadas de acordo com suas respostas.",
            stepsCompleted = listOf(0, 1, 2),
            version = V1,
            flowType = ADULT
        ),
        TitleAndDescriptionData(
            title = "Falta muito pouco",
            description = "Conclua as etapas para desbloquear seu primeiro Plano de Ação. " +
                    "As tarefas serão personalizadas de acordo com suas respostas.",
            stepsCompleted = listOf(3),
            version = V1,
            flowType = ADULT
        ),
        TitleAndDescriptionData(
            title = "Seu Plano de Ação",
            description = "Pronto! Com base nas suas respostas, criamos seu primeiro " +
                    "Plano de Ação. Prepare-se para uma vida mais saudável.",
            stepsCompleted = listOf(4, 5),
            version = V1,
            flowType = ADULT
        ),
        TitleAndDescriptionData(
            title = "Complete esta etapa",
            description = "Conclua a etapa abaixo para desbloquear o primeiro Plano de Ação do seu dependente.",
            stepsCompleted = listOf(0),
            version = V1,
            flowType = CHILD
        ),
        TitleAndDescriptionData(
            title = "Seu Plano de Ação",
            description = "Pronto! Com base nas suas respostas, criamos o primeiro Plano de Ação do seu dependente.",
            stepsCompleted = listOf(1, 2),
            version = V1,
            flowType = CHILD
        ),
        TitleAndDescriptionData(
            title = "Sua jornada começa aqui",
            description = "Cuidar da saúde é mais fácil quando você sabe por onde começar. Desbloqueie seu " +
                    "primeiro Plano de Ação com tarefas personalizadas.",
            stepsCompleted = listOf(0),
            version = V2,
            flowType = ADULT
        ),
        TitleAndDescriptionData(
            title = "Raio-X da sua saúde",
            description = "Descubra como está sua saúde através de algumas perguntas que nos ajudam a criar " +
                    "um Plano de Ação para você.",
            stepsCompleted = listOf(1),
            version = V2,
            flowType = ADULT
        ),
        TitleAndDescriptionData(
            title = "Falta muito pouco",
            description = "Está é a última etapa para desbloquear seu primeiro Plano de Ação. As tarefas serão" +
                    " personalizadas de acordo com suas respostas.",
            stepsCompleted = listOf(2),
            version = V2,
            flowType = ADULT
        ),
        TitleAndDescriptionData(
            title = "Seu Plano de Ação chegou",
            description = "Prepare-se para uma vida mais saudável. Com base nas suas respostas, criamos" +
                    " seu primeiro Plano de Ação.",
            stepsCompleted = listOf(3, 4),
            version = V2,
            flowType = ADULT
        ),
        TitleAndDescriptionData(
            title = "Sua jornada começa aqui",
            description = "Cuidar da saúde é mais fácil quando você sabe por onde começar. Desbloqueie o primeiro " +
                    "Plano de Ação do seu dependente.",
            stepsCompleted = listOf(0),
            version = V2,
            flowType = CHILD
        ),
        TitleAndDescriptionData(
            title = "Seu Plano de Ação chegou",
            description = "Prepare-se para uma vida mais saudável. Com base nas suas respostas, criamos o primeiro " +
                    "Plano de Ação do seu dependente.",
            stepsCompleted = listOf(1,2),
            version = V2,
            flowType = CHILD
        ),
        TitleAndDescriptionData(
            title = "Complete as etapas",
            description = "Conclua as etapas para desbloquear seu primeiro Plano de Ação. " +
                    "As tarefas serão personalizadas de acordo com suas respostas.",
            stepsCompleted = listOf(0, 1, 2),
            version = V3,
            flowType = ADULT
        ),
        TitleAndDescriptionData(
            title = "Falta muito pouco",
            description = "Conclua as etapas para desbloquear seu primeiro Plano de Ação. " +
                    "As tarefas serão personalizadas de acordo com suas respostas.",
            stepsCompleted = listOf(3, 4),
            version = V3,
            flowType = ADULT
        ),
        TitleAndDescriptionData(
            title = "Seu Plano de Ação",
            description = "Pronto! Com base nas suas respostas, criamos seu primeiro " +
                    "Plano de Ação. Prepare-se para uma vida mais saudável.",
            stepsCompleted = listOf(5, 6),
            version = V3,
            flowType = ADULT
        ),
        TitleAndDescriptionData(
            title = "Complete as etapas",
            description = "Conclua as etapas para desbloquear o primeiro Plano de Ação do seu dependente.",
            stepsCompleted = listOf(0),
            version = V3,
            flowType = CHILD
        ),
        TitleAndDescriptionData(
            title = "Falta muito pouco",
            description = "Conclua a etapa abaixo para desbloquear o primeiro Plano de Ação do seu dependente.",
            stepsCompleted = listOf(1),
            version = V3,
            flowType = CHILD
        ),
        TitleAndDescriptionData(
            title = "Seu Plano de Ação",
            description = "Pronto! Com base nas suas respostas, criamos o primeiro Plano de Ação do seu dependente.",
            stepsCompleted = listOf(2, 3),
            version = V3,
            flowType = CHILD
        )
    )

    private val templateTypes = listOf(
        OnboardingStepTypes(
            flowType = ADULT,
            types = listOf(VIDEO, COVER, SCORE_MAGENTA, ALICE_INFO, CONCLUSION),
            version = V1
        ),
        OnboardingStepTypes(
            flowType = CHILD,
            types = listOf(COVER, CONCLUSION),
            version = V1
        ),
        OnboardingStepTypes(
            flowType = ADULT,
            types = listOf(ALICE_INFO, SCORE_MAGENTA, COVER, CONCLUSION),
            version = V2
        ),
        OnboardingStepTypes(
            flowType = CHILD,
            types = listOf(COVER, CONCLUSION),
            version = V2
        ),
        OnboardingStepTypes(
            flowType = ADULT,
            types = listOf(COVER_MFC, VIDEO, COVER, SCORE_MAGENTA, ALICE_INFO, CONCLUSION),
            version = V3
        ),
        OnboardingStepTypes(
            flowType = CHILD,
            types = listOf(COVER_MFC, COVER, CONCLUSION),
            version = V3
        )
    )
}
