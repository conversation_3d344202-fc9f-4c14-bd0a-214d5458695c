package br.com.alice.member.api

import br.com.alice.common.RangeUUID
import br.com.alice.common.data.dsl.matchers.ResponseAssert
import br.com.alice.common.extensions.money
import br.com.alice.common.helpers.bodyAsJson
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.common.helpers.returns
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.InvoicePaymentStatus
import br.com.alice.data.layer.models.InvoiceStatus
import br.com.alice.data.layer.models.MemberInvoice
import br.com.alice.member.api.controllers.InvoiceController
import br.com.alice.member.api.models.InvoiceResponse
import br.com.alice.member.api.models.InvoiceStatusResponse
import br.com.alice.member.api.models.PaymentInfoResponse
import br.com.alice.moneyin.client.BillingAccountablePartyService
import br.com.alice.moneyin.client.InvoiceLiquidationService
import br.com.alice.moneyin.client.InvoicePaymentService
import br.com.alice.moneyin.client.InvoicePdfService
import br.com.alice.moneyin.client.InvoicesService
import br.com.alice.person.client.PersonService
import com.github.kittinunf.result.success
import io.ktor.client.statement.bodyAsChannel
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.mockk
import kotlinx.coroutines.runBlocking
import org.assertj.core.api.Assertions.assertThat
import java.math.BigDecimal
import java.time.LocalDate
import java.time.LocalDateTime
import kotlin.test.BeforeTest
import kotlin.test.Test

class InvoiceRoutesTest : RoutesTestHelper() {

    private val BASE_URL = ServiceConfig.baseUrl

    private val invoiceService: InvoicesService = mockk()
    private val invoicePaymentService: InvoicePaymentService = mockk()
    private val billingAccountablePartyService: BillingAccountablePartyService = mockk()
    private val personService: PersonService = mockk()
    private val invoiceLiquidationService: InvoiceLiquidationService = mockk()
    private val invoicePdfService: InvoicePdfService = mockk()

    private val token = RangeUUID.generate().toString()

    private val person = TestModelFactory.buildPerson()
    private val member = TestModelFactory.buildMember(person.id)

    private val userAgent = "Android/1.0.0-0 (sweet/30)"
    private val headers = mapOf(
        "User-Agent" to userAgent,
    )

    @BeforeTest
    override fun setup() {
        super.setup()
        module.single {
            InvoiceController(
                invoiceService,
                invoicePaymentService,
                billingAccountablePartyService,
                personService,
                invoiceLiquidationService,
                invoicePdfService,
            )
        }
    }

    @Test
    fun `#getInvoices should return a list of invoices by person`() {
        val dueDate = LocalDateTime.now().plusDays(10)
        val billingAccountableParty = TestModelFactory.buildBillingAccountableParty()
        val payment = TestModelFactory.buildInvoicePayment(
            paymentDetail = TestModelFactory.buildBoletoPaymentDetail(),
            billingAccountablePartyId = billingAccountableParty.id
        )

        val expectedInvoices = listOf(
            MemberInvoice(
                memberId = member.id,
                personId = member.personId,
                totalAmount = BigDecimal("100"),
                referenceDate = LocalDate.now(),
                dueDate = dueDate,
                invoicePayments = listOf(payment),
            ),
        )

        coEvery {
            invoiceService.listByPersonAndStatuses(
                member.personId,
                listOf(
                    InvoiceStatus.OPEN,
                    InvoiceStatus.PAID,
                    InvoiceStatus.CANCELED_BY_LIQUIDATION,
                    InvoiceStatus.FAILED
                ),
                withPayments = true
            )
        } returns expectedInvoices.success()
        coEvery { billingAccountablePartyService.findById(listOf(billingAccountableParty.id)) } returns listOf(
            billingAccountableParty
        ).success()
        coEvery { personService.get(person.id) } returns person.success()

        authenticatedAs(token, toTestPerson(person)) {
            get("/membership/invoices", headers) { response ->
                ResponseAssert.assertThat(response).isSuccessfulJson()

                val invoices: List<InvoiceResponse> = response.bodyAsJson()
                assertThat(invoices).hasSize(1)

                val firstInvoice = invoices.first()
                assertThat(firstInvoice.status).isEqualTo(InvoiceStatusResponse.OPEN)

                coVerify(exactly = 1) {
                    invoiceService.listByPersonAndStatuses(
                        member.personId,
                        listOf(
                            InvoiceStatus.OPEN,
                            InvoiceStatus.PAID,
                            InvoiceStatus.CANCELED_BY_LIQUIDATION,
                            InvoiceStatus.FAILED,
                        ),
                        withPayments = true
                    )
                }
            }
        }
    }

    @Test
    fun `#getInvoices should return a list of invoices by person with expired payment`() {
        val dueDate = LocalDateTime.now().plusDays(10)
        val billingAccountableParty = TestModelFactory.buildBillingAccountableParty()
        val payment = TestModelFactory.buildInvoicePayment(
            status = InvoicePaymentStatus.EXPIRED,
            paymentDetail = TestModelFactory.buildBoletoPaymentDetail(),
            billingAccountablePartyId = billingAccountableParty.id,
        )

        val expectedInvoices = listOf(
            MemberInvoice(
                memberId = member.id,
                personId = member.personId,
                totalAmount = BigDecimal("100"),
                referenceDate = LocalDate.now(),
                dueDate = dueDate,
                invoicePayments = listOf(payment)
            ),
        )

        coEvery {
            invoiceService.listByPersonAndStatuses(
                member.personId,
                listOf(
                    InvoiceStatus.OPEN,
                    InvoiceStatus.PAID,
                    InvoiceStatus.CANCELED_BY_LIQUIDATION,
                    InvoiceStatus.FAILED
                ),
                withPayments = true
            )
        } returns expectedInvoices.success()
        coEvery { billingAccountablePartyService.findById(listOf(billingAccountableParty.id)) } returns listOf(
            billingAccountableParty
        ).success()
        coEvery { personService.get(person.id) } returns person.success()

        authenticatedAs(token, toTestPerson(person)) {
            get("/membership/invoices", headers) { response ->
                ResponseAssert.assertThat(response).isSuccessfulJson()

                val invoices: List<InvoiceResponse> = response.bodyAsJson()
                assertThat(invoices).hasSize(1)

                val firstInvoice = invoices.first()
                assertThat(firstInvoice.status).isEqualTo(InvoiceStatusResponse.OPEN)
                assertThat(firstInvoice.paymentInfo!!.id).isEqualTo(payment.id.toString())

                coVerify(exactly = 1) {
                    invoiceService.listByPersonAndStatuses(
                        member.personId,
                        listOf(
                            InvoiceStatus.OPEN,
                            InvoiceStatus.PAID,
                            InvoiceStatus.CANCELED_BY_LIQUIDATION,
                            InvoiceStatus.FAILED,
                        ),
                        withPayments = true
                    )
                }
            }
        }
    }

    @Test
    fun `#getInvoices should return a list of invoices by person with invoice liquidation`() {
        val dueDate = LocalDateTime.now().plusDays(10)
        val billingAccountableParty = TestModelFactory.buildBillingAccountableParty()
        val invoiceBillingAccountableParty = TestModelFactory.buildBillingAccountableParty()
        val payment = TestModelFactory.buildInvoicePayment(
            paymentDetail = TestModelFactory.buildBoletoPaymentDetail(),
            billingAccountablePartyId = billingAccountableParty.id
        )

        val canceledPayment = TestModelFactory.buildInvoicePayment(
            paymentDetail = TestModelFactory.buildBoletoPaymentDetail(
                dueDate = LocalDateTime.of(2024, 5, 13, 12, 0)
            ),
            billingAccountablePartyId = billingAccountableParty.id,
            status = InvoicePaymentStatus.CANCELED,
        )

        val memberInvoiceGroupId = RangeUUID.generate()

        val invoiceLiquidation =
            TestModelFactory.buildInvoiceLiquidation(
                memberInvoiceGroupIds = listOf(memberInvoiceGroupId),
                billingAccountablePartyId = invoiceBillingAccountableParty.id,
                amount = BigDecimal("100.00"),
                dueDate = LocalDate.of(2024, 10, 1),
            )

        val canceledInvoiceLiquidationPayment =
            TestModelFactory.buildInvoicePayment(
                paymentDetail = TestModelFactory.buildBoletoPaymentDetail(),
                billingAccountablePartyId = invoiceBillingAccountableParty.id,
                invoiceLiquidationId = invoiceLiquidation.id,
                status = InvoicePaymentStatus.CANCELED,
            )

        val detailBoleto = TestModelFactory.buildBoletoPaymentDetail(
            dueDate = LocalDateTime.of(2024, 10, 1, 12, 0)
        )

        val invoiceLiquidationPayment =
            TestModelFactory.buildInvoicePayment(
                paymentDetail = detailBoleto,
                billingAccountablePartyId = invoiceBillingAccountableParty.id,
                invoiceLiquidationId = invoiceLiquidation.id,
                status = InvoicePaymentStatus.PENDING,
            )

        val memberInvoice1 = MemberInvoice(
            memberId = member.id,
            personId = member.personId,
            totalAmount = BigDecimal("100"),
            referenceDate = LocalDate.of(2023, 10, 1),
            dueDate = dueDate,
            invoicePayments = listOf(payment),
        )

        val memberInvoice2 = MemberInvoice(
            memberId = member.id,
            personId = member.personId,
            totalAmount = BigDecimal("100"),
            referenceDate = LocalDate.of(2023, 9, 1),
            dueDate = LocalDateTime.of(2023, 11, 1, 12, 0),
            invoicePayments = listOf(canceledPayment),
            status = InvoiceStatus.CANCELED_BY_LIQUIDATION,
            memberInvoiceGroupId = memberInvoiceGroupId,
        )

        val expectedInvoices = listOf(memberInvoice1, memberInvoice2)

        coEvery {
            invoiceService.listByPersonAndStatuses(
                member.personId,
                listOf(
                    InvoiceStatus.OPEN,
                    InvoiceStatus.PAID,
                    InvoiceStatus.CANCELED_BY_LIQUIDATION,
                    InvoiceStatus.FAILED
                ),
                withPayments = true
            )
        } returns expectedInvoices.success()

        coEvery {
            billingAccountablePartyService.findById(
                listOf(
                    billingAccountableParty.id,
                    invoiceLiquidation.billingAccountablePartyId,
                )
            )
        } returns listOf(
            billingAccountableParty,
            invoiceBillingAccountableParty,
        ).success()

        coEvery {
            invoiceLiquidationService.listByMemberInvoiceGroupIds(listOf(memberInvoiceGroupId))
        } returns listOf(
            invoiceLiquidation,
        ).success()

        coEvery {
            invoicePaymentService.getByInvoiceLiquidationIds(
                listOf(invoiceLiquidation.id),
                true
            )
        } returns listOf(
            canceledInvoiceLiquidationPayment,
            invoiceLiquidationPayment,
        ).success()

        coEvery { personService.get(person.id) } returns person.success()

        authenticatedAs(token, toTestPerson(person)) {
            get("/membership/invoices", headers) { response ->
                ResponseAssert.assertThat(response).isSuccessfulJson()

                val invoices: List<InvoiceResponse> = response.bodyAsJson()
                assertThat(invoices).hasSize(3)

                assertThat(invoices[1])
                    .isEqualTo(
                        InvoiceResponse(
                            id = invoiceLiquidation.id,
                            referenceDate = "Outubro, 2024",
                            dueDate = "2024-10-01",
                            status = InvoiceStatusResponse.OPEN,
                            totalAmount = 100.money,
                            breakdown = null,
                            paymentOptions = null,
                            paymentInfo = PaymentInfoResponse(
                                id = invoiceLiquidationPayment.id.toString(),
                                title = "Código de barra",
                                documentUrl = "${BASE_URL}/invoices/bank_slip/${invoiceLiquidationPayment.id}",
                                code = detailBoleto.digitableLine,
                            )
                        )
                    )

                val firstInvoice = invoices.first()
                assertThat(firstInvoice.status).isEqualTo(InvoiceStatusResponse.OPEN)

                val secondInvoice = invoices[1]
                assertThat(secondInvoice.status).isEqualTo(InvoiceStatusResponse.OPEN)

                val thirdInvoice = invoices[2]
                assertThat(thirdInvoice.status).isEqualTo(InvoiceStatusResponse.CANCELED)


                coVerify(exactly = 1) {
                    invoiceService.listByPersonAndStatuses(
                        member.personId,
                        listOf(
                            InvoiceStatus.OPEN,
                            InvoiceStatus.PAID,
                            InvoiceStatus.CANCELED_BY_LIQUIDATION,
                            InvoiceStatus.FAILED,
                        ),
                        withPayments = true
                    )
                }
            }
        }
    }

    @Test
    fun `#downloadInvoicePdf should generate and download invoice bank slip successfully`() = runBlocking {

        val invoicePaymentId = RangeUUID.generate()
        val invoiceBytes = byteArrayOf(1, 2, 3, 4, 5)

        coEvery { invoicePdfService.generateInvoice(invoicePaymentId) } returns invoiceBytes

        coInternalAuthentication {
            get("/invoices/bank_slip/${invoicePaymentId}") { response ->
                ResponseAssert.assertThat(response).isOK()
                assertThat(response.bodyAsChannel()).isEqualTo(invoiceBytes)

                coVerifyOnce { invoicePdfService.generateInvoice(any()) }
            }
        }

    }
}
