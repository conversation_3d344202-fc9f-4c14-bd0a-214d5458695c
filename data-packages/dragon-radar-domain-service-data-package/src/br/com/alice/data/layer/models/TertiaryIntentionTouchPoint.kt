package br.com.alice.data.layer.models

import br.com.alice.common.Disease
import br.com.alice.common.RangeUUID
import br.com.alice.common.core.Model
import br.com.alice.common.core.PersonId
import br.com.alice.common.core.PersonReference
import br.com.alice.common.models.HealthInformation
import br.com.alice.common.serialization.JsonSerializable
import br.com.alice.common.core.exceptions.InvalidArgumentException
import br.com.alice.common.extensions.catchResult
import com.github.kittinunf.result.success
import java.math.BigDecimal
import java.time.LocalDate
import java.time.LocalDateTime
import java.util.UUID

val COORDINATED_TYPES = listOf(
    TertiaryIntentionCoordinated.TIR_COORDINATE,
    TertiaryIntentionCoordinated.TIR_ONSITE,
    TertiaryIntentionCoordinated.TIR_EXPERIENCE,
)

data class TertiaryIntentionTouchPoint(
    override val id: UUID = RangeUUID.generate(),
    override val personId: PersonId,
    val staffId: UUID? = null,
    val providerUnitId: UUID? = null,
    val type: TertiaryIntentionType? = null,
    val reason: String? = null,
    val objectiveCodes: List<Disease>? = emptyList(),
    val coordinated: TertiaryIntentionCoordinated? = null,
    val hasDischargeSummaryOnTime: Boolean = false,
    val dischargeSummaryEvaluation: TertiaryIntentionDischargeSummaryEvaluation? = null,
    val condition: String? = null,
    val startedAt: LocalDateTime? = null,
    val endedAt: LocalDateTime? = null,
    val hospitalizationOrigin: TertiaryIntentionHospitalizationOrigin? = null,
    val hospitalizationResponsible: TertiaryIntentionHospitalizationResponsible? = null,
    val internalPhysician: String? = null,
    val hospitalizationSpecialty: String? = null,
    val objectiveCodesReason: String? = null,
    val hasFollowTheFlow: Boolean? = null,
    val flowErrorReason: String? = null,
    val hadDiscussion: Boolean? = null,
    val hadInterappointment: Boolean? = null,
    val procedureDescription: String? = null,
    val hospitalizationPostProgramming: String? = null,
    val healthEventId: UUID? = null,
    val surgeryStatus: TertiaryIntentionSurgeryStatus? = null,
    val surgeryIndicationDate: LocalDateTime? = null,
    val surgeryAdmissionDate: LocalDateTime? = null,
    val surgeonSpecialistId: UUID? = null,
    val summaryReferenceId: UUID? = null,
    val summaryReferenceModel: SummaryReferenceType? = null,
    val hospitalizationProcedures: List<TertiaryIntentionHospitalizationProcedure> = emptyList(),
    val evolutions: List<TertiaryIntentionEvolution> = emptyList(),
    val semiIntensiveCare: List<TertiaryIntentionSemiIntensiveCare> = emptyList(),
    val intensiveCare: List<TertiaryIntentionIntensiveCare> = emptyList(),
    val dischargeForecastInDays: Int? = null,
    val avoidableByPrimaryCare: Boolean? = null,
    val appropriatenessEvaluationProtocol: Boolean? = null,
    val newBorn: Boolean? = null,
    val newBornWeight: BigDecimal? = null,
    val appropriateHospitalizationTime: Boolean? = null,
    val dehospitalizationResources: Boolean? = null,
    val rehospitalization: Boolean? = null,
    val hospitalizationReleaseType: TertiaryIntentionHospitalizationReleaseType? = null,
    val totvsGuiaId: UUID? = null,
    val notifications: List<TertiaryIntentionNotification> = emptyList(),
    val counterReferrals: List<UUID> = emptyList(),
    val totvsGuias: List<UUID> = emptyList(),
    val hasCausalLink: Boolean? = null,
    val version: Int = 0,
    val createdAt: LocalDateTime = LocalDateTime.now(),
    val updatedAt: LocalDateTime = LocalDateTime.now(),
) : Model, PersonReference, HealthInformation {

    fun isCoordinated() = this.coordinated != null && COORDINATED_TYPES.contains(this.coordinated)

    fun isUncoordinated() = !isCoordinated()
}

data class TertiaryIntentionSemiIntensiveCare(
    val date: LocalDate,
) : JsonSerializable

data class TertiaryIntentionIntensiveCare(
    val date: LocalDate,
    val requiresMechanicalVentilation: Boolean? = null,
) : JsonSerializable

data class TertiaryIntentionNotification(
    val id: UUID,
    val title: String?,
    val origin: Origin,
    val status: OriginStatus,
    val createdAt: LocalDateTime? = LocalDateTime.now(),
    val seen: TertiaryIntentionLastSeen? = null,
) : JsonSerializable

enum class OriginStatus {
    AUTHORIZED,
    CANCELLED,
    PARTIALLY_AUTHORIZED,
    PENDING,
    UNAUTHORIZED,
    UNKNOWN,
    COMPLETED
}

data class TertiaryIntentionLastSeen(
    val staffId: UUID,
    val seenAt: LocalDateTime,
)

enum class Origin {
    COUNTER_REFERRAL,
    TOTVS_GUIA,
}

enum class TertiaryIntentionCoordinated(val description: String) {
    TIR_COORDINATE("Sim"),
    TIR_ONSITE("Sim, mas Onsite resolveria"),
    TIR_EXPERIENCE("Sim, mas por Experiência"),
    TIR_NOT_BUT_RELEVANT("Não, mas pertinente"),
    TIR_NOT_AND_NON_RELEVANT("Não e não era pertinente"),
    TIR_NOT_MISSING_SUMMARY("Não foi possível classificar por falta de sumário de alta"),
    TIR_NOT_MISSING_RISK_SCALE("Não foi possível classificar por falta de escala de risco"),
}

enum class TertiaryIntentionHospitalizationOrigin {
    URGENCY, ELECTIVE
}

enum class TertiaryIntentionHospitalizationResponsible(val description: String, val active: Boolean) {
    ALICE_COMMUNITY("Comunidade Alice", true),
    ALICE_REAR("Retaguarda Alice", true),
    HOSPITAL_REAR("Retaguarda Hospital", true),
    HOSPITAL_TEAM("Equipe Hospitalista", false),
    PRIVATE("Particular", true),
}

enum class TertiaryIntentionSurgeryStatus(val description: String) {
    COVERAGE_ANALYSIS("Análise de cobertura"),
    STAND_BY_GRACE("Cirurgia Standby carência ou CPT"),
    PENDING("Cirurgia Pendente"),
    SCHEDULED("Cirurgia Agendada"),
    IN_A_WEEK("Cirurgia em 7 dias"),
    DECLINED_BY_MEMBER("Cirurgia recusada pelo membro"),
    DENIED("Cirurgia negada"),
    NATIONAL_COVERAGE("Cirurgia por Cobertura Nacional"),
    PERFORMED("Cirurgia Realizada"),
    POSOPERATIVE("Pós-operatório"),
}

enum class SummaryReferenceType {
    DISCHARGE_SUMMARY,
    PROVIDER_HEALTH_DOCUMENT
}

data class TertiaryIntentionHospitalizationProcedure(
    var procedureDate: LocalDate,
    var procedureCode: String,
)

data class TertiaryIntentionEvolution(
    var createdAt: LocalDateTime,
    var description: String,
    var staffId: UUID,
)

enum class TertiaryIntentionDischargeSummaryEvaluation(val description: String) {
    USEFUL_INFORMATION("Sim, estava com informações úteis"),
    PARTIALLY_COMPLETE("Parcialmente completo"),
    MISSING_INFORMATION("Não, está faltando muitas informações"),
}

enum class TertiaryIntentionHospitalizationReleaseType(val description: String) {
    DISCHARGE("Alta"),
    DEATH("Óbito"),
    TRANSFER("Transferência"),
}
