package br.com.alice.coverage.consumers

import br.com.alice.common.RangeUUID
import br.com.alice.common.data.dsl.matchers.ResultAssert.Companion.assertThat
import br.com.alice.common.helpers.coVerifyNone
import br.com.alice.common.kafka.interfaces.KafkaProducerService
import br.com.alice.common.models.SpecialistTier
import br.com.alice.coverage.br.com.alice.coverage.event.AccreditedNetworkCassiOrigin
import br.com.alice.coverage.br.com.alice.coverage.event.AccreditedNetworkRegisterCassiEvent
import br.com.alice.coverage.br.com.alice.coverage.event.AccreditedNetworkRegisterUnitEvent
import br.com.alice.coverage.br.com.alice.coverage.event.ProviderBundleChangeEvent
import br.com.alice.coverage.br.com.alice.coverage.event.SpecialtyTiersBundleChangeEvent
import br.com.alice.data.layer.helpers.TestModelFactory.buildCassiSpecialist
import br.com.alice.data.layer.helpers.TestModelFactory.buildProductBundle
import br.com.alice.data.layer.helpers.TestModelFactory.buildProvider
import br.com.alice.data.layer.models.ProductBundle
import br.com.alice.data.layer.models.ProductBundleType
import br.com.alice.data.layer.models.SpecialtyTiers
import br.com.alice.product.model.events.ProductBundleCreatedEvent
import br.com.alice.product.model.events.ProductBundleUpdatedEvent
import br.com.alice.product.transport_model.ProductBundleUpdateSummary
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.mockk
import kotlinx.coroutines.runBlocking
import kotlin.test.Test

class ProductBundleConsumerTest : ConsumerTest() {
    private val kafkaProducerService: KafkaProducerService = mockk()

    private val consumer = ProductBundleConsumer(kafkaProducerService)

    private val provider1 = buildProvider()
    private val provider2 = buildProvider()
    private val providerBundle = buildProductBundle(providerIds = listOf(provider1.id, provider2.id))

    private val cassiSpecialist1 = buildCassiSpecialist()
    private val cassiSpecialist2 = buildCassiSpecialist()
    private val cassiBundle = buildProductBundle(
        providerIds = emptyList(),
        externalSpecialists = listOf(cassiSpecialist1.id, cassiSpecialist2.id),
        type = ProductBundleType.CASSI_SPECIALIST
    )

    private val specialty1 = SpecialtyTiers(
        specialtyId = RangeUUID.generate(),
        tiers = listOf(SpecialistTier.EXPERT)
    )
    private val specialty2 = SpecialtyTiers(
        specialtyId = RangeUUID.generate(),
        tiers = listOf(SpecialistTier.SUPER_EXPERT)
    )
    private val specialty3 = SpecialtyTiers(
        specialtyId = RangeUUID.generate(),
        tiers = listOf(SpecialistTier.OPT_IN)
    )

    private val productBundleUpdateSummary = ProductBundleUpdateSummary()
    @Test
    fun `sendAccreditedNetworkProviderByBundleCreate should send provider events`() = runBlocking {
        coEvery {
            kafkaProducerService.produce(ProviderBundleChangeEvent(provider1.id))
        } returns produceResult
        coEvery {
            kafkaProducerService.produce(ProviderBundleChangeEvent(provider2.id))
        } returns produceResult

        val result = consumer.sendAccreditedNetworkProviderByBundleCreate(ProductBundleCreatedEvent(providerBundle))
        assertThat(result).isSuccess()

        coVerify(exactly = 2) { kafkaProducerService.produce(any<AccreditedNetworkRegisterUnitEvent>()) }
    }

    @Test
    fun `sendAccreditedNetworkProviderByBundleCreate should not send events when providers bundle is empty`(): Unit =
        runBlocking {
            val event = ProductBundleCreatedEvent(providerBundle.copy(providerIds = emptyList()))
            val result = consumer.sendAccreditedNetworkProviderByBundleCreate(event)
            assertThat(result).isSuccessWithData(false)
        }

    @Test
    fun `sendAccreditedNetworkProviderByBundleUpdate should send provider events`() = runBlocking {
        val productBundleUpdateSummary = ProductBundleUpdateSummary(
            providerIdsAdded = listOf(provider1.id, provider2.id)
        )
        coEvery {
            kafkaProducerService.produce(ProviderBundleChangeEvent(provider1.id))
        } returns produceResult
        coEvery {
            kafkaProducerService.produce(ProviderBundleChangeEvent(provider2.id))
        } returns produceResult

        val result = consumer.sendAccreditedNetworkProviderByBundleUpdate(
            ProductBundleUpdatedEvent(
                productBundle = providerBundle,
                productBundleUpdateSummary = productBundleUpdateSummary
            )
        )
        assertThat(result).isSuccess()

        coVerify(exactly = 2) { kafkaProducerService.produce(any<AccreditedNetworkRegisterUnitEvent>()) }
    }

    @Test
    fun `sendAccreditedNetworkProviderByBundleUpdate should not send events when providers bundle is empty`(): Unit =
        runBlocking {
            val event = ProductBundleUpdatedEvent(
                productBundle = providerBundle.copy(
                    providerIds = emptyList()
                ),
                productBundleUpdateSummary = productBundleUpdateSummary
            )
            val result = consumer.sendAccreditedNetworkProviderByBundleUpdate(event)
            assertThat(result).isSuccessWithData(false)
        }

    @Test
    fun `sendAccreditedNetworkProviderByBundleUpdate should send cassi specialist events`() = runBlocking {
        val productBundleUpdateSummary = ProductBundleUpdateSummary(
            externalSpecialistsIdsAdded = listOf(cassiSpecialist1.id, cassiSpecialist2.id)
        )

        coEvery {
            kafkaProducerService.produce(
                AccreditedNetworkRegisterCassiEvent(
                    cassiSpecialist1.id,
                    AccreditedNetworkCassiOrigin.PRODUCT_BUNDLE
                )
            )
        } returns produceResult
        coEvery {
            kafkaProducerService.produce(
                AccreditedNetworkRegisterCassiEvent(
                    cassiSpecialist2.id,
                    AccreditedNetworkCassiOrigin.PRODUCT_BUNDLE
                )
            )
        } returns produceResult

        val result = consumer.sendAccreditedNetworkProviderByBundleUpdate(
            ProductBundleUpdatedEvent(
                productBundle = cassiBundle,
                productBundleUpdateSummary = productBundleUpdateSummary
            )
        )
        assertThat(result).isSuccess()

        coVerify(exactly = 2) { kafkaProducerService.produce(any<AccreditedNetworkRegisterUnitEvent>()) }
    }

    @Test
    fun `sendAccreditedNetworkProviderByBundleUpdate should not send events when cassi specialist bundle is empty`(): Unit =
        runBlocking {
            val event = ProductBundleUpdatedEvent(
                productBundle = cassiBundle.copy(externalSpecialists = emptyList()),
                productBundleUpdateSummary = productBundleUpdateSummary
            )
            val result = consumer.sendAccreditedNetworkProviderByBundleUpdate(event)
            assertThat(result).isSuccessWithData(false)
        }

    @Test
    fun `sendAccreditedNetworkProviderByBundleCreate should send cassi specialist events`() = runBlocking {
        coEvery {
            kafkaProducerService.produce(
                AccreditedNetworkRegisterCassiEvent(
                    cassiSpecialist1.id,
                    AccreditedNetworkCassiOrigin.PRODUCT_BUNDLE
                )
            )
        } returns produceResult
        coEvery {
            kafkaProducerService.produce(
                AccreditedNetworkRegisterCassiEvent(
                    cassiSpecialist2.id,
                    AccreditedNetworkCassiOrigin.PRODUCT_BUNDLE
                )
            )
        } returns produceResult

        val result = consumer.sendAccreditedNetworkProviderByBundleCreate(ProductBundleCreatedEvent(cassiBundle))
        assertThat(result).isSuccess()

        coVerify(exactly = 2) { kafkaProducerService.produce(any<AccreditedNetworkRegisterUnitEvent>()) }
    }

    @Test
    fun `sendAccreditedNetworkProviderByBundleCreate should not send events when cassi specialist bundle is empty`(): Unit =
        runBlocking {
            val event = ProductBundleCreatedEvent(cassiBundle.copy(externalSpecialists = emptyList()))
            val result = consumer.sendAccreditedNetworkProviderByBundleCreate(event)
            assertThat(result).isSuccessWithData(false)
        }

    @Test
    fun `sendAccreditedNetworkProviderByBundleUpdate should send specialtyTiersEvent when bundle is activated`() = runBlocking {
        val toSendEvent = listOf(specialty1, specialty3)
        toSendEvent.forEach {
            coEvery {
                kafkaProducerService.produce(
                    SpecialtyTiersBundleChangeEvent(it)
                )
            } returns produceResult
        }

        val result = consumer.sendAccreditedNetworkProviderByBundleUpdate(ProductBundleUpdatedEvent(
            ProductBundle(
                name = "Specialty Tiers Bundle",
                type = ProductBundleType.SPECIALITY_TIERS,
                specialtyTiers = listOf(specialty1, specialty3)
            ), ProductBundleUpdateSummary(
                specialtyTiersAdded = listOf(specialty1),
                specialtyTiersRemoved = listOf(specialty2, specialty2.copy()),
                activated = true,
            )
        ))
        assertThat(result).isSuccess()

        coVerify(exactly = toSendEvent.size) { kafkaProducerService.produce(any()) }
    }

    @Test
    fun `sendAccreditedNetworkProviderByBundleUpdate should send specialtyTiersEvent when bundle is inactivated`() = runBlocking {
        val toSendEvent = listOf(specialty1, specialty3, specialty2)
        toSendEvent.forEach {
            coEvery {
                kafkaProducerService.produce(
                    SpecialtyTiersBundleChangeEvent(it)
                )
            } returns produceResult
        }

        val result = consumer.sendAccreditedNetworkProviderByBundleUpdate(ProductBundleUpdatedEvent(
            ProductBundle(
                name = "Specialty Tiers Bundle",
                type = ProductBundleType.SPECIALITY_TIERS,
                specialtyTiers = listOf(specialty1, specialty3)
            ), ProductBundleUpdateSummary(
                specialtyTiersAdded = listOf(specialty1),
                specialtyTiersRemoved = listOf(specialty2, specialty2.copy()),
                inactivated = true,
            )
        ))
        assertThat(result).isSuccess()

        coVerify(exactly = toSendEvent.size) { kafkaProducerService.produce(any()) }
    }

    @Test
    fun `sendAccreditedNetworkProviderByBundleCreate should send specialtyTiersEvent when active`() = runBlocking {
        val toSendEvent = listOf(specialty1, specialty2)
        toSendEvent.forEach {
            coEvery {
                kafkaProducerService.produce(
                    SpecialtyTiersBundleChangeEvent(it)
                )
            } returns produceResult
        }

        val result = consumer.sendAccreditedNetworkProviderByBundleCreate(ProductBundleCreatedEvent(
            ProductBundle(
                name = "Specialty Tiers Bundle",
                type = ProductBundleType.SPECIALITY_TIERS,
                specialtyTiers = listOf(specialty1, specialty2),
                active = true
            )
        ))
        assertThat(result).isSuccess()
        coVerify(exactly = toSendEvent.size) { kafkaProducerService.produce(any()) }
    }

    @Test
    fun `sendAccreditedNetworkProviderByBundleCreate should not send specialtyTiersEvent when inactive`() = runBlocking {
        val result = consumer.sendAccreditedNetworkProviderByBundleCreate(ProductBundleCreatedEvent(
            ProductBundle(
                name = "Specialty Tiers Bundle",
                type = ProductBundleType.SPECIALITY_TIERS,
                specialtyTiers = listOf(specialty1, specialty2)
            )
        ))
        assertThat(result).isSuccess()

        coVerifyNone { kafkaProducerService.produce(any()) }
    }

    @Test
    fun `sendAccreditedNetworkProviderByBundleUpdate should send specialtyTiersEvent with bundle tiers and removed`() = runBlocking {
        val toSendEvent = listOf(specialty1, specialty2)
        toSendEvent.forEach {
            coEvery {
                kafkaProducerService.produce(
                    SpecialtyTiersBundleChangeEvent(it)
                )
            } returns produceResult
        }

        val result = consumer.sendAccreditedNetworkProviderByBundleUpdate(ProductBundleUpdatedEvent(
            ProductBundle(
                name = "Specialty Tiers Bundle",
                type = ProductBundleType.SPECIALITY_TIERS,
                specialtyTiers = listOf(specialty1, specialty3)
            ), ProductBundleUpdateSummary(
                specialtyTiersAdded = listOf(specialty1),
                specialtyTiersRemoved = listOf(specialty2, specialty2.copy())
            )
        ))
        assertThat(result).isSuccess()

        coVerify(exactly = toSendEvent.size) { kafkaProducerService.produce(any()) }
    }

    @Test
    fun `sendAccreditedNetworkProviderByBundleUpdate should send ProviderBundleChangeEvent when bundle is activated`() = runBlocking {
        val provider3 = buildProvider()
        val provider4 = buildProvider()
        val bundle = buildProductBundle(
            type = ProductBundleType.HOSPITAL,
            providerIds = listOf(provider1.id, provider2.id)
        ).copy(active = true)
        val summary = ProductBundleUpdateSummary(
            providerIdsAdded = listOf(provider3.id),
            providerIdsRemoved = listOf(provider4.id),
            activated = true,
        )
        val event = ProductBundleUpdatedEvent(
            productBundle = bundle,
            productBundleUpdateSummary = summary,
        )

        coEvery {
            kafkaProducerService.produce(
                ProviderBundleChangeEvent(provider1.id)
            )
        } returns produceResult
        coEvery {
            kafkaProducerService.produce(
                ProviderBundleChangeEvent(provider2.id)
            )
        } returns produceResult

        val result = consumer.sendAccreditedNetworkProviderByBundleUpdate(event)
        assertThat(result).isSuccess()

        coVerify(exactly = 2) { kafkaProducerService.produce(any()) }
    }

    @Test
    fun `sendAccreditedNetworkProviderByBundleUpdate should send ProviderBundleChangeEvent when bundle is inactivated`() = runBlocking {
        val provider3 = buildProvider()
        val provider4 = buildProvider()
        val bundle = buildProductBundle(
            type = ProductBundleType.HOSPITAL,
            providerIds = listOf(provider1.id, provider2.id)
        ).copy(active = true)
        val summary = ProductBundleUpdateSummary(
            providerIdsAdded = listOf(provider3.id),
            providerIdsRemoved = listOf(provider4.id),
            inactivated = true,
        )
        val event = ProductBundleUpdatedEvent(
            productBundle = bundle,
            productBundleUpdateSummary = summary,
        )

        coEvery {
            kafkaProducerService.produce(
                ProviderBundleChangeEvent(provider1.id)
            )
        } returns produceResult
        coEvery {
            kafkaProducerService.produce(
                ProviderBundleChangeEvent(provider2.id)
            )
        } returns produceResult
        coEvery {
            kafkaProducerService.produce(
                ProviderBundleChangeEvent(provider4.id)
            )
        } returns produceResult

        val result = consumer.sendAccreditedNetworkProviderByBundleUpdate(event)
        assertThat(result).isSuccess()

        coVerify(exactly = 3) { kafkaProducerService.produce(any()) }
    }

    @Test
    fun `sendAccreditedNetworkProviderByBundleUpdate should send ProviderBundleChangeEvent with provider ids and removed`() = runBlocking {
        val provider3 = buildProvider()
        val provider4 = buildProvider()
        val bundle = buildProductBundle(
            type = ProductBundleType.HOSPITAL,
            providerIds = listOf(provider1.id, provider2.id)
        ).copy(active = true)
        val summary = ProductBundleUpdateSummary(
            providerIdsAdded = listOf(provider3.id),
            providerIdsRemoved = listOf(provider4.id)
        )
        val event = ProductBundleUpdatedEvent(
            productBundle = bundle,
            productBundleUpdateSummary = summary,
        )

        coEvery {
            kafkaProducerService.produce(
                ProviderBundleChangeEvent(provider3.id)
            )
        } returns produceResult
        coEvery {
            kafkaProducerService.produce(
                ProviderBundleChangeEvent(provider4.id)
            )
        } returns produceResult

        val result = consumer.sendAccreditedNetworkProviderByBundleUpdate(event)
        assertThat(result).isSuccess()

        coVerify(exactly = 2) { kafkaProducerService.produce(any()) }
    }

    @Test
    fun `sendAccreditedNetworkProviderByBundleCreate should not send ProviderBundleChangeEvent when inactive`() = runBlocking {
        val bundle = buildProductBundle(
            type = ProductBundleType.HOSPITAL,
            providerIds = listOf(provider1.id, provider2.id),
        ).copy(active = false)
        val event = ProductBundleCreatedEvent(
            productBundle = bundle,
        )

        val result = consumer.sendAccreditedNetworkProviderByBundleCreate(event)

        assertThat(result).isSuccess()

        coVerifyNone { kafkaProducerService.produce(any()) }
    }

    @Test
    fun `sendAccreditedNetworkProviderByBundleCreate should send ProviderBundleChangeEvent when active`() = runBlocking {
        val bundle = buildProductBundle(
            type = ProductBundleType.HOSPITAL,
            providerIds = listOf(provider1.id, provider2.id),
        ).copy(active = true)
        val event = ProductBundleCreatedEvent(
            productBundle = bundle,
        )

        coEvery {
            kafkaProducerService.produce(ProviderBundleChangeEvent(provider1.id))
        } returns produceResult
        coEvery {
            kafkaProducerService.produce(ProviderBundleChangeEvent(provider2.id))
        } returns produceResult

        val result = consumer.sendAccreditedNetworkProviderByBundleCreate(event)

        assertThat(result).isSuccess()

        coVerify(exactly = 2) { kafkaProducerService.produce(any()) }
    }

    @Test
    fun `sendAccreditedNetworkProviderByBundleCreate should send ProviderBundleChangeEvent when active when type is VACCINE`() = runBlocking {
        val bundle = buildProductBundle(
            type = ProductBundleType.VACCINE,
            providerIds = listOf(provider1.id, provider2.id),
        ).copy(active = true)
        val event = ProductBundleCreatedEvent(
            productBundle = bundle,
        )

        coEvery {
            kafkaProducerService.produce(ProviderBundleChangeEvent(provider1.id))
        } returns produceResult
        coEvery {
            kafkaProducerService.produce(ProviderBundleChangeEvent(provider2.id))
        } returns produceResult

        val result = consumer.sendAccreditedNetworkProviderByBundleCreate(event)

        assertThat(result).isSuccess()

        coVerify(exactly = 2) { kafkaProducerService.produce(any()) }
    }

    @Test
    fun `sendAccreditedNetworkProviderByBundleUpdate should send AccreditedNetworkRegisterCassiEvent with specialist ids added and removed`() = runBlocking {
        val cassiSpecialist1 = buildCassiSpecialist()
        val cassiSpecialist2 = buildCassiSpecialist()
        val cassiSpecialist3 = buildCassiSpecialist()
        val cassiSpecialist4 = buildCassiSpecialist()

        val bundle = buildProductBundle(
            type = ProductBundleType.CASSI_SPECIALIST,
            externalSpecialists = listOf(cassiSpecialist1.id, cassiSpecialist2.id)
        ).copy(active = true)
        val summary = ProductBundleUpdateSummary(
            externalSpecialistsIdsAdded = listOf(cassiSpecialist3.id),
            externalSpecialistsIdsRemoved = listOf(cassiSpecialist4.id)
        )
        val event = ProductBundleUpdatedEvent(
            productBundle = bundle,
            productBundleUpdateSummary = summary,
        )

        coEvery {
            kafkaProducerService.produce(
                AccreditedNetworkRegisterCassiEvent(
                    cassiSpecialist3.id,
                    AccreditedNetworkCassiOrigin.PRODUCT_BUNDLE,
                )
            )
        } returns produceResult
        coEvery {
            kafkaProducerService.produce(
                AccreditedNetworkRegisterCassiEvent(
                    cassiSpecialist4.id,
                    AccreditedNetworkCassiOrigin.PRODUCT_BUNDLE,
                )
            )
        } returns produceResult

        val result = consumer.sendAccreditedNetworkProviderByBundleUpdate(event)
        assertThat(result).isSuccess()

        coVerify(exactly = 2) { kafkaProducerService.produce(any()) }
    }

    @Test
    fun `sendAccreditedNetworkProviderByBundleCreate should not send AccreditedNetworkRegisterCassiEvent when inactive`() = runBlocking {
        val cassiSpecialist1 = buildCassiSpecialist()
        val cassiSpecialist2 = buildCassiSpecialist()
        val bundle = buildProductBundle(
            type = ProductBundleType.CASSI_SPECIALIST,
            externalSpecialists = listOf(cassiSpecialist1.id, cassiSpecialist2.id),
        ).copy(active = false)
        val event = ProductBundleCreatedEvent(
            productBundle = bundle,
        )

        val result = consumer.sendAccreditedNetworkProviderByBundleCreate(event)

        assertThat(result).isSuccess()

        coVerifyNone { kafkaProducerService.produce(any()) }
    }

    @Test
    fun `sendAccreditedNetworkProviderByBundleCreate should send AccreditedNetworkRegisterCassiEvent when active`() = runBlocking {
        val cassiSpecialist1 = buildCassiSpecialist()
        val cassiSpecialist2 = buildCassiSpecialist()
        val bundle = buildProductBundle(
            type = ProductBundleType.CASSI_SPECIALIST,
            externalSpecialists = listOf(cassiSpecialist1.id, cassiSpecialist2.id),
        ).copy(active = true)
        val event = ProductBundleCreatedEvent(
            productBundle = bundle,
        )

        coEvery {
            kafkaProducerService.produce(
                AccreditedNetworkRegisterCassiEvent(
                    cassiSpecialist1.id,
                    AccreditedNetworkCassiOrigin.PRODUCT_BUNDLE,
                )
            )
        } returns produceResult
        coEvery {
            kafkaProducerService.produce(
                AccreditedNetworkRegisterCassiEvent(
                    cassiSpecialist2.id,
                    AccreditedNetworkCassiOrigin.PRODUCT_BUNDLE,
                )
            )
        } returns produceResult

        val result = consumer.sendAccreditedNetworkProviderByBundleCreate(event)

        assertThat(result).isSuccess()

        coVerify(exactly = 2) { kafkaProducerService.produce(any()) }
    }

    @Test
    fun `sendAccreditedNetworkProviderByBundleUpdate should send AccreditedNetworkRegisterCassiEvent when bundle is activated`() = runBlocking {
        val cassiSpecialist1 = buildCassiSpecialist()
        val cassiSpecialist2 = buildCassiSpecialist()
        val cassiSpecialist3 = buildCassiSpecialist()
        val cassiSpecialist4 = buildCassiSpecialist()

        val bundle = buildProductBundle(
            type = ProductBundleType.CASSI_SPECIALIST,
            externalSpecialists = listOf(cassiSpecialist1.id, cassiSpecialist2.id)
        ).copy(active = true)
        val summary = ProductBundleUpdateSummary(
            externalSpecialistsIdsAdded = listOf(cassiSpecialist3.id),
            externalSpecialistsIdsRemoved = listOf(cassiSpecialist4.id),
            activated = true,
        )
        val event = ProductBundleUpdatedEvent(
            productBundle = bundle,
            productBundleUpdateSummary = summary,
        )

        coEvery {
            kafkaProducerService.produce(
                AccreditedNetworkRegisterCassiEvent(
                    cassiSpecialist1.id,
                    AccreditedNetworkCassiOrigin.PRODUCT_BUNDLE,
                )
            )
        } returns produceResult
        coEvery {
            kafkaProducerService.produce(
                AccreditedNetworkRegisterCassiEvent(
                    cassiSpecialist2.id,
                    AccreditedNetworkCassiOrigin.PRODUCT_BUNDLE,
                )
            )
        } returns produceResult

        val result = consumer.sendAccreditedNetworkProviderByBundleUpdate(event)
        assertThat(result).isSuccess()

        coVerify(exactly = 2) { kafkaProducerService.produce(any()) }
    }

    @Test
    fun `sendAccreditedNetworkProviderByBundleUpdate should send AccreditedNetworkRegisterCassiEvent when bundle is inactivated`() = runBlocking {
        val cassiSpecialist1 = buildCassiSpecialist()
        val cassiSpecialist2 = buildCassiSpecialist()
        val cassiSpecialist3 = buildCassiSpecialist()
        val cassiSpecialist4 = buildCassiSpecialist()

        val bundle = buildProductBundle(
            type = ProductBundleType.CASSI_SPECIALIST,
            externalSpecialists = listOf(cassiSpecialist1.id, cassiSpecialist2.id)
        ).copy(active = true)
        val summary = ProductBundleUpdateSummary(
            externalSpecialistsIdsAdded = listOf(cassiSpecialist3.id),
            externalSpecialistsIdsRemoved = listOf(cassiSpecialist4.id),
            inactivated = true,
        )
        val event = ProductBundleUpdatedEvent(
            productBundle = bundle,
            productBundleUpdateSummary = summary,
        )

        coEvery {
            kafkaProducerService.produce(
                AccreditedNetworkRegisterCassiEvent(
                    cassiSpecialist1.id,
                    AccreditedNetworkCassiOrigin.PRODUCT_BUNDLE,
                )
            )
        } returns produceResult
        coEvery {
            kafkaProducerService.produce(
                AccreditedNetworkRegisterCassiEvent(
                    cassiSpecialist2.id,
                    AccreditedNetworkCassiOrigin.PRODUCT_BUNDLE,
                )
            )
        } returns produceResult
        coEvery {
            kafkaProducerService.produce(
                AccreditedNetworkRegisterCassiEvent(
                    cassiSpecialist4.id,
                    AccreditedNetworkCassiOrigin.PRODUCT_BUNDLE,
                )
            )
        } returns produceResult

        val result = consumer.sendAccreditedNetworkProviderByBundleUpdate(event)
        assertThat(result).isSuccess()

        coVerify(exactly = 3) { kafkaProducerService.produce(any()) }
    }
}
