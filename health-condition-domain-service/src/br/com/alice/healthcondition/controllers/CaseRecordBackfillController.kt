package br.com.alice.healthcondition.controllers

import br.com.alice.clinicalaccount.client.PersonInternalReferenceService
import br.com.alice.common.Response
import br.com.alice.common.core.extensions.toPersonId
import br.com.alice.common.foldResponse
import br.com.alice.common.logging.logger
import br.com.alice.data.layer.models.CaseRecordCreatedByType
import br.com.alice.data.layer.models.CaseSeverity
import br.com.alice.data.layer.models.CaseStatus
import br.com.alice.healthcondition.client.CaseRecordService
import br.com.alice.healthcondition.model.CaseRecordUpdatedRequest
import br.com.alice.healthcondition.services.PersonCaseServiceImpl
import com.github.kittinunf.result.flatMap
import com.github.kittinunf.result.map
import com.github.kittinunf.result.success
import io.ktor.http.HttpStatusCode
import java.time.LocalDateTime
import java.util.UUID
import java.util.concurrent.atomic.AtomicInteger

class CaseRecordBackfillController(
    private val personCaseService: PersonCaseServiceImpl,
    private val caseRecordService: CaseRecordService,
    private val personInternalReferenceService: PersonInternalReferenceService,
) : HealthConditionController() {

    suspend fun inactiveConditions(request: ConditionsIdsRequest): Response = withHealthConditionEnvironment {
        val successCount = AtomicInteger(0)
        val errorsCount = AtomicInteger(0)
        val mapErrors = mutableMapOf<UUID, Any?>()

        if (request.ids.isEmpty()) return@withHealthConditionEnvironment Response(
            HttpStatusCode.OK,
            InactiveConditionsResponse(successCount, errorsCount, mapErrors)
        )

        personCaseService.findByIds(request.ids).flatMap { personCases ->
            personCases.forEach { personCase ->
                if (personCase.addedAt.isBefore(LocalDateTime.now().minusDays(30))) {
                    caseRecordService.edit(
                        CaseRecordUpdatedRequest(
                            personId = personCase.personId,
                            caseId = personCase.id,
                            status = personCase.status,
                            responsibleStaffId = personCase.responsibleStaffId!!,
                            description = personCase.asDisease,
                            observation = personCase.observation,
                            severity = CaseSeverity.INACTIVE,
                            startedAt = personCase.startedAt,
                            caseCreatedBy = CaseRecordCreatedByType.SYSTEM
                        )
                    ).fold(
                        {
                            successCount.incrementAndGet()
                        }, {
                            logger.error("Error in backfill inactivation of ciaps", it)

                            errorsCount.incrementAndGet()
                            mapErrors.put(personCase.id, it.message)
                        }
                    )
                }
            }

            InactiveConditionsResponse(successCount, errorsCount, mapErrors).success()
        }.foldResponse()
    }

    suspend fun cretePersonCaseByRecordId(request: ConditionsIdsRequest) = withHealthConditionEnvironment {
        val successCount = AtomicInteger(0)
        val errorsCount = AtomicInteger(0)
        val mapErrors = mutableMapOf<UUID, Any?>()

        caseRecordService.getByIds(request.ids).flatMap { cases ->

            cases.forEach { case ->
                personCaseService.createdByCaseRecord(case).fold(
                    {
                        successCount.incrementAndGet()
                    }, {
                        logger.error("Error in backfill to create person case from case record", it)

                        errorsCount.incrementAndGet()
                        mapErrors[case.id] = it.message
                    }
                )
            }
            InactiveConditionsResponse(successCount, errorsCount, mapErrors).success()

        }.foldResponse()

    }

    suspend fun createMissingPersonCaseByInternalCode(request: PersonCaseRecordToPersonCaseRequest) =
        withHealthConditionEnvironment {
            val successCount = AtomicInteger(0)
            val errorsCount = AtomicInteger(0)
            val mapErrors = mutableMapOf<UUID, Any?>()

            personInternalReferenceService.getByInternalCodes(request.memberInternalCodes).map { people ->
                people.map { person ->
                    personCaseService.getByPersonId(personId = person.personId).flatMap { personCases ->
                        val personHealthConditionIds = personCases.map { it.healthConditionId }
                        val caseIds = personCases.map { it.id }
                        logger.info(
                            "createMissingPersonCaseByInternalCode: found person cases",
                            "person_id" to person.personId,
                            "case_records_filtered" to personHealthConditionIds,
                            "case_ids" to caseIds
                        )
                        caseRecordService.getCasesByPersonId(personId = person.personId).map { caseRecords ->
                            logger.info(
                                "createMissingPersonCaseByInternalCode: found case records",
                                "person_id" to person.personId,
                                "case_records" to caseRecords
                            )
                            caseRecords.filter { !caseIds.contains(it.caseId) && !personHealthConditionIds.contains(it.healthConditionId) }
                                .distinctBy { it.healthConditionId } // Get unique case record health condition ids and check if person don't have this caseRecord already or this health condition
                        }
                    }.map { filteredCaseRecords ->
                        logger.info(
                            "createMissingPersonCaseByInternalCode: Creating person case from case record",
                            "person_id" to person.personId,
                            "case_records_filtered" to filteredCaseRecords
                        )
                        filteredCaseRecords.forEach { case ->
                            personCaseService.createdByCaseRecord(case).fold(
                                {
                                    successCount.incrementAndGet()
                                }, {
                                    logger.error(
                                        "createPersonCaseByCaseRecordsPerson: Error in backfill to create person case from case record",
                                        "person_id" to person.personId,
                                        it
                                    )

                                    errorsCount.incrementAndGet()
                                    mapErrors[case.id] = it.message
                                }
                            )
                        }
                    }
                }
                InactiveConditionsResponse(successCount, errorsCount, mapErrors)
            }.foldResponse()
        }

    suspend fun setMemberFormerManagerPhysicianOnCaseRecord(request: ConditionsCaseRecordsRequest): Response =
        withHealthConditionEnvironment {
            val successCount = AtomicInteger(0)
            val errorsCount = AtomicInteger(0)
            val mapErrors = mutableMapOf<UUID, Any?>()
            val observationMessage = "Migração para a Liga de Saúde"
            request.records.forEach { record ->
                personCaseService.findByIds(record.caseList).map { personCases ->
                    personCases.forEach { personCase ->
                        if (personCase.personId == record.personId.toPersonId() && listOf(
                                CaseStatus.ACTIVE,
                                CaseStatus.PENDING
                            ).contains(personCase.status) &&
                            personCase.responsibleStaffId != record.newResponsibleStaffId &&
                            personCase.severity != CaseSeverity.INACTIVE
                        ) {
                            caseRecordService.edit(
                                CaseRecordUpdatedRequest(
                                    personId = personCase.personId,
                                    caseId = personCase.id,
                                    status = personCase.status,
                                    responsibleStaffId = record.newResponsibleStaffId,
                                    description = personCase.asDisease,
                                    observation = observationMessage,
                                    severity = personCase.severity,
                                    startedAt = personCase.startedAt,
                                    addedByStaffId = record.newResponsibleStaffId,
                                    caseCreatedBy = CaseRecordCreatedByType.STAFF
                                )
                            ).fold(
                                {
                                    successCount.incrementAndGet()
                                }, {
                                    logger.error("Error in backfill to set former manager physician on case record", it)

                                    errorsCount.incrementAndGet()
                                    mapErrors.put(personCase.id, it.message)
                                }
                            )
                        } else {
                            errorsCount.incrementAndGet()
                            mapErrors.put(personCase.id, "Person case is not available")
                        }
                    }
                }
            }
            InactiveConditionsResponse(successCount, errorsCount, mapErrors).success().foldResponse()
        }
}


data class ConditionsIdsRequest(
    val ids: List<UUID> = emptyList()
)

data class ConditionsCaseRecordsRequest(
    val records: List<CaseRecordsIdsAndManagerPhysiciansIds> = emptyList()
)

data class CaseRecordsIdsAndManagerPhysiciansIds(
    val personId: UUID,
    val caseList: List<UUID>,
    val newResponsibleStaffId: UUID
)

data class InactiveConditionsResponse(
    val successCount: AtomicInteger,
    val errorsCount: AtomicInteger,
    val mapErrors: Map<UUID, Any?> = emptyMap()
)

data class PersonCaseRecordToPersonCaseRequest(
    val memberInternalCodes: List<String>
)
