package br.com.alice.app.content.services.router

import br.com.alice.app.content.client.screens.AliceNowScreenService
import br.com.alice.app.content.client.screens.AppointmentHubScreenService
import br.com.alice.app.content.client.screens.CopayDetailsScreenService
import br.com.alice.app.content.client.screens.CopayScreenService
import br.com.alice.app.content.client.screens.DemandScreenService
import br.com.alice.app.content.client.screens.ExplorerScreenService
import br.com.alice.app.content.client.screens.HealthScreenService
import br.com.alice.app.content.client.screens.MainMenuService
import br.com.alice.app.content.client.screens.MemberProfileScreenService
import br.com.alice.app.content.client.screens.OmbudsmanScreenService
import br.com.alice.app.content.client.screens.RedesignHealthPlanScreenService
import br.com.alice.app.content.client.screens.TestResultScreenService
import br.com.alice.app.content.client.screens.UnifiedHomeScreenService
import br.com.alice.app.content.client.screens.RedesignUnifiedHealthScreenService
import br.com.alice.app.content.constants.CHANNELS_SCREEN
import br.com.alice.app.content.constants.SCHEDULE_SCREEN_WITH_APP_BAR
import br.com.alice.app.content.model.AliceScreensData
import br.com.alice.app.content.model.PaginationLayout
import br.com.alice.app.content.model.RemoteAction
import br.com.alice.app.content.model.ScreenLayout
import br.com.alice.app.content.model.ScreenType
import br.com.alice.app.content.model.ScreensTransport
import br.com.alice.app.content.model.redesignHealthPlanHome
import br.com.alice.app.content.services.screens.AccreditedNetworkMenuServiceImpl
import br.com.alice.common.RangeUUID
import br.com.alice.common.core.PersonId
import br.com.alice.common.data.dsl.matchers.ResultAssert.Companion.assertThat
import br.com.alice.common.featureflag.withFeatureFlag
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.common.mobile.Platform
import br.com.alice.common.mobile.SemanticVersion
import br.com.alice.data.layer.models.ActionPlanTaskStatus
import br.com.alice.data.layer.models.ActionPlanTaskType
import br.com.alice.common.Brand
import br.com.alice.data.layer.models.DemandActionPlanStatus
import br.com.alice.data.layer.models.FeatureNamespace
import br.com.alice.data.layer.models.HealthFormQuestionType
import br.com.alice.questionnaire.models.QuestionnaireQuestionInputResponse
import br.com.alice.questionnaire.models.QuestionnaireQuestionResponse
import com.github.kittinunf.result.success
import io.mockk.called
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.confirmVerified
import io.mockk.mockk
import kotlinx.coroutines.runBlocking
import kotlin.test.Test

class AliceScreensServiceImplTest {

    private val aliceNowScreenService: AliceNowScreenService = mockk()
    private val unifiedHomeScreenService: UnifiedHomeScreenService = mockk()
    private val healthScreenService: HealthScreenService = mockk()
    private val demandScreenService: DemandScreenService = mockk()
    private val explorerScreenService: ExplorerScreenService = mockk()
    private val mainMenuService: MainMenuService = mockk()
    private val memberProfileScreenService: MemberProfileScreenService = mockk()
    private val accreditedNetworkMenuServiceImpl: AccreditedNetworkMenuServiceImpl = mockk()
    private val testResultScreenService: TestResultScreenService = mockk()
    private val copayScreenService: CopayScreenService = mockk()
    private val copayDetailsScreenService: CopayDetailsScreenService = mockk()
    private val ombudsmanScreenService: OmbudsmanScreenService = mockk()
    private val redesignUnifiedHealthScreenService: RedesignUnifiedHealthScreenService = mockk()
    private val redesignHealthPlanScreenService: RedesignHealthPlanScreenService = mockk()
    private val appointmentHubScreenService: AppointmentHubScreenService = mockk()

    private val service = AliceScreensServiceImpl(
        aliceNowScreenService,
        unifiedHomeScreenService,
        healthScreenService,
        demandScreenService,
        explorerScreenService,
        mainMenuService,
        memberProfileScreenService,
        accreditedNetworkMenuServiceImpl,
        testResultScreenService,
        copayScreenService,
        copayDetailsScreenService,
        ombudsmanScreenService,
        redesignUnifiedHealthScreenService,
        redesignHealthPlanScreenService,
        appointmentHubScreenService,
    )

    private val personId = PersonId()
    private val appVersion = SemanticVersion("2.0.0")

    @Test
    fun `#getOtherAliceScreens should get health screen`(): Unit =
        runBlocking {
            val healthScreen = ScreensTransport(
                id = ScreenType.HEALTH.value,
                layout = ScreenLayout(type = "single_column", body = emptyList())
            )
            coEvery { healthScreenService.getByPersonId(personId) } returns healthScreen.success()

            val result = service.getOtherAliceScreens(
                AliceScreensData(
                    personId, ScreenType.HEALTH, appVersion = appVersion
                )
            )
            assertThat(result).isSuccessWithData(healthScreen)
        }

    @Test
    fun `#getOtherAliceScreens should get redesign alice agora screen`() = runBlocking {
        val aliceNowScreen = ScreensTransport(
            id = ScreenType.REDESIGN_ALICE_AGORA.value,
            layout = ScreenLayout(type = "single_column", body = emptyList())
        )

        coEvery {
            aliceNowScreenService.getRedesignHomeAA(personId, appVersion)
        } returns aliceNowScreen.success()

        val result = service.getOtherAliceScreens(
            AliceScreensData(
                personId,
                ScreenType.REDESIGN_ALICE_AGORA,
                appVersion = appVersion
            )
        )

        assertThat(result).isSuccessWithData(aliceNowScreen)

        coVerifyOnce { aliceNowScreenService.getRedesignHomeAA(personId, appVersion) }
    }

    @Test
    fun `#getOtherAliceScreens should get main menu`(): Unit =
        runBlocking {
            val mainMenu = ScreensTransport(
                id = ScreenType.MAIN_MENU.value,
                layout = ScreenLayout(type = "single_column", body = emptyList())
            )
            coEvery { mainMenuService.getMainMenu(personId) } returns mainMenu.success()

            val result = service.getOtherAliceScreens(
                AliceScreensData(
                    personId,
                    ScreenType.MAIN_MENU,
                    appVersion = appVersion,
                )
            )
            assertThat(result).isSuccessWithData(mainMenu)
        }

    @Test
    fun `#getOtherAliceScreens should get channels screen`(): Unit =
        runBlocking {
            val result = service.getOtherAliceScreens(
                AliceScreensData(
                    personId,
                    ScreenType.CHANNELS,
                    appVersion = appVersion
                )
            )

            assertThat(result).isSuccessWithData(CHANNELS_SCREEN)
        }

    @Test
    fun `#getOtherAliceScreens should get schedule screen with app bar`(): Unit =
        runBlocking {
            val result = service.getOtherAliceScreens(
                AliceScreensData(
                    personId,
                    ScreenType.SCHEDULE,
                    appVersion = appVersion,
                )
            )

            assertThat(result).isSuccessWithData(SCHEDULE_SCREEN_WITH_APP_BAR)
        }

    @Test
    fun `#getOtherAliceScreens should get health plan demand tasks screen`(): Unit =
        runBlocking {

            val demandId = RangeUUID.generate()

            val healthDemandTasksScreen = ScreensTransport(
                id = ScreenType.HEALTH_DEMAND_TASKS.value,
                layout = ScreenLayout(type = "single_column", body = emptyList())
            )
            coEvery { demandScreenService.getByDemandId(personId, demandId) } returns healthDemandTasksScreen.success()

            val result = service.getOtherAliceScreens(
                AliceScreensData(
                    personId,
                    ScreenType.HEALTH_DEMAND_TASKS,
                    demandId = demandId,
                    appVersion = appVersion
                )
            )

            assertThat(result).isSuccessWithData(healthDemandTasksScreen)

            coVerifyOnce { demandScreenService.getByDemandId(personId, demandId) }

            confirmVerified(
                demandScreenService
            )
        }

    @Test
    fun `#getOtherAliceScreens Should get plan details screen`(): Unit =
        runBlocking {

            val planDetailsScreen = ScreensTransport(
                id = ScreenType.PLAN_DETAILS_MENU.value,
                layout = ScreenLayout(type = "single_column", body = emptyList())
            )

            coEvery { mainMenuService.getPlanDetailsScreen() } returns planDetailsScreen.success()

            val result = service.getOtherAliceScreens(
                AliceScreensData(
                    personId,
                    ScreenType.PLAN_DETAILS_MENU,
                    appVersion = appVersion,
                )
            )

            assertThat(result).isSuccessWithData(planDetailsScreen)

            coVerifyOnce { mainMenuService.getPlanDetailsScreen() }
        }

    @Test
    fun `#getUnifiedHealthScreen Should get unified home screen`(): Unit =
        runBlocking {
            val home = ScreensTransport(
                id = ScreenType.UNIFIED_HEALTH.value,
                layout = ScreenLayout(type = "single_column", body = emptyList())
            )

            coEvery { unifiedHomeScreenService.getUnifiedScreenByPersonId(personId) } returns home.success()

            val result = service.getUnifiedHealthScreen(personId)

            assertThat(result).isSuccessWithData(home)
        }

    @Test
    fun `#getOtherAliceScreens should get AccreditedNetworkMenu`(): Unit =
        runBlocking {
            val accreditedNetworkMenu = ScreensTransport(
                id = ScreenType.ACCREDITED_NETWORK_MENU.value,
                layout = ScreenLayout(type = "single_column", body = emptyList())
            )

            coEvery {
                accreditedNetworkMenuServiceImpl.getAccreditedNetworkMenu(
                    any(),
                    any(),
                    any()
                )
            } returns accreditedNetworkMenu.success()

            val result =
                service.getOtherAliceScreens(
                    AliceScreensData(
                        personId,
                        ScreenType.ACCREDITED_NETWORK_MENU,
                        appVersion = appVersion,
                    )
                )

            assertThat(result).isSuccessWithData(accreditedNetworkMenu)
        }

    @Test
    fun `#getOtherAliceScreens should get AccreditedNetworkEmergencyModal`(): Unit =
        runBlocking {
            val accreditedNetworkEmergencyModal = ScreensTransport(
                id = ScreenType.ACCREDITED_NETWORK_EMERGENCY_MODAL.value,
                layout = ScreenLayout(type = "single_column", body = emptyList())
            )

            coEvery { accreditedNetworkMenuServiceImpl.getAccreditedNetworkEmergencyModal() } returns accreditedNetworkEmergencyModal.success()

            val result = service.getOtherAliceScreens(
                AliceScreensData(
                    personId,
                    ScreenType.ACCREDITED_NETWORK_EMERGENCY_MODAL,
                    appVersion = appVersion
                )
            )

            assertThat(result).isSuccessWithData(accreditedNetworkEmergencyModal)
        }

    @Test
    fun `#getOtherAliceScreens should get AccreditedNetworkEmergencyMenu`(): Unit =
        runBlocking {
            val accreditedNetworkEmergencyMenu = ScreensTransport(
                id = ScreenType.ACCREDITED_NETWORK_EMERGENCY_MENU.value,
                layout = ScreenLayout(type = "single_column", body = emptyList())
            )

            coEvery {
                accreditedNetworkMenuServiceImpl.getAccreditedNetworkEmergencyMenu(
                    Brand.ALICE,
                    appVersion
                )
            } returns accreditedNetworkEmergencyMenu.success()

            val result = service.getOtherAliceScreens(
                AliceScreensData(
                    personId,
                    ScreenType.ACCREDITED_NETWORK_EMERGENCY_MENU,
                    appVersion = appVersion
                )
            )

            assertThat(result).isSuccessWithData(accreditedNetworkEmergencyMenu)

            coVerifyOnce { accreditedNetworkMenuServiceImpl.getAccreditedNetworkEmergencyMenu(any(), any()) }
        }

    @Test
    fun `#getOtherAliceScreens should get AccreditedNetworkHospitalMenu`(): Unit =
        runBlocking {
            val accreditedNetworkHospitalMenu = ScreensTransport(
                id = ScreenType.ACCREDITED_NETWORK_EMERGENCY_MENU.value,
                layout = ScreenLayout(type = "single_column", body = emptyList())
            )

            coEvery { accreditedNetworkMenuServiceImpl.getAccreditedNetworkHospitalMenu() } returns accreditedNetworkHospitalMenu.success()

            val result = service.getOtherAliceScreens(
                AliceScreensData(
                    personId,
                    ScreenType.ACCREDITED_NETWORK_HOSPITAL_MENU,
                    appVersion = appVersion
                )
            )

            assertThat(result).isSuccessWithData(accreditedNetworkHospitalMenu)
        }

    @Test
    fun `#getCopayScreens should get copay menu screen`(): Unit =
        runBlocking {
            val fakeCopayMenuScreen = ScreensTransport(
                id = ScreenType.COPAY_MENU.value,
                layout = ScreenLayout(type = "single_column", body = emptyList())
            )
            coEvery { copayScreenService.getCopayMenuScreen(personId) } returns fakeCopayMenuScreen

            val result = service.getCopayScreens(personId, ScreenType.COPAY_MENU)

            assertThat(result).isSuccessWithData(fakeCopayMenuScreen)

            coVerifyOnce { copayScreenService.getCopayMenuScreen(personId) }
        }

    @Test
    fun `#getCopayScreens should get copay limits screen`(): Unit =
        runBlocking {
            val fakeCopayLimitsScreen = ScreensTransport(
                id = ScreenType.COPAY_LIMITS.value,
                layout = ScreenLayout(type = "single_column", body = emptyList())
            )
            coEvery { copayScreenService.getCopayLimitsScreen(personId) } returns fakeCopayLimitsScreen

            val result = service.getCopayScreens(personId, ScreenType.COPAY_LIMITS)

            assertThat(result).isSuccessWithData(fakeCopayLimitsScreen)

            coVerifyOnce { copayScreenService.getCopayLimitsScreen(personId) }
        }

    @Test
    fun `#getCopayScreens should throw error when screen is not for copay context`(): Unit =
        runBlocking {
            val result = service.getCopayScreens(personId, ScreenType.HEALTH_ALL_DEMANDS)

            assertThat(result).isFailureOfType(IllegalArgumentException::class)
        }

    @Test
    fun `#getOtherAliceScreens should throw error when is duquesa screen`(): Unit =
        runBlocking {
            val result = service.getOtherAliceScreens(
                AliceScreensData(
                    personId,
                    ScreenType.DUQUESA_HOME,
                    appVersion = appVersion
                )
            )

            assertThat(result).isFailureOfType(IllegalArgumentException::class)
        }

    @Test
    fun `#getOmbudsmanScreens should redirect to ombudsman service when OMBUDSMAN_MENU`(): Unit =
        runBlocking {
            val expectedScreen = ScreensTransport(
                id = ScreenType.OMBUDSMAN_MENU.value,
                layout = ScreenLayout(type = "single_column", body = emptyList())
            )
            coEvery { ombudsmanScreenService.getMenuScreen(personId) } returns expectedScreen.success()

            val result = service.getOmbudsmanScreens(personId, ScreenType.OMBUDSMAN_MENU, Platform.ANDROID)

            assertThat(result).isSuccessWithData(expectedScreen)

            coVerifyOnce { ombudsmanScreenService.getMenuScreen(any()) }
        }

    @Test
    fun `#getOmbudsmanScreens should redirect to ombudsman service when OMBUDSMAN_LOW_RISK`(): Unit =
        runBlocking {
            val expectedScreen = ScreensTransport(
                id = ScreenType.OMBUDSMAN_LOW_RISK.value,
                layout = ScreenLayout(type = "single_column", body = emptyList())
            )
            coEvery {
                ombudsmanScreenService.getLowRiskScreen(
                    personId,
                    Platform.ANDROID
                )
            } returns expectedScreen.success()

            val result = service.getOmbudsmanScreens(personId, ScreenType.OMBUDSMAN_LOW_RISK, Platform.ANDROID)

            assertThat(result).isSuccessWithData(expectedScreen)

            coVerifyOnce { ombudsmanScreenService.getLowRiskScreen(any(), any()) }
        }

    @Test
    fun `#getOmbudsmanScreens should redirect to ombudsman service when OMBUDSMAN_MEDIUM_RISK`(): Unit =
        runBlocking {
            val expectedScreen = ScreensTransport(
                id = ScreenType.OMBUDSMAN_MEDIUM_RISK.value,
                layout = ScreenLayout(type = "single_column", body = emptyList())
            )
            coEvery { ombudsmanScreenService.getMediumRiskScreen(personId) } returns expectedScreen.success()

            val result = service.getOmbudsmanScreens(personId, ScreenType.OMBUDSMAN_MEDIUM_RISK, Platform.ANDROID)

            assertThat(result).isSuccessWithData(expectedScreen)

            coVerifyOnce { ombudsmanScreenService.getMediumRiskScreen(any()) }
        }

    @Test
    fun `#getOmbudsmanScreens should throw error when not ombudsman context`(): Unit =
        runBlocking {
            val result = service.getOmbudsmanScreens(personId, ScreenType.COPAY_MENU, Platform.ANDROID)

            assertThat(result).isFailureOfType(IllegalArgumentException::class)

            coVerify { ombudsmanScreenService wasNot called }
        }


    @Test
    fun `#getPaginatedHealthDemandsScreen should call getPaginatedDemandsByPersonId`(): Unit =
        runBlocking {
            val paginationLayout = PaginationLayout(
                sections = emptyList(),
                action = null
            )

            coEvery {
                healthScreenService.getPaginatedDemandsByPersonId(
                    personId,
                    0,
                    3,
                    null
                )
            } returns paginationLayout.success()

            val result = service.getPaginatedHealthDemandsScreen(personId, 0, 3, null)
            assertThat(result).isSuccessWithData(paginationLayout)

            coVerifyOnce { healthScreenService.getPaginatedDemandsByPersonId(any(), any(), any(), any()) }
        }

    @Test
    fun `#getPaginatedHealthDemandsScreen should call getPaginatedDemandsByPersonId with filter type`(): Unit =
        runBlocking {
            val paginationLayout = PaginationLayout(
                sections = emptyList(),
                action = null
            )

            coEvery {
                healthScreenService.getPaginatedDemandsByPersonId(
                    personId,
                    0,
                    3,
                    "EATING"
                )
            } returns paginationLayout.success()

            val result = service.getPaginatedHealthDemandsScreen(personId, 0, 3, "EATING")
            assertThat(result).isSuccessWithData(paginationLayout)

            coVerifyOnce { healthScreenService.getPaginatedDemandsByPersonId(any(), any(), any(), any()) }
        }

    @Test
    fun `#getHealthDemandsScreen should call new flow`(): Unit =
        runBlocking {
            withFeatureFlag(FeatureNamespace.ALICE_APP, "health_plan_task_pagination_version", "2.4.0") {
                val expectedScreen = ScreensTransport(
                    id = ScreenType.HEALTH_ALL_DEMANDS.value,
                    layout = ScreenLayout(type = "single_column", body = emptyList())
                )

                coEvery {
                    healthScreenService.getFirstPageDemandsByPersonId(personId)
                } returns expectedScreen.success()

                val result = service.getHealthAllDemandsScreen(personId, null, SemanticVersion("2.5.0"))
                assertThat(result).isSuccessWithData(expectedScreen)
            }
            coVerifyOnce { healthScreenService.getFirstPageDemandsByPersonId(any()) }
        }

    @Test
    fun `#getExplorerScreen should get result from ExplorerScreenService`(): Unit =
        runBlocking {
            val expectedScreen = ScreensTransport(
                id = ScreenType.EXPLORER.value,
                layout = ScreenLayout(type = "single_column", body = emptyList())
            )

            coEvery {
                explorerScreenService.getExplorer(appVersion, personId)
            } returns expectedScreen.success()

            val result = service.getOtherAliceScreens(
                AliceScreensData(
                    personId,
                    ScreenType.EXPLORER,
                    appVersion = appVersion
                )
            )
            assertThat(result).isSuccessWithData(expectedScreen)

            coVerifyOnce { explorerScreenService.getExplorer(any(), any()) }
        }

    @Test
    fun `#getRedesignUnifiedHealthScreen should get result from RedesignUnifiedHealthScreenService`(): Unit =
        runBlocking {
            val questionnaireQuestionResponse = QuestionnaireQuestionResponse(
                id = RangeUUID.generate(),
                questionnaireId = RangeUUID.generate(),
                question = "Test Question",
                progress = 0,
                details = null,
                input = QuestionnaireQuestionInputResponse(
                    action = RemoteAction.redesignHealthPlanHome(),
                    displayAttributes = null,
                    options = emptyList(),
                    type = HealthFormQuestionType.FREE_TEXT,
                )
            )
            val expectedScreen = ScreensTransport(
                id = ScreenType.REDESIGN_UNIFIED_HEALTH.value,
                layout = ScreenLayout(type = "single_column", body = emptyList())
            )

            coEvery {
                redesignUnifiedHealthScreenService.getRedesignUnifiedHealth(
                    appVersion,
                    personId,
                    questionnaireQuestionResponse
                )
            } returns expectedScreen.success()

            val result = service.getOtherAliceScreens(
                AliceScreensData(
                    personId,
                    ScreenType.REDESIGN_UNIFIED_HEALTH,
                    question = questionnaireQuestionResponse,
                    appVersion = appVersion
                )
            )
            assertThat(result).isSuccessWithData(expectedScreen)

            coVerifyOnce {
                redesignUnifiedHealthScreenService.getRedesignUnifiedHealth(
                    any(),
                    any(),
                    any(),
                    any(),
                )
            }
        }

    @Test
    fun `#getRedesignHealthPlanHomeScreen should get result from RedesignHealthPlanScreenService`(): Unit =
        runBlocking {
            val expectedScreen = ScreensTransport(
                id = ScreenType.REDESIGN_HEALTH_PLAN_HOME.value,
                layout = ScreenLayout(type = "single_column", body = emptyList())
            )

            coEvery {
                redesignHealthPlanScreenService.getHealthPlanHome(personId, appVersion)
            } returns expectedScreen.success()

            val result = service.getOtherAliceScreens(
                AliceScreensData(
                    personId,
                    ScreenType.REDESIGN_HEALTH_PLAN_HOME,
                    appVersion = appVersion
                )
            )
            assertThat(result).isSuccessWithData(expectedScreen)

            coVerifyOnce { redesignHealthPlanScreenService.getHealthPlanHome(any(), appVersion) }
        }

    @Test
    fun `#getRedesignHealthPlanHomeDemandListScreen should get result from RedesignHealthPlanScreenService`(): Unit =
        runBlocking {
            val expectedScreen = ScreensTransport(
                id = ScreenType.REDESIGN_HEALTH_PLAN_HOME_DEMAND_LIST.value,
                layout = ScreenLayout(type = "single_column", body = emptyList())
            )

            coEvery {
                redesignHealthPlanScreenService.getHealthPlanHomeDemandList(
                    personId = personId,
                    demandStatuses = listOf(DemandActionPlanStatus.DONE),
                    taskStatuses = listOf(ActionPlanTaskStatus.DONE)
                )
            } returns expectedScreen.success()

            val result = service.getOtherAliceScreens(
                AliceScreensData(
                    personId = personId,
                    screenType = ScreenType.REDESIGN_HEALTH_PLAN_HOME_DEMAND_LIST,
                    demandStatuses = listOf(DemandActionPlanStatus.DONE),
                    taskStatuses = listOf(ActionPlanTaskStatus.DONE),
                    appVersion = appVersion
                )
            )
            assertThat(result).isSuccessWithData(expectedScreen)

            coVerifyOnce { redesignHealthPlanScreenService.getHealthPlanHomeDemandList(any(), any(), any()) }
        }

    @Test
    fun `#getRedesignHealthPlanTaskListFiltersScreen should get result from RedesignHealthPlanScreenService`(): Unit =
        runBlocking {
            val expectedScreen = ScreensTransport(
                id = ScreenType.REDESIGN_HEALTH_PLAN_TASK_LIST_FILTERS.value,
                layout = ScreenLayout(type = "single_column", body = emptyList())
            )

            coEvery {
                redesignHealthPlanScreenService.getHealthPlanTaskListFilters(personId)
            } returns expectedScreen.success()

            val result = service.getOtherAliceScreens(
                AliceScreensData(
                    personId,
                    ScreenType.REDESIGN_HEALTH_PLAN_TASK_LIST_FILTERS,
                    appVersion = appVersion
                )
            )
            assertThat(result).isSuccessWithData(expectedScreen)

            coVerifyOnce { redesignHealthPlanScreenService.getHealthPlanTaskListFilters(any()) }
        }

    @Test
    fun `#getRedesignHealthPlanTaskListScreen should get result from RedesignHealthPlanScreenService`(): Unit =
        runBlocking {
            val expectedScreen = ScreensTransport(
                id = ScreenType.REDESIGN_HEALTH_PLAN_TASK_LIST.value,
                layout = ScreenLayout(type = "single_column", body = emptyList())
            )

            coEvery {
                redesignHealthPlanScreenService.getHealthPlanTaskList(
                    personId,
                )
            } returns expectedScreen.success()

            val result = service.getOtherAliceScreens(
                AliceScreensData(
                    personId,
                    ScreenType.REDESIGN_HEALTH_PLAN_TASK_LIST,
                    appVersion = appVersion
                )
            )
            assertThat(result).isSuccessWithData(expectedScreen)

            coVerifyOnce { redesignHealthPlanScreenService.getHealthPlanTaskList(any()) }
        }

    @Test
    fun `#getRedesignHealthPlanDemandDetailScreen should get result from RedesignHealthPlanScreenService`(): Unit =
        runBlocking {
            val demandId = RangeUUID.generate()
            val expectedScreen = ScreensTransport(
                id = ScreenType.REDESIGN_HEALTH_PLAN_DEMAND_DETAIL.value,
                layout = ScreenLayout(type = "single_column", body = emptyList())
            )

            coEvery {
                redesignHealthPlanScreenService.getHealthPlanDemandDetail(demandId)
            } returns expectedScreen.success()

            val result = service.getOtherAliceScreens(
                AliceScreensData(
                    personId = personId,
                    screenType = ScreenType.REDESIGN_HEALTH_PLAN_DEMAND_DETAIL,
                    demandId = demandId,
                    appVersion = appVersion,
                )
            )
            assertThat(result).isSuccessWithData(expectedScreen)

            coVerifyOnce { redesignHealthPlanScreenService.getHealthPlanDemandDetail(any()) }
        }

    @Test
    fun `#getMemberProfile should get result from MemberProfileScreenService`(): Unit =
        runBlocking {
            val expectedScreen = ScreensTransport(
                id = ScreenType.MEMBER_PROFILE.value,
                layout = ScreenLayout(type = "single_column", body = emptyList())
            )

            coEvery {
                memberProfileScreenService.getMemberProfile(personId)
            } returns expectedScreen.success()

            val result = service.getOtherAliceScreens(
                AliceScreensData(
                    personId,
                    ScreenType.MEMBER_PROFILE,
                    appVersion = appVersion
                )
            )
            assertThat(result).isSuccessWithData(expectedScreen)

            coVerifyOnce { memberProfileScreenService.getMemberProfile(any()) }
        }

    @Test
    fun `#getRedesignHealthPlanHistoryScreen should get result from RedesignHealthPlanScreenService`(): Unit =
        runBlocking {
            val expectedScreen = ScreensTransport(
                id = ScreenType.REDESIGN_HEALTH_PLAN_HISTORY.value,
                layout = ScreenLayout(type = "single_column", body = emptyList())
            )

            coEvery {
                redesignHealthPlanScreenService.getHealthPlanHistory(personId)
            } returns expectedScreen.success()

            val result = service.getOtherAliceScreens(
                AliceScreensData(
                    personId,
                    ScreenType.REDESIGN_HEALTH_PLAN_HISTORY,
                    appVersion = appVersion
                )
            )
            assertThat(result).isSuccessWithData(expectedScreen)

            coVerifyOnce { redesignHealthPlanScreenService.getHealthPlanHistory(any()) }
        }

    @Test
    fun `#getRedesignHealthPlanHomeTaskListFiltersScreen should get result from RedesignHealthPlanScreenService`(): Unit =
        runBlocking {
            val demandId = RangeUUID.generate()
            val expectedScreen = ScreensTransport(
                id = ScreenType.REDESIGN_HEALTH_PLAN_TASK_LIST_FILTERS.value,
                layout = ScreenLayout(type = "single_column", body = emptyList())
            )

            coEvery {
                redesignHealthPlanScreenService.getHealthPlanTaskListFilters(
                    personId = personId,
                    demandId = demandId,
                    taskStatuses = listOf(ActionPlanTaskStatus.DONE),
                    demandStatuses = listOf(DemandActionPlanStatus.ACTIVE, DemandActionPlanStatus.DONE)
                )
            } returns expectedScreen.success()

            val result = service.getOtherAliceScreens(
                AliceScreensData(
                    personId,
                    ScreenType.REDESIGN_HEALTH_PLAN_TASK_LIST_FILTERS,
                    demandId = demandId,
                    appVersion = appVersion,
                    taskStatuses = listOf(ActionPlanTaskStatus.DONE),
                    demandStatuses = listOf(DemandActionPlanStatus.ACTIVE, DemandActionPlanStatus.DONE)
                )
            )
            assertThat(result).isSuccessWithData(expectedScreen)

            coVerifyOnce { redesignHealthPlanScreenService.getHealthPlanTaskListFilters(any(), any(), any(), any()) }
        }

    @Test
    fun `#getRedesignHealthPlanHomeTaskListScreen should get result from RedesignHealthPlanScreenService`(): Unit =
        runBlocking {
            val demandId = RangeUUID.generate()
            val expectedScreen = ScreensTransport(
                id = ScreenType.REDESIGN_HEALTH_PLAN_TASK_LIST.value,
                layout = ScreenLayout(type = "single_column", body = emptyList())
            )

            coEvery {
                redesignHealthPlanScreenService.getHealthPlanTaskList(
                    personId = personId,
                    demandId = demandId,
                    filterByTaskType = ActionPlanTaskType.EMERGENCY.name,
                    taskStatuses = listOf(ActionPlanTaskStatus.DONE),
                    demandStatuses = listOf(DemandActionPlanStatus.ACTIVE, DemandActionPlanStatus.DONE)
                )
            } returns expectedScreen.success()

            val result = service.getOtherAliceScreens(
                AliceScreensData(
                    personId,
                    ScreenType.REDESIGN_HEALTH_PLAN_TASK_LIST,
                    demandId = demandId,
                    appVersion = appVersion,
                    filterByTaskType = ActionPlanTaskType.EMERGENCY.name,
                    taskStatuses = listOf(ActionPlanTaskStatus.DONE),
                    demandStatuses = listOf(DemandActionPlanStatus.ACTIVE, DemandActionPlanStatus.DONE)
                )
            )
            assertThat(result).isSuccessWithData(expectedScreen)

            coVerifyOnce { redesignHealthPlanScreenService.getHealthPlanTaskList(any(), any(), any(), any(), any()) }
        }
}


