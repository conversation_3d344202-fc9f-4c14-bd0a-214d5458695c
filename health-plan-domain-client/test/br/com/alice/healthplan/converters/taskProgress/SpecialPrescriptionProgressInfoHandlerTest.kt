package br.com.alice.healthplan.converters.taskProgress

import br.com.alice.common.RangeUUID
import br.com.alice.common.core.extensions.atEndOfTheDay
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.ActionType
import br.com.alice.data.layer.models.Deadline
import br.com.alice.data.layer.models.Dose
import br.com.alice.data.layer.models.Frequency
import br.com.alice.data.layer.models.FrequencyType
import br.com.alice.data.layer.models.HealthPlanTaskStatus
import br.com.alice.data.layer.models.MedicineUnit
import br.com.alice.data.layer.models.MedicineUnit.CAPSULE
import br.com.alice.data.layer.models.MedicineUnit.PILLS
import br.com.alice.data.layer.models.MedicineUnit.TABLET
import br.com.alice.data.layer.models.PeriodUnit
import br.com.alice.data.layer.models.PrescriptionMedicine
import br.com.alice.data.layer.models.PrescriptionMedicineType
import br.com.alice.data.layer.models.RouteOfAdministration
import br.com.alice.data.layer.models.Start
import br.com.alice.data.layer.models.StartType
import br.com.alice.data.layer.models.copy
import br.com.alice.healthplan.models.TaskProgressType
import io.mockk.coEvery
import io.mockk.mockkStatic
import org.assertj.core.api.Assertions.assertThat
import java.time.LocalDate
import java.time.LocalDateTime
import kotlin.random.Random
import kotlin.test.Test

class SpecialPrescriptionProgressInfoHandlerTest {
    @Test
    fun `#canBuild returns true for a continuous prescription of a special medicine`() {
        val task = TestModelFactory.buildHealthPlanTaskPrescription().copy(
            initiatedByMemberAt = LocalDateTime.now(),
            deadline = Deadline(unit = PeriodUnit.CONTINUOUS, quantity = 0),
            content = mapOf(
                "dose" to Dose(CAPSULE, 1f),
                "action" to ActionType.TAKE,
                "routeOfAdministration" to RouteOfAdministration.ORAL,
                "medicine" to PrescriptionMedicine(
                    name = "Novalgina",
                    unit = CAPSULE,
                    quantity = "15",
                    concentration = "mg",
                    concentrationQuantity = "500",
                    drug = "Dipirona",
                    type = PrescriptionMedicineType.SPECIAL,
                    id = RangeUUID.generate(),
                    drugId = Random.nextInt(1000, 10000)
                ),
                "packing" to 1
            ),
        )

        val result = SpecialPrescriptionProgressInfoHandler().canBuild(task)

        assertThat(result).isTrue
    }

    @Test
    fun `#canBuild returns false for a continuous prescription of a simple medicine`() {
        val task = TestModelFactory.buildHealthPlanTaskPrescription().copy(
            initiatedByMemberAt = LocalDateTime.now(),
            deadline = Deadline(unit = PeriodUnit.CONTINUOUS, quantity = 0),
            content = mapOf(
                "dose" to Dose(CAPSULE, 1f),
                "action" to ActionType.TAKE,
                "routeOfAdministration" to RouteOfAdministration.ORAL,
                "medicine" to PrescriptionMedicine(
                    name = "Novalgina",
                    unit = CAPSULE,
                    quantity = "15",
                    concentration = "mg",
                    concentrationQuantity = "500",
                    drug = "Dipirona",
                    type = PrescriptionMedicineType.SIMPLE,
                    id = RangeUUID.generate(),
                    drugId = Random.nextInt(1000, 10000)
                ),
                "packing" to 1
            ),
        )

        val result = SpecialPrescriptionProgressInfoHandler().canBuild(task)

        assertThat(result).isFalse
    }

    @Test
    fun `#canBuild returns false for a non continuous prescription of a special medicine`() {
        val task = TestModelFactory.buildHealthPlanTaskPrescription().copy(
            initiatedByMemberAt = LocalDateTime.now(),
            deadline = Deadline(unit = PeriodUnit.DAY, quantity = 10),
            content = mapOf(
                "dose" to Dose(CAPSULE, 1f),
                "action" to ActionType.TAKE,
                "routeOfAdministration" to RouteOfAdministration.ORAL,
                "medicine" to PrescriptionMedicine(
                    name = "Novalgina",
                    unit = CAPSULE,
                    quantity = "15",
                    concentration = "mg",
                    concentrationQuantity = "500",
                    drug = "Dipirona",
                    type = PrescriptionMedicineType.SPECIAL,
                    id = RangeUUID.generate(),
                    drugId = Random.nextInt(1000, 10000)
                ),
                "packing" to 1
            ),
        )

        val result = SpecialPrescriptionProgressInfoHandler().canBuild(task)

        assertThat(result).isFalse
    }

    @Test
    fun `#canBuild returns false for all medicine units but CAPSULE, PILLS and TABLET`() {
        MedicineUnit.values()
            .filter { !listOf(CAPSULE, PILLS, TABLET).contains(it) }
            .forEach {
                val task = TestModelFactory.buildHealthPlanTaskPrescription().copy(
                    initiatedByMemberAt = LocalDateTime.now(),
                    deadline = Deadline(unit = PeriodUnit.CONTINUOUS, quantity = 0),
                    content = mapOf(
                        "dose" to Dose(CAPSULE, 1f),
                        "action" to ActionType.TAKE,
                        "routeOfAdministration" to RouteOfAdministration.ORAL,
                        "medicine" to PrescriptionMedicine(
                            name = "Novalgina",
                            unit = it,
                            quantity = "15",
                            concentration = "mg",
                            concentrationQuantity = "500",
                            drug = "Dipirona",
                            type = PrescriptionMedicineType.SPECIAL,
                            id = RangeUUID.generate(),
                            drugId = Random.nextInt(1000, 10000)
                        ),
                        "packing" to 1
                    ),
                )

                val result = SpecialPrescriptionProgressInfoHandler().canBuild(task)

                assertThat(result).isFalse
            }
    }

    @Test
    fun `#canBuild returns false for all FrequencyType but EVERY and QUANTITY_IN_PERIOD`() {
        FrequencyType.values()
            .filter {
                !listOf(
                    FrequencyType.EVERY,
                    FrequencyType.QUANTITY_IN_PERIOD,
                    FrequencyType.TIMES,
                ).contains(it)
            }
            .forEach {
                val task = TestModelFactory.buildHealthPlanTaskPrescription().copy(
                    initiatedByMemberAt = LocalDateTime.now(),
                    deadline = Deadline(unit = PeriodUnit.CONTINUOUS, quantity = 0),
                    frequency = Frequency(
                        type = it,
                        unit = PeriodUnit.HOUR,
                        quantity = 6
                    ),
                    content = mapOf(
                        "dose" to Dose(CAPSULE, 1f),
                        "action" to ActionType.TAKE,
                        "routeOfAdministration" to RouteOfAdministration.ORAL,
                        "medicine" to PrescriptionMedicine(
                            name = "Novalgina",
                            unit = PILLS,
                            quantity = "15",
                            concentration = "mg",
                            concentrationQuantity = "500",
                            drug = "Dipirona",
                            type = PrescriptionMedicineType.SPECIAL,
                            id = RangeUUID.generate(),
                            drugId = Random.nextInt(1000, 10000)
                        ),
                        "packing" to 1
                    ),
                )

                val result = SpecialPrescriptionProgressInfoHandler().canBuild(task)

                assertThat(result).isFalse
            }
    }

    @Test
    fun `#build returns the correct progressType`() {
        val task = TestModelFactory.buildHealthPlanTaskPrescription().copy(
            initiatedByMemberAt = LocalDateTime.now(),
            deadline = Deadline(unit = PeriodUnit.CONTINUOUS, quantity = 0),
            content = mapOf(
                "dose" to Dose(CAPSULE, 1f),
                "action" to ActionType.TAKE,
                "routeOfAdministration" to RouteOfAdministration.ORAL,
                "medicine" to PrescriptionMedicine(
                    name = "Rivotril",
                    unit = CAPSULE,
                    quantity = "15",
                    concentration = "mg",
                    concentrationQuantity = "500",
                    drug = "Clonazepam",
                    type = PrescriptionMedicineType.SPECIAL,
                    id = RangeUUID.generate(),
                    drugId = Random.nextInt(1000, 10000)
                ),
                "packing" to 1
            ),
        )

        val result = SpecialPrescriptionProgressInfoHandler().build(task)

        assertThat(result.progressType)
            .isEqualTo(TaskProgressType.SPECIAL_PRESCRIPTION_CONTINUOUS)
    }

    @Test
    fun `#build returns endAt as the end of the day when the medicine ends`() {
        mockkStatic(LocalDateTime::class) {
            val yesterday = LocalDateTime.of(2021, 6, 6, 14, 20)
            val now = LocalDateTime.of(2021, 6, 7, 16, 0)

            coEvery { LocalDateTime.now() } returns now

            val task = TestModelFactory.buildHealthPlanTaskPrescription().copy(
                initiatedByMemberAt = yesterday,
                deadline = Deadline(quantity = 0, unit = PeriodUnit.CONTINUOUS),
                frequency = Frequency(
                    type = FrequencyType.QUANTITY_IN_PERIOD,
                    unit = PeriodUnit.HOUR,
                    quantity = 6
                ),
                content = mapOf(
                    "dose" to Dose(PILLS, 2f),
                    "action" to ActionType.TAKE,
                    "routeOfAdministration" to RouteOfAdministration.ORAL,
                    "medicine" to PrescriptionMedicine(
                        name = "NOVALGINA",
                        drug = "DIPIRONA",
                        type = PrescriptionMedicineType.SPECIAL,
                        unit = PILLS,
                        quantity = "10.0"
                    ),
                    "packing" to 2
                )
            )

            val result = SpecialPrescriptionProgressInfoHandler().build(task)

            assertThat(result.endAt).isEqualTo(
                LocalDate.of(2021, 6, 8).atEndOfTheDay()
            )
        }
    }

    @Test
    fun `#build returns the progress based on the remaining medicine`() {
        mockkStatic(LocalDateTime::class) {
            val yesterday = LocalDateTime.of(2021, 6, 6, 14, 20)
            val now = LocalDateTime.of(2021, 6, 7, 16, 0)

            coEvery { LocalDateTime.now() } returns now

            val task = TestModelFactory.buildHealthPlanTaskPrescription().copy(
                status = HealthPlanTaskStatus.ACTIVE,
                initiatedByMemberAt = yesterday,
                deadline = Deadline(quantity = 0, unit = PeriodUnit.CONTINUOUS),
                start = Start(type = StartType.IMMEDIATE),
                frequency = Frequency(
                    type = FrequencyType.QUANTITY_IN_PERIOD,
                    unit = PeriodUnit.HOUR,
                    quantity = 6
                ),
                content = mapOf(
                    "dose" to Dose(PILLS, 2f),
                    "action" to ActionType.TAKE,
                    "routeOfAdministration" to RouteOfAdministration.ORAL,
                    "medicine" to PrescriptionMedicine(
                        name = "NOVALGINA",
                        drug = "DIPIRONA",
                        type = PrescriptionMedicineType.SPECIAL,
                        unit = PILLS,
                        quantity = "10.0"
                    ),
                    "packing" to 2
                )
            )

            val result = SpecialPrescriptionProgressInfoHandler().build(task)

            assertThat(result.progress).isEqualTo(33)
        }
    }

    @Test
    fun `#build returns progress as 0 for a task initiated on the same day`() {
        mockkStatic(LocalDateTime::class) {
            val twoHoursAgo = LocalDateTime.of(2021, 6, 7, 14, 20)
            val now = LocalDateTime.of(2021, 6, 7, 16, 43)

            coEvery { LocalDateTime.now() } returns now

            val task = TestModelFactory.buildHealthPlanTaskPrescription().copy(
                initiatedByMemberAt = twoHoursAgo,
                deadline = Deadline(unit = PeriodUnit.CONTINUOUS, quantity = 0),
                frequency = Frequency(
                    type = FrequencyType.QUANTITY_IN_PERIOD,
                    unit = PeriodUnit.HOUR,
                    quantity = 6
                ),
                content = mapOf(
                    "dose" to Dose(PILLS, 2f),
                    "action" to ActionType.TAKE,
                    "routeOfAdministration" to RouteOfAdministration.ORAL,
                    "medicine" to PrescriptionMedicine(
                        name = "NOVALGINA",
                        drug = "DIPIRONA",
                        type = PrescriptionMedicineType.SPECIAL,
                        unit = PILLS,
                        quantity = "10.0"
                    ),
                    "packing" to 2
                )
            )

            val result = SpecialPrescriptionProgressInfoHandler().build(task)

            assertThat(result.progress).isEqualTo(0)
        }
    }

    @Test
    fun `#build returns progress on the day the medicine ends`() {
        mockkStatic(LocalDateTime::class) {
            val fourDaysAgo = LocalDateTime.of(2021, 6, 2, 14, 20)
            val now = LocalDateTime.of(2021, 6, 6, 8, 43)

            coEvery { LocalDateTime.now() } returns now

            val task = TestModelFactory.buildHealthPlanTaskPrescription().copy(
                initiatedByMemberAt = fourDaysAgo,
                deadline = Deadline(unit = PeriodUnit.CONTINUOUS, quantity = 0),
                frequency = Frequency(
                    type = FrequencyType.EVERY,
                    unit = PeriodUnit.DAY,
                    quantity = 5
                ),
                content = mapOf(
                    "dose" to Dose(PILLS, 1f),
                    "action" to ActionType.TAKE,
                    "routeOfAdministration" to RouteOfAdministration.ORAL,
                    "medicine" to PrescriptionMedicine(
                        name = "NOVALGINA",
                        drug = "DIPIRONA",
                        type = PrescriptionMedicineType.SPECIAL,
                        unit = PILLS,
                        quantity = "5.0"
                    ),
                    "packing" to 1
                )
            )

            val result = SpecialPrescriptionProgressInfoHandler().build(task)

            assertThat(result.progress).isEqualTo(80)
        }
    }

    @Test
    fun `#build returns progress as 100 after the day the medicine ended`() {
        mockkStatic(LocalDateTime::class) {
            val fourDaysAgo = LocalDateTime.of(2021, 6, 2, 14, 20)
            val now = LocalDateTime.of(2021, 6, 10, 8, 43)

            coEvery { LocalDateTime.now() } returns now

            val task = TestModelFactory.buildHealthPlanTaskPrescription().copy(
                initiatedByMemberAt = fourDaysAgo,
                deadline = Deadline(unit = PeriodUnit.CONTINUOUS, quantity = 0),
                frequency = Frequency(
                    type = FrequencyType.EVERY,
                    unit = PeriodUnit.DAY,
                    quantity = 5
                ),
                content = mapOf(
                    "dose" to Dose(PILLS, 1f),
                    "action" to ActionType.TAKE,
                    "routeOfAdministration" to RouteOfAdministration.ORAL,
                    "medicine" to PrescriptionMedicine(
                        name = "NOVALGINA",
                        drug = "DIPIRONA",
                        type = PrescriptionMedicineType.SPECIAL,
                        unit = PILLS,
                        quantity = "5.0"
                    ),
                    "packing" to 1
                )
            )

            val result = SpecialPrescriptionProgressInfoHandler().build(task)

            assertThat(result.progress).isEqualTo(100)
        }
    }

    @Test
    fun `#build returns new_prescription as false for a deadline greater than 10 days from now`() {
        mockkStatic(LocalDateTime::class) {
            val threeDaysAgo = LocalDateTime.of(2021, 6, 15, 14, 0)
            val now = LocalDateTime.of(2021, 6, 17, 9, 0)

            coEvery { LocalDateTime.now() } returns now

            val task = TestModelFactory.buildHealthPlanTaskPrescription().copy(
                initiatedByMemberAt = threeDaysAgo,
                deadline = Deadline(unit = PeriodUnit.CONTINUOUS, quantity = 0),
                frequency = Frequency(
                    type = FrequencyType.EVERY,
                    unit = PeriodUnit.DAY,
                    quantity = 0
                ),
                content = mapOf(
                    "dose" to Dose(PILLS, 1f),
                    "action" to ActionType.TAKE,
                    "routeOfAdministration" to RouteOfAdministration.ORAL,
                    "medicine" to PrescriptionMedicine(
                        name = "NOVALGINA",
                        drug = "DIPIRONA",
                        type = PrescriptionMedicineType.SPECIAL,
                        unit = PILLS,
                        quantity = "5.0"
                    ),
                    "packing" to 3
                )
            )

            val result = SpecialPrescriptionProgressInfoHandler().build(task)

            assertThat(result.endAt).isEqualTo(LocalDate.of(2021, 6, 29).atEndOfTheDay())
            assertThat(result.newPrescription).isFalse()
        }
    }

    @Test
    fun `#build returns new_prescription as true for a deadline equals 10 days from now`() {
        mockkStatic(LocalDateTime::class) {
            val fiveDaysAgo = LocalDateTime.of(2021, 6, 15, 14, 0)
            val now = LocalDateTime.of(2021, 6, 20, 9, 0)

            coEvery { LocalDateTime.now() } returns now

            val task = TestModelFactory.buildHealthPlanTaskPrescription().copy(
                initiatedByMemberAt = fiveDaysAgo,
                deadline = Deadline(unit = PeriodUnit.CONTINUOUS, quantity = 0),
                frequency = Frequency(
                    type = FrequencyType.EVERY,
                    unit = PeriodUnit.DAY,
                    quantity = 0
                ),
                content = mapOf(
                    "dose" to Dose(PILLS, 1f),
                    "action" to ActionType.TAKE,
                    "routeOfAdministration" to RouteOfAdministration.ORAL,
                    "medicine" to PrescriptionMedicine(
                        name = "NOVALGINA",
                        drug = "DIPIRONA",
                        type = PrescriptionMedicineType.SPECIAL,
                        unit = PILLS,
                        quantity = "5.0"
                    ),
                    "packing" to 3
                )
            )

            val result = SpecialPrescriptionProgressInfoHandler().build(task)

            assertThat(result.endAt).isEqualTo(LocalDate.of(2021, 6, 29).atEndOfTheDay())
            assertThat(result.newPrescription).isTrue()
        }
    }

    @Test
    fun `#build returns new_prescription as true for a deadline less than 10 days from now`() {
        mockkStatic(LocalDateTime::class) {
            val nineDaysAgo = LocalDateTime.of(2021, 6, 15, 14, 0)
            val now = LocalDateTime.of(2021, 6, 24, 9, 0)

            coEvery { LocalDateTime.now() } returns now

            val task = TestModelFactory.buildHealthPlanTaskPrescription().copy(
                initiatedByMemberAt = nineDaysAgo,
                deadline = Deadline(unit = PeriodUnit.CONTINUOUS, quantity = 0),
                frequency = Frequency(
                    type = FrequencyType.EVERY,
                    unit = PeriodUnit.DAY,
                    quantity = 0
                ),
                content = mapOf(
                    "dose" to Dose(PILLS, 1f),
                    "action" to ActionType.TAKE,
                    "routeOfAdministration" to RouteOfAdministration.ORAL,
                    "medicine" to PrescriptionMedicine(
                        name = "NOVALGINA",
                        drug = "DIPIRONA",
                        type = PrescriptionMedicineType.SPECIAL,
                        unit = PILLS,
                        quantity = "5.0"
                    ),
                    "packing" to 3
                )
            )

            val result = SpecialPrescriptionProgressInfoHandler().build(task)

            assertThat(result.endAt).isEqualTo(LocalDate.of(2021, 6, 29).atEndOfTheDay())
            assertThat(result.newPrescription).isTrue()
        }
    }

    @Test
    fun `#build returns new_prescription as true for a deadline ending on the current day`() {
        mockkStatic(LocalDateTime::class) {
            val fifteenDaysAgo = LocalDateTime.of(2021, 6, 15, 14, 0)
            val now = LocalDateTime.of(2021, 6, 29, 9, 0)

            coEvery { LocalDateTime.now() } returns now

            val task = TestModelFactory.buildHealthPlanTaskPrescription().copy(
                initiatedByMemberAt = fifteenDaysAgo,
                deadline = Deadline(unit = PeriodUnit.CONTINUOUS, quantity = 0),
                frequency = Frequency(
                    type = FrequencyType.EVERY,
                    unit = PeriodUnit.DAY,
                    quantity = 0
                ),
                content = mapOf(
                    "dose" to Dose(PILLS, 1f),
                    "action" to ActionType.TAKE,
                    "routeOfAdministration" to RouteOfAdministration.ORAL,
                    "medicine" to PrescriptionMedicine(
                        name = "NOVALGINA",
                        drug = "DIPIRONA",
                        type = PrescriptionMedicineType.SPECIAL,
                        unit = PILLS,
                        quantity = "5.0"
                    ),
                    "packing" to 3
                )
            )

            val result = SpecialPrescriptionProgressInfoHandler().build(task)

            assertThat(result.endAt).isEqualTo(LocalDate.of(2021, 6, 29).atEndOfTheDay())
            assertThat(result.newPrescription).isTrue()
        }
    }

    @Test
    fun `#build returns alert_message as false for a deadline greater than 10 days from now`() {
        mockkStatic(LocalDateTime::class) {
            val threeDaysAgo = LocalDateTime.of(2021, 6, 15, 14, 0)
            val now = LocalDateTime.of(2021, 6, 17, 9, 0)

            coEvery { LocalDateTime.now() } returns now

            val task = TestModelFactory.buildHealthPlanTaskPrescription().copy(
                initiatedByMemberAt = threeDaysAgo,
                deadline = Deadline(unit = PeriodUnit.CONTINUOUS, quantity = 0),
                frequency = Frequency(
                    type = FrequencyType.EVERY,
                    unit = PeriodUnit.DAY,
                    quantity = 0
                ),
                content = mapOf(
                    "dose" to Dose(PILLS, 1f),
                    "action" to ActionType.TAKE,
                    "routeOfAdministration" to RouteOfAdministration.ORAL,
                    "medicine" to PrescriptionMedicine(
                        name = "NOVALGINA",
                        drug = "DIPIRONA",
                        type = PrescriptionMedicineType.SPECIAL,
                        unit = PILLS,
                        quantity = "5.0"
                    ),
                    "packing" to 3
                )
            )

            val result = SpecialPrescriptionProgressInfoHandler().build(task)

            assertThat(result.endAt).isEqualTo(
                LocalDate.of(2021, 6, 29).atEndOfTheDay()
            )
            assertThat(result.alertMessage).isFalse()
        }
    }

    @Test
    fun `#build returns alert_message as true for a deadline equals 10 days from now`() {
        mockkStatic(LocalDateTime::class) {
            val fiveDaysAgo = LocalDateTime.of(2021, 6, 15, 14, 0)
            val now = LocalDateTime.of(2021, 6, 20, 9, 0)

            coEvery { LocalDateTime.now() } returns now

            val task = TestModelFactory.buildHealthPlanTaskPrescription().copy(
                initiatedByMemberAt = fiveDaysAgo,
                deadline = Deadline(unit = PeriodUnit.CONTINUOUS, quantity = 0),
                frequency = Frequency(
                    type = FrequencyType.EVERY,
                    unit = PeriodUnit.DAY,
                    quantity = 0
                ),
                content = mapOf(
                    "dose" to Dose(PILLS, 1f),
                    "action" to ActionType.TAKE,
                    "routeOfAdministration" to RouteOfAdministration.ORAL,
                    "medicine" to PrescriptionMedicine(
                        name = "NOVALGINA",
                        drug = "DIPIRONA",
                        type = PrescriptionMedicineType.SPECIAL,
                        unit = PILLS,
                        quantity = "5.0"
                    ),
                    "packing" to 3
                )
            )

            val result = SpecialPrescriptionProgressInfoHandler().build(task)

            assertThat(result.endAt).isEqualTo(
                LocalDate.of(2021, 6, 29).atEndOfTheDay()
            )
            assertThat(result.alertMessage).isTrue()
        }
    }

    @Test
    fun `#build returns alert_message as true for a deadline less than 10 days from now`() {
        mockkStatic(LocalDateTime::class) {
            val nineDaysAgo = LocalDateTime.of(2021, 6, 15, 14, 0)
            val now = LocalDateTime.of(2021, 6, 24, 9, 0)

            coEvery { LocalDateTime.now() } returns now

            val task = TestModelFactory.buildHealthPlanTaskPrescription().copy(
                initiatedByMemberAt = nineDaysAgo,
                deadline = Deadline(unit = PeriodUnit.CONTINUOUS, quantity = 0),
                frequency = Frequency(
                    type = FrequencyType.EVERY,
                    unit = PeriodUnit.DAY,
                    quantity = 0
                ),
                content = mapOf(
                    "dose" to Dose(PILLS, 1f),
                    "action" to ActionType.TAKE,
                    "routeOfAdministration" to RouteOfAdministration.ORAL,
                    "medicine" to PrescriptionMedicine(
                        name = "NOVALGINA",
                        drug = "DIPIRONA",
                        type = PrescriptionMedicineType.SPECIAL,
                        unit = PILLS,
                        quantity = "5.0"
                    ),
                    "packing" to 3
                )
            )

            val result = SpecialPrescriptionProgressInfoHandler().build(task)

            assertThat(result.endAt).isEqualTo(
                LocalDate.of(2021, 6, 29).atEndOfTheDay()
            )
            assertThat(result.alertMessage).isTrue()
        }
    }

    @Test
    fun `#build returns alert_message as true for a deadline ending on the current day`() {
        mockkStatic(LocalDateTime::class) {
            val fifteenDaysAgo = LocalDateTime.of(2021, 6, 15, 14, 0)
            val now = LocalDateTime.of(2021, 6, 29, 9, 0)

            coEvery { LocalDateTime.now() } returns now

            val task = TestModelFactory.buildHealthPlanTaskPrescription().copy(
                initiatedByMemberAt = fifteenDaysAgo,
                deadline = Deadline(unit = PeriodUnit.CONTINUOUS, quantity = 0),
                frequency = Frequency(
                    type = FrequencyType.EVERY,
                    unit = PeriodUnit.DAY,
                    quantity = 0
                ),
                content = mapOf(
                    "dose" to Dose(PILLS, 1f),
                    "action" to ActionType.TAKE,
                    "routeOfAdministration" to RouteOfAdministration.ORAL,
                    "medicine" to PrescriptionMedicine(
                        name = "NOVALGINA",
                        drug = "DIPIRONA",
                        type = PrescriptionMedicineType.SPECIAL,
                        unit = PILLS,
                        quantity = "5.0"
                    ),
                    "packing" to 3
                )
            )

            val result = SpecialPrescriptionProgressInfoHandler().build(task)

            assertThat(result.endAt).isEqualTo(
                LocalDate.of(2021, 6, 29).atEndOfTheDay()
            )
            assertThat(result.alertMessage).isTrue()
        }
    }

    @Test
    fun `#build returns compact sentence as message for a deadline greater than 10 days from now`() {
        mockkStatic(LocalDateTime::class) {
            val threeDaysAgo = LocalDateTime.of(2021, 6, 15, 14, 0)
            val now = LocalDateTime.of(2021, 6, 17, 9, 0)

            coEvery { LocalDateTime.now() } returns now

            val task = TestModelFactory.buildHealthPlanTaskPrescription().copy(
                initiatedByMemberAt = threeDaysAgo,
                deadline = Deadline(unit = PeriodUnit.CONTINUOUS, quantity = 0),
                frequency = Frequency(
                    type = FrequencyType.EVERY,
                    unit = PeriodUnit.DAY,
                    quantity = 0
                ),
                content = mapOf(
                    "dose" to Dose(PILLS, 1f),
                    "action" to ActionType.TAKE,
                    "routeOfAdministration" to RouteOfAdministration.ORAL,
                    "medicine" to PrescriptionMedicine(
                        name = "NOVALGINA",
                        drug = "DIPIRONA",
                        type = PrescriptionMedicineType.SPECIAL,
                        unit = PILLS,
                        quantity = "5.0"
                    ),
                    "packing" to 3
                )
            )

            val result = SpecialPrescriptionProgressInfoHandler().build(task)

            assertThat(result.endAt).isEqualTo(
                LocalDate.of(2021, 6, 29).atEndOfTheDay()
            )
            assertThat(result.message).isEqualTo("Tomar 1 comprimido todos dias")
        }
    }

    @Test
    fun `#build returns medicine count as message for a deadline equals 10 days from now`() {
        mockkStatic(LocalDateTime::class) {
            val fiveDaysAgo = LocalDateTime.of(2021, 6, 15, 14, 0)
            val now = LocalDateTime.of(2021, 6, 20, 9, 0)

            coEvery { LocalDateTime.now() } returns now

            val task = TestModelFactory.buildHealthPlanTaskPrescription().copy(
                initiatedByMemberAt = fiveDaysAgo,
                deadline = Deadline(unit = PeriodUnit.CONTINUOUS, quantity = 0),
                frequency = Frequency(
                    type = FrequencyType.EVERY,
                    unit = PeriodUnit.DAY,
                    quantity = 0
                ),
                content = mapOf(
                    "dose" to Dose(PILLS, 1f),
                    "action" to ActionType.TAKE,
                    "routeOfAdministration" to RouteOfAdministration.ORAL,
                    "medicine" to PrescriptionMedicine(
                        name = "NOVALGINA",
                        drug = "DIPIRONA",
                        type = PrescriptionMedicineType.SPECIAL,
                        unit = TABLET,
                        quantity = "5.0"
                    ),
                    "packing" to 3
                )
            )

            val result = SpecialPrescriptionProgressInfoHandler().build(task)

            assertThat(result.endAt).isEqualTo(
                LocalDate.of(2021, 6, 29).atEndOfTheDay()
            )
            assertThat(result.message).isEqualTo("10 pastilhas restantes")
        }
    }

    @Test
    fun `#build returns medicine count as message for a deadline less than 10 days from now`() {
        mockkStatic(LocalDateTime::class) {
            val nineDaysAgo = LocalDateTime.of(2021, 6, 15, 14, 0)
            val now = LocalDateTime.of(2021, 6, 24, 9, 0)

            coEvery { LocalDateTime.now() } returns now

            val task = TestModelFactory.buildHealthPlanTaskPrescription().copy(
                initiatedByMemberAt = nineDaysAgo,
                deadline = Deadline(unit = PeriodUnit.CONTINUOUS, quantity = 0),
                frequency = Frequency(
                    type = FrequencyType.EVERY,
                    unit = PeriodUnit.DAY,
                    quantity = 0
                ),
                content = mapOf(
                    "dose" to Dose(PILLS, 1f),
                    "action" to ActionType.TAKE,
                    "routeOfAdministration" to RouteOfAdministration.ORAL,
                    "medicine" to PrescriptionMedicine(
                        name = "NOVALGINA",
                        drug = "DIPIRONA",
                        type = PrescriptionMedicineType.SPECIAL,
                        unit = CAPSULE,
                        quantity = "5.0"
                    ),
                    "packing" to 3
                )
            )

            val result = SpecialPrescriptionProgressInfoHandler().build(task)

            assertThat(result.endAt).isEqualTo(
                LocalDate.of(2021, 6, 29).atEndOfTheDay()
            )
            assertThat(result.message).isEqualTo("6 cápsulas restantes")
        }
    }

    @Test
    fun `#build returns medicine count as message for a deadline ending on the current day`() {
        mockkStatic(LocalDateTime::class) {
            val fifteenDaysAgo = LocalDateTime.of(2021, 6, 15, 14, 0)
            val now = LocalDateTime.of(2021, 6, 29, 9, 0)

            coEvery { LocalDateTime.now() } returns now

            val task = TestModelFactory.buildHealthPlanTaskPrescription().copy(
                initiatedByMemberAt = fifteenDaysAgo,
                deadline = Deadline(unit = PeriodUnit.CONTINUOUS, quantity = 0),
                frequency = Frequency(
                    type = FrequencyType.EVERY,
                    unit = PeriodUnit.DAY,
                    quantity = 0
                ),
                content = mapOf(
                    "dose" to Dose(PILLS, 1f),
                    "action" to ActionType.TAKE,
                    "routeOfAdministration" to RouteOfAdministration.ORAL,
                    "medicine" to PrescriptionMedicine(
                        name = "NOVALGINA",
                        drug = "DIPIRONA",
                        type = PrescriptionMedicineType.SPECIAL,
                        unit = PILLS,
                        quantity = "5.0"
                    ),
                    "packing" to 3
                )
            )

            val result = SpecialPrescriptionProgressInfoHandler().build(task)

            assertThat(result.endAt).isEqualTo(
                LocalDate.of(2021, 6, 29).atEndOfTheDay()
            )
            assertThat(result.message).isEqualTo("1 comprimido restante")
        }
    }

    @Test
    fun `#build returns medicine count as message for a past deadline`() {
        mockkStatic(LocalDateTime::class) {
            val fifteenDaysAgo = LocalDateTime.of(2021, 6, 15, 14, 0)
            val now = LocalDateTime.of(2021, 7, 2, 9, 0)

            coEvery { LocalDateTime.now() } returns now

            val task = TestModelFactory.buildHealthPlanTaskPrescription().copy(
                initiatedByMemberAt = fifteenDaysAgo,
                deadline = Deadline(unit = PeriodUnit.CONTINUOUS, quantity = 0),
                frequency = Frequency(
                    type = FrequencyType.EVERY,
                    unit = PeriodUnit.DAY,
                    quantity = 0
                ),
                content = mapOf(
                    "dose" to Dose(PILLS, 1f),
                    "action" to ActionType.TAKE,
                    "routeOfAdministration" to RouteOfAdministration.ORAL,
                    "medicine" to PrescriptionMedicine(
                        name = "NOVALGINA",
                        drug = "DIPIRONA",
                        type = PrescriptionMedicineType.SPECIAL,
                        unit = PILLS,
                        quantity = "5.0"
                    ),
                    "packing" to 3
                )
            )

            val result = SpecialPrescriptionProgressInfoHandler().build(task)

            assertThat(result.endAt).isEqualTo(
                LocalDate.of(2021, 6, 29).atEndOfTheDay()
            )
            assertThat(result.message).isEqualTo("0 comprimido restante")
        }
    }
}
