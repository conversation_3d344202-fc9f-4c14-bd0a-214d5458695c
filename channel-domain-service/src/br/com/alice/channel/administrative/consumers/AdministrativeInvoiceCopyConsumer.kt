package br.com.alice.channel.administrative.consumers

import br.com.alice.channel.administrative.services.internal.AdministrativeInvoiceCopyService
import br.com.alice.channel.administrative.util.Tags.INVOICE_SECOND_COPY_TRIGGER_TAGS
import br.com.alice.channel.core.consumers.Consumer
import br.com.alice.channel.core.context.FirestoreContextUsage
import br.com.alice.channel.core.services.internal.ChannelService
import br.com.alice.channel.core.setAdministrativeDetails
import br.com.alice.channel.core.util.getSecondCopyInitialMessage
import br.com.alice.channel.core.util.getSecondCopyRoutingMessage
import br.com.alice.channel.models.ChannelDocument
import br.com.alice.channel.notifier.ChannelUpsertedEvent
import br.com.alice.channel.notifier.ChannelUpsertedPayload
import br.com.alice.common.core.extensions.containsAny
import br.com.alice.common.extensions.catchResult
import br.com.alice.common.extensions.then
import br.com.alice.common.observability.recordResult
import br.com.alice.data.layer.models.AdministrativeDetailMemberType
import br.com.alice.data.layer.models.AdministrativeDetails
import br.com.alice.data.layer.models.ChannelCategory.ADMINISTRATIVE
import br.com.alice.data.layer.models.ChannelChangeAction.CREATE_CHAT
import br.com.alice.data.layer.models.ChannelSubCategoryClassifier.AUTOMATION
import br.com.alice.data.layer.models.FeatureNamespace
import br.com.alice.featureconfig.core.FeatureService
import com.github.kittinunf.result.flatMap
import com.github.kittinunf.result.flatMapError
import com.github.kittinunf.result.map
import com.github.kittinunf.result.success

class AdministrativeInvoiceCopyConsumer(
    private val channelService: ChannelService,
    private val administrativeInvoiceCopyService: AdministrativeInvoiceCopyService
) : Consumer() {

    @OptIn(FirestoreContextUsage::class)
    suspend fun process(event: ChannelUpsertedEvent) = span("process") { span ->
        withSubscribersEnvironment {
            span.setChannel(event)

            val payload = event.payload
            val channelId = payload.channelId
            val personId = payload.personId

            if (!payload.isValid()) return@withSubscribersEnvironment false.success()

            administrativeInvoiceCopyService.getAndSaveAdministrativeDetails(channelId, personId)
                .flatMap { administrativeDetails ->
                    span.setAdministrativeDetails(administrativeDetails)

                    if (administrativeDetails.isMemberB2C()) administrativeInvoiceCopyService.dropAndRoute(channelId)
                    else processChannel(channelId, administrativeDetails)
                }.flatMapError { handleProcessError(channelId) }
        }.recordResult(span)
    }

    @FirestoreContextUsage
    private suspend fun processChannel(channelId: String, administrativeDetails: AdministrativeDetails) =
        channelService.getChannel(channelId).flatMap { channel ->
            if (administrativeDetails.isB2BUpToFiveLivesAndFinancialRepresentative()) sendInvoices(channel)
            else drop(channel)
        }

    @FirestoreContextUsage
    private suspend fun sendInvoices(channel: ChannelDocument) = catchResult {
        administrativeInvoiceCopyService.sendMessageToChannel(channel, getSecondCopyInitialMessage())
        administrativeInvoiceCopyService.sendInvoicesToChannel(channel)
        administrativeInvoiceCopyService.hideOrShowMemberInput(channel.id(), false)
    }

    @FirestoreContextUsage
    private suspend fun drop(channel: ChannelDocument) =
        administrativeInvoiceCopyService.dropAndRoute(channel.id())
            .then { administrativeInvoiceCopyService.sendMessageToChannel(channel, getDefaultMessage()) }

    @FirestoreContextUsage
    private suspend fun handleProcessError(channelId: String) =
        channelService.getChannel(channelId)
            .flatMap { administrativeInvoiceCopyService.dropAndRoute(it, getSecondCopyRoutingMessage()) }
            .map { true }

    private fun getDefaultMessage() = FeatureService.get(
        FeatureNamespace.CHANNELS,
        "chat_adm_get_more_context_default_message",
        ""
    )

    private fun ChannelUpsertedPayload.isValid() =
        action == CREATE_CHAT &&
                category == ADMINISTRATIVE &&
                subCategoryClassifier == AUTOMATION &&
                tags.containsAny(INVOICE_SECOND_COPY_TRIGGER_TAGS)

    private fun AdministrativeDetails.isB2BUpToFiveLivesAndFinancialRepresentative() =
        this.memberType == AdministrativeDetailMemberType.B2B
                && this.companyLifesAmount?.let { it <= 5 } ?: false
                && this.isFinancialRepresentative ?: false

    private fun AdministrativeDetails.isMemberB2C() = this.memberType == AdministrativeDetailMemberType.B2C
}
