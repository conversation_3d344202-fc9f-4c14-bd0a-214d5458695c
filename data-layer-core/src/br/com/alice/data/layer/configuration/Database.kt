package br.com.alice.data.layer.configuration

import br.com.alice.common.core.BaseConfig
import br.com.alice.common.core.RunningMode
import br.com.alice.data.layer.PostgreDriver
import com.zaxxer.hikari.HikariConfig
import com.zaxxer.hikari.HikariDataSource
import com.zaxxer.hikari.util.DriverDataSource
import io.ktor.server.config.HoconApplicationConfig
import org.jdbi.v3.core.Jdbi
import org.jdbi.v3.postgres.PostgresPlugin
import java.sql.DriverManager
import java.util.Properties

fun buildHikariDataSource(config: HoconApplicationConfig, environment: String, database: String) : HikariDataSource {
    val connectionPoolConfig = HikariConfig()

    connectionPoolConfig.jdbcUrl = config.property("$environment.$database.database.url").getString()
    connectionPoolConfig.username = config.property("$environment.$database.database.user").getString()
    connectionPoolConfig.password = config.propertyOrNull("$environment.$database.database.pass")?.getString()
    connectionPoolConfig.maximumPoolSize = config.property("$environment.$database.database.maxPoolSize").getString().toInt()
    connectionPoolConfig.connectionTimeout = config.propertyOrNull("$environment.$database.database.connectionTimeout")?.getString()?.toLong() ?: 30000L //valor default pro HikariCP

    val driverClass = if (BaseConfig.instance.runningMode == RunningMode.PRODUCTION) {
        DriverManager.registerDriver(PostgreDriver())
        PostgreDriver::class.java.name
    } else null

    connectionPoolConfig.dataSource = DriverDataSource(
        connectionPoolConfig.jdbcUrl,
        driverClass,
        Properties(),
        connectionPoolConfig.username,
        connectionPoolConfig.password
    )

    return HikariDataSource(connectionPoolConfig)
}

class Database(private val config: HoconApplicationConfig) {
    fun get(database: String, environment: String = config.property("systemEnv").getString()): Jdbi {
        return Jdbi.create(HikariDataSource(buildHikariDataSource(config, environment, database))).installPlugin(PostgresPlugin())
    }
}
