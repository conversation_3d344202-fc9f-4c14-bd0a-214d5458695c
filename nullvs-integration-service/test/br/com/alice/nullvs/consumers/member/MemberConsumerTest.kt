package br.com.alice.nullvs.consumers.member

import br.com.alice.common.Brand
import br.com.alice.common.RangeUUID
import br.com.alice.common.core.exceptions.BadRequestException
import br.com.alice.common.core.extensions.onlyDigits
import br.com.alice.common.core.extensions.onlyNumbers
import br.com.alice.common.data.dsl.matchers.ResultAssert
import br.com.alice.common.featureflag.withFeatureFlag
import br.com.alice.common.featureflag.withFeatureFlags
import br.com.alice.common.helpers.coVerifyNone
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.common.helpers.returns
import br.com.alice.common.kafka.exceptions.AutoRetryableException
import br.com.alice.common.kafka.internals.LocalProducer
import br.com.alice.common.models.Sex
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.ExternalModelType
import br.com.alice.data.layer.models.FeatureNamespace
import br.com.alice.data.layer.models.InternalModelType
import br.com.alice.data.layer.models.Member
import br.com.alice.data.layer.models.MemberStatus
import br.com.alice.data.layer.models.MemberStatusHistoryEntry
import br.com.alice.data.layer.models.Person
import br.com.alice.nullvs.common.NullvsActionType
import br.com.alice.nullvs.consumers.ConsumerTest
import br.com.alice.nullvs.events.NullvsMemberActivatedEvent
import br.com.alice.nullvs.events.NullvsMemberBatchRequestEvent
import br.com.alice.nullvs.events.NullvsMemberReactivationBatchRequestEvent
import br.com.alice.nullvs.exceptions.CityCodeNotFoundException
import br.com.alice.nullvs.exceptions.DependsOnModelException
import br.com.alice.nullvs.exceptions.NonHadPreviousActivationException
import br.com.alice.nullvs.exceptions.NotFullMothersNameException
import br.com.alice.nullvs.exceptions.NullANSFieldException
import br.com.alice.nullvs.exceptions.NullPersonalFieldException
import br.com.alice.nullvs.exceptions.PersonAddressFieldException
import br.com.alice.nullvs.exceptions.SkipException
import br.com.alice.nullvs.exceptions.TestPersonalRegisterException
import br.com.alice.nullvs.exceptions.TotvsClientNotFoundByInternalIdException
import br.com.alice.nullvs.exceptions.TotvsMemberNotFoundByInternalIdException
import br.com.alice.nullvs.exceptions.TotvsParentMemberNotFoundByInternalIdException
import br.com.alice.nullvs.models.Meta
import br.com.alice.nullvs.models.TotvsCityCode
import br.com.alice.nullvs.models.TotvsGroupCompany
import br.com.alice.nullvs.models.TotvsRelationType
import br.com.alice.nullvs.models.TotvsUser
import br.com.alice.nullvs.models.member.GracePeriodType
import br.com.alice.nullvs.models.member.NullvsMemberBatchRequest
import br.com.alice.nullvs.models.member.NullvsMemberReactivationBatchRequest
import br.com.alice.nullvs.models.member.TotvsMemberReactivationRequest
import br.com.alice.nullvs.models.member.TotvsMemberRequest
import br.com.alice.nullvs.services.internals.TotvsMemberIntegrationService
import br.com.alice.nullvs.services.internals.dependency.NullvsDependencyService
import br.com.alice.person.br.com.alice.person.model.events.MemberReactivatedEvent
import br.com.alice.person.client.MemberService
import br.com.alice.person.model.events.MemberActivatedEvent
import br.com.alice.person.model.events.MemberCancelledEvent
import com.github.kittinunf.result.failure
import com.github.kittinunf.result.success
import io.mockk.coEvery
import io.mockk.mockk
import kotlinx.coroutines.runBlocking
import org.assertj.core.api.Assertions
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.TestInstance
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.MethodSource
import java.time.LocalDate
import java.time.LocalDateTime
import kotlin.test.BeforeTest
import kotlin.test.Test

@TestInstance(TestInstance.Lifecycle.PER_CLASS)
class MemberConsumerTest : ConsumerTest() {
    private val totvsMemberIntegrationService: TotvsMemberIntegrationService = mockk()
    private val memberService: MemberService = mockk()
    private val nullvsDependencyService: NullvsDependencyService = mockk()

    companion object {
        @JvmStatic
        fun exceptions() = listOf(
            NullPersonalFieldException(""),
            NotFullMothersNameException(""),
            TestPersonalRegisterException(""),
            PersonAddressFieldException(""),
            CityCodeNotFoundException(""),
            TestPersonalRegisterException(""),
            SkipException(""),
            TotvsParentMemberNotFoundByInternalIdException(""),
            DependsOnModelException(RangeUUID.generate(), InternalModelType.MEMBER)
        )
    }

    private val consumer =
        MemberConsumer(totvsMemberIntegrationService, LocalProducer, memberService, nullvsDependencyService)

    @BeforeTest
    fun setup() {
        super.before()
        LocalProducer.clearMessages()
    }

    private fun buildNullvsMemberBatchRequest(person: Person, member: Member, meta: Meta) = NullvsMemberBatchRequest(
        meta,
        NullvsActionType.CREATE,
        TotvsMemberRequest(
            company = TotvsGroupCompany.ALICE_INDIVIDUAL,
            client = "billing-accountable-party",
            createdAt = LocalDateTime.now(),
            ANSProductId = "ans-product-id",
            idPayload = 1,
            beneficiaries = listOf(
                TotvsMemberRequest.TotvsBeneficiary(
                    userType = TotvsUser.HOLDER,
                    email = person.email,
                    fullName = person.fullRegisterName,
                    fullSocialName = person.fullSocialName,
                    aliceId = member.id,
                    nationalId = person.nationalId.onlyNumbers(),
                    identityDocument = person.identityDocument,
                    mothersName = person.mothersName!!,
                    addressPostalCode = person.mainAddress!!.postalCode!!.onlyDigits(),
                    addressStreet = person.mainAddress!!.street,
                    addressNumber = person.mainAddress!!.number,
                    addressComplement = person.mainAddress!!.complement,
                    addressNeighborhood = person.mainAddress!!.neighbourhood!!,
                    addressCity = TotvsCityCode("3550308", "SAO PAULO", "SP"),
                    addressState = person.mainAddress!!.state.toString(),
                    phoneNumber = person.phoneNumber!!,
                    dateOfBirth = person.dateOfBirth!!,
                    createdAt = person.createdAt,
                    parentBeneficiaryRelationType = TotvsRelationType.HOLDER,
                    sex = Sex.FEMALE,
                    cnsNumber = person.cnsNumber,
                    ccoCodeANS = null,
                    ANSProductId = "ans-id",
                    canceledReason = null,
                    canceledAt = null,
                    cpts = listOf(
                        TotvsMemberRequest.TotvsBeneficiary.CPT(
                            cid = "XPTO",
                            startedAt = LocalDate.of(2023, 6, 1),
                            periodInDays = 730L
                        )
                    ),
                    gracePeriods = listOf(
                        TotvsMemberRequest.TotvsBeneficiary.GracePeriod(
                            type = GracePeriodType.BIRTH,
                            startedAt = LocalDate.of(2023, 6, 1),
                            periodInDays = 300L
                        )
                    ),
                ),
            ),
        ),
    )

    @Nested
    inner class MemberReactivated {

        private val member = TestModelFactory.buildMember()
        private val event = MemberReactivatedEvent(member)
        private val mlcEvent = TestModelFactory.buildMemberLifeCycleEvents(memberId = member.id)
        private val familyCode = "0111406"
        private val meta = Meta(
            eventId = event.messageId,
            eventName = event.name,
            internalModelName = InternalModelType.MEMBER,
            internalId = member.id,
            externalModelName = ExternalModelType.BENEFICIARY,
            integratedAt = event.eventDate,
            originalTopic = MemberReactivatedEvent.name,
            integrationEventName = NullvsMemberReactivationBatchRequestEvent.name,
        )
        private val requestMemberReactivationBatchRequest = NullvsMemberReactivationBatchRequest(
            metadata = meta,
            action = NullvsActionType.REACTIVATION,
            totvsMemberRequest = TotvsMemberReactivationRequest(
                totvsId = familyCode!!,
                idPayload = 1,
                aliceId = member.id,
                actionAt = mlcEvent.actionAt,
                actionCode = mlcEvent.reason.externalCode,
                observation = mlcEvent.observation,
            ),
        )

        @Test
        fun `#should produce NullvsMemberReactivationBatchRequestEvent`() =
            runBlocking {
                coEvery {
                    totvsMemberIntegrationService.reactivateMemberTotvs(meta, member)
                } returns requestMemberReactivationBatchRequest

                val result = consumer.memberReactivated(event)

                ResultAssert.assertThat(result).isSuccessWithData(true)

                Assertions.assertThat(LocalProducer.hasEvent(NullvsMemberReactivationBatchRequestEvent.name)).isTrue

                coVerifyOnce { totvsMemberIntegrationService.reactivateMemberTotvs(meta, member) }
            }
    }

    @Nested
    inner class MemberActivated {
        private val member = TestModelFactory.buildMember()
        private val event = MemberActivatedEvent(member)
        private val meta = Meta(
            eventId = event.messageId,
            eventName = event.name,
            internalModelName = InternalModelType.MEMBER,
            internalId = member.id,
            externalModelName = ExternalModelType.BENEFICIARY,
            integratedAt = event.eventDate,
            originalTopic = MemberActivatedEvent.name,
            integrationEventName = NullvsMemberBatchRequestEvent.name,
        )
        private val person = TestModelFactory.buildPerson(
            mothersName = "Mother's name",
            phoneNumber = "phone",
            dateOfBirth = LocalDateTime.of(1994, 10, 17, 14, 0),
        )

        private val requestMemberBatchRequest = buildNullvsMemberBatchRequest(person, member, meta)

        @Test
        fun `#should return an exception when something is wrong`() = runBlocking {
            withFeatureFlag(FeatureNamespace.NULLVS, "should_integrate_member_nullvs", true) {
                coEvery {
                    totvsMemberIntegrationService.createBeneficiaryTotvs(
                        meta,
                        member
                    )
                } returns Exception("")

                val result = consumer.memberActivated(event)

                ResultAssert.assertThat(result).isFailureOfType(Exception::class)

                coVerifyOnce { totvsMemberIntegrationService.createBeneficiaryTotvs(meta, member) }
            }
        }

        @Test
        fun `#should return an AutoRetryableException in case of TotvsClientNotFoundByInternalIdException`() =
            runBlocking {
                withFeatureFlag(FeatureNamespace.NULLVS, "should_integrate_member_nullvs", true) {
                    coEvery {
                        totvsMemberIntegrationService.createBeneficiaryTotvs(
                            meta,
                            member
                        )
                    } returns TotvsClientNotFoundByInternalIdException(member.id).failure()

                    val result = consumer.memberActivated(event)

                    ResultAssert.assertThat(result).isFailureOfType(AutoRetryableException::class)

                    coVerifyOnce { totvsMemberIntegrationService.createBeneficiaryTotvs(meta, member) }
                }
            }

        @ParameterizedTest(name = "should return success when exception {0} happens to avoid dlq")
        @MethodSource("br.com.alice.nullvs.consumers.member.MemberConsumerTest#exceptions")
        fun `#should ignore return an error on TestPersonalRegisterException`(exception: BadRequestException) =
            runBlocking {
                withFeatureFlag(FeatureNamespace.NULLVS, "should_integrate_member_nullvs", true) {
                    coEvery {
                        totvsMemberIntegrationService.createBeneficiaryTotvs(
                            meta,
                            member
                        )
                    } returns exception.failure()

                    val result = consumer.memberActivated(event)

                    ResultAssert.assertThat(result).isSuccessWithData(false)

                    coVerifyOnce { totvsMemberIntegrationService.createBeneficiaryTotvs(meta, member) }
                }
            }

        @Test
        fun `should return success when NullANSFieldException happens to avoid dlq`() = runBlocking<Unit> {
            withFeatureFlag(FeatureNamespace.NULLVS, "should_integrate_member_nullvs", true) {

                coEvery {
                    totvsMemberIntegrationService.createBeneficiaryTotvs(
                        meta,
                        member
                    )
                } returns NullANSFieldException("error").failure()

                val result = consumer.memberActivated(event)

                ResultAssert.assertThat(result).isSuccess()
            }
        }

        @Test
        fun `#should produce NullvsMemberBatchRequestEvent`() =
            runBlocking {
                coEvery {
                    totvsMemberIntegrationService.createBeneficiaryTotvs(
                        meta,
                        member
                    )
                } returns requestMemberBatchRequest

                val result = consumer.memberActivated(event)

                ResultAssert.assertThat(result).isSuccessWithData(true)

                Assertions.assertThat(LocalProducer.hasEvent(NullvsMemberBatchRequestEvent.name)).isTrue

                coVerifyOnce { totvsMemberIntegrationService.createBeneficiaryTotvs(meta, member) }
            }

    }

    @Nested
    inner class MemberCancelled {
        private val member = TestModelFactory.buildMember()
        private val event = MemberCancelledEvent(member)
        private val meta = Meta(
            eventId = event.messageId,
            eventName = event.name,
            internalModelName = InternalModelType.MEMBER,
            internalId = member.id,
            externalModelName = ExternalModelType.BENEFICIARY,
            integratedAt = event.eventDate,
            originalTopic = MemberCancelledEvent.name,
            integrationEventName = NullvsMemberBatchRequestEvent.name,
        )
        private val person = TestModelFactory.buildPerson(
            mothersName = "Mother's name",
            phoneNumber = "phone",
            dateOfBirth = LocalDateTime.of(1994, 10, 17, 14, 0),
        )

        private val requestMemberBatchRequest = buildNullvsMemberBatchRequest(person, member, meta)

        @Test
        fun `#should produce NullvsMemberBatchRequestEvent to cancel a member when a MemberCancelledEvent is consumed by Nullvs`() =
            runBlocking {
                coEvery {
                    totvsMemberIntegrationService.cancelBeneficiaryTotvs(
                        meta,
                        member,
                    )
                } returns requestMemberBatchRequest

                val result = consumer.memberCancelled(event)

                ResultAssert.assertThat(result).isSuccessWithData(true)

                Assertions.assertThat(LocalProducer.hasEvent(NullvsMemberBatchRequestEvent.name)).isTrue

                coVerifyOnce { totvsMemberIntegrationService.cancelBeneficiaryTotvs(meta, member) }
            }

        @Test
        fun `#should produce NullvsMemberBatchRequestEvent even when the member is DUQUESA when the skip FF is false`() =
            runBlocking {
                withFeatureFlags(
                    FeatureNamespace.NULLVS,
                    mapOf("should_skip_duquesa_member_nullvs" to false)
                ) {
                    val member = member.copy(brand = Brand.DUQUESA)
                    val event = MemberCancelledEvent(member)
                    val meta = meta.copy(eventId = event.messageId, integratedAt = event.eventDate)

                    val requestMemberBatchRequest = buildNullvsMemberBatchRequest(person, member, meta)

                    coEvery {
                        totvsMemberIntegrationService.cancelBeneficiaryTotvs(
                            meta,
                            member,
                        )
                    } returns requestMemberBatchRequest

                    val result = consumer.memberCancelled(event)

                    ResultAssert.assertThat(result).isSuccessWithData(true)

                    Assertions.assertThat(LocalProducer.hasEvent(NullvsMemberBatchRequestEvent.name)).isTrue

                    coVerifyOnce { totvsMemberIntegrationService.cancelBeneficiaryTotvs(meta, member) }
                }
            }

        @Test
        fun `#should return an AutoRetryableException in case of TotvsMemberNotFoundByInternalIdException`() =
            runBlocking {
                withFeatureFlag(FeatureNamespace.NULLVS, "should_integrate_member_nullvs", true) {
                    coEvery {
                        totvsMemberIntegrationService.cancelBeneficiaryTotvs(
                            meta,
                            member
                        )
                    } returns TotvsMemberNotFoundByInternalIdException(member.id).failure()

                    val result = consumer.memberCancelled(event)

                    ResultAssert.assertThat(result).isFailureOfType(AutoRetryableException::class)

                    coVerifyOnce { totvsMemberIntegrationService.cancelBeneficiaryTotvs(meta, member) }
                }
            }

        @Test
        fun `#should ignore return an error on NonHadPreviousActivationException`() = runBlocking {
            withFeatureFlag(FeatureNamespace.NULLVS, "should_integrate_member_nullvs", true) {

                coEvery {
                    totvsMemberIntegrationService.cancelBeneficiaryTotvs(
                        meta,
                        member
                    )
                } returns NonHadPreviousActivationException(member.id).failure()

                val result = consumer.memberCancelled(event)

                ResultAssert.assertThat(result).isSuccessWithData(false)

                coVerifyOnce { totvsMemberIntegrationService.cancelBeneficiaryTotvs(meta, member) }
            }
        }

        @Test
        fun `#should produce NullvsMemberBatchRequestEvent to cancel a member when member is duquesa without activation status`() =
            runBlocking {

                val memberDuquesa = member.copy(
                    brand = Brand.DUQUESA, statusHistory = listOf(
                        MemberStatusHistoryEntry(MemberStatus.PENDING, LocalDateTime.now().minusDays(10).toString()),
                        MemberStatusHistoryEntry(MemberStatus.CANCELED, LocalDateTime.now().toString())
                    )
                )

                val eventDuquesa = MemberCancelledEvent(memberDuquesa)

                val metaDuquesa = Meta(
                    eventId = eventDuquesa.messageId,
                    eventName = eventDuquesa.name,
                    internalModelName = InternalModelType.MEMBER,
                    internalId = memberDuquesa.id,
                    externalModelName = ExternalModelType.BENEFICIARY,
                    integratedAt = eventDuquesa.eventDate,
                    originalTopic = MemberCancelledEvent.name,
                    integrationEventName = NullvsMemberBatchRequestEvent.name,
                )


                coEvery {
                    totvsMemberIntegrationService.cancelBeneficiaryTotvs(
                        metaDuquesa,
                        memberDuquesa,
                    )
                } returns requestMemberBatchRequest

                coEvery {
                    totvsMemberIntegrationService.cancelBeneficiaryTotvs(
                        metaDuquesa,
                        memberDuquesa,
                    )
                } returns requestMemberBatchRequest


                val result = consumer.memberCancelled(eventDuquesa)

                ResultAssert.assertThat(result).isSuccessWithData(true)

                Assertions.assertThat(LocalProducer.hasEvent(NullvsMemberBatchRequestEvent.name)).isTrue

                coVerifyOnce { totvsMemberIntegrationService.cancelBeneficiaryTotvs(metaDuquesa, memberDuquesa) }
            }
    }

    @Nested
    inner class SyncDependentsOfActivatedMember {
        private val person = TestModelFactory.buildPerson(
            mothersName = "Mother's name",
            phoneNumber = "phone",
            dateOfBirth = LocalDateTime.of(1994, 10, 17, 14, 0),
        )

        @Test
        fun `#should produce NullvsMemberBatchRequestEvent to create a dependent member when dependent is found`() =
            runBlocking {
                withFeatureFlag(FeatureNamespace.NULLVS, "use_dependency_service", false) {
                    val parentMember = TestModelFactory.buildMember(
                        status = MemberStatus.ACTIVE,
                    )

                    val dependent = TestModelFactory.buildMember(
                        parentMember = parentMember.id,
                        status = MemberStatus.ACTIVE,
                    )

                    val event = NullvsMemberActivatedEvent(parentMember.id, "000000000")

                    val meta = Meta(
                        eventId = event.messageId,
                        eventName = event.name,
                        internalModelName = InternalModelType.MEMBER,
                        internalId = dependent.id,
                        externalModelName = ExternalModelType.BENEFICIARY,
                        integratedAt = event.eventDate,
                        originalTopic = NullvsMemberActivatedEvent.name,
                        integrationEventName = NullvsMemberBatchRequestEvent.name,
                    )


                    coEvery {
                        memberService.findDependsByParentMemberIdAndStatus(
                            any(),
                            any()
                        )
                    } returns listOf(dependent).success()

                    coEvery {
                        totvsMemberIntegrationService.createBeneficiaryTotvs(
                            any(),
                            dependent
                        )
                    } returns buildNullvsMemberBatchRequest(person, dependent, meta)

                    val result = consumer.syncDependentsOfActivatedMember(event)

                    ResultAssert.assertThat(result).isSuccess()

                    Assertions.assertThat(LocalProducer.hasEvent(NullvsMemberBatchRequestEvent.name)).isTrue

                    coVerifyOnce {
                        memberService.findDependsByParentMemberIdAndStatus(
                            parentMember.id,
                            listOf(MemberStatus.ACTIVE)
                        )
                    }
                    coVerifyOnce {
                        totvsMemberIntegrationService.createBeneficiaryTotvs(
                            match { it.internalId == dependent.id },
                            dependent
                        )
                    }
                }
            }

        @Test
        fun `#should return success when there are no dependents`() = runBlocking {
            withFeatureFlag(FeatureNamespace.NULLVS, "use_dependency_service", false) {
                val parentMember = TestModelFactory.buildMember(
                    status = MemberStatus.ACTIVE,
                )

                val event = NullvsMemberActivatedEvent(parentMember.id, "000000000")


                coEvery {
                    memberService.findDependsByParentMemberIdAndStatus(
                        any(),
                        any()
                    )
                } returns emptyList<Member>().success()

                val result = consumer.syncDependentsOfActivatedMember(event)

                ResultAssert.assertThat(result).isSuccess()

                Assertions.assertThat(LocalProducer.hasEvent(NullvsMemberBatchRequestEvent.name)).isFalse

                coVerifyOnce {
                    memberService.findDependsByParentMemberIdAndStatus(
                        parentMember.id,
                        listOf(MemberStatus.ACTIVE)
                    )
                }
                coVerifyNone { totvsMemberIntegrationService.createBeneficiaryTotvs(any(), any()) }
            }
        }

        @Test
        fun `#should reprocess the consumer when the FF use_dependency_service is true`() = runBlocking {
            val parentMember = TestModelFactory.buildMember(
                status = MemberStatus.ACTIVE,
            )

            coEvery {
                nullvsDependencyService.reprocessItsDependents(parentMember.id, InternalModelType.MEMBER)
            } returns emptyList()

            val event = NullvsMemberActivatedEvent(parentMember.id, "000000000")

            val result = consumer.syncDependentsOfActivatedMember(event)

            ResultAssert.assertThat(result).isSuccess()

            Assertions.assertThat(LocalProducer.hasEvent(NullvsMemberBatchRequestEvent.name)).isFalse

            coVerifyOnce { nullvsDependencyService.reprocessItsDependents(any(), any()) }

            coVerifyNone {
                memberService.findDependsByParentMemberIdAndStatus(any(), any())
                totvsMemberIntegrationService.createBeneficiaryTotvs(any(), any())
            }
        }
    }
}
