package br.com.alice.business.services

import br.com.alice.business.client.BeneficiaryOnboardingService
import br.com.alice.business.converters.model.toModel
import br.com.alice.business.converters.model.toTransport
import br.com.alice.business.events.BeneficiaryOnboardingPhaseChangedEvent
import br.com.alice.business.events.BeneficiaryOnboardingUpdatedEvent
import br.com.alice.business.events.BeneficiaryTermAcceptedEvent
import br.com.alice.business.events.MoveToPhaseEvent
import br.com.alice.business.exceptions.InvalidBeneficiaryOnboardingFlowTypeUpdateException
import br.com.alice.business.logics.BeneficiaryOnboardingLogic
import br.com.alice.business.model.AcceptTermsTransport
import br.com.alice.business.model.BeneficiaryTermType
import br.com.alice.common.RangeUUID
import br.com.alice.common.core.exceptions.DuplicatedItemException
import br.com.alice.common.data.dsl.matchers.ResultAssert.Companion.assertThat
import br.com.alice.common.data.dsl.matchers.queryEq
import br.com.alice.common.helpers.coVerifyNone
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.common.helpers.returns
import br.com.alice.common.kafka.interfaces.KafkaProducerService
import br.com.alice.common.kafka.interfaces.ProducerResult
import br.com.alice.common.service.data.dsl.and
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.BeneficiaryAlreadyOnCorrectPhaseException
import br.com.alice.data.layer.models.BeneficiaryOnboardingFlowType
import br.com.alice.data.layer.models.BeneficiaryOnboardingPhaseType
import br.com.alice.data.layer.models.InvalidBeneficiaryOnboardingToPhaseException
import br.com.alice.data.layer.models.InvalidBusinessOnboardingFlowStateException
import br.com.alice.data.layer.models.withPhases
import br.com.alice.data.layer.services.BeneficiaryOnboardingModelDataService
import br.com.alice.data.layer.services.BeneficiaryOnboardingPhaseModelDataService
import io.mockk.clearAllMocks
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.every
import io.mockk.mockk
import io.mockk.mockkObject
import io.mockk.unmockkAll
import kotlinx.coroutines.runBlocking
import java.time.LocalDateTime
import kotlin.test.AfterTest
import kotlin.test.BeforeTest
import kotlin.test.Test

class BeneficiaryOnboardingServiceImplTest {
    private val beneficiaryOnboardingDataService: BeneficiaryOnboardingModelDataService = mockk()
    private val beneficiaryOnboardingPhaseDataService: BeneficiaryOnboardingPhaseModelDataService = mockk()
    private val producerService: KafkaProducerService = mockk()
    private val beneficiaryOnboardingService = BeneficiaryOnboardingServiceImpl(
        beneficiaryOnboardingDataService,
        beneficiaryOnboardingPhaseDataService,
        producerService
    )

    @BeforeTest
    fun setup() {
        mockkObject(BeneficiaryOnboardingLogic)
    }

    @AfterTest
    fun clean() {
        clearAllMocks()
        unmockkAll()
    }

    @Test
    fun `#moveToNextPhase returns and create a new BeneficiaryOnboardingPhase`() = runBlocking {
        val beneficiaryId = RangeUUID.generate()
        val beneficiaryOnboardingId = RangeUUID.generate()
        val beneficiaryOnboardingPhase = TestModelFactory
            .buildBeneficiaryOnboardingPhase(
                phase = BeneficiaryOnboardingPhaseType.READY_TO_ONBOARD,
                beneficiaryOnboardingId = beneficiaryOnboardingId
            )
        val beneficiaryOnboarding = TestModelFactory.buildBeneficiaryOnboarding(
            id = beneficiaryOnboardingId, beneficiaryId = beneficiaryId, phases = listOf(beneficiaryOnboardingPhase)
        ).toModel()

        coEvery { beneficiaryOnboardingDataService.findOne(any()) } returns beneficiaryOnboarding
        coEvery { beneficiaryOnboardingPhaseDataService.add(any()) } returns beneficiaryOnboarding.phases.first()
        coEvery { beneficiaryOnboardingPhaseDataService.find(any()) } returns beneficiaryOnboarding.phases
        coEvery { producerService.produce(any(), any()) } returns mockk()

        val result = beneficiaryOnboardingService.moveToNextPhase(beneficiaryId)
        assertThat(result).isSuccessWithData(beneficiaryOnboardingPhase)

        coVerifyOnce {
            beneficiaryOnboardingPhaseDataService.add(match { it.phase == beneficiaryOnboarding.nextPhase().phase })
        }

        coVerifyOnce {
            beneficiaryOnboardingPhaseDataService.find(queryEq {
                where { this.beneficiaryOnboardingId.eq(beneficiaryOnboardingId) }
            })
        }

        coVerifyOnce {
            beneficiaryOnboardingDataService.findOne(queryEq {
                where { this.beneficiaryId.eq(beneficiaryId) }
            })
        }

        coVerifyOnce { producerService.produce(match {
            it is BeneficiaryOnboardingPhaseChangedEvent &&
                    it.payload.beneficiaryId == beneficiaryId &&
                    it.payload.newPhase.phase == beneficiaryOnboardingPhase.phase
        }, beneficiaryId.toString()) }
    }

    @Test
    fun `#findByBeneficiaryId returns the BeneficiaryOnboarding related`() = runBlocking {
        val beneficiaryId = RangeUUID.generate()
        val beneficiaryOnboardingId = RangeUUID.generate()
        val beneficiaryOnboardingPhase = TestModelFactory
            .buildBeneficiaryOnboardingPhase(
                phase = BeneficiaryOnboardingPhaseType.READY_TO_ONBOARD,
                beneficiaryOnboardingId = beneficiaryOnboardingId
            )
        val beneficiaryOnboarding = TestModelFactory.buildBeneficiaryOnboarding(
            id = beneficiaryOnboardingId, beneficiaryId = beneficiaryId, phases = listOf(beneficiaryOnboardingPhase)
        ).toModel()

        coEvery { beneficiaryOnboardingDataService.findOne(any()) } returns beneficiaryOnboarding
        coEvery { beneficiaryOnboardingPhaseDataService.find(any()) } returns beneficiaryOnboarding.phases

        val result = beneficiaryOnboardingService.findByBeneficiaryId(beneficiaryId)
        assertThat(result).isSuccessWithData(beneficiaryOnboarding.toTransport())

        coVerifyOnce {
            beneficiaryOnboardingDataService.findOne(queryEq {
                where { this.beneficiaryId.eq(beneficiaryId) }
            })
        }

        coVerifyOnce {
            beneficiaryOnboardingPhaseDataService.find(queryEq {
                where { this.beneficiaryOnboardingId.eq(beneficiaryOnboarding.id) }
            })
        }
    }

    @Test
    fun `#findByBeneficiaryIds returns the list of BeneficiaryOnboarding related`() = runBlocking {
        val beneficiaryId = RangeUUID.generate()
        val beneficiaryOnboardingId = RangeUUID.generate()
        val beneficiaryOnboardingPhase = TestModelFactory
            .buildBeneficiaryOnboardingPhase(
                phase = BeneficiaryOnboardingPhaseType.READY_TO_ONBOARD,
                beneficiaryOnboardingId = beneficiaryOnboardingId
            )

        val beneficiaryOnboarding = TestModelFactory.buildBeneficiaryOnboarding(
            id = beneficiaryOnboardingId, beneficiaryId = beneficiaryId, phases = listOf(beneficiaryOnboardingPhase)
        ).toModel()

        coEvery { beneficiaryOnboardingDataService.find(any()) } returns listOf(beneficiaryOnboarding)
        coEvery { beneficiaryOnboardingPhaseDataService.find(any()) } returns beneficiaryOnboarding.phases

        val result = beneficiaryOnboardingService.findByBeneficiaryIds(listOf(beneficiaryId))
        assertThat(result).isSuccessWithData(listOf(beneficiaryOnboarding.toTransport()))

        coVerifyOnce {
            beneficiaryOnboardingDataService.find(queryEq {
                where { this.beneficiaryId.inList(listOf(beneficiaryId)) }
            })
        }

        coVerifyOnce {
            beneficiaryOnboardingPhaseDataService.find(queryEq {
                where { this.beneficiaryOnboardingId.inList(listOf(beneficiaryOnboarding.id)) }
            })
        }
    }

    @Test
    fun `#findByBeneficiaryIds returns the list of BeneficiaryOnboarding related without phases`() = runBlocking {
        val beneficiaryId = RangeUUID.generate()
        val beneficiaryOnboardingId = RangeUUID.generate()
        val beneficiaryOnboardingPhase = TestModelFactory
            .buildBeneficiaryOnboardingPhase(
                phase = BeneficiaryOnboardingPhaseType.READY_TO_ONBOARD,
                beneficiaryOnboardingId = beneficiaryOnboardingId
            )

        val beneficiaryOnboarding = TestModelFactory.buildBeneficiaryOnboarding(
            id = beneficiaryOnboardingId, beneficiaryId = beneficiaryId, phases = listOf(beneficiaryOnboardingPhase)
        ).toModel()

        coEvery { beneficiaryOnboardingDataService.find(any()) } returns listOf(beneficiaryOnboarding)

        val result = beneficiaryOnboardingService.findByBeneficiaryIds(
            listOf(beneficiaryId),
            findOptions = BeneficiaryOnboardingService.FindOptions(withPhases = false),
        )
        assertThat(result).isSuccessWithData(listOf(beneficiaryOnboarding.toTransport()))

        coVerifyOnce {
            beneficiaryOnboardingDataService.find(queryEq {
                where { this.beneficiaryId.inList(listOf(beneficiaryId)) }
            })
        }

        coVerifyNone {
            beneficiaryOnboardingPhaseDataService.find(any())
        }
    }

    @Test
    fun `#update should update undefined Flow successfully`() = runBlocking {
        val beneficiaryId = RangeUUID.generate()
        val beneficiaryOnboardingId = RangeUUID.generate()

        val beneficiaryOnboardingPhase = TestModelFactory.buildBeneficiaryOnboardingPhase(
            phase = BeneficiaryOnboardingPhaseType.READY_TO_ONBOARD,
            beneficiaryOnboardingId = beneficiaryOnboardingId,
        )
        val beneficiaryOnboarding = TestModelFactory.buildBeneficiaryOnboarding(
            id = beneficiaryOnboardingId,
            beneficiaryId = beneficiaryId,
            flowType = BeneficiaryOnboardingFlowType.UNDEFINED,
            phases = listOf(beneficiaryOnboardingPhase),
        ).toModel()
        val beneficiaryOnboardingWithNewFlowType = TestModelFactory.buildBeneficiaryOnboarding(
            id = beneficiaryOnboardingId,
            beneficiaryId = beneficiaryId,
            flowType = BeneficiaryOnboardingFlowType.FULL_RISK_FLOW,
            phases = listOf(beneficiaryOnboardingPhase),
        ).toModel()

        coEvery { beneficiaryOnboardingDataService.findOne(any()) } returns beneficiaryOnboarding
        coEvery { beneficiaryOnboardingPhaseDataService.find(any()) } returns beneficiaryOnboarding.phases
        coEvery { beneficiaryOnboardingDataService.update(any()) } returns beneficiaryOnboardingWithNewFlowType
        coEvery { producerService.produce( match { it.name == "BENEFICIARY-ONBOARDING-UPDATED" })
        } returns ProducerResult(
            producedAt = LocalDateTime.now(),
            topic = BeneficiaryOnboardingUpdatedEvent.name,
            offset = 1L
        )

        val beneficiaryOnboardingUpdated = beneficiaryOnboardingService.update(
            beneficiaryOnboardingWithNewFlowType.toTransport()
        )

        assertThat(beneficiaryOnboardingUpdated).isSuccessWithData(beneficiaryOnboardingWithNewFlowType.toTransport())

        coVerify {
            beneficiaryOnboardingDataService.findOne(queryEq {
                where { this.beneficiaryId.eq(beneficiaryOnboardingWithNewFlowType.beneficiaryId) }
            })
        }

        coVerify {
            beneficiaryOnboardingPhaseDataService.find(queryEq {
                where { this.beneficiaryOnboardingId.eq(beneficiaryOnboardingWithNewFlowType.id) }
            })
        }

        coVerify {
            beneficiaryOnboardingDataService.update(beneficiaryOnboardingWithNewFlowType)
        }
        coVerifyOnce { producerService.produce( match { it.name == "BENEFICIARY-ONBOARDING-UPDATED" }) }

    }

    @Test
    fun `#update should update Flow successfully when new status is undefined`() = runBlocking {
        val beneficiaryId = RangeUUID.generate()
        val beneficiaryOnboardingId = RangeUUID.generate()

        val beneficiaryOnboardingPhase = TestModelFactory.buildBeneficiaryOnboardingPhase(
            phase = BeneficiaryOnboardingPhaseType.READY_TO_ONBOARD,
            beneficiaryOnboardingId = beneficiaryOnboardingId,
        )
        val beneficiaryOnboarding = TestModelFactory.buildBeneficiaryOnboarding(
            id = beneficiaryOnboardingId,
            beneficiaryId = beneficiaryId,
            flowType = BeneficiaryOnboardingFlowType.FULL_RISK_FLOW,
            phases = listOf(beneficiaryOnboardingPhase),
        ).toModel()
        val beneficiaryOnboardingWithNewFlowType = TestModelFactory.buildBeneficiaryOnboarding(
            id = beneficiaryOnboardingId,
            beneficiaryId = beneficiaryId,
            flowType = BeneficiaryOnboardingFlowType.UNDEFINED,
            phases = listOf(beneficiaryOnboardingPhase),
        ).toModel()

        coEvery { beneficiaryOnboardingDataService.findOne(any()) } returns beneficiaryOnboarding
        coEvery { beneficiaryOnboardingPhaseDataService.find(any()) } returns beneficiaryOnboarding.phases
        coEvery { beneficiaryOnboardingDataService.update(any()) } returns beneficiaryOnboardingWithNewFlowType
        coEvery { producerService.produce( match { it.name == "BENEFICIARY-ONBOARDING-UPDATED" })
        } returns ProducerResult(
            producedAt = LocalDateTime.now(),
            topic = BeneficiaryOnboardingUpdatedEvent.name,
            offset = 1L
        )

        val beneficiaryOnboardingUpdated = beneficiaryOnboardingService.update(
            beneficiaryOnboardingWithNewFlowType.toTransport()
        )

        assertThat(beneficiaryOnboardingUpdated).isSuccessWithData(beneficiaryOnboardingWithNewFlowType.toTransport())

        coVerify {
            beneficiaryOnboardingDataService.findOne(queryEq {
                where { this.beneficiaryId.eq(beneficiaryOnboardingWithNewFlowType.beneficiaryId) }
            })
        }

        coVerify {
            beneficiaryOnboardingPhaseDataService.find(queryEq {
                where { this.beneficiaryOnboardingId.eq(beneficiaryOnboardingWithNewFlowType.id) }
            })
        }

        coVerify {
            beneficiaryOnboardingDataService.update(beneficiaryOnboardingWithNewFlowType)
        }
        coVerifyOnce { producerService.produce( match { it.name == "BENEFICIARY-ONBOARDING-UPDATED" }) }

    }

    @Test
    fun `#moveToPhase should get and update its phase`() = runBlocking<Unit> {
        val beneficiaryId = RangeUUID.generate()
        val phase = BeneficiaryOnboardingPhaseType.HEALTH_DECLARATION
        val beneficiaryOnboarding = TestModelFactory.buildBeneficiaryOnboarding(beneficiaryId = beneficiaryId).toModel()
        val beneficiaryOnboardingPhase = TestModelFactory.buildBeneficiaryOnboardingPhase(
            phase = BeneficiaryOnboardingPhaseType.READY_TO_ONBOARD,
            beneficiaryOnboardingId = beneficiaryOnboarding.id
        ).toModel()
        val beneficiaryOnboardingPhases = listOf(beneficiaryOnboardingPhase)
        val newBeneficiaryOnboardingPhase = TestModelFactory.buildBeneficiaryOnboardingPhase(
            beneficiaryOnboardingId = beneficiaryOnboarding.id
        ).toModel()
        val transactedAt = LocalDateTime.now()

        coEvery { beneficiaryOnboardingDataService.findOne(any()) } returns beneficiaryOnboarding
        coEvery { beneficiaryOnboardingPhaseDataService.find(any()) } returns beneficiaryOnboardingPhases
        coEvery { beneficiaryOnboardingPhaseDataService.add(any()) } returns newBeneficiaryOnboardingPhase
        coEvery { producerService.produce(any(), any()) } returns mockk()

        val result = beneficiaryOnboardingService.moveToPhase(beneficiaryId, phase, transactedAt)
        assertThat(result).isSuccessWithData(newBeneficiaryOnboardingPhase.toTransport())

        coVerifyOnce {
            beneficiaryOnboardingDataService.findOne(queryEq { where { this.beneficiaryId.eq(beneficiaryId) } })
        }
        coVerifyOnce {
            beneficiaryOnboardingPhaseDataService.find(queryEq {
                where { this.beneficiaryOnboardingId.eq(beneficiaryOnboarding.id) }
            })
        }
        coVerifyOnce { beneficiaryOnboardingPhaseDataService.add(match {
            it.phase == phase &&
                    it.beneficiaryOnboardingId == beneficiaryOnboarding.id &&
                    it.id != beneficiaryOnboardingPhase.id
        }) }
        coVerifyOnce { producerService.produce(match {
            it is BeneficiaryOnboardingPhaseChangedEvent &&
                    it.payload.beneficiaryId == beneficiaryId &&
                    it.payload.newPhase.phase == newBeneficiaryOnboardingPhase.phase
        }, beneficiaryId.toString()) }
    }

    @Test
    fun `#moveToPhase if requested phase is invalid, returns failure`() = runBlocking<Unit> {
        val beneficiaryId = RangeUUID.generate()
        val phase = BeneficiaryOnboardingPhaseType.FINISHED
        val beneficiaryOnboarding = TestModelFactory.buildBeneficiaryOnboarding(
            beneficiaryId = beneficiaryId
        ).toModel()
        val beneficiaryOnboardingPhase = TestModelFactory.buildBeneficiaryOnboardingPhase(
            beneficiaryOnboardingId = beneficiaryOnboarding.id
        ).toModel()
        val beneficiaryOnboardingPhases = listOf(beneficiaryOnboardingPhase)
        val transactedAt = LocalDateTime.now()

        coEvery { beneficiaryOnboardingDataService.findOne(any()) } returns beneficiaryOnboarding
        coEvery { beneficiaryOnboardingPhaseDataService.find(any()) } returns beneficiaryOnboardingPhases

        val result = beneficiaryOnboardingService.moveToPhase(beneficiaryId, phase, transactedAt)
        assertThat(result).isFailureOfType(InvalidBeneficiaryOnboardingToPhaseException::class)

        coVerifyOnce {
            beneficiaryOnboardingDataService.findOne(queryEq { where { this.beneficiaryId.eq(beneficiaryId) } })
        }
        coVerifyOnce {
            beneficiaryOnboardingPhaseDataService.find(queryEq {
                where { this.beneficiaryOnboardingId.eq(beneficiaryOnboarding.id) }
            })
        }
        coVerifyNone { beneficiaryOnboardingPhaseDataService.add(any()) }
    }

    @Test
    fun `#moveToPhase if requested phase is the same, returns failure`() = runBlocking<Unit> {
        val beneficiaryId = RangeUUID.generate()
        val phase = BeneficiaryOnboardingPhaseType.SEND_HEALTH_DECLARATION
        val beneficiaryOnboarding = TestModelFactory.buildBeneficiaryOnboarding(beneficiaryId = beneficiaryId).toModel()
        val beneficiaryOnboardingPhase = TestModelFactory.buildBeneficiaryOnboardingPhase(
            beneficiaryOnboardingId = beneficiaryOnboarding.id,
            phase = BeneficiaryOnboardingPhaseType.SEND_HEALTH_DECLARATION
        ).toModel()
        val beneficiaryOnboardingPhases = listOf(beneficiaryOnboardingPhase)
        val transactedAt = LocalDateTime.now()

        coEvery { beneficiaryOnboardingDataService.findOne(any()) } returns beneficiaryOnboarding
        coEvery { beneficiaryOnboardingPhaseDataService.find(any()) } returns beneficiaryOnboardingPhases

        val result = beneficiaryOnboardingService.moveToPhase(beneficiaryId, phase, transactedAt)
        assertThat(result).isFailureOfType(InvalidBusinessOnboardingFlowStateException::class)

        coVerifyOnce {
            beneficiaryOnboardingDataService.findOne(queryEq { where { this.beneficiaryId.eq(beneficiaryId) } })
        }
        coVerifyOnce {
            beneficiaryOnboardingPhaseDataService.find(queryEq {
                where { this.beneficiaryOnboardingId.eq(beneficiaryOnboarding.id) }
            })
        }
        coVerifyNone { beneficiaryOnboardingPhaseDataService.add(any()) }
    }

    @Test
    fun `#moveToPhase if requested phase is before the next, returns failure`() = runBlocking<Unit> {
        val beneficiaryId = RangeUUID.generate()
        val phase = BeneficiaryOnboardingPhaseType.SEND_HEALTH_DECLARATION
        val beneficiaryOnboarding = TestModelFactory.buildBeneficiaryOnboarding(beneficiaryId = beneficiaryId).toModel()
        val beneficiaryOnboardingPhase = TestModelFactory.buildBeneficiaryOnboardingPhase(
            beneficiaryOnboardingId = beneficiaryOnboarding.id,
            phase = BeneficiaryOnboardingPhaseType.HEALTH_DECLARATION_APPOINTMENT_SCHEDULED
        ).toModel()
        val beneficiaryOnboardingPhases = listOf(beneficiaryOnboardingPhase)
        val transactedAt = LocalDateTime.now()

        coEvery { beneficiaryOnboardingDataService.findOne(any()) } returns beneficiaryOnboarding
        coEvery { beneficiaryOnboardingPhaseDataService.find(any()) } returns beneficiaryOnboardingPhases

        val result = beneficiaryOnboardingService.moveToPhase(beneficiaryId, phase, transactedAt)
        assertThat(result).isFailureOfType(BeneficiaryAlreadyOnCorrectPhaseException::class)

        coVerifyOnce {
            beneficiaryOnboardingDataService.findOne(queryEq { where { this.beneficiaryId.eq(beneficiaryId) } })
        }
        coVerifyOnce {
            beneficiaryOnboardingPhaseDataService.find(queryEq {
                where { this.beneficiaryOnboardingId.eq(beneficiaryOnboarding.id) }
            })
        }
        coVerifyNone { beneficiaryOnboardingPhaseDataService.add(any()) }
    }

    @Test
    fun `#moveToPhase should produce a event with beneficiaryId as partitionKey`() = runBlocking<Unit> {
        val beneficiaryId = RangeUUID.generate()
        val phase = BeneficiaryOnboardingPhaseType.FINISHED

        coEvery { producerService.produce(any(), any()) } returns mockk()

        val result = beneficiaryOnboardingService.moveToPhaseRequest(beneficiaryId, phase)
        assertThat(result).isSuccessWithData(true)

        coVerifyOnce { producerService.produce(match { it: MoveToPhaseEvent ->
            it.payload.beneficiaryId == beneficiaryId &&
                    it.payload.requiredPhaseType == phase
        }, beneficiaryId.toString()) }
    }

    @Test
    fun `#moveToPhase should throw error if flow is UNDEFINED`() = runBlocking<Unit> {
        val beneficiaryId = RangeUUID.generate()
        val beneficiaryOnboarding = TestModelFactory.buildBeneficiaryOnboarding(
            beneficiaryId = beneficiaryId,
            flowType = BeneficiaryOnboardingFlowType.UNDEFINED).toModel()
        val beneficiaryOnboardingPhase = TestModelFactory.buildBeneficiaryOnboardingPhase(
            beneficiaryOnboardingId = beneficiaryOnboarding.id
        ).toModel()
        val beneficiaryOnboardingPhases = listOf(beneficiaryOnboardingPhase)

        coEvery { beneficiaryOnboardingDataService.findOne(any()) } returns beneficiaryOnboarding
        coEvery { beneficiaryOnboardingPhaseDataService.find(any()) } returns beneficiaryOnboardingPhases

        val result = beneficiaryOnboardingService.moveToNextPhase(beneficiaryId)
        assertThat(result).isFailureOfType(InvalidBusinessOnboardingFlowStateException::class)

        coVerifyOnce {
            beneficiaryOnboardingDataService.findOne(queryEq { where { this.beneficiaryId.eq(beneficiaryId) } })
        }
        coVerifyOnce {
            beneficiaryOnboardingPhaseDataService.find(queryEq {
                where { this.beneficiaryOnboardingId.eq(beneficiaryOnboarding.id) }
            })
        }
        coVerifyNone { beneficiaryOnboardingPhaseDataService.add(any()) }
    }

    @Test
    fun `#update throw error when update Flow Type when beneficiary already pass first phase`() = runBlocking {
        val beneficiaryId = RangeUUID.generate()
        val beneficiaryOnboardingId = RangeUUID.generate()

        val beneficiaryOnboardingPhase = TestModelFactory
            .buildBeneficiaryOnboardingPhase(
                phase = BeneficiaryOnboardingPhaseType.HEALTH_DECLARATION,
                beneficiaryOnboardingId = beneficiaryOnboardingId
            )
        val beneficiaryOnboarding = TestModelFactory
            .buildBeneficiaryOnboarding(
                id = beneficiaryOnboardingId,
                beneficiaryId = beneficiaryId,
                flowType = BeneficiaryOnboardingFlowType.NO_RISK_FLOW,
                phases = listOf(beneficiaryOnboardingPhase)
            ).toModel()
        val beneficiaryOnboardingWithNewFlowType = TestModelFactory
            .buildBeneficiaryOnboarding(
                id = beneficiaryOnboardingId,
                beneficiaryId = beneficiaryId,
                flowType = BeneficiaryOnboardingFlowType.FULL_RISK_FLOW,
                phases = listOf(beneficiaryOnboardingPhase)
            )

        coEvery { beneficiaryOnboardingDataService.findOne(any()) } returns beneficiaryOnboarding
        coEvery { beneficiaryOnboardingPhaseDataService.find(any()) } returns beneficiaryOnboarding.phases

        val beneficiaryOnboardingUpdated = beneficiaryOnboardingService.update(beneficiaryOnboardingWithNewFlowType)

        assertThat(beneficiaryOnboardingUpdated).isFailureOfType(InvalidBeneficiaryOnboardingFlowTypeUpdateException::class)

        coVerify {
            beneficiaryOnboardingDataService.findOne(queryEq {
                where { this.beneficiaryId.eq(beneficiaryOnboarding.beneficiaryId) }
            })
        }

        coVerify {
            beneficiaryOnboardingPhaseDataService.find(queryEq {
                where { this.beneficiaryOnboardingId.eq(beneficiaryOnboarding.id) }
            })
        }

    }

    @Test
    fun `#findByCurrentPhase returns onboardings in the correct phase`() = runBlocking {
        val currentPhase = BeneficiaryOnboardingPhaseType.REGISTRATION
        val beneficiaryOnboardingId = RangeUUID.generate()
        val range = IntRange(0, 19)
        val beneficiaryOnboardingPhaseReadyToOnboard = TestModelFactory.buildBeneficiaryOnboardingPhase(
            phase = BeneficiaryOnboardingPhaseType.READY_TO_ONBOARD,
            beneficiaryOnboardingId = beneficiaryOnboardingId,
            transactedAt = LocalDateTime.of(2021, 12, 1, 10, 0)
        )
        val beneficiaryOnboardingPhaseRegistration = TestModelFactory.buildBeneficiaryOnboardingPhase(
            phase = BeneficiaryOnboardingPhaseType.REGISTRATION,
            beneficiaryOnboardingId = beneficiaryOnboardingId,
            transactedAt = LocalDateTime.of(2022, 1, 1, 10, 0)
        )
        val beneficiaryOnboarding = TestModelFactory.buildBeneficiaryOnboarding(
            id = beneficiaryOnboardingId,
            phases = listOf(
                beneficiaryOnboardingPhaseReadyToOnboard,
                beneficiaryOnboardingPhaseRegistration,
            )
        ).toModel()

        coEvery { beneficiaryOnboardingPhaseDataService.find(any()) } returns
                listOf(beneficiaryOnboarding.phases[1])
        coEvery { beneficiaryOnboardingPhaseDataService.find(any()) } returns
                listOf(beneficiaryOnboarding.phases[1])
        coEvery { beneficiaryOnboardingDataService.find(any()) } returns
                listOf(beneficiaryOnboarding)
        coEvery { beneficiaryOnboardingPhaseDataService.find(any()) } returns beneficiaryOnboarding.phases

        val beneficiaries = beneficiaryOnboardingService.findByCurrentPhase(
            BeneficiaryOnboardingPhaseType.REGISTRATION, range
        )

        assertThat(beneficiaries).isSuccessWithData(listOf(beneficiaryOnboarding.toTransport()))

        coVerifyOnce { beneficiaryOnboardingPhaseDataService.find(
            queryEq { where { this.phase.eq(currentPhase) }
                .orderBy { transactedAt }.sortOrder { desc }.offset { range.first }.limit { range.count() }
            }
        ) }

        coVerifyOnce { beneficiaryOnboardingPhaseDataService.find(
            queryEq { where { this.beneficiaryOnboardingId.inList(listOf(beneficiaryOnboardingId)) } }
        ) }

        coVerifyOnce { beneficiaryOnboardingDataService.find(
            queryEq { where { this.id.inList(listOf(beneficiaryOnboarding.id)) } }
        ) }

        coVerifyOnce { beneficiaryOnboardingPhaseDataService.find(
            queryEq { where { this.beneficiaryOnboardingId.eq(beneficiaryOnboardingId) } }
        ) }
    }

    @Test
    fun `#findByCurrentPhase returns onboardings in the correct phase and flowtype`() = runBlocking {
        val currentPhase = BeneficiaryOnboardingPhaseType.REGISTRATION
        val flowtype = BeneficiaryOnboardingFlowType.PARTIAL_RISK_FLOW
        val beneficiaryOnboardingId = RangeUUID.generate()
        val range = IntRange(0, 19)
        val beneficiaryOnboardingPhaseReadyToOnboard = TestModelFactory.buildBeneficiaryOnboardingPhase(
            phase = BeneficiaryOnboardingPhaseType.READY_TO_ONBOARD,
            beneficiaryOnboardingId = beneficiaryOnboardingId,
            transactedAt = LocalDateTime.of(2021, 12, 1, 10, 0)
        )
        val beneficiaryOnboardingPhaseRegistration = TestModelFactory.buildBeneficiaryOnboardingPhase(
            phase = BeneficiaryOnboardingPhaseType.REGISTRATION,
            beneficiaryOnboardingId = beneficiaryOnboardingId,
            transactedAt = LocalDateTime.of(2022, 1, 1, 10, 0)
        )
        val beneficiaryOnboarding = TestModelFactory.buildBeneficiaryOnboarding(
            id = beneficiaryOnboardingId,
            phases = listOf(
                beneficiaryOnboardingPhaseReadyToOnboard,
                beneficiaryOnboardingPhaseRegistration,
            ),
            flowType = BeneficiaryOnboardingFlowType.PARTIAL_RISK_FLOW,
        ).toModel()

        coEvery { beneficiaryOnboardingPhaseDataService.find(any()) } returns listOf(beneficiaryOnboarding.phases[1])
        coEvery { beneficiaryOnboardingPhaseDataService.find(any()) } returns  listOf(beneficiaryOnboarding.phases[1])
        coEvery { beneficiaryOnboardingDataService.find(any()) } returns listOf(beneficiaryOnboarding)
        coEvery { beneficiaryOnboardingPhaseDataService.find(any()) } returns beneficiaryOnboarding.phases

        val beneficiaries = beneficiaryOnboardingService.findByCurrentPhase(
            BeneficiaryOnboardingPhaseType.REGISTRATION, range, flowtype
        )

        assertThat(beneficiaries).isSuccessWithData(listOf(beneficiaryOnboarding.toTransport()))

        coVerifyOnce { beneficiaryOnboardingPhaseDataService.find(
            queryEq { where { this.phase.eq(currentPhase) }
                .orderBy { transactedAt }.sortOrder { desc }.offset { range.first }.limit { range.count() }
            }
        ) }

        coVerifyOnce { beneficiaryOnboardingPhaseDataService.find(
            queryEq { where { this.beneficiaryOnboardingId.inList(listOf(beneficiaryOnboardingId)) } }
        ) }

        coVerifyOnce { beneficiaryOnboardingDataService.find(
            queryEq { where { this.id.inList(listOf(beneficiaryOnboarding.id)) } }
        ) }

        coVerifyOnce { beneficiaryOnboardingPhaseDataService.find(
            queryEq { where { this.beneficiaryOnboardingId.eq(beneficiaryOnboardingId) } }
        ) }
    }

    @Test
    fun `#findByCurrentPhase does not return onboardings when there is no phases equal to filtered phase`() =
        runBlocking {
            val currentPhase = BeneficiaryOnboardingPhaseType.HEALTH_DECLARATION
            val range = IntRange(0, 19)

            coEvery { beneficiaryOnboardingPhaseDataService.find(any()) } returns emptyList()

            val beneficiaries = beneficiaryOnboardingService.findByCurrentPhase(currentPhase, range)

            assertThat(beneficiaries).isSuccessWithData(emptyList())

            coVerifyOnce { beneficiaryOnboardingPhaseDataService.find(
                queryEq { where { this.phase.eq(currentPhase) }
                    .orderBy { transactedAt }.sortOrder { desc }.offset { range.first }.limit { range.count() }
                }
            ) }

            coVerifyNone { beneficiaryOnboardingDataService.find(any()) }
        }

    @Test
    fun `#findByCurrentPhase does not return onboardings when filtered phase is not the latest`() = runBlocking {
        val currentPhase = BeneficiaryOnboardingPhaseType.REGISTRATION
        val beneficiaryOnboardingId = RangeUUID.generate()
        val range = IntRange(0, 19)
        val beneficiaryOnboardingPhaseReadyToOnboard = TestModelFactory.buildBeneficiaryOnboardingPhase(
            phase = BeneficiaryOnboardingPhaseType.READY_TO_ONBOARD,
            beneficiaryOnboardingId = beneficiaryOnboardingId,
            transactedAt = LocalDateTime.of(2022, 1, 1, 10, 0),
        ).toModel()
        val beneficiaryOnboardingPhaseRegistration = TestModelFactory.buildBeneficiaryOnboardingPhase(
            phase = BeneficiaryOnboardingPhaseType.REGISTRATION,
            beneficiaryOnboardingId = beneficiaryOnboardingId,
            transactedAt = LocalDateTime.of(2021, 12, 1, 10, 0),
        ).toModel()

        coEvery { beneficiaryOnboardingPhaseDataService.find(any()) } returns listOf(
            beneficiaryOnboardingPhaseRegistration
        )
        coEvery { beneficiaryOnboardingPhaseDataService.find(any()) } returns listOf(
            beneficiaryOnboardingPhaseReadyToOnboard, beneficiaryOnboardingPhaseRegistration
        )

        val beneficiaries = beneficiaryOnboardingService.findByCurrentPhase(currentPhase, range)

        assertThat(beneficiaries).isSuccessWithData(emptyList())

        coVerifyOnce { beneficiaryOnboardingPhaseDataService.find(
            queryEq { where { this.phase.eq(currentPhase) }
                .orderBy { transactedAt }.sortOrder { desc }.offset { range.first }.limit { range.count() }
            }
        ) }

        coVerifyOnce { beneficiaryOnboardingPhaseDataService.find(
            queryEq { where { this.beneficiaryOnboardingId.inList(listOf(beneficiaryOnboardingId)) } }
        ) }

        coVerifyNone { beneficiaryOnboardingDataService.find(any()) }
    }

    @Test
    fun `#countByCurrentPhase returns onboardings in the correct phase`() = runBlocking {
        val currentPhase = BeneficiaryOnboardingPhaseType.REGISTRATION
        val count = 1
        coEvery { beneficiaryOnboardingPhaseDataService.count(any())} returns count

        val beneficiaries = beneficiaryOnboardingService.countByCurrentPhase(currentPhase)

        assertThat(beneficiaries).isSuccessWithData(count)

        coVerifyOnce { beneficiaryOnboardingPhaseDataService.count(queryEq {
            where {
                this.phase.eq(currentPhase)
            }
        }) }
    }

    @Test
    fun `#add - calls dataService with model and adds phase if not null`() = runBlocking {
        val addedModel = TestModelFactory.buildBeneficiaryOnboarding().toModel()
        val phase = TestModelFactory.buildBeneficiaryOnboardingPhase(beneficiaryOnboardingId = addedModel.id).toModel()
        val model = TestModelFactory.buildBeneficiaryOnboarding().toModel().withPhases(listOf(phase))

        coEvery { beneficiaryOnboardingDataService.add(any()) } returns addedModel
        coEvery { beneficiaryOnboardingPhaseDataService.add(any()) } returns phase

        val result = beneficiaryOnboardingService.add(model.toTransport())
        assertThat(result).isSuccessWithData(addedModel.withPhases(listOf(phase)).toTransport())

        coVerifyOnce { beneficiaryOnboardingDataService.add(model) }
        coVerifyNone { beneficiaryOnboardingDataService.findOne(any()) }
        coVerifyOnce { beneficiaryOnboardingPhaseDataService.add(phase) }
    }

    @Test
    fun `#add - calls dataService with model and do not add phase if null`() = runBlocking {
        val model = TestModelFactory.buildBeneficiaryOnboarding().toModel().copy(phases = emptyList())
        val addedModel = TestModelFactory.buildBeneficiaryOnboarding().toModel()

        coEvery { beneficiaryOnboardingDataService.add(any()) } returns addedModel

        val result = beneficiaryOnboardingService.add(model.toTransport())
        assertThat(result).isSuccessWithData(addedModel.toTransport())

        coVerifyOnce { beneficiaryOnboardingDataService.add(model) }
        coVerifyNone { beneficiaryOnboardingPhaseDataService.add(any()) }
    }

    @Test
    fun `#add - get created if duplicated with model and adds phase if not null`() = runBlocking {
        val phase = TestModelFactory.buildBeneficiaryOnboardingPhase().toModel()
        val model = TestModelFactory.buildBeneficiaryOnboarding().toModel().withPhases(listOf(phase))
        val addedModel = TestModelFactory.buildBeneficiaryOnboarding().toModel()
        val addedPhase = phase.copy(beneficiaryOnboardingId = addedModel.id)

        coEvery { beneficiaryOnboardingDataService.add(any()) } returns DuplicatedItemException("")
        coEvery { beneficiaryOnboardingDataService.findOne(any()) } returns addedModel
        coEvery { beneficiaryOnboardingPhaseDataService.add(any()) } returns addedPhase
        coEvery { producerService.produce(any()) } returns mockk()

        val result = beneficiaryOnboardingService.add(model.toTransport())
        assertThat(result).isSuccessWithData(addedModel.withPhases(listOf(addedPhase)).toTransport())

        coVerifyOnce { beneficiaryOnboardingDataService.add(model) }
        coVerifyOnce { beneficiaryOnboardingDataService.findOne(any()) }
        coVerifyOnce { beneficiaryOnboardingPhaseDataService.add(addedPhase) }
    }

    @Test
    fun `#getPreviousPhase - get previous phase`() = runBlocking {
        val previousPhase = TestModelFactory.buildBeneficiaryOnboardingPhase(
            phase = BeneficiaryOnboardingPhaseType.READY_TO_ONBOARD
        ).toModel()
        val phase = TestModelFactory.buildBeneficiaryOnboardingPhase(
            phase = BeneficiaryOnboardingPhaseType.SEND_HEALTH_DECLARATION
        ).toModel()

        coEvery { beneficiaryOnboardingPhaseDataService.findOne(any()) } returns previousPhase

        val result = beneficiaryOnboardingService.getPreviousPhase(phase.toTransport())
        assertThat(result).isSuccessWithData(previousPhase.toTransport())

        coVerifyOnce {
            beneficiaryOnboardingPhaseDataService.findOne(queryEq {
                where { this.beneficiaryOnboardingId.eq(phase.beneficiaryOnboardingId) and
                        this.transactedAt.lessEq(phase.transactedAt) and
                        br.com.alice.common.service.data.dsl.not(this.id.eq(phase.id))
                }.orderBy { this.transactedAt }.sortOrder { desc }
            })
        }
    }

    @Test
    fun `#addWithPhase - create phase and call add function`() = runBlocking {
        val beneficiaryId = RangeUUID.generate()
        val flowType = BeneficiaryOnboardingFlowType.FULL_RISK_FLOW
        val initialProductId = RangeUUID.generate()
        val phaseType = BeneficiaryOnboardingPhaseType.READY_TO_ONBOARD
        val transactedAt = LocalDateTime.now()

        val phase = TestModelFactory.buildBeneficiaryOnboardingPhase().toModel()
        val onboarding = TestModelFactory.buildBeneficiaryOnboarding().toModel().withPhases(listOf(phase))
        val addedPhase = phase.copy(beneficiaryOnboardingId = onboarding.id)

        every {
            BeneficiaryOnboardingLogic.createBeneficiaryOnboardingWithPhases(any(), any(), any(), any(), any())
        } returns onboarding.toTransport()
        coEvery { beneficiaryOnboardingDataService.add(any()) } returns onboarding
        coEvery { beneficiaryOnboardingPhaseDataService.add(any()) } returns addedPhase

        val result = beneficiaryOnboardingService.addWithPhase(
            beneficiaryId,
            flowType,
            initialProductId,
            phaseType,
            transactedAt
        )
        assertThat(result).isSuccessWithData(onboarding.withPhases(listOf(addedPhase)).toTransport())

        coVerifyOnce { beneficiaryOnboardingDataService.add(onboarding) }
        coVerifyNone { beneficiaryOnboardingDataService.findOne(any()) }
        coVerifyOnce { beneficiaryOnboardingPhaseDataService.add(addedPhase) }
    }

    @Test
    fun `#acceptTerms should update terms timestamp as expected`() = runBlocking {
        val beneficiaryId = RangeUUID.generate()
        val acceptedAt = LocalDateTime.now()
        val beneficiaryOnboarding = TestModelFactory.buildBeneficiaryOnboarding(
            beneficiaryId = beneficiaryId
        ).toModel()
        val updatedBeneficiaryOnboarding = beneficiaryOnboarding.copy(acceptedDataProcessingTermsAt = acceptedAt)
        val signature = TestModelFactory.buildUserSignature(signedAt = acceptedAt)
        val acceptTermsTransport = AcceptTermsTransport(
            beneficiaryId = beneficiaryId,
            signature = signature,
            terms = listOf(BeneficiaryTermType.DATA_PROCESSING_TERMS)
        )

        coEvery { beneficiaryOnboardingDataService.findOne(
            queryEq { where { this.beneficiaryId.eq(beneficiaryId) } }
        ) } returns beneficiaryOnboarding
        coEvery { beneficiaryOnboardingDataService.update(updatedBeneficiaryOnboarding) } returns updatedBeneficiaryOnboarding
        coEvery { producerService.produce(any())} returns ProducerResult(
            producedAt = LocalDateTime.now(),
            topic = BeneficiaryTermAcceptedEvent.name,
            offset = 1L,
        )

        val result = beneficiaryOnboardingService.acceptTerms(acceptTermsTransport)
        assertThat(result).isSuccessWithData(updatedBeneficiaryOnboarding.toTransport())

        coVerifyOnce { beneficiaryOnboardingDataService.findOne(queryEq { where { this.beneficiaryId.eq(beneficiaryId) } }) }
        coVerifyOnce { beneficiaryOnboardingDataService.update(updatedBeneficiaryOnboarding) }
        coVerifyOnce { producerService.produce(match { it.name == BeneficiaryTermAcceptedEvent.name }) }
    }

    @Test
    fun `resetBeneficiaryOnboardingWithRiskFlow should reset to new flowType if currentPhase is READY_TO_ONBOARD`() = runBlocking {
        val beneficiaryId = RangeUUID.generate()
        val beneficiaryOnboardingId = RangeUUID.generate()
        val beneficiaryOnboardingPhase = TestModelFactory.buildBeneficiaryOnboardingPhase(
            phase = BeneficiaryOnboardingPhaseType.READY_TO_ONBOARD,
            beneficiaryOnboardingId = beneficiaryOnboardingId
        )
        val beneficiaryOnboarding = TestModelFactory.buildBeneficiaryOnboarding(
            id = beneficiaryOnboardingId,
            flowType = BeneficiaryOnboardingFlowType.NO_RISK_FLOW,
            beneficiaryId = beneficiaryId,
            phases = listOf(beneficiaryOnboardingPhase)
        ).toModel()

        val updatedOnboarding = beneficiaryOnboarding.copy(flowType = BeneficiaryOnboardingFlowType.FULL_RISK_FLOW)

        coEvery { beneficiaryOnboardingDataService.findOne(any()) } returns beneficiaryOnboarding
        coEvery { beneficiaryOnboardingDataService.update(any()) } returns updatedOnboarding
        coEvery { beneficiaryOnboardingPhaseDataService.find(any())  } returns
                listOf(beneficiaryOnboarding.phases.first())
        coEvery { producerService.produce(any())} returns ProducerResult(
            producedAt = LocalDateTime.now(),
            topic = BeneficiaryOnboardingUpdatedEvent.name,
            offset = 1L,
        )

        val result = beneficiaryOnboardingService.resetBeneficiaryOnboardingWithFlowType(beneficiaryId, BeneficiaryOnboardingFlowType.FULL_RISK_FLOW)
        assertThat(result).isSuccessWithData(updatedOnboarding.toTransport())

        coVerifyOnce { beneficiaryOnboardingDataService.findOne(queryEq { where { this.beneficiaryId.eq(beneficiaryId) } }) }
        coVerifyOnce { beneficiaryOnboardingDataService.update(updatedOnboarding) }
        coVerifyNone { beneficiaryOnboardingPhaseDataService.add(any()) }
    }

    @Test
    fun `resetBeneficiaryOnboardingWithRiskFlow should reset to new flowType and move phase`() = runBlocking {
        val beneficiaryId = RangeUUID.generate()
        val beneficiaryOnboardingId = RangeUUID.generate()
        val beneficiaryOnboardingPhase = TestModelFactory.buildBeneficiaryOnboardingPhase(
            phase = BeneficiaryOnboardingPhaseType.HEALTH_DECLARATION,
            beneficiaryOnboardingId = beneficiaryOnboardingId
        )
        val newOnboardingPhase = TestModelFactory.buildBeneficiaryOnboardingPhase(
            phase = BeneficiaryOnboardingPhaseType.READY_TO_ONBOARD,
            beneficiaryOnboardingId = beneficiaryOnboardingId
        ).toModel()
        val beneficiaryOnboarding = TestModelFactory.buildBeneficiaryOnboarding(
            id = beneficiaryOnboardingId,
            flowType = BeneficiaryOnboardingFlowType.NO_RISK_FLOW,
            beneficiaryId = beneficiaryId,
            phases = listOf(beneficiaryOnboardingPhase)
        ).toModel()

        val updatedOnboarding = beneficiaryOnboarding.copy(flowType = BeneficiaryOnboardingFlowType.FULL_RISK_FLOW)

        coEvery { beneficiaryOnboardingDataService.findOne(any()) } returns beneficiaryOnboarding
        coEvery { beneficiaryOnboardingDataService.update(any()) } returns updatedOnboarding
        coEvery { beneficiaryOnboardingPhaseDataService.find(any())  } returns beneficiaryOnboarding.phases
        coEvery { beneficiaryOnboardingPhaseDataService.add(any())  } returns newOnboardingPhase
        coEvery { producerService.produce(any(), any())} returns ProducerResult(
            producedAt = LocalDateTime.now(),
            topic = BeneficiaryOnboardingUpdatedEvent.name,
            offset = 1L,
        )

        val result = beneficiaryOnboardingService.resetBeneficiaryOnboardingWithFlowType(beneficiaryId, BeneficiaryOnboardingFlowType.FULL_RISK_FLOW)
        assertThat(result).isSuccessWithData(updatedOnboarding.toTransport())

        coVerifyOnce { beneficiaryOnboardingDataService.findOne(queryEq { where { this.beneficiaryId.eq(beneficiaryId) } }) }
        coVerifyOnce { beneficiaryOnboardingDataService.update(updatedOnboarding) }
        coVerifyOnce { beneficiaryOnboardingPhaseDataService.add(match {it.phase == BeneficiaryOnboardingPhaseType.READY_TO_ONBOARD}) }
    }
}
