package br.com.alice.api.ops.controllers.sales_channel

import br.com.alice.common.Response
import br.com.alice.common.controllers.Controller
import br.com.alice.common.core.extensions.toLocalDate
import br.com.alice.common.core.extensions.toRangeSafeUUID
import br.com.alice.common.foldResponse
import br.com.alice.sales_channel.service.SalesAgentService
import io.ktor.http.HttpHeaders
import io.ktor.http.HttpStatusCode
import io.ktor.http.Parameters

class SalesAgentController(
    private val salesAgentService: SalesAgentService
) : Controller() {
    suspend fun index(queryParams: Parameters): Response {
        val range = parseRange(queryParams)
        val searchToken = parseFilter(queryParams, "q") ?: ""

        val results = salesAgentService.search(searchToken, range, null, null).get()
        val size = salesAgentService.searchCount(searchToken, null, null).get()

        return Response(
            HttpStatusCode.OK,
            results,
            mapOf(HttpHeaders.ContentRange to size.toString())
        )
    }

    suspend fun get(id: String): Response {
        return salesAgentService.get(id.toRangeSafeUUID()).foldResponse()
    }

    suspend fun update(id: String, request: UpdateSalesAgentRequest): Response {
        val salesAgent = salesAgentService.get(id.toRangeSafeUUID()).get()
        val updatedSalesAgent = salesAgent.copy(
            name = request.name,
            documentNumber = request.documentNumber,
            email = request.email,
            birthDate = request.birthDate.toLocalDate(),
            salesFirmId = request.salesFirmId.toRangeSafeUUID()
        )

        val result = salesAgentService.update(updatedSalesAgent)

        return result.foldResponse()
    }

    data class UpdateSalesAgentRequest(
        val name: String,
        val documentNumber: String,
        val email: String,
        val phoneNumber: String,
        val birthDate: String,
        val salesFirmId: String,
    )
}
