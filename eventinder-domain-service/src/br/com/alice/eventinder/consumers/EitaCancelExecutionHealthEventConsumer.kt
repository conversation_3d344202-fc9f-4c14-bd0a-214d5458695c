package br.com.alice.eventinder.consumers

import br.com.alice.common.extensions.then
import br.com.alice.common.extensions.thenError
import br.com.alice.common.kafka.interfaces.KafkaProducerService
import br.com.alice.common.logging.logger
import br.com.alice.eventinder.client.HealthEventsService
import br.com.alice.eventinder.events.HealthEventCanceledExecutionEvent
import br.com.alice.exec.indicator.events.EitaCancelExecutionHealthEvent

class EitaCancelExecutionHealthEventConsumer(
    private val healthEventsService: HealthEventsService,
    private val kafkaProducerService: KafkaProducerService
) : Consumer() {

    suspend fun handleEitaCancelExecutionHealthEvent(event: EitaCancelExecutionHealthEvent) =
        withSubscribersEnvironment {
            val healthEventId = event.payload

            logger.info(
                "Processing EitaCancelExecutionHealthEvent on eventinder",
                "health_event_id" to healthEventId
            )

            healthEventsService.cancelExecution(healthEventId).then {
                logger.info(
                    "HealthEventsService::cancelExecution successfully",
                    "health_event" to it
                )
                kafkaProducerService.produce(HealthEventCanceledExecutionEvent(it))
            }.thenError {
                logger.error(
                    "Error while consuming EitaCancelExecutionHealthEvent",
                    "error" to it
                )
            }

        }
}
