package br.com.alice.refund.service

import br.com.alice.common.core.PersonId
import br.com.alice.common.rfc.RemoteService
import br.com.alice.common.rfc.Service
import br.com.alice.data.layer.models.Refund
import br.com.alice.data.layer.models.RefundStep
import br.com.alice.refund.models.RefundRequest
import br.com.alice.refund.models.RefundResponse
import com.github.kittinunf.result.Result
import java.util.UUID

@RemoteService
interface RefundService : Service {
    override val namespace get() = "refund"

    override val serviceName get() = "refund_service"

    suspend fun get(id: UUID): Result<Refund, Throwable>

    suspend fun findByPersonId(personId: PersonId, offSet: Int, limit: Int): Result<List<Refund>, Throwable>

    suspend fun getRefundRequested(refundId: UUID): Result<RefundResponse, Throwable>

    suspend fun create(personId: PersonId, refundRequest: RefundRequest): Result<RefundResponse, Throwable>

    suspend fun advanceStep(
        refundId: UUID,
        financialDataId: UUID? = null,
        currentStep: RefundStep
    ): Result<RefundResponse, Throwable>

    suspend fun getIncompleteRefund(
        refundId: UUID
    ): Result<RefundResponse, Throwable>
}
