package br.com.alice.healthplan.consumers

import br.com.alice.common.RangeUUID
import br.com.alice.common.core.extensions.isNotNullOrEmpty
import br.com.alice.common.extensions.then
import br.com.alice.common.kafka.interfaces.KafkaProducerService
import br.com.alice.common.logging.logger
import br.com.alice.data.layer.models.CounterReferral
import br.com.alice.data.layer.models.Deadline
import br.com.alice.data.layer.models.FeatureNamespace
import br.com.alice.data.layer.models.HealthPlanTask
import br.com.alice.data.layer.models.HealthPlanTaskStatus
import br.com.alice.data.layer.models.HealthPlanTaskType
import br.com.alice.data.layer.models.HealthProfessional
import br.com.alice.data.layer.models.Referral
import br.com.alice.data.layer.models.SpecialistType
import br.com.alice.data.layer.models.SuggestedSpecialist
import br.com.alice.data.layer.models.TaskSourceType
import br.com.alice.data.layer.models.TaskUpdatedBy
import br.com.alice.featureconfig.core.FeatureService
import br.com.alice.healthplan.client.HealthPlanTaskService
import br.com.alice.healthplan.models.HealthPlanTaskTransport
import br.com.alice.secondary.attention.events.CounterReferralCreatedEvent
import br.com.alice.secondary.attention.events.HealthPlanTaskReferralsHandleEvent
import br.com.alice.secondary.attention.events.TaskReferralsEventHandleType
import br.com.alice.staff.client.HealthProfessionalService
import com.github.kittinunf.result.Result
import com.github.kittinunf.result.map
import com.github.kittinunf.result.success
import java.time.LocalDate
import java.util.UUID
import kotlinx.coroutines.async
import kotlinx.coroutines.coroutineScope

const val TEST_REQUEST_DUE_DATE_EXTRA_DAYS = 30

class CounterReferralConsumer(
    private val healthPlanTaskService: HealthPlanTaskService,
    private val healthProfessionalService: HealthProfessionalService,
    private val kafkaProducerService: KafkaProducerService,
) : Consumer() {

    suspend fun handleAutomaticFollowUp(event: CounterReferralCreatedEvent) =
        withSubscribersEnvironment {
            val counterReferral = event.payload.counterReferral
            logger.info(
                "CounterReferralConsumer handleAutomaticFollowUp event",
                "counter_referral" to counterReferral
            )
            if (counterReferral.appointmentId != null)
                return@withSubscribersEnvironment false.success()

            if (!canRunEvent(counterReferral))
                return@withSubscribersEnvironment false.success()

            val (healthCommunitySpecialist, healthPlanTask) = getHealthSpecialistAndHealthPlanTask(
                counterReferral.staffId,
                counterReferral.referralId!!
            )

            buildAndCreateFollowUp(
                counterReferral,
                healthCommunitySpecialist,
                healthPlanTask
            ).map { taskTransport ->
                incrementAdditionalCounterReferral(counterReferral, taskTransport)
            }
        }

    private suspend fun canRunEvent(counterReferral: CounterReferral): Boolean {
        if (!enableAutoPublishFollowUpTask()) {
            logger.info("CounterReferralConsumer#handleAutomaticFollowUp -> Feature Flag Disabled")
            return false
        }

        if (!counterReferral.hasCoordinatedFollowUp())
            return false

        if (followUpAlreadyCreated(counterReferral.referralId!!)) {
            logger.info(
                "CounterReferralConsumer#handleAutomaticFollowUp -> Ignoring",
                "counter_referral_id" to counterReferral.id
            )
            return false
        }

        return true
    }

    private suspend fun incrementAdditionalCounterReferral(
        counterReferral: CounterReferral,
        taskTransport: HealthPlanTaskTransport
    ): HealthPlanTaskTransport {
        if (counterReferral.hasExceptionFollowUp()) {
            healthPlanTaskService.get(taskTransport.id!!)
                .map { task ->
                    HealthPlanTaskReferralsHandleEvent(
                        TaskReferralsEventHandleType.INCREMENT_ADDITIONAL_COUNTER_REFERRAL,
                        task,
                        counterReferral
                    )
                }
                .then {
                    kafkaProducerService.produce(it, it.payload.healthPlanTask.id.toString())
                }
        }
        return taskTransport
    }

    private suspend fun followUpAlreadyCreated(referralId: UUID): Boolean =
        healthPlanTaskService.getByOriginTaskIdAndType(referralId, HealthPlanTaskType.REFERRAL).map {
            it.isNotEmpty()
        }.get()

    private suspend fun getHealthSpecialistAndHealthPlanTask(
        staffId: UUID,
        referralId: UUID
    ): Pair<HealthProfessional, HealthPlanTask> = coroutineScope {
        val healthCommunitySpecialistDeferred = async { healthProfessionalService.findByStaffId(staffId).get() }
        val healthPlanTaskDeferred = async { healthPlanTaskService.get(referralId).get() }
        val healthSpecialist = healthCommunitySpecialistDeferred.await()
        val healthPlanTask = healthPlanTaskDeferred.await()

        Pair(healthSpecialist, healthPlanTask)
    }

    private suspend fun buildAndCreateFollowUp(
        counterReferral: CounterReferral,
        healthProfessional: HealthProfessional,
        task: HealthPlanTask
    ): Result<HealthPlanTaskTransport, Throwable> =
        healthPlanTaskService.create(
            healthPlanTask = createFollowUpReferral(counterReferral, healthProfessional, task.specialize()),
            staffId = healthProfessional.staffId
        )

    private fun healthPlanTaskTitle(
        referral: Referral
    ) = if (referral.title?.contains(" - Retorno") == true) {
        referral.title
    } else {
        referral.title + " - Retorno"
    }

    private fun createFollowUpReferral(
        counterReferral: CounterReferral,
        healthProfessional: HealthProfessional,
        healthPlanTask: Referral
    ) = Referral(
        suggestedSpecialist = SuggestedSpecialist(
            id = counterReferral.staffId,
            name = healthProfessional.name,
            type = SpecialistType.STAFF,
        ),
        specialty = healthPlanTask.specialty,
        subSpecialty = healthPlanTask.subSpecialty,
        diagnosticHypothesis = null,
        task = HealthPlanTask(
            title = healthPlanTaskTitle(healthPlanTask),
            description = healthPlanTask.description,
            personId = healthPlanTask.personId,
            healthPlanId = healthPlanTask.healthPlanId,
            status = HealthPlanTaskStatus.ACTIVE,
            lastRequesterStaffId = healthProfessional.staffId,
            requestersStaffIds = setOf(healthProfessional.staffId),
            releasedByStaffId = healthProfessional.staffId,
            type = healthPlanTask.type,
            start = healthPlanTask.start,
            groupId = healthPlanTask.groupId,
            createdBy = TaskUpdatedBy(
                source = TaskSourceType.COUNTER_REFERRAL,
                id = counterReferral.id
            ),
            favorite = false,
            dueDate = getDueDate(counterReferral),
            deadline = Deadline(
                quantity = 1,
                date = getDueDate(counterReferral).atStartOfDay()
            ),
            originTaskId = healthPlanTask.id,
            id = RangeUUID.generate(),
            caseRecordDetails = counterReferral.caseRecordDetails
                .filter { it.caseId != null }
                .ifEmpty { healthPlanTask.caseRecordDetails }
                .distinct(),
        )
    ).generalize()

    private fun getDueDate(counterReferral: CounterReferral): LocalDate =
        if (counterReferral.testRequests.isNotNullOrEmpty()) {
            counterReferral.appointmentDate.plusDays(
                TEST_REQUEST_DUE_DATE_EXTRA_DAYS + counterReferral.followUpDays!!.toLong()
            )
        } else {
            counterReferral.followUpDays?.let { followUpDays ->
                counterReferral.appointmentDate.plusDays(followUpDays.toLong())
            } ?: counterReferral.appointmentDate.plusDays(TEST_REQUEST_DUE_DATE_EXTRA_DAYS.toLong())
        }

    private fun enableAutoPublishFollowUpTask() =
        FeatureService.get(
            FeatureNamespace.EHR,
            "enable_auto_publish_follow_up_task",
            false
        )
}
