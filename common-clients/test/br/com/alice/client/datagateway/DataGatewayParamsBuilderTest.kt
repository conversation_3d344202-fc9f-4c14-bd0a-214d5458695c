package br.com.alice.client.datagateway

import kotlin.test.Test
import kotlin.test.assertEquals

class DataGatewayParamsBuilderTest {

    @Test
    fun `#buildParams should build with all params successfully`() {
        val stringColumnName = "stringColumn"
        val stringColumnValue = "value"

        val numberColumnName = "numberColumn"
        val numberColumnValue = 1L

        val listColumnName = "listColumn"
        val listColumnValue = listOf(1, 2, 3)

        val nullColumnName = "nullColumn"

        val filters = listOf(
            DataGatewayFilter(
                column = stringColumnName,
                operator = DataGatewayFilterOperator.EQUALS,
                value = stringColumnValue
            ),
            DataGatewayFilter(
                column = numberColumnName,
                operator = DataGatewayFilterOperator.GREATER_THAN,
                value = numberColumnValue
            ),
            DataGatewayFilter(
                column = listColumnName,
                operator = DataGatewayFilterOperator.IN,
                value = listColumnValue
            ),
            DataGatewayFilter(
                column = nullColumnName,
                operator = DataGatewayFilterOperator.IS_NULL
            )
        )
        val sort = listOf(
            DataGatewaySort(
                column = stringColumnName,
                order = DataGatewaySortOrder.DESC
            )
        )
        val fields = listOf("firstColumn", "secondColumn")
        val pageSize = 10L
        val pageNumber = 1L

        val expected = listOf(
            "filter[$stringColumnName]=$stringColumnValue",
            "filter[$numberColumnName][gt]=$numberColumnValue",
            "filter[$listColumnName]=${listColumnValue.joinToString(",")}",
            "filter[$nullColumnName][is_null]=",
            "sort=-$stringColumnName",
            "fields=${fields.joinToString(",")}",
            "page[number]=$pageNumber",
            "page[size]=$pageSize"
        ).joinToString("&").let { "?$it" }

        val result = DataGatewayParamsBuilder.buildParams(
            DataGatewayFilterParams(
                filters = filters,
                sort = sort,
                fields = fields,
                pageSize = pageSize,
                pageNumber = pageNumber
            )
        )

        assertEquals(expected, result)
    }

    @Test
    fun `#buildParams should build containing all available operator filters successfully`() {
        val stringColumnName = "stringColumn"
        val stringColumnValue = "value"

        val numberColumnName = "numberColumn"
        val numberColumnValue = 1L

        val listColumnName = "listColumn"
        val listColumnValue = listOf(1, 2, 3)

        val nullColumnName = "nullColumn"

        val filters = listOf(
            DataGatewayFilter(
                column = stringColumnName,
                operator = DataGatewayFilterOperator.EQUALS,
                value = stringColumnValue
            ),
            DataGatewayFilter(
                column = stringColumnName,
                operator = DataGatewayFilterOperator.NOT_EQUALS,
                value = stringColumnValue
            ),
            DataGatewayFilter(
                column = listColumnName,
                operator = DataGatewayFilterOperator.IN,
                value = listColumnValue
            ),
            DataGatewayFilter(
                column = listColumnName,
                operator = DataGatewayFilterOperator.NOT_IN,
                value = listColumnValue
            ),
            DataGatewayFilter(
                column = stringColumnName,
                operator = DataGatewayFilterOperator.LIKE,
                value = stringColumnValue
            ),
            DataGatewayFilter(
                column = numberColumnName,
                operator = DataGatewayFilterOperator.GREATER_THAN,
                value = numberColumnValue
            ),
            DataGatewayFilter(
                column = numberColumnName,
                operator = DataGatewayFilterOperator.LESS_THAN,
                value = numberColumnValue
            ),
            DataGatewayFilter(
                column = nullColumnName,
                operator = DataGatewayFilterOperator.IS_NULL
            ),
            DataGatewayFilter(
                column = nullColumnName,
                operator = DataGatewayFilterOperator.IS_NOT_NULL
            )
        )

        val expected = listOf(
            "filter[$stringColumnName]=$stringColumnValue",
            "filter[$stringColumnName][ne]=$stringColumnValue",
            "filter[$listColumnName]=${listColumnValue.joinToString(",")}",
            "filter[$listColumnName][not_in]=${listColumnValue.joinToString(",")}",
            "filter[$stringColumnName][like]=$stringColumnValue",
            "filter[$numberColumnName][gt]=$numberColumnValue",
            "filter[$numberColumnName][lt]=$numberColumnValue",
            "filter[$nullColumnName][is_null]=",
            "filter[$nullColumnName][is_not_null]="
        ).joinToString("&").let { "?$it" }

        val result = DataGatewayParamsBuilder.buildParams(
            DataGatewayFilterParams(filters = filters)
        )

        assertEquals(expected, result)
    }


    @Test
    fun `#buildParams should build using sort params only`() {
        val stringColumnName = "stringColumn"
        val numberColumnName = "numberColumn"

        val sort = listOf(
            DataGatewaySort(
                column = stringColumnName,
                order = DataGatewaySortOrder.DESC
            ),
            DataGatewaySort(
                column = numberColumnName,
                order = DataGatewaySortOrder.ASC
            )
        )

        val expected = "?sort=-$stringColumnName,$numberColumnName"

        val result = DataGatewayParamsBuilder.buildParams(
            DataGatewayFilterParams(sort = sort)
        )

        assertEquals(expected, result)
    }

}
