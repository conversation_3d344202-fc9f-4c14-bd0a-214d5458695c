package br.com.alice.communication.crm.hubspot.b2b

import br.com.alice.common.RangeUUID
import br.com.alice.communication.crm.hubspot.b2c.client.*
import br.com.alice.communication.crm.sales.b2b.AssociationFromDealToContact
import br.com.alice.communication.crm.sales.b2b.CompanyResultV2
import br.com.alice.communication.crm.sales.b2b.CompanyV2
import br.com.alice.communication.crm.sales.b2b.ContactInformation
import br.com.alice.communication.crm.sales.b2b.ContactInformationProperties
import br.com.alice.communication.crm.sales.b2b.ContactResultV2
import br.com.alice.communication.crm.sales.b2b.ContactV2
import br.com.alice.communication.crm.sales.b2b.DealPipelineV2
import br.com.alice.communication.crm.sales.b2b.DealResultV2
import br.com.alice.communication.crm.sales.b2b.DealStageV2
import br.com.alice.communication.crm.sales.b2b.DealV2
import br.com.alice.communication.crm.sales.b2b.DealWithStage
import br.com.alice.communication.crm.sales.b2b.DealsResult
import br.com.alice.communication.crm.sales.b2b.HubspotSalesFunnelPipelineException
import io.mockk.called
import io.mockk.clearMocks
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.mockk
import kotlinx.coroutines.runBlocking
import org.assertj.core.api.Assertions.assertThat
import java.time.LocalDateTime
import kotlin.test.BeforeTest
import kotlin.test.Test
import kotlin.test.assertFailsWith

class HubspotSalesFunnelPipelineImplTest {
    private val hubspotClient = mockk<HubspotClient>()
    private val hsCrmPipe = HubspotSalesFunnelPipelineImpl(hubspotClient)

    private val contact = ContactV2(
        email = "<EMAIL>",
        nationalId = "12558814028",
        phone = "+5511982737281",
        nickname = "Luis",
        dateOfBirth = LocalDateTime.of(1980, 1, 23, 0, 0),
        utmSource = null,
        utmMedium = null,
        utmCampaignName = null,
        utmTerm = null,
        utmContent = null,
    )

    @BeforeTest
    fun setup() {
        clearMocks(hubspotClient)
    }

    @Test
    fun `#createNewContact creates contact when there is no contact on Hubspot with same email`() = runBlocking<Unit> {
        val searchRequest = HubspotSearchRequest(
            filters = listOf(
                HubspotFilter(
                    propertyName = "email",
                    value = contact.email,
                    operator = FilterOperator.EQ
                )
            )
        )
        val contactId = "12345"

        coEvery { hubspotClient.searchContact(searchRequest) } returns HubspotContactSearchResponse(
            total = 0,
            results = emptyList()
        )
        coEvery {
            hubspotClient.createContact(match {
                it.email == contact.email
            })
        } returns ContactResponse(contactId)

        val result = hsCrmPipe.createNewContact(contact)

        assertThat(result).isEqualTo(ContactResultV2(contactId))
        coVerify(exactly = 1) { hubspotClient.searchContact(searchRequest) }
        coVerify { hubspotClient.updateContact(any(), any()) wasNot called }
        coVerify(exactly = 1) { hubspotClient.createContact(match { it.email == contact.email }) }
    }

    @Test
    fun `#createNewContact updates contact when there is a contact on Hubspot with same email`() = runBlocking<Unit> {
        val searchRequest = HubspotSearchRequest(
            filters = listOf(
                HubspotFilter(
                    propertyName = "email",
                    value = contact.email,
                    operator = FilterOperator.EQ
                )
            )
        )
        val contactId = "12345"

        coEvery { hubspotClient.searchContact(searchRequest) } returns HubspotContactSearchResponse(
            total = 1,
            results = listOf(ContactResponse(contactId))
        )
        coEvery {
            hubspotClient.updateContact(contactId, match {
                it.email == contact.email
            })
        } returns ContactResponse(contactId)

        val result = hsCrmPipe.createNewContact(contact)

        assertThat(result).isEqualTo(ContactResultV2(contactId))
        coVerify(exactly = 1) { hubspotClient.searchContact(searchRequest) }
        coVerify(exactly = 1) { hubspotClient.updateContact(contactId, match { it.email == contact.email }) }
        coVerify { hubspotClient.createContact(any()) wasNot called }
    }

    @Test
    fun `#createNewContact throws HubspotContactConflictException when on conflict`() = runBlocking<Unit> {
        val searchRequest = HubspotSearchRequest(
            filters = listOf(
                HubspotFilter(
                    propertyName = "email",
                    value = contact.email,
                    operator = FilterOperator.EQ
                )
            )
        )
        val contactId = "12345"

        coEvery { hubspotClient.searchContact(searchRequest) } returns HubspotContactSearchResponse(
            total = 0,
            results = emptyList()
        )
        coEvery {
            hubspotClient.createContact(match { it.email == contact.email })
        } throws HubspotContactConflictException(
            message = "ContactV2 already exists. Existing ID: 12345",
            contactId = contactId
        )

        assertFailsWith<HubspotContactConflictException> { hsCrmPipe.createNewContact(contact) }
        coVerify(exactly = 1) { hubspotClient.searchContact(searchRequest) }
        coVerify { hubspotClient.updateContact(any(), any()) wasNot called }
        coVerify(exactly = 1) { hubspotClient.createContact(match { it.email == contact.email }) }
    }

    @Test
    fun `#associateContactToCompany should return success when no error`() = runBlocking<Unit> {
        val contactId = "123"
        val companyId = "456"
        coEvery { hubspotClient.associateContactToCompany(contactId, companyId) } returns AssociationResponse(numErrors = 0L, status = "OK")

        val result = hsCrmPipe.associateContactToCompany(contactId, companyId)

        assertThat(result.success).isEqualTo(true)
        coVerify(exactly = 1) { hubspotClient.associateContactToCompany(contactId, companyId) }
    }

    @Test
    fun `#searchContactOnHubspot should return the ContactResultV2 when it exists on hubspot`() = runBlocking<Unit> {
        val searchRequest = HubspotSearchRequest(
            filters = listOf(
                HubspotFilter(
                    propertyName = "email",
                    value = "<EMAIL>",
                    operator = FilterOperator.EQ
                )
            )
        )
        val contactId = "123"
        val contact = ContactResultV2(contactId)
        coEvery { hubspotClient.searchContact(searchRequest) } returns HubspotContactSearchResponse(
            total = 1,
            results = listOf(ContactResponse(id = contactId)),
        )
        val result = hsCrmPipe.searchContactOnHubspot("<EMAIL>")

        assertThat(result).isEqualTo(contact)
        coVerify(exactly = 1) { hubspotClient.searchContact(searchRequest) }
    }

    @Test
    fun `#searchContactOnHubspot should return the ContactResultV2 when it not exists on hubspot`() = runBlocking<Unit> {
        val searchRequest = HubspotSearchRequest(
            filters = listOf(
                HubspotFilter(
                    propertyName = "email",
                    value = "<EMAIL>",
                    operator = FilterOperator.EQ
                )
            )
        )
        coEvery { hubspotClient.searchContact(searchRequest) } returns HubspotContactSearchResponse(
            total = 0,
            results = emptyList(),
        )
        val result = hsCrmPipe.searchContactOnHubspot("<EMAIL>")

        assertThat(result).isEqualTo(null)
        coVerify(exactly = 1) { hubspotClient.searchContact(searchRequest) }
    }

    @Test
    fun `#updateCompany should return success`() = runBlocking<Unit> {
        val companyId = "456"
        val company = CompanyV2(
            name = "company name",
            cnpj = "79.167.780/0001-19",
            employeesNumber = "500+",
            city = "BH",
            leadId = RangeUUID.generate()

        )
        val hubspotCompany = HubspotCompany(
            name = company.name,
            cnpj = company.cnpj,
            numeroDeFuncionariosDaEmpresa = company.employeesNumber,
            city = company.city,
            leadId = company.leadId.toString()
        )
        coEvery { hubspotClient.updateCompany(companyId, hubspotCompany) } returns CompanyResponse(companyId)

        val result = hsCrmPipe.updateCompany(companyId, company)

        assertThat(result).isEqualTo(CompanyResultV2(companyId))
    }

    @Test
    fun `#findCompanyByCnpj should return company when it exists on hubspot`() = runBlocking<Unit> {
        val cnpj = "50888870000117"
        val searchRequest = HubspotSearchRequest(
            filters = listOf(
                HubspotFilter(propertyName = "cnpj", value = cnpj, operator = FilterOperator.EQ)
            ),
            sorts = listOf(
                HubspotSort(propertyName = "createdate", direction = SortDirection.DESCENDING)
            )
        )
        val companyId = "123"
        val company = CompanyResultV2(companyId)
        coEvery { hubspotClient.searchCompany(searchRequest) } returns HubspotCompanySearchResponse(
            total = 1,
            results = listOf(CompanyResponse(id = companyId))
        )

        val result = hsCrmPipe.findCompanyByCnpj(cnpj)

        assertThat(result).isEqualTo(company)
        coVerify(exactly = 1) { hubspotClient.searchCompany(searchRequest) }
    }

    @Test
    fun `#findCompanyByCnpj should return exception`(): Unit = runBlocking {
        val cnpj = "50888870000117"
        val searchRequest = HubspotSearchRequest(
            filters = listOf(
                HubspotFilter(propertyName = "cnpj", value = cnpj, operator = FilterOperator.EQ)
            ),
            sorts = listOf(
                HubspotSort(propertyName = "createdate", direction = SortDirection.DESCENDING)
            )
        )
        coEvery { hubspotClient.searchCompany(searchRequest) } throws HubspotException("Error")

        assertFailsWith<HubspotSalesFunnelPipelineException> {
            hsCrmPipe.findCompanyByCnpj(cnpj)
        }
        coVerify(exactly = 1) { hubspotClient.searchCompany(searchRequest) }
    }

    @Test
    fun `#getAssociationFromDealToContact should return success`() = runBlocking<Unit> {
        val dealId = "456"
        val contactIdResult = "123"
        val associationResult = HubspotGetAssociationFromDealToContactResponse(
            results = listOf(
                HubspotGetAssociationFromDealToContactResult(
                    id = contactIdResult,
                    type = "deal_contact",
                )
            ),
        )
        coEvery { hubspotClient.getAssociationFromDealToContact(dealId) } returns associationResult

        val result = hsCrmPipe.getAssociationFromDealToContact(dealId)

        assertThat(result).isEqualTo(AssociationFromDealToContact(listOf(contactIdResult)))
    }

    @Test
    fun `#getContactWithAllInformation should return success`() = runBlocking<Unit> {
        val contactId = "123"
        val filter = HubspotSearchRequest(
            filters = listOf(
                HubspotFilter(
                    propertyName = "hs_object_id",
                    value = contactId,
                    operator = FilterOperator.EQ,
                )
            )
        )
        val contactResponse = HubspotContactWithPropertiesSearchResponse(
            total = 1,
            results = listOf(
                ContactPropertiesResponse(
                    id = contactId,
                    properties = ContactProperties(email = "<EMAIL>")
                )
            ),
        )
        coEvery { hubspotClient.searchContactWithProperties(filter) } returns contactResponse

        val result = hsCrmPipe.getContactWithAllInformation(contactId)

        assertThat(result).isEqualTo(
            ContactInformationProperties(
                contacts = listOf(
                    ContactInformation(
                        email = "<EMAIL>",
                        hs_object_id = contactId,
                    )
                )
            )
        )
    }

    @Test
    fun `#getContactWithAllInformationByEmail should return success`() = runBlocking<Unit> {
        val email = "<EMAIL>"
        val filter = HubspotSearchRequest(
            filters = listOf(
                HubspotFilter(
                    propertyName = "email",
                    value = email,
                    operator = FilterOperator.EQ,
                )
            )
        )
        val contactResponse = HubspotContactWithPropertiesSearchResponse(
            total = 1,
            results = listOf(
                ContactPropertiesResponse(
                    id = "2112",
                    properties = ContactProperties(email = email)
                )
            ),
        )
        coEvery { hubspotClient.searchContactWithProperties(filter) } returns contactResponse

        val result = hsCrmPipe.getContactWithAllInformationByEmail(email)

        assertThat(result).isEqualTo(
            ContactInformation(
                email = "<EMAIL>",
                hs_object_id = "2112",
            )
        )
    }

    @Test
    fun `#findAllDealsByCnpj should return success`() = runBlocking<Unit> {
        val cnpj = "12345678901234"
        val filter = HubspotSearchRequest(
            filters = listOf(
                HubspotFilter(
                    propertyName = "cnpj_da_empresa",
                    value = cnpj,
                    operator = FilterOperator.EQ,
                )
            )
        )
        val contactResponse = HubspotDealSearchResponse(
            total = 1,
            results = listOf(
                DealWithStageResponse(
                    id = "2112",
                    properties = DealProperties("BACKGROUND_CHECK")
                ),
                DealWithStageResponse(
                    id = "2322",
                    properties = DealProperties("LOST_LEAD")
                )
            ),
        )
        coEvery { hubspotClient.searchDeal(filter) } returns contactResponse

        val result = hsCrmPipe.findAllDealsByCnpj(cnpj)

        assertThat(result).isEqualTo(
            DealsResult(
                deals = listOf(
                    DealWithStage("2112", "BACKGROUND_CHECK"),
                    DealWithStage("2322", "LOST_LEAD"),
                )
            )
        )
    }

    @Test
    fun `#getContactInformationByDealId should return success`() = runBlocking<Unit> {
        val dealId = "34342"
        val contactId = "123"
        val filter = HubspotSearchRequest(
            filters = listOf(
                HubspotFilter(
                    propertyName = "associations.deal",
                    value = dealId,
                    operator = FilterOperator.EQ,
                )
            )
        )
        val contactResponse = HubspotContactWithPropertiesSearchResponse(
            total = 1,
            results = listOf(
                ContactPropertiesResponse(
                    id = contactId,
                    properties = ContactProperties(email = "<EMAIL>")
                )
            ),
        )
        coEvery { hubspotClient.searchContactWithProperties(filter) } returns contactResponse

        val result = hsCrmPipe.getContactInformationByDealId(dealId)

        assertThat(result).isEqualTo(
            ContactInformationProperties(
                contacts = listOf(
                    ContactInformation(
                        email = "<EMAIL>",
                        hs_object_id = contactId,
                    )
                )
            )
        )
    }

    @Test
    fun `#getContactInformationByDealId should return HubspotSalesFunnelPipelineException when an error occurs`() = runBlocking<Unit> {
        val dealId = "34342"
        val filter = HubspotSearchRequest(
            filters = listOf(
                HubspotFilter(
                    propertyName = "associations.deal",
                    value = dealId,
                    operator = FilterOperator.EQ,
                )
            )
        )
        coEvery {
            hubspotClient.searchContactWithProperties(filter)
        } throws HubspotException("Error")

        assertFailsWith<HubspotSalesFunnelPipelineException> {
            hsCrmPipe.getContactInformationByDealId(dealId)
        }
    }

    @Test
    fun `updateDeal should handle null leadId and simulationId`() = runBlocking<Unit> {
        val dealId = RangeUUID.generate().toString()
        val deal = DealV2(
            email = "<EMAIL>",
            name = "Test Deal",
            stage = DealStageV2.LEAD,
            pipeline = DealPipelineV2.DEFAULT,
            declaredAge = 30,
            leadId = null,
            simulationId = null,
        )
        val hubspotDeal = HubspotDeal(
            dealEmail = deal.email,
            dealname = deal.name,
            dealstage = "268585632",
            pipeline = "73075358",
            declaredAge = deal.declaredAge,
            leadId = null,
            simulationId = null,
        )
        coEvery { hubspotClient.updateDeal(dealId, hubspotDeal) } returns DealResponse(id = "12345")

        val result = hsCrmPipe.updateDeal(dealId, deal)

        assertThat(result).isEqualTo(DealResultV2("12345"))
        coVerify(exactly = 1) { hubspotClient.updateDeal(dealId, hubspotDeal) }
    }
}
