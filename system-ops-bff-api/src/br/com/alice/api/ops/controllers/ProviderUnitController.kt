package br.com.alice.api.ops.controllers

import br.com.alice.api.ops.converters.ProviderUnitConverter
import br.com.alice.api.ops.converters.ProviderUnitResponseConverter
import br.com.alice.api.ops.models.InputList
import br.com.alice.api.ops.models.LivanceIntegrationTransport
import br.com.alice.api.ops.models.ProviderUnitRequest
import br.com.alice.api.ops.models.ProviderUnitResponse
import br.com.alice.api.ops.models.ProviderUnitWithAdditionalInfoResponse
import br.com.alice.api.ops.utils.BankInstitutions
import br.com.alice.common.Response
import br.com.alice.common.controllers.Controller
import br.com.alice.common.core.Status
import br.com.alice.common.core.extensions.toUUID
import br.com.alice.common.extensions.getOrNullIfNotFound
import br.com.alice.common.extensions.mapEach
import br.com.alice.common.extensions.then
import br.com.alice.common.foldResponse
import br.com.alice.common.logging.logger
import br.com.alice.data.layer.models.PartnerIntegrationProviderType
import br.com.alice.data.layer.models.PartnerIntegrationProviderUnit
import br.com.alice.data.layer.models.ProviderUnit
import br.com.alice.data.layer.models.StructuredAddress
import br.com.alice.provider.client.PartnerIntegrationProviderUnitService
import br.com.alice.provider.client.ProviderUnitFilter
import br.com.alice.provider.client.ProviderUnitGroupService
import br.com.alice.provider.client.ProviderUnitService
import com.github.kittinunf.result.Result
import com.github.kittinunf.result.flatMap
import com.github.kittinunf.result.map
import io.ktor.http.HttpHeaders
import io.ktor.http.HttpStatusCode
import io.ktor.http.Parameters
import java.util.UUID
import kotlinx.coroutines.async
import kotlinx.coroutines.coroutineScope

class ProviderUnitController(
    private val providerUnitService: ProviderUnitService,
    private val providerUnitGroupService: ProviderUnitGroupService,
    private val partnerIntegrationService: PartnerIntegrationProviderUnitService,
) : Controller() {

    suspend fun index(queryParams: Parameters): Response = coroutineScope {
        val range = parseRange(queryParams)
        val filterQuery = parseQuery(queryParams)
        val providersType =
            parseFilter<String>(queryParams, "type")?.split(",")?.map { ProviderUnit.Type.valueOf(it) } ?: emptyList()
        val status = parseStatus(queryParams)?.split(",")?.map { Status.valueOf(it) } ?: emptyList()
        val ids = parseIds(queryParams)?.map { it.toUUID() } ?: emptyList()

        val response =
            async { getByFilter(query = filterQuery, type = providersType, status = status, ids = ids, range = range) }
        val totalCount =
            async { countByFilter(query = filterQuery, type = providersType, status = status, ids = ids) }

        Response(HttpStatusCode.OK, response.await(), mapOf(HttpHeaders.ContentRange to totalCount.await().toString()))
    }

    suspend fun getProviderUnitGroupsTitle(): Response {
        val providerUnitGroups = providerUnitGroupService.getAll().get()

        val response = providerUnitGroups.map {
            InputList(
                it.id.toString(),
                it.title
            )
        }
        val totalCount = response.size

        return Response(HttpStatusCode.OK, response, mapOf(HttpHeaders.ContentRange to totalCount.toString()))
    }

    fun getBankInstitutions(): Response =
        Response(
            HttpStatusCode.OK,
            BankInstitutions.values().map {
                InputList(name = it.description, id = it.code)
            },
            mapOf(HttpHeaders.ContentRange to BankInstitutions.values().size.toString())
        )

    suspend fun getById(id: String) =
        providerUnitService.get(id.toUUID())
            .map { providerUnit ->
                val livance = getPartnerIntegrationLivanceData(providerUnit)

                ProviderUnitResponseConverter.convert(
                    ProviderUnitWithAdditionalInfoResponse(
                        providerUnit = providerUnit,
                        address = providerUnit.address,
                        livance = livance
                    )
                )
            }.foldResponse()

    suspend fun create(request: ProviderUnitRequest): Response =
        addProviderUnitAndLocation(request, ProviderUnitConverter::convert).foldResponse()

    suspend fun update(id: UUID, request: ProviderUnitRequest): Response =
        updateProviderAndLocation(id, request, ProviderUnitConverter::convert).foldResponse()

    private fun buildProviderUnitWithoutAddress(providerUnit: ProviderUnit): ProviderUnitWithAdditionalInfoResponse =
        ProviderUnitWithAdditionalInfoResponse(providerUnit = providerUnit)

    private suspend fun addProviderUnitAndLocation(
        request: ProviderUnitRequest,
        convert: (ProviderUnitRequest) -> ProviderUnit
    ): Result<ProviderUnitResponse, Throwable> {
        val providerUnitToAdd = convert(request)
        return providerUnitService.addWithAddress(providerUnitToAdd, buildStructuredAddress(request))
            .map { providerUnit ->
                val livance = createLivancePartnerIntegration(providerUnit, request)

                ProviderUnitResponseConverter.convert(
                    ProviderUnitWithAdditionalInfoResponse(
                        providerUnit = providerUnit,
                        address = providerUnit.address,
                        livance = livance
                    )
                )
            }.then {
                logger.info(
                    "Added ProviderUnit",
                    "current_staff_id" to currentUserIdKey(),
                    "model" to it,
                    "populatedRequest" to request
                )
            }
    }

    private suspend fun updateProviderAndLocation(
        id: UUID,
        request: ProviderUnitRequest,
        convert: (ProviderUnitRequest) -> ProviderUnit
    ): Result<ProviderUnitResponse, Throwable> {
        return providerUnitService.get(id)
            .then {
                logger.info(
                    "should updated unit",
                    "current_staff_id" to currentUserIdKey(),
                    "actual" to it,
                    "request" to request
                )
            }
            .flatMap {
                val providerUnit = convert(request)
                val unit = it.copy(
                    type = providerUnit.type,
                    name = providerUnit.name,
                    contractOrigin = providerUnit.contractOrigin,
                    providerUnitGroupId = providerUnit.providerUnitGroupId,
                    site = providerUnit.site,
                    cnpj = providerUnit.cnpj,
                    cnes = providerUnit.cnes,
                    bankCode = providerUnit.bankCode,
                    agencyNumber = providerUnit.agencyNumber,
                    accountNumber = providerUnit.accountNumber,
                    phones = providerUnit.phones,
                    workingPeriods = providerUnit.workingPeriods,
                    qualifications = providerUnit.qualifications,
                    imageUrl = providerUnit.imageUrl,
                    providerId = providerUnit.providerId,
                    clinicalStaffIds = providerUnit.clinicalStaffIds,
                    administrativeStaff = providerUnit.administrativeStaff,
                    brand = providerUnit.brand,
                    externalBrandId = providerUnit.externalBrandId,
                    medicalSpecialtyProfile = providerUnit.medicalSpecialtyProfile,
                    urlSlug = providerUnit.urlSlug,
                    status = providerUnit.status,
                    deAccreditationDate = providerUnit.deAccreditationDate,
                    showOnScheduler = providerUnit.showOnScheduler,
                    attendanceTypes = providerUnit.attendanceTypes,
                    hasHospitalHealthTeam = providerUnit.hasHospitalHealthTeam,
                    showOnApp = providerUnit.showOnApp
                )
                val address = buildStructuredAddress(request)
                providerUnitService.updateWithAddress(unit, address)
            }.then { updated ->
                logger.info(
                    "Updated ProviderUnit",
                    "current_staff_id" to currentUserIdKey(),
                    "provider_unit_id" to updated.id,
                    "request" to request,
                    "model" to updated
                )
            }.map { providerUnit ->
                val livance = updateLivancePartnerIntegration(providerUnit, request)

                ProviderUnitResponseConverter.convert(
                    ProviderUnitWithAdditionalInfoResponse(
                        providerUnit = providerUnit,
                        address = providerUnit.address,
                        livance = livance
                    )
                )
            }
    }

    private suspend fun buildStructuredAddress(
        request: ProviderUnitRequest
    ): StructuredAddress {
        if (request.verifyToStructuredAddress()) {
            logger.info(
                "Incomplete address to create structured address", "request" to request
            )
            throw IllegalArgumentException("Incomplete address to create structured address")
        }

        val address = StructuredAddress(
            street = request.street,
            number = request.number,
            complement = request.complement,
            neighborhood = request.neighborhood,
            city = request.city,
            state = request.state,
            zipcode = request.zipcode,
            latitude = request.latitude,
            longitude = request.longitude,
        )


        logger.info(
            "build ProviderUnits StructuredAddress", "current_staff_id" to currentUserIdKey(), "request" to request,
            "model" to address
        )

        return address
    }

    private suspend fun getByFilter(
        query: String?,
        type: List<ProviderUnit.Type>,
        status: List<Status>,
        ids: List<UUID>,
        range: IntRange
    ): List<ProviderUnitResponse> =
        providerUnitService.getByFilterWithRange(filter = ProviderUnitFilter(query, status, type, ids), range).mapEach {
            ProviderUnitResponseConverter.convert(buildProviderUnitWithoutAddress(it))
        }.get()

    private suspend fun countByFilter(
        query: String?,
        type: List<ProviderUnit.Type>,
        status: List<Status>,
        ids: List<UUID>,
    ): Int = providerUnitService.countByFilter(filter = ProviderUnitFilter(query, status, type, ids)).get()

    private suspend fun getPartnerIntegrationLivanceData(providerUnit: ProviderUnit) =
        partnerIntegrationService.getByProviderUnitIdAndProviderType(
            providerUnitId = providerUnit.id,
            providerType = PartnerIntegrationProviderType.LIVANCE
        )
            .map { LivanceIntegrationTransport(locationId = it.externalId) }
            .getOrNullIfNotFound()

    private suspend fun createLivancePartnerIntegration(providerUnit: ProviderUnit, request: ProviderUnitRequest) =
        request.livance?.locationId?.let { locationId ->
            partnerIntegrationService.create(
                partnerIntegration = PartnerIntegrationProviderUnit(
                    providerUnitId = providerUnit.id,
                    externalId = locationId,
                    providerType = PartnerIntegrationProviderType.LIVANCE
                )
            )
                .map { LivanceIntegrationTransport(locationId = it.externalId) }
                .get()
        }

    private suspend fun updateLivancePartnerIntegration(
        providerUnit: ProviderUnit,
        request: ProviderUnitRequest
    ): LivanceIntegrationTransport? {
        val saved = partnerIntegrationService.getByProviderUnitIdAndProviderType(
            providerUnitId = providerUnit.id,
            providerType = PartnerIntegrationProviderType.LIVANCE
        ).getOrNullIfNotFound()

        if (saved != null && request.livance?.locationId == null) {
            partnerIntegrationService.delete(saved)
            return null
        }

        return request.livance?.locationId?.let { locationId ->
            if (saved == null)
                createLivancePartnerIntegration(providerUnit, request)
            else
                partnerIntegrationService.update(saved.copy(externalId = locationId))
                    .map { LivanceIntegrationTransport(locationId = it.externalId) }
                    .get()
        }
    }
}
