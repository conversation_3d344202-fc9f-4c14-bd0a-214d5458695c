package br.com.alice.api.scheduler.controllers.v1

import br.com.alice.api.scheduler.ControllerTestHelper
import br.com.alice.api.scheduler.controllers.model.AppointmentScheduleEventTypeDateExceptionRequest
import br.com.alice.api.scheduler.controllers.model.AppointmentScheduleEventTypeDateExceptionResponse
import br.com.alice.api.scheduler.controllers.model.AppointmentScheduleEventTypeDateExceptionResponseItem
import br.com.alice.api.scheduler.controllers.model.AppointmentScheduleEventTypeRequest
import br.com.alice.api.scheduler.controllers.model.AppointmentScheduleEventTypeResponse
import br.com.alice.api.scheduler.controllers.model.AppointmentScheduleEventTypeResponseItem
import br.com.alice.api.scheduler.controllers.model.AppointmentScheduleEventTypeStaffAssociationsRequest
import br.com.alice.api.scheduler.controllers.model.AppointmentScheduleEventTypeStaffAssociationsResponse
import br.com.alice.api.scheduler.controllers.model.AppointmentScheduleEventTypeWithSpecialtiesAndProviderUnitsResponse
import br.com.alice.api.scheduler.controllers.model.AppointmentScheduleEventTypeWithSpecialtiesResponse
import br.com.alice.api.scheduler.controllers.model.CategoryResponse
import br.com.alice.api.scheduler.controllers.model.EventTypeLastUpdatedBy
import br.com.alice.api.scheduler.controllers.model.MedicalSpecialtyResponse
import br.com.alice.api.scheduler.controllers.model.MedicalSpecialtyWithType
import br.com.alice.api.scheduler.controllers.model.StaffAppointmentScheduleEventTypeAssociationsRequest
import br.com.alice.api.scheduler.converters.AppointmentScheduleEventTypeResponseConverter
import br.com.alice.api.scheduler.converters.AppointmentScheduleEventTypeWithProviderUnitsAndSpecialtiesResponseConverter
import br.com.alice.api.scheduler.converters.SimpleStaffResponseConverter
import br.com.alice.common.RangeUUID
import br.com.alice.common.convertTo
import br.com.alice.common.core.Status
import br.com.alice.common.core.extensions.atEndOfTheDay
import br.com.alice.common.core.extensions.beginningOfTheMonthWithSaoPauloTimezone
import br.com.alice.common.core.extensions.endOfTheMonthWithSaoPauloTimezone
import br.com.alice.common.core.extensions.toBrazilianDateTimeFormat
import br.com.alice.common.data.dsl.matchers.ResponseAssert.Companion.assertThat
import br.com.alice.common.helpers.bodyAsJson
import br.com.alice.common.models.Staff
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.AgeRatingType
import br.com.alice.data.layer.models.AppointmentScheduleEventTypeLocation
import br.com.alice.data.layer.models.AppointmentScheduleType
import br.com.alice.data.layer.models.EventTypeProviderUnit
import br.com.alice.data.layer.models.MedicalSpecialtyType
import br.com.alice.data.layer.models.Weekday
import br.com.alice.provider.client.MedicalSpecialtyService
import br.com.alice.schedule.client.AppointmentScheduleEventTypeDateExceptionService
import br.com.alice.schedule.client.AppointmentScheduleEventTypeService
import br.com.alice.schedule.client.AppointmentScheduleOptionService
import br.com.alice.schedule.client.EventTypeProviderUnitService
import br.com.alice.schedule.client.StaffAvailabilityService
import br.com.alice.schedule.converters.AppointmentScheduleEventTypeWithProviderUnitsConverter
import br.com.alice.schedule.model.AppointmentScheduleEventTypeLocationAvailability
import br.com.alice.schedule.model.SlotForSpecificDuration
import br.com.alice.schedule.model.StaffAvailabilityResponse
import br.com.alice.staff.client.StaffService
import com.github.kittinunf.result.success
import io.mockk.called
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.mockk
import org.assertj.core.api.Assertions.assertThat
import java.time.LocalDate
import java.time.LocalDateTime
import java.time.LocalTime
import kotlin.test.BeforeTest
import kotlin.test.Test

class AppointmentScheduleEventTypeControllerTest : ControllerTestHelper() {

    private val apiVersion = "v1"
    private val appointmentScheduleEventTypeService: AppointmentScheduleEventTypeService = mockk()
    private val appointmentScheduleOptionService: AppointmentScheduleOptionService = mockk()
    private val medicalSpecialtyService: MedicalSpecialtyService = mockk()
    private val staffAvailabilityService: StaffAvailabilityService = mockk()
    private val appointmentScheduleEventTypeDateExceptionService: AppointmentScheduleEventTypeDateExceptionService =
        mockk()
    private val eventTypeProviderUnitService: EventTypeProviderUnitService = mockk()
    private val staffService: StaffService = mockk()

    private val appointmentScheduleEventTypeController = AppointmentScheduleEventTypeController(
        appointmentScheduleEventTypeService,
        appointmentScheduleOptionService,
        medicalSpecialtyService,
        staffAvailabilityService,
        appointmentScheduleEventTypeDateExceptionService,
        eventTypeProviderUnitService,
        staffService
    )

    private val medicalSpecialty = TestModelFactory.buildMedicalSpecialty()
    private val medicalSubSpecialty = medicalSpecialty.copy(
        name = "Colocar Gesso",
        type = MedicalSpecialtyType.SUBSPECIALTY,
        parentSpecialtyId = medicalSpecialty.id,
    )

    private val appointmentScheduleEventType = TestModelFactory.buildAppointmentScheduleEventType(
        specialtyId = medicalSpecialty.id,
        subSpecialtyIds = listOf(medicalSpecialty.id),
        category = AppointmentScheduleType.TEST,
        lastUpdatedBy = staff.id,
    )
    private val appointmentScheduleEventTypeWithProviderUnit =
        AppointmentScheduleEventTypeWithProviderUnitsConverter.convert(
            appointmentScheduleEventType,
            emptyList(),
            emptyMap()
        )
    private val appointmentScheduleOption = TestModelFactory.buildAppointmentScheduleOption(
        appointmentScheduleEventTypeId = appointmentScheduleEventType.id,
        staffId = staff.id
    )
    private val validAppointmentScheduleEvenTypeLocation = AppointmentScheduleEventTypeLocationAvailability(
        providerUnitId = appointmentScheduleEventType.id,
        availableWeekDays = Weekday.values().toList(),
        availabilityStartTime = LocalTime.parse("12:00"),
        availabilityEndTime = LocalTime.parse("18:00")
    )
    private val request = AppointmentScheduleEventTypeRequest(
        title = appointmentScheduleEventType.title,
        specialtyId = appointmentScheduleEventType.specialtyId,
        subSpecialtyIds = appointmentScheduleEventType.subSpecialtyIds,
        showOnApp = appointmentScheduleEventType.showOnApp,
        category = appointmentScheduleEventType.category,
        duration = appointmentScheduleEventType.duration,
        locationType = appointmentScheduleEventType.locationType,
        internalObservation = appointmentScheduleEventType.internalObservation,
        membersRisk = null,
        groupByType = appointmentScheduleEventType.groupByType,
        locations = listOf(validAppointmentScheduleEvenTypeLocation)
    )
    private val requestToken = RangeUUID.generate().toString()
    private val appointmentScheduleEventTypeDateException =
        TestModelFactory.buildAppointmentScheduleEventTypeDateException(
            appointmentScheduleEventTypeId = appointmentScheduleEventType.id,
        )
    private val staffForAuth = staff.convertTo(Staff::class)

    @BeforeTest
    override fun setup() {
        super.setup()
        this.module.single { appointmentScheduleEventTypeController }

        coEvery {
            appointmentScheduleEventTypeService.createWithProviderUnits(
                any(),
                any()
            )
        } returns appointmentScheduleEventType.success()

        coEvery {
            appointmentScheduleOptionService.associateStaff(staff.id, appointmentScheduleEventType.id)
        } returns appointmentScheduleOption.success()
    }

    @Test
    fun `#create appointment schedule event type should return 200 OK with newly created object`() {
        val appointmentScheduleEventTypeForChildren = appointmentScheduleEventType.copy(
            category = AppointmentScheduleType.HEALTH_DECLARATION,
            forChildren = true
        )
        val expected = AppointmentScheduleEventTypeResponseConverter.convert(appointmentScheduleEventTypeForChildren)
        coEvery {
            appointmentScheduleEventTypeService.createWithProviderUnits(
                appointmentScheduleEventType = match { it.forChildren },
                eventTypeProviderUnits = any()
            )
        } returns appointmentScheduleEventTypeForChildren.success()

        authenticatedAs(requestToken, staffForAuth) {
            post(
                to = "${apiVersion}/appointment_schedule_event_type",
                body = request.copy(forChildren = true)
            ) { response ->
                assertThat(response).isOKWithData(expected)
                val body: AppointmentScheduleEventTypeResponseItem = response.bodyAsJson()
                assertThat(body.forChildren).isTrue
            }
        }

        coVerify(exactly = 1) {
            appointmentScheduleEventTypeService.createWithProviderUnits(
                match {
                    it.title == appointmentScheduleEventType.title &&
                            it.specialtyId == appointmentScheduleEventType.specialtyId &&
                            it.subSpecialtyIds == appointmentScheduleEventType.subSpecialtyIds &&
                            it.showOnApp == appointmentScheduleEventType.showOnApp &&
                            it.category == appointmentScheduleEventType.category &&
                            it.duration == appointmentScheduleEventType.duration &&
                            it.locationType == appointmentScheduleEventType.locationType &&
                            it.membersRisk.isEmpty() &&
                            it.groupByType == appointmentScheduleEventType.groupByType
                },
                match {
                    it.contains(
                        AppointmentScheduleEventTypeLocationAvailability(
                            providerUnitId = appointmentScheduleEventType.id,
                            type = AppointmentScheduleEventTypeLocation.ON_SITE,
                            duration = 30,
                            minimumTimeToScheduleBeforeAppointmentTime = 1,
                            numberOfDaysFromNowToAllowScheduling = 90,
                            availableWeekDays = Weekday.values().toList(),
                            availabilityStartTime = validAppointmentScheduleEvenTypeLocation.availabilityStartTime,
                            availabilityEndTime = validAppointmentScheduleEvenTypeLocation.availabilityEndTime
                        )
                    )
                }
            )
        }
    }

    @Test
    fun `#create appointment schedule event type should return 200 OK when create with category that allows nullable specialties`() {
        val validRequest = request.copy(
            specialtyId = null,
            subSpecialtyId = null,
            subSpecialtyIds = emptyList()
        )
        val expected = AppointmentScheduleEventTypeResponseConverter.convert(appointmentScheduleEventType)

        authenticatedAs(requestToken, staffForAuth) {
            post("${apiVersion}/appointment_schedule_event_type", validRequest) {
                assertThat(it).isOKWithData(expected)
            }
        }

        coVerify(exactly = 1) {
            appointmentScheduleEventTypeService.createWithProviderUnits(
                match {
                    it.category == AppointmentScheduleType.TEST
                            && it.specialtyId == null
                            && it.subSpecialtyIds?.isEmpty() == true
                },
                match {
                    it.contains(
                        AppointmentScheduleEventTypeLocationAvailability(
                            providerUnitId = appointmentScheduleEventType.id,
                            type = AppointmentScheduleEventTypeLocation.ON_SITE,
                            duration = 30,
                            minimumTimeToScheduleBeforeAppointmentTime = 1,
                            numberOfDaysFromNowToAllowScheduling = 90,
                            availableWeekDays = Weekday.values().toList(),
                            availabilityStartTime = validAppointmentScheduleEvenTypeLocation.availabilityStartTime,
                            availabilityEndTime = validAppointmentScheduleEvenTypeLocation.availabilityEndTime
                        )
                    )
                }
            )
        }
    }

    @Test
    fun `#create appointment schedule event type should return 400 when requests are invalid`() {
        val invalidRequests = listOf(
            request.copy(
                locations = listOf(validAppointmentScheduleEvenTypeLocation.copy(duration = 10))
            ),
            request.copy(
                locations = listOf(
                    validAppointmentScheduleEvenTypeLocation.copy(
                        minimumTimeToScheduleBeforeAppointmentTime = 0
                    )
                )
            ),
            request.copy(
                locations = listOf(validAppointmentScheduleEvenTypeLocation.copy(numberOfDaysFromNowToAllowScheduling = 0))
            ),
            request.copy(
                locations = listOf(validAppointmentScheduleEvenTypeLocation.copy(numberOfDaysFromNowToAllowScheduling = 91))
            ),
            request.copy(
                category = AppointmentScheduleType.OTHER,
                specialtyId = null,
                subSpecialtyId = null
            ),
            request.copy(
                category = AppointmentScheduleType.OTHER,
                subSpecialtyId = null,
                subSpecialtyIds = emptyList()
            ),
            request.copy(locations = emptyList())
        )

        invalidRequests.map { invalidRequest ->
            authenticatedAs(requestToken, staffForAuth) {
                post("${apiVersion}/appointment_schedule_event_type", invalidRequest) {
                    assertThat(it).isBadRequest()
                }
            }
        }
    }

    @Test
    fun `#update should update and return 200 OK with updated object`() {
        val validRequest = request.copy(isMultiProfessionalReferral = true)
        val expectedAppointmentScheduleEventType = appointmentScheduleEventType.copy(isMultiProfessionalReferral = true)
        val expected = AppointmentScheduleEventTypeResponseConverter.convert(expectedAppointmentScheduleEventType)

        coEvery {
            appointmentScheduleEventTypeService.updateWithProviderUnits(any(), any())
        } returns expectedAppointmentScheduleEventType.success()

        authenticatedAs(requestToken, staffForAuth) {
            put("${apiVersion}/appointment_schedule_event_type/${appointmentScheduleEventType.id}", validRequest) {
                assertThat(it).isOKWithData(expected)
            }
        }

        coVerify(exactly = 1) {
            appointmentScheduleEventTypeService.updateWithProviderUnits(
                match {
                    it.title == appointmentScheduleEventType.title &&
                            it.specialtyId == appointmentScheduleEventType.specialtyId &&
                            it.subSpecialtyIds == appointmentScheduleEventType.subSpecialtyIds &&
                            it.showOnApp == appointmentScheduleEventType.showOnApp &&
                            it.category == appointmentScheduleEventType.category &&
                            it.duration == appointmentScheduleEventType.duration &&
                            it.locationType == appointmentScheduleEventType.locationType &&
                            it.id == appointmentScheduleEventType.id &&
                            it.isMultiProfessionalReferral &&
                            it.membersRisk.isEmpty() &&
                            it.groupByType == appointmentScheduleEventType.groupByType
                },
                match {
                    it.contains(
                        AppointmentScheduleEventTypeLocationAvailability(
                            providerUnitId = appointmentScheduleEventType.id,
                            type = AppointmentScheduleEventTypeLocation.ON_SITE,
                            duration = 30,
                            minimumTimeToScheduleBeforeAppointmentTime = 1,
                            numberOfDaysFromNowToAllowScheduling = 90,
                            availableWeekDays = Weekday.values().toList(),
                            availabilityStartTime = validAppointmentScheduleEvenTypeLocation.availabilityStartTime,
                            availabilityEndTime = validAppointmentScheduleEvenTypeLocation.availabilityEndTime
                        )
                    )
                }
            )
        }
    }

    @Test
    fun `#update should return 400 when creating with empty title`() {
        val invalidRequest = request.copy(title = "       ")

        authenticatedAs(requestToken, staffForAuth) {
            put("${apiVersion}/appointment_schedule_event_type/${appointmentScheduleEventType.id}", invalidRequest) {
                assertThat(it).isBadRequest()
            }
        }

        coVerify { appointmentScheduleEventTypeService wasNot called }
    }

    @Test
    fun `#get appointment schedule event types with params should return 200 OK with objects`() {
        val id = RangeUUID.generate()

        coEvery { appointmentScheduleEventTypeService.get(id = id) } returns appointmentScheduleEventType.success()

        val specialtyIds = listOfNotNull(
            (appointmentScheduleEventType.subSpecialtyIds ?: emptyList())
                .plus(appointmentScheduleEventType.specialtyId)
        ).flatten()

        val specialties = listOf(medicalSpecialty, medicalSubSpecialty)
        val statuses = listOf(Status.ACTIVE, Status.INACTIVE)

        val specialtiesWithType = listOf(
            MedicalSpecialtyWithType(
                id = medicalSpecialty.id,
                name = medicalSpecialty.name,
                type = medicalSpecialty.type
            ),
            MedicalSpecialtyWithType(
                id = medicalSubSpecialty.id,
                name = medicalSubSpecialty.name,
                type = medicalSubSpecialty.type
            )
        )

        coEvery {
            medicalSpecialtyService.getByIds(specialtyIds.filterNotNull())
        } returns specialties.success()

        coEvery {
            appointmentScheduleEventTypeService.query(
                searchQuery = "asd",
                status = statuses,
                appointmentScheduleType = AppointmentScheduleType.COMMUNITY,
                range = IntRange(1, 20)
            )
        } returns listOf(appointmentScheduleEventTypeWithProviderUnit).success()

        coEvery {
            appointmentScheduleEventTypeService.count(
                searchQuery = "asd",
                status = statuses,
                appointmentScheduleType = AppointmentScheduleType.COMMUNITY,
            )
        } returns 10.success()

        val expected = AppointmentScheduleEventTypeResponse(
            items = listOf(
                AppointmentScheduleEventTypeWithProviderUnitsAndSpecialtiesResponseConverter.convert(
                    source = appointmentScheduleEventTypeWithProviderUnit,
                    specialties = specialtiesWithType
                )
            ), total = 10
        )

        authenticatedAs(requestToken, staffForAuth) {
            get("${apiVersion}/appointment_schedule_event_type?range=[1,20]&filter={\"q\":\"asd\"}&type=COMMUNITY&status=[\"ACTIVE\",\"INACTIVE\"]") {
                assertThat(it).isOKWithData(expected)
            }
        }
    }

    @Test
    fun `#getAppointmentScheduleCategories should return categories`() {
        val expected = AppointmentScheduleType.values().sortedBy { it.description }.map {
            CategoryResponse(
                description = it.description,
                value = it.name
            )
        }
        authenticatedAs(requestToken, staffForAuth) {
            get("${apiVersion}/appointment_schedule_categories") {
                assertThat(it).isOKWithData(expected)
            }
        }
    }

    @Test
    fun `#getMedicalSpecialties should return medical specialties`() {
        val expected = listOf(
            MedicalSpecialtyResponse(
                name = medicalSpecialty.name,
                id = medicalSpecialty.id,
            )
        )

        coEvery {
            medicalSpecialtyService.getByName("asd", MedicalSpecialtyType.SPECIALTY)
        } returns listOf(medicalSpecialty).success()

        authenticatedAs(requestToken, staffForAuth) {
            get("${apiVersion}/medical_specialties?filter={\"q\":\"asd\"}") {
                assertThat(it).isOKWithData(expected)
            }
        }
    }

    @Test
    fun `#getMedicalSubSpecialties should return sub specialties for a given specialty`() {
        val parentId = RangeUUID.generate()

        val expected = listOf(
            MedicalSpecialtyResponse(
                name = medicalSpecialty.name,
                id = medicalSpecialty.id,
            )
        )

        coEvery {
            medicalSpecialtyService.getByParentId(parentId)
        } returns listOf(medicalSpecialty).success()

        authenticatedAs(requestToken, staffForAuth) {
            get("${apiVersion}/medical_specialties/${parentId}/sub_specialties") {
                assertThat(it).isOKWithData(expected)
            }
        }
    }

    @Test
    fun `#getWithSpecialties should return appointment schedule event type given both medical specialties`() {
        val id = RangeUUID.generate()
        val appointmentScheduleEventTypeForChildren =
            appointmentScheduleEventTypeWithProviderUnit.copy(forChildren = true)

        coEvery {
            appointmentScheduleEventTypeService.getWithProviderUnits(id = id)
        } returns appointmentScheduleEventTypeForChildren.success()

        val specialtyIds = listOfNotNull(
            (appointmentScheduleEventType.subSpecialtyIds ?: emptyList())
                .plus(appointmentScheduleEventType.specialtyId)
        ).flatten().filterNotNull()

        val specialties = listOf(medicalSpecialty, medicalSubSpecialty)

        val specialtiesWithType = listOf(
            MedicalSpecialtyWithType(
                id = medicalSpecialty.id,
                name = medicalSpecialty.name,
                type = medicalSpecialty.type
            ),
            MedicalSpecialtyWithType(
                id = medicalSubSpecialty.id,
                name = medicalSubSpecialty.name,
                type = medicalSubSpecialty.type
            )
        )

        coEvery {
            medicalSpecialtyService.getByIds(specialtyIds)
        } returns specialties.success()

        coEvery {
            staffService.get(staff.id)
        } returns staff.success()

        val expected = AppointmentScheduleEventTypeWithProviderUnitsAndSpecialtiesResponseConverter.convert(
            source = appointmentScheduleEventTypeForChildren,
            specialties = specialtiesWithType,
            lastUpdatedByStaff = staff,
        )

        authenticatedAs(requestToken, staffForAuth) {
            get("${apiVersion}/appointment_schedule_event_type/${id}") { response ->
                assertThat(response).isOKWithData(expected)
                val responseAsJson: AppointmentScheduleEventTypeWithSpecialtiesResponse = response.bodyAsJson()
                assertThat(responseAsJson.forChildren).isTrue
            }
        }
    }

    @Test
    fun `#getWithSpecialties should return appointment schedule event type given one medical specialties`() {
        val id = RangeUUID.generate()

        val appointmentScheduleEventTypeWithOneSpecialty = appointmentScheduleEventTypeWithProviderUnit.copy(
            id = id,
            subSpecialtyId = null,
            subSpecialtyIds = emptyList(),
            locations = listOf(
                AppointmentScheduleEventTypeLocationAvailability(
                    providerUnitId = null,
                    type = AppointmentScheduleEventTypeLocation.REMOTE
                ),
                AppointmentScheduleEventTypeLocationAvailability(
                    providerUnitId = id,
                    type = AppointmentScheduleEventTypeLocation.ON_SITE
                )
            )
        )

        coEvery {
            appointmentScheduleEventTypeService.getWithProviderUnits(id = id)
        } returns appointmentScheduleEventTypeWithOneSpecialty.success()

        val specialtyIds = listOfNotNull(
            appointmentScheduleEventTypeWithOneSpecialty.specialtyId,
            appointmentScheduleEventTypeWithOneSpecialty.subSpecialtyId
        )

        val specialties = listOf(medicalSpecialty)

        val specialtiesWithType = listOf(
            MedicalSpecialtyWithType(
                id = medicalSpecialty.id,
                name = medicalSpecialty.name,
                type = medicalSpecialty.type
            )
        )

        coEvery { medicalSpecialtyService.getByIds(specialtyIds) } returns specialties.success()

        coEvery {
            staffService.get(staff.id)
        } returns staff.success()

        val expected = AppointmentScheduleEventTypeWithSpecialtiesAndProviderUnitsResponse(
            id = appointmentScheduleEventTypeWithOneSpecialty.id,
            title = appointmentScheduleEventTypeWithOneSpecialty.title,
            specialty = specialtiesWithType.find { it.type == MedicalSpecialtyType.SPECIALTY },
            subSpecialties = specialtiesWithType.filter { it.type == MedicalSpecialtyType.SUBSPECIALTY },
            showOnApp = appointmentScheduleEventTypeWithOneSpecialty.showOnApp,
            category = appointmentScheduleEventTypeWithOneSpecialty.category,
            duration = appointmentScheduleEventTypeWithOneSpecialty.duration,
            locationType = AppointmentScheduleEventTypeLocation.HYBRID,
            minimumTimeToScheduleBeforeAppointmentTime = appointmentScheduleEventTypeWithOneSpecialty.minimumTimeToScheduleBeforeAppointmentTime,
            isMultiProfessionalReferral = appointmentScheduleEventTypeWithOneSpecialty.isMultiProfessionalReferral,
            numberOfDaysFromNowToAllowScheduling = appointmentScheduleEventTypeWithOneSpecialty.numberOfDaysFromNowToAllowScheduling,
            status = appointmentScheduleEventTypeWithOneSpecialty.status,
            description = appointmentScheduleEventTypeWithOneSpecialty.description,
            internalObservation = appointmentScheduleEventTypeWithOneSpecialty.internalObservation,
            providerUnitIds = appointmentScheduleEventTypeWithOneSpecialty.providerUnitIds,
            membersRisk = appointmentScheduleEventTypeWithOneSpecialty.membersRisk,
            groupByType = appointmentScheduleEventTypeWithOneSpecialty.groupByType,
            availableWeekDays = appointmentScheduleEventTypeWithOneSpecialty.availableWeekDays,
            locations = appointmentScheduleEventTypeWithOneSpecialty.locations,
            updatedAt = appointmentScheduleEventTypeWithOneSpecialty.updatedAt.toBrazilianDateTimeFormat(),
            lastUpdatedByStaff = EventTypeLastUpdatedBy(
                id = staff.id,
                name = staff.fullName,
            ),
            forChildren = false,
            ageRating = AgeRatingType.BOTH
        )

        authenticatedAs(requestToken, staffForAuth) {
            get("${apiVersion}/appointment_schedule_event_type/${id}") {
                assertThat(it).isOKWithData(expected)
            }
        }
    }

    @Test
    fun `#getWithSpecialties should return appointment schedule event type without medical specialties`() {
        val id = RangeUUID.generate()

        val appointmentScheduleEventTypeWithoutSpecialty = appointmentScheduleEventTypeWithProviderUnit.copy(
            id = id,
            specialtyId = null,
            subSpecialtyId = null,
            subSpecialtyIds = emptyList(),
        )

        coEvery {
            appointmentScheduleEventTypeService.getWithProviderUnits(id = id)
        } returns appointmentScheduleEventTypeWithoutSpecialty.success()

        coEvery {
            staffService.get(staff.id)
        } returns staff.success()

        val expected = AppointmentScheduleEventTypeWithProviderUnitsAndSpecialtiesResponseConverter.convert(
            source = appointmentScheduleEventTypeWithoutSpecialty,
            specialties = listOf(),
            lastUpdatedByStaff = staff,
        )

        authenticatedAs(requestToken, staffForAuth) {
            get("${apiVersion}/appointment_schedule_event_type/${id}") {
                assertThat(it).isOKWithData(expected)
            }
        }

        coVerify { medicalSpecialtyService wasNot called }
    }

    @Test
    fun `#getStaffsAssociatedToEventType should return staffs associated to event type`() {
        coEvery {
            appointmentScheduleOptionService.getStaffsAssociatedToEvents(appointmentScheduleEventType.id, null)
        } returns listOf(staff).success()

        val expected = listOf(staff).map { SimpleStaffResponseConverter.convert(it) }

        authenticatedAs(requestToken, staffForAuth) {
            get("${apiVersion}/appointment_schedule_event_type/${appointmentScheduleEventType.id}/staffs") {
                assertThat(it).isOKWithData(expected)
            }
        }
    }

    @Test
    fun `#getEventTypesAssociatedToStaff should return event types associated to staff`() {
        coEvery {
            appointmentScheduleOptionService.getEventsWithProviderUnits(staff.id)
        } returns listOf(appointmentScheduleEventTypeWithProviderUnit).success()

        val expected = listOf(appointmentScheduleEventTypeWithProviderUnit).map {
            AppointmentScheduleEventTypeWithProviderUnitsAndSpecialtiesResponseConverter.convert(it)
        }

        authenticatedAs(requestToken, staffForAuth) {
            get("${apiVersion}/staff/${staff.id}/schedule_preference/appointment_schedule_event_types") {
                assertThat(it).isOKWithData(expected)
            }
        }
    }

    @Test
    fun `#associateStaffs should associate staffs to event type`() {
        val request = AppointmentScheduleEventTypeStaffAssociationsRequest(
            staffIds = listOf(staff.id)
        )
        val expectedResponse = AppointmentScheduleEventTypeStaffAssociationsResponse(
            associatedStaffIds = listOf(staff.id),
            notAssociatedStaffIds = emptyList(),
        )

        coEvery {
            appointmentScheduleEventTypeService.get(appointmentScheduleEventType.id)
        } returns appointmentScheduleEventType.success()

        coEvery {
            appointmentScheduleEventTypeService.update(
                appointmentScheduleEventType.copy(
                    lastUpdatedBy = staff.id
                )
            )
        } returns appointmentScheduleEventType.success()

        authenticatedAs(requestToken, staffForAuth) {
            post("${apiVersion}/appointment_schedule_event_type/${appointmentScheduleEventType.id}/staffs", request) {
                assertThat(it).isOKWithData(expectedResponse)
            }
        }
    }

    @Test
    fun `#associateStaffToEventTypes should associate staff to event types`() {
        val request = StaffAppointmentScheduleEventTypeAssociationsRequest(
            appointmentScheduleEventTypeIds = listOf(appointmentScheduleEventType.id)
        )
        val expectedResponse = AppointmentScheduleEventTypeStaffAssociationsResponse(
            associatedStaffIds = listOf(staff.id),
            notAssociatedStaffIds = emptyList(),
        )

        coEvery {
            appointmentScheduleEventTypeService.get(appointmentScheduleEventType.id)
        } returns appointmentScheduleEventType.success()

        coEvery {
            appointmentScheduleEventTypeService.update(
                appointmentScheduleEventType.copy(
                    lastUpdatedBy = staff.id
                )
            )
        } returns appointmentScheduleEventType.success()

        authenticatedAs(requestToken, staffForAuth) {
            post("${apiVersion}/staff/${staff.id}/schedule_preference/appointment_schedule_event_types", request) {
                assertThat(it).isOKWithData(expectedResponse)
            }
        }
    }

    @Test
    fun `#getAvailabilityForAppointmentScheduleEventTypeForPeriod get staff availability for period`() {
        val localDateTimeNow = LocalDateTime.now()
        val currentDate = beginningOfTheMonthWithSaoPauloTimezone(localDateTimeNow)
        val toDate = endOfTheMonthWithSaoPauloTimezone(localDateTimeNow)

        val slots = listOf(
            SlotForSpecificDuration(
                startTime = LocalDateTime.now(),
                endTime = LocalDateTime.now().plusMinutes(30),
                durationInMinutes = 30,
            )
        )

        val expectedResponse = StaffAvailabilityResponse(slots)

        coEvery {
            appointmentScheduleEventTypeService.get(appointmentScheduleEventType.id)
        } returns appointmentScheduleEventType.success()
        coEvery {
            staffAvailabilityService.getForEventTypeAndPeriod(
                currentDate,
                toDate,
                appointmentScheduleEventType.id,
                appointmentScheduleEventType
            )
        } returns slots.success()
        coEvery {
            eventTypeProviderUnitService.getForEventType(appointmentScheduleEventType.id)
        } returns emptyList<EventTypeProviderUnit>().success()

        authenticatedAs(requestToken, staffForAuth) {
            get("${apiVersion}/appointment_schedule_event_type/${appointmentScheduleEventType.id}/availability?toDate=$toDate") { response ->
                val responseAsJson: StaffAvailabilityResponse = response.bodyAsJson()
                assertThat(responseAsJson).isEqualTo(expectedResponse)
            }
        }
    }

    @Test
    fun `#getAvailabilityForAppointmentScheduleEventTypeForPeriod for single staff`() {
        val localDateTimeNow = LocalDateTime.now()
        val currentDate = beginningOfTheMonthWithSaoPauloTimezone(localDateTimeNow)
        val toDate = endOfTheMonthWithSaoPauloTimezone(localDateTimeNow)

        val slots = listOf(
            SlotForSpecificDuration(
                startTime = LocalDateTime.now(),
                endTime = LocalDateTime.now().plusMinutes(30),
                durationInMinutes = 30,
                staffId = staff.id
            )
        )

        val expectedResponse = StaffAvailabilityResponse(
            slots
        )

        coEvery {
            appointmentScheduleEventTypeService.get(appointmentScheduleEventType.id)
        } returns appointmentScheduleEventType.success()
        coEvery {
            staffAvailabilityService.getForStaffAndEventTypeAndPeriod(
                staff.id,
                startDateTime = currentDate.atStartOfDay(),
                endDateTime = toDate.atEndOfTheDay(),
                appointmentScheduleEventType.id,
                appointmentScheduleEventType
            )
        } returns slots.success()
        coEvery {
            eventTypeProviderUnitService.getForEventType(appointmentScheduleEventType.id)
        } returns emptyList<EventTypeProviderUnit>().success()

        authenticatedAs(requestToken, staffForAuth) {
            get("${apiVersion}/appointment_schedule_event_type/${appointmentScheduleEventType.id}/availability?toDate=$toDate&staffId=${staff.id}") { response ->
                val responseAsJson: StaffAvailabilityResponse = response.bodyAsJson()
                assertThat(responseAsJson).isEqualTo(expectedResponse)
            }
        }
    }

    @Test
    fun `#getAvailabilityForAppointmentScheduleEventTypeForPeriod should get for digital slots when providerUnitId is digital`() {
        val localDateTimeNow = LocalDateTime.now()
        val currentDate = beginningOfTheMonthWithSaoPauloTimezone(localDateTimeNow)
        val toDate = endOfTheMonthWithSaoPauloTimezone(localDateTimeNow)

        val slots = listOf(
            SlotForSpecificDuration(
                startTime = LocalDateTime.now(),
                endTime = LocalDateTime.now().plusMinutes(30),
                durationInMinutes = 30,
                staffId = staff.id
            )
        )

        val expectedResponse = StaffAvailabilityResponse(slots)

        coEvery {
            appointmentScheduleEventTypeService.get(appointmentScheduleEventType.id)
        } returns appointmentScheduleEventType.success()
        coEvery {
            staffAvailabilityService.getForStaffAndEventTypeAndPeriod(
                staffId = staff.id,
                startDateTime = currentDate.atStartOfDay(),
                endDateTime = toDate.atEndOfTheDay(),
                appointmentScheduleEventTypeId = appointmentScheduleEventType.id,
                appointmentScheduleEventType = appointmentScheduleEventType,
                providerUnitId = null,
                byPassSchedulingConstraints = true,
                filterWeekday = null
            )
        } returns slots.success()

        authenticatedAs(requestToken, staffForAuth) {
            get("${apiVersion}/appointment_schedule_event_type/${appointmentScheduleEventType.id}/availability?toDate=$toDate&staffId=${staff.id}&providerUnitId=digital&byPassSchedulingConstraints=true") { response ->
                val responseAsJson: StaffAvailabilityResponse = response.bodyAsJson()
                assertThat(responseAsJson).isEqualTo(expectedResponse)
            }
        }

        coVerify { eventTypeProviderUnitService wasNot called }
    }

    @Test
    fun `#createDateException should create event type date exception`() {
        coEvery {
            appointmentScheduleEventTypeDateExceptionService.add(any())
        } returns appointmentScheduleEventTypeDateException.success()

        val request = AppointmentScheduleEventTypeDateExceptionRequest(exceptionDate = LocalDate.now())
        val expectedResponse = appointmentScheduleEventTypeDateException

        authenticatedAs(requestToken, staffForAuth) {
            post(
                "${apiVersion}/appointment_schedule_event_type/${appointmentScheduleEventType.id}/date_exceptions",
                request
            ) {
                assertThat(it).isOKWithData(expectedResponse)
            }
        }
    }

    @Test
    fun `#deleteDateException should delete date exception`() {
        val dateExceptionId = RangeUUID.generate()

        coEvery {
            appointmentScheduleEventTypeDateExceptionService.delete(dateExceptionId)
        } returns appointmentScheduleEventTypeDateException.success()

        val expectedResponse = appointmentScheduleEventTypeDateException

        authenticatedAs(requestToken, staffForAuth) {
            delete("${apiVersion}/appointment_schedule_event_type/date_exceptions/${dateExceptionId}") {
                assertThat(it).isOKWithData(expectedResponse)
            }
        }
    }

    @Test
    fun `#getDateExceptions should get event type date exceptions`() {
        coEvery {
            appointmentScheduleEventTypeDateExceptionService.getForEventType(
                id = appointmentScheduleEventType.id,
            )
        } returns listOf(appointmentScheduleEventTypeDateException).success()

        val expectedResponse = AppointmentScheduleEventTypeDateExceptionResponse(
            appointmentScheduleEventTypeDateExceptions = listOf(
                appointmentScheduleEventTypeDateException.convertTo(
                    AppointmentScheduleEventTypeDateExceptionResponseItem::class
                )
            )
        )

        authenticatedAs(requestToken, staffForAuth) {
            get("${apiVersion}/appointment_schedule_event_type/${appointmentScheduleEventType.id}/date_exceptions") {
                assertThat(it).isOKWithData(expectedResponse)
            }
        }
    }

}
