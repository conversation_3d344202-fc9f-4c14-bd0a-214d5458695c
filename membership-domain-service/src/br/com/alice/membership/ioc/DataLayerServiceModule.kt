package br.com.alice.membership.ioc

import br.com.alice.common.client.DefaultHttpClient
import br.com.alice.common.rfc.Invoker
import br.com.alice.common.service.data.layer.DataLayerClientConfiguration
import br.com.alice.data.layer.services.*
import org.koin.dsl.module

val DataLayerServiceModule = module(createdAtStart = true) {

    // Clients
    single<Invoker> { DataLayerClientConfiguration.build(DefaultHttpClient(timeoutInMillis = 10_000)) }

    // Services
    single<DeviceModelDataService> { DeviceModelDataServiceClient(get()) }
    single<PersonLoginModelDataService> { PersonLoginModelDataServiceClient(get()) }
    single<OnboardingContractModelDataService> { OnboardingContractModelDataServiceClient(get()) }
    single<PersonOnboardingModelDataService> { PersonOnboardingModelDataServiceClient(get()) }
    single<PersonPreferencesModelDataService> { PersonPreferencesModelDataServiceClient(get()) }
    single<ProductOrderModelDataService> { ProductOrderModelDataServiceClient(get()) }
    single<HealthDeclarationDataService> { HealthDeclarationDataServiceClient(get()) }
    single<PersonRegistrationModelDataService> { PersonRegistrationModelDataServiceClient(get()) }
    single<HealthGoalModelDataService> { HealthGoalModelDataServiceClient(get()) }
    single<PersonHealthcareTeamRecommendationModelDataService> { PersonHealthcareTeamRecommendationModelDataServiceClient(get()) }
    single<PersonHealthGoalModelDataService> { PersonHealthGoalModelDataServiceClient(get()) }
    single<PersonTaskModelDataService> { PersonTaskModelDataServiceClient(get()) }
    single<PromoCodeModelDataService> { PromoCodeModelDataServiceClient(get()) }
    single<ShoppingCartModelDataService> { ShoppingCartModelDataServiceClient(get()) }
    single<UpdateAppRuleModelDataService> { UpdateAppRuleModelDataServiceClient(get()) }
    single<FaqGroupModelDataService> { FaqGroupModelDataServiceClient(get()) }
    single<FaqFeedbackModelDataService> { FaqFeedbackModelDataServiceClient(get()) }
    single<FaqContentModelDataService> { FaqContentModelDataServiceClient(get()) }
    single<EmailCommunicationModelDataService> { EmailCommunicationModelDataServiceClient(get()) }
    single<PersonDocumentsUploadModelDataService> { PersonDocumentsUploadModelDataServiceClient(get()) }
    single<PersonBenefitModelDataService> { PersonBenefitModelDataServiceClient(get()) }
    single<MemberContractTermModelDataService> { MemberContractTermModelDataServiceClient(get()) }
    single<HealthPlanTaskStatusHistoryModelDataService> { HealthPlanTaskStatusHistoryModelDataServiceClient(get()) }
    single<UpdatedPersonContactInfoTempModelDataService> { UpdatedPersonContactInfoTempModelDataServiceClient(get()) }
    single<MemberAccreditedNetworkTrackerModelDataService> { MemberAccreditedNetworkTrackerModelDataServiceClient(get()) }
    single<MemberProductChangeScheduleModelDataService> { MemberProductChangeScheduleModelDataServiceClient(get()) }
    single<MemberLifeCycleEventsModelDataService> { MemberLifeCycleEventsModelDataServiceClient(get()) }
}
