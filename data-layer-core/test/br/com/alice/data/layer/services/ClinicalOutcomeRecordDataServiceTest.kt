package br.com.alice.data.layer.services

import br.com.alice.common.RangeUUID
import br.com.alice.common.core.PersonId
import br.com.alice.common.data.dsl.matchers.ResultAssert.Companion.assertThat
import br.com.alice.data.layer.helpers.DataServiceTestHelper
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.helpers.TestTableFactory
import br.com.alice.data.layer.models.ClinicalOutcomeRecord
import br.com.alice.data.layer.models.OutcomeConf
import br.com.alice.data.layer.repositories.JdbiRepository
import br.com.alice.data.layer.tables.OutcomeConfTable
import br.com.alice.data.layer.tables.PersonTable
import kotlinx.coroutines.runBlocking
import org.assertj.core.api.Assertions
import kotlin.test.Test

class ClinicalOutcomeRecordDataServiceTest : DataServiceTestHelper() {

    private val personRepo = JdbiRepository(mainJdbi, PersonTable::class)
    private val personTokenService = PersonTokenServiceImpl(tokenJdbi) as PersonTokenService
    private val oucomeConfRepo = JdbiRepository(mainJdbi, OutcomeConfTable::class)
    private val clinicalOutcomeRecordDataService = ClinicalOutcomeRecordDataServiceClient(httpInvoker)

    @Test
    fun `#add and get new entity`() = runBlocking {
        val personId = PersonId()
        val personToken = personTokenService.createForPersonId(personId).get()
        val personEntity = TestTableFactory.buildPersonTable(personToken.personPiiToken)
        personRepo.add(personEntity)

        val outcomeConf = OutcomeConfTable(
            type = OutcomeConf.OutcomeType.CLINICAL,
            key = "MSQ",
            description = "MSQ",
        )
        oucomeConfRepo.add(outcomeConf)

        val referencedLink1 = ClinicalOutcomeRecord.ReferencedLink(
            id = RangeUUID.generate(),
            model = ClinicalOutcomeRecord.ReferenceLinkModel.HEALTH_FORM_ANSWER_GROUP
        )
        val referencedLink2 = ClinicalOutcomeRecord.ReferencedLink(
            id = RangeUUID.generate(),
            model = ClinicalOutcomeRecord.ReferenceLinkModel.CLINICAL_OUTCOME_RECORD
        )
        val record = TestModelFactory.buildClinicalOutcomeRecord(
            personId = personToken.personId,
            referencedLinks = listOf(
                referencedLink1,
                referencedLink2
            ),
            addedBy = ClinicalOutcomeRecord.AddedBy(
                id = personId.id, type = ClinicalOutcomeRecord.AddedByType.MEMBER
            ),
            outcomeConfId = outcomeConf.id,
        ).sanitize()

        val record2 = TestModelFactory.buildClinicalOutcomeRecord(
            personId = personToken.personId,
            referencedLinks = listOf(
                referencedLink2
            ),
            addedBy = ClinicalOutcomeRecord.AddedBy(
                id = personId.id, type = ClinicalOutcomeRecord.AddedByType.STAFF
            ),
            outcomeConfId = outcomeConf.id,
        ).sanitize()

        val first = clinicalOutcomeRecordDataService.add(record).get()
        Assertions.assertThat(first.addedBy.id).isNull()

        val retrieveByOutcomeConfId = clinicalOutcomeRecordDataService.find { where { this.outcomeConfId.eq(outcomeConf.id) } }
        assertThat(retrieveByOutcomeConfId).isSuccessWithData(listOf(record))

        val retrieveByPersonId = clinicalOutcomeRecordDataService.find { where { this.personId.eq(record.personId) } }
        assertThat(retrieveByPersonId).isSuccessWithData(listOf(record))

        val retrieveByReferenceLink = clinicalOutcomeRecordDataService.find {
            where { this.referencedLinks.eq(referencedLink1) }
        }
        assertThat(retrieveByReferenceLink).isSuccessWithData(listOf(record))

        assertThat(clinicalOutcomeRecordDataService.add(record2)).isSuccess()

        val retrieveByReferenceLinks = clinicalOutcomeRecordDataService.find {
            where { this.referencedLinks.containsAll(listOf(referencedLink1, referencedLink2)) }
        }
        assertThat(retrieveByReferenceLinks).isSuccessWithData(listOf(record))

        val existsByReferenceLinks = clinicalOutcomeRecordDataService.exists {
            where { this.referencedLinks.containsAll(listOf(referencedLink1, referencedLink2)) }
        }
        assertThat(existsByReferenceLinks).isSuccessWithData(true)

        val retrieveByAddedByStaff = clinicalOutcomeRecordDataService.find {
            where { this.addedBy.eq(ClinicalOutcomeRecord.AddedBy(type = ClinicalOutcomeRecord.AddedByType.STAFF)) }
        }
        assertThat(retrieveByAddedByStaff).isSuccessWithData(listOf(record2))

        val retrieveByAddedByMember = clinicalOutcomeRecordDataService.find {
            where { this.addedBy.eq(ClinicalOutcomeRecord.AddedBy(type = ClinicalOutcomeRecord.AddedByType.MEMBER)) }
        }
        assertThat(retrieveByAddedByMember).isSuccessWithData(listOf(record))
    }

}
