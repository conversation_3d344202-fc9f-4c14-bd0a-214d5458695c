package br.com.alice.api.ops.converters

import br.com.alice.api.ops.controllers.HealthPlanTaskTemplateRequest
import br.com.alice.common.RangeUUID
import br.com.alice.data.layer.models.HealthPlanTaskTemplate
import br.com.alice.data.layer.models.HealthPlanTaskType
import org.assertj.core.api.Assertions.assertThat
import kotlin.test.Test


internal class HealthPlanTaskTemplateConverterTest {

    private val specialtyId = RangeUUID.generate()
    private val subSpecialtyId = RangeUUID.generate()

    private val request = HealthPlanTaskTemplateRequest(
        type = HealthPlanTaskType.REFERRAL,
        title = "title",
        description = "description",
        frequency = null,
        start = null,
        deadline = null,
        active = true,
        content = mapOf(
            "specialty" to mapOf("name" to "specialty", "id" to specialtyId.toString()),
            "sub_specialty" to mapOf("name" to "subSpecialty", "id" to subSpecialtyId.toString()),
            "diagnostic_hypothesis" to "diagnosticHypothesis",
            "sessions_quantity" to 3,
            "follow_up_max_quantity" to 1
        )
    )

    private val expected = HealthPlanTaskTemplate(
        type = request.type,
        title = request.title,
        description = request.description,
        frequency = request.frequency,
        start = request.start,
        deadline = request.deadline,
        active = request.active,
        content = mapOf(
            "specialty" to mapOf("name" to "specialty", "id" to specialtyId.toString()),
            "subSpecialty" to mapOf("name" to "subSpecialty", "id" to subSpecialtyId.toString()),
            "diagnosticHypothesis" to "diagnosticHypothesis",
            "sessionsQuantity" to 3.0,
            "followUpMaxQuantity" to 1.0
        ),
    )

    @Test
    fun `#convert REFERRAL`() {
        val converted = HealthPlanTaskTemplateConverter.convert(request)
        assertThat(converted).usingRecursiveComparison()
            .ignoringFields("id", "updatedAt", "createdAt")
            .isEqualTo(expected)
    }

    @Test
    fun `#convert EMERGENCY`() {
        val request = HealthPlanTaskTemplateRequest(
            type = HealthPlanTaskType.EMERGENCY,
            title = "title",
            description = "description",
            frequency = null,
            start = null,
            deadline = null,
            active = true,
            content = mapOf(
                "specialty" to mapOf("name" to "specialty", "id" to specialtyId.toString()),
                "diagnostic_hypothesis" to "diagnosticHypothesis",
            )
        )

        val expected = HealthPlanTaskTemplate(
            type = request.type,
            title = request.title,
            description = request.description,
            frequency = request.frequency,
            start = request.start,
            deadline = request.deadline,
            active = request.active,
            content = mapOf(
                "specialty" to mapOf("name" to "specialty", "id" to specialtyId.toString()),
                "diagnosticHypothesis" to "diagnosticHypothesis",
            ),
        )

        val converted = HealthPlanTaskTemplateConverter.convert(request)
        assertThat(converted).usingRecursiveComparison()
            .ignoringFields("id", "updatedAt", "createdAt")
            .isEqualTo(expected)
    }

    @Test
    fun `#convert REFERRAL return if missing sessions`() {
        val request = request.copy(
            content = mapOf(
                "specialty" to mapOf("name" to "specialty", "id" to specialtyId.toString()),
                "sub_specialty" to mapOf("name" to "subSpecialty", "id" to subSpecialtyId.toString()),
                "diagnostic_hypothesis" to "diagnosticHypothesis"
            )
        )
        val expected = expected.copy(
            content = mapOf(
                "specialty" to mapOf("name" to "specialty", "id" to specialtyId.toString()),
                "subSpecialty" to mapOf("name" to "subSpecialty", "id" to subSpecialtyId.toString()),
                "diagnosticHypothesis" to "diagnosticHypothesis",
            )
        )
        val converted = HealthPlanTaskTemplateConverter.convert(request)
        assertThat(converted).usingRecursiveComparison()
            .ignoringFields("id", "updatedAt", "createdAt")
            .isEqualTo(expected)
    }

    @Test
    fun `#convert TEST_REQUEST`() {
        val request = request.copy(
            type = HealthPlanTaskType.TEST_REQUEST,
            content = mapOf("code" to "code")
        )
        val expected = expected.copy(
            type = HealthPlanTaskType.TEST_REQUEST,
            content = mapOf("code" to "code")
        )

        val converted = HealthPlanTaskTemplateConverter.convert(request)
        assertThat(converted).usingRecursiveComparison()
            .ignoringFields("id", "updatedAt", "createdAt")
            .isEqualTo(expected)
    }

    @Test
    fun `#convert returns null content to other types`() {
        val request = request.copy(
            type = HealthPlanTaskType.SLEEP,
            content = mapOf("any" to "any")
        )
        val expected = expected.copy(
            type = HealthPlanTaskType.SLEEP,
            content = null
        )

        val converted = HealthPlanTaskTemplateConverter.convert(request)
        assertThat(converted).usingRecursiveComparison()
            .ignoringFields("id", "updatedAt", "createdAt")
            .isEqualTo(expected)
    }
}
