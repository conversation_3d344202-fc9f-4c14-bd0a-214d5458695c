package br.com.alice.api.memberwannabe.api

import br.com.alice.api.memberwannabe.builders.FinishedSimulationResponseBuilder
import br.com.alice.api.memberwannabe.builders.LeadInformationBuilder
import br.com.alice.api.memberwannabe.builders.ProductsComparisonResponseBuilder
import br.com.alice.api.memberwannabe.builders.TrackingInfoBuilder
import br.com.alice.api.memberwannabe.controllers.HealthProductSimulationController
import br.com.alice.api.memberwannabe.models.CreateSimulationResponse
import br.com.alice.api.memberwannabe.models.FinishRequest
import br.com.alice.api.memberwannabe.models.FinishedSimulationProvider
import br.com.alice.api.memberwannabe.models.FinishedSimulationWithLeadResponse
import br.com.alice.api.memberwannabe.models.HealthProductSimulationAnswerTransport
import br.com.alice.api.memberwannabe.models.HealthProductSimulationAnswersRequest
import br.com.alice.api.memberwannabe.models.HealthProductSimulationQuestionBundleGroupResponse
import br.com.alice.api.memberwannabe.models.HealthProductSimulationQuestionBundleResponse
import br.com.alice.api.memberwannabe.models.HealthProductSimulationQuestionOptionResponse
import br.com.alice.api.memberwannabe.models.HealthProductSimulationQuestionResponse
import br.com.alice.api.memberwannabe.models.HealthProductSimulationRequest
import br.com.alice.api.memberwannabe.models.HealthProductSimulationResponse
import br.com.alice.api.memberwannabe.models.ProductRecommendationsResponse
import br.com.alice.api.memberwannabe.models.SimulationValidityResponse
import br.com.alice.api.memberwannabe.models.TrackingInfoRequest
import br.com.alice.api.memberwannabe.models.UpdateSimulationProductRequest
import br.com.alice.api.memberwannabe.models.UpsertLeadRequest
import br.com.alice.bottini.buildProductRecommendation
import br.com.alice.bottini.client.LeadQualificationService
import br.com.alice.bottini.client.LeadService
import br.com.alice.bottini.client.ProductComparisonService
import br.com.alice.bottini.client.SimulationNavigationService
import br.com.alice.bottini.exceptions.SimulationQuestionsNotAnsweredException
import br.com.alice.bottini.models.FinishedSimulation
import br.com.alice.bottini.models.HealthProductSimulationAnswerResponse
import br.com.alice.bottini.models.HealthProductSimulationQuestion
import br.com.alice.bottini.models.HealthProductSimulationQuestionBundle
import br.com.alice.bottini.models.HealthProductSimulationQuestionBundleGroup
import br.com.alice.bottini.models.HealthProductSimulationQuestionOption
import br.com.alice.bottini.models.LeadInformation
import br.com.alice.bottini.models.ProductComparison
import br.com.alice.bottini.models.ProductWithHospitalsAndLaboratories
import br.com.alice.bottini.models.QualifiedResult
import br.com.alice.bottini.models.SimulationValidity
import br.com.alice.common.RangeUUID
import br.com.alice.common.convertTo
import br.com.alice.common.core.exceptions.NotFoundException
import br.com.alice.common.core.extensions.toUUID
import br.com.alice.common.data.dsl.matchers.ResponseAssert
import br.com.alice.common.data.dsl.matchers.ResponseAssert.Companion.assertThat
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.FeatureConfig
import br.com.alice.data.layer.models.FeatureNamespace
import br.com.alice.data.layer.models.FeatureType
import br.com.alice.data.layer.models.HealthProductSimulation
import br.com.alice.data.layer.models.HealthProductSimulationAnswer
import br.com.alice.data.layer.models.HealthProductSimulationQuestionType
import br.com.alice.data.layer.models.HealthProductSimulationQuestionType.AGES
import br.com.alice.data.layer.models.HealthProductSimulationSimulatorVersion
import br.com.alice.data.layer.models.HealthProductSimulationType
import br.com.alice.data.layer.models.ProductBundleType
import br.com.alice.data.layer.models.ProviderType
import br.com.alice.data.layer.models.TrackingInfo
import br.com.alice.featureconfig.core.FeatureService
import br.com.alice.provider.client.ProviderUnitService
import com.github.kittinunf.result.failure
import com.github.kittinunf.result.success
import io.mockk.coEvery
import io.mockk.mockk
import io.mockk.slot
import kotlinx.coroutines.runBlocking
import org.assertj.core.api.Assertions.assertThat
import java.time.LocalDateTime
import kotlin.test.BeforeTest
import kotlin.test.Test

class HealthProductSimulationRoutesTest : RoutesTestHelper() {
    private val productComparisonService: ProductComparisonService = mockk()
    private val leadService: LeadService = mockk()
    private val providerUnitService: ProviderUnitService = mockk()
    private val leadQualifierService: LeadQualificationService = mockk()
    private val simulationNavigationService: SimulationNavigationService = mockk()

    private val healthProductSimulationController = HealthProductSimulationController(
        productComparisonService,
        leadService,
        providerUnitService,
        leadQualifierService,
        simulationNavigationService,
    )

    private val healthProductSimulationRequest = HealthProductSimulationRequest(
        version = HealthProductSimulationSimulatorVersion.V8.name,
    )
    private val simulationUrl = "https://alice.com.br/simulacoes/42b8733b-3b36-4fe9-ab3d-5722cbe24900/resultado"
    private val request = FinishRequest(
        trackingInfo = TrackingInfoRequest(
            utmSource = "source",
            utmMedium = "medium",
            utmCampaign = "campaign",
            utmContent = "content",
            utmTerm = "term",
            simulationUrl = simulationUrl,
        ),
    )

    @BeforeTest
    override fun setup() {
        super.setup()
        module.single { healthProductSimulationController }
    }

    @Test
    fun `#post simulation should call create and return 200 OK`() {
        val simulation = TestModelFactory.buildHealthProductSimulation()
        val question = HealthProductSimulationQuestion(
            type = HealthProductSimulationQuestionType.HOSPITAL_OPTION,
            options = listOf(
                HealthProductSimulationQuestionOption(
                    text = "Hospital Albert Einstein",
                    value = "einstein",
                    details = "5"
                ),
                HealthProductSimulationQuestionOption(
                    text = "Beneficiencia Portuguesa",
                    value = "bp",
                    details = "3"
                ),
            ),
            bundleGroups = listOf(
                HealthProductSimulationQuestionBundleGroup(
                    type = ProductBundleType.LABORATORY,
                    bundles = listOf(
                        HealthProductSimulationQuestionBundle(
                            id = "5fb8d48a-6c53-423c-aec4-8e28afd95526".toUUID(),
                            name = "Lab. Albert Einstein",
                            imageUrl = "https://s3.aws.com/einstein.png",
                            type = ProductBundleType.LABORATORY,
                            mutable = true,
                            selected = false,
                            priceScale = 3,
                        ),
                        HealthProductSimulationQuestionBundle(
                            id = "f3c39b04-6ace-4f83-ad35-6a3abe87a541".toUUID(),
                            name = "Fleury Medicina e Saúde",
                            imageUrl = "https://s3.aws.com/fleury.png",
                            type = ProductBundleType.LABORATORY,
                            mutable = true,
                            selected = false,
                            priceScale = 2,
                        ),
                        HealthProductSimulationQuestionBundle(
                            id = "4d8add2f-b0c3-4da0-9ac2-b115a1fdb204".toUUID(),
                            name = "Laboratório A+",
                            imageUrl = "https://s3.aws.com/a_mais.png",
                            type = ProductBundleType.LABORATORY,
                            mutable = false,
                            selected = true,
                            priceScale = 1,
                        ),
                    )
                )
            )
        )

        val answerResponse = HealthProductSimulationAnswerResponse(simulation = simulation, question = question)
        coEvery {
            simulationNavigationService.startSimulation(
                HealthProductSimulationSimulatorVersion.V8.name,
                TrackingInfoBuilder.build(
                    healthProductSimulationRequest.trackingInfo, "Ktor client", null
                )
            )
        } returns answerResponse.success()

        post("/simulation", healthProductSimulationRequest) { response ->
            val expectedResponse = CreateSimulationResponse(
                simulation = HealthProductSimulationResponse(
                    id = simulation.id,
                    answers = emptyList(),
                    simulationType = null,
                    progressPercent = 0
                ),
                question = HealthProductSimulationQuestionResponse(
                    type = HealthProductSimulationQuestionType.HOSPITAL_OPTION,
                    options = listOf(
                        HealthProductSimulationQuestionOptionResponse(
                            text = "Hospital Albert Einstein",
                            value = "einstein",
                            details = "5"
                        ),
                        HealthProductSimulationQuestionOptionResponse(
                            text = "Beneficiencia Portuguesa",
                            value = "bp",
                            details = "3"
                        ),
                    ),
                    bundleGroups = listOf(
                        HealthProductSimulationQuestionBundleGroupResponse(
                            type = ProviderType.LABORATORY,
                            bundles = listOf(
                                HealthProductSimulationQuestionBundleResponse(
                                    id = "5fb8d48a-6c53-423c-aec4-8e28afd95526".toUUID(),
                                    name = "Lab. Albert Einstein",
                                    imageUrl = "https://s3.aws.com/einstein.png",
                                    type = ProviderType.LABORATORY,
                                    mutable = true,
                                    selected = false,
                                    priceScale = 3
                                ),
                                HealthProductSimulationQuestionBundleResponse(
                                    id = "f3c39b04-6ace-4f83-ad35-6a3abe87a541".toUUID(),
                                    name = "Fleury Medicina e Saúde",
                                    imageUrl = "https://s3.aws.com/fleury.png",
                                    type = ProviderType.LABORATORY,
                                    mutable = true,
                                    selected = false,
                                    priceScale = 2
                                ),
                                HealthProductSimulationQuestionBundleResponse(
                                    id = "4d8add2f-b0c3-4da0-9ac2-b115a1fdb204".toUUID(),
                                    name = "Laboratório A+",
                                    imageUrl = "https://s3.aws.com/a_mais.png",
                                    type = ProviderType.LABORATORY,
                                    mutable = false,
                                    selected = true,
                                    priceScale = 1
                                ),
                            )
                        )
                    )
                )
            )

            assertThat(response).isOKWithData(expectedResponse)
        }
    }

    @Test
    fun `#create should call create with version query param`() {
        val version = HealthProductSimulationSimulatorVersion.V8.name
        val request = healthProductSimulationRequest.copy(version = version)

        post("/simulation", request) {
            coVerifyOnce {
                simulationNavigationService.startSimulation(
                    version,
                    TrackingInfoBuilder.build(request.trackingInfo, "Ktor client", null)
                )
            }
        }
    }

    @Test
    fun `#answer should return 200 OK when a answer was correctly answered`() = runBlocking {
        val simulation = TestModelFactory.buildHealthProductSimulation(type = HealthProductSimulationType.COMPANY)

        val slot = slot<List<HealthProductSimulationAnswer>>()

        val request = HealthProductSimulationAnswersRequest(
            answers = listOf(
                HealthProductSimulationAnswerTransport(answer = "28", questionType = AGES)
            ),
            currentQuestionType = AGES
        )

        val answerResponse = HealthProductSimulationAnswerResponse(simulation = simulation)
        coEvery {
            simulationNavigationService.answerAndMoveForward(
                simulation.id,
                capture(slot),
                AGES
            )
        } returns answerResponse.success()

        put("/simulation/${simulation.id}/answer", request) { response ->
            val expectedAnswers = listOf(
                HealthProductSimulationAnswer(answer = "28", questionType = AGES)
            )
            val updatedAnswers = slot.captured

            assertThat(updatedAnswers).isEqualTo(expectedAnswers)
            assertThat(response).isOK()
        }

        coVerifyOnce { simulationNavigationService.answerAndMoveForward(any(), any(), any()) }
    }

    @Test
    fun `#finish should return 404 Not Found when an simulation was not found`() = runBlocking {
        val simulationId = RangeUUID.generate()
        val trackingInfo = request.trackingInfo.convertTo(TrackingInfo::class).copy(userAgent = "Ktor client")

        coEvery {
            simulationNavigationService.finishSimulation(
                simulationId,
                trackingInfo,
            )
        } returns NotFoundException("not_Found").failure()

        post("/simulation/$simulationId/finish", request) { response ->
            assertThat(response).isNotFound()
        }

        coVerifyOnce { simulationNavigationService.finishSimulation(any(), any()) }
    }

    @Test
    fun `#finish should return 400 BadRequest when simulation are not answered`() = runBlocking {
        val simulationId = RangeUUID.generate()
        val trackingInfo = request.trackingInfo.convertTo(TrackingInfo::class).copy(userAgent = "Ktor client")

        coEvery {
            simulationNavigationService.finishSimulation(
                simulationId,
                trackingInfo,
            )
        } returns SimulationQuestionsNotAnsweredException().failure()

        post("/simulation/$simulationId/finish", request) { response ->
            assertThat(response).isBadRequestWithErrorCode("simulation_not_answered")
        }

        coVerifyOnce { simulationNavigationService.finishSimulation(any(), any()) }
    }

    @Test
    fun `#finish should return 200 OK when a simulation was finished`() = runBlocking {
        val simulationId = RangeUUID.generate()
        val trackingInfo = request.trackingInfo.convertTo(TrackingInfo::class).copy(userAgent = "Ktor client")
        coEvery {
            simulationNavigationService.finishSimulation(
                simulationId,
                trackingInfo,
            )
        } returns true.success()

        post("/simulation/$simulationId/finish", request) { response ->
            assertThat(response).isOK()
        }

        coVerifyOnce { simulationNavigationService.finishSimulation(any(), any()) }
    }

    @Test
    fun `#get should return 404 Not Found when an simulation was not found`() {
        val simulationId = RangeUUID.generate()

        coEvery {
            simulationNavigationService.getFinishedSimulation(simulationId)
        } returns NotFoundException("not_Found").failure()

        get("/simulation/$simulationId") { response ->
            assertThat(response).isNotFound()
        }
    }

    @Test
    fun `#get should return 400 BadRequest when an simulation was not found`() {
        val simulationId = RangeUUID.generate()

        coEvery {
            simulationNavigationService.getFinishedSimulation(simulationId)
        } returns SimulationQuestionsNotAnsweredException().failure()

        get("/simulation/$simulationId") { response ->
            assertThat(response).isBadRequestWithErrorCode("simulation_not_answered")
        }
    }

    @Test
    fun `#get should return 200 OK when a simulation was finished`() {
        val lead = TestModelFactory.buildLead()
        val simulationId = RangeUUID.generate()
        val finishedSimulation = FinishedSimulation(
            simulationId = simulationId,
            simulatorVersion = HealthProductSimulationSimulatorVersion.V3,
            simulationTypeHistory = emptyList(),
            leadId = lead.id,
            productRecommendations = listOf(TestModelFactory.buildProductRecommendation()),
            validity = SimulationValidity(expiresAt = null, expired = false),
        )
        val providerUnits = listOf(TestModelFactory.buildProviderUnit())
        val leadInformation = LeadInformationBuilder.build(lead, QualifiedResult(true, true))

        coEvery {
            simulationNavigationService.getFinishedSimulation(simulationId)
        } returns finishedSimulation.success()
        coEvery { leadService.get(lead.id) } returns lead.success()
        coEvery { leadQualifierService.qualify(lead) } returns QualifiedResult(true, true).success()
        coEvery { providerUnitService.getByIds(any()) } returns providerUnits.success()

        val expectedFinishedSimulationResponse =
            FinishedSimulationResponseBuilder.buildFinishedSimulationWithLead(
                finishedSimulation,
                leadInformation,
                providerUnits
            )

        get("/simulation/$simulationId") { response ->
            assertThat(response).isOKWithData(expectedFinishedSimulationResponse)
        }
    }

    @Test
    fun `#get should return 200 OK when a expired simulation was finished`() {
        val lead = TestModelFactory.buildLead()
        val simulationId = RangeUUID.generate()
        val expiresAt = LocalDateTime.of(2020, 1, 1, 10, 10, 10)
        val finishedSimulation = FinishedSimulation(
            simulationId = simulationId,
            simulationType = HealthProductSimulationType.MICRO_COMPANY,
            HealthProductSimulationSimulatorVersion.V3,
            simulationTypeHistory = emptyList(),
            leadId = lead.id,
            productRecommendations = listOf(TestModelFactory.buildProductRecommendation()),
            validity = SimulationValidity(expiresAt = expiresAt, expired = true),
        )
        val providerUnits = listOf(TestModelFactory.buildProviderUnit())
        val leadInformation = LeadInformationBuilder.build(lead, QualifiedResult(true, true))

        coEvery {
            simulationNavigationService.getFinishedSimulation(simulationId)
        } returns finishedSimulation.success()
        coEvery { leadService.get(lead.id) } returns lead.success()
        coEvery { leadQualifierService.qualify(lead) } returns QualifiedResult(true, true).success()
        coEvery { providerUnitService.getByIds(any()) } returns providerUnits.success()

        val expectedFinishedSimulationResponse =
            FinishedSimulationResponseBuilder.buildFinishedSimulationWithLead(
                finishedSimulation,
                leadInformation,
                providerUnits
            )

        get("/simulation/$simulationId") { response ->
            assertThat(response).isOKWithData(expectedFinishedSimulationResponse)
        }
    }

    @Test
    fun `#updateExpired should return 200 OK`() {
        val lead = TestModelFactory.buildLead()
        val simulationId = RangeUUID.generate()
        val finishedSimulation = FinishedSimulation(
            simulationId = simulationId,
            simulationType = HealthProductSimulationType.MICRO_COMPANY,
            HealthProductSimulationSimulatorVersion.V3,
            simulationTypeHistory = emptyList(),
            leadId = lead.id,
            productRecommendations = listOf(TestModelFactory.buildProductRecommendation()),
            validity = SimulationValidity(expiresAt = null, expired = false),
        )
        val providerUnits = listOf(TestModelFactory.buildProviderUnit())
        coEvery {
            simulationNavigationService.updateExpiredOpportunity(simulationId)
        } returns finishedSimulation.success()
        coEvery { providerUnitService.getByIds(any()) } returns providerUnits.success()

        val expectedFinishedSimulationResponse =
            FinishedSimulationResponseBuilder.buildFinishedSimulation(finishedSimulation, providerUnits)

        post("/simulation/$simulationId/update_opportunity") { response ->
            assertThat(response).isOKWithData(expectedFinishedSimulationResponse)
        }
    }

    @Test
    fun `#updateExpired should return 404 NotFound when no opportunity id found for simulation`() {
        val simulationId = RangeUUID.generate()

        coEvery {
            simulationNavigationService.updateExpiredOpportunity(simulationId)
        } throws NotFoundException()
        post("/simulation/$simulationId/update_opportunity") { response ->
            assertThat(response).isNotFound()
        }
    }

    @Test
    fun `#updateSimulationProduct should update simulation product`() {
        val lead = TestModelFactory.buildLead()
        val request = UpdateSimulationProductRequest(
            productId = RangeUUID.generate().toString()
        )
        val simulationId = RangeUUID.generate()
        val finishedSimulation = FinishedSimulation(
            simulationId = simulationId,
            simulationType = HealthProductSimulationType.MICRO_COMPANY,
            HealthProductSimulationSimulatorVersion.V3,
            simulationTypeHistory = emptyList(),
            leadId = lead.id,
            productRecommendations = listOf(TestModelFactory.buildProductRecommendation()),
            validity = SimulationValidity(expiresAt = null, expired = false),
        )
        val providerUnits = listOf(TestModelFactory.buildProviderUnit())
        
        coEvery {
            simulationNavigationService.updateProductV2(
                simulationId = simulationId,
                productId = request.productId.toUUID()
            )
        } returns finishedSimulation.success()
        coEvery { providerUnitService.getByIds(any()) } returns providerUnits.success()

        val expectedFinishedSimulationResponse =
            FinishedSimulationResponseBuilder.buildFinishedSimulation(finishedSimulation, providerUnits)
        put("/simulation/${simulationId}/update_product/v2", request) { response ->
            assertThat(response).isOKWithData(expectedFinishedSimulationResponse)
        }
    }

    @Test
    fun `#upsertLead should upsert lead from simulation and return lead information`() {
        val simulationId = RangeUUID.generate()
        val upsertLeadRequest = UpsertLeadRequest(
            trackingInfo = TrackingInfoRequest(
                utmSource = "source",
                utmMedium = "medium",
                utmCampaign = "campaign",
                utmContent = "content",
                utmTerm = "term"
            )
        )
        val trackingInfo =
            upsertLeadRequest.trackingInfo.convertTo(TrackingInfo::class).copy(userAgent = "Ktor client")

        val leadInfo = LeadInformation(
            id = RangeUUID.generate(),
            name = "Marple",
            email = "<EMAIL>",
            phoneNumber = "5511982782000",
            isSuperTarget = true,
            isTarget = true
        )

        coEvery {
            simulationNavigationService.upsertLead(
                simulationId,
                trackingInfo
            )
        } returns leadInfo.success()

        post("/simulation/$simulationId/lead", upsertLeadRequest) { response ->
            assertThat(response).isOKWithData(
                leadInfo
            )
        }
    }

    @Test
    fun `#getProductOptionsToComparison return product options to comparison`() {
        val providersNumberToTake = 10
        val lead = TestModelFactory.buildLead()
        val simulation = HealthProductSimulation(
            id = RangeUUID.generate(),
            answers = listOf(HealthProductSimulationAnswer(questionType = AGES, answer = "[{\"age\":\"28\"}]")),
            leadId = lead.id
        )
        val providers = TestModelFactory.buildProviders()
        val mainProduct = TestModelFactory.buildProduct(title = "main product")
        val otherProduct = TestModelFactory.buildProduct(title = "other product")
        val productComparison = ProductComparison(
            simulation = simulation,
            productWithHospitalsAndLaboratories = listOf(
                ProductWithHospitalsAndLaboratories(
                    product = mainProduct,
                    hospitals = providers.filter { ProviderType.hospitalTypes().contains(it.type) },
                    laboratories = providers.filter { it.type == ProviderType.LABORATORY },
                    totalOfHospitals = 15,
                    totalOfLaboratories = 20
                ),
                ProductWithHospitalsAndLaboratories(
                    product = otherProduct,
                    hospitals = listOf(providers.first { ProviderType.hospitalTypes().contains(it.type) }),
                    laboratories = listOf(providers.first { it.type == ProviderType.LABORATORY }),
                    totalOfHospitals = 15,
                    totalOfLaboratories = 20
                )
            ),
            selectedProductId = mainProduct.id
        )
        coEvery {
            productComparisonService.getProductOptionsToComparison(
                simulation.id,
                mainProduct.id,
                providersNumberToTake
            )
        } returns productComparison.success()

        val expectedResult = ProductsComparisonResponseBuilder.build(
            productsWithHospitalsAndLaboratories = productComparison.productWithHospitalsAndLaboratories,
            selectedProductId = mainProduct.id,
            simulation = productComparison.simulation
        )

        get("/simulation/${simulation.id}/product/${mainProduct.id}/comparison?providers_number_to_take=10") { response ->
            assertThat(response).isOKWithData(expectedResult)
        }
    }

    @Test
    fun `#getProductOptionsToComparison return product options to comparison using default providersNumberToTake `() {
        val providersNumberToTake = 8
        val lead = TestModelFactory.buildLead()
        val simulation = HealthProductSimulation(
            id = RangeUUID.generate(),
            answers = listOf(HealthProductSimulationAnswer(questionType = AGES, answer = "[{\"age\":\"28\"}]")),
            leadId = lead.id
        )
        val providers = TestModelFactory.buildProviders()
        val mainProduct = TestModelFactory.buildProduct(title = "main product")
        val otherProduct = TestModelFactory.buildProduct(title = "other product")
        val productComparison = ProductComparison(
            simulation = simulation,
            productWithHospitalsAndLaboratories = listOf(
                ProductWithHospitalsAndLaboratories(
                    product = mainProduct,
                    hospitals = providers.filter { ProviderType.hospitalTypes().contains(it.type) },
                    laboratories = providers.filter { it.type == ProviderType.LABORATORY },
                    totalOfHospitals = 15,
                    totalOfLaboratories = 20
                ),
                ProductWithHospitalsAndLaboratories(
                    product = otherProduct,
                    hospitals = listOf(providers.first { ProviderType.hospitalTypes().contains(it.type) }),
                    laboratories = listOf(providers.first { it.type == ProviderType.LABORATORY }),
                    totalOfHospitals = 15,
                    totalOfLaboratories = 20

                )
            ),
            selectedProductId = mainProduct.id
        )
        coEvery {
            productComparisonService.getProductOptionsToComparison(
                simulation.id,
                mainProduct.id,
                providersNumberToTake
            )
        } returns productComparison.success()

        val expectedResult = ProductsComparisonResponseBuilder.build(
            productsWithHospitalsAndLaboratories = productComparison.productWithHospitalsAndLaboratories,
            selectedProductId = mainProduct.id,
            simulation = productComparison.simulation
        )

        get("/simulation/${simulation.id}/product/${mainProduct.id}/comparison") { response ->
            assertThat(response).isOKWithData(expectedResult)
        }
    }

    @Test
    fun `#get should return 200`(): Unit = runBlocking {
        val hospitals = listOf(
            TestModelFactory.buildProvider()
        )
        val productRecommendations = listOf(
            TestModelFactory.buildProductRecommendation(
                hospitals = hospitals,
            )
        )
        val simulationId = RangeUUID.generate()
        val lead = TestModelFactory.buildLead()
        val simulationValidity = SimulationValidity(
            expiresAt = LocalDateTime.now().plusDays(7),
            expired = false,
        )
        val finishedSimulation = FinishedSimulation(
            simulationId = simulationId,
            simulationType = HealthProductSimulationType.MICRO_COMPANY,
            simulatorVersion = HealthProductSimulationSimulatorVersion.V8,
            simulationTypeHistory = emptyList(),
            leadId = lead.id,
            productRecommendations = productRecommendations,
            validity = simulationValidity,
        )
        coEvery {
            simulationNavigationService.getFinishedSimulation(simulationId)
        } returns finishedSimulation.success()
        coEvery {
            leadService.get(lead.id)
        } returns lead.success()
        coEvery {
            leadQualifierService.qualify(lead)
        } returns QualifiedResult(
            isIMP = true,
            isTarget = true,
            isSuperTarget = true,
            isAntiIMP = false,
        ).success()

        get("/simulation/$simulationId/") { response ->
            ResponseAssert.assertThat(response).isOKWithData(
                FinishedSimulationWithLeadResponse(
                    simulationId = finishedSimulation.simulationId,
                    simulationType = finishedSimulation.simulationType,
                    simulatorVersion = finishedSimulation.simulatorVersion,
                    productRecommendations = finishedSimulation.productRecommendations.map {
                        ProductRecommendationsResponse(
                            title = it.title,
                            name = it.name,
                            totalPrice = it.totalPrice,
                            pricesByAge = it.pricesByAge,
                            isSelected = it.isSelected,
                            priceByEmployeeRange = it.priceByEmployeeRange,
                            employeeNumberOfTotalPrice = it.employeeNumberOfTotalPrice,
                            hospitals = hospitals.map {
                                FinishedSimulationProvider(
                                    id = it.id,
                                    name = it.name,
                                    icon = it.imageUrl,
                                    type = it.type,
                                    providerUnitsCount = 0,
                                )
                            },
                            laboratories = emptyList(),
                            hospitalsCount = 0,
                            laboratoriesCount = 0,
                            anchor = it.anchor,
                            id = it.id,
                            accommodation = it.accommodation,
                            coPayment = true,
                            bundles = it.bundles,
                        )
                    },
                    validity = SimulationValidityResponse(
                        expiresAt = simulationValidity.expiresAt,
                        expired = simulationValidity.expired,
                    ),
                    leadInformation = LeadInformation(
                        id = lead.id,
                        name = lead.nickName,
                        email = lead.email,
                        phoneNumber = lead.phoneNumber,
                        isTarget = true,
                        isSuperTarget = true,
                    )
                )
            )
        }
    }
}
