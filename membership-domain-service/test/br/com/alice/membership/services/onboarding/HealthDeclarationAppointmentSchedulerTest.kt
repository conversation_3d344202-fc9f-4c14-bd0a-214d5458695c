package br.com.alice.membership.services.onboarding

import br.com.alice.business.client.BeneficiaryService
import br.com.alice.common.core.exceptions.NotFoundException
import br.com.alice.common.core.extensions.toUrlEncoded
import br.com.alice.common.data.dsl.matchers.ResultAssert.Companion.assertThat
import br.com.alice.common.featureflag.withFeatureFlag
import br.com.alice.common.helpers.returns
import br.com.alice.common.idToken
import br.com.alice.common.serialization.gson
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.BeneficiaryOnboardingFlowType
import br.com.alice.data.layer.models.FeatureNamespace
import br.com.alice.data.layer.models.PersonCalendly
import br.com.alice.membership.HealthDeclarationAppointment
import br.com.alice.membership.ServiceConfig
import br.com.alice.membership.client.onboarding.InvalidOnboardingStateException
import br.com.alice.person.client.PersonService
import br.com.alice.schedule.client.PersonCalendlyService
import com.github.kittinunf.result.failure
import io.mockk.coEvery
import io.mockk.every
import io.mockk.mockk
import io.mockk.mockkObject
import kotlinx.coroutines.runBlocking
import java.time.LocalDateTime
import kotlin.test.Test

class HealthDeclarationAppointmentSchedulerTest {

    val person = TestModelFactory.buildPerson(dateOfBirth = LocalDateTime.now().minusYears(30))
    val personCalendly = PersonCalendly(personId = person.id)
    val utmTerm = gson.toJson(mapOf("person_calendly_id" to personCalendly.id)).toUrlEncoded()

    private val personService: PersonService = mockk()
    private val beneficiaryService: BeneficiaryService = mockk()
    private val personCalendlyService: PersonCalendlyService = mockk()
    private val healthDeclarationAppointmentConfig = HealthDeclarationAppointment(
        calendarUrl = "https://webview.alice.com.br/schedule-first-appointment/?schedule_url=https://calendly.com/time-alice/fale-com-alice&name=[[NAME]]&email=[[EMAIL]]",
        saraCalendarUrl = "https://webview.alice.com.br/scheduler/?appointmentScheduleEventTypeId=816cbc90-c1bd-491d-9893-62eea0bf3e00&token=[[MEMBER_TOKEN]]#schedule",
        beneficiaryFullRiskCalendarUrl = "https://webview.alice.com.br/schedule-first-appointment/?schedule_url=https://calendly.com/time-alice/papo-com-enfermeira-alice&name=[[NAME]]&email=[[EMAIL]]",
        beneficiaryFullRiskSaraCalendarUrl = "https://webview.alice.com.br/scheduler/?appointmentScheduleEventTypeId=816cbc90-c1bd-491d-9893-62eea0bf3e00&token=[[MEMBER_TOKEN]]#schedule",
        beneficiaryPartialRiskCalendarUrl = "https://webview.alice.com.br/schedule-first-appointment/?schedule_url= https://calendly.com/time-alice/vamos-falar-de-coberturas&name=[[NAME]]&email=[[EMAIL]]",
        beneficiaryPartialRiskSaraCalendarUrl = "https://webview.alice.com.br/scheduler/?appointmentScheduleEventTypeId=816cbc90-c1bd-491d-9893-62eea0bf3e00&token=[[MEMBER_TOKEN]]#schedule",
        //TODO: Change this URL when the correct URL is available
        beneficiaryFamilyCallCalendarUrl = "https://webview.alice.com.br/schedule-first-appointment/?schedule_url=https://calendly.com/time-alice/fale-com-alice&name=[[NAME]]&email=[[EMAIL]]",
        beneficiaryFamilyCallSaraCalendarUrl = "https://webview.alice.com.br/scheduler/?appointmentScheduleEventTypeId=816cbc90-c1bd-491d-9893-62eea0bf3e00&token=[[MEMBER_TOKEN]]#schedule",
        beneficiaryIntermittentNurseCalendarUrl = "https://webview.alice.com.br/schedule-first-appointment/?schedule_url=https://calendly.com/alicesaude/dev2-papo-com-enfermeira-intermitente-b2b&name=[[NAME]]&email=[[EMAIL]]",
    )

    init {
        every { personService.path() } returns "membership/person"
    }

    private val healthDeclarationAppointmentScheduler = HealthDeclarationAppointmentSchedulerImpl(
        personService,
        personCalendlyService,
        beneficiaryService
    )

    @Test
    fun `#getScheduleUrl should return URL as expected`() = runBlocking {
        mockkObject(ServiceConfig) {
            coEvery { personCalendlyService.getOrCreate(person.id) } returns personCalendly
            coEvery { personService.get(person.id) } returns person
            every { ServiceConfig.healthDeclarationAppointment } returns healthDeclarationAppointmentConfig

            val result = healthDeclarationAppointmentScheduler.getScheduleUrl(person.id)
            assertThat(result).isSuccessWithData(
                healthDeclarationAppointmentConfig.calendarUrl
                    .replace("[[NAME]]", person.contactName.toUrlEncoded())
                    .replace("[[EMAIL]]", person.email.toUrlEncoded())
                    .plus("&utm_term=$utmTerm")
                    .plus("&person_calendly_id=${personCalendly.id}")
            )
        }
    }

    @Test
    fun `#getScheduleUrl should return Sara URL as expected`() = runBlocking {
        mockkObject(ServiceConfig) {
            withFeatureFlag(
                namespace = FeatureNamespace.SCHEDULE,
                key = "should_use_sara_url_on_health_declaration_appointment",
                value = true
            ) {
                every { ServiceConfig.healthDeclarationAppointment } returns healthDeclarationAppointmentConfig

                val result = healthDeclarationAppointmentScheduler.getScheduleUrl(person.id)
                assertThat(result).isSuccessWithData(
                    healthDeclarationAppointmentConfig.saraCalendarUrl
                        .replace("[[MEMBER_TOKEN]]", idToken().orEmpty())
                )
            }
        }
    }

    @Test
    fun `#getB2BScheduleUrl should return URL as expected for PARTIAL_RISK_FLOW`() = runBlocking {
        mockkObject(ServiceConfig) {
            val beneficiaryOnboarding =
                TestModelFactory.buildBeneficiaryOnboarding(flowType = BeneficiaryOnboardingFlowType.PARTIAL_RISK_FLOW)
            val beneficiary = TestModelFactory.buildBeneficiary(onboarding = beneficiaryOnboarding)

            coEvery {
                beneficiaryService.findByPersonId(
                    person.id,
                    BeneficiaryService.FindOptions(withOnboarding = true)
                )
            } returns beneficiary
            coEvery {
                beneficiaryService.findByParentId(
                    beneficiary.id,
                    onlyDirectDependents = true
                )
            } returns listOf()
            coEvery { personService.get(person.id) } returns person
            coEvery { personCalendlyService.getOrCreate(person.id) } returns personCalendly

            every { ServiceConfig.healthDeclarationAppointment } returns healthDeclarationAppointmentConfig

            val result = healthDeclarationAppointmentScheduler.getBeneficiaryScheduleUrl(person.id)
            assertThat(result).isSuccessWithData(
                healthDeclarationAppointmentConfig.beneficiaryPartialRiskCalendarUrl
                    .replace("[[NAME]]", person.contactName.toUrlEncoded())
                    .replace("[[EMAIL]]", person.email.toUrlEncoded())
                    .plus("&utm_term=$utmTerm")
                    .plus("&person_calendly_id=${personCalendly.id}")
            )
        }
    }

    @Test
    fun `#getB2BScheduleUrl should return Sara URL as expected for PARTIAL_RISK_FLOW`() = runBlocking {
        mockkObject(ServiceConfig) {
            withFeatureFlag(
                namespace = FeatureNamespace.SCHEDULE,
                key = "should_use_sara_url_on_health_declaration_appointment",
                value = true
            ) {
                val beneficiaryOnboarding =
                    TestModelFactory.buildBeneficiaryOnboarding(flowType = BeneficiaryOnboardingFlowType.PARTIAL_RISK_FLOW)
                val beneficiary = TestModelFactory.buildBeneficiary(onboarding = beneficiaryOnboarding)

                coEvery {
                    beneficiaryService.findByPersonId(
                        person.id,
                        BeneficiaryService.FindOptions(withOnboarding = true)
                    )
                } returns beneficiary
                coEvery {
                    beneficiaryService.findByParentId(
                        beneficiary.id,
                        onlyDirectDependents = true
                    )
                } returns listOf()
                coEvery { personService.get(person.id) } returns person

                every { ServiceConfig.healthDeclarationAppointment } returns healthDeclarationAppointmentConfig

                val result = healthDeclarationAppointmentScheduler.getBeneficiaryScheduleUrl(person.id)
                assertThat(result).isSuccessWithData(
                    healthDeclarationAppointmentConfig.beneficiaryPartialRiskSaraCalendarUrl
                        .replace("[[MEMBER_TOKEN]]", idToken().orEmpty())
                )
            }
        }
    }

    @Test
    fun `#getB2BScheduleUrl should return URL as expected for FULL_RISK_FLOW`() = runBlocking {
        mockkObject(ServiceConfig) {
            val beneficiaryOnboarding =
                TestModelFactory.buildBeneficiaryOnboarding(flowType = BeneficiaryOnboardingFlowType.FULL_RISK_FLOW)
            val beneficiary = TestModelFactory.buildBeneficiary(onboarding = beneficiaryOnboarding)

            coEvery {
                beneficiaryService.findByPersonId(
                    person.id,
                    BeneficiaryService.FindOptions(withOnboarding = true)
                )
            } returns beneficiary
            coEvery { personService.get(person.id) } returns person
            coEvery { personCalendlyService.getOrCreate(person.id) } returns personCalendly
            coEvery {
                beneficiaryService.findByParentId(
                    beneficiary.id,
                    onlyDirectDependents = true
                )
            } returns listOf()

            every { ServiceConfig.healthDeclarationAppointment } returns healthDeclarationAppointmentConfig

            val result = healthDeclarationAppointmentScheduler.getBeneficiaryScheduleUrl(person.id)
            assertThat(result).isSuccessWithData(
                healthDeclarationAppointmentConfig.beneficiaryFullRiskCalendarUrl
                    .replace("[[NAME]]", person.contactName.toUrlEncoded())
                    .replace("[[EMAIL]]", person.email.toUrlEncoded())
                    .plus("&utm_term=$utmTerm")
                    .plus("&person_calendly_id=${personCalendly.id}")
            )
        }
    }

    @Test
    fun `#getB2BScheduleUrl should return Sara URL as expected for FULL_RISK_FLOW`() = runBlocking {
        mockkObject(ServiceConfig) {
            withFeatureFlag(
                namespace = FeatureNamespace.SCHEDULE,
                key = "should_use_sara_url_on_health_declaration_appointment",
                value = true
            ) {
                val beneficiaryOnboarding =
                    TestModelFactory.buildBeneficiaryOnboarding(flowType = BeneficiaryOnboardingFlowType.FULL_RISK_FLOW)
                val beneficiary = TestModelFactory.buildBeneficiary(onboarding = beneficiaryOnboarding)

                coEvery {
                    beneficiaryService.findByPersonId(
                        person.id,
                        BeneficiaryService.FindOptions(withOnboarding = true)
                    )
                } returns beneficiary
                coEvery {
                    beneficiaryService.findByParentId(
                        beneficiary.id,
                        onlyDirectDependents = true
                    )
                } returns listOf()
                coEvery { personService.get(person.id) } returns person

                every { ServiceConfig.healthDeclarationAppointment } returns healthDeclarationAppointmentConfig

                val result = healthDeclarationAppointmentScheduler.getBeneficiaryScheduleUrl(person.id)
                assertThat(result).isSuccessWithData(
                    healthDeclarationAppointmentConfig.beneficiaryFullRiskSaraCalendarUrl
                        .replace("[[MEMBER_TOKEN]]", idToken().orEmpty())
                )
            }
        }
    }

    @Test
    fun `#getB2BScheduleUrl should return error if onboarding is in invalid flow type`() = runBlocking {
        mockkObject(ServiceConfig) {
            val beneficiaryOnboarding =
                TestModelFactory.buildBeneficiaryOnboarding(flowType = BeneficiaryOnboardingFlowType.NO_RISK_FLOW)
            val beneficiary = TestModelFactory.buildBeneficiary(onboarding = beneficiaryOnboarding)

            coEvery {
                beneficiaryService.findByPersonId(
                    person.id,
                    BeneficiaryService.FindOptions(withOnboarding = true)
                )
            } returns beneficiary
            coEvery {
                beneficiaryService.findByParentId(
                    beneficiary.id,
                    onlyDirectDependents = true
                )
            } returns listOf()

            coEvery { personService.get(person.id) } returns person

            every { ServiceConfig.healthDeclarationAppointment } returns healthDeclarationAppointmentConfig

            val result = healthDeclarationAppointmentScheduler.getBeneficiaryScheduleUrl(person.id)
            assertThat(result).isFailureOfType(InvalidOnboardingStateException::class)
        }
    }

    @Test
    fun `#getB2BScheduleUrl should return error if person hasn't Beneficiary`() = runBlocking {
        mockkObject(ServiceConfig) {

            coEvery {
                beneficiaryService.findByPersonId(
                    person.id,
                    BeneficiaryService.FindOptions(withOnboarding = true)
                )
            } returns NotFoundException().failure()

            every { ServiceConfig.healthDeclarationAppointment } returns healthDeclarationAppointmentConfig

            val result = healthDeclarationAppointmentScheduler.getBeneficiaryScheduleUrl(person.id)
            assertThat(result).isFailureOfType(NotFoundException::class)
        }
    }

    @Test
    fun `#getB2BScheduleUrl should return URL as expected for family video call`() = runBlocking {
        mockkObject(ServiceConfig) {
            withFeatureFlag(
                namespace = FeatureNamespace.SCHEDULE,
                key = "is_family_video_call_link_enabled",
                value = true
            ) {

                val dependent = TestModelFactory.buildBeneficiary()

                val beneficiaryOnboarding =
                    TestModelFactory.buildBeneficiaryOnboarding(flowType = BeneficiaryOnboardingFlowType.FULL_RISK_FLOW)
                val beneficiary = TestModelFactory.buildBeneficiary(
                    onboarding = beneficiaryOnboarding
                )

                coEvery {
                    beneficiaryService.findByPersonId(
                        person.id,
                        BeneficiaryService.FindOptions(withOnboarding = true)
                    )
                } returns beneficiary
                coEvery {
                    beneficiaryService.findByParentId(
                        beneficiary.id,
                        onlyDirectDependents = true
                    )
                } returns listOf(dependent)
                coEvery { personService.get(person.id) } returns person
                coEvery { personCalendlyService.getOrCreate(person.id) } returns personCalendly

                every { ServiceConfig.healthDeclarationAppointment } returns healthDeclarationAppointmentConfig

                val result = healthDeclarationAppointmentScheduler.getBeneficiaryScheduleUrl(person.id)

                assertThat(result).isSuccessWithData(
                    healthDeclarationAppointmentConfig.beneficiaryFamilyCallCalendarUrl
                        .replace("[[NAME]]", person.contactName.toUrlEncoded())
                        .replace("[[EMAIL]]", person.email.toUrlEncoded())
                        .plus("&utm_term=$utmTerm")
                        .plus("&person_calendly_id=${personCalendly.id}")
                        .plus("&family=true")
                )
            }
        }
    }

    @Test
    fun `#getB2BScheduleUrl should not return URL for family video call when flag is disabled`() = runBlocking {
        mockkObject(ServiceConfig) {
            withFeatureFlag(
                namespace = FeatureNamespace.SCHEDULE,
                key = "is_family_video_call_link_enabled",
                value = false
            ) {

                val dependent = TestModelFactory.buildBeneficiary()

                val beneficiaryOnboarding =
                    TestModelFactory.buildBeneficiaryOnboarding(flowType = BeneficiaryOnboardingFlowType.FULL_RISK_FLOW)
                val beneficiary = TestModelFactory.buildBeneficiary(
                    onboarding = beneficiaryOnboarding
                )

                coEvery {
                    beneficiaryService.findByPersonId(
                        person.id,
                        BeneficiaryService.FindOptions(withOnboarding = true)
                    )
                } returns beneficiary
                coEvery {
                    beneficiaryService.findByParentId(
                        beneficiary.id,
                        onlyDirectDependents = true
                    )
                } returns listOf(dependent)
                coEvery { personService.get(person.id) } returns person
                coEvery { personCalendlyService.getOrCreate(person.id) } returns personCalendly

                every { ServiceConfig.healthDeclarationAppointment } returns healthDeclarationAppointmentConfig

                val result = healthDeclarationAppointmentScheduler.getBeneficiaryScheduleUrl(person.id)

                assertThat(result).isSuccess()
                assertThat(result).isNotEqualTo(
                    healthDeclarationAppointmentConfig.beneficiaryFamilyCallCalendarUrl
                        .replace("[[NAME]]", person.contactName.toUrlEncoded())
                        .replace("[[EMAIL]]", person.email.toUrlEncoded())
                        .plus("&utm_term=$utmTerm")
                        .plus("&person_calendly_id=${personCalendly.id}")
                        .plus("&family=true")
                )
            }
        }
    }

    @Test
    fun `#getB2BScheduleUrl should not return URL for family video call when FF is enabled but beneficiary has no dependents`() =
        runBlocking {
            mockkObject(ServiceConfig) {
                withFeatureFlag(
                    namespace = FeatureNamespace.SCHEDULE,
                    key = "is_family_video_call_link_enabled",
                    value = true
                ) {
                    val beneficiaryOnboarding =
                        TestModelFactory.buildBeneficiaryOnboarding(flowType = BeneficiaryOnboardingFlowType.FULL_RISK_FLOW)
                    val beneficiary = TestModelFactory.buildBeneficiary(onboarding = beneficiaryOnboarding)

                    coEvery {
                        beneficiaryService.findByPersonId(
                            person.id,
                            BeneficiaryService.FindOptions(withOnboarding = true)
                        )
                    } returns beneficiary
                    coEvery {
                        beneficiaryService.findByParentId(
                            beneficiary.id,
                            onlyDirectDependents = true
                        )
                    } returns listOf()
                    coEvery { personService.get(person.id) } returns person
                    coEvery { personCalendlyService.getOrCreate(person.id) } returns personCalendly

                    every { ServiceConfig.healthDeclarationAppointment } returns healthDeclarationAppointmentConfig

                    val result = healthDeclarationAppointmentScheduler.getBeneficiaryScheduleUrl(person.id)

                    assertThat(result).isSuccess()
                    assertThat(result).isNotEqualTo(
                        healthDeclarationAppointmentConfig.beneficiaryFamilyCallCalendarUrl
                            .replace("[[NAME]]", person.contactName.toUrlEncoded())
                            .replace("[[EMAIL]]", person.email.toUrlEncoded())
                            .plus("&utm_term=$utmTerm")
                            .plus("&person_calendly_id=${personCalendly.id}")
                            .plus("&family=true")
                    )
                }
            }
        }

    @Test
    fun `#getB2BScheduleUrl should return URL for intermittent nurse video call when adult`() = runBlocking {
        mockkObject(ServiceConfig) {
            withFeatureFlag(
                namespace = FeatureNamespace.SCHEDULE,
                key = "is_family_video_call_link_enabled",
                value = true
            ) {
                val beneficiaryOnboarding =
                    TestModelFactory.buildBeneficiaryOnboarding(flowType = BeneficiaryOnboardingFlowType.FULL_RISK_FLOW)
                val beneficiary = TestModelFactory.buildBeneficiary(onboarding = beneficiaryOnboarding)

                coEvery {
                    beneficiaryService.findByPersonId(
                        person.id,
                        BeneficiaryService.FindOptions(withOnboarding = true)
                    )
                } returns beneficiary
                coEvery {
                    beneficiaryService.findByParentId(
                        beneficiary.id,
                        onlyDirectDependents = true
                    )
                } returns listOf()
                coEvery { personService.get(person.id) } returns person
                coEvery { personCalendlyService.getOrCreate(person.id) } returns personCalendly

                every { ServiceConfig.healthDeclarationAppointment } returns healthDeclarationAppointmentConfig

                val result = healthDeclarationAppointmentScheduler.getBeneficiaryScheduleUrl(person.id, false)

                assertThat(result).isSuccessWithData(
                    healthDeclarationAppointmentConfig.beneficiaryIntermittentNurseCalendarUrl
                        .replace("[[NAME]]", person.contactName.toUrlEncoded())
                        .replace("[[EMAIL]]", person.email.toUrlEncoded())
                        .plus("&utm_term=$utmTerm")
                        .plus("&person_calendly_id=${personCalendly.id}")
                )
            }
        }
    }

    @Test
    fun `#getB2BScheduleUrl if person is minor should return URL for highRisk even if is lowrisk`() = runBlocking {
        mockkObject(ServiceConfig) {
            withFeatureFlag(
                namespace = FeatureNamespace.SCHEDULE,
                key = "is_family_video_call_link_enabled",
                value = true
            ) {
                val beneficiaryOnboarding =
                    TestModelFactory.buildBeneficiaryOnboarding(flowType = BeneficiaryOnboardingFlowType.FULL_RISK_FLOW)
                val beneficiary = TestModelFactory.buildBeneficiary(onboarding = beneficiaryOnboarding)

                coEvery {
                    beneficiaryService.findByPersonId(
                        person.id,
                        BeneficiaryService.FindOptions(withOnboarding = true)
                    )
                } returns beneficiary
                coEvery {
                    beneficiaryService.findByParentId(
                        beneficiary.id,
                        onlyDirectDependents = true
                    )
                } returns listOf()
                coEvery { personService.get(any()) } returns person.copy(dateOfBirth = LocalDateTime.now().minusYears(10))
                coEvery { personCalendlyService.getOrCreate(person.id) } returns personCalendly

                every { ServiceConfig.healthDeclarationAppointment } returns healthDeclarationAppointmentConfig

                val result = healthDeclarationAppointmentScheduler.getBeneficiaryScheduleUrl(person.id, false)

                assertThat(result).isSuccessWithData(
                    healthDeclarationAppointmentConfig.beneficiaryFullRiskCalendarUrl
                        .replace("[[NAME]]", person.contactName.toUrlEncoded())
                        .replace("[[EMAIL]]", person.email.toUrlEncoded())
                        .plus("&utm_term=$utmTerm")
                        .plus("&person_calendly_id=${personCalendly.id}")
                )
            }
        }
    }
}
