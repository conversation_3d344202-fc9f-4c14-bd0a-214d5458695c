package br.com.alice.channel.core.services.internal

import br.com.alice.channel.core.FirestoreTestHelper
import br.com.alice.channel.core.context.FirestoreContextUsage
import br.com.alice.channel.core.controllers.ChannelGenerateProtocolANS
import br.com.alice.channel.core.controllers.MessageToChannel
import br.com.alice.channel.core.controllers.SendMessageToChannelRequest
import br.com.alice.channel.core.controllers.StaffUpdateOnChannelsRequest
import br.com.alice.channel.core.extensions.toModel
import br.com.alice.channel.core.services.internal.firestore.MessageFirestoreService
import br.com.alice.channel.event.HealthcareTeamUpsertedPayload
import br.com.alice.channel.event.ShouldAddProtocolANS
import br.com.alice.channel.models.ChannelDocument
import br.com.alice.channel.models.ChannelStaffInfo
import br.com.alice.common.core.extensions.toPersonId
import br.com.alice.common.core.extensions.toUUID
import br.com.alice.common.data.dsl.matchers.ResultAssert.Companion.assertThat
import br.com.alice.common.data.dsl.matchers.queryEq
import br.com.alice.common.helpers.coVerifyNone
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.common.kafka.interfaces.KafkaProducerService
import br.com.alice.common.kafka.interfaces.ProducerResult
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.HealthcareTeamChannel
import br.com.alice.data.layer.services.HealthcareTeamChannelDataService
import br.com.alice.person.client.PersonService
import br.com.alice.staff.client.StaffService
import com.github.kittinunf.result.failure
import com.github.kittinunf.result.success
import com.google.cloud.Timestamp
import com.google.cloud.firestore.WriteResult
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.every
import io.mockk.mockk
import io.mockk.mockkObject
import kotlinx.coroutines.runBlocking
import java.time.LocalDate
import java.time.LocalDateTime
import kotlin.random.Random
import kotlin.test.BeforeTest
import kotlin.test.Test
import br.com.alice.channel.core.services.internal.ChannelService as InternalChannelService

class ChannelBackfillServiceTest : FirestoreTestHelper() {

    private val staffService: StaffService = mockk()
    private val personService: PersonService = mockk()
    private val internalChannelService: InternalChannelService = mockk()
    private val messageFirestoreService: MessageFirestoreService = mockk()
    private val healthcareTeamChannelDataService: HealthcareTeamChannelDataService = mockk()
    private val kafkaProducerService: KafkaProducerService = mockk()

    private val service = ChannelBackfillServiceImpl(
        staffService,
        personService,
        internalChannelService,
        messageFirestoreService,
        healthcareTeamChannelDataService,
        kafkaProducerService
    )

    private val person = TestModelFactory.buildPerson()
    private val staff = TestModelFactory.buildStaff(id = staffId.toUUID())
    private val personId = person.id.toString()

    private val execute = true
    private val channelDocument = ChannelDocument(
        id = channelId,
        personId = personId,
        channelPersonId = personId
    )
    private val channelDocumentWithStaff =
        ChannelDocument(
            id = channelId,
            staff = mutableMapOf(
                staffId to ChannelStaffInfo(
                    id = staffId,
                    name = staff.firstName,
                    firstName = staff.firstName,
                    lastName = staff.lastName,
                    description = "Med AA",
                    profileImageUrl = staff.profileImageUrl.orEmpty()
                )
            ),
            staffIds = listOf(staffId),
            personId = personId,
            channelPersonId = personId
        )

    private val messageToChannel = MessageToChannel(
        channelId = channelId,
        staffId = staffId,
        message = "Olá @nickname, como você está se sentindo hoje?",
    )
    private val sendMessageToChannelRequest = SendMessageToChannelRequest(
        execute = execute,
        channels = listOf(messageToChannel),
    )

    @BeforeTest
    fun before() {
        super.setup()

        coEvery { personService.get(personId.toPersonId(), withUserType = false) } returns person.success()
        every { firestore.batch() } returns batch
        every { batch.commit().get() } returns mockk<List<WriteResult>>()
    }

    @OptIn(FirestoreContextUsage::class)
    @Test
    fun `#sendMessageToChannel should send message to channel when staffId is null`(): Unit =
        runBlocking {
            val request = sendMessageToChannelRequest.copy(channels = listOf(messageToChannel.copy(staffId = null)))
            val successCount = sendMessageToChannelRequest.channels?.size ?: 0
            val errorsCount = 0
            val response = BackfillResponse(successCount, errorsCount)

            coEvery { internalChannelService.getChannel(channelId) } returns channelDocument.success()
            coEvery { messageFirestoreService.add(channelId, any()) } returns channelId.success()

            val result = mockFirestoreContextCommit { service.sendMessageToChannel(request) }

            assertThat(result).isSuccessWithData(response)
        }

    @OptIn(FirestoreContextUsage::class)
    @Test
    fun `#sendMessageToChannel should send message to channel when staff is on the channel`(): Unit =
        runBlocking {
            val successCount = sendMessageToChannelRequest.channels?.size ?: 0
            val errorsCount = 0
            val response = BackfillResponse(successCount, errorsCount)

            coEvery { internalChannelService.getChannel(channelId) } returns channelDocumentWithStaff.success()
            coEvery { staffService.get(staffId.toUUID()) } returns staff.success()
            coEvery { messageFirestoreService.add(channelId, any()) } returns channelId.success()

            val result = mockFirestoreContextCommit { service.sendMessageToChannel(sendMessageToChannelRequest) }

            assertThat(result).isSuccessWithData(response)
        }

    @OptIn(FirestoreContextUsage::class)
    @Test
    fun `#sendMessageToChannel should not send message to channel when staff is not on the channel`(): Unit =
        runBlocking {
            val successCount = 0
            val errorsCount = sendMessageToChannelRequest.channels?.size ?: 0
            val response = BackfillResponse(successCount, errorsCount)

            every { documentSnapshot.toModel(ChannelDocument::class) } returns channelDocument
            coEvery { staffService.get(staffId.toUUID()) } returns staff.success()
            coEvery { messageFirestoreService.add(channelId, any()) } returns channelId.success()

            val result = mockFirestoreContextCommit { service.sendMessageToChannel(sendMessageToChannelRequest) }

            assertThat(result).isSuccessWithData(response)
        }

    @Test
    fun `#forceStaffUpdateOnChannels should send an HealthcareTeamChannelUpsertedEvent for each healthcareTeamId`() = runBlocking {
        val healthcareTeamIds = listOf(
            "03c62518-3875-439b-ae03-e70da2204b00",
            "0e215bd8-b76a-4a01-8f41-33d316265600",
            "1737f907-04bc-41f6-b262-ba20e3572300",
        )

        val healthcareTeamChannels = healthcareTeamIds.map {
            HealthcareTeamChannel(
                healthcareTeamId = it.toUUID()
            )
        }

        coEvery {
            healthcareTeamChannelDataService.find(
                queryEq {
                    where {
                        this.healthcareTeamIdField.inList(
                            healthcareTeamIds.map { it.toUUID() }
                        )
                    }
                }
            )
        } returns healthcareTeamChannels.success()

        coEvery {
            kafkaProducerService.produce(match {
                val payload = it.payload
                payload is HealthcareTeamUpsertedPayload && healthcareTeamChannels.contains(payload.healthcareTeamChannel)
            })
        } returns ProducerResult(
            LocalDateTime.now(),
            "topic-name",
            0
        )

        val result = service.forceStaffUpdateOnChannels(StaffUpdateOnChannelsRequest(healthcareTeamIds))
        assertThat(result).isSuccessWithData(BackfillResponse(
            successCount = 3,
            errorsCount = 0
        ))

        coVerify(exactly = 3) { kafkaProducerService.produce(any()) }
        coVerifyOnce { healthcareTeamChannelDataService.find(any()) }
    }

    @Test
    fun `#addProtocolAns - should not generate protocol if is empty list of channelIds`() = runBlocking {
        val channelIds = emptyList<String>()
        val request = ChannelGenerateProtocolANS(channelIds, LocalDate.now())
        val expected = BackfillResponse(
            successCount = 0,
            errorsCount = 0
        )

        val result = service.addProtocolAns(request)

        assertThat(result).isSuccessWithData(expected)

        coVerifyNone {
            internalChannelService.getChannelsById(any())
            kafkaProducerService.produce(any())
        }
    }

    @Test
    fun `#addProtocolAns - should generate protocol and send event`() = runBlocking {
        mockkObject(Random)
        val channelIds = listOf(channelDocument.id())
        val date = LocalDate.now()
        val request = ChannelGenerateProtocolANS(channelIds, date)
        val expected = BackfillResponse(
            successCount = 1,
            errorsCount = 0
        )
        val cont = "111"
        val protocol = cont.padStart(6, '0').substring(0, 6).let {
            StringBuilder()
                .append("421928")
                .append(date.year)
                .append(String.format("%02d", date.monthValue))
                .append(date.dayOfMonth)
                .append(it)
                .toString()
        }

        val event = ShouldAddProtocolANS(
            channelId = channelDocument.id(),
            protocol = protocol
        )
        every { Random.nextLong(1, 999999) } returns cont.toLong()

        coEvery { internalChannelService.getChannelsById(channelIds) } returns listOf(channelDocument).success()
        coEvery { kafkaProducerService.produce(event) } returns mockk()


        val result = service.addProtocolAns(request)

        assertThat(result).isSuccessWithData(expected)

        coVerifyOnce {
            internalChannelService.getChannelsById(any())
            kafkaProducerService.produce(any())
        }
    }

    @Test
    fun `#addProtocolAns - should generate many protocol and send events`() = runBlocking {
        mockkObject(Random)
        val channelDocument2 = channelDocument.copy(
            id = "channelId2",
            personId = personId,
            channelPersonId = personId,
            createdAt = Timestamp.now(),
        )

        val channelIds = listOf(channelDocument.id(), channelDocument2.id())
        val date = LocalDate.now()
        val request = ChannelGenerateProtocolANS(channelIds, date)
        val expected = BackfillResponse(
            successCount = 2,
            errorsCount = 0
        )
        val cont = "111"
        val cont2 = "222"

        val protocol1 = cont.padStart(6, '0').substring(0, 6).let {
            StringBuilder()
                .append("421928")
                .append(date.year)
                .append(String.format("%02d", date.monthValue))
                .append(date.dayOfMonth)
                .append(it)
                .toString()
        }

        val protocol2 = cont2.padStart(6, '0').substring(0, 6).let {
            StringBuilder()
                .append("421928")
                .append(date.year)
                .append(String.format("%02d", date.monthValue))
                .append(date.dayOfMonth)
                .append(it)
                .toString()
        }

        val event1 = ShouldAddProtocolANS(
            channelId = channelDocument.id(),
            protocol = protocol1
        )
        val event2 = ShouldAddProtocolANS(
            channelId = channelDocument2.id(),
            protocol = protocol2
        )
        every { Random.nextLong(1, 999999) } returnsMany listOf(cont.toLong(), cont2.toLong())

        coEvery { internalChannelService.getChannelsById(channelIds) } returns listOf(channelDocument, channelDocument2).success()
        coEvery { kafkaProducerService.produce(event1) } returns mockk()
        coEvery { kafkaProducerService.produce(event2) } returns mockk()


        val result = service.addProtocolAns(request)

        assertThat(result).isSuccessWithData(expected)

        coVerifyOnce {
            internalChannelService.getChannelsById(any())
        }

        coVerify(exactly = 2) {
            kafkaProducerService.produce(any())
        }
    }


    @Test
    fun `#addProtocolAns - should return error`() = runBlocking {
        mockkObject(Random)
        val channelIds = listOf(channelDocument.id())
        val date = LocalDate.now()
        val request = ChannelGenerateProtocolANS(channelIds, date)
        val expected = BackfillResponse(
            successCount = 0,
            errorsCount = 1
        )
        val cont = "111"
        every { Random.nextLong(1, 999999) } returns cont.toLong()

        coEvery { internalChannelService.getChannelsById(channelIds) } returns Exception("Error").failure()

        val result = service.addProtocolAns(request)

        assertThat(result).isSuccessWithData(expected)

        coVerifyOnce {
            internalChannelService.getChannelsById(any())
        }
        coVerifyNone {
            kafkaProducerService.produce(any())
        }
    }


    @Test
    fun `#addProtocolAns - should return error and success`() = runBlocking {
        mockkObject(Random)
        val channelDocument2 = channelDocument.copy(
            id = "channelId2",
            personId = personId,
            channelPersonId = personId,
            createdAt = Timestamp.now(),
        )

        val channelIds = listOf(channelDocument.id(), channelDocument2.id())
        val date = LocalDate.now()
        val request = ChannelGenerateProtocolANS(channelIds, date)
        val expected = BackfillResponse(
            successCount = 1,
            errorsCount = 1
        )
        val cont = "111"
        val cont2 = "222"

        val protocol1 = cont.padStart(6, '0').substring(0, 6).let {
            StringBuilder()
                .append("421928")
                .append(date.year)
                .append(String.format("%02d", date.monthValue))
                .append(date.dayOfMonth)
                .append(it)
                .toString()
        }

        val protocol2 = cont2.padStart(6, '0').substring(0, 6).let {
            StringBuilder()
                .append("421928")
                .append(date.year)
                .append(String.format("%02d", date.monthValue))
                .append(date.dayOfMonth)
                .append(it)
                .toString()
        }

        val event1 = ShouldAddProtocolANS(
            channelId = channelDocument.id(),
            protocol = protocol1
        )
        val event2 = ShouldAddProtocolANS(
            channelId = channelDocument2.id(),
            protocol = protocol2
        )
        every { Random.nextLong(1, 999999) } returnsMany listOf(cont.toLong(), cont2.toLong())

        coEvery { internalChannelService.getChannelsById(channelIds) } returns listOf(channelDocument, channelDocument2).success()
        coEvery { kafkaProducerService.produce(event1) } returns mockk()
        coEvery { kafkaProducerService.produce(event2) } throws Exception("Error")


        val result = service.addProtocolAns(request)

        assertThat(result).isSuccessWithData(expected)

        coVerifyOnce {
            internalChannelService.getChannelsById(any())
        }

        coVerify(exactly = 2) {
            kafkaProducerService.produce(any())
        }
    }


}
