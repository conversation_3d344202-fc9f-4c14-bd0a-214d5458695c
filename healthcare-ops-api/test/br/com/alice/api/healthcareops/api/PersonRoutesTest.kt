package br.com.alice.api.healthcareops.api

import br.com.alice.api.healthcareops.controllers.DocumentsResponse
import br.com.alice.api.healthcareops.controllers.PersonController
import br.com.alice.api.healthcareops.models.AddressRequest
import br.com.alice.api.healthcareops.models.PersonResponse
import br.com.alice.api.healthcareops.models.PersonUpdateRequest
import br.com.alice.api.healthcareops.services.DocumentService
import br.com.alice.authentication.UserType
import br.com.alice.clinicalaccount.client.PersonInternalReferenceService
import br.com.alice.common.RangeUUID
import br.com.alice.common.convertTo
import br.com.alice.common.core.PersonId
import br.com.alice.common.core.Role
import br.com.alice.common.core.exceptions.NotFoundException
import br.com.alice.common.data.dsl.matchers.ResponseAssert.Companion.assertThat
import br.com.alice.common.helpers.bodyAsJson
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.common.helpers.returns
import br.com.alice.common.models.Gender
import br.com.alice.common.models.Sex
import br.com.alice.common.models.State
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.Address
import br.com.alice.data.layer.models.OnboardingContract
import br.com.alice.data.layer.models.Person
import br.com.alice.data.layer.models.PersonInternalReference
import br.com.alice.membership.client.ContractRegistry
import br.com.alice.person.client.PersonService
import com.github.kittinunf.result.failure
import com.github.kittinunf.result.success
import io.mockk.called
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.mockk
import io.mockk.slot
import org.assertj.core.api.Assertions
import org.assertj.core.api.Assertions.assertThat
import java.time.LocalDate
import kotlin.test.BeforeTest
import kotlin.test.Test


class PersonRoutesTest : RoutesTestHelper() {

    private val documentService: DocumentService = mockk()
    private val contractRegistry: ContractRegistry = mockk()
    private val personService: PersonService = mockk()
    private val personInternalReferenceService: PersonInternalReferenceService = mockk()

    private val personId = PersonId()
    private val person = TestModelFactory.buildPerson(personId = personId)
    private val personInternalRef = PersonInternalReference(
        personId = personId,
        internalCode = "NC1AAA0"
    )
    private val updateRequest = PersonUpdateRequest(
        firstName = "Primeiro Nome",
        lastName = "Segundo Nome",
        email = "<EMAIL>",
        cnsNumber = "123433",
        identityDocument = "288332221222",
        identityDocumentIssuingBody = "SSP/SP",
        dateOfBirth = "1998-04-09",
        nickName = "NickName",
        mothersName = "MothersName",
        phoneNumber = "11999333222",
        addresses = listOf(
            AddressRequest(
                state = State.SP,
                city = "São Paulo",
                street = "Rua XXX",
                number = "999",
                complement = "AP 300",
                postalCode = "00033322",
                neighbourhood = "Jd Paulista"
            )
        ),
        sex = Sex.INTERSEX,
        gender = Gender.MALE
    )

    @BeforeTest
    override fun setup() {
        super.setup()
        module.single { PersonController(documentService, contractRegistry, personService, personInternalReferenceService) }
    }

    @Test
    fun `#triggerSignedContractEvent should call contract signed event trigger`() {
        val onboardingContract = TestModelFactory.buildOnboardingContract(personId)
        coEvery { contractRegistry.triggerSignedContractEvent(personId) } returns onboardingContract.success()

        authenticatedAs(token, staffToBeAuthenticated) {
            post("/people/$personId/contract_signed/trigger") { response ->
                val result: OnboardingContract = response.bodyAsJson()

                assertThat(result).isEqualTo(onboardingContract)
            }
        }
    }

    @Test
    fun `#updatePerson should return 400 Bad Request when person was not found`() {
        coEvery { personService.get(personId) } returns NotFoundException("Person not found for personId $personId").failure()

        authenticatedAs(token, staffToBeAuthenticated) {
            put("/people/$personId", body = updateRequest) { assertThat(it).isNotFound() }
        }
    }

    @Test
    fun `#updatePerson should return 200 OK when person was updated with some new information`() {
        val cnsNumber = "3723283"
        val slot = slot<Person>()

        coEvery { personService.get(personId) } returns person.success()
        coEvery { personService.update(person = capture(slot), shouldValidateAdditionalInfo = true) } returns person.copy(cnsNumber = cnsNumber).success()

        val updateRequest = updateRequest.copy(
            cnsNumber = cnsNumber
        )

        val expectedUpdate = Person(
            nationalId = person.nationalId,
            cnsNumber = cnsNumber,
            email = updateRequest.email,
            addresses = updateRequest.addresses.map { it.convertTo(Address::class) },
            identityDocument = updateRequest.identityDocument,
            identityDocumentIssuingBody = updateRequest.identityDocumentIssuingBody,
            nickName = updateRequest.nickName,
            mothersName = updateRequest.mothersName,
            phoneNumber = updateRequest.phoneNumber,
            dateOfBirth = LocalDate.parse("1998-04-09").atStartOfDay(),
            firstName = updateRequest.firstName,
            lastName = updateRequest.lastName,
            sex = updateRequest.sex,
            gender = updateRequest.gender,
            userType = UserType.MEMBER,
            piiInternalCode = person.piiInternalCode,
        )

        authenticatedAs(token, staffToBeAuthenticated) {
            put("/people/$personId", body = updateRequest) { response ->
                assertThat(response).isSuccessfulJson()

                val updatedPerson = slot.captured
                assertThat(updatedPerson).usingRecursiveComparison().ignoringFields("id", "createdAt", "updatedAt").isEqualTo(expectedUpdate)

                val personResponse: PersonResponse = response.bodyAsJson()
                assertThat(personResponse.cnsNumber).isEqualTo(cnsNumber)
            }
        }

        coVerifyOnce { personService.update(any(), any(), any()) }
    }

    @Test
    fun `#getDocumentPicture should return a placeholder when person does not have any picture`() {
        val personWithoutDocuments = person.copy(documentPictureUrl = null, identityDocumentVaultId = null)
        coEvery { personService.get(personId) } returns personWithoutDocuments.success()

        authenticatedAs(token, staffToBeAuthenticated) {
            get("/people/$personId/document") { response ->
                assertThat(response).isSuccessfulJson()

                val json: DocumentsResponse = response.bodyAsJson()
                assertThat(json.selfiePictureUrl).isEqualTo(DocumentService.IMAGE_PLACEHOLDER)
                assertThat(json.identityPictureUrl).isEqualTo(DocumentService.IMAGE_PLACEHOLDER)
            }
        }
    }

    @Test
    fun `#getDocumentPicture should return a placeholder when file vault doesnt have a picture`() {
        val documentVaultId = RangeUUID.generate().toString()

        val personWithIdentityDocument = person.copy(
            documentPictureUrl = null,
            identityDocumentVaultId = documentVaultId
        )

        coEvery { personService.get(personId) } returns personWithIdentityDocument.success()
        coEvery { documentService.getDocumentUrlFromVault(documentVaultId) } returns null

        authenticatedAs(token, staffToBeAuthenticated) {
            get("/people/$personId/document") { response ->
                assertThat(response).isSuccessfulJson()

                val json: DocumentsResponse = response.bodyAsJson()
                assertThat(json.identityPictureUrl).isEqualTo(DocumentService.IMAGE_PLACEHOLDER)
            }
        }
    }

    @Test
    fun `#getDocumentPicture return 404 NotFound when person does not exits`() {
        val personId = PersonId()
        coEvery { personService.get(personId) } returns NotFoundException("some_error").failure()

        authenticatedAs(token, staffToBeAuthenticated) {
            get("/people/$personId/document") { response ->
                assertThat(response).isNotFoundWithErrorCode("resource_not_found")
                coVerify(exactly = 0) { documentService.getExternalUrl(any()) }
            }
        }
    }

    @Test
    fun `#terminate session should terminate user session`() {
        val personId = PersonId(RangeUUID.generate(RangeUUID.PERSON_ID_RANGE))

        coEvery {
            personService.terminateSession(personId)
        } returns true.success()

        authenticatedAs(token, staffToBeAuthenticated) {
            post("/people/$personId/terminate_session") { response ->
                val expectedResponse = true

                val result: Boolean = response.bodyAsJson()
                Assertions.assertThat(result).isEqualTo(expectedResponse)

                coVerify(exactly = 1) { personService.terminateSession(personId) }
            }
        }
    }


    @Test
    fun `#searchPerson should return 400 Bad Request when does not have params`() {
        authenticatedAs(token, staffToBeAuthenticated) {
            get("/person") {
                assertThat(it).isBadRequestWithErrorCode("required_field_missing")

                coVerify { personService wasNot called }
                coVerify { personInternalReferenceService wasNot called }
            }
        }
    }

    @Test
    fun `#searchPerson search by tokens if not internal code`() {
        val query = "NC100"
        coEvery { personService.findBySearchTokens(query) } returns listOf(person).success()

        authenticatedWithRolesAs(token, staffToBeAuthenticated, listOf(Role.HEALTH_COMMUNITY.name)) {
            get("/person?query=$query") {
                assertThat(it).isSuccessfulJson()
                val list: List<Person> = it.bodyAsJson()
                assertThat(list).isEqualTo(listOf(person))

                coVerify { personInternalReferenceService wasNot called }
                coVerify(exactly = 1) { personService.findBySearchTokens(query) }
            }
        }
    }

    @Test
    fun `#searchPerson search by tokens if does not have role to search by internal code`() {
        val query = "NC1AAA0"
        coEvery { personService.findBySearchTokens(query) } returns listOf(person).success()

        authenticatedWithRolesAs(token, staffToBeAuthenticated, listOf(Role.PRODUCT_TECH.name)) {
            get("/person?query=$query") {
                assertThat(it).isSuccessfulJson()
                val list: List<Person> = it.bodyAsJson()
                assertThat(list).isEqualTo(listOf(person))

                coVerify { personInternalReferenceService wasNot called }
                coVerify(exactly = 1) { personService.findBySearchTokens(query) }
            }
        }
    }

    @Test
    fun `#searchPerson returns emptyList if fails to find person`() {
        val query = personInternalRef.internalCode
        coEvery { personInternalReferenceService.getByInternalCode(query) } returns Exception("").failure()

        authenticatedWithRolesAs(token, staffToBeAuthenticated, listOf(Role.HEALTH_COMMUNITY.name)) {
            get("/person?query=$query") {
                assertThat(it).isSuccessfulJson()
                val list: List<Person> = it.bodyAsJson()
                assertThat(list).isEmpty()

                coVerify { personService wasNot called }
                coVerify(exactly = 1) { personInternalReferenceService.getByInternalCode(query) }
            }
        }
    }

    @Test
    fun `#searchPerson search by internal code if has role and code`() {
        val query = personInternalRef.internalCode
        coEvery { personInternalReferenceService.getByInternalCode(query) } returns personInternalRef.success()
        coEvery { personService.get(personInternalRef.personId) } returns person.success()

        authenticatedWithRolesAs(token, staffToBeAuthenticated, listOf(Role.HEALTH_COMMUNITY.name)) {
            get("/person?query=$query") {
                assertThat(it).isSuccessfulJson()
                val list: List<Person> = it.bodyAsJson()
                assertThat(list).isEqualTo(listOf(person))

                coVerify(exactly = 0) { personService.findBySearchTokens(query) }
                coVerify(exactly = 1) { personInternalReferenceService.getByInternalCode(query) }
                coVerify(exactly = 1) { personService.get(personInternalRef.personId) }
            }
        }
    }

    @Test
    fun `#searchPerson search by nationalId with symbols`() {
        val query = "123.456.789-09"
        coEvery { personService.findBySearchTokens(any()) } returns listOf(person)

        authenticatedWithRolesAs(token, staffToBeAuthenticated, listOf(Role.HEALTH_COMMUNITY.name)) {
            get("/person?query=$query") {
                assertThat(it).isSuccessfulJson()
                val list: List<Person> = it.bodyAsJson()
                assertThat(list).isEqualTo(listOf(person))
            }
        }

        coVerifyOnce { personService.findBySearchTokens("12345678909") }
    }
}
