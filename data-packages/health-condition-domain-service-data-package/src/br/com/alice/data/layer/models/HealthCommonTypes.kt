package br.com.alice.data.layer.models

import br.com.alice.client.dalya.models.DalyaTriageRecommendationResponse
import br.com.alice.common.Disease
import br.com.alice.common.serialization.JsonSerializable
import java.math.BigDecimal
import java.math.RoundingMode
import java.util.UUID
import kotlin.math.pow

/** Health Condition **/

data class CaseRecordDetails(
    val caseId: UUID? = null,
    val description: DiseaseDetails,
    val cipes: List<DiseaseDetails>? = emptyList(),
    val severity: CaseSeverity,
    val follow: FollowUp? = null,
    val observation: String? = null,
    val channel: ChannelId? = null,
    val specialistOpinion: SpecialistOpinionDetails? = null,
    val seriousness: CaseSeriousness? = null,
    val dalyaRecommendation: DalyaTriageRecommendationResponse? = null
)

data class DiseaseDetails(
    val id: String? = null,
    val type: Disease.Type,
    val value: String,
    val description: String? = null
) {
    fun isCid() = type == Disease.Type.CID_10
    fun isCiap() = type == Disease.Type.CIAP_2
    fun isCondition() = isCiap() || isCid()
    fun getDiseaseDescription() = "${HealthConditionCodeType.valueOf(type.name).description} ${value} - ${description}"
}

enum class CaseStatus {
    ACTIVE, CANCELLED, PENDING, CONTEMPLATING;

    companion object {
        fun ofGoal() = listOf(ACTIVE, CANCELLED, CONTEMPLATING)
        fun ofCases() = listOf(ACTIVE, CANCELLED, PENDING)
        fun actives() = listOf(ACTIVE, CONTEMPLATING)
    }
}

enum class CaseSeverity(val description: String) {
    COMPENSATED("Compensado"),
    DECOMPENSATED("Descompensado"),
    INACTIVE("Inativo"),
    PAUSED("Pausado"),
    ONGOING("Em andamento"),
    DONE("Concluído");

    companion object {
        fun ofGoal() = listOf(PAUSED, ONGOING, DONE)
        fun ofCases() = listOf(COMPENSATED, DECOMPENSATED, INACTIVE)
        fun actives() = listOf(COMPENSATED, DECOMPENSATED, ONGOING)
    }
}

enum class CaseSeriousness(val description: String) {
    LOW("Baixo"),
    MEDIUM("Médio"),
    HIGH("Alto")
}

enum class HealthConditionCodeType(val description: String) {
    CID_10("CID"),
    CIAP_2("CIAP"),
    CIPE("CIPE"),
    GOAL("Objetivo de Saúde"),
    FREE_TEXT("Texto Livre"),
    SYMPTOM("Sintoma"),
}

data class SpecialistOpinionDetails(
    val question: String? = null,
    val abstract: String? = null,
    val attachments: List<Attachment> = emptyList(),
    val medicalSpecialtyId: UUID? = null
)

data class FollowUp(
    val id: String,
    val value: Int,
    val title: String,
    val description: String,
)

data class ChannelId(
    val id: String? = null,
    val name: String?
)

data class Cpt(
    val condition: String,
    val cids: List<String>,
) : JsonSerializable

data class Imc(
    val height: Double,
    val weight: Double
) {

    fun calculate(): BigDecimal {
        val heightInMeters = (height/100).pow(2)
        return BigDecimal(weight / heightInMeters).setScale(2, RoundingMode.HALF_DOWN)
    }
    override fun toString() = "Altura: $height cm - Peso: $weight kg"
}
