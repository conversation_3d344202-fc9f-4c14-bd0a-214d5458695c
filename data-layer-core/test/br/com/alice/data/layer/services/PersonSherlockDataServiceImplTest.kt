package br.com.alice.data.layer.services

import br.com.alice.common.RangeUUID
import br.com.alice.common.core.Model
import br.com.alice.common.core.PersonId
import br.com.alice.common.data.dsl.matchers.ResultAssert.Companion.assertThat
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.common.helpers.verifyOnce
import br.com.alice.common.logging.logger
import br.com.alice.common.service.data.dsl.Query
import br.com.alice.data.layer.helpers.DataLayerTestModelFactory
import br.com.alice.data.layer.models.PersonModel
import br.com.alice.data.layer.pipelines.DatabasePipeline
import br.com.alice.data.layer.pipelines.DatabasePipelineFactory
import br.com.alice.data.layer.tables.PersonTable
import br.com.alice.data.layer.tables.Table
import com.github.kittinunf.result.success
import io.mockk.called
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.confirmVerified
import io.mockk.every
import io.mockk.mockk
import io.mockk.mockkObject
import io.mockk.mockkStatic
import io.mockk.verify
import kotlinx.coroutines.runBlocking
import java.security.MessageDigest
import kotlin.reflect.KClass
import kotlin.test.AfterTest
import kotlin.test.Test

class PersonSherlockDataServiceImplTest {

    private val factory: DatabasePipelineFactory = mockk()
    private val personTokenService: PersonTokenService = mockk()

    private val dataBasePipeline: DatabasePipeline<PersonModel> = mockk()

    private val id = RangeUUID.generate(RangeUUID.PERSON_ID_RANGE)
    private val personId = PersonId(id)

    private val person = DataLayerTestModelFactory.buildPerson(personId = personId)

    @AfterTest
    fun confirmMocks() = confirmVerified(
        factory,
        personTokenService,
        dataBasePipeline,
    )

    @Test
    fun `#findByIds returns persons found by personId after token conversion`() = runBlocking {
        val query = Query(
            where = PersonSherlockDataService.PersonIdField().inList(listOf(personId))
        )

        mockkObject(logger)

        every { factory.get(PersonModel::class, PersonTable::class) } returns dataBasePipeline
        every { personTokenService.getPersonIdByUUID(id) } returns personId
        coEvery { dataBasePipeline.findByQuery(query) } returns listOf(person).success()

        val personSherlockDataService = PersonSherlockDataServiceImpl(factory, personTokenService)

        val result = personSherlockDataService.findByIds(listOf(id))
        assertThat(result).isSuccessWithData(listOf(person))

        verifyOnce { factory.get(any<KClass<Model>>(), any<KClass<Table<*>>>()) }
        verifyOnce { logger.info("sherlock_person_token_conversion", "ids" to listOf(id)) }
        verifyOnce { personTokenService.getPersonIdByUUID(any()) }
        coVerifyOnce { dataBasePipeline.findByQuery(any()) }
    }

    @Test
    fun `#getPersonIds returns personIds from ids`() = runBlocking {
        mockkObject(logger)

        every { factory.get(PersonModel::class, PersonTable::class) } returns dataBasePipeline
        every { personTokenService.getPersonIdByUUID(id) } returns personId

        val personSherlockDataService = PersonSherlockDataServiceImpl(factory, personTokenService)

        val result = personSherlockDataService.getPersonIds(listOf(id))
        assertThat(result).isSuccessWithData(listOf(personId))

        verifyOnce { factory.get(any<KClass<Model>>(), any<KClass<Table<*>>>()) }
        verifyOnce { logger.info("sherlock_person_token_conversion", "ids" to listOf(id)) }
        verifyOnce { personTokenService.getPersonIdByUUID(any()) }
        verify { dataBasePipeline wasNot called }
    }

    @Test
    fun `#get returns person found by personId after token conversion`() = runBlocking {
        every { factory.get(PersonModel::class, PersonTable::class) } returns dataBasePipeline
        every { personTokenService.getPersonIdByUUID(id) } returns personId
        coEvery { dataBasePipeline.get(personId) } returns person.success()

        val personSherlockDataService = PersonSherlockDataServiceImpl(factory, personTokenService)

        val result = personSherlockDataService.get(id)
        assertThat(result).isSuccessWithData(person)

        verifyOnce { factory.get(any<KClass<Model>>(), any<KClass<Table<*>>>()) }
        verifyOnce { personTokenService.getPersonIdByUUID(any()) }
        coVerifyOnce { dataBasePipeline.get(any()) }
    }

    @Test
    fun `#generateHashByIds -- returns persons found by personId after token conversion`() = runBlocking {
        val personPiiToken = PersonPiiToken()
        val personNonPiiToken = PersonNonPiiToken(RangeUUID.generate(RangeUUID.PERSON_NON_PII_TOKEN_RANGE))

        val query = Query(
            where = PersonSherlockDataService.PersonIdField().inList(listOf(personId))
        )

        val saltForUseAtHashGenerate : Map<String, String> = mapOf(
            "person_id01" to "01",
            "person_id02" to "02",
            "person_id03" to "03"
        )

        val expected = mapOf(
            "person_id01" to "64696765737465642d3031",
            "person_id02" to "64696765737465642d3032",
            "person_id03" to "64696765737465642d3033"
        )

        val messageDigest: MessageDigest = mockk()

        val zeroOne = "${personId.id}_person_id01_01"
        val zeroTwo = "${personPiiToken.id}_person_id02_02"
        val zeroThree = "${personNonPiiToken.id}_person_id03_03"

        val digestedZeroOne: ByteArray = "digested-01".toByteArray()
        val digestedZeroTwo: ByteArray = "digested-02".toByteArray()
        val digestedZeroThree: ByteArray = "digested-03".toByteArray()

        mockkObject(logger)

        mockkStatic(MessageDigest::class)

        every { factory.get(PersonModel::class, PersonTable::class) } returns dataBasePipeline
        every { personTokenService.getPersonIdByUUID(id) } returns personId
        every { personTokenService.getPersonPiiToken(personId) } returns personPiiToken
        every { personTokenService.getPersonNonPiiToken(personId) } returns personNonPiiToken
        coEvery { dataBasePipeline.findByQuery(query) } returns listOf(person).success()

        every { MessageDigest.getInstance("SHA-256") } returns messageDigest

        every { messageDigest.digest(zeroOne.toByteArray()) } returns digestedZeroOne
        every { messageDigest.digest(zeroTwo.toByteArray()) } returns digestedZeroTwo
        every { messageDigest.digest(zeroThree.toByteArray()) } returns digestedZeroThree

        val personSherlockDataService = PersonSherlockDataServiceImpl(factory, personTokenService)

        val result = personSherlockDataService.generateHashByIds(listOf(id.toString()), saltForUseAtHashGenerate)
        assertThat(result).isSuccessWithData(listOf(expected))

        verifyOnce { factory.get(any<KClass<Model>>(), any<KClass<Table<*>>>()) }
        verifyOnce { logger.info("sherlock_fetch_token_and_calculation_hashs", "person_id" to personId) }
        verifyOnce { personTokenService.getPersonIdByUUID(any()) }
        verifyOnce { personTokenService.getPersonPiiToken(any<PersonId>()) }
        verifyOnce { personTokenService.getPersonNonPiiToken(any<PersonId>()) }
        coVerify { dataBasePipeline wasNot called }
    }
}
