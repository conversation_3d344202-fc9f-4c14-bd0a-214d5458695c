
package br.com.alice.dbintegrationservice.models;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;


/**
 * <p>Java class for ct_EnviaLoteResultadosRequest_v2 complex type.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType name="ct_EnviaLoteResultadosRequest_v2">
 *   &lt;complexContent>
 *     &lt;extension base="{http://diagnosticosdobrasil.com.br}RequestMessage">
 *       &lt;sequence>
 *         &lt;element name="LoteResultado" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *       &lt;/sequence>
 *     &lt;/extension>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "ct_EnviaLoteResultadosRequest_v2", propOrder = {
    "loteResultado"
})
public class CtEnviaLoteResultadosRequestV2
    extends RequestMessage
{

    @XmlElement(name = "LoteResultado")
    protected String loteResultado;

    /**
     * Gets the value of the loteResultado property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getLoteResultado() {
        return loteResultado;
    }

    /**
     * Sets the value of the loteResultado property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setLoteResultado(String value) {
        this.loteResultado = value;
    }

}
