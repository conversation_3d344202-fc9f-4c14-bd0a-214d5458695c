package br.com.alice.healthlogicsapi.controllers

import br.com.alice.common.data.dsl.matchers.ResponseAssert.Companion.assertThat
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.healthlogicsapi.ControllerTestHelper
import br.com.alice.healthlogicsapi.models.HealthPlanTaskTemplateSimpleResponse
import br.com.alice.healthplan.client.HealthPlanTaskTemplateService
import com.github.kittinunf.result.success
import io.mockk.clearAllMocks
import io.mockk.coEvery
import io.mockk.mockk
import kotlinx.coroutines.runBlocking
import kotlin.test.BeforeTest
import kotlin.test.Test

class HealthPlanTaskTemplateControllerTest: ControllerTestHelper() {

    private val healthPlanTaskTemplateService: HealthPlanTaskTemplateService = mockk()

    private val controller = HealthPlanTaskTemplateController(healthPlanTaskTemplateService)

    private val taskTemplate = TestModelFactory.buildHealthPlanTaskTemplate()

    @BeforeTest
    override fun setup() {
        super.setup()
        clearAllMocks()

        module.single { controller }
    }

    @Test
    fun `#index - should return template from filter`() = runBlocking {
        val range = IntRange(0,19)
        val sort = "title" to "ASC"
        val filter = mapOf("title" to "test")
        val expected = listOf(
            HealthPlanTaskTemplateSimpleResponse(
                id = taskTemplate.id,
                type = taskTemplate.type,
                title = taskTemplate.title
            )
        )
        coEvery { healthPlanTaskTemplateService.getFilteredByRange(range, filter, sort) } returns listOf(taskTemplate).success()

        authenticatedAs(idToken, staff) {
            get("/health_logic/template?range=[0,19]&sort=[\"title\", \"ASC\"]&filter={\"title\":\"test\"}") { response ->

                assertThat(response).isOKWithData(expected)

                coVerifyOnce { healthPlanTaskTemplateService.getFilteredByRange(any(), any(), any()) }
            }
        }
    }

    @Test
    fun `#index - should return template from empty filter`() = runBlocking {
        val range = IntRange(0,19)
        val sort = null
        val filter = emptyMap<String, Any>()
        val expected = listOf(
            HealthPlanTaskTemplateSimpleResponse(
                id = taskTemplate.id,
                type = taskTemplate.type,
                title = taskTemplate.title
            )
        )
        coEvery { healthPlanTaskTemplateService.getFilteredByRange(range, filter, sort) } returns listOf(taskTemplate).success()

        authenticatedAs(idToken, staff) {
            get("/health_logic/template") { response ->

                assertThat(response).isOKWithData(expected)

                coVerifyOnce { healthPlanTaskTemplateService.getFilteredByRange(any(), any(), any()) }
            }
        }
    }

}
