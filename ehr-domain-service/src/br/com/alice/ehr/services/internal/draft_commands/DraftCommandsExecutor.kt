package br.com.alice.ehr.services.internal.draft_commands

import br.com.alice.common.core.exceptions.BadRequestException
import br.com.alice.common.core.exceptions.DuplicatedItemException
import br.com.alice.common.core.exceptions.NotFoundException
import br.com.alice.common.extensions.then
import br.com.alice.common.kafka.interfaces.KafkaProducerService
import br.com.alice.common.serialization.gson
import br.com.alice.common.service.data.dsl.and
import br.com.alice.data.layer.models.ClinicalBackground
import br.com.alice.data.layer.models.DeletedModel
import br.com.alice.data.layer.models.DraftCommandAction
import br.com.alice.data.layer.models.DraftCommandModel
import br.com.alice.data.layer.models.DraftCommandReferencedModel
import br.com.alice.data.layer.models.DraftCommandReferencedModel.CLINICAL_BACKGROUND
import br.com.alice.data.layer.models.DraftCommandReferencedModel.HEALTH_MEASUREMENT
import br.com.alice.data.layer.models.DraftCommandReferencedModel.PREGNANCY
import br.com.alice.data.layer.models.DraftCommandStatus
import br.com.alice.data.layer.models.HealthMeasurementModel
import br.com.alice.data.layer.models.PregnancyModel
import br.com.alice.data.layer.models.copy
import br.com.alice.data.layer.services.ClinicalBackgroundDataService
import br.com.alice.data.layer.services.DraftCommandModelDataService
import br.com.alice.data.layer.services.HealthMeasurementModelDataService
import br.com.alice.data.layer.services.PregnancyModelDataService
import br.com.alice.ehr.converters.toTransport
import br.com.alice.ehr.event.ClinicalBackgroundUpdatedEvent
import br.com.alice.ehr.event.HealthMeasurementCreatedEvent
import br.com.alice.ehr.event.PregnancyUpsertEvent
import com.github.kittinunf.result.flatMap
import java.util.UUID

class DraftCommandsExecutor(
    private val appointmentId: UUID,
    private val draftCommandDataService: DraftCommandModelDataService,
    pregnancyDataService: PregnancyModelDataService,
    healthMeasurementDataService: HealthMeasurementModelDataService,
    clinicalBackgroundDataService: ClinicalBackgroundDataService,
    eventProducer: KafkaProducerService,
) {
    private val appliers: Map<DraftCommandReferencedModel, Applier> = mapOf(
        PREGNANCY to PregnancyApplier(pregnancyDataService, eventProducer),
        CLINICAL_BACKGROUND to ClinicalBackgroundApplier(
            clinicalBackgroundDataService,
            eventProducer
        ),
        HEALTH_MEASUREMENT to HealthMeasurementApplier(
            healthMeasurementDataService,
            eventProducer
        ),
    )

    suspend fun call() {
        commands().forEach {
            applyCommand(it)
            markAsExecuted(it)
        }
    }

    private suspend fun commands(): List<DraftCommandModel> {
        val self = this

        return draftCommandDataService.find {
            where {
                this.appointmentId.eq(self.appointmentId) and
                    this.status.eq(DraftCommandStatus.PENDING.toString())
            }.orderBy { createdAt }.sortOrder { asc }
        }.get().sortedBy { it.createdAt }
    }

    private suspend fun applyCommand(command: DraftCommandModel) {
        val applier = appliers.getValue(command.referencedModel)

        when (command.action) {
            DraftCommandAction.ADD -> applier.applyAdd(command)
            DraftCommandAction.UPDATE -> applier.applyUpdate(command)
            DraftCommandAction.DELETE -> applier.applyDelete(deletedModelId(command))
        }
    }

    private fun deletedModelId(command: DraftCommandModel): UUID {
        val deletedModel = gson.fromJson(
            command.serializedModel, DeletedModel::class.java
        )

        return deletedModel.id
    }

    private suspend fun markAsExecuted(command: DraftCommandModel) {
        draftCommandDataService.update(
            command.copy(status = DraftCommandStatus.EXECUTED)
        ).get()
    }
}

abstract class Applier {
    suspend fun applyAdd(command: DraftCommandModel) {
        try {
            executeAdd(command)
        } catch (_: DuplicatedItemException) {
            // do nothing
        }
    }

    suspend fun applyUpdate(command: DraftCommandModel) {
        try {
            executeUpdate(command)
        } catch (_: NotFoundException) {
            // do nothing
        }
    }

    abstract suspend fun executeAdd(command: DraftCommandModel)

    abstract suspend fun executeUpdate(command: DraftCommandModel)

    abstract suspend fun applyDelete(id: UUID)
}

private class PregnancyApplier(
    private val dataService: PregnancyModelDataService,
    private val kafkaProducerService: KafkaProducerService
) : Applier() {
    override suspend fun executeAdd(command: DraftCommandModel) {
        dataService.add(deserializeModel(command)).then {
            kafkaProducerService.produce(PregnancyUpsertEvent(it.toTransport()))
        }.get()
    }

    override suspend fun executeUpdate(command: DraftCommandModel) {
        deserializeModel(command).let { currentPregnancy ->
            dataService.get(currentPregnancy.id).flatMap { pregnancy ->
                dataService.update(currentPregnancy.copy(version = pregnancy.version))
            }.then {
                kafkaProducerService.produce(PregnancyUpsertEvent(it.toTransport()))
            }.get()
        }
    }

    override suspend fun applyDelete(id: UUID) {
        val model = dataService.get(id).get()

        dataService.update(model.copy(active = false)).then {
            kafkaProducerService.produce(PregnancyUpsertEvent(it.toTransport()))
        }.get()
    }

    private fun deserializeModel(command: DraftCommandModel): PregnancyModel {
        return gson.fromJson(
            command.serializedModel, PregnancyModel::class.java
        )
    }
}

private class HealthMeasurementApplier(
    private val dataService: HealthMeasurementModelDataService,
    private val eventProducer: KafkaProducerService,
) : Applier() {
    override suspend fun executeAdd(command: DraftCommandModel) {
        val measurement = dataService.add(deserializeModel(command)).get()

        eventProducer.produce(HealthMeasurementCreatedEvent(measurement.toTransport()))
    }

    override suspend fun executeUpdate(command: DraftCommandModel) {
        deserializeModel(command).let { currentHealthMeasurement ->
            dataService.get(currentHealthMeasurement.id).flatMap { healthMeasurement ->
                dataService.update(currentHealthMeasurement.copy(version = healthMeasurement.version))
            }.get()
        }
    }

    override suspend fun applyDelete(id: UUID) {
        throw BadRequestException(
            "cannot delete a health measurement",
            "invalid_operation"
        )
    }

    private fun deserializeModel(command: DraftCommandModel): HealthMeasurementModel {
        return gson.fromJson(
            command.serializedModel, HealthMeasurementModel::class.java
        )
    }
}

private class ClinicalBackgroundApplier(
    private val dataService: ClinicalBackgroundDataService,
    private val eventProducer: KafkaProducerService,
) : Applier() {
    override suspend fun executeAdd(command: DraftCommandModel) {
        val background = dataService.add(deserializeModel(command)).get()

        eventProducer.produce(
            ClinicalBackgroundUpdatedEvent(background),
            background.personId.toString(),
        )
    }

    override suspend fun executeUpdate(command: DraftCommandModel) {
        deserializeModel(command).let { currentClinicalBackground ->
            dataService.get(currentClinicalBackground.id).flatMap { clinicalBackground ->
                dataService.update(currentClinicalBackground.copy(version = clinicalBackground.version))
            }.get()
        }
    }

    override suspend fun applyDelete(id: UUID) {
        throw BadRequestException(
            "cannot delete a clinical background",
            "invalid_operation"
        )
    }

    private fun deserializeModel(command: DraftCommandModel): ClinicalBackground {
        return gson.fromJson(
            command.serializedModel, ClinicalBackground::class.java
        )
    }
}
