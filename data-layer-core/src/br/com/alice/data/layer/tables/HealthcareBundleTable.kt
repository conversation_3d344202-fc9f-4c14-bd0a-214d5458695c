package br.com.alice.data.layer.tables

import br.com.alice.common.RangeUUID
import br.com.alice.common.core.extensions.unaccent
import br.com.alice.common.hash.MD5
import br.com.alice.common.service.data.client.TsVector
import br.com.alice.data.layer.models.HealthcareBundleCategory
import br.com.alice.data.layer.models.HealthcareBundleStatus
import br.com.alice.common.UpdatedBy
import java.time.LocalDate
import java.time.LocalDateTime
import java.util.UUID

internal data class HealthcareBundleTable(
    // Identification fields
    val code: String? = null,
    val name: String,
    val providerUnitGroupId: UUID? = null,
    var providerName: String? = null,
    val primaryTuss: String? = null,
    var compositionHash: String? = null,
    val primaryTussHealthcareResourceId: UUID? = null,
    val category: HealthcareBundleCategory = HealthcareBundleCategory.HEALTH_INSTITUTION,

    // Composition and integration fields
    val status: HealthcareBundleStatus = HealthcareBundleStatus.DRAFT,
    val groups: List<UUID>? = emptyList(),
    val hasMedicalFees: Boolean,
    val includedResources: List<UUID>? = emptyList(),
    val excludedResources: List<UUID>? = emptyList(),
    val validAfter: LocalDate,
    val validBefore: LocalDate? = null,

    // Basic fields
    override val id: UUID = RangeUUID.generate(),
    override val version: Int = 0,
    override val createdAt: LocalDateTime = LocalDateTime.now(),
    override val updatedAt: LocalDateTime = LocalDateTime.now(),
    var searchTokens: TsVector? = null,
    val updatedBy: UpdatedBy? = null
) : Table<HealthcareBundleTable> {

    init {
        val value = "$code $primaryTuss $name"
        searchTokens = TsVector(value.unaccent())

        compositionHash = generateCompositionHash()
    }

    private fun generateCompositionHash(): String {
        return MD5.digestToMD5("|Group|${groups?.sorted().toString()}" +
                "|Included|${includedResources?.sorted()}" +
                "|Excluded|${excludedResources?.sorted()}" +
                "|PrimaryTuss|$primaryTuss")
    }

    override fun copyTable(
        version: Int,
        updatedAt: LocalDateTime,
        createdAt: LocalDateTime
    ) =
        copy(
            version = version,
            createdAt = createdAt,
            updatedAt = updatedAt
        )
}
